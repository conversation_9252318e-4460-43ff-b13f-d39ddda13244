package com.linkus.km.elasticsearch.service;

import java.util.List;

import com.linkus.km.elasticsearch.dto.EsKnowledgeHotkeyResponse;

public interface IEsKnowledgeHotkeyService {

	/**
	 * 保存搜索关键词
	 * 
	 * @param systemId 系统id
	 * @param key
	 */
	Long addHotkey(String systemId, String key);

	/**
	 * 热搜词搜索
	 * 
	 * @param systemId
	 * @param pageSize
	 * @return
	 */
	List<EsKnowledgeHotkeyResponse> searchHotkeys(String systemId, Integer pageSize);
}
