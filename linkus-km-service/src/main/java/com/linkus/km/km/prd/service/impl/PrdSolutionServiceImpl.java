package com.linkus.km.km.prd.service.impl;

import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_L;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.condition.impl.mini.DC_R;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.util.StringUtil;
import com.linkus.km.km.prd.dao.ISysPageCnfgDao;
import com.linkus.km.km.prd.model.TeSysPageCnfg;
import com.linkus.km.km.prd.service.IPrdSolutionService;
import com.linkus.km.prj.dao.IKmFileDao;
import com.linkus.km.prj.model.FolderTreeVo;
import com.linkus.km.prj.model.TeFile;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTree;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class PrdSolutionServiceImpl implements IPrdSolutionService {

    @Resource
    private ISysDefService sysDefService;

    @Resource(name="kmFileDaoImpl")
    private IKmFileDao kmFileDao;

    @Autowired
    private ISysPageCnfgDao sysPageCnfgDao;

    @Override
    public FolderTreeVo getFolderTreeAndFiles(ObjectId kmBaseDefId) {

        List<SysDef> rootDomainList = sysDefService.getSysDefTreeRootsBySrc(kmBaseDefId, SysDefTypeCodeName.KM_DOMAIN);
        ObjectId rootId = rootDomainList.get(0).getId();
        Set<ObjectId> authedNodeIdSet = new HashSet<>();
        authedNodeIdSet.add(rootId);
        SysDefTree sysDefTree = sysDefService.getSubTree(rootId, 4, authedNodeIdSet, SysDefTypeCodeName.KM_DOMAIN.getValue());

        FolderTreeVo folderTreeVo = new FolderTreeVo();
        traverseTreeNode(sysDefTree, folderTreeVo);

        List<TeFile> teFiles = kmFileDao.queryFilesByFileBaseId(kmBaseDefId);
        List<TeFile> filePages = teFiles.stream().filter(file ->  null == file.getKpType()).collect(Collectors.toList());
        teFiles = teFiles.stream().filter(file -> null == file.getLinkedFileIds() && null != file.getKpType()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filePages)){
            List<ObjectId> fileIds = filePages.stream().map(TeFile::getId).collect(Collectors.toList());
            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DFN.common_isValid, true));
            conds.add(new DC_I<>(DbFieldName.SysPageCnfg.rcdId, fileIds));
            conds.add(new DC_E(DbFieldName.SysPageCnfg.isApplied, true));
            conds.add(new DC_E(DbFieldName.SysPageCnfg.isReleased, true));
            List<TeSysPageCnfg> pageCnfgs = sysPageCnfgDao.findByConds(conds, null);
            Map<ObjectId, ObjectId> idMap = pageCnfgs.stream().collect(Collectors.toMap(TeSysPageCnfg::getRcdId, cnfg -> cnfg.getPage().getCid()));
            fileIds = pageCnfgs.stream().map(TeSysPageCnfg::getRcdId).collect(Collectors.toList());
            List<TeFile> files = kmFileDao.findFileByNameAndIds(fileIds, null);
            for (TeFile file : files) {
                file.setSrcFileId(idMap.get(file.getId()));
            }
            teFiles.addAll(files);
        }
        Map<ObjectId, List<TeFile>> mapList = teFiles.stream().filter(teFile -> null != teFile.getFileFolderDefId())
                .collect(Collectors.groupingBy(TeFile::getFileFolderDefId));
        for (ObjectId id : mapList.keySet()) {
            List<TeFile> files = mapList.get(id);
            //排序的规则，先按No的大小升序，no为null的按照lastModifyTime倒序排序
            sortTeFiles(files);
            mapList.put(id,files);
        }

        List<TeSysDef> child = sysDefService.querySysDefs(StringUtil.toObjectId(SysDefTypeConstants.KMDOMAIN_DEF_ID), kmBaseDefId, null);
        List<TeSysDef> childList = child.stream().filter(def -> def.getParent2SelfIds().size() > 5).collect(Collectors.toList());
        for (TeSysDef sysDef : childList) {
            List<ObjectId> parent2SelfIds = sysDef.getParent2SelfIds();
            ObjectId id = parent2SelfIds.get(4);
            List<TeFile> files = mapList.get(id);
            files.addAll(mapList.get(sysDef.getId()));
            sortTeFiles(files);
            mapList.put(id,files);
        }
        setFiles(folderTreeVo,mapList);
        return folderTreeVo;
    }

    private static void sortTeFiles(List<TeFile> files) {
        files.sort((a, b) -> {
            if (a.getNo() != null && b.getNo() != null) {
                return a.getNo().compareTo(b.getNo());
            } else if (a.getNo() == null && b.getNo() == null) {
                // no都为null，按lastModifyTime倒序
                if (a.getLastModifyTime() == null && b.getLastModifyTime() == null) {
                    return 0;
                } else if (a.getLastModifyTime() == null) {
                    return 1;
                } else if (b.getLastModifyTime() == null) {
                    return -1;
                } else {
                    return b.getLastModifyTime().compareTo(a.getLastModifyTime());
                }
            } else if (a.getNo() == null) {
                // a.no为null，排在后面
                return 1;
            } else {
                // b.no为null，排在后面
                return -1;
            }
        });
    }

    @Override
    public void updateDefDescAndCn(ObjectId defId, String desc, String defCode) {
        TeSysDef sysDefById = sysDefService.getTeSysDefById(defId);
        sysDefById.setDefDesc(desc);
        sysDefById.setDefCode(defCode);
        sysDefService.updateSysDef(sysDefById);
    }

    @Override
    public List<TeFile> fuzzyQueryView(ObjectId fileFolderDefId, String desc, String tag, String sortName, boolean asc) {
        List<IDbCondition> conds = new ArrayList<>();
        List<TeSysDef> allChildrenByDefId = sysDefService.getAllChildrenByDefId(fileFolderDefId);
        List<ObjectId> ids = allChildrenByDefId.stream().map(TeSysDef::getId).collect(Collectors.toList());
        ids.add(fileFolderDefId);
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_I<>(DFN.file_fileFolderDefId, ids));
        if (StringUtil.isNotNull(desc)) {
            List<IDbCondition> orConds = new ArrayList<>();
            orConds.add(new DC_L(DFN.file_fileName, desc, true));
            orConds.add(new DC_L(DFN.file_uploadDesc, desc, true));
            orConds.add(new DC_L(DFN.file_hyperText, desc, true));
            conds.add(new DC_OR(orConds));
        }
        if (StringUtil.isNotNull(tag)) {
            conds.add(new DC_R(DFN.file_tags, tag));
        }
        conds.add(new DC_E(DFN.file_linkedFileIds, null));
        Sort sort = null;
        if (StringUtil.isNotNull(sortName)) {
            if (asc) {
                sort = Sort.by(Sort.Direction.ASC, sortName);
            } else {
                sort = Sort.by(Sort.Direction.DESC, sortName);
            }

        }
        return kmFileDao.findByConds(conds, sort);
    }

    @Override
    public Map<String, Object> fuzzyQueryFileId(ObjectId fileFolderDefId, ObjectId fileBaseDefId,String fileName, String keyWord,ObjectId userId) {
        Map<String,Object> result = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        List<TeSysDef> allChildrenByDefId = sysDefService.getAllChildrenByDefId(fileFolderDefId);
        List<ObjectId> ids = allChildrenByDefId.stream().map(TeSysDef::getId).collect(Collectors.toList());
        ids.add(fileFolderDefId);
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_I<>(DFN.file_fileFolderDefId, ids));
        if (StringUtil.isNotNull(fileName)){
            conds.add(new DC_E(DFN.file_fileName, fileName));
        }

        if (StringUtil.isNotNull(keyWord)){
            String escapedString = keyWord.replace("+", "\\+");
            conds.add(new DC_R(DFN.file_fileName, escapedString));
        }
        List<TeFile> teFiles = kmFileDao.findByConds(conds, Sort.by(Sort.Direction.DESC, DFN.file_uploadTime.n()));
        result.put("isAuth",true);
        if (!teFiles.isEmpty()){
            result.put("file",teFiles.get(0));
        }
        return result;
    }

    private void setFiles(FolderTreeVo folderTreeVo, Map<ObjectId, List<TeFile>> mapList){
        List<FolderTreeVo> children = folderTreeVo.getChildren();
        if (null != children && !children.isEmpty()){
            for (FolderTreeVo child : children) {
                child.setFiles(mapList.get(StringUtil.toObjectId(child.getValue())));
                setFiles(child,mapList);
            }
        }
    }


    private void traverseTreeNode(SysDefTree treeNode, FolderTreeVo folderTreeVo) {
        converTreeNode(treeNode, folderTreeVo);
        if (treeNode.getChildren() != null && !treeNode.getChildren().isEmpty()) {
            // 找到下一个需要处理的子节点
            // 递归调用本身
            for (SysDefTree childNode : treeNode.getChildren()) {
                FolderTreeVo childPrdCtlg = null;
                ObjectId nodeId = childNode.getCurrent().getId();
                if (nodeId != null) {
                    for (FolderTreeVo child : folderTreeVo.getChildren()) {
                        if (StringUtil.toObjectId(child.getValue()).equals(nodeId)) {
                            childPrdCtlg = child;
                            break;
                        }
                    }
                }

                if (childNode != null) {
                    traverseTreeNode(childNode, childPrdCtlg);
                }
            }
        }
    }

    private void converTreeNode(SysDefTree treeNode, FolderTreeVo folderTreeVo) {
        // 节点本身
        ObjectId ctlgId = treeNode.getCurrent().getId();
        folderTreeVo.setValue(ctlgId.toHexString());
        folderTreeVo.setLabel(treeNode.getCurrent().getDefName());
        folderTreeVo.setAllowed(treeNode.getAllowed() == null || treeNode.getAllowed());
        folderTreeVo.setAuthed(treeNode.getAuthed());
        folderTreeVo.setExpandable(treeNode.getExpandable());
        folderTreeVo.setDesc(treeNode.getCurrent().getDefDesc());
        // 处理父节点
        SysDef parentNode = treeNode.getParent();
        if (parentNode != null) {
            folderTreeVo.setParentId(parentNode.getId());
        }

        // 处理子节点
        if (treeNode.getChildren() != null && treeNode.getChildren().size() > 0) {
            List<FolderTreeVo> children = new ArrayList<>();
            for (SysDefTree childNode : treeNode.getChildren()) {
                FolderTreeVo childPrdCtlg = new FolderTreeVo();
                childPrdCtlg.setValue(childNode.getCurrent().getId().toHexString());
                childPrdCtlg.setLabel(childNode.getCurrent().getDefName());
                childPrdCtlg.setParentId(ctlgId);
                childPrdCtlg.setAllowed(childNode.getAllowed() == null || childNode.getAllowed());
                childPrdCtlg.setAuthed(childNode.getAuthed());
                childPrdCtlg.setDesc(childNode.getCurrent().getDefDesc());
                children.add(childPrdCtlg);
            }
            for (int i = 0; i < children.size(); i++) {
                FolderTreeVo d = children.get(i);
                if (i != 0) {
                    d.setPrevSiblingId(StringUtil.toObjectId(children.get(i - 1).getValue()));
                }
                if (i != children.size() - 1) {
                    d.setNextSiblingId(StringUtil.toObjectId(children.get(i + 1).getValue()));
                }
            }

            folderTreeVo.setChildren(children);
        }
    }
}
