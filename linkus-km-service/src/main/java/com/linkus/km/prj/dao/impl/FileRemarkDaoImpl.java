package com.linkus.km.prj.dao.impl;

import com.linkus.base.db.mongo.MongoDaoSupport;
import com.linkus.base.util.StringUtil;
import com.linkus.km.prj.dao.IFileRemarkDao;
import com.linkus.km.prj.model.TeFileRemark;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("FileRemarkDaoImpl")
public class FileRemarkDaoImpl extends MongoDaoSupport<TeFileRemark> implements IFileRemarkDao {

	@Override
	public List<TeFileRemark> queryFileRemarkByFileId(ObjectId fileId) {
		Query query = new Query();
		if (StringUtil.isNotNull(fileId))
			query.addCriteria(Criteria.where("fileId").is(fileId));
		query.with(Sort.by(Direction.DESC, "addTime"));
		List<TeFileRemark> list = find(query);
		return list;
	}

	@Override
	public List<TeFileRemark> queryFileRemarkByFileIdAndParentId(ObjectId fileId, ObjectId parentId) {
		Query query = new Query();
		Criteria criteria = new Criteria();
		criteria.and("fileId").is(fileId);
		criteria.and("_id").ne("ObjectId()");
		if(StringUtil.isNull(parentId)){
			criteria.and("parentId").is(null);
		}else{
			criteria.and("parentId").is(parentId);
		}
		query.addCriteria(criteria);
		query.with(Sort.by(Direction.DESC, "addTime"));
		List<TeFileRemark> list = find(query);
		return list;
	}

	@Override
	public List<Map> queryRemarkCount(List<ObjectId> baseIds, List<ObjectId> fileIds) {
		
		Aggregation agg = null;
		Criteria criteria = new Criteria();
		criteria.and("fileBaseId").in(baseIds);
		criteria.and("isValid").is(true);
		if(null != fileIds){
			criteria.and("fileId").in(fileIds);
		}
		agg = Aggregation.newAggregation(
				Aggregation.match(criteria),
				//Aggregation.sort(Sort.by(Direction.DESC, "count")),
				Aggregation.group("fileId").count().as("count")
		);
		return mongoTemplate.aggregate(agg, "fileRemark", Map.class).getMappedResults();
	}
}
