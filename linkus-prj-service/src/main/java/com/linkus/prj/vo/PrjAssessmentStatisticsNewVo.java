package com.linkus.prj.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import lombok.Data;
import lombok.With;
import org.bson.types.ObjectId;

@Data
@HeadRowHeight(25)
public class PrjAssessmentStatisticsNewVo {
	@ExcelIgnore
	private ObjectId id;
	@ExcelIgnore
	private ObjectId prjId;
	@ExcelProperty({"BU"})
	private String sbuName;
	@ExcelProperty({"大区"})
	private String bigRegionName;
	@ExcelProperty({"区域"})
	private String regionName;
	@ExcelProperty({"工程部"})
	private String engDept;
	@ExcelProperty({"省份"})
	private String provName;
	@ExcelProperty({"项目集编码"})
	private String prjCode;
	@ExcelProperty({"项目集名称"})
	private String prjName;
	@ExcelProperty({"分类"})
	private String levelName;
	@ExcelProperty({"项目经理"})
	private String pm;
	@ExcelProperty({"产品线"})
	private String srdName;
	@ExcelProperty({"实际上线月份"})
	private String actualOnlineDate;
	@ExcelProperty({"实际交维/验收月"})
	private String actualOmDate;
	@ExcelProperty({"项目集关闭日期"})
	private String actualCloseDate;
	@ExcelProperty({"考评状态"})
	private String kpStatus;
	@ExcelProperty({"过程考评得分","考评最大月"})
	private String processMaxYm;
	@ExcelProperty({"过程考评得分","项目基础得分"})
	private String processPrjBasePoint;
	@ExcelProperty({"过程考评得分","附加绩效"})
	private String processAdditPoint;
	@ExcelProperty({"过程考评得分","项目得分"})
	private String processPoint;
	@ExcelProperty({"过程考评得分","责任总监得分"})
	private String processPointSm;
	@ExcelProperty({"过程考评得分","备注"})
	private String processDesc;

	@ExcelProperty({"初评/阶段得分","项目基础得分"})
	private String preliminaryAndPhasePrjBasePoint;
	@ExcelProperty({"初评/阶段得分","附加绩效"})
	private String preliminaryAndPhaseAdditPoint;
	@ExcelProperty({"初评/阶段得分","项目得分"})
	private String preliminaryAndPhasePoint;
	@ExcelProperty({"初评/阶段得分","责任总监得分"})
	private String preliminaryAndPhasePointSm;
	@ExcelProperty({"初评/阶段得分","备注"})
	private String preliminaryAndPhaseDesc;

	@ExcelProperty({"终评/结果得分","项目基础得分"})
	private String finalAndResultPrjBasePoint;
	@ExcelProperty({"终评/结果得分","附加绩效"})
	private String finalAndResultAdditPoint;
	@ExcelProperty({"终评/结果得分","项目得分"})
	private String finalAndResultPoint;
	@ExcelProperty({"终评/结果得分","责任总监得分"})
	private String finalAndResultPointSm;
	@ExcelProperty({"终评/结果得分","备注"})
	private String finalAndResultsDesc;




}
