package com.linkus.prj.vo;

import org.bson.types.ObjectId;

public class EmpSkillTestApplyVo {
	
	private ObjectId  id;		//报名id,来自 sysUserApply.id
	private String  typeName;	//来自 sysUserPfm.type
	private String  addTime;    
	private String  name;       //姓名,来自 sysUserPfm. emp
	private String  nt;			//NT,来自 sysUserPfm.emp
	private String  deptName;	//部门,来自 sysUserPfm.  将text1/text2/text3/text4进行拼接
	private String  postName;   //岗位,如果status.cid为ObjectId("6131dc901f72fb02cecd185e")，则为非认证岗位，否则为认证岗位
	private ObjectId  pfmId;    //报名id,来自 sysUserPfm 对应_id
	private String  bizName;    //考试场次,来自 sysUserPfm 对应biz.name
	private ObjectId  bizId;    //考试场次,来自 sysUserPfm 对应biz.cid
	private String  subject;    //考试科目,来自 sysUserPfm 对应itemInfo.item.name
	private ObjectId  subjectCid;    //考试科目,来自 sysUserPfm 对应itemInfo.item.cid
	private String  score;      //考试得分,来自 sysUserPfm.score 
	private String  pfmDesc;	 //报名说明,来自 sysUserPfm.desc
	private String  testStatus;  //认证状态 ,来自 sysUserPfm.status.name
	private String  managerCheckUser;//经理审批人
	private String  managerCheckTime;//经理审批时间
	private String  mmdCheckUser;//MMD审批人
	private String  mmdCheckTime;//MMD审批时间
	private String  defName;//报名状态 sysUserPfm.def.name
	private String  text5;//应通过月份 sysUserPfm.text5
	public ObjectId getId() {
		return id;
	}
	public void setId(ObjectId id) {
		this.id = id;
	}
	public String getTypeName() {
		return typeName;
	}
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}
	public String getAddTime() {
		return addTime;
	}
	public void setAddTime(String addTime) {
		this.addTime = addTime;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getNt() {
		return nt;
	}
	public void setNt(String nt) {
		this.nt = nt;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getPostName() {
		return postName;
	}
	public void setPostName(String postName) {
		this.postName = postName;
	}
	public String getSubject() {
		return subject;
	}
	public void setSubject(String subject) {
		this.subject = subject;
	}
	public String getScore() {
		return score;
	}
	public void setScore(String score) {
		this.score = score;
	}
	public String getPfmDesc() {
		return pfmDesc;
	}
	public void setPfmDesc(String pfmDesc) {
		this.pfmDesc = pfmDesc;
	}
	public String getTestStatus() {
		return testStatus;
	}
	public void setTestStatus(String testStatus) {
		this.testStatus = testStatus;
	}
	public String getManagerCheckUser() {
		return managerCheckUser;
	}
	public void setManagerCheckUser(String managerCheckUser) {
		this.managerCheckUser = managerCheckUser;
	}
	public String getManagerCheckTime() {
		return managerCheckTime;
	}
	public void setManagerCheckTime(String managerCheckTime) {
		this.managerCheckTime = managerCheckTime;
	}
	public String getMmdCheckUser() {
		return mmdCheckUser;
	}
	public void setMmdCheckUser(String mmdCheckUser) {
		this.mmdCheckUser = mmdCheckUser;
	}
	public String getMmdCheckTime() {
		return mmdCheckTime;
	}
	public void setMmdCheckTime(String mmdCheckTime) {
		this.mmdCheckTime = mmdCheckTime;
	}
	public String getBizName() {
		return bizName;
	}
	public void setBizName(String bizName) {
		this.bizName = bizName;
	}
	public ObjectId getPfmId() {
		return pfmId;
	}
	public ObjectId getBizId() {
		return bizId;
	}
	public ObjectId getSubjectCid() {
		return subjectCid;
	}
	public void setPfmId(ObjectId pfmId) {
		this.pfmId = pfmId;
	}
	public void setBizId(ObjectId bizId) {
		this.bizId = bizId;
	}
	public void setSubjectCid(ObjectId subjectCid) {
		this.subjectCid = subjectCid;
	}
	public String getDefName() {
		return defName;
	}
	public String getText5() {
		return text5;
	}
	public void setDefName(String defName) {
		this.defName = defName;
	}
	public void setText5(String text5) {
		this.text5 = text5;
	}
	
}
