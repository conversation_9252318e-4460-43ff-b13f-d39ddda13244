package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * @Author: feizj
 * @Description: 效能偏差偏差

 */
@Data
public class PrjEffectDeviationVo {

    @ExcelIgnore()
    private ObjectId id;

    @ExcelIgnore()
    private ObjectId typeId;

    @ExcelIgnore()
    private Integer lineNo;

    @ExcelIgnore()
    private ObjectId provId;

    @ExcelIgnore
    private ObjectId prjId;

    @ExcelIgnore()
    private ObjectId causeTypeId;

    @ExcelIgnore()
    private ObjectId causeSubTypeId;

    @ExcelProperty({"是否填写"})
    private String isFillIn;

    @ExcelProperty({"发起邮件反馈"})
    private String isMailed;

    @ExcelProperty({"大区"})
    private String bigRegion;

    @ExcelProperty({"区域"})
    private String region;

    @ExcelProperty({"工程部"})
    private String engDept;

    @ExcelProperty({"省份"})
    private String prov;

    @ExcelProperty({"跟踪人"})
    private String trackUserName;

    @ExcelProperty({"预测流水号"})
    private String subOrderCode;

    @ExcelProperty({"净销售额(K)"})
    private String netSaleAmt;

    @ExcelProperty({"项目编码"})
    private String subPrjCode;

    @ExcelProperty({"项目集编码"})
    private String prjCode;

    @ExcelProperty({"项目集名称"})
    private String prjName;
    @ExcelProperty({"项目集状态"})
    private String statusName;

    @ExcelProperty({"项目集创建日期"})
    private String addTime;

    @ExcelProperty({"项目集关闭日期"})
    private String prjCloseDate;

    @ExcelProperty({"基准发布日期"})
    private String bmkPublishDate;

    @ExcelProperty({"项目经理"})
    private String pmUserName;

    @ExcelProperty({"分类"})
    private String levelName;

    @ExcelProperty({"POM状态"})
    private String subPomStatus;

    @ExcelProperty({"管理月份"})
    private String ym;

    @ExcelProperty({"ABP净销售额(K)"})
    private Double abpSalesM;

    @ExcelProperty({"ABP GM(%)"})
    private String abpGm;

    @ExcelProperty({"基准预测收入(K)"})
    private Double bmkForecastIncome;

    @ExcelProperty({"基准GM(%)"})
    private String bmkGm;
    @ExcelIgnore
    private Double gmDiff;
    @ExcelIgnore
    private Double gmDiffRaate;

    @ExcelProperty({"ABP正式"})
    private Double abpOopEmpNum;

    @ExcelProperty({"ABP外包"})
    private Double abpOopOsEmpNum;

    @ExcelProperty({"基准正式"})
    private Double sumEmpNum;

    @ExcelProperty({"基准外包"})
    private Double sumOsEmpNum;

    @ExcelProperty({"正式+外包偏差"})
    private Double diff;

    @ExcelProperty({"正式+外包偏差率（%）"})
    private String diffRate;

    @ExcelProperty({"是否偏差"})
    private String isOverSpend;

    @ExcelProperty({"人力偏差情况"})
    private String hrDvtDesc;

    @ExcelProperty({"偏差原因分析"})
    private String desc;

    @ExcelProperty({"整改措施"})
    private String rectifyAction;

    @ExcelProperty({"整改完成日期"})
    private String rectifyEndDate;

    @ExcelProperty({"原因大类"})
    private String causeType;
    @ExcelProperty({"病因"})
    private String causeSubType;
    @ExcelProperty({"药方"})
    private String causeSub2Type;
    @ExcelProperty({"是否分析"})
    private String hasAnalysis;
    @ExcelIgnore()
    private String effectHasConform;
    @ExcelProperty({"原因分析"})
    private String notes;
    @ExcelProperty({"整改措施-评审"})
    private String rectifyActionReviewed;
    @ExcelProperty({"状态"})
    private String rectifyStatus;
    @ExcelProperty({"严重程度"})
    private String colorSign;
    @ExcelIgnore()
    private Double blIncomePer100;//基准“百元人工-收入”
    @ExcelIgnore()
    private Double actIncomePer100; //实际“百元人工-收入”
    @ExcelIgnore()
    private Double blIncome;//基准收入
    @ExcelIgnore()
    private Double actIncome;//实际收入
    @ExcelIgnore()
    private Double incomeDiff;
    @ExcelIgnore()
    private Double incomeDiffRate;

    @ExcelIgnore()
    private Double blRgFee;//基准人工费
    @ExcelIgnore()
    private Double actRgFee;//实际人工费
    @ExcelIgnore()
    private Double rgFeeDiff;
    @ExcelIgnore()
    private Double rgFeeDiffRate;
    @ExcelIgnore
    private Double effectDeviation;//效能偏差
    @ExcelIgnore
    private Double effectFactorDiff;//实际VS基准效能因子-差值
    @ExcelIgnore
    private String symptom;//病症
    @ExcelIgnore
    private ObjectId causeSub2TypeId;//药方
    @ExcelIgnore
    private List<String> descList;
    @ExcelIgnore
    private List<String> rectifyActionList;

    //合计
    @ExcelIgnore
    private Double blAllCostFee;
    //合计
    @ExcelIgnore
    private Double actAllCostFee;
    @ExcelIgnore
    private Double allCostFeeDiff;
    @ExcelIgnore
    private Double allCostFeeDiffRate;

    //正式
    @ExcelIgnore
    private Double blEmpFee;
    //正式
    @ExcelIgnore
    private Double actEmpFee;
    @ExcelIgnore
    private Double empFeeDiff;
    @ExcelIgnore
    private Double empFeeDiffRate;

    //外包
    @ExcelIgnore
    private Double blOsEmpFee;
    //外包
    @ExcelIgnore
    private Double actOsEmpFee;
    @ExcelIgnore
    private Double osEmpFeeDiff;
    @ExcelIgnore
    private Double osEmpFeeDiffRate;

    //实习
    @ExcelIgnore
    private Double blTraineeFee;
    //实习
    @ExcelIgnore
    private Double actTraineeFee;
    @ExcelIgnore
    private Double traineeFeeDiff;
    @ExcelIgnore
    private Double traineeFeeDiffRate;

    //技术分包
    @ExcelIgnore
    private Double blJsfbFee;
    //技术分包
    @ExcelIgnore
    private Double actJsfbFee;
    @ExcelIgnore
    private Double jsfbFeeDiff;
    @ExcelIgnore
    private Double jsfbFeeDiffRate;

    //直接费用合计
    @ExcelIgnore
    private Double blDirectFee;
    @ExcelIgnore
    private Double actDirectFee;
    @ExcelIgnore
    private Double directFeeDiff;
    @ExcelIgnore
    private Double directFeeDiffRate;

    //差旅费
    @ExcelIgnore
    private Double blTravelFee;
    //差旅费
    @ExcelIgnore
    private Double actTravelFee;
    @ExcelIgnore
    private Double travelFeeDiff;
    @ExcelIgnore
    private Double travelFeeDiffRate;

    //餐费
    @ExcelIgnore
    private Double blDiningFee;
    //餐费
    @ExcelIgnore
    private Double actDiningFee;
    @ExcelIgnore
    private Double diningFeeDiff;
    @ExcelIgnore
    private Double diningFeeDiffRate;

    //其它费
    @ExcelIgnore
    private Double blOtherFee;
    //其它费
    @ExcelIgnore
    private Double actOtherFee;
    @ExcelIgnore
    private Double otherFeeDiff;
    @ExcelIgnore
    private Double otherFeeDiffRate;

    //人力资源合计
    @ExcelIgnore
    private Double blAllEmpNum;
    @ExcelIgnore
    private Double actAllEmpNum;
    @ExcelIgnore
    private Double allEmpNumDiff;
    @ExcelIgnore
    private Double allEmpNumDiffRate;

    //人力资源正式
    @ExcelIgnore
    private Double blEmpNum;
    //人力资源正式
    @ExcelIgnore
    private Double actEmpNum;
    @ExcelIgnore
    private Double empNumDiff;
    @ExcelIgnore
    private Double empNumDiffRate;

    //人力资源外包
    @ExcelIgnore
    private Double blOsEmpNum;
    //人力资源外包
    @ExcelIgnore
    private Double actOsEmpNum;
    @ExcelIgnore
    private Double osEmpNumDiff;
    @ExcelIgnore
    private Double osEmpNumDiffRate;
    @ExcelProperty
    private String firstVerPlanEndDate;
    @ExcelProperty
    private String needCheck;
    @ExcelProperty
    private String mgtAction;
}
