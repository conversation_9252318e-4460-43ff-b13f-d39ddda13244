package com.linkus.prj.model.vo;

import java.util.List;
import java.util.Map;

public class CertifyStatisticChartVo {
	private    List<String> text2Line;//维度-区域
	private    Map<String, Integer>     text2CertifyNumMap;//			左-区域/右-应认证人数
	private    Map<String, Integer>     text2CertifyPassNumMap;//		左-区域/右-通过人数
	private    Map<String, String>      text2CertifyPassRatioMap;//		左-区域/右-认证通过率
	public List<String> getText2Line() {
		return text2Line;
	}
	public Map<String, Integer> getText2CertifyNumMap() {
		return text2CertifyNumMap;
	}
	public Map<String, Integer> getText2CertifyPassNumMap() {
		return text2CertifyPassNumMap;
	}
	public Map<String, String> getText2CertifyPassRatioMap() {
		return text2CertifyPassRatioMap;
	}
	public void setText2Line(List<String> text2Line) {
		this.text2Line = text2Line;
	}
	public void setText2CertifyNumMap(Map<String, Integer> text2CertifyNumMap) {
		this.text2CertifyNumMap = text2CertifyNumMap;
	}
	public void setText2CertifyPassNumMap(Map<String, Integer> text2CertifyPassNumMap) {
		this.text2CertifyPassNumMap = text2CertifyPassNumMap;
	}
	public void setText2CertifyPassRatioMap(
			Map<String, String> text2CertifyPassRatioMap) {
		this.text2CertifyPassRatioMap = text2CertifyPassRatioMap;
	}
	
	
	

}
