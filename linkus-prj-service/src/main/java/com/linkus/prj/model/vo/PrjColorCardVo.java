package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.linkus.prj.model.TePrjDvtProgress;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.bson.types.ObjectId;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PrjColorCardVo {
    @ExcelIgnore()
    private ObjectId id;

    @ExcelIgnore()
    private ObjectId typeId;

    @ExcelIgnore
    private String ym;
    @ExcelIgnore
    private ObjectId prjId;

    @ExcelIgnore
    private ObjectId provId;

    @ExcelIgnore
    private ObjectId causeTypeId;

    @ExcelIgnore
    private List<TePrjDvtProgress> progress;

    @ExcelProperty({"是否填写"})
    private String isFillIn;

    @ExcelProperty({"大区"})
    private String bigRegion;

    @ExcelProperty({"区域"})
    private String region;

    @ExcelProperty({"工程部"})
    private String engDept;

    @ExcelProperty({"省份"})
    private String prov;

    @ExcelProperty({"预测流水号"})
    private String subOrderCode;

    @ExcelProperty({"项目编码"})
    private String prjCode;

    @ExcelProperty({"项目名称"})
    private String prjName;

    @ExcelProperty({"项目分类"})
    private String levelName;

    @ExcelProperty({"项目经理"})
    private String pmUserName;


    @ExcelProperty({"签约日期"})
    private String signDate;

    @ExcelProperty({"签约金额"})
    private Double signAmount;

    @ExcelProperty({"项目里程碑"})
    private String prjMst;

    @ExcelProperty({"里程碑进度款"})
    private Double prjMstAmt;

    @ExcelProperty({"基准版计划完成日期"})
    private String prjMstPed;

    @ExcelProperty({"相对计算日期"})
    private String relativeDate;

    @ExcelProperty({"参考计算日期"})
    private String refDate;

    @ExcelProperty({"延期天数"})
    private Integer delayDays;

    @ExcelProperty({"挂牌阶段"})
    private String colorCardType;

    @ExcelProperty({"挂牌类型"})
    private String colorCard;

    @ExcelProperty({"产品线"})
    private String pl;

    @ExcelProperty({"是否已摘牌"})
    private String isDelisted;

    @ExcelProperty({"挂牌原因"})
    private String listReason;



    @ExcelProperty({"计划摘牌时间"})
    private String planDelistDate;

    @ExcelProperty({"原因分类"})
    private String causeType;

    @ExcelProperty({"原因分析"})
    private String notes;
    //首版计划完成时间
    @ExcelIgnore
    private String firstVerPlanEndDate;
    //实际完成时间
    @ExcelIgnore
    private String actEndDate;
    //整改措施
    @ExcelIgnore
    private String rectifyActionReviewed;

}
