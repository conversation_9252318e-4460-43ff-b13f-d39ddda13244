package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.Map;

@Data
@HeadRowHeight(25)
public class SrdLevel1SystemScorePredictVo {
    @ExcelIgnore
    private ObjectId id;
    @ExcelProperty({"SRD一级"})
    private String srd;
    @ExcelProperty({"综合得分"})
    private Double score;
    @ExcelIgnore
    private ObjectId importantPrjId;
    @ExcelProperty({"${important}","项目"})
    private Integer importantPrj;
    @ColumnWidth(12)
    @ExcelProperty({"${important}","得分"})
    private Double importantPrjScore;
    @ExcelIgnore
    private ObjectId notImportantPrjId;
    @ExcelProperty({"${notImportant}","项目"})
    private Integer notImportantPrj;
    @ColumnWidth(15)
    @ExcelProperty({"${notImportant}","得分"})
    private Double notImportantPrjScore;
    @ExcelIgnore
    private ObjectId systemExecuteId;
    @ExcelIgnore
    private Integer systemExecutePrjNum;
    @ExcelProperty({"${systemExecute}","得分 "})
    private Double systemExecuteScore;
    @ColumnWidth(15)
    @ExcelProperty({"${systemExecute}","扣分 "})
    private Double systemExecuteLostScore;
    @ExcelIgnore
    private String systemExecuteLostScoreDesc;

    @ExcelIgnore
    private ObjectId lifeLongId;
    @ExcelProperty({"故障追责","扣分"})
    private Double lifeLongLostScore;
    @ExcelProperty({"故障追责","扣分说明"})
    private String lifeLongLostScoreDesc;

    @ExcelIgnore
    private ObjectId deliverValueId;
    @ExcelProperty({"价值交付","扣分"})
    private Double deliverValueLostScore;
    @ExcelProperty({"价值交付","扣分说明"})
    private String deliverValueLostScoreDesc;

    @ExcelIgnore
    private ObjectId complaintId;
    @ExcelProperty({"投诉追责","扣分"})
    private Double complaintLostPoint;
    @ExcelProperty({"投诉追责","扣分说明"})
    private String complaintLostPointDesc;
    @ExcelIgnore
    private ObjectId auditId;
    @ExcelProperty({"审计追责","扣分"})
    private Double auditLostPoint;
    @ExcelProperty({"审计追责","扣分说明"})
    private String auditLostPointDesc;

    @ExcelIgnore
    private List<Map> importPrjList;
    @ExcelIgnore
    private List<Map> notImportPrjList;
    @ExcelIgnore
    private List<Map> systemExecutePrjList;
}
