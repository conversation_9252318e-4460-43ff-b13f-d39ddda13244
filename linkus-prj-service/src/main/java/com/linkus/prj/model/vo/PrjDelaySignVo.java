package com.linkus.prj.model.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.bson.types.ObjectId;

import java.util.List;

@Data
public class PrjDelaySignVo {

    @ExcelIgnore()
    private ObjectId id;

    @ExcelIgnore()
    private ObjectId typeId;

    @ExcelIgnore
    private ObjectId prjId;

    @ExcelProperty({"是否填写"})
    private String isFillIn;

    @ExcelProperty({"发起邮件反馈"})
    private String isMailed;

    @ExcelProperty({"大区"})
    private String bigRegion;

    @ExcelProperty({"区域"})
    private String region;

    @ExcelProperty({"工程部"})
    private String engDept;

    @ExcelIgnore
    private ObjectId provId;
    @ExcelProperty({"省份"})
    private String prov;

    @ExcelProperty({"预测流水号"})
    private String subOrderCode;

    @ExcelProperty({"净销售额(K)"})
    private String netSaleAmt;

    @ExcelProperty({"项目编码"})
    private String prjCode;

    @ExcelProperty({"项目名称"})
    private String prjName;

    @ExcelProperty({"项目经理"})
    private String pmUserName;

    @ExcelProperty({"分类"})
    private String levelName;

    @ExcelProperty({"提前立项日期"})
    private String oaPrjStartDate;

    @ExcelProperty({"管理月份"})
    private String ym;

    @ExcelProperty({"立项时长"})
    private Integer projectedDays;

    @ExcelProperty({"累计成本(K)"})
    private Double totalFee;

    @ExcelIgnore
    private List<String> descList;
    @ExcelProperty({"未签约原因"})
    private String desc;

    @ExcelIgnore
    private List<String> rectifyActionList;
    @ExcelProperty({"解决措施"})
    private String rectifyAction;

    @ExcelProperty({"计划解决日期"})
    private String rectifyEndDate;

    @ExcelIgnore
    private ObjectId causeTypeId;
    @ExcelProperty({"原因分类"})
    private String causeType;

    @ExcelProperty({"原因分析"})
    private String notes;

    @ExcelProperty({"严重程度"})
    private String colorSign;

    @ExcelProperty({"首次通报月份"})
    private String firstNotifyYm;
}
