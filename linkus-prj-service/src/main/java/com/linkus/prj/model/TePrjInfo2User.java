package com.linkus.prj.model;

import org.bson.types.ObjectId;

public class TePrjInfo2User {

	private ObjectId userId;
	private String loginName;
	private String userName;
	private String jobCode;

	public ObjectId getUserId() {
		return userId;
	}

	public void setUserId(ObjectId userId) {
		this.userId = userId;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getJobCode() {
		return jobCode;
	}

	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) return true;
		if (o == null || getClass() != o.getClass()) return false;

		TePrjInfo2User that = (TePrjInfo2User) o;

		if (userId != null ? !userId.equals(that.userId) : that.userId != null) return false;
		if (loginName != null ? !loginName.equals(that.loginName) : that.loginName != null) return false;
		if (userName != null ? !userName.equals(that.userName) : that.userName != null) return false;
		return jobCode != null ? jobCode.equals(that.jobCode) : that.jobCode == null;
	}

	@Override
	public int hashCode() {
		int result = userId != null ? userId.hashCode() : 0;
		result = 31 * result + (loginName != null ? loginName.hashCode() : 0);
		result = 31 * result + (userName != null ? userName.hashCode() : 0);
		result = 31 * result + (jobCode != null ? jobCode.hashCode() : 0);
		return result;
	}
}
