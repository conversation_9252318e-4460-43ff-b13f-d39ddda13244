package com.linkus.prj.model;

import java.io.Serializable;

import com.linkus.base.db.mongo.model.TeIdNameCn;

/**
 * [订单和合同产品线信息：对应mongo表中的abpPj和abpOrder中的pls数据]
 *
 * <AUTHOR> [leijb]
 * @version : [v1.0]
 * @createTime : [2022/5/30 13:51]
 */
public class TeAbpPl implements Serializable {
	private static final long serialVersionUID = -5041771569652549078L;

	private Integer productId;
	private String productCode;
	private String buCode;
	private Double pct;
	private Double amt;
	private TeIdNameCn pl;
	private Double pctNew;
	private String orgSplitType;
	private String coordSbuType;
	private Boolean isValid;

	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getBuCode() {
		return buCode;
	}

	public void setBuCode(String buCode) {
		this.buCode = buCode;
	}

	public Double getPct() {
		return pct;
	}

	public void setPct(Double pct) {
		this.pct = pct;
	}

	public Double getAmt() {
		return amt;
	}

	public void setAmt(Double amt) {
		this.amt = amt;
	}

	public TeIdNameCn getPl() {
		return pl;
	}

	public void setPl(TeIdNameCn pl) {
		this.pl = pl;
	}

	public Double getPctNew() {
		return pctNew;
	}

	public void setPctNew(Double pctNew) {
		this.pctNew = pctNew;
	}

	public String getOrgSplitType() {
		return orgSplitType;
	}

	public void setOrgSplitType(String orgSplitType) {
		this.orgSplitType = orgSplitType;
	}

	public String getCoordSbuType() {
		return coordSbuType;
	}

	public void setCoordSbuType(String coordSbuType) {
		this.coordSbuType = coordSbuType;
	}

	public Boolean getIsValid() {
		return isValid;
	}

	public void setIsValid(Boolean isValid) {
		this.isValid = isValid;
	}

}
