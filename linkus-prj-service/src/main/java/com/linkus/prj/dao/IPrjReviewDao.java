package com.linkus.prj.dao;

import com.linkus.base.db.mongo.MongoDao;
import com.linkus.prj.model.TePrjReview;
import org.bson.types.ObjectId;

import java.util.List;

public interface IPrjReviewDao extends MongoDao<TePrjReview>{
	List<TePrjReview> getPrjReviewsByPrjIdAndReviewType(ObjectId prjId, String typeCode);

	List<TePrjReview> getPrjReviewsByPrjIds(List<ObjectId> prjIds, String typeCode);

	List<TePrjReview> getPrjReviewsByPrjIdsAndMonth(List<ObjectId> prjIds, String typeCode,String startMonth,String endMonth);
}
