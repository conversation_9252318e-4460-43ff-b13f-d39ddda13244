package com.linkus.prj.service;

import com.linkus.base.util.PageBean;
import com.linkus.prj.model.TePrjBudget;
import com.linkus.prj.model.TePrjInfo;
import com.linkus.prj.model.TePrjInfoPrjBmks;
import com.linkus.prj.model.vo.*;
import com.linkus.prj.vo.DevManageStatisVo;
import com.linkus.prj.vo.PrjDvtMgtMergeVo;
import com.linkus.prj.vo.PrjDvtMgtVo;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

public interface IPrjDvtMgtMergeService {
    PageBean getPrjDvtMgtMergeData(PrjDvtMgtMergeVo vo,TeSysUser loginUser);
    void exportPrjDvtMgtMergeData(PrjDvtMgtMergeVo vo, TeSysUser loginUser,HttpServletResponse response)throws IOException;
    void sendPrjDvtMgtMergeDataMail(PrjDvtMgtVo vo);
    Map<ObjectId,Integer> countPrjDvtMgtSubscript(ObjectId prjId,String ym);
    List<PrjDelaySignVo> getDelaySignDvtMgtDetailData(ObjectId prjId,String ym);
    List<PrjDvtMgtCostOverEfVo> getProgressDelayDvtMgtDetailData(ObjectId prjId, String ym);
    List<PrjColorCardVo> getPrjMstDvtMgtDetailData(ObjectId prjId, String ym);
    PrjDvtMgtCostOverABCGVo getPrjCostDvtMgtDetailData(ObjectId prjId, String ym);
    PrjEffectDeviationVo getEffectDvtMgtDetailData(ObjectId prjId, String ym);
    PrjEffectDeviationVo getImplPlanDvtMgtDetailData(ObjectId prjId, String ym);
}
