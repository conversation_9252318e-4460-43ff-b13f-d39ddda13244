package com.linkus.prj.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_LT;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.*;
import com.linkus.itf.api.client.ItfAiPrjMstClient;
import com.linkus.itf.api.model.ItfAiPrjMst;
import com.linkus.mail.param.MailInfo;
import com.linkus.mail.service.IMailService;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.IPrjDvtMgtDao;
import com.linkus.prj.dao.IPrjInfoDao;
import com.linkus.prj.dao.IPrjPlanTaskDao;
import com.linkus.prj.model.*;
import com.linkus.prj.model.vo.*;
import com.linkus.prj.service.*;
import com.linkus.prj.util.CellStyleHandler;
import com.linkus.prj.vo.PrjDvtMgtMergeVo;
import com.linkus.prj.vo.PrjDvtMgtVo;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import com.mongodb.client.MongoCursor;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfig;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("PrjDvtMgtMergeServiceImpl")
public class PrjDvtMgtMergeServiceImpl extends PrjDvtMgtServiceImpl implements IPrjDvtMgtMergeService {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IPrjInfoDao prjInfoDao;
    @Autowired
    private ISysDefRoleUserDao sysDefRoleUserDao;
    @Autowired
    private IPrjInfoService prjInfoService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IPrjDvtMgtDao prjDvtMgtDao;
    @Autowired
    private FreeMarkerConfig freeMarkerConfig;
    @Autowired
    private IMailService mailService;
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private IPrjPlanTaskDao prjPlanTaskDao;
    @Autowired
    private ItfAiPrjMstClient itfAiPrjMstClient;

    @Override
    public PageBean getPrjDvtMgtMergeData(PrjDvtMgtMergeVo vo,TeSysUser loginUser) {
        PageBean pageBean = new PageBean();
        //权限
        if (StringUtil.isNull(vo.getSbuId())){
            vo.setSbuId(PrjConstant.BU_CODE_BSC);
        }
        List<ObjectId> prjIds = getAuth(vo,loginUser);
        //查询语句
        List<Document> sql = new ArrayList<>();
        //查询项目集
        Document match = new Document();
        match.append(DFN.common_isValid.n(),new Document("$ne",false));
        match.append(DFN.prjInfo__sbuId.n(), vo.getSbuId());
        match.append(DFN.prjInfo__isPrjSet.n(),true);
        if (CollectionUtils.isNotEmpty(prjIds)){
            match.append(DFN.prjInfo__prjId.n(),new Document("$in",prjIds));
        }
        if (StringUtil.isNotNull(vo.getPrjCode())){
            match.append(DFN.prjInfo__prjCode.n(),new Document("$regex",vo.getPrjCode()));
        }
        if (StringUtil.isNotNull(vo.getPrjName())){
            match.append(DFN.prjInfo__prjName.n(),new Document("$regex",vo.getPrjName()));
        }
        if (StringUtil.isNotNull(vo.getStatusName())){
            match.append(DFN.prjInfo__status.dot(DFN.common_name).n(),new Document("$regex",vo.getStatusName()));
        }
        if (StringUtil.isNotNull(vo.getLevelName())){
            match.append(DFN.prjInfo__level.dot(DFN.common_name).n(),new Document("$regex",vo.getLevelName()));
        }
        List<Document> andMatch = new ArrayList<>();
        if (StringUtil.isNotNull(vo.getPmUserName())){
            List<Document> orMatch = new ArrayList<>();
            orMatch.add(new Document(DFN.prjInfo__pmUser.dot(DFN.common_userName).n(),new Document("$regex",vo.getPmUserName())));
            orMatch.add(new Document(DFN.prjInfo__pmUser.dot(DFN.common_loginName).n(),new Document("$regex",vo.getPmUserName())));
            andMatch.add(new Document("$or",orMatch));
        }
        if (StringUtil.isNotNull(vo.getTrackUserName())){
            List<Document> orMatch = new ArrayList<>();
            orMatch.add(new Document(DFN.prjInfo__trackUser.dot(DFN.common_userName).n(),new Document("$regex",vo.getTrackUserName())));
            orMatch.add(new Document(DFN.prjInfo__trackUser.dot(DFN.common_loginName).n(),new Document("$regex",vo.getTrackUserName())));
            andMatch.add(new Document("$or",orMatch));
        }
        if (CollectionUtils.isNotEmpty(andMatch)){
            match.append("$and",andMatch);
        }
        sql.add(new Document("$match",match));
        //关联省份
        Document lookUpProv = new Document();
        lookUpProv.append("from","sysDef").append("localField","prov.cid").append("foreignField","_id").append("as","prov");
        sql.add(new Document("$lookup",lookUpProv));
        sql.add(new Document("$unwind","$prov"));
        //映射字段
        Document project = new Document();
        project.append("prjId","$prjId")
                .append("prjName","$prjName")
                .append("prjCode","$prjCode")
                .append("subPrjs","$subPrjs")
                .append("pmUser","$pmUser")
                .append("trackUser","$trackUser")
                .append("levelName","$level.name")
                .append("statusName","$status.name")
                .append("provName","$prov.defName")
                .append("netSaleAmt","$netSaleAmt")
                .append("bigRegionName",
                        new Document("$reduce",new Document(
                                "input","$prov.cndtItems")
                                .append("initialValue","")
                                .append("in",new Document(
                                        "$concat", Arrays.asList("$$value",
                                        new Document("$cond",
                                                new Document("if",
                                                        new Document("$eq",
                                                                Arrays.asList("$$this.codeName","abpBigRegion")))
                                                        .append("then","$$this.name").append("else","")))
                                ))
                        ))
                .append("regionName",
                        new Document("$reduce",new Document(
                                "input","$prov.cndtItems")
                                .append("initialValue","")
                                .append("in",new Document(
                                        "$concat", Arrays.asList("$$value",
                                        new Document("$cond",
                                                new Document("if",
                                                        new Document("$eq",
                                                                Arrays.asList("$$this.codeName","abpRegion")))
                                                        .append("then","$$this.name").append("else","")))
                                ))
                        ))
                .append("engDeptName",
                        new Document("$reduce",new Document(
                                "input","$prov.cndtItems")
                                .append("initialValue","")
                                .append("in",new Document(
                                        "$concat", Arrays.asList("$$value",
                                        new Document("$cond",
                                                new Document("if",
                                                        new Document("$eq",
                                                                Arrays.asList("$$this.codeName","abpEngDept")))
                                                        .append("then","$$this.name").append("else","")))
                                ))
                        ));
        sql.add(new Document("$project",project));
        //过滤省份、大区、区域
        Document provMatch = new Document();
        if (StringUtil.isNotNull(vo.getBigRegion())){
            provMatch.append("bigRegionName",new Document("$regex",vo.getBigRegion()));
        }
        if (StringUtil.isNotNull(vo.getRegion())){
            provMatch.append("regionName",new Document("$regex",vo.getRegion()));
        }
        if (StringUtil.isNotNull(vo.getEngDept())){
            provMatch.append("engDeptName",new Document("$regex",vo.getEngDept()));
        }
        if (StringUtil.isNotNull(vo.getProv())){
            provMatch.append("provName",new Document("$regex",vo.getProv()));
        }
        if (!provMatch.isEmpty()){
            sql.add(new Document("$match",provMatch));
        }
        //项目集关联偏差数据
        sql.add(new Document("$lookup",new Document("from","prjDvtMgt").append("localField","prjId").append("foreignField","prj.cid").append("as","prjDvtMgt")));
        //子项目关联偏差数据
        sql.add(new Document("$lookup",new Document("from","prjDvtMgt").append("localField","subPrjs.cid").append("foreignField","prj.cid").append("as","otherDvtMgt")));
        //映射字段
        sql.add(new Document("$project",
                new Document("prjId","$prjId")
                        .append("prjName","$prjName")
                        .append("prjCode","$prjCode")
                        .append("subPrjs","$subPrjs")
                        .append("pmUser","$pmUser")
                        .append("trackUser","$trackUser")
                        .append("levelName","$levelName")
                        .append("statusName","$statusName")
                        .append("netSaleAmt","$netSaleAmt")
                        .append("provName","$provName")
                        .append("bigRegionName","$bigRegionName")
                        .append("regionName","$regionName")
                        .append("engDeptName","$engDeptName")
                        .append("prjDvtMgt",new Document("$filter",
                                new Document("input","$prjDvtMgt")
                                        .append("as","item")
                                        .append("cond",new Document("$and",
                                                Arrays.asList(
                                                        new Document("$eq",Arrays.asList("$$item.isValid",true)),
                                                        new Document("$eq",Arrays.asList("$$item.ym",vo.getYm())),
                                                        new Document("$in",Arrays.asList("$$item.type.cid",Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID,PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID,
                                                                PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID,PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID, PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID)))
                                                )))))
                        .append("otherDvtMgt",new Document("$filter",
                                new Document("input","$otherDvtMgt")
                                        .append("as","item")
                                        .append("cond",new Document("$and",
                                                Arrays.asList(
                                                        new Document("$eq",Arrays.asList("$$item.isValid",true)),
                                                        new Document("$eq",Arrays.asList("$$item.ym",vo.getYm())),
                                                        new Document("$in",Arrays.asList("$$item.type.cid",Arrays.asList(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID)))
                                                )))))
        ));
        //映射字段
        sql.add(new Document("$project",
                new Document("prjId","$prjId")
                        .append("prjName","$prjName")
                        .append("prjCode","$prjCode")
                        .append("pmUser","$pmUser")
                        .append("trackUser","$trackUser")
                        .append("levelName","$levelName")
                        .append("statusName","$statusName")
                        .append("netSaleAmt","$netSaleAmt")
                        .append("provName","$provName")
                        .append("bigRegionName","$bigRegionName")
                        .append("regionName","$regionName")
                        .append("engDeptName","$engDeptName")
                        .append("prjDvtMgt",new Document("$concatArrays",Arrays.asList("$prjDvtMgt","$otherDvtMgt")))
        ));
        //映射字段
        sql.add(new Document("$project",
                new Document("prjId","$prjId")
                        .append("prjName","$prjName")
                        .append("prjCode","$prjCode")
                        .append("pmUser","$pmUser")
                        .append("trackUser","$trackUser")
                        .append("levelName","$levelName")
                        .append("statusName","$statusName")
                        .append("netSaleAmt","$netSaleAmt")
                        .append("provName","$provName")
                        .append("bigRegionName","$bigRegionName")
                        .append("regionName","$regionName")
                        .append("engDeptName","$engDeptName")
                        .append("devMgtSize",new Document("$size","$prjDvtMgt"))
                        .append("delaySign",new Document("$split",Arrays.asList(new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",",",new Document("$cond",new Document(
                                                        "if",new Document("$eq",Arrays.asList("$$this.type.cid",PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID)))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))),",")))
                        .append("progressDelay",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$eq",Arrays.asList("$$this.type.cid",PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID)))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))))
                        .append("colorCard",new Document("$split",Arrays.asList(new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",",",new Document("$cond",new Document(
                                                        "if",new Document("$eq",Arrays.asList("$$this.type.cid",PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID)))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))),",")))
                        .append("implPlan",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$eq",Arrays.asList("$$this.type.cid",PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID)))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))))
                        .append("cost",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$in",Arrays.asList("$$this.type.cid",Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID))))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))))
                        .append("effect",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue","")
                                        .append("in",new Document("$concat",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$eq",Arrays.asList("$$this.type.cid",PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID)))
                                                        .append("then","$$this.colorSign").append("else",""))
                                        )))))
                        .append("feedBack",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue",0)
                                        .append("in",new Document("$sum",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$or",Arrays.asList(
                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.desc",0)),0)),
                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.rectifyAction",0)),0)),
                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.rectifyEndDate",0)),0)))))
                                                        .append("then",0).append("else",1))
                                        )))))
                        .append("analysis",new Document("$reduce",
                                new Document("input","$prjDvtMgt")
                                        .append("initialValue",0)
                                        .append("in",new Document("$sum",Arrays.asList(
                                                "$$value",new Document("$cond",new Document(
                                                        "if",new Document("$or",Arrays.asList(
                                                                new Document("$and",Arrays.asList(
                                                                        new Document("$in",Arrays.asList("$$this.type.cid",Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID,PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID,
                                                                                PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID,PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID, PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID))),
                                                                        new Document("$or",Arrays.asList(
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.causeType",0)),0)),
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.causeSubType",0)),0)),
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.causeSub2Type",0)),0))))
                                                                )),
                                                                new Document("$and",Arrays.asList(
                                                                        new Document("$in",Arrays.asList("$$this.type.cid",Arrays.asList(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID))),
                                                                        new Document("$or",Arrays.asList(
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.causeType",0)),0)),
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.notes",0)),0)),
                                                                                new Document("$eq",Arrays.asList(new Document("$ifNull",Arrays.asList("$$this.rdcInfo.firstVerPlanEndDate",0)),0))))
                                                                ))
                                                ))).append("then",0).append("else",1))
                                        )))
                                ))

        ));
        sql.add(new Document("$addFields",
                new Document("feedBackDiff",new Document("$subtract",Arrays.asList("$devMgtSize","$feedBack")))
                        .append("analysisDiff",new Document("$subtract",Arrays.asList("$devMgtSize","$analysis")))));

        Document otherMatch = new Document();
        //严重程度
        if (CollectionUtils.isNotEmpty(vo.getDegree())){
            if (vo.getDegree().contains("empty") && vo.getDegree().size() == 1){
                otherMatch.append("devMgtSize",0);
            }else{
                List<Document> orDoc = new ArrayList<>();
                if (vo.getDegree().contains("empty")){
                    orDoc.add(new Document("devMgtSize",0));
                }
                orDoc.add(new Document("delaySign",new Document("$in",vo.getDegree())));
                orDoc.add(new Document("progressDelay",new Document("$in",vo.getDegree())));
                orDoc.add(new Document("colorCard",new Document("$in",vo.getDegree())));
                orDoc.add(new Document("implPlan",new Document("$in",vo.getDegree())));
                orDoc.add(new Document("cost",new Document("$in",vo.getDegree())));
                orDoc.add(new Document("effect",new Document("$in",vo.getDegree())));
                otherMatch.append("$or",orDoc);
            }
        }

        //签约延期
        if (StringUtil.isNotNull(vo.getDelaySign())){
            if (vo.getDelaySign().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("delaySign",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("delaySign",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //进度延期
        if (StringUtil.isNotNull(vo.getProgressDelay())){
            if (vo.getProgressDelay().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("progressDelay",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("progressDelay",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //色拍跟踪
        if (StringUtil.isNotNull(vo.getColorCard())){
            if (vo.getColorCard().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("colorCard",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("colorCard",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //实施计划偏差
        if (StringUtil.isNotNull(vo.getImplPlan())){
            if (vo.getImplPlan().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("implPlan",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("implPlan",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //成本偏差
        if (StringUtil.isNotNull(vo.getCost())){
            if (vo.getCost().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("cost",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("cost",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //效能偏差
        if (StringUtil.isNotNull(vo.getEffect())){
            if (vo.getEffect().equals(PrjConstant.PRJ_PROGRESS_NORMAL_DESC)){
                otherMatch.append("effect",new Document("$nin",PrjConstant.COLOR_SIGN_NAMES));
            }else {
                otherMatch.append("effect",new Document("$in",PrjConstant.COLOR_SIGN_NAMES));
            }
        }
        //反馈状态
        if (StringUtil.isNotNull(vo.getFeedBack())){
            if (vo.getFeedBack().equals(SysDefConstants.NAME_NODE_TASK_STATUS_CLOSED)){
                otherMatch.append("feedBackDiff",0);
            }else {
                otherMatch.append("feedBackDiff",new Document("$gt",0));
            }
        }
        //分析状态
        if (StringUtil.isNotNull(vo.getAnalysis())){
            if (vo.getAnalysis().equals(SysDefConstants.NAME_NODE_TASK_STATUS_CLOSED)){
                otherMatch.append("analysisDiff",0);
            }else {
                otherMatch.append("analysisDiff",new Document("$gt",0));
            }
        }
        if (!otherMatch.isEmpty()){
            sql.add(new Document("$match",otherMatch));
        }
        List<Document> countSql = new ArrayList<>();
        //总行数
        countSql.addAll(sql);
        countSql.add(new Document("$count","count"));
        List<Document> countResult = prjInfoDao.aggregate(countSql);
        if (CollectionUtils.isEmpty(countResult)){
            return pageBean;
        }
        int count = (int) countResult.get(0).get("count");
        pageBean.setCount(count);

        sql.add(new Document("$sort",new Document("provName",1)));
        if (vo.getPageIndex() !=null && IntegerUtil.getNotNull(vo.getPageSize()) > 0){
            sql.add(new Document("$skip",vo.getPageIndex()*vo.getPageSize()));
            sql.add(new Document("$limit",vo.getPageSize()));
        }

        MongoCursor<Document> cursor =mongoTemplate.getCollection(DBT.PRJINFO.n()).aggregate(sql, Document.class).allowDiskUse(true).cursor();
        List<PrjDvtMgtMergeVo> voList = new ArrayList<>();
        while (cursor.hasNext()){
            Document next = cursor.next();
            PrjDvtMgtMergeVo mergeVo = new PrjDvtMgtMergeVo();
            mergeVo.setPrjId(StringUtil.toObjectId(next.get("prjId")));
            mergeVo.setPrjName(next.getString("prjName"));
            mergeVo.setPrjCode(next.getString("prjCode"));
            mergeVo.setBigRegion(next.getString("bigRegionName"));
            mergeVo.setRegion(next.getString("regionName"));
            mergeVo.setEngDept(next.getString("engDeptName"));
            mergeVo.setProv(next.getString("provName"));
            mergeVo.setLevelName(next.getString("levelName"));
            mergeVo.setStatusName(next.getString("statusName"));
            mergeVo.setNetSaleAmt(BigDecimalUtils.getDoublePercentHalfNum(next.getDouble("netSaleAmt"),2));
            Document pmUser = (Document) next.get("pmUser");
            if (pmUser != null){
                mergeVo.setPmUserId(pmUser.getObjectId("userId"));
                mergeVo.setPmUserName(StringUtil.getNotNullStr(pmUser.getString("userName"))+"/"+StringUtil.getNotNullStr(pmUser.getString("loginName")));
            }
            Document trackUser = (Document) next.get("trackUser");
            if (trackUser != null){
                mergeVo.setTrackUserId(trackUser.getObjectId("userId"));
                mergeVo.setTrackUserName(StringUtil.getNotNullStr(trackUser.getString("userName"))+"/"+StringUtil.getNotNullStr(trackUser.getString("loginName")));
            }
            List<String> delaySigns = next.getList("delaySign", String.class);
            if (CollectionUtils.isNotEmpty(delaySigns)){
                delaySigns = delaySigns.stream().filter(item -> StringUtil.isNotNull(item)).collect(Collectors.toList());
            }
            mergeVo.setDelaySign(StringUtil.listToStr(delaySigns));
            mergeVo.setProgressDelay(StringUtil.getNotNullStr(next.getString("progressDelay")));
            List<String> colorCards = next.getList("colorCard", String.class);
            if (CollectionUtils.isNotEmpty(colorCards)){
                colorCards = colorCards.stream().filter(item -> StringUtil.isNotNull(item)).collect(Collectors.toList());
            }
            mergeVo.setColorCard(StringUtil.listToStr(colorCards));
            mergeVo.setImplPlan(StringUtil.getNotNullStr(next.getString("implPlan")));
            mergeVo.setCost(StringUtil.getNotNullStr(next.getString("cost")));
            mergeVo.setEffect(StringUtil.getNotNullStr(next.getString("effect")));
            Integer devMgtSize = next.getInteger("devMgtSize");
            Integer feedBack = next.getInteger("feedBack");
            mergeVo.setFeedBack(feedBack+"/"+devMgtSize);
            Integer analysis = next.getInteger("analysis");
            mergeVo.setAnalysis(analysis+"/"+devMgtSize);
            voList.add(mergeVo);
        }
        pageBean.setObjectList(voList);
        return pageBean;
    }
    private List<ObjectId> getAuth(PrjDvtMgtMergeVo mergeVo, TeSysUser loginUser){
        //角色
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
        //人员id
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //查询是否是bu运营管理员
        List<TeSysDefRoleUser> buPrjBudgetAdminUsers = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.AI_BU);
        if (CollectionUtils.isNotEmpty(buPrjBudgetAdminUsers)){
            return null;
        }
        List<ObjectId> provIds = new ArrayList<>();
        //不是bu运营管理员，查询省份权限
        //不是，查询省份运营管理员
        roleIds.clear();
        roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
        List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.ABP_PROV);
        if (CollectionUtils.isNotEmpty(operateAdmins)) {
            List<ObjectId> provIdList = operateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(provIdList)){
                provIds.addAll(provIdList);
            }
        }
        //省份经理
        roleIds.clear();
        roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
        roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
        List<TeSysDefRoleUser> managerList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.AI_BU);
        if (CollectionUtils.isNotEmpty(managerList)){
            List<ObjectId> provIdList = managerList.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(provIdList)){
                provIds.addAll(provIdList);
            }
        }

        //查询当前用户是项目经理项目集
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_E(DFN.prjInfo__sbuId,mergeVo.getSbuId()));
        List<IDbCondition> orConds = new ArrayList<>();
        orConds.add(new DC_E(DFN.prjInfo__pmUser.dot(DFN.common_userId),loginUser.getId()));
        if (CollectionUtils.isNotEmpty(provIds)){
            orConds.add(new DC_I<ObjectId>(DFN.prjInfo__prov.dot(DFN.common_cid),provIds));
        }
        conds.add(new DC_OR(orConds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.prjInfo__prjId);
        List<TePrjInfo> prjInfos = prjInfoDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(prjInfos)){
            throw BusinessException.initExc("你没有查询权限");
        }
        return prjInfos.stream().map(TePrjInfo::getPrjId).collect(Collectors.toList());
    }
    @Override
    public void exportPrjDvtMgtMergeData(PrjDvtMgtMergeVo vo, TeSysUser loginUser,HttpServletResponse response) throws IOException {
        PageBean pageBean = getPrjDvtMgtMergeData(vo,loginUser);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode("项目异常偏差.xlsx", "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0, "项目异常偏差")
                .head(PrjDvtMgtMergeVo.class)
                .registerWriteHandler(new CellStyleHandler())
                .build();
        excelWriter.write(pageBean.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public void sendPrjDvtMgtMergeDataMail(PrjDvtMgtVo vo) {
        List<ObjectId> prjIds = vo.getPrjIds();
        //查询项目数据
        List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            throw BusinessException.initExc("项目信息为空");
        }
        //子项目数据
        Map<ObjectId, TePrjInfo> subPrjMap = new HashMap<>();
        List<ObjectId> subPrjIds = prjInfoList.stream().filter(prj -> CollectionUtils.isNotEmpty(prj.getSubPrjs())).map(TePrjInfo::getSubPrjs)
                .flatMap(Collection::stream).filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(subPrjIds)){
            prjIds.addAll(subPrjIds);
            List<TePrjInfo> subPrjs = prjInfoService.queryPrjInfoByPrjdefId(subPrjIds);
            if (CollectionUtils.isNotEmpty(subPrjs)){
                subPrjMap = subPrjs.stream().collect(Collectors.toMap(TePrjInfo::getPrjId, Function.identity()));
            }
        }

        List<ObjectId> userIdList = new ArrayList<>();
        //项目经理
        List<ObjectId> pmUserIds = prjInfoList.stream().filter(prj -> prj.getPmUser() != null && prj.getPmUser().getUserId() != null).map(prj -> prj.getPmUser().getUserId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pmUserIds)){
            throw BusinessException.initExc("收件人信息为空");
        }
        userIdList.addAll(pmUserIds);
        //省份id
        List<ObjectId> provIds = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
        Map<ObjectId,List<ObjectId>> provUserMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(provIds)){
            //查询省份运营管理员
            List<ObjectId> roleIds = new ArrayList<>();
            roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
            List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.ABP_PROV);
            if (CollectionUtils.isNotEmpty(operateAdmins)) {
                for (TeSysDefRoleUser roleUser : operateAdmins){
                    ObjectId provId = roleUser.getDefId();
                    TeSysDefRoleUser2User user = roleUser.getRoleUser();
                    if (user == null || user.getUserId() == null){
                        continue;
                    }
                    userIdList.add(user.getUserId());
                    //塞值
                    List<ObjectId> userIds = provUserMap.getOrDefault(provId,new ArrayList<>());
                    if (!userIds.contains(user.getUserId())){
                        userIds.add(user.getUserId());
                    }
                    provUserMap.put(provId,userIds);
                }
            }
            //省份经理
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
            roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
            List<TeSysDefRoleUser> managerList = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(managerList)){
                for (TeSysDefRoleUser roleUser : managerList){
                    ObjectId provId = roleUser.getDefId();
                    TeSysDefRoleUser2User user = roleUser.getRoleUser();
                    if (user == null || user.getUserId() == null){
                        continue;
                    }
                    userIdList.add(user.getUserId());
                    //塞值
                    List<ObjectId> userIds = provUserMap.getOrDefault(provId,new ArrayList<>());
                    if (!userIds.contains(user.getUserId())){
                        userIds.add(user.getUserId());
                    }
                    provUserMap.put(provId,userIds);
                }
            }
        }
        //查询人员数据
        List<TeSysUser> sysUsers = sysUserService.getUsersByIds(userIdList);
        if (CollectionUtils.isEmpty(sysUsers)){
            throw BusinessException.initExc("收件人信息为空");
        }
        //根据人员分组
        Map<ObjectId, TeSysUser> sysUserMap = sysUsers.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, Function.identity()));
        if (MapUtils.isEmpty(sysUserMap)){
            throw BusinessException.initExc("收件人邮箱为空");
        }
        //查询偏差数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DbFieldName.PrjDvtMgt.ym,vo.getYm()));
        conds.add(new DC_I<ObjectId>(DbFieldName.PrjDvtMgt.prj.dot(DFN.common_cid),prjIds));
        conds.add(new DC_I<ObjectId>(DbFieldName.PrjDvtMgt.type.dot(DbFieldName.common_cid),Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID,
                PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID,PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID,
                PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID,PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID)));
        List<PrjDvtMgt> prjDvtMgts = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgts)){
            return;
        }
        //根据项目、类型划分数据
        Map<ObjectId, Map<ObjectId, PrjDvtMgt>> prjDvtMgtMap = prjDvtMgts.stream().collect(Collectors.groupingBy(mgt -> mgt.getPrj().getCid(), Collectors.toMap(mgt -> mgt.getType().getCid(), Function.identity(), (v1, v2) -> v2)));
        List<MailInfo> mailInfoList = new ArrayList<>();
        Set<ObjectId> dvtIdSet = new HashSet<>();
        //遍历
        for (TePrjInfo prjInfo : prjInfoList){
            ObjectId prjId = prjInfo.getPrjId();
            List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
            List<ObjectId> subPrjIdList = null;
            if (CollectionUtils.isNotEmpty(subPrjs)){
                subPrjIdList = subPrjs.stream().filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
            }
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if (pmUser == null || pmUser.getUserId() == null || sysUserMap.get(pmUser.getUserId()) == null){
                continue;
            }
            //收件人邮箱
            String toListMail = sysUserMap.get(pmUser.getUserId()).getMailBox();
            List<String> ccAddressList = new ArrayList<>();
            //省份
            TeIdNameCn prov = prjInfo.getProv();
            if (prov != null && prov.getCid() != null && CollectionUtils.isNotEmpty(provUserMap.get(prov.getCid()))){
                //抄送人
                List<ObjectId> userIds = provUserMap.get(prov.getCid());
                for (ObjectId userId : userIds){
                    TeSysUser sysUser = sysUserMap.get(userId);
                    if (sysUser != null && !ccAddressList.contains(sysUser.getMailBox())){
                        ccAddressList.add(sysUser.getMailBox());
                    }
                }
            }
            PrjDvtMgtMailVo mgtMailVo = new PrjDvtMgtMailVo();
            mgtMailVo.setPmUserName(StringUtil.getNotNullStr(pmUser.getUserName()));
            mgtMailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));

            List<PrjDvtMgtMergeMailVo> mailVos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(subPrjIdList)){
                for (ObjectId subPrjId : subPrjIdList){
                    Map<ObjectId, PrjDvtMgt> subMgtMap = prjDvtMgtMap.get(subPrjId);
                    TePrjInfo subPrj = subPrjMap.get(subPrjId);
                    if (MapUtils.isNotEmpty(subMgtMap) && subPrj != null){
                        for (ObjectId typeId : subMgtMap.keySet()){
                            PrjDvtMgt prjDvtMgt = subMgtMap.get(typeId);
                            //塞值
                            PrjDvtMgtMergeMailVo mailVo = new PrjDvtMgtMergeMailVo();
                            mailVo.setTypeId(typeId);
                            mailVo.setPrjCode(StringUtil.getNotNullStr(subPrj.getPrjCode()));
                            mailVo.setPrjName(StringUtil.getNotNullStr(subPrj.getPrjName()));
                            RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                            if (rcdInfo != null){
                                if (typeId.equals(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID)){
                                    mailVo.setProjectTime(subPrj.getOaPrjStartDate());
                                    mailVo.setProjectDuration(rcdInfo.getProjectedDays());
                                    mailVo.setNum(1);
                                }else if (typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID)){
                                    mailVo.setPrjMst(StringUtil.getNotNullStr(rcdInfo.getPrjMst()));
                                    mailVo.setDelayDays(rcdInfo.getDelayDays());
                                    mailVo.setColorCardType(rcdInfo.getColorCardType());
                                    mailVo.setNum(3);
                                }
                            }
                            mailVos.add(mailVo);
                            dvtIdSet.add(prjDvtMgt.getId());
                        }
                    }
                }
            }
            //偏差数据
            Map<ObjectId, PrjDvtMgt> prjDvtMgtMapByType = prjDvtMgtMap.get(prjId);
            if (MapUtils.isNotEmpty(prjDvtMgtMapByType)){
                for (ObjectId typeId : prjDvtMgtMapByType.keySet()){
                    PrjDvtMgt prjDvtMgt = prjDvtMgtMapByType.get(typeId);
                    //项目
                    TeIdNameCn prj = prjDvtMgt.getPrj();
                    //塞值
                    PrjDvtMgtMergeMailVo mailVo = new PrjDvtMgtMergeMailVo();
                    mailVo.setTypeId(typeId);
                    mailVo.setPrjCode(StringUtil.getNotNullStr(prj.getCodeName()));
                    mailVo.setPrjName(StringUtil.getNotNullStr(prj.getName()));
                    RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                    if (rcdInfo != null){
                        if (typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID)){
                            mailVo.setTaskName(prjDvtMgt.getRcd() == null ? "" : StringUtil.getNotNullStr(prjDvtMgt.getRcd().getName()));
                            mailVo.setPlanEndTime(DateUtil.formatDate2Str(rcdInfo.getPed(),DateUtil.DATE_FORMAT));
                            mailVo.setNum(2);
                        }else if (typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID) || typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID)){
                            //基准累计人力资源（人月）
                            mailVo.setBlAccuManMonth(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuManMonth(),2));
                            //实际累计人力资源（人月）
                            mailVo.setActAccuManMonth(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuManMonth(),2));
                            //累计人力资源(实际VS基准)(实际-基准）
                            double mixedEmpDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuManMonth(),rcdInfo.getBlAccuManMonth(),2);
                            mailVo.setAccuManMonthDiff(mixedEmpDiff);
                            //累计人力资源（偏差率）（实际-基准）/基准
                            double mixedEmpDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuManMonth()) ?
                                    (NumberUtils.equalZero(mixedEmpDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(mixedEmpDiff,rcdInfo.getBlAccuManMonth(),2);
                            mailVo.setAccuManMonthDiffRate(mixedEmpDiffRate);

                            //基准累计差旅费（K）
                            mailVo.setBlAccuTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuTravelFee(),2));
                            //实际累计差旅费（K）
                            mailVo.setActAccuTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuTravelFee(),2));
                            //累计差旅费(实际VS基准)
                            double travelFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuTravelFee(),rcdInfo.getBlAccuTravelFee(),2);
                            mailVo.setAccuTravelFeeDiff(travelFeeDiff);
                            //累计差旅费（偏差率）
                            double travelFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuTravelFee()) ?
                                    (NumberUtils.equalZero(travelFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(travelFeeDiff,rcdInfo.getBlAccuTravelFee(),2);
                            mailVo.setAccuTravelFeeDiffRate(travelFeeDiffRate);

                            //基准累计餐费（K）
                            mailVo.setBlAccuDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuDiningFee(),2));
                            //实际累计餐费（K）
                            mailVo.setActAccuDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuDiningFee(),2));
                            //累计餐费（实际VS基准）
                            double diningFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuDiningFee(),rcdInfo.getBlAccuDiningFee(),2);
                            mailVo.setAccuDiningFeeDiff(diningFeeDiff);
                            //累计餐费（偏差率）
                            double diningFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuDiningFee()) ?
                                    (NumberUtils.equalZero(diningFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(diningFeeDiff,rcdInfo.getBlAccuDiningFee(),2);
                            mailVo.setAccuDiningFeeDiffRate(diningFeeDiffRate);

                            //基准累计其它费（K）
                            mailVo.setBlAccuOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuOtherFee(),2));
                            //实际累计其它费（K）
                            mailVo.setActAccuOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuOtherFee(),2));
                            //累计其它费（实际VS基准）
                            double otherFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuOtherFee(),rcdInfo.getBlAccuOtherFee(),2);
                            mailVo.setAccuOtherFeeDiff(otherFeeDiff);
                            //累计其它费（偏差率）
                            double otherFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuOtherFee()) ?
                                    (NumberUtils.equalZero(otherFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(otherFeeDiff,rcdInfo.getBlAccuOtherFee(),2);
                            mailVo.setAccuOtherFeeDiffRate(otherFeeDiffRate);

                            //基准累计人月均差旅费（K）
                            double blAccuTravelMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuTravelFee() == null ? 0d : rcdInfo.getBlAccuTravelFee()*1000,rcdInfo.getBlAccuManMonth(),2);
                            mailVo.setBlAccuTravelMonthAvgFee(blAccuTravelMonthAvgFee);
                            //实际累计人月均差旅费（K）
                            double actAccuTravelMonthAvgFee =BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuTravelFee() == null ? 0d : rcdInfo.getActAccuTravelFee()*1000,rcdInfo.getActAccuManMonth(),2);
                            mailVo.setActAccuTravelMonthAvgFee(actAccuTravelMonthAvgFee);
                            //累计人月均差旅费(实际VS基准)
                            double accuTravelMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuTravelMonthAvgFee,blAccuTravelMonthAvgFee,2);
                            mailVo.setAccuTravelMonthAvgFeeDiff(accuTravelMonthAvgFeeDiff);
                            //累计人月均差旅费（偏差率）
                            double accuTravelMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuTravelMonthAvgFee) ?
                                    (NumberUtils.equalZero(accuTravelMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuTravelMonthAvgFeeDiff,blAccuTravelMonthAvgFee,2);
                            mailVo.setAccuTravelMonthAvgFeeDiffRate(accuTravelMonthAvgFeeDiffRate);

                            //基准累计人月均餐费（K）
                            double blAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuDiningFee() == null ? 0d : rcdInfo.getBlAccuDiningFee()*1000,rcdInfo.getBlAccuManMonth(),2);
                            mailVo.setBlAccuDiningMonthAvgFee(blAccuDiningMonthAvgFee);
                            //实际累计人月均餐费（K）
                            double actAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuDiningFee() == null ? 0d : rcdInfo.getActAccuDiningFee()*1000,rcdInfo.getActAccuManMonth(),2);
                            mailVo.setActAccuDiningMonthAvgFee(actAccuDiningMonthAvgFee);
                            //累计人月均餐费(实际VS基准)
                            double accuDiningMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuDiningMonthAvgFee,blAccuDiningMonthAvgFee,2);
                            mailVo.setAccuDiningMonthAvgFeeDiff(accuDiningMonthAvgFeeDiff);
                            //累计人月均餐费（偏差率）
                            double accuDiningMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuDiningMonthAvgFee) ?
                                    (NumberUtils.equalZero(accuDiningMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuDiningMonthAvgFeeDiff,blAccuDiningMonthAvgFee,2);
                            mailVo.setAccuDiningMonthAvgFeeDiffRate(accuDiningMonthAvgFeeDiffRate);

                            //基准累计人月均其它费（K）
                            double blAccuMonthAvgOtherFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuOtherFee() == null ? 0d : rcdInfo.getBlAccuOtherFee()*1000,rcdInfo.getBlAccuManMonth(),2);
                            mailVo.setBlAccuMonthAvgOtherFee(blAccuMonthAvgOtherFee);
                            //实际累计人月均其它费（K）
                            double actAccuOtherMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuOtherFee() == null ? 0d : rcdInfo.getActAccuOtherFee()*1000,rcdInfo.getActAccuManMonth(),2);
                            mailVo.setActAccuOtherMonthAvgFee(actAccuOtherMonthAvgFee);
                            //累计人月均其它费(实际VS基准)
                            double accuOtherMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuOtherMonthAvgFee,blAccuMonthAvgOtherFee,2);
                            mailVo.setAccuOtherMonthAvgFeeDiff(accuOtherMonthAvgFeeDiff);
                            //累计人月均其它费（偏差率）
                            double accuOtherMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuMonthAvgOtherFee) ?
                                    (NumberUtils.equalZero(accuOtherMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuOtherMonthAvgFeeDiff,blAccuMonthAvgOtherFee,2);
                            mailVo.setAccuOtherMonthAvgFeeDiffRate(accuOtherMonthAvgFeeDiffRate);
                            mailVo.setNum(5);
                        }else if (typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID)){
                            mailVo.setAbpEmpAndOsEmp(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getAbpEmp(), 2) + BigDecimalUtils.getDoubleHalfNum(rcdInfo.getAbpOutsrc(), 2));
                            mailVo.setBlEmpAndOsEmp(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlEmp(), 2) + BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlOutsrc(), 2));
                            double abpSumEmpNum = DoubleUtil.getNotNull(rcdInfo.getAbpEmp())
                                    + DoubleUtil.getNotNull(rcdInfo.getAbpOutsrc())*0.5;
                            double sumEmpNum = DoubleUtil.getNotNull(rcdInfo.getBlEmp())
                                    + DoubleUtil.getNotNull(rcdInfo.getBlOutsrc()) * 0.5;
                            // 差值
                            double diff = abpSumEmpNum - sumEmpNum;
                            mailVo.setDiff(diff);
                            // 偏差率
                            double diffRate = getDiffRate(sumEmpNum, abpSumEmpNum);
                            mailVo.setDiffRate(diffRate);
                            mailVo.setNum(4);
                        }else if (typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID)){
                            // 基准费用标准-全周期（元）：直接费用/人工费 (人工费 = 正式人工+外包人工+技术分包)
                            double bmkFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getBlDirectFee()),
                                    DoubleUtil.getNotNull(rcdInfo.getBlRgFee()));
                            // 实际费用标准-截止统计月（元）：直接费用/人工费 人工费 = 正式人工+外包人工+技术分包)
                            double actualFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getActDirectFee()),
                                    DoubleUtil.getNotNull(rcdInfo.getActRgFee())+DoubleUtil.getNotNull(rcdInfo.getActTechSubFee()));
                            // 差值（元）：
                            double diff = actualFeeStandard - bmkFeeStandard;
                            // 费用标准-偏差率：（实际-计划）/计划
                            double diffRate = getDiffRate(bmkFeeStandard, actualFeeStandard);
                            mailVo.setBmkEffect(BigDecimalUtils.getDoubleHalfNum(bmkFeeStandard, 2));
                            mailVo.setActEffect(BigDecimalUtils.getDoubleHalfNum(actualFeeStandard, 2));
                            mailVo.setDiff(BigDecimalUtils.getDoubleHalfNum(diff, 2));
                            mailVo.setDiffRate(BigDecimalUtils.getDoubleHalfNum(diffRate, 2));
                            mailVo.setNum(6);
                        }
                    }
                    mailVos.add(mailVo);
                    dvtIdSet.add(prjDvtMgt.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(mailVos)){
                continue;
            }
            mailVos = mailVos.stream().sorted(Comparator.comparing(PrjDvtMgtMergeMailVo::getNum)).collect(Collectors.toList());
            mgtMailVo.setPrjDvtMgtMergeMailVos(mailVos);

            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(Arrays.asList(toListMail));
            mailInfo.setCcList(ccAddressList);
            mailInfo.setSubject("【PMS项目管理】异常偏差反馈提醒");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mgtMailVo));
            mailInfoList.add(mailInfo);
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
        if (CollectionUtils.isNotEmpty(dvtIdSet)){
            List<IDbCondition> updateConds = new ArrayList<>();
            updateConds.add(new DC_I<>(DFN.common__id, new ArrayList<>(dvtIdSet)));
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DFN.PrjDvtMgt.isMailed, true));
            prjDvtMgtDao.updateByConds(updateConds, updates);
        }
    }

    private String getMailContent(PrjDvtMgtMailVo mgtMailVo) {
        Template template = null;
        try {
            template = freeMarkerConfig.getConfiguration().getTemplate("prjDevMgtMergeMail.ftl","utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        Writer out = new StringWriter();
        try {
            // 输出数据到模板中，生成文件。
            template.process(mgtMailVo, out);
            out.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toString();
    }

    @Override
    public Map<ObjectId, Integer> countPrjDvtMgtSubscript(ObjectId prjId, String ym) {
        Map<ObjectId,Integer> result = new HashMap<>();
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        List<ObjectId> prjIds = new ArrayList<>();
        prjIds.add(prjId);
        //获取子项目id
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isNotEmpty(subPrjs)){
            List<ObjectId> subPrjIds = subPrjs.stream().filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subPrjIds)){
                prjIds.addAll(subPrjIds);
            }
        }
        //类型
        List<ObjectId> typeIds = new ArrayList<>();
        typeIds.add(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID);
        //查询偏差数据
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, prjIds, typeIds);
        if (CollectionUtils.isEmpty(prjDvtMgts)){
            return result;
        }
        for (PrjDvtMgt dvtMgt : prjDvtMgts){
            ObjectId typeId = dvtMgt.getType().getCid();
            Integer num = result.getOrDefault(typeId, 0);
            //反馈
            String desc = dvtMgt.getDesc();
            String rectifyAction = dvtMgt.getRectifyAction();
            Date rectifyEndDate = dvtMgt.getRectifyEndDate();

            //分析
            TeIdNameCn causeType = dvtMgt.getCauseType();
            TeIdNameCn causeSubType = dvtMgt.getCauseSubType();
            TeIdNameCn causeSub2Type = dvtMgt.getCauseSub2Type();
            String notes = dvtMgt.getNotes();
            String firstVerPlanEndDate = "";

            RcdInfo rcdInfo = dvtMgt.getRcdInfo();
            if (rcdInfo != null){
                firstVerPlanEndDate = rcdInfo.getFirstVerPlanEndDate();
            }
            if (typeId.equals(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID) || typeId.equals(PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID)){
                if (StringUtil.isNull(desc) || StringUtil.isNull(rectifyAction) || rectifyEndDate == null ||
                        causeType == null || StringUtil.isNull(notes) || StringUtil.isNull(firstVerPlanEndDate)){
                    num++;
                }
            }else {
                if (StringUtil.isNull(desc) || StringUtil.isNull(rectifyAction) || rectifyEndDate == null ||
                        causeType == null || causeSubType == null || causeSub2Type == null){
                    num++;
                }
            }
            result.put(typeId,num);
        }
        return result;
    }
    private List<PrjDvtMgt> getPrjDvtMgts(String ym,List<ObjectId> prjIds,List<ObjectId> typeIds){
        //查询偏差数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DbFieldName.PrjDvtMgt.ym,ym));
        conds.add(new DC_I<ObjectId>(DbFieldName.PrjDvtMgt.prj.dot(DFN.common_cid),prjIds));
        conds.add(new DC_I<ObjectId>(DbFieldName.PrjDvtMgt.type.dot(DFN.common_cid),typeIds));
        List<PrjDvtMgt> prjDvtMgts = prjDvtMgtDao.findByFieldAndConds(conds, null);
        return prjDvtMgts;
    }
    /**
     * 签约进度：取项目集下所有子项目信息，及其对应管理月份下“签约延期”数据
     */
    @Override
    public List<PrjDelaySignVo> getDelaySignDvtMgtDetailData(ObjectId prjId, String ym) {
        List<PrjDelaySignVo> prjDelaySignVos = new ArrayList<>();
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isEmpty(subPrjs)){
            return prjDelaySignVos;
        }
        List<ObjectId> subPrjIds = subPrjs.stream().filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subPrjIds)){
            return prjDelaySignVos;
        }
        //查询子项目
        List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIds);
        if (CollectionUtils.isEmpty(subPrjList)){
            return prjDelaySignVos;
        }
        //查询偏差数据
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, subPrjIds, Collections.singletonList(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID));
        Map<ObjectId, PrjDvtMgt> prjDvtMgtMap = null;
        if (CollectionUtils.isNotEmpty(prjDvtMgts)){
            //根据项目划分数据
            prjDvtMgtMap = prjDvtMgts.stream().collect(Collectors.toMap(dvt -> dvt.getPrj().getCid(), Function.identity(), (v1, v2) -> v2));
        }
        //遍历
        //子项目编码、提前立项日期、签约日期、立项时长(月)、严重程度、未签约原因、解决措施、计划解决日期、首版计划日期、原因大类、原因分析
        for (TePrjInfo subPrj : subPrjList){
            PrjDelaySignVo vo= new PrjDelaySignVo();
            vo.setPrjId(subPrj.getPrjId());
            vo.setPrjName(subPrj.getPrjName());
            vo.setPrjCode(subPrj.getPrjCode());
            vo.setOaPrjStartDate(subPrj.getOaPrjStartDate());
            vo.setSignDate(subPrj.getSignDate() == null ? "" : DateUtil.formatDate2Str(subPrj.getSignDate(),DateUtil.DATE_FORMAT));
            if (MapUtils.isNotEmpty(prjDvtMgtMap) && prjDvtMgtMap.get(subPrj.getPrjId()) != null){
                PrjDvtMgt prjDvtMgt = prjDvtMgtMap.get(subPrj.getPrjId());
                vo.setId(prjDvtMgt.getId());
                vo.setColorSign(prjDvtMgt.getColorSign());
                RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                if (rcdInfo != null) {
                    vo.setProjectedDays(rcdInfo.getProjectedDays());
                    vo.setFirstVerPlanEndDate(rcdInfo.getFirstVerPlanEndDate());
                }
                vo.setDesc(prjDvtMgt.getDesc());
                vo.setRectifyAction(prjDvtMgt.getRectifyAction());
                vo.setRectifyEndDate(prjDvtMgt.getRectifyEndDate() == null ? "" : DateUtil.formatDate2Str(prjDvtMgt.getRectifyEndDate(),DateUtil.DATE_FORMAT));
                if(prjDvtMgt.getCauseType() != null) {
                    vo.setCauseType(prjDvtMgt.getCauseType().getName());
                    vo.setCauseTypeId(prjDvtMgt.getCauseType().getCid());
                }
                vo.setNotes(prjDvtMgt.getNotes());
            }
            prjDelaySignVos.add(vo);
        }
        return prjDelaySignVos;
    }

    /**
     * 总体进度：取项目集最新版本过程管控计划下，所有目标里程碑任务，及其对应管理月份下“进度延期”数据
     */
    @Override
    public List<PrjDvtMgtCostOverEfVo> getProgressDelayDvtMgtDetailData(ObjectId prjId, String ym) {
        List<PrjDvtMgtCostOverEfVo> prjProgressDelays = new ArrayList<>();
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        Map<ObjectId, TePrjPlanTask> prjPlanTaskMap = null;
        String sbuId = prjInfo.getSbuId();
        //查询目标里程碑任务
        //目标里程碑、计划完成日期、实际完成日期、延期(月)、严重程度、延期说明、整改措施、整改完成日期、首版计划日期、原因大类、病因、药方、是否考核、原因分析、整改措施-评审、状态
        if (StringUtil.isNotNull(sbuId)){
            List<String> buList = new ArrayList<>();
            buList.add(sbuId);
            //查询bu数据
            List<SysDef> buDefList = sysDefService.getSysDefsByCodeNames(buList, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(buDefList)){
                //根据项目所在的bu获取最大版本号
                List<ObjectId> maxVerIdList = prjInfoService.getMaxVerIdListByBuDefIds(buDefList);
                //查询目标里程碑任务
                List<IDbCondition> conds = new ArrayList<>();
                conds.add(new DC_E(DFN.common_isValid,true));
                conds.add(new DC_E(DFN.prjPlanTask__prjId, prjId));
                conds.add(new DC_I<ObjectId>(DFN.prjPlanTask__prjPlanVerId,maxVerIdList));
                conds.add(new DC_E(DFN.sysVerMgt__verMgtType.dot(DFN.common_cn), PrjConstant.PRJ_PROCESS_CONTROL_PLANVER));
                conds.add(new DC_E(DFN.prjPlanTask__def.dot(DFN.common_cn), PrjConstant.TARGET_MILE_STONE));
                List<DbFieldName> fieldNameList = new ArrayList<>();
                fieldNameList.add(DFN.prjPlanTask__taskName);
                fieldNameList.add(DFN.prjPlanTask__planEndDate);
                fieldNameList.add(DFN.prjPlanTask__prjId);
                fieldNameList.add(DFN.prjPlanTask__verMgtType);
                List<TePrjPlanTask> prjPlanTasks = prjPlanTaskDao.findByFieldAndConds(conds, fieldNameList);
                if (CollectionUtils.isNotEmpty(prjPlanTasks)){
                    prjPlanTaskMap = prjPlanTasks.stream().collect(Collectors.toMap(TePrjPlanTask::getId, Function.identity()));
                    for (TePrjPlanTask prjPlanTask : prjPlanTasks){
                        PrjDvtMgtCostOverEfVo vo = new PrjDvtMgtCostOverEfVo();
                        //目标里程碑
                        vo.setRcd(new TeIdName(prjPlanTask.getId(),prjPlanTask.getTaskName()));
                        vo.setPed((prjPlanTask.getPlanEndDate() == null ? "" : DateUtil.formatDate2Str(prjPlanTask.getPlanEndDate(),DateUtil.DATE_FORMAT)));
                        vo.setActEndDate(prjPlanTask.getActualEndDate() == null ? "" : DateUtil.formatDate2Str(prjPlanTask.getActualEndDate(),DateUtil.DATE_FORMAT));
                        //延期月数，管理月份 - 计划完成月 + 1
                        if (StringUtil.isNotNull(ym) && StringUtil.isNotNull(vo.getPed())){
                            Date ymDate = DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT2);
                            Date pedDate = DateUtil.parseDate(vo.getPed(), DateUtil.DATE_FORMAT);
                            List<Date> monthsBetween = DateUtil.getMonthsBetween(pedDate, ymDate);
                            vo.setDelayMonthNum(monthsBetween.size() == 0 ? 1 : monthsBetween.size());
                        }
                    }
                }
            }
        }
        //查询偏差数据
        //目标里程碑、计划完成日期、实际完成日期、延期(月)、严重程度、延期说明、整改措施、整改完成日期、首版计划日期、原因大类、病因、药方、是否考核、原因分析、整改措施-评审、状态
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, Collections.singletonList(prjId), Collections.singletonList(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID));
        if (CollectionUtils.isNotEmpty(prjDvtMgts)){
            for (PrjDvtMgt prjDvtMgt : prjDvtMgts){
                PrjDvtMgtCostOverEfVo vo = new PrjDvtMgtCostOverEfVo();
                vo.setId(prjDvtMgt.getId());
                //目标里程碑
                TeIdName rcd = prjDvtMgt.getRcd();
                vo.setRcd(rcd);
                RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                if (rcdInfo != null){
                    vo.setPed((rcdInfo.getPed() == null ? "" : DateUtil.formatDate2Str(rcdInfo.getPed(),DateUtil.DATE_FORMAT)));
                    vo.setFirstVerPlanEndDate(rcdInfo.getFirstVerPlanEndDate());
                    vo.setNeedCheck(rcdInfo.getNeedCheck());
                }
                if (rcd != null && MapUtils.isNotEmpty(prjPlanTaskMap) && prjPlanTaskMap.get(rcd.getCid()) != null){
                    TePrjPlanTask tePrjPlanTask = prjPlanTaskMap.get(rcd.getCid());
                    vo.setActEndDate(tePrjPlanTask.getActualEndDate() == null ? "" : DateUtil.formatDate2Str(tePrjPlanTask.getActualEndDate(),DateUtil.DATE_FORMAT));
                }
                //延期月数，管理月份 - 计划完成月 + 1
                if (StringUtil.isNotNull(ym) && StringUtil.isNotNull(vo.getPed())){
                    Date ymDate = DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT2);
                    Date pedDate = DateUtil.parseDate(vo.getPed(), DateUtil.DATE_FORMAT);
                    List<Date> monthsBetween = DateUtil.getMonthsBetween(pedDate, ymDate);
                    vo.setDelayMonthNum(monthsBetween.size() == 0 ? 1 : monthsBetween.size());
                }
                vo.setColorSign(prjDvtMgt.getColorSign());
                vo.setDesc(prjDvtMgt.getDesc());
                vo.setRectifyAction(prjDvtMgt.getRectifyAction());
                vo.setRectifyEndDate(prjDvtMgt.getRectifyEndDate() == null ? "" : DateUtil.formatDate2Str(prjDvtMgt.getRectifyEndDate(),DateUtil.DATE_FORMAT));
                if(prjDvtMgt.getCauseType() != null) {
                    vo.setCauseType(prjDvtMgt.getCauseType().getName());
                    vo.setCauseTypeId(prjDvtMgt.getCauseType().getCid());
                }
                if(prjDvtMgt.getCauseSubType() != null) {
                    vo.setCauseSubType(prjDvtMgt.getCauseSubType().getName());
                    vo.setCauseSubTypeId(prjDvtMgt.getCauseSubType().getCid());
                }
                if (prjDvtMgt.getCauseSub2Type() != null){
                    vo.setCauseSub2TypeId(prjDvtMgt.getCauseSub2Type().getCid());
                    vo.setCauseSub2Type(prjDvtMgt.getCauseSub2Type().getName());
                }
                vo.setNotes(prjDvtMgt.getNotes());
                vo.setRectifyActionReviewed(prjDvtMgt.getRectifyActionReviewed());
                vo.setRectifyStatus(prjDvtMgt.getRectifyStatus() == null ? "" : prjDvtMgt.getRectifyStatus().getName());
                prjProgressDelays.add(vo);
            }
        }

        return prjProgressDelays;
    }
    /**
     * 里程碑进度：取项目集下所有子项目里程碑信息，及其对应管理月份下“色牌跟踪”数据
     */
    @Override
    public List<PrjColorCardVo> getPrjMstDvtMgtDetailData(ObjectId prjId, String ym) {
        List<PrjColorCardVo> prjColorCardVos = new ArrayList<>();
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        //子项目
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isEmpty(subPrjs)){
            return prjColorCardVos;
        }
        List<ObjectId> subPrjIds = subPrjs.stream().filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subPrjIds)){
            return prjColorCardVos;
        }
        //查询子项目
        List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIds);
        if (CollectionUtils.isEmpty(subPrjList)){
            return prjColorCardVos;
        }
        //子项目编码
        List<String> subPrjCodes = subPrjs.stream().filter(sub ->  StringUtil.isNotNull(sub.getCode())).map(TePrjInfoSubPrj::getCode).collect(Collectors.toList());
        Map<String, List<ItfAiPrjMst>> aiPrjMstMap = null;
        //查询里程碑数据
        if (CollectionUtils.isNotEmpty(subPrjCodes)){
            List<ItfAiPrjMst> itfAiPrjMsts = itfAiPrjMstClient.getAiPrjMstDatas(subPrjCodes).getData(true);
            if (CollectionUtils.isNotEmpty(itfAiPrjMsts)) {
                aiPrjMstMap = itfAiPrjMsts.stream().collect(Collectors.groupingBy(ItfAiPrjMst::getProjectCode));
            }
        }
        //查询偏差数据
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, subPrjIds, Collections.singletonList(PrjConstant.PRJ_DVT_MGT_TYPE_COLOR_CARD_ID));
        Map<ObjectId, PrjDvtMgt> prjDvtMgtMap = null;
        if (CollectionUtils.isNotEmpty(prjDvtMgts)){
            //根据项目划分数据
            prjDvtMgtMap = prjDvtMgts.stream().collect(Collectors.toMap(dvt -> dvt.getPrj().getCid(), Function.identity(), (v1, v2) -> v2));
        }
        //子项目编码、项目里程碑、基准版计划完成日期、实际完成时间、延期(天)、挂牌阶段、挂牌原因、计划摘牌时间、首版计划日期、改进措施、原因大类、原因分析
        for (TePrjInfo subPrj : subPrjList){
            PrjColorCardVo dvtVo  = new PrjColorCardVo();
            if (MapUtils.isNotEmpty(prjDvtMgtMap) && prjDvtMgtMap.get(subPrj.getPrjId()) != null){
                PrjDvtMgt prjDvtMgt = prjDvtMgtMap.get(subPrj.getPrjId());
                dvtVo.setPrjCode(subPrj.getPrjCode());
                dvtVo.setPrjId(subPrj.getPrjId());
                dvtVo.setPrjName(subPrj.getPrjName());
                dvtVo.setId(prjDvtMgt.getId());
                RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                if (rcdInfo != null) {
                    dvtVo.setPrjMst(StringUtil.getNotNullStr(rcdInfo.getPrjMst()));
                    dvtVo.setPrjMstPed(rcdInfo.getPrjMstPed());
                    dvtVo.setDelayDays(rcdInfo.getDelayDays());
                    dvtVo.setColorCardType(rcdInfo.getColorCardType());
                    dvtVo.setFirstVerPlanEndDate(rcdInfo.getFirstVerPlanEndDate());
                }
                dvtVo.setListReason(prjDvtMgt.getListReason());
                dvtVo.setPlanDelistDate(prjDvtMgt.getPlanDelistDate());
                if(prjDvtMgt.getCauseType() != null) {
                    dvtVo.setCauseType(prjDvtMgt.getCauseType().getName());
                    dvtVo.setCauseTypeId(prjDvtMgt.getCauseType().getCid());
                }
                dvtVo.setNotes(prjDvtMgt.getNotes());
                dvtVo.setRectifyActionReviewed(prjDvtMgt.getRectifyActionReviewed());

            }
            if (MapUtils.isNotEmpty(aiPrjMstMap) && CollectionUtils.isNotEmpty(aiPrjMstMap.get(subPrj.getPrjCode()))){
                List<ItfAiPrjMst> itfAiPrjMsts = aiPrjMstMap.get(subPrj.getPrjCode());
                for (ItfAiPrjMst prjMst : itfAiPrjMsts){
                    PrjColorCardVo vo  = new PrjColorCardVo();
                    vo.setPrjCode(subPrj.getPrjCode());
                    vo.setPrjId(subPrj.getPrjId());
                    vo.setPrjName(subPrj.getPrjName());
                    vo.setPrjMst(prjMst.getCodeName());
                    vo.setPrjMstPed(prjMst.getPlanCompleteDate());
                    vo.setActEndDate(prjMst.getBusinessManagementDate());

                    if (StringUtil.isNotNull(dvtVo.getPrjMst()) && dvtVo.getPrjMst().equals(vo.getPrjMst())){
                        dvtVo.setActEndDate(prjMst.getBusinessManagementDate());
                    }
                    prjColorCardVos.add(vo);
                }
            }
            if (dvtVo.getPrjId() != null){
                prjColorCardVos.add(dvtVo);
            }
        }
        return prjColorCardVos;
    }

    /**
     * 累计成本：取项目集截止管理月累计、人月均成本（参考 异常偏差-成本偏差，反馈填写）
     */
    @Override
    public PrjDvtMgtCostOverABCGVo getPrjCostDvtMgtDetailData(ObjectId prjId, String ym) {
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        //查询偏差数据
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, Collections.singletonList(prjId), Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID));
        if (CollectionUtils.isEmpty(prjDvtMgts)){
            return null;
        }
        PrjDvtMgtCostOverABCGVo vo = new PrjDvtMgtCostOverABCGVo();
        //累计：人力资源(人月)、差旅费(K)、餐费(K)、其它费(K)，累计人月均：差旅费(元)、餐费(元)、其它费(元)
        //类型包含：基准、实际、偏差、偏差率
        //偏差反馈&分析：偏差原因分析、整改措施、整改完成日期、首版计划日期、原因大类、病因、药方、状态、原因分析、整改措施-评审、管理动作、是否考核
        PrjDvtMgt prjDvtMgt = prjDvtMgts.get(0);
        vo.setId(prjDvtMgt.getId());
        RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
        if (rcdInfo != null){
            vo.setFirstVerPlanEndDate(rcdInfo.getFirstVerPlanEndDate());
            vo.setNeedCheck(rcdInfo.getNeedCheck());
            vo.setMgtAction(rcdInfo.getMgtAction());
            //基准累计人力资源（人月）
            vo.setBmkAllEmpNum(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuManMonth(),2));
            //实际累计人力资源（人月）
            vo.setActualAllEmpNum(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuManMonth(),2));
            //累计人力资源(实际VS基准)(实际-基准）
            double mixedEmpDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuManMonth(),rcdInfo.getBlAccuManMonth(),2);
            vo.setEmpNumDiff(mixedEmpDiff);
            //累计人力资源（偏差率）（实际-基准）/基准
            double mixedEmpDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuManMonth()) ?
                    (NumberUtils.equalZero(mixedEmpDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(mixedEmpDiff,rcdInfo.getBlAccuManMonth(),2);
            vo.setEmpNumDiffRate(mixedEmpDiffRate);

            //基准累计差旅费（K）
            vo.setBmkTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuTravelFee(),2));
            //实际累计差旅费（K）
            vo.setActualTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuTravelFee(),2));
            //累计差旅费(实际VS基准)
            double travelFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuTravelFee(),rcdInfo.getBlAccuTravelFee(),2);
            vo.setTravelFeeDiff(travelFeeDiff);
            //累计差旅费（偏差率）
            double travelFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuTravelFee()) ?
                    (NumberUtils.equalZero(travelFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(travelFeeDiff,rcdInfo.getBlAccuTravelFee(),2);
            vo.setTravelFeeDiffRate(travelFeeDiffRate);

            //基准累计餐费（K）
            vo.setBmkDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuDiningFee(),2));
            //实际累计餐费（K）
            vo.setActualDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuDiningFee(),2));
            //累计餐费（实际VS基准）
            double diningFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuDiningFee(),rcdInfo.getBlAccuDiningFee(),2);
            vo.setDiningFeeDiff(diningFeeDiff);
            //累计餐费（偏差率）
            double diningFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuDiningFee()) ?
                    (NumberUtils.equalZero(diningFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(diningFeeDiff,rcdInfo.getBlAccuDiningFee(),2);
            vo.setDiningFeeDiffRate(diningFeeDiffRate);

            //基准累计其它费（K）
            vo.setBmkOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuOtherFee(),2));
            //实际累计其它费（K）
            vo.setActualOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuOtherFee(),2));
            //累计其它费（实际VS基准）
            double otherFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuOtherFee(),rcdInfo.getBlAccuOtherFee(),2);
            vo.setOtherFeeDiff(otherFeeDiff);
            //累计其它费（偏差率）
            double otherFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuOtherFee()) ?
                    (NumberUtils.equalZero(otherFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(otherFeeDiff,rcdInfo.getBlAccuOtherFee(),2);
            vo.setOtherFeeDiffRate(otherFeeDiffRate);

            //基准累计人月均差旅费（K）
            double blAccuTravelMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuTravelFee() == null ? 0d : rcdInfo.getBlAccuTravelFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            vo.setBmkTravelFeePerAvg(blAccuTravelMonthAvgFee);
            //实际累计人月均差旅费（K）
            double actAccuTravelMonthAvgFee =BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuTravelFee() == null ? 0d : rcdInfo.getActAccuTravelFee()*1000,rcdInfo.getActAccuManMonth(),2);
            vo.setActualTravelFeePerAvg(actAccuTravelMonthAvgFee);
            //累计人月均差旅费(实际VS基准)
            double accuTravelMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuTravelMonthAvgFee,blAccuTravelMonthAvgFee,2);
            vo.setTravelFeePerAvgDiff(accuTravelMonthAvgFeeDiff);
            //累计人月均差旅费（偏差率）
            double accuTravelMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuTravelMonthAvgFee) ?
                    (NumberUtils.equalZero(accuTravelMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuTravelMonthAvgFeeDiff,blAccuTravelMonthAvgFee,2);
            vo.setTravelFeePerAvgDiffRate(accuTravelMonthAvgFeeDiffRate+"");

            //基准累计人月均餐费（K）
            double blAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuDiningFee() == null ? 0d : rcdInfo.getBlAccuDiningFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            vo.setBmkDiningFeePerAvg(blAccuDiningMonthAvgFee);
            //实际累计人月均餐费（K）
            double actAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuDiningFee() == null ? 0d : rcdInfo.getActAccuDiningFee()*1000,rcdInfo.getActAccuManMonth(),2);
            vo.setActualDiningFeePerAvg(actAccuDiningMonthAvgFee);
            //累计人月均餐费(实际VS基准)
            double accuDiningMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuDiningMonthAvgFee,blAccuDiningMonthAvgFee,2);
            vo.setDiningFeePerAvgDiff(accuDiningMonthAvgFeeDiff);
            //累计人月均餐费（偏差率）
            double accuDiningMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuDiningMonthAvgFee) ?
                    (NumberUtils.equalZero(accuDiningMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuDiningMonthAvgFeeDiff,blAccuDiningMonthAvgFee,2);
            vo.setDiningFeePerAvgDiffRate(accuDiningMonthAvgFeeDiffRate);

            //基准累计人月均其它费（K）
            double blAccuMonthAvgOtherFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuOtherFee() == null ? 0d : rcdInfo.getBlAccuOtherFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            vo.setBmkOtherFeePerAvg(blAccuMonthAvgOtherFee);
            //实际累计人月均其它费（K）
            double actAccuOtherMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuOtherFee() == null ? 0d : rcdInfo.getActAccuOtherFee()*1000,rcdInfo.getActAccuManMonth(),2);
            vo.setActualOtherFeePerAvg(actAccuOtherMonthAvgFee);
            //累计人月均其它费(实际VS基准)
            double accuOtherMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuOtherMonthAvgFee,blAccuMonthAvgOtherFee,2);
            vo.setOtherFeePerAvgDiff(accuOtherMonthAvgFeeDiff);
            //累计人月均其它费（偏差率）
            double accuOtherMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuMonthAvgOtherFee) ?
                    (NumberUtils.equalZero(accuOtherMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuOtherMonthAvgFeeDiff,blAccuMonthAvgOtherFee,2);
            vo.setOtherFeePerAvgDiffRate(accuOtherMonthAvgFeeDiffRate+"");
        }
        vo.setDesc(prjDvtMgt.getDesc());
        vo.setRectifyAction(prjDvtMgt.getRectifyAction());
        vo.setRectifyEndDate(prjDvtMgt.getRectifyEndDate() == null ? "" : DateUtil.formatDate2Str(prjDvtMgt.getRectifyEndDate(),DateUtil.DATE_FORMAT));
        if(prjDvtMgt.getCauseType() != null) {
            vo.setCauseType(prjDvtMgt.getCauseType().getName());
            vo.setCauseTypeId(prjDvtMgt.getCauseType().getCid());
        }
        if(prjDvtMgt.getCauseSubType() != null) {
            vo.setCauseSubType(prjDvtMgt.getCauseSubType().getName());
            vo.setCauseSubTypeId(prjDvtMgt.getCauseSubType().getCid());
        }
        if (prjDvtMgt.getCauseSub2Type() != null){
            vo.setCauseSub2TypeId(prjDvtMgt.getCauseSub2Type().getCid());
            vo.setCauseSub2Type(prjDvtMgt.getCauseSub2Type().getName());
        }
        vo.setNotes(prjDvtMgt.getNotes());
        vo.setRectifyActionReviewed(prjDvtMgt.getRectifyActionReviewed());
        vo.setRectifyStatus(prjDvtMgt.getRectifyStatus() == null ? "" : prjDvtMgt.getRectifyStatus().getName());
        return vo;
    }

    /**
     * 效能：取项目集最新版本基准及经营计划数据
     */
    @Override
    public PrjEffectDeviationVo getEffectDvtMgtDetailData(ObjectId prjId, String ym) {
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        //获取最新基准版本
        List<TePrjInfoPrjBmks> prjBmks = prjInfo.getPrjBmks();
        Map<ObjectId, List<TePrjBudget>> prjBudgetMap = null;
        Map<ObjectId,TePrjInfoPrjBmks> maxPrjBmkMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(prjBmks)){
            boolean present =prjBmks.stream()
                    .filter(bmk -> bmk.getPrjBmkVerNo() != null && bmk.getPrjBudgetVerId() != null)
                    .max((v1, v2) -> v1.getPrjBmkVerNo().compareTo(v2.getPrjBmkVerNo())).isPresent();
            if (present) {
                TePrjInfoPrjBmks maxPrjBmk = prjBmks.stream()
                        .filter(bmk -> bmk.getPrjBmkVerNo() != null && bmk.getPrjBudgetVerId() != null)
                        .max((v1, v2) -> v1.getPrjBmkVerNo().compareTo(v2.getPrjBmkVerNo())).get();
                ObjectId prjBudgetVerId = maxPrjBmk.getPrjBudgetVerId();
                //查询成本数据,基准数据，取项目集的，全周期
                List<TePrjBudget> prjBudgetList = getPrjBudgetList(Collections.singletonList(prjId), Collections.singletonList(prjBudgetVerId));
                if (CollectionUtils.isNotEmpty(prjBudgetList)){
                    prjBudgetMap = prjBudgetList.stream().collect(Collectors.groupingBy(TePrjBudget::getPrjId));
                }
                maxPrjBmkMap.put(prjId,maxPrjBmk);
            }
        }
        //子项目
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        List<TePrjInfo> subPrjList = null;
        if (CollectionUtils.isNotEmpty(subPrjs)){
            List<ObjectId> subPrjIds = subPrjs.stream().filter(sub -> sub.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(subPrjIds)){
                //查询子项目
                subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIds);
            }
        }
        String bgtYm = DateUtil.formatDate2Str(DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT2), DateUtil.DATE_MONTH_FOTMAT);
        Map<ObjectId, Map<String, Double>> incomeAndRgfeeMap = getIncomeAndRgfee(Collections.singletonList(prjInfo), subPrjList, Collections.singletonList(prjInfo.getPrjCode()), prjBudgetMap, maxPrjBmkMap, prjInfo.getSbuId(), bgtYm);

        //查询偏差数据
        List<PrjDvtMgt> prjDvtMgts = getPrjDvtMgts(ym, Collections.singletonList(prjId), Arrays.asList(PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID,PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID));
        PrjDvtMgt prjDvtMgt = null;
        if (CollectionUtils.isNotEmpty(prjDvtMgts)){
            prjDvtMgt = prjDvtMgts.get(0);
        }
        //总成本(K)：合计、人工费、正式、外包、技术分包、直接费用、差旅费、餐费、其它费，人力资源(人月)：合计、正式、外包，预测收入(K)，GM(%)，效能(元)
        //类型包含：基准、实施计划、偏差、偏差率
        PrjEffectDeviationVo vo = new PrjEffectDeviationVo();
        Map<String, Double> incomeAndRgfee = incomeAndRgfeeMap.get(prjId);

        double forecastIncome = 0d;
        double blIncome = 0d;
        double blRgFee = 0d;
        double sumAllEmpAndDirectFee = 0d;
        double actIncome = 0d;
        double actRgFee = 0d;
        double allCostFee = 0d;
        double allIncome = 0d;

        double blEmpFee = 0d;
        double blOsEmpFee = 0d;
        double blTraineeFee = 0d;
        double blJsfbFee = 0d;
        double blTravelFee = 0d;
        double blDiningFee = 0d;
        double blOtherFee = 0d;
        double blEmpNum = 0d;
        double blOsEmpNum = 0d;

        double actEmpFee = 0d;
        double actOsEmpFee = 0d;
        double actTraineeFee = 0d;
        double actJsfbFee = 0d;
        double actTravelFee = 0d;
        double actDiningFee = 0d;
        double actOtherFee = 0d;
        double actEmpNum = 0d;
        double actOsEmpNum = 0d;
        if (MapUtils.isNotEmpty(incomeAndRgfee)){
            forecastIncome = incomeAndRgfee.get("forecastIncome");
            blIncome = incomeAndRgfee.get("blIncome");
            blRgFee = incomeAndRgfee.get("blRgFee");
            sumAllEmpAndDirectFee = incomeAndRgfee.get("sumAllEmpAndDirectFee");
            actIncome = incomeAndRgfee.get("actIncome");
            actRgFee = incomeAndRgfee.get("actRgFee");
            allCostFee = incomeAndRgfee.get("allCostFee");
            allIncome = incomeAndRgfee.get("allIncome");

            blEmpFee = incomeAndRgfee.get("blEmpFee");
            blOsEmpFee = incomeAndRgfee.get("blOsEmpFee");
            blTraineeFee = incomeAndRgfee.get("blTraineeFee");
            blJsfbFee = incomeAndRgfee.get("blJsfbFee");
            blTravelFee = incomeAndRgfee.get("blTravelFee");
            blDiningFee = incomeAndRgfee.get("blDiningFee");
            blOtherFee = incomeAndRgfee.get("blOtherFee");
            blEmpNum = incomeAndRgfee.get("blEmpNum");
            blOsEmpNum = incomeAndRgfee.get("blOsEmpNum");

            actEmpFee = incomeAndRgfee.get("actEmpFee");
            actOsEmpFee = incomeAndRgfee.get("actOsEmpFee");
            actTraineeFee = incomeAndRgfee.get("actTraineeFee");
            actJsfbFee = incomeAndRgfee.get("actJsfbFee");
            actTravelFee = incomeAndRgfee.get("actTravelFee");
            actDiningFee = incomeAndRgfee.get("actDiningFee");
            actOtherFee = incomeAndRgfee.get("actOtherFee");
            actEmpNum = incomeAndRgfee.get("actEmpNum");
            actOsEmpNum = incomeAndRgfee.get("actOsEmpNum");
        }

        //合计
        vo.setBlAllCostFee(BigDecimalUtils.getDoubleHalfNum(sumAllEmpAndDirectFee,2));
        vo.setActAllCostFee(BigDecimalUtils.getDoubleHalfNum(allCostFee,2));
        vo.setAllCostFeeDiff(BigDecimalUtils.getDoubleHalfNum(allCostFee - sumAllEmpAndDirectFee,2));
        vo.setAllCostFeeDiffRate(getDiffRate(sumAllEmpAndDirectFee,allCostFee));
        // 人工费
        vo.setBlRgFee(BigDecimalUtils.getDoubleHalfNum(blRgFee,2));
        vo.setActRgFee(BigDecimalUtils.getDoubleHalfNum(actRgFee,2));
        vo.setRgFeeDiff(BigDecimalUtils.getDoubleHalfNum(actRgFee - blRgFee,2));
        vo.setRgFeeDiffRate(getDiffRate(blRgFee,actRgFee));
        // 正式
        vo.setBlEmpFee(BigDecimalUtils.getDoubleHalfNum(blEmpFee,2));
        vo.setActEmpFee(BigDecimalUtils.getDoubleHalfNum(actEmpFee,2));
        vo.setEmpFeeDiff(BigDecimalUtils.getDoubleHalfNum(actEmpFee - blEmpFee,2));
        vo.setEmpFeeDiffRate(getDiffRate(blEmpFee,actEmpFee));
        // 外包
        vo.setBlOsEmpFee(BigDecimalUtils.getDoubleHalfNum(blOsEmpFee,2));
        vo.setActOsEmpFee(BigDecimalUtils.getDoubleHalfNum(actOsEmpFee,2));
        vo.setOsEmpFeeDiff(BigDecimalUtils.getDoubleHalfNum(actOsEmpFee - blOsEmpFee,2));
        vo.setOsEmpFeeDiffRate(getDiffRate(blOsEmpFee,actOsEmpFee));
        //实习
        vo.setBlTraineeFee(BigDecimalUtils.getDoubleHalfNum(blTraineeFee,2));
        vo.setActTraineeFee(BigDecimalUtils.getDoubleHalfNum(actTraineeFee,2));
        vo.setTraineeFeeDiff(BigDecimalUtils.getDoubleHalfNum(actTraineeFee - blTraineeFee,2));
        vo.setTraineeFeeDiffRate(getDiffRate(blTraineeFee,actTraineeFee));
        //技术分包
        vo.setBlJsfbFee(BigDecimalUtils.getDoubleHalfNum(blJsfbFee,2));
        vo.setActJsfbFee(BigDecimalUtils.getDoubleHalfNum(actJsfbFee,2));
        vo.setJsfbFeeDiff(BigDecimalUtils.getDoubleHalfNum(actJsfbFee - blJsfbFee,2));
        vo.setJsfbFeeDiffRate(getDiffRate(blJsfbFee,actJsfbFee));
        //直接费用
        vo.setBlDirectFee(BigDecimalUtils.getDoubleHalfNum(blTravelFee+blDiningFee+blOtherFee,2));
        vo.setActDirectFee(BigDecimalUtils.getDoubleHalfNum(actTravelFee+actDiningFee+actOtherFee,2));
        vo.setDirectFeeDiff(BigDecimalUtils.getDoubleHalfNum(vo.getActDirectFee() - vo.getBlDirectFee(),2));
        vo.setDirectFeeDiffRate(getDiffRate(vo.getBlDirectFee(),vo.getActDirectFee()));
        //差旅费
        vo.setBlTravelFee(BigDecimalUtils.getDoubleHalfNum(blTravelFee,2));
        vo.setActTravelFee(BigDecimalUtils.getDoubleHalfNum(actTravelFee,2));
        vo.setTravelFeeDiff(BigDecimalUtils.getDoubleHalfNum(actTravelFee - blTravelFee,2));
        vo.setTravelFeeDiffRate(getDiffRate(blTravelFee,actTravelFee));
        //餐费
        vo.setBlDiningFee(BigDecimalUtils.getDoubleHalfNum(blDiningFee,2));
        vo.setActDiningFee(BigDecimalUtils.getDoubleHalfNum(actDiningFee,2));
        vo.setDiningFeeDiff(BigDecimalUtils.getDoubleHalfNum(actDiningFee - blDiningFee,2));
        vo.setDiningFeeDiffRate(getDiffRate(blDiningFee,actDiningFee));
        //其它费
        vo.setBlOtherFee(BigDecimalUtils.getDoubleHalfNum(blOtherFee,2));
        vo.setActOtherFee(BigDecimalUtils.getDoubleHalfNum(actOtherFee,2));
        vo.setOtherFeeDiff(BigDecimalUtils.getDoubleHalfNum(actOtherFee - blOtherFee,2));
        vo.setOtherFeeDiffRate(getDiffRate(blOtherFee,actOtherFee));
        //人力资源(人月)：合计
        vo.setBlAllEmpNum(BigDecimalUtils.getDoubleHalfNum(blEmpNum+blOsEmpNum,2));
        vo.setActAllEmpNum(BigDecimalUtils.getDoubleHalfNum(actEmpNum+actOsEmpNum,2));
        vo.setAllEmpNumDiff(BigDecimalUtils.getDoubleHalfNum(vo.getActAllEmpNum() - vo.getBlAllEmpNum(),2));
        vo.setAllEmpNumDiffRate(getDiffRate(vo.getBlAllEmpNum(),vo.getActAllEmpNum()));
        //正式
        vo.setBlEmpNum(BigDecimalUtils.getDoubleHalfNum(blEmpNum,2));
        vo.setActEmpNum(BigDecimalUtils.getDoubleHalfNum(actEmpNum,2));
        vo.setEmpNumDiff(BigDecimalUtils.getDoubleHalfNum(actEmpNum - blEmpNum,2));
        vo.setEmpNumDiffRate(getDiffRate(blEmpNum,actEmpNum));
        //外包
        vo.setBlOsEmpNum(BigDecimalUtils.getDoubleHalfNum(blOsEmpNum,2));
        vo.setActOsEmpNum(BigDecimalUtils.getDoubleHalfNum(actOsEmpNum,2));
        vo.setOsEmpNumDiff(BigDecimalUtils.getDoubleHalfNum(actOsEmpNum - blOsEmpNum,2));
        vo.setOsEmpNumDiffRate(getDiffRate(blOsEmpNum,actOsEmpNum));
        //预测收入(K)
        vo.setBlIncome(BigDecimalUtils.getDoubleHalfNum(blIncome,2));
        vo.setActIncome(BigDecimalUtils.getDoubleHalfNum(actIncome,2));
        vo.setIncomeDiff(BigDecimalUtils.getDoubleHalfNum(actIncome - blIncome,2));
        vo.setIncomeDiffRate(getDiffRate(blIncome,actIncome));
        //GM(%)
        double bmkGm = BigDecimalUtils.getDoubleHalfNum(getFeeStandard(forecastIncome - sumAllEmpAndDirectFee, forecastIncome), 2);
        double actGm = BigDecimalUtils.getDoubleHalfNum(getFeeStandard(allIncome - allCostFee, allIncome), 2);
        vo.setBmkGm(bmkGm+"");
        vo.setAbpGm(actGm+"");
        vo.setGmDiff(BigDecimalUtils.getDoubleHalfNum(actGm - bmkGm,2));
        vo.setGmDiffRaate(getDiffRate(bmkGm,actGm));
        //效能(元)
        // 基准效能因子-全周期：基准百元人工-收入
        double blIncomePer100 = getFeeStandard(blIncome, blRgFee);
        // 实际效能因子-截止统计月：实际百元人工-收入
        double actIncomePer100 = getFeeStandard(actIncome, actRgFee);
        vo.setBlIncomePer100(BigDecimalUtils.getDoubleHalfNum(blIncomePer100, 2));
        vo.setActIncomePer100(BigDecimalUtils.getDoubleHalfNum(actIncomePer100, 2));
        vo.setEffectFactorDiff( BigDecimalUtils.subtractDouble(actIncomePer100,blIncomePer100,2));
        vo.setEffectDeviation(getDiffRate(blIncomePer100,actIncomePer100));

        //偏差原因分析、整改措施、整改完成日期、首版计划日期、原因大类、病因、药方、状态、原因分析、整改措施-评审
        if (prjDvtMgt != null){
            vo.setId(prjDvtMgt.getId());
            RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
            if (rcdInfo != null){
                vo.setFirstVerPlanEndDate(rcdInfo.getFirstVerPlanEndDate());
                vo.setNeedCheck(rcdInfo.getNeedCheck());
                vo.setMgtAction(rcdInfo.getMgtAction());
            }
            vo.setDesc(prjDvtMgt.getDesc());
            vo.setRectifyAction(prjDvtMgt.getRectifyAction());
            vo.setRectifyEndDate(prjDvtMgt.getRectifyEndDate() == null ? "" : DateUtil.formatDate2Str(prjDvtMgt.getRectifyEndDate(),DateUtil.DATE_FORMAT));
            if(prjDvtMgt.getCauseType() != null) {
                vo.setCauseType(prjDvtMgt.getCauseType().getName());
                vo.setCauseTypeId(prjDvtMgt.getCauseType().getCid());
            }
            if(prjDvtMgt.getCauseSubType() != null) {
                vo.setCauseSubType(prjDvtMgt.getCauseSubType().getName());
                vo.setCauseSubTypeId(prjDvtMgt.getCauseSubType().getCid());
            }
            if (prjDvtMgt.getCauseSub2Type() != null){
                vo.setCauseSub2TypeId(prjDvtMgt.getCauseSub2Type().getCid());
                vo.setCauseSub2Type(prjDvtMgt.getCauseSub2Type().getName());
            }
            vo.setNotes(prjDvtMgt.getNotes());
            vo.setRectifyActionReviewed(prjDvtMgt.getRectifyActionReviewed());
            vo.setRectifyStatus(prjDvtMgt.getRectifyStatus() == null ? "" : prjDvtMgt.getRectifyStatus().getName());
        }
        return vo;
    }

    /**
     * 基准VS实施计划：取项目集最新版本基准及经营计划数据
     */
    @Override
    public PrjEffectDeviationVo getImplPlanDvtMgtDetailData(ObjectId prjId, String ym) {
        //查询项目
        TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
        if (prjInfo == null){
            throw BusinessException.initExc("当前项目集不存在");
        }
        return null;
    }
}