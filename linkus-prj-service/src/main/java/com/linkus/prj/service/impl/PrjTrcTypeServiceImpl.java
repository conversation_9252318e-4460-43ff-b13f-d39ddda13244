package com.linkus.prj.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.linkus.prj.dao.IPrjTrcTypeDao;
import com.linkus.prj.dao.IPrjTrcTypeKeyFieldDao;
import com.linkus.prj.model.TePrjTrcType;
import com.linkus.prj.model.TePrjTrcType2User;
import com.linkus.prj.model.TePrjTrcTypeKeyField;
import com.linkus.prj.service.IPrjTrcTypeService;
import com.linkus.prj.vo.TePrjTrcTypeVo;
import com.linkus.sysuser.model.TeSysUser;

@Service("prjTrcTypeServiceImpl")
public class PrjTrcTypeServiceImpl implements IPrjTrcTypeService {

	@Autowired
	private IPrjTrcTypeDao prjTrcTypeDao;
	
	@Autowired
	private IPrjTrcTypeKeyFieldDao prjTrcTypeKeyFieldDao;
	
	/**
	 * addTePrjTrcType	新增跟踪类型
	 * 
	 * @param tePrjTrcType
	 * @return
	 */
	@Override
	public TePrjTrcType addTePrjTrcType(TePrjTrcType tePrjTrcType, TeSysUser user) {
		TePrjTrcType2User addUser=new TePrjTrcType2User();
		BeanUtils.copyProperties(user, addUser);
		addUser.setUserId(user.getId());
		tePrjTrcType.setAddUser(addUser);
		tePrjTrcType.setAddTime(new Date());
		tePrjTrcType.setIsValid(true);
		return prjTrcTypeDao.save(tePrjTrcType);
	}

	@Override
	public void removeTePrjTrcType(TePrjTrcType tePrjTrcType) {
		tePrjTrcType.setIsValid(false);
		prjTrcTypeDao.updateByObjectId(tePrjTrcType.getId(), tePrjTrcType);
		TePrjTrcTypeKeyField tePrjTrcTypeKeyField=new TePrjTrcTypeKeyField();
		tePrjTrcTypeKeyField.setTrcTypeId(tePrjTrcType.getId());
		List<TePrjTrcTypeKeyField> tePrjTrcTypeKeyFields = prjTrcTypeKeyFieldDao.findByCondition(tePrjTrcTypeKeyField);
		for (TePrjTrcTypeKeyField tmp : tePrjTrcTypeKeyFields) {
			tmp.setIsValid(false);
			prjTrcTypeKeyFieldDao.updateByObjectId(tmp.getId(), tmp);
		}
	}
	
	/**
	 * findByPrjIdAndNull	查询跟踪类型，根据prjId或者为null
	 * 
	 * @param tePrjTrcType
	 * @return
	 */
	@Override
	public List<TePrjTrcTypeVo> queryByPrjIdAndNull(TePrjTrcType tePrjTrcType) {
		List<TePrjTrcType> tePrjTrcTypes=prjTrcTypeDao.findByPrjIdAndNull(tePrjTrcType);
		List<TePrjTrcTypeVo> tePrjTrcTypeVos=new ArrayList<>();
		TePrjTrcTypeVo tePrjTrcTypeVo=null;
		for (TePrjTrcType tmp : tePrjTrcTypes) {
			tePrjTrcTypeVo=new TePrjTrcTypeVo();
			BeanUtils.copyProperties(tmp, tePrjTrcTypeVo);
			tePrjTrcTypeVos.add(tePrjTrcTypeVo);
		}
		return tePrjTrcTypeVos;
	}

}
