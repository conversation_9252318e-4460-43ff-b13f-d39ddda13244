package com.linkus.prj.service.impl;

import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.constants.PrjReportConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.DbEqualCondition;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.DoubleUtil;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.bom.api.client.AbpActEmpPlFeignClient;
import com.linkus.bom.api.model.ItfAbpActEmpPl;
import com.linkus.bom.api.model.ItfAbpActEmpPlVo;
import com.linkus.bom.api.model.ItfPlsVo;
import com.linkus.common.model.TeIdNameCn;
import com.linkus.mail.param.MailInfo;
import com.linkus.msg.api.client.MailFeignClient;
import com.linkus.msg.api.model.ItfMailInfo;
import com.linkus.oitf.antdbdao.AntDbDao;
import com.linkus.oitf.model.vo.ProEmpDateVo;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.IPrjInfoDao;
import com.linkus.prj.dao.IPrjItfDeptDao;
import com.linkus.prj.model.*;
import com.linkus.prj.model.vo.WorkHourVo;
import com.linkus.prj.service.IPrjBudgetService;
import com.linkus.prj.service.IPrjInfoService;
import com.linkus.prj.service.IProEmpDateService;
import com.linkus.prj.vo.*;
import com.linkus.rms.api.client.RmsEmpDateFeignClient;
import com.linkus.rms.api.model.ItfRmsWhDateVo;
import com.linkus.rms.api.model.ProEmpDateFromRmsVo;
import com.linkus.rms.api.model.RmsEmpDateVo;
import com.linkus.rms.api.model.RmsManDayAndNumVo;
import com.linkus.sys.api.client.SysAiccFeignClient;
import com.linkus.sys.api.model.ItfSysAicc;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.dao.ISysAiccDao;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysAicc;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2Role;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.vo.PrjGroupUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.linkus.base.db.base.field.DbFieldName.AbpActFeeRate.ccList;

@Slf4j
@Service("ProEmpDateServiceImpl")
public class ProEmpDateServiceImpl implements IProEmpDateService {
	
	@Autowired
	private IPrjInfoService prjInfoService;
	@Autowired
	private IPrjBudgetService prjBudgetService;

	@Resource
	private SysDefDao sysDefDao;
	
	@Resource
	private ISysCalDao sysCalDao;
	
	@Resource
	private AntDbDao antDbDao;
	@Resource
	private ISysDefRoleUserDao sysDefRoleUserDao;

	@Autowired
	private IPrjItfDeptDao prjItfDeptDao;
	@Autowired
	private IPrjInfoDao prjInfoDao;
	@Autowired
	private ISysUserDao sysUserDao;
	@Autowired
	private JdbcTemplate jdbcTemplate;
	@Autowired
	private MongoTemplate mongoTemplate;
	@Autowired
	private RmsEmpDateFeignClient rmsEmpDateFeignClient;
	@Autowired
	private MailFeignClient mailFeignClient;
	@Resource
	private ISysAiccDao sysAiccDao;
	@Resource
	private AbpActEmpPlFeignClient abpActEmpPlFeignClient;

	@Value("${workHoursAbnormalMailToList}")
	private String workHoursAbnormalMailToList;

	@Value("${workHoursAbnormalMailccList}")
	private String workHoursAbnormalMailccList;


	private final static Map<String, String> employeeTypeMap = new HashMap(){{
		put("Employee","正式");
		put("Contractor","正式");
		put("Trainee","实习");
		put("Outsource1","外包");
	}};

	private final static String WH_PL_KEY = "%s_%s_%s";
	public final static String WH_E_R_KEY = "%s_%s";
	private final static String ON_THE_JOB_TRUE = "在职";
	private final static String ON_THE_JOB_FALSE = "离职";

	public List<String> getYearMonthList(ObjectId prjId, List<ObjectId> prjLevels, List<String> sbuCodes){
		if(prjId == null && prjLevels.size()==0) {
			return null;
		}
		
		List<TePrjInfo> prjInfos = new ArrayList<>();
		if(prjId != null) {
			
			List<ObjectId> prjIdList = new ArrayList<>();
			prjIdList.add(prjId);
			prjInfos.addAll(prjInfoService.queryPrjInfoByPrjdefId(prjIdList));
			
		}else if(prjLevels.size() > 0) {
			PrjInfoQueryParam param = new PrjInfoQueryParam(prjLevels, sbuCodes);
			prjInfos.addAll(prjInfoService.getPrjInfoByLevelsAndIds(param));
		}
		if(prjInfos.size() == 0) {
			return new ArrayList<>();
		}
		
		List<Integer> dmpPrjIds = new ArrayList<>();
		List<ObjectId> subPrjIdList = new ArrayList<>();
		for(TePrjInfo prjInfo : prjInfos) {
			if(prjInfo.getDmpPrjId() != null) {
				dmpPrjIds.add(prjInfo.getDmpPrjId());
			}
			
			if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
				for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
					subPrjIdList.add(subPrj.getCid());
				}
			}
		}
		if (CollectionUtils.isEmpty(subPrjIdList)){
			return null;
		}
		List<String> result = rmsEmpDateFeignClient.getPrjEmpDateyyyyMM(subPrjIdList).getData(true);
		//List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIdList);
		//if(subPrjList!=null && subPrjList.size()>0) {
		//	for(TePrjInfo subPrj : subPrjList) {
		//		if(subPrj.getDmpPrjId() != null) {
		//			dmpPrjIds.add(subPrj.getDmpPrjId());
		//		}
		//	}
		//}
		//
		//List<String> result = new ArrayList<>();
		//if(dmpPrjIds.size() > 0) {
		//	result = antDbDao.getProEmpYearMonthList(dmpPrjIds);
		//}
		
		return result;
	}
	
	public List<PrjDeptWorkingHourDataVo> getDeptWorkingHoursData(ObjectId prjId, String startYearMonth, String endYearMonth, List<ObjectId> prjLevels, List<String> sbuCodes){
		if(prjId == null && prjLevels.size()==0) {
			return null;
		}
		
		List<TePrjInfo> prjInfos = new ArrayList<>();
		List<ObjectId> prjIdList = new ArrayList<>();
		if(prjId != null) {
			prjIdList.add(prjId);
			prjInfos.addAll(prjInfoService.queryPrjInfoByPrjdefId(prjIdList));
		}else if(prjLevels.size()>0){
			PrjInfoQueryParam param = new PrjInfoQueryParam(prjLevels, sbuCodes);
			prjInfos.addAll(prjInfoService.getPrjInfoByLevelsAndIds(param));
		}
		if(prjInfos.size() == 0) {
			return new ArrayList<>();
		}

		List<ObjectId> subPrjIdList = new ArrayList<>();
		List<String> subIds = new ArrayList<>();
		Map<ObjectId, TePrjInfo> prjIdToPrjInfoMap = new HashMap<>();
		for(TePrjInfo prjInfo : prjInfos) {
			if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
				for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
					subPrjIdList.add(subPrj.getCid());
				}
			}
			if(prjInfo.getSbuId()!=null && !subIds.contains(prjInfo.getSbuId())) {
				subIds.add(prjInfo.getSbuId());
			}
			
			prjIdToPrjInfoMap.put(prjInfo.getPrjId(), prjInfo);
		}
		List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIdList);
		if(subPrjList!=null && subPrjList.size()>0) {
			for(TePrjInfo subPrj : subPrjList) {
				prjIdToPrjInfoMap.put(subPrj.getPrjId(), subPrj);
			}
		}
		if (CollectionUtils.isEmpty(subPrjIdList)){
			return new ArrayList<>();
		}
		Map<ObjectId, List<ProEmpDateFromRmsVo>> empDateListMap = rmsEmpDateFeignClient
				.getProEmpDateList(subPrjIdList, StringUtil.getNotNullStr(startYearMonth),
						StringUtil.getNotNullStr(endYearMonth)).getData(true);

		List<PrjDeptWorkingHourDataVo> result = new ArrayList<>();
		//Map<Integer, List<ProEmpDateVo>> empDateListMap = antDbDao.getProEmpDateList(dmpPrjIds, startYearMonth,endYearMonth);
		if(MapUtils.isEmpty(empDateListMap)) {
			return result;
		}
		List<TeItfDept> deptList = this.queryItfDeptData();
		Map<String, Map<String, String>> ccIdMap = this.packageCCIdDeptNameMap(deptList);

		Set<String> yearMonthSet = new HashSet<>();
		for (ObjectId subPrjId: empDateListMap.keySet()){
			List<ProEmpDateFromRmsVo> proEmpDateVos = empDateListMap.get(subPrjId);
			if (CollectionUtils.isEmpty(proEmpDateVos)){
				continue;
			}
			yearMonthSet.addAll(proEmpDateVos.stream().map(proEmpDateVo -> proEmpDateVo.getYearMonth()).collect(Collectors.toSet()));
		}
		if (CollectionUtils.isEmpty(yearMonthSet)){
			return null;
		}
		//获取工作日
		Map<String, Integer> ymMap = getWorkDaysByMonth(yearMonthSet);

		Map<String, PrjDeptWorkingHourDataVo> uniqueDataMap = new HashMap<>();
		for (TePrjInfo prjInfo : prjInfos){
			List<PrjDeptWorkingHourDataVo> dataVos = new ArrayList<>();
			List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
			if(CollectionUtils.isEmpty(subPrjs)) continue;
			for (TePrjInfoSubPrj subPrj : subPrjs){
				if (CollectionUtils.isNotEmpty(empDateListMap.get(subPrj.getCid()))) {
					List<ProEmpDateFromRmsVo> proEmpDateVos = empDateListMap.get(subPrj.getCid());
					//根据cc聚合
					Map<String, List<ProEmpDateFromRmsVo>> costCenterMap = proEmpDateVos.stream()
							.collect(Collectors.groupingBy(proEmpDateVo -> proEmpDateVo.getCostcenterId()));
					if(MapUtils.isEmpty(costCenterMap)) continue;
					for (String ccId: costCenterMap.keySet()){
						List<ProEmpDateFromRmsVo> costCenterList = costCenterMap.get(ccId);
						if (CollectionUtils.isNotEmpty(costCenterList)){
							//根据职级聚合
							Map<String, List<ProEmpDateFromRmsVo>> bandTypeMap = costCenterList.stream()
									.collect(Collectors.groupingBy(ProEmpDateFromRmsVo::getBandType));
							for (String bandType:bandTypeMap.keySet()){
								List<ProEmpDateFromRmsVo> bandTypeList = bandTypeMap.get(bandType);
								if (CollectionUtils.isNotEmpty(bandTypeList)){
									//根据人员类型聚合
									Map<String, List<ProEmpDateFromRmsVo>> employeeTypeMaps = bandTypeList.stream()
											.collect(Collectors.groupingBy(ProEmpDateFromRmsVo::getEmployeeType));
									for (String employeeType : employeeTypeMaps.keySet()){
										List<ProEmpDateFromRmsVo> proEmpDateVoList = employeeTypeMaps.get(employeeType);
										if (CollectionUtils.isEmpty(proEmpDateVoList)){
											continue;
										}
										double workDaysVal = 0d;
										Set<String> empIdSet = new HashSet<>();
										int manDay = 0;
										for (ProEmpDateFromRmsVo empDateVo : proEmpDateVoList){
											String ym = empDateVo.getYearMonth();
											Integer workDays = ymMap.get(ym);
											workDaysVal+= (workDays==0 ? 0 : empDateVo.getManDay()/(double) workDays);
											List<String> empIdList = empDateVo.getEmpIdList();
											empIdSet.addAll(empIdList);
											manDay+= empDateVo.getManDay();
										}

										BigDecimal workDaysValD = new BigDecimal(workDaysVal);
										double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
										DecimalFormat df   = new DecimalFormat("#0.00");
										String pfmValueStr = df.format(workDayRes);

										String orgId = null;
										String deptName;
										Map<String, String> orgInfoMap = ccIdMap.get(ccId);
										if (MapUtils.isNotEmpty(orgInfoMap)) {
											orgId = orgInfoMap.get("orgId");
											if (StringUtils.isNotEmpty(orgInfoMap.get("orgName"))) {
												deptName = orgInfoMap.get("orgName");
											}else{
												deptName = "其他";
											}
										} else {
											deptName = "其他";
										}
										String uniqueKey;
										if (orgId == null){
											uniqueKey = prjInfo.getPrjId().toHexString()
													+ "_" + bandType + "_" + employeeType;
										} else {
											uniqueKey = prjInfo.getPrjId().toString()
													+ "_" + orgId
													+ "_" + bandType + "_" + employeeType;
										}
//										System.out.println("uniqueKey：" + uniqueKey + "：" + deptName);
										PrjDeptWorkingHourDataVo workingHourDataVo = uniqueDataMap.get(uniqueKey);
										if(workingHourDataVo != null) {
											workingHourDataVo.setManDayNum(workingHourDataVo.getManDayNum() + manDay);
											workingHourDataVo.setEmpNum(workingHourDataVo.getEmpNum() + empIdSet.size());
											workingHourDataVo.setManMonthNum(df.format((Double.valueOf(workingHourDataVo.getManMonthNum())+workDayRes)));
										}else {
											workingHourDataVo = new PrjDeptWorkingHourDataVo();
											workingHourDataVo.setPrjName(prjInfo.getPrjName());
											workingHourDataVo.setDepartName(deptName);
											workingHourDataVo.setBandType(bandType);
											workingHourDataVo.setManDayNum(manDay);
											workingHourDataVo.setEmpNum(empIdSet.size());
											workingHourDataVo.setSbuId(prjIdToPrjInfoMap.get(subPrj.getCid()).getSbuId());
											workingHourDataVo.setPrjId(prjInfo.getPrjId());
											workingHourDataVo.setEmployeeType(employeeTypeMap.get(employeeType));
											workingHourDataVo.setManMonthNum(pfmValueStr);
											dataVos.add(workingHourDataVo);
											uniqueDataMap.put(uniqueKey, workingHourDataVo);
										}
									}
								}
							}
						}
					}
				}
			}

			//if (prjInfo.getDmpPrjId() != null && CollectionUtils.isNotEmpty(empDateListMap.get(dmpPrjId))){
			//	List<ProEmpDateVo> proEmpDateVos = empDateListMap.get(dmpPrjId);
			//	//根据cc聚合
			//	Map<String, List<ProEmpDateVo>> costCenterMap = proEmpDateVos.stream().collect(Collectors.groupingBy(proEmpDateVo -> proEmpDateVo.getCostcenterId()));
			//	for (String ccId: costCenterMap.keySet()){
			//		List<ProEmpDateVo> costCenterList = costCenterMap.get(ccId);
			//		if (CollectionUtils.isNotEmpty(costCenterList)){
			//			//根据职级聚合
			//			Map<String, List<ProEmpDateVo>> bandTypeMap = costCenterList.stream().collect(Collectors.groupingBy(ProEmpDateVo::getBandType));
			//			for (String bandType:bandTypeMap.keySet()){
			//				List<ProEmpDateVo> bandTypeList = bandTypeMap.get(bandType);
			//				if (CollectionUtils.isNotEmpty(bandTypeList)){
			//					//根据人员类型聚合
			//					Map<String, List<ProEmpDateVo>> employeeTypeMaps = bandTypeList.stream().collect(Collectors.groupingBy(ProEmpDateVo::getEmployeeType));
			//					for (String employeeType : employeeTypeMaps.keySet()){
			//						List<ProEmpDateVo> proEmpDateVoList = employeeTypeMaps.get(employeeType);
			//						if (CollectionUtils.isEmpty(proEmpDateVoList)){
			//							continue;
			//						}
			//						double workDaysVal = 0d;
			//						Set<Integer> empIdSet = new HashSet<>();
			//						int manDay = 0;
			//						for (ProEmpDateVo empDateVo : proEmpDateVoList){
			//							String ym = empDateVo.getYearMonth();
			//							Integer workDays = ymMap.get(ym);
			//							workDaysVal+= (workDays==0 ? 0 : empDateVo.getManDay()/(double) workDays);
			//
			//							List<Integer> empIdList = empDateVo.getEmpIdList();
			//							empIdSet.addAll(empIdList);
			//
			//							manDay+= empDateVo.getManDay();
			//						}
			//
			//						BigDecimal workDaysValD = new BigDecimal(workDaysVal);
			//						double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			//						DecimalFormat df   = new DecimalFormat("#0.00");
			//						String pfmValueStr = df.format(workDayRes);
			//
			//						PrjDeptWorkingHourDataVo workingHourDataVo = new PrjDeptWorkingHourDataVo();
			//						workingHourDataVo.setPrjName(prjInfo.getPrjName());
			//						if (ccIdMap.containsKey(ccId)) {
			//							workingHourDataVo.setDepartName(ccIdMap.get(ccId));
			//						} else {
			//							workingHourDataVo.setDepartName("其他");
			//						}
			//						workingHourDataVo.setBandType(bandType);
			//						workingHourDataVo.setManDayNum(manDay);
			//						workingHourDataVo.setEmpNum(empIdSet.size());
			//						workingHourDataVo.setSbuId(prjInfo.getSbuId());
			//						workingHourDataVo.setPrjId(prjInfo.getPrjId());
			//						workingHourDataVo.setEmployeeType(employeeTypeMap.get(employeeType));
			//						workingHourDataVo.setManMonthNum(pfmValueStr);
			//						dataVos.add(workingHourDataVo);
			//						String buDataKey = "";
			//						if (ccId == null){
			//							buDataKey = prjInfo.getPrjId().toString()+"_"+workingHourDataVo.getBandType()+"_"+workingHourDataVo.getEmployeeType();
			//						} else {
			//							buDataKey = prjInfo.getPrjId().toString()+"_"+ccId+"_"+workingHourDataVo.getBandType()+"_"+workingHourDataVo.getEmployeeType();
			//						}
			//						uniqueDataMap.put(buDataKey, workingHourDataVo);
			//					}
			//				}
			//			}
			//		}
			//	}
			//}
			//if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
			//	for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
			//		TePrjInfo subPrjDef = prjIdToPrjInfoMap.get(subPrj.getCid());
			//		if(subPrj.getCid()!=null && !subPrj.getCid().equals(prjInfo.getPrjId()) && subPrjDef != null && empDateListMap.get(subPrjDef.getDmpPrjId()) != null) {
			//			List<ProEmpDateVo> proEmpDateVos = empDateListMap.get(subPrjDef.getDmpPrjId());
			//			//根据cc聚合
			//			Map<String, List<ProEmpDateVo>> costCenterMap = proEmpDateVos.stream().collect(Collectors.groupingBy(proEmpDateVo -> proEmpDateVo.getCostcenterId()));
			//			for (String ccId: costCenterMap.keySet()){
			//				List<ProEmpDateVo> costCenterList = costCenterMap.get(ccId);
			//				if (CollectionUtils.isNotEmpty(costCenterList)){
			//					//根据职级聚合
			//					Map<String, List<ProEmpDateVo>> bandTypeMap = costCenterList.stream().collect(Collectors.groupingBy(ProEmpDateVo::getBandType));
			//					for (String bandType:bandTypeMap.keySet()){
			//						List<ProEmpDateVo> bandTypeList = bandTypeMap.get(bandType);
			//						if (CollectionUtils.isNotEmpty(bandTypeList)){
			//							//根据人员类型聚合
			//							Map<String, List<ProEmpDateVo>> employeeTypeMaps = bandTypeList.stream().collect(Collectors.groupingBy(ProEmpDateVo::getEmployeeType));
			//							for (String employeeType : employeeTypeMaps.keySet()){
			//								List<ProEmpDateVo> proEmpDateVoList = employeeTypeMaps.get(employeeType);
			//								if (CollectionUtils.isEmpty(proEmpDateVoList)){
			//									continue;
			//								}
			//								double workDaysVal = 0d;
			//								Set<Integer> empIdSet = new HashSet<>();
			//								int manDay = 0;
			//								for (ProEmpDateVo empDateVo : proEmpDateVoList){
			//									String ym = empDateVo.getYearMonth();
			//									Integer workDays = ymMap.get(ym);
			//									workDaysVal+= (workDays==0 ? 0 : empDateVo.getManDay()/(double) workDays);
			//
			//									List<Integer> empIdList = empDateVo.getEmpIdList();
			//									empIdSet.addAll(empIdList);
			//
			//									manDay+= empDateVo.getManDay();
			//								}
			//
			//								BigDecimal workDaysValD = new BigDecimal(workDaysVal);
			//								double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			//								DecimalFormat df   = new DecimalFormat("#0.00");
			//								String pfmValueStr = df.format(workDayRes);
			//
			//								String uniqueKey = "";
			//								if (ccId == null){
			//									uniqueKey = prjInfo.getPrjId().toString()+"_"+bandType+"_"+employeeTypeMap.get(employeeType);
			//								} else {
			//									uniqueKey = prjInfo.getPrjId().toString()+"_"+ccId+"_"+bandType+"_"+employeeTypeMap.get(employeeType);
			//								}
			//								PrjDeptWorkingHourDataVo workingHourDataVo = uniqueDataMap.get(uniqueKey);
			//								if(workingHourDataVo != null) {
			//									workingHourDataVo.setManDayNum(workingHourDataVo.getManDayNum() + manDay);
			//									workingHourDataVo.setEmpNum(workingHourDataVo.getEmpNum() + empIdSet.size());
			//									workingHourDataVo.setManMonthNum(df.format((Double.valueOf(workingHourDataVo.getManMonthNum())+workDayRes)));
			//								}else {
			//									workingHourDataVo = new PrjDeptWorkingHourDataVo();
			//									workingHourDataVo.setPrjName(prjInfo.getPrjName());
			//									if (ccIdMap.containsKey(ccId)) {
			//										workingHourDataVo.setDepartName(ccIdMap.get(ccId));
			//									}else {
			//										workingHourDataVo.setDepartName("其他");
			//									}
			//									workingHourDataVo.setBandType(bandType);
			//									workingHourDataVo.setManDayNum(manDay);
			//									workingHourDataVo.setEmpNum(empIdSet.size());
			//									workingHourDataVo.setSbuId(prjIdToPrjInfoMap.get(subPrj.getCid()).getSbuId());
			//									workingHourDataVo.setPrjId(prjInfo.getPrjId());
			//									workingHourDataVo.setEmployeeType(employeeTypeMap.get(employeeType));
			//									workingHourDataVo.setManMonthNum(pfmValueStr);
			//									dataVos.add(workingHourDataVo);
			//									uniqueDataMap.put(uniqueKey, workingHourDataVo);
			//								}
			//							}
			//						}
			//					}
			//				}
			//			}
			//		}
			//	}
			//}

			if(dataVos.size() > 0) {
				result.addAll(dataVos);
			}
		}

//		int workDays = 0;
//		if (StringUtil.isNotNull(yearMonth)){
//			sysCalDao.getWorkDays(yearMonth.substring(0,4)+"-"+yearMonth.substring(4)).size();
//		}
//
//		// Map<String, TeSysDef> sbuCcid2SysDefMap = getSbuCcid2SysDefMap(subIds);
//		for(TePrjInfo prjInfo : prjInfos) {
//			List<PrjDeptWorkingHourDataVo> dataVos = new ArrayList<>();
//			if(prjInfo.getDmpPrjId() != null && empDateListMap.get(prjInfo.getDmpPrjId()) != null) {
////				String prjSbuId = prjInfo.getSbuId();
//				for(ProEmpDateVo empDateVo : empDateListMap.get(prjInfo.getDmpPrjId())) {
//
//					PrjDeptWorkingHourDataVo workingHourDataVo = new PrjDeptWorkingHourDataVo();
//					workingHourDataVo.setPrjName(prjInfo.getPrjName());
//
//					// String sbuCcidKey = prjSbuId + "_" + empDateVo.getCostcenterId();
//					String ccId = empDateVo.getCostcenterId();
////					if(sbuCcid2SysDefMap.containsKey(sbuCcidKey)) {
////						TeSysDef deptDef = sbuCcid2SysDefMap.get(sbuCcidKey);
//					if (ccIdMap.containsKey(ccId)) {
////						workingHourDataVo.setDepartName(deptDef.getDefName());
//						workingHourDataVo.setDepartName(ccIdMap.get(ccId));
//						workingHourDataVo.setBandType(empDateVo.getBandType());
//						workingHourDataVo.setManDayNum(empDateVo.getManDay());
//						workingHourDataVo.setEmpNum(empDateVo.getManNum());
//						workingHourDataVo.setSbuId(prjInfo.getSbuId());
//						workingHourDataVo.setPrjId(prjInfo.getPrjId());
//						workingHourDataVo.setEmployeeType(employeeTypeMap.get(empDateVo.getEmployeeType()));
//						dataVos.add(workingHourDataVo);
//
////						String buDataKey = prjInfo.getPrjId().toString()+"_"+deptDef.getId().toString()
////								+"_"+workingHourDataVo.getBandType()+"_"+workingHourDataVo.getEmployeeType();
//						String buDataKey = prjInfo.getPrjId().toString()+"_"+ccId+"_"+workingHourDataVo.getBandType()+"_"+workingHourDataVo.getEmployeeType();
//
//						uniqueDataMap.put(buDataKey, workingHourDataVo);
//					} else {
//						String otherKey = prjInfo.getPrjId().toString() + "_" + empDateVo.getBandType() + "_" + employeeTypeMap.get(empDateVo.getEmployeeType());
//						if(otherManNumMap.containsKey(otherKey)) {
//							otherManNumMap.put(otherKey, otherManNumMap.get(otherKey)+empDateVo.getManNum());
//						}else {
//							otherManNumMap.put(otherKey, empDateVo.getManNum());
//						}
//
//						if(otherManDayMap.containsKey(otherKey)) {
//							otherManDayMap.put(otherKey, otherManDayMap.get(otherKey)+empDateVo.getManDay());
//						}else {
//							otherManDayMap.put(otherKey, empDateVo.getManDay());
//						}
//					}
//				}
//			}
//
//			if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
//				for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
//					TePrjInfo subPrjDef = prjIdToPrjInfoMap.get(subPrj.getCid());
//					if(subPrj.getCid()!=null && !subPrj.getCid().equals(prjInfo.getPrjId()) && subPrjDef != null && empDateListMap.get(subPrjDef.getDmpPrjId()) != null) {
//						for(ProEmpDateVo empDateVo : empDateListMap.get(prjIdToPrjInfoMap.get(subPrj.getCid()).getDmpPrjId())) {
////							String sbuCcidKey = subPrjDef.getSbuId() + "_" + empDateVo.getCostcenterId();
////							if(sbuCcid2SysDefMap.containsKey(sbuCcidKey)) {
////								TeSysDef deptDef = sbuCcid2SysDefMap.get(sbuCcidKey);
//
//							String ccId = empDateVo.getCostcenterId();
//							if (ccIdMap.containsKey(ccId)) {
//
////								String uniqueKey = prjInfo.getPrjId().toString()+"_"+deptDef.getId().toString()+"_"+empDateVo.getBandType()+"_"+employeeTypeMap.get(empDateVo.getEmployeeType());
//								String uniqueKey = prjInfo.getPrjId().toString()+"_"+ccId+"_"+empDateVo.getBandType()+"_"+employeeTypeMap.get(empDateVo.getEmployeeType());
//								PrjDeptWorkingHourDataVo workingHourDataVo = uniqueDataMap.get(uniqueKey);
//								if(workingHourDataVo != null) {
//									workingHourDataVo.setManDayNum(workingHourDataVo.getManDayNum() + empDateVo.getManDay());
//									workingHourDataVo.setEmpNum(workingHourDataVo.getEmpNum() + empDateVo.getManNum());
//								}else {
//									workingHourDataVo = new PrjDeptWorkingHourDataVo();
//									workingHourDataVo.setPrjName(prjInfo.getPrjName());
////									workingHourDataVo.setDepartName(deptDef.getDefName());
//									workingHourDataVo.setDepartName(ccIdMap.get(ccId));
//									workingHourDataVo.setBandType(empDateVo.getBandType());
//									workingHourDataVo.setManDayNum(empDateVo.getManDay());
//									workingHourDataVo.setEmpNum(empDateVo.getManNum());
//									workingHourDataVo.setSbuId(prjIdToPrjInfoMap.get(subPrj.getCid()).getSbuId());
//									workingHourDataVo.setPrjId(prjInfo.getPrjId());
//									workingHourDataVo.setEmployeeType(employeeTypeMap.get(empDateVo.getEmployeeType()));
//									dataVos.add(workingHourDataVo);
//
//									String newUniqueKey = prjInfo.getPrjId().toString()+"_"+ccId+"_"+workingHourDataVo.getBandType()+"_"+workingHourDataVo.getEmployeeType();
//									uniqueDataMap.put(newUniqueKey, workingHourDataVo);
//								}
//							}else {
//								String otherKey = prjInfo.getPrjId().toString() + "_" + empDateVo.getBandType() + "_" + employeeTypeMap.get(empDateVo.getEmployeeType());
//								if(otherManNumMap.containsKey(otherKey)) {
//									otherManNumMap.put(otherKey, otherManNumMap.get(otherKey)+empDateVo.getManNum());
//								}else {
//									otherManNumMap.put(otherKey, empDateVo.getManNum());
//								}
//
//								if(otherManDayMap.containsKey(otherKey)) {
//									otherManDayMap.put(otherKey, otherManDayMap.get(otherKey)+empDateVo.getManDay());
//								}else {
//									otherManDayMap.put(otherKey, empDateVo.getManDay());
//								}
//							}
//						}
//					}
//				}
//			}
//
//			if(dataVos.size() > 0) {
//				result.addAll(dataVos);
//			}
//		}
//
//		for(Map.Entry<String, Integer> entry : otherManNumMap.entrySet()) {
//			String[] keys = entry.getKey().split("_");
//
//			String strPrjId = keys[0];
//			String bandType = keys[1];
//			String employeeType = keys[2];
//			PrjDeptWorkingHourDataVo dataVo = new PrjDeptWorkingHourDataVo();
//			dataVo.setPrjName(prjIdToPrjInfoMap.get(new ObjectId(strPrjId)).getPrjName());
//			dataVo.setDepartName("其他");
//			dataVo.setBandType(bandType);
//			dataVo.setEmpNum(entry.getValue());
//			dataVo.setManDayNum(otherManDayMap.get(entry.getKey()));
//
//			double workDaysVal = workDays == 0 ? 0 : dataVo.getManDayNum() / (double) workDays;
//			BigDecimal workDaysValD = new BigDecimal(workDaysVal);
//			double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
//			DecimalFormat df = new DecimalFormat("#0.00");
//			String pfmValueStr = df.format(workDayRes);
//
//			dataVo.setManMonthNum(pfmValueStr);
//			dataVo.setEmployeeType(employeeType);
//			result.add(dataVo);
//		}
		
		
		Collections.sort(result, new Comparator<PrjDeptWorkingHourDataVo>() {
			@Override
			public int compare(PrjDeptWorkingHourDataVo o1, PrjDeptWorkingHourDataVo o2) {
				if(o1.getPrjName().equals(o2.getPrjName())) {
					if(o1.getDepartName().equals(o2.getDepartName())) {
						int o1BandTypeWeight = getBandTypeSortWeight(o1.getBandType());
						int o2BandTypeWeight = getBandTypeSortWeight(o2.getBandType());
						return o1BandTypeWeight - o2BandTypeWeight;
					}else {
						if(o1.getDepartName().equals("其他") || o2.getDepartName().equals("其他")) {
							int o1DepartWeight = o1.getDepartName().equals("其他") ? 1 : 0;
							int o2DepartWeight = o2.getDepartName().equals("其他") ? 1 : 0;
							return o1DepartWeight - o2DepartWeight;
						}else {
							return o1.getDepartName().compareTo(o2.getDepartName());
						}
					}
				}else {
					return o1.getPrjName().compareTo(o2.getPrjName());
				}
			}
		});
		
		//合计
		PrjDeptWorkingHourDataVo total = new PrjDeptWorkingHourDataVo();
		total.setPrjName("合计");
		total.setDepartName("");
		total.setBandType("");
		int totalEmpNum = 0;
		int totalManDayNum = 0;
		double totalManMonthNum = 0;
		for(PrjDeptWorkingHourDataVo data : result) {
			//double workDaysVal = (workDays==0 ? 0 : data.getManDayNum()/(double) workDays);
			//BigDecimal workDaysValD = new BigDecimal(workDaysVal);
			//double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			//DecimalFormat df   = new DecimalFormat("#0.00");
			//String pfmValueStr = df.format(workDayRes);
			//data.setManMonthNum(pfmValueStr);
			totalManMonthNum+=StringUtil.toDouble(data.getManMonthNum());
			totalEmpNum += data.getEmpNum();
			totalManDayNum += data.getManDayNum();
		}
		total.setEmpNum(totalEmpNum);
		total.setManDayNum(totalManDayNum);
		//double workDaysVal = workDays == 0 ? 0 : totalManDayNum / (double) workDays;
		//BigDecimal workDaysValD = new BigDecimal(workDaysVal);
		//double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
		DecimalFormat df = new DecimalFormat("#0.00");
		String pfmValueStr = df.format(totalManMonthNum);
		total.setManMonthNum(pfmValueStr);
		result.add(total);
		
		return result;
	}

	// 查询HR人员部门信息
	private List<TeItfDept> queryItfDeptData() {

		// 查询人员信息
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DbFieldName.itfDept_orgId);
		fieldNames.add(DbFieldName.itfDept_orgNameZh);
		fieldNames.add(DbFieldName.itfDept_orgParentId);
		fieldNames.add(DbFieldName.itfDept_orgParentName);
		fieldNames.add(DbFieldName.itfDept_orgParentZh);
		fieldNames.add(DbFieldName.itfDept_orgCurLevel);
		fieldNames.add(DbFieldName.itfDept_orgType);
		fieldNames.add(DbFieldName.itfDept_orgCode);
		fieldNames.add(DbFieldName.itfDept_manager);
		List<IDbCondition> userConds = new ArrayList<>();
		List<TeItfDept> deptList = prjItfDeptDao.findByFieldAndConds(userConds, fieldNames);
		return deptList;
	}

	/**
	 *  获取Map<orgCode, orgParentZh>
	 * 	org_cur_level<=6,直接取orgParentZh
	 * 	org_cur_level>6, 根据org_parent_id和org_id往上递归查找，直到org_cur_level为4时停止，并取其org_parent_zh值就可以了。
	 *
	 * 	改成 orgCurLevel > 6, 向上递归查找，到6时，取orgParentZh
	 * 	orgCurLevel < 6, 直接取orgNameZh
	 */
	private Map<String, Map<String, String>> packageCCIdDeptNameMap(List<TeItfDept> deptList) {

		Map<String, Map<String, String>> ccIdMap = new HashMap<>();
		if (null == deptList || deptList.isEmpty())
			return ccIdMap;

		Map<String, TeItfDept> itfDeptMap = new HashMap<>();
		for (TeItfDept dept : deptList) {
			String orgId = dept.getOrgId();
			itfDeptMap.put(orgId, dept);
		}

		TeItfDept itfDept = null;
		for (TeItfDept dept : deptList) {
			int orgCurLevel = dept.getOrgCurLevel();
			String orgId = dept.getOrgId();
			String orgNameZh = dept.getOrgNameZh();
			String orgParentId = dept.getOrgParentId();
			String orgCode = dept.getOrgCode();
			// System.out.println("orgCurLevel=" + orgCurLevel + ", orgParentId="+ orgParentId + ", orgParentZh=" + orgParentZh + ", orgCode=" + orgCode);

			while(orgCurLevel > 5) {
				if (orgId.equals(orgParentId)) {
					break;
				}
				itfDept = itfDeptMap.get(orgParentId);
				if (null == itfDept) break;
				orgId = itfDept.getOrgId();
				orgNameZh = itfDept.getOrgNameZh();
				orgCurLevel = itfDept.getOrgCurLevel();
				orgParentId = itfDept.getOrgParentId();
			}
			Map<String, String> orgMap = new HashMap<>();
			orgMap.put("orgId", orgId);
			orgMap.put("orgName", orgNameZh);
			ccIdMap.put(orgCode, orgMap);
		}
		return ccIdMap;
	}

	private Map<String, TeSysDef> getSbuCcid2SysDefMap(List<String> subIds) {
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName)
				, SysDefTypeCodeName.RESOURCE_DEPT.getValue()));
		conds.add(new DC_I<>(DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefCodeName), subIds));
		List<TeSysDef> sysDefs = sysDefDao.findByConds(conds, null);
		Map<String, TeSysDef> sbuCcid2SysDefMap = new HashMap<>();
		for(TeSysDef sysDef : sysDefs) {
			String sbuId = sysDef.getSrcDef().getSrcDefCodeName();
			if(StringUtil.isNotNull(sysDef.getDefDesc())) {
				String[] ccIds = sysDef.getDefDesc().split(",");
				for(String ccId : ccIds) {
					if(StringUtil.isNotNull(ccId)) {
						sbuCcid2SysDefMap.put(sbuId+"_"+ccId.trim(), sysDef);
					}
				}
			}
		}
		return sbuCcid2SysDefMap;
	}

	public PrjEmpYearVo getWorkingHoursData(ObjectId prjId){
		if(prjId == null ) {
			return null;
		}
		
		List<TePrjInfo> prjInfos = new ArrayList<>();
		List<ObjectId> prjIdList = new ArrayList<>();
		if(prjId != null) {
			prjIdList.add(prjId);
			prjInfos.addAll(prjInfoService.queryPrjInfoByPrjdefId(prjIdList));
		}
		if(prjInfos.size() == 0) {
			return new PrjEmpYearVo();//new ArrayList<>();
		}

		List<ObjectId> subPrjIdList = new ArrayList<>();
		List<String> subIds = new ArrayList<>();//剔重之后的下级(subId)
		Map<ObjectId, TePrjInfo> prjIdToPrjInfoMap = new HashMap<>();//项目及子项目TePrjInfo对象的prjId(ObjectId)：项目及子项目TePrjInfo对象
		for(TePrjInfo prjInfo : prjInfos) {
			if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
				for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
					subPrjIdList.add(subPrj.getCid());
				}
			}
			if(prjInfo.getSbuId()!=null && !subIds.contains(prjInfo.getSbuId())) {
				subIds.add(prjInfo.getSbuId());
			}
			prjIdToPrjInfoMap.put(prjInfo.getPrjId(), prjInfo);//获取所有项目TePrjInfo对象
		}
		List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIdList);//获取所有子项目TePrjInfo对象
		if(subPrjList!=null && subPrjList.size()>0) {
			for(TePrjInfo subPrj : subPrjList) {
				prjIdToPrjInfoMap.put(subPrj.getPrjId(), subPrj);//获取所有项目的子项目TePrjInfo对象
			}
		}
		
		List<PrjDeptWorkingHourDataVo> result = new ArrayList<>();

		//Map<Integer, List<ProEmpDateVo>> empDateListMap = antDbDao.getEmpDataList(dmpPrjIds);//从pro_emp_date表获取ProEmpDateVo对象
		Map<ObjectId, List<ProEmpDateFromRmsVo>> empDateListMap = rmsEmpDateFeignClient
				.getEmpDateList(subPrjIdList).getData(true);

		if(empDateListMap.size() == 0) {
			return new PrjEmpYearVo();//result;
		}
		
		for(TePrjInfo prjInfo : prjInfos) {
				List<PrjDeptWorkingHourDataVo> dataVos = new ArrayList<>();
				//获取项目下所有workingHourDataVo信息
				//if(prjInfo.getDmpPrjId() != null && empDateListMap.get(prjInfo.getDmpPrjId()) != null) {
				//	for(ProEmpDateFromRmsVo empDateVo : empDateListMap.get(prjInfo.getDmpPrjId())) {
				//		PrjDeptWorkingHourDataVo workingHourDataVo = new PrjDeptWorkingHourDataVo();
				//			workingHourDataVo.setPrjName(prjInfo.getPrjName());
				//		//	workingHourDataVo.setDepartName(deptDef.getDefName());
				//			workingHourDataVo.setBandType(empDateVo.getBandType());
				//			workingHourDataVo.setManDayNum(empDateVo.getManDay());
				//			workingHourDataVo.setEmpNum(empDateVo.getEmpIdList() == null ? 0 : empDateVo.getEmpIdList().size());
				//			workingHourDataVo.setSbuId(prjInfo.getSbuId());
				//			workingHourDataVo.setPrjId(prjInfo.getPrjId());
				//			workingHourDataVo.setYearMonth(empDateVo.getYearMonth());//所在月份
				//
				//			dataVos.add(workingHourDataVo);
				//		}
				//	}
				//获取项目的子的所有workingHourDataVo信息
				if(prjInfo.getSubPrjs()!=null && prjInfo.getSubPrjs().size()>0) {
						for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
							TePrjInfo subPrjDef = prjIdToPrjInfoMap.get(subPrj.getCid());
							if(subPrj.getCid()!=null && !subPrj.getCid().equals(prjInfo.getPrjId()) && subPrjDef != null 
									&& empDateListMap.get(subPrjDef.getPrjId()) != null) {
								for(ProEmpDateFromRmsVo empDateVo : empDateListMap.get(subPrjDef.getPrjId())) {
									        PrjDeptWorkingHourDataVo workingHourDataVo = new PrjDeptWorkingHourDataVo();
											workingHourDataVo.setPrjName(prjInfo.getPrjName());
											//workingHourDataVo.setDepartName(deptDef.getDefName());
											workingHourDataVo.setBandType(empDateVo.getBandType());
											workingHourDataVo.setManDayNum(empDateVo.getManDay());
											workingHourDataVo.setEmpNum(empDateVo.getEmpIdList() == null ? 0 : empDateVo.getEmpIdList().size());
											workingHourDataVo.setSbuId(prjIdToPrjInfoMap.get(subPrj.getCid()).getSbuId());
											workingHourDataVo.setPrjId(prjInfo.getPrjId());
											workingHourDataVo.setYearMonth(empDateVo.getYearMonth());//所在月份
											dataVos.add(workingHourDataVo);
									
								}
							}
						}
				}
				if(dataVos.size() > 0) {
					result.addAll(dataVos);
				}
		}
		//计算人月
		for(PrjDeptWorkingHourDataVo data : result) {
			//根据所在月份,计算所在月的工作日数
			String yearMonth = data.getYearMonth();
			int workDays = sysCalDao.getWorkDays(yearMonth).size();
			double workDaysVal = workDays == 0 ? 0 : data.getManDayNum() / (double) workDays;
			BigDecimal workDaysValD = new BigDecimal(workDaysVal);
			double workDayRes = workDaysValD.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			DecimalFormat df = new DecimalFormat("#0.00");
			String pfmValueStr = df.format(workDayRes);
			data.setManMonthNum(pfmValueStr);
		}
		PrjEmpYearVo res = this.getWorkEmpMonthMap(result);
		return res;
	}

	@Override
	public PrjEmpYearMonthVo getWorkingHoursDataByPlEmpInput(ObjectId prjId) {
		PrjEmpYearMonthVo empYearMonthVo = new PrjEmpYearMonthVo();
		Map<String,List<PrjDeptMonthWorkingHourDataVo>> result = new HashMap<>();
		TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toString());
		if (null != prjInfo){
			List<TePrjInfoPrjBmks> prjBmks = prjInfo.getPrjBmks();
			if (null != prjBmks){
				ObjectId maxBudgetVerId = prjInfoService.getPrjMaxBudgetVerId(prjId);
				List<TePrjBudget> prjBudgets = prjBudgetService
						.getPrjBudgetByVerIdAndPrjId(maxBudgetVerId.toString(), prjId.toString());
				String SameMonth = "";
				String nextMonth = DateUtil.getNextMonth(new Date(),1);
				String[] split = nextMonth.split("-");
				for (String s : split){
					SameMonth = SameMonth+s;
				}
				List<String> yearMonthList = new ArrayList<>();
				for (TePrjBudget prjBudget : prjBudgets) {
					String ym = prjBudget.getYm();
					if (null != prjBudget && StringUtil.isNotNull(ym)){
						if (SameMonth.equals(ym)){
							break;
						}
						if (!yearMonthList.contains(ym)){
							yearMonthList.add(ym);
						}

						List<PrjDeptMonthWorkingHourDataVo> hourDataList = new ArrayList<>();
						List<TePrjBudget2EmpInput> plEmpInput = prjBudget.getPlEmpInput();
						if (CollectionUtils.isNotEmpty(plEmpInput)){
							for (TePrjBudget2EmpInput tePrjBudget2EmpInput : plEmpInput) {
								if (null != tePrjBudget2EmpInput && null != tePrjBudget2EmpInput.getName()){
									PrjDeptMonthWorkingHourDataVo workingHourData = new PrjDeptMonthWorkingHourDataVo();
									workingHourData.setDepartName(tePrjBudget2EmpInput.getName());
									//部门项目基准投入（人月）
									workingHourData.setPrjBenchmarkNum(StringUtil.getNotNullStr(tePrjBudget2EmpInput.getEmpInput()));
									hourDataList.add(workingHourData);
								}
							}
						}
						result.put(ym,hourDataList);
					}
				}
				empYearMonthVo.setYearMonthList(yearMonthList);
			}

//			List<Integer> dmpPrjIds = new ArrayList<>();
			List<ObjectId> subPrjIdList = new ArrayList<>();
			List<String> subIds = new ArrayList<>();
			List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
			if (null != subPrjs && !subPrjs.isEmpty()){
				for(TePrjInfoSubPrj subPrj : subPrjs) {
					subPrjIdList.add(subPrj.getCid());
				}
				if(prjInfo.getSbuId()!=null && !subIds.contains(prjInfo.getSbuId())) {
					subIds.add(prjInfo.getSbuId());
				}
				List<TePrjInfo> subPrjList = prjInfoService.queryPrjInfoByPrjdefId(subPrjIdList);
//				if(null != subPrjList && !subPrjList.isEmpty()) {
//					for(TePrjInfo subPrj : subPrjList) {
//						if(null != subPrj.getDmpPrjId()) {
//							dmpPrjIds.add(subPrj.getDmpPrjId());
//						}
//					}
//				}
				Map<String, TeSysDef> sbuCcid2SysDefMap = getSbuCcid2SysDefMap(subIds);

//				Map<Integer, List<ProEmpDateVo>> empDateListMap = antDbDao.getEmpDataListByMonth(dmpPrjIds);
				List<RmsManDayAndNumVo> mdnVoList = rmsEmpDateFeignClient.listPrjManDayAndNumInfo(subPrjIdList).getData(true);
				if (CollectionUtils.isNotEmpty(mdnVoList)){
					Map<ObjectId, List<RmsManDayAndNumVo>> prjId2ManDayAndNumMap = mdnVoList.stream().collect(Collectors.groupingBy(s->s.getPrjId()));
					for (TePrjInfo subPrjInfo : subPrjList) {
						ObjectId subPrjId = subPrjInfo.getPrjId();
						if (null != subPrjInfo && null != subPrjId && null != prjId2ManDayAndNumMap.get(subPrjId)){
							for(RmsManDayAndNumVo empDateVo : prjId2ManDayAndNumMap.get(subPrjId)) {
								String sbuCcidKey = subPrjInfo.getSbuId() + "_" + empDateVo.getCostCenterId();
								List<PrjDeptMonthWorkingHourDataVo> hourDataVos = result.get(empDateVo.getYm());
								if(sbuCcid2SysDefMap.containsKey(sbuCcidKey)) {
									TeSysDef deptDef = sbuCcid2SysDefMap.get(sbuCcidKey);
									setResult(empYearMonthVo, result, empDateVo, hourDataVos, deptDef.getDefName());
								} else {
									setResult(empYearMonthVo, result, empDateVo, hourDataVos, "其他");
								}
							}
						}
					}
				}
			}
		}
		for (String ym : result.keySet()) {
			int workDays = sysCalDao.getWorkDays(ym.substring(0,4)
					+"-"+ym.substring(4)).size();
			List<PrjDeptMonthWorkingHourDataVo> prjDeptMonthWorkingHourDataVos = result.get(ym);
			for (PrjDeptMonthWorkingHourDataVo hourDataVo : prjDeptMonthWorkingHourDataVos) {
				//工时系统投入（人月）
				hourDataVo.setWorkingHourNum(workDays==0 ?
						0 : Math.round(hourDataVo.getWorkingHourNum()/(double) workDays));
			}
		}
		if (CollectionUtils.isNotEmpty(empYearMonthVo.getYearMonthList())) {
			Collections.sort(empYearMonthVo.getYearMonthList());
		}
		empYearMonthVo.setResult(result);
		return empYearMonthVo;
	}

	private void setResult(PrjEmpYearMonthVo empYearMonthVo, Map<String, List<PrjDeptMonthWorkingHourDataVo>> result,
						   RmsManDayAndNumVo empDateVo, List<PrjDeptMonthWorkingHourDataVo> hourDataVos, String deptName) {
		if (null == hourDataVos || hourDataVos.isEmpty()) {
			hourDataVos = new ArrayList<>();
			PrjDeptMonthWorkingHourDataVo vo = new PrjDeptMonthWorkingHourDataVo();
			vo.setDepartName(deptName);
			vo.setWorkingHourNum(empDateVo.getManDay());
			hourDataVos.add(vo);
			List<String> yearMonthList = empYearMonthVo.getYearMonthList();
			if (CollectionUtils.isEmpty(yearMonthList)){
				yearMonthList = new ArrayList<>();
			}
			if (!yearMonthList.contains(empDateVo.getYm()) && StringUtil.isNotNull(empDateVo.getYm())){
				yearMonthList.add(empDateVo.getYm());
			}
			empYearMonthVo.setYearMonthList(yearMonthList);
			result.put(empDateVo.getYm(), hourDataVos);
		} else {
			boolean isHave = false;
			for (PrjDeptMonthWorkingHourDataVo hourDataVo : hourDataVos) {
				if (hourDataVo.getDepartName().equals(deptName)) {
					isHave = true;
					hourDataVo.setWorkingHourNum(hourDataVo
							.getWorkingHourNum() + empDateVo.getManDay());
				}
			}
			if (!isHave) {
				PrjDeptMonthWorkingHourDataVo vo = new PrjDeptMonthWorkingHourDataVo();
				vo.setDepartName(deptName);
				vo.setWorkingHourNum(empDateVo.getManDay());
				hourDataVos.add(vo);
			}
		}
	}

	private PrjEmpYearVo getWorkEmpMonthMap(List<PrjDeptWorkingHourDataVo> result){
		PrjEmpYearVo  prjEmpYearVo = new PrjEmpYearVo();
		Set<String> dataLineSet = new HashSet<String>();
		for (PrjDeptWorkingHourDataVo prjDeptWorkingHourDataVo : result){
			String yearMonth = prjDeptWorkingHourDataVo.getYearMonth();
			dataLineSet.add(yearMonth);
		}
		List<PrjDeptWorkingHourDataVo>  highResultList = new ArrayList<PrjDeptWorkingHourDataVo>();
		List<PrjDeptWorkingHourDataVo>  middleResultList = new ArrayList<PrjDeptWorkingHourDataVo>();
		List<PrjDeptWorkingHourDataVo>  primaryResultList = new ArrayList<PrjDeptWorkingHourDataVo>();
		for (PrjDeptWorkingHourDataVo prjDeptWorkingHourDataVo : result) {
			String bandType = prjDeptWorkingHourDataVo.getBandType();
			if(PrjReportConstants.PRJREPORT_BANDTYPE_DEFNAME_HIGH.equals(bandType)){
				highResultList.add(prjDeptWorkingHourDataVo);
			}
			if(PrjReportConstants.PRJREPORT_BANDTYPE_DEFNAME_MIDDLE.equals(bandType)){
				middleResultList.add(prjDeptWorkingHourDataVo);
			}
			if(PrjReportConstants.PRJREPORT_BANDTYPE_DEFNAME_PRIMARY.equals(bandType)){
				primaryResultList.add(prjDeptWorkingHourDataVo);
			}
		}
		prjEmpYearVo.setDataLine(dataLineSet);
		prjEmpYearVo.setHighResult(highResultList);
		prjEmpYearVo.setMiddleResult(middleResultList);
		prjEmpYearVo.setPrimaryResult(primaryResultList);
    	return  prjEmpYearVo;
	}
	
	@Override
	public PageBean exportDeptWorkingHoursData(ObjectId prjId, String startYearMonth, String endYearMonth,List<ObjectId> prjLevelIds, List<String> sbuCodes){
		List<PrjDeptWorkingHourDataVo> prjDeptWorkingHoursData = this.getDeptWorkingHoursData(prjId, startYearMonth, endYearMonth, prjLevelIds, sbuCodes);
		List<LinkedHashMap<Object, Object>> list = new ArrayList<LinkedHashMap<Object, Object>>();
		LinkedHashMap<Object, Object> titleMap0 = new LinkedHashMap<Object, Object>();
		int index = 0;
		titleMap0.put(index++, "项目");
		titleMap0.put(index++, "部门");
		titleMap0.put(index++, "人员类型");
		titleMap0.put(index++, "职级");
		titleMap0.put(index++, "人员");
		titleMap0.put(index++, "人日");
		titleMap0.put(index++, "人月");
		list.add(titleMap0);
		for(PrjDeptWorkingHourDataVo workingHourDetail : prjDeptWorkingHoursData){
			LinkedHashMap<Object, Object> linkedMap = new LinkedHashMap<Object, Object>();
			linkedMap.put("prjName", workingHourDetail.getPrjName());
			linkedMap.put("departName", workingHourDetail.getDepartName());
			linkedMap.put("employeeType", workingHourDetail.getEmployeeType());
			linkedMap.put("bandType", workingHourDetail.getBandType());
			linkedMap.put("empNum", workingHourDetail.getEmpNum());
			linkedMap.put("manDayNum", workingHourDetail.getManDayNum());
			linkedMap.put("manMonthNum", workingHourDetail.getManMonthNum());
			list.add(linkedMap);
			
		}
		PageBean bean = new PageBean();
		bean.setObjectList(list);
		return bean;
	}
	
	private int getBandTypeSortWeight(String bandType) {
		if(bandType.equals("初")) {
			return 1;
		}else if(bandType.equals("中")){
			return 2;
		}else if(bandType.equals("高")) {
			return 3;
		}else {
			return 4;
		}
	}

	public List<Map<String,Integer>> getPopulationTrend(ObjectId prjId, String type) {
		//返回结果
		List<Map<String,Integer>> result = new ArrayList<>();
		//这个是用来存放 日期与对应的在项人员，人员已经去重过了
		Map<String,Set<String>> map = new HashMap<>();
		//查询结束日期:实际交维日期,如果没有则选择今天
		TePrjInfo prjInfo = prjInfoService.getTePrjInfoById(prjId.toHexString());
		Date actualOmDate = prjInfo.getActualOmDate();
		if(actualOmDate == null){
			actualOmDate = DateUtil.parseDate(DateUtil.formatDate2Str(new Date(), DateUtil.DATE_FORMAT), DateUtil.DATE_FORMAT);
		}else{
			actualOmDate = DateUtil.parseDate(DateUtil.formatDate2Str(prjInfo.getActualOmDate(), DateUtil.DATE_FORMAT), DateUtil.DATE_FORMAT);//交维时间
		}
		List<IDbCondition> fieldConds = new ArrayList<>();
		List<DbFieldName>  fieldNames = new ArrayList<>();
		fieldConds.add(new DbEqualCondition(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
		fieldNames.add(DFN.sysDefRoleUser_enterTime);
		fieldNames.add(DFN.sysDefRoleUser_quitTime);
		fieldNames.add(DFN.sysDefRoleUser__roleUser);
		fieldNames.add(DFN.sysDefRoleUser_isValid);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.findByFieldAndConds(fieldConds, fieldNames);
		Date minDate = actualOmDate;//统计表中的起始时间，统计表中结束时间有了就是 actualOmDate
		for (TeSysDefRoleUser user:sysDefRoleUserList){
			Date enterTime = user.getEnterTime();
			Date quitTime = user.getQuitTime();
			Boolean isValid = user.getIsValid();
			if(enterTime == null){
				//说明这条数据应该是历史数据中表示已经删除的数据
				continue;
			}
			if(quitTime == null && isValid){//说明现在还是在项的
				//1.比较 actualOmDate < enterTime
				if(DateUtil.compareDate(enterTime,actualOmDate)>0){
					//这条数据不记录进来
					continue;
				}else {//enterTime < actualOmDate
					List<Date> datesBetween = DateUtil.getDatesBetween(enterTime, actualOmDate);
					minDate = putValueIntoMap(datesBetween,map,user.getRoleUser().getLoginName(),minDate,type);
				}
			}else if(!isValid && quitTime != null){//已离项
				//1. enterTime <= actualOmDate <= quitTime
				if(DateUtil.compareDate(enterTime,actualOmDate)<=0 && DateUtil.compareDate(actualOmDate,quitTime)<=0){
					List<Date> datesBetween = DateUtil.getDatesBetween(enterTime, actualOmDate);
					minDate = putValueIntoMap(datesBetween,map,user.getRoleUser().getLoginName(),minDate,type);
				}
				//2. actualOmDate < enterTime < quitTime
				if(DateUtil.compareDate(actualOmDate,enterTime)<0 && DateUtil.compareDate(enterTime,quitTime)<0){
					//不在统计范围，过
					continue;
				}
				//3. enterTime < quitTime <= actualOmDate
				if(DateUtil.compareDate(quitTime,actualOmDate)<=0){
					List<Date> datesBetween = DateUtil.getDatesBetween(enterTime, quitTime);
					minDate = putValueIntoMap(datesBetween,map,user.getRoleUser().getLoginName(),minDate,type);
				}
			}
		}
		//根据类型判断 是按月统计还是按天统计
		if(type.equals("day")){
			//获取统计日期内所有时间
			List<Date> dateRange = DateUtil.getDatesBetween(minDate, actualOmDate);
			for(Date d:dateRange){
				String dateStr = DateUtil.formatDate2Str(d,DateUtil.DATE_FORMAT);
				Map<String,Integer> singleResult = new HashMap<>();
				if(map.get(dateStr) != null){
					singleResult.put(dateStr,map.get(dateStr).size());
					result.add(singleResult);
				}else {
					singleResult.put(dateStr,0);
					result.add(singleResult);
				}
			}
		}else{//按月统计
			//获取统计日期内所有月
			List<Date> dateRange = DateUtil.getMonthBetween(minDate, actualOmDate);
			for(Date d:dateRange){
				String dateStr = DateUtil.formatDate2Str(d,DateUtil.DATE_MONTH_FOTMAT2);
				Map<String,Integer> singleResult = new HashMap<>();
				if(map.get(dateStr) != null){
					singleResult.put(dateStr,map.get(dateStr).size());
					result.add(singleResult);
				}else {
					singleResult.put(dateStr,0);
					result.add(singleResult);
				}
			}
		}
		return result;
	}

	private Date putValueIntoMap(List<Date> datesBetween, Map<String,Set<String>> map, String loginName ,Date minDate, String type){
		for (Date date:datesBetween){
			if(minDate == null || DateUtil.compareDate(minDate,date)>0){
				minDate = date;
			}
			String dateStr = null;
			if(type.equals("day")){
				dateStr = DateUtil.formatDate2Str(date,DateUtil.DATE_FORMAT);
			}else {
				dateStr = DateUtil.formatDate2Str(date,DateUtil.DATE_MONTH_FOTMAT2);
			}
			Set<String> ntList = map.get(dateStr);
			if(ntList == null){
				ntList = new HashSet<>();
			}
			ntList.add(loginName);
			map.put(dateStr,ntList);
		}
		return minDate;
	}

	/**
	 *	项目小组人数统计
	 * @param prjId 项目ID
	 */
	@Override
	public CommonResult prjGroupUserCount(ObjectId prjId) {
		//先从“sysDef”中取出defType.defTypecodeName为prjGroup下的且srcDef.srcDefId为该项目ID下的且parentDefId不为null下的记录，
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
		conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId),prjId));
		conds.add(new DC_E(DFN.sysDef__parentDefId, null, true));
		conds.add(new DC_E(DFN.sysDef__isValid,true));
		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysDef_id);
		fieldNameList.add(DFN.sysDef__parentDefId);
		fieldNameList.add(DFN.sysDef__parentDefPath);
		fieldNameList.add(DFN.sysDef__defName);
		fieldNameList.add(DFN.sysDef__defNo);
		Sort sort = Sort.by(Sort.Direction.ASC,DFN.sysDef__defNo.n());
		//小组信息
		List<TeSysDef> groupDefs = sysDefDao.findByFieldAndConds(conds, fieldNameList,sort);
		if (CollectionUtils.isEmpty(groupDefs)){
			return null;
		}
		//获取项目管理层小组信息
		List<TeSysDef> prjAdminGroupList = groupDefs.stream().filter(group -> group.getDefName().equals(SysDefConstants.ROOTGROUP_DEF_NAME_3)).limit(1).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(prjAdminGroupList)){
			return null;
		}
		//获取项目管理层小组信息
		TeSysDef prjAdminGroup = prjAdminGroupList.get(0);

		//获取一级小组信息
		List<TeSysDef> firstLevelGroupList = groupDefs.stream().filter(group -> group.getParentDefId().equals(prjAdminGroup.getId())).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(firstLevelGroupList)){
			return null;
		}
		List<ObjectId> firstLevelGroupIdList = firstLevelGroupList.stream().map(TeSysDef::getId).collect(Collectors.toList());

		//小组map
		Map<ObjectId,List<ObjectId>> groupMap = new HashMap<>();
		List<ObjectId> childGroupIds = new ArrayList<>();
		childGroupIds.addAll(firstLevelGroupIdList);
		//遍历获取一级小组下的所有小组，放到map中
		for (ObjectId parentGroupId : firstLevelGroupIdList){
			List<ObjectId> childGroupIdList = new ArrayList<>();
			childGroupIdList.add(parentGroupId);

			List<ObjectId> parentGroupIdList = new ArrayList<>();
			parentGroupIdList.add(parentGroupId);

			this.getChildGroup(parentGroupIdList,groupDefs,childGroupIdList);
			groupMap.put(parentGroupId,childGroupIdList);
			childGroupIds.addAll(childGroupIdList);
		}

		//从“sysDefRoleUser”中取出defType.defTypeCodeName为prjGroup下的
		// 且srcDef.srcDefId为所选项目ID下的且defId在子小组清单下的所有人
		conds.clear();
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName),SysDefTypeConstants.PRJGROUP_CODENAME));
		conds.add(new DC_E(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId),prjId));
		conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__defId,childGroupIds));
		//进入时间小于等于当前时间，并且退出时间未null或退出时间大于等于当前时间的
		List<IDbCondition> andConds = new ArrayList<>();
		andConds.add(new DC_LT(DFN.sysDefRoleUser_enterTime,new Date(),true));
		List<IDbCondition> orConds = new ArrayList<>();
		orConds.add(new DC_E(DFN.sysDefRoleUser_quitTime,null));
		orConds.add(new DC_GT(DFN.sysDefRoleUser_quitTime,DateUtil.getStartDayByDate(new Date()),true));
		andConds.add(new DC_OR(orConds));

		conds.add(new DC_AO(andConds));
		fieldNameList.clear();
		fieldNameList.add(DFN.sysDefRoleUser__defId);
		fieldNameList.add(DFN.sysDefRoleUser__roleUser);
		List<TeSysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.findByFieldAndConds(conds, fieldNameList);

		Map<String,Integer> result = new HashMap<>();
		for (TeSysDef firstLevelGroup : firstLevelGroupList){
			List<ObjectId> groupIds = groupMap.get(firstLevelGroup.getId());
			int count = 0;
			if (CollectionUtils.isNotEmpty(groupIds)){
				for (TeSysDefRoleUser sysDefRoleUser : sysDefRoleUserList){
					if (groupIds.contains(sysDefRoleUser.getDefId())){
						count++;
					}
				}
			}
			result.put(firstLevelGroup.getDefName(),count);
		}
		return CommonResult.success(result);
	}

	private void getChildGroup(List<ObjectId> parentGroupIds,List<TeSysDef> groupDefs,List<ObjectId> childGroupIdList){
		if (CollectionUtils.isEmpty(parentGroupIds)){
			return;
		}
		List<ObjectId> groupIds = new ArrayList<>();
		for (ObjectId parentGroupId : parentGroupIds){
			for (TeSysDef group : groupDefs){
				ObjectId parentDefId = group.getParentDefId();
				if (parentDefId.equals(parentGroupId)){
					groupIds.add(group.getId());
				}
			}
		}
		if (CollectionUtils.isEmpty(groupIds)){
			return;
		}
		childGroupIdList.addAll(groupIds);
		getChildGroup(groupIds,groupDefs,childGroupIdList);
	}

	/**
	 * <AUTHOR> @Description 人员工时统计
	 * @Date 14:45 2022/7/15
	 * @Param
	 * @return
	 **/
	@Override
	public List<WorkHourVo> getHumanWorkingHoursData(ProEmpDateVo proEmpDateVo, List<String> sbuCodes) {
		// 获取子项目信息
		List<TePrjInfo> prjInfoList = listWhPrjSetInfo(proEmpDateVo, sbuCodes);
		if (CollectionUtils.isEmpty(prjInfoList)){
			return null;
		}
		List<ObjectId> prjIdList = new ArrayList<>();
		List<String> prjCodeList = new ArrayList<>();
		Map<ObjectId, TePrjInfo> prjId2InfoMap = new HashMap<>();
		for(TePrjInfo prjInfo : prjInfoList) {
			prjIdList.add(prjInfo.getPrjId());
			prjCodeList.add(prjInfo.getPrjCode());
			prjId2InfoMap.put(prjInfo.getPrjId(), prjInfo);
		}
		// 查询工时信息
		// 过滤条件：项目、起止日期、工号、姓名、NT账号、所属组、岗位角色、人员类型、职级
		ProEmpDateFromRmsVo proEmpDateFromRmsVo = new ProEmpDateFromRmsVo();
		BeanUtils.copyProperties(proEmpDateVo, proEmpDateFromRmsVo);
		proEmpDateFromRmsVo.setPrjIds(prjIdList);
		List<ItfRmsWhDateVo> humanWorkingHourList = rmsEmpDateFeignClient.getHumanWorkingHoursData(proEmpDateFromRmsVo).getData(true);
		if (CollectionUtils.isEmpty(humanWorkingHourList)){
			return null;
		}
		// 跨库查询，在工时数据的信息的基础上，再进行相关信息的过滤
		Set<ObjectId> userIdSet = new HashSet<>();
		Set<String> yearMonthSet = new HashSet<>();
		Set<String> ccIdSet = new HashSet<>();
		String firstYm = processWhInfo(humanWorkingHourList, userIdSet, yearMonthSet, ccIdSet);
		if (CollectionUtils.isEmpty(userIdSet) || CollectionUtils.isEmpty(yearMonthSet)){
			return null;
		}
		// 获取工作日
		Map<String, Integer> ym2WorkDayNumMap = getWorkDaysByMonth(yearMonthSet);
		// 根据查询条件过滤后得到的用户
		List<ObjectId> finalUserIdList = new ArrayList<>();
		Map<ObjectId, TeSysUser> userId2InfoMap = new HashMap<>();
		List<TeSysUser> finalUserList = listUserByWH(new ArrayList<>(userIdSet), proEmpDateVo);
		for (TeSysUser finalUser : finalUserList) {
			finalUserIdList.add(finalUser.getId());
			userId2InfoMap.put(finalUser.getId(), finalUser);
		}
		if (CollectionUtils.isEmpty(finalUserIdList)){
			return null;
		}
		// CC部门（查询条件过滤后）
		Map<String, String> ccId2NameMap = getWhCcInfo(ccIdSet, proEmpDateVo.getCcNameCN());
		// 产品线（查询条件过滤后） 需要考虑BU、项目、ym
		Map<String, TreeMap<String, String>> buCodePrjCodeLoginName2Ym2PlNameMap =
				getEmpPlName(firstYm, prjCodeList, finalUserIdList, proEmpDateVo.getPlName());
		// 度量角色
		Map<String, TreeMap<String, String>> buCodeLoginName2Ym2EvalRoleNameMap =
				getEvaluateRoleInfo(firstYm, finalUserIdList, proEmpDateVo.getEvaluateRoleName());
		// 过滤，且第一次分组
		List<WorkHourVo> resultList = new ArrayList<>();
		for (ItfRmsWhDateVo empDateVo : humanWorkingHourList) {
			if (empDateVo.getEmp() == null || empDateVo.getEmp().getUserId() == null
					|| StringUtils.isEmpty(empDateVo.getEmp().getLoginName())){
				continue;
			}
			ObjectId userId = empDateVo.getEmp().getUserId();
			String loginName = empDateVo.getEmp().getLoginName();
			// 过滤条件：在职状态、上级经理名称
			// 因为在查询用户时，已经按照过滤条件过滤，且人员信息不应该不存在，
			// 所以如果从sysUser中取不到人员信息，则过滤掉
			TeSysUser sysUser = userId2InfoMap.get(userId);
			if (sysUser == null){
				continue;
			}
			// 过滤条件：所属部门
			// 如果传入了所属部门的过滤条件，而没有取到对应的ccName，则过滤掉
			String ccNameCN = ccId2NameMap.get(empDateVo.getCostCenterId());
			if (StringUtils.isNotEmpty(proEmpDateVo.getCcNameCN()) && StringUtils.isEmpty(ccNameCN)){
				continue;
			}
			ObjectId prjId = empDateVo.getPrjId();
			TePrjInfo prjInfo = prjId2InfoMap.get(prjId);
			String buCode = prjInfo.getSbuId();
			String prjCode = prjInfo.getPrjCode();
			String ym = empDateVo.getYm();
			// 过滤条件：产品线 buCodePrjCodeYmLoginName2PlNameMap
			// 如果传入了产品线的过滤条件，而没有取到对应的plName，则过滤掉
			// 若没有取到对应月份的标签，则取最新一个月的
			String plKey = String.format(WH_PL_KEY, buCode, prjCode, loginName);
			String plName = findNearestMonthData(buCodePrjCodeLoginName2Ym2PlNameMap.get(plKey), ym);
			if (StringUtils.isNotEmpty(proEmpDateVo.getPlName()) && StringUtils.isEmpty(plName)){
				continue;
			}
			// 过滤条件：度量角色 buCodeYmLoginName2EvalRoleNameMap
			// 如果传入了产品线的过滤条件，而没有取到对应的plName，则过滤掉
			String evalRoleKey = String.format(WH_E_R_KEY, buCode, loginName);
			String evaluateRoleName = findNearestMonthData(buCodeLoginName2Ym2EvalRoleNameMap.get(evalRoleKey), ym);
			if (StringUtils.isNotEmpty(proEmpDateVo.getEvaluateRoleName()) && StringUtils.isEmpty(evaluateRoleName)){
				continue;
			}
			TeUser emp = new TeUser(empDateVo.getEmp().getUserId(), empDateVo.getEmp().getLoginName(),
					empDateVo.getEmp().getUserName(), empDateVo.getEmp().getJobCode());
			WorkHourVo result = new WorkHourVo();
			// 此时的数据是按项目和月份的，需要汇总下
			// 之所以按照项目和月份做分组，是因为产品线和度量角色，是按照月份配置的
			result.setPrjId(prjId);
			result.setYearMonth(ym);
			// 工号、姓名、NT账号
			result.setEmp(new TeUser(emp.getUserId(), emp.getLoginName(), emp.getUserName(), emp.getJobCode()));
			// 所属组
			result.setGroupName(empDateVo.getGroupName());
			// 上级经理名称
			result.setManager(sysUser.getManager());
			// 所属部门
			result.setCcNameCN(ccNameCN);
			// 在职状态
			result.setIsOnTheJob(BooleanUtils.isTrue(sysUser.getIsValid()) ? ON_THE_JOB_TRUE : ON_THE_JOB_FALSE);
			// 岗位角色
			result.setRoleName(empDateVo.getRoleName());
			// 人员类型
			result.setEmployeeType(employeeTypeMap.get(empDateVo.getEmployeeType()));
			// 产品线
			result.setPlName(plName);
			// 度量角色
			result.setEvaluateRoleName(evaluateRoleName);
			// 职级
			result.setBandType(empDateVo.getBandType());
			// 项目时长（人天）
			result.setManDayNum(empDateVo.getManDay());
			// 人月
			result.setManMonthNum(DoubleUtil.getNotNull(empDateVo.getManDay()) / DoubleUtil.getNotNull(ym2WorkDayNumMap.get(ym)));
			result.setEmpBuId(empDateVo.getEmpBuId());
			resultList.add(result);
		}

		if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EVAL_ROLE.equals(proEmpDateVo.getSearchType())){
			return packagePlEvalRoleWh(resultList);
		} else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EMPLOYEE_TYPE.equals(proEmpDateVo.getSearchType())){
			return packagePlEmployeeTypeWh(resultList);
		}else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_BAND_TYPE.equals(proEmpDateVo.getSearchType())){
			return packagePlBandTypeWh(resultList);
		}
		return packageHumanWh(resultList);
	}

	public static void main(String[] args) {
		// 创建 TreeMap 并按顺序存储月份数据
		TreeMap<String, String> monthData = new TreeMap<>();
		monthData.put("2024-07", "Data for 2024-07");
		monthData.put("2024-05", "Data for 2024-05");
		monthData.put("2024-09", "Data for 2024-09");
		monthData.put("2024-08", "Data for 2024-08");
		monthData.put("2024-06", "Data for 2024-06");

		String inputMonth = "2024-01";
		String result = findNearestMonthData(monthData, inputMonth);
		System.out.println("Closest month data: " + result);
	}

	public static String findNearestMonthData(TreeMap<String, String> monthTreeMap, String inputMonth) {
		if (MapUtils.isEmpty(monthTreeMap)){
			return "";
		}
		// 先检查是否存在完全匹配的月份
		if (monthTreeMap.containsKey(inputMonth)) {
			return monthTreeMap.get(inputMonth);
		}
		// 找到大于或等于输入月份的最小键
		String closestMonth = monthTreeMap.ceilingKey(inputMonth);
		// 如果没有大于输入月份的键，取最大的月份
		if (closestMonth == null) {
			closestMonth = monthTreeMap.lastKey();
		}
		return monthTreeMap.get(closestMonth) == null ? "" : monthTreeMap.get(closestMonth);
	}

	public static ObjectId findNearestMonthDataId(TreeMap<String, ObjectId> monthTreeMap, String inputMonth) {
		if (MapUtils.isEmpty(monthTreeMap)){
			return null;
		}
		// 先检查是否存在完全匹配的月份
		if (monthTreeMap.containsKey(inputMonth)) {
			return monthTreeMap.get(inputMonth);
		}
		// 找到大于或等于输入月份的最小键
		String closestMonth = monthTreeMap.ceilingKey(inputMonth);
		// 如果没有大于输入月份的键，取最大的月份
		if (closestMonth == null) {
			closestMonth = monthTreeMap.lastKey();
		}
		return monthTreeMap.get(closestMonth) == null ? null : monthTreeMap.get(closestMonth);
	}

	private List<WorkHourVo> packagePlEvalRoleWh(List<WorkHourVo> resultList) {
		WorkHourVo totalResult = new WorkHourVo();
		totalResult.setPlName("合计");
		List<WorkHourVo> finalResutList = new ArrayList<>(resultList.stream().collect(Collectors.groupingBy(
				result -> Arrays.asList(result.getPlName(), result.getEvaluateRoleName()),
				Collectors.collectingAndThen(
						Collectors.toList(),
						finalResultList -> {
							int manDay = 0;
							double manMonthNum = 0;
							// 使用一次for循环累加多个得分
							Set<String> loginNameSet = new HashSet<>();
							for (WorkHourVo finalResult : finalResultList) {
								manDay += finalResult.getManDayNum();
								manMonthNum += DoubleUtil.getNotNull(finalResult.getManMonthNum());
								loginNameSet.add(finalResult.getEmp().getLoginName());
							}
							// 合计行数据
							totalResult.addTotal(manDay, loginNameSet.size(), BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							// 再次分组后的合计（按照产品线和度量角色分组）
							WorkHourVo empDateVo = finalResultList.get(0);
							WorkHourVo newEmpDateVo = new WorkHourVo();
							newEmpDateVo.setEmp(empDateVo.getEmp());
							newEmpDateVo.setPlName(empDateVo.getPlName());
							newEmpDateVo.setEvaluateRoleName(empDateVo.getEvaluateRoleName());
							newEmpDateVo.setManNum(loginNameSet.size());
							newEmpDateVo.setManDayNum(manDay);
							newEmpDateVo.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							return newEmpDateVo;
						}
				)
		)).values()
		.stream()
		.sorted(Comparator.comparing(WorkHourVo::getPlName))
		.collect(Collectors.toList()));
		totalResult.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(totalResult.getManMonthNum(), 2));
		finalResutList.add(totalResult);
		return finalResutList;
	}
	private List<WorkHourVo> packagePlEmployeeTypeWh(List<WorkHourVo> resultList) {
		WorkHourVo totalResult = new WorkHourVo();
		totalResult.setPlName("合计");
		List<WorkHourVo> finalResutList =  new ArrayList<>(resultList.stream().collect(Collectors.groupingBy(
				result -> Arrays.asList(result.getPlName(), result.getEmployeeType()),
				Collectors.collectingAndThen(
						Collectors.toList(),
						finalResultList -> {
							int manDay = 0;
							double manMonthNum = 0;
							// 使用一次for循环累加多个得分
							Set<String> loginNameSet = new HashSet<>();
							for (WorkHourVo finalResult : finalResultList) {
								manDay += finalResult.getManDayNum();
								manMonthNum += DoubleUtil.getNotNull(finalResult.getManMonthNum());
								loginNameSet.add(finalResult.getEmp().getLoginName());
							}
							// 合计行数据
							totalResult.addTotal(manDay, loginNameSet.size(), BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							// 再次分组后的合计（按照产品线和度量角色分组）
							WorkHourVo empDateVo = finalResultList.get(0);
							WorkHourVo newEmpDateVo = new WorkHourVo();
							newEmpDateVo.setEmp(empDateVo.getEmp());
							newEmpDateVo.setPlName(empDateVo.getPlName());
							newEmpDateVo.setEmployeeType(empDateVo.getEmployeeType());
							newEmpDateVo.setManNum(loginNameSet.size());
							newEmpDateVo.setManDayNum(manDay);
							newEmpDateVo.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							return newEmpDateVo;
						}
				)
		)).values()
				.stream()
				.sorted(Comparator.comparing(WorkHourVo::getPlName))
				.collect(Collectors.toList()));
		totalResult.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(totalResult.getManMonthNum(), 2));
		finalResutList.add(totalResult);
		return finalResutList;
	}
	private List<WorkHourVo> packagePlBandTypeWh(List<WorkHourVo> resultList) {
		WorkHourVo totalResult = new WorkHourVo();
		totalResult.setPlName("合计");
		List<WorkHourVo> finalResutList = new ArrayList<>(resultList.stream().collect(Collectors.groupingBy(
				result -> Arrays.asList(result.getPlName(), result.getBandType()),
				Collectors.collectingAndThen(
						Collectors.toList(),
						finalResultList -> {
							int manDay = 0;
							double manMonthNum = 0;
							// 使用一次for循环累加多个得分
							Set<String> loginNameSet = new HashSet<>();
							for (WorkHourVo finalResult : finalResultList) {
								manDay += finalResult.getManDayNum();
								manMonthNum += DoubleUtil.getNotNull(finalResult.getManMonthNum());
								loginNameSet.add(finalResult.getEmp().getLoginName());
							}
							// 合计行数据
							totalResult.addTotal(manDay, loginNameSet.size(), BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							// 再次分组后的合计（按照产品线和度量角色分组）
							WorkHourVo empDateVo = finalResultList.get(0);
							WorkHourVo newEmpDateVo = new WorkHourVo();
							newEmpDateVo.setEmp(empDateVo.getEmp());
							newEmpDateVo.setPlName(empDateVo.getPlName());
							newEmpDateVo.setBandType(empDateVo.getBandType());
							newEmpDateVo.setManNum(loginNameSet.size());
							newEmpDateVo.setManDayNum(manDay);
							newEmpDateVo.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							return newEmpDateVo;
						}
				)
		)).values()
				.stream()
				.sorted(Comparator.comparing(WorkHourVo::getPlName))
				.collect(Collectors.toList()));
		totalResult.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(totalResult.getManMonthNum(), 2));
		finalResutList.add(totalResult);
		return finalResutList;
	}

	private List<WorkHourVo> packageHumanWh(List<WorkHourVo> resultList) {
		WorkHourVo totalResult = new WorkHourVo();
		totalResult.setEmp(new TeUser(null, null, null, "合计"));
		List<WorkHourVo> finalResutList = new ArrayList<>(resultList.stream().collect(Collectors.groupingBy(
				result -> Arrays.asList(result.getEmp().getLoginName(), result.getCcNameCN(), result.getRoleName(),
						result.getEmployeeType(), result.getPlName(), result.getEvaluateRoleName(), result.getBandType()),
				Collectors.collectingAndThen(
						Collectors.toList(),
						finalResultList -> {
							int manDay = 0;
							double manMonthNum = 0;
							// 使用一次for循环累加多个得分
							for (WorkHourVo finalResult : finalResultList) {
								manDay += finalResult.getManDayNum();
								manMonthNum += DoubleUtil.getNotNull(finalResult.getManMonthNum());
							}
							// 合计行数据
							totalResult.addManDayAndMonthNum(manDay, BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							// 再次分组后的合计（按照产品线和度量角色分组）
							WorkHourVo empDateVo = finalResultList.get(0);
							WorkHourVo newEmpDateVo = new WorkHourVo();
							BeanUtils.copyProperties(empDateVo, newEmpDateVo);
							newEmpDateVo.setManDayNum(manDay);
							newEmpDateVo.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(manMonthNum, 2));
							return newEmpDateVo;
						}
				)
		)).values());
		totalResult.setManMonthNum(BigDecimalUtils.getDoubleHalfNum(totalResult.getManMonthNum(), 2));
		finalResutList.add(totalResult);
		return finalResutList;
	}

	private static String processWhInfo(List<ItfRmsWhDateVo> humanWorkingHourList, Set<ObjectId> userIdSet,
										Set<String> yearMonthSet, Set<String> ccIdSet) {
		String firstYm = null;
		for (ItfRmsWhDateVo humanWorkingHour : humanWorkingHourList) {
			if (humanWorkingHour.getEmp() == null || humanWorkingHour.getEmp().getUserId() == null
				|| StringUtils.isEmpty(humanWorkingHour.getYm())){
				continue;
			}
			userIdSet.add(humanWorkingHour.getEmp().getUserId());
			yearMonthSet.add(humanWorkingHour.getYm());
			if (StringUtils.isEmpty(firstYm) || firstYm.compareTo(humanWorkingHour.getYm()) > 0){
				firstYm = humanWorkingHour.getYm();
			}
			if (StringUtils.isNotEmpty(humanWorkingHour.getCostCenterId())) {
				ccIdSet.add(humanWorkingHour.getCostCenterId());
			}
		}
		return firstYm;
	}

	private Map<String, TreeMap<String, String>> getEmpPlName(String startYm, List<String> prjCodeList, List<ObjectId> userIdList, String plName) {
		if (CollectionUtils.isEmpty(prjCodeList)){
			return Collections.emptyMap();
		}
		ItfAbpActEmpPlVo vo = new ItfAbpActEmpPlVo();
		vo.setPrjCodeList(prjCodeList);
		vo.setUserIdList(userIdList);
		vo.setStartYm(startYm);
		vo.setEmpPlName(plName);
		List<ItfAbpActEmpPl> itfList = abpActEmpPlFeignClient.listAbpActEmpPlByVo(vo).getData(true);
		if (CollectionUtils.isEmpty(itfList)){
			return Collections.emptyMap();
		}
		Map<String, TreeMap<String, String>> buCodePrjCodeYmLoginName2PlNameMap = new HashMap<>();
		for (ItfAbpActEmpPl itfPl : itfList) {
			if (StringUtils.isEmpty(itfPl.getEmpBuCode()) || StringUtils.isEmpty(itfPl.getPrjCode())
					|| StringUtils.isEmpty(itfPl.getYm()) || CollectionUtils.isEmpty(itfPl.getEmpPls())){
				continue;
			}
			List<ItfPlsVo> empPls = itfPl.getEmpPls();
			boolean present = empPls.stream().filter(pl -> pl.getPct() != null && pl.getPl() != null).max((v1, v2) -> v1.getPct().compareTo(v2.getPct())).isPresent();
			TeIdNameCn empPl = null;
			if (present){
				empPl = empPls.stream().filter(pl -> pl.getPct() != null && pl.getPl() != null)
						.max((v1, v2) -> v1.getPct().compareTo(v2.getPct())).get().getPl();
			}else {
				empPl = itfPl.getEmpPls().get(0).getPl();
			}
			if (empPl == null || StringUtils.isEmpty(empPl.getName())){
				continue;
			}
			String key = String.format(WH_PL_KEY, itfPl.getEmpBuCode(), itfPl.getPrjCode(), itfPl.getEmp().getLoginName());
			TreeMap<String, String> ym2PlNameMap = buCodePrjCodeYmLoginName2PlNameMap.getOrDefault(key, new TreeMap<>());
			ym2PlNameMap.put(itfPl.getYm(), empPl.getName());
			buCodePrjCodeYmLoginName2PlNameMap.put(key, ym2PlNameMap);
		}
		return buCodePrjCodeYmLoginName2PlNameMap;
	}

	private Map<String, TreeMap<String, String>> getEvaluateRoleInfo(String startYm, List<ObjectId> userIdList, String roleName) {
		List<TeSysDefRoleUser> sysDefRoleUserList = listEvalRoleInfo(startYm, userIdList, roleName);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return Collections.emptyMap();
		}
		Map<String, TreeMap<String, String>> buCodeLoginName2Ym2EvalRoleNameMap = new HashMap<>();
		for (TeSysDefRoleUser defRoleUser : sysDefRoleUserList) {
			if (defRoleUser.getSrcDef() == null || StringUtils.isEmpty(defRoleUser.getSrcDef().getSrcDefCodeName())
					|| StringUtils.isEmpty(defRoleUser.getYm())
					|| StringUtils.isEmpty(defRoleUser.getRoleUser().getLoginName())){
				continue;
			}
			List<TeSysDefRoleUser2Role> roleList = defRoleUser.getRole();
			if (CollectionUtils.isEmpty(roleList) || roleList.size() != 1){
				continue;
			}
			String key = String.format(WH_E_R_KEY, defRoleUser.getSrcDef().getSrcDefCodeName(), defRoleUser.getRoleUser().getLoginName());
			TreeMap<String, String> ym2EvalRoleNameMap = buCodeLoginName2Ym2EvalRoleNameMap.getOrDefault(key, new TreeMap<>());
			ym2EvalRoleNameMap.put(defRoleUser.getYm(), roleList.get(0).getRoleName());
			buCodeLoginName2Ym2EvalRoleNameMap.put(key, ym2EvalRoleNameMap);
		}
		return buCodeLoginName2Ym2EvalRoleNameMap;
	}

	@Override
	public Map<String, TreeMap<String, ObjectId>> getEvaluateRoleIdInfo(String startYm, List<ObjectId> userIdList, String roleName) {
		List<TeSysDefRoleUser> sysDefRoleUserList = listEvalRoleInfo(startYm, userIdList, roleName);
		if (CollectionUtils.isEmpty(sysDefRoleUserList)){
			return Collections.emptyMap();
		}
		Map<String, TreeMap<String, ObjectId>> buCodeUserId2Ym2EvalRoleIdMap = new HashMap<>();
		for (TeSysDefRoleUser defRoleUser : sysDefRoleUserList) {
			if (defRoleUser.getSrcDef() == null || StringUtils.isEmpty(defRoleUser.getSrcDef().getSrcDefCodeName())
					|| StringUtils.isEmpty(defRoleUser.getYm())
					|| defRoleUser.getRoleUser().getUserId() == null){
				continue;
			}
			List<TeSysDefRoleUser2Role> roleList = defRoleUser.getRole();
			if (CollectionUtils.isEmpty(roleList) || roleList.size() != 1){
				continue;
			}
			String key = String.format(WH_E_R_KEY, defRoleUser.getSrcDef().getSrcDefCodeName(),
					defRoleUser.getRoleUser().getUserId().toHexString());
			TreeMap<String, ObjectId> ym2EvalRoleIdMap = buCodeUserId2Ym2EvalRoleIdMap.computeIfAbsent(key, k ->new TreeMap<>());
			ym2EvalRoleIdMap.put(defRoleUser.getYm(), roleList.get(0).getRoleId());
		}
		return buCodeUserId2Ym2EvalRoleIdMap;
	}

	private List<TeSysDefRoleUser> listEvalRoleInfo(String startYm, List<ObjectId> userIdList, String roleName){
		if (CollectionUtils.isEmpty(userIdList)){
			return Collections.emptyList();
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid, true));
		conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName), PrjConstant.EIP_ORG_STRUCTURE.getCodeName()));
		conds.add(new DC_I<>(DFN.sysDefRoleUser__roleUser.dot(DFN.sysDefRoleUser__userId), userIdList));
		if (StringUtils.isNotEmpty(startYm)){
			conds.add(new DC_GT(DFN.sysDefRoleUser_ym, startYm, true));
		}
		if (StringUtils.isNotEmpty(roleName)){
			conds.add(new DC_R(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleName), roleName));
		}
		List<DbFieldName> fieldNames = Arrays.asList(
				DFN.sysDefRoleUser_srcDef,
				DFN.sysDefRoleUser_ym,
				DFN.sysDefRoleUser__roleUser,
				DFN.sysDefRoleUser__role
		);
		return sysDefRoleUserDao.findByFieldAndConds(conds, fieldNames);
	}

	private Map<String, String> getWhCcInfo(Set<String> ccIdSet, String ccNameCN) {
		if (CollectionUtils.isEmpty(ccIdSet)){
			return Collections.emptyMap();
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_I<>(DFN.sysAicc_ccId, new ArrayList<>(ccIdSet)));
		if (StringUtils.isNotEmpty(ccNameCN)){
			conds.add(new DC_R(DFN.sysAicc_ccNameCn, ccNameCN));
		}
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.sysAicc_ccId);
		fieldNames.add(DFN.sysAicc_ccNameCn);
		List<TeSysAicc> ccList = sysAiccDao.findByFieldAndConds(conds, fieldNames);
		if (CollectionUtils.isEmpty(ccList)){
			return Collections.emptyMap();
		}
		return ccList.stream().collect(Collectors.toMap(TeSysAicc::getCcId, TeSysAicc::getCcNameCn));
	}

	private List<TePrjInfo> listWhPrjSetInfo(ProEmpDateVo proEmpDateVo, List<String> sbuCodes) {
		if(proEmpDateVo.getPrjId() == null) {
			return Collections.emptyList();
		}
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_E(DFN.common_isValid,false,true));
		conds.add(new DC_E(DFN.prjInfo__prjId, proEmpDateVo.getPrjId()));
		if (CollectionUtils.isNotEmpty(sbuCodes)){
			conds.add(new DC_I<>(DFN.prjInfo__sbuId, sbuCodes));
		}
		conds.add(new DC_E(DFN.prjInfo__subPrjs.dot(DFN.common_cid),null,true));
		//筛选字段，提高速度
		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.prjInfo__prjId);
		fieldNameList.add(DFN.prjInfo__subPrjs);
		fieldNameList.add(DFN.prjInfo__prjName);
		//查询项目信息
		List<TePrjInfo> prjInfoList = prjInfoDao.findByFieldAndConds(conds, fieldNameList);
		if (CollectionUtils.isEmpty(prjInfoList)){
			return Collections.emptyList();
		}
		List<ObjectId> subPrjIdList = new ArrayList<>();
		for(TePrjInfo prjInfo : prjInfoList) {
			if(CollectionUtils.isEmpty(prjInfo.getSubPrjs())) {
				continue;
			}
			for(TePrjInfoSubPrj subPrj : prjInfo.getSubPrjs()) {
				subPrjIdList.add(subPrj.getCid());
			}
		}
		if (CollectionUtils.isEmpty(subPrjIdList)){
			return Collections.emptyList();
		}
		//子项目信息
		conds.clear();
		conds.add(new DC_E(DFN.common_isValid,false,true));
		conds.add(new DC_I<>(DFN.prjInfo__prjId,subPrjIdList));
		fieldNameList.clear();
		fieldNameList.add(DFN.prjInfo__prjId);
		fieldNameList.add(DFN.prjInfo__prjCode);
		fieldNameList.add(DFN.prjInfo__prjName);
		fieldNameList.add(DFN.prjInfo__sbuId);
		return prjInfoDao.findByFieldAndConds(conds, fieldNameList);
	}

	private List<TeSysUser> listUserByWH(List<ObjectId> userIdList, ProEmpDateVo proEmpDateVo){
		List<IDbCondition> conds = new ArrayList<>();
		conds.add(new DC_I<>(DFN.common__id, userIdList));
		if (StringUtil.isNotNull(proEmpDateVo.getManagerName())){
			conds.add(new DC_L(DFN.sysUser_manager.dot(DFN.common_userName), proEmpDateVo.getManagerName()));
		}
		if (PrjConstant.IS_ON_THE_JOB_YES.equals(proEmpDateVo.getIsOnTheJob())){
			conds.add(new DC_E(DFN.common_isValid,true));
		}else if (PrjConstant.IS_ON_THE_JOB_NO.equals(proEmpDateVo.getIsOnTheJob())){
			conds.add(new DC_E(DFN.common_isValid,true,true));
		}
		List<DbFieldName> fieldNameList = new ArrayList<>();
		fieldNameList.add(DFN.sysUser__loginName);
		fieldNameList.add(DFN.sysUser__userName);
		fieldNameList.add(DFN.sysUser_jobCode);
		fieldNameList.add(DFN.sysUser_manager);
		fieldNameList.add(DFN.sysUser_ccNameCn);
		fieldNameList.add(DFN.sysUser_dmpUserType);
		fieldNameList.add(DFN.sysUser_orgName);
		fieldNameList.add(DFN.common_isValid);
		fieldNameList.add(DFN.sysUser_lastUpdateTime);
		fieldNameList.add(DFN.common_addTime);
		return sysUserDao.findByFieldAndConds(conds, fieldNameList);
	}

	@Override
	public PageBean exportHumanWorkingHoursData(ProEmpDateVo proEmpDateVo, List<String> sbuCodes) {
		List<WorkHourVo> humanWorkingHoursDatas = this.getHumanWorkingHoursData(proEmpDateVo, sbuCodes);
		List<LinkedHashMap<Object, Object>> list = new ArrayList<LinkedHashMap<Object, Object>>();
		LinkedHashMap<Object, Object> titleMap0 = new LinkedHashMap<Object, Object>();
		int index = 0;
		titleMap0.put(index++, "工号");
		titleMap0.put(index++, "姓名");
		titleMap0.put(index++, "NT账号");
		titleMap0.put(index++, "所属组");
		titleMap0.put(index++, "上级经理名称");
		titleMap0.put(index++, "归属BU");
		titleMap0.put(index++, "所属部门");
		titleMap0.put(index++, "在职状态");
		titleMap0.put(index++, "岗位角色");
		titleMap0.put(index++, "人员类型");
		titleMap0.put(index++, "产品线");
		titleMap0.put(index++, "度量角色");
		titleMap0.put(index++, "职级");
		titleMap0.put(index++, "项目时长");
		titleMap0.put(index++, "人月");
		list.add(titleMap0);
		for(WorkHourVo workingHourDetail : humanWorkingHoursDatas){
			LinkedHashMap<Object, Object> linkedMap = new LinkedHashMap<Object, Object>();
			linkedMap.put("jobCode", workingHourDetail.getEmp().getJobCode());
			linkedMap.put("userName", workingHourDetail.getEmp().getUserName());
			linkedMap.put("loginName", workingHourDetail.getEmp().getLoginName());
			linkedMap.put("groupName", workingHourDetail.getGroupName());
			linkedMap.put("manager", workingHourDetail.getManager() == null ? "" : workingHourDetail.getManager().getUserName());
			linkedMap.put("empBuId", workingHourDetail.getEmpBuId());
			linkedMap.put("ccNameCn", workingHourDetail.getCcNameCN());
			linkedMap.put("isOnTheJob", workingHourDetail.getIsOnTheJob());
			linkedMap.put("roleName", workingHourDetail.getRoleName());
			linkedMap.put("employeeType", workingHourDetail.getEmployeeType());
			linkedMap.put("plName", workingHourDetail.getPlName());
			linkedMap.put("evaluateRoleName", workingHourDetail.getEvaluateRoleName());
			linkedMap.put("bandType", workingHourDetail.getBandType());
			linkedMap.put("manDayNum", workingHourDetail.getManDayNum());
			linkedMap.put("manMonthNum", workingHourDetail.getManMonthNum());
			list.add(linkedMap);
		}
		PageBean bean = new PageBean();
		bean.setObjectList(list);
		return bean;
	}

	@Override
	public PageBean exportPlWorkingHours(ProEmpDateVo proEmpDateVo) {
		List<WorkHourVo> humanWorkingHoursDatas = this.getHumanWorkingHoursData(proEmpDateVo, null);
		List<LinkedHashMap<Object, Object>> list = new ArrayList<>();
		LinkedHashMap<Object, Object> titleMap0 = getHeadInfo(proEmpDateVo);
		list.add(titleMap0);
		for(WorkHourVo workingHourDetail : humanWorkingHoursDatas){
			LinkedHashMap<Object, Object> linkedMap = new LinkedHashMap<>();
			linkedMap.put("plName", workingHourDetail.getPlName());
			if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EVAL_ROLE.equals(proEmpDateVo.getSearchType())){
				linkedMap.put("evaluateRoleName", workingHourDetail.getEvaluateRoleName());
			} else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EMPLOYEE_TYPE.equals(proEmpDateVo.getSearchType())){
				linkedMap.put("employeeType", workingHourDetail.getEmployeeType());
			}else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_BAND_TYPE.equals(proEmpDateVo.getSearchType())){
				linkedMap.put("bandType", workingHourDetail.getBandType());
			}
			linkedMap.put("manNum", workingHourDetail.getManNum());
			linkedMap.put("manDayNum", workingHourDetail.getManDayNum());
			linkedMap.put("manMonthNum", workingHourDetail.getManMonthNum());
			list.add(linkedMap);
		}
		PageBean bean = new PageBean();
		bean.setObjectList(list);
		return bean;
	}

	private static LinkedHashMap<Object, Object> getHeadInfo(ProEmpDateVo proEmpDateVo) {
		LinkedHashMap<Object, Object> titleMap0 = new LinkedHashMap<>();
		int index = 0;
		titleMap0.put(index++, "产品线");
		if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EVAL_ROLE.equals(proEmpDateVo.getSearchType())){
			titleMap0.put(index++, "度量角色");
		} else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_EMPLOYEE_TYPE.equals(proEmpDateVo.getSearchType())){
			titleMap0.put(index++, "人员类型");
		}else if (PrjConstant.WORK_HOUR_SEARCH_TYPE_PL_BAND_TYPE.equals(proEmpDateVo.getSearchType())){
			titleMap0.put(index++, "职级");
		}
		titleMap0.put(index++, "人数");
		titleMap0.put(index++, "人日");
		titleMap0.put(index++, "人月");
		return titleMap0;
	}

	//查询项目人员最后加入的小组
	private List<PrjGroupUser> getLatestGroup(List<ObjectId> prjIds,List<ObjectId> userIds){
		List<PrjGroupUser> result = new ArrayList<>();
		List<org.bson.Document> sql = new ArrayList<>();

		org.bson.Document matchObj = new org.bson.Document();
		matchObj.append(DFN.sysDefRoleUser_defType.dot(DFN.sysDefRoleUser_defTypeCodeName).n(),SysDefTypeCodeName.PRJ_GROUP.getValue());
		matchObj.append(DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId).n(),new org.bson.Document("$in",prjIds));
		matchObj.append(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userId).n(),new org.bson.Document("$in",userIds));
		matchObj.append(DFN.sysDefRoleUser__roleUser.dot(DFN.common_loginName).n(),new org.bson.Document("$ne",null));
		matchObj.append(DFN.sysDefRoleUser__defId.n(),new org.bson.Document("$ne",null));
		sql.add(new org.bson.Document("$match",matchObj));

		org.bson.Document sort = new org.bson.Document();
		sort.append(DFN.common_addTime.n(),-1);
		sql.add(new org.bson.Document("$sort",sort));

		org.bson.Document group = new org.bson.Document();
		group.append(DFN.common__id.n(),new org.bson.Document(DFN.prjInfo__prjId.n(),"$"+DFN.sysDefRoleUser_srcDef.dot(DFN.sysDefRoleUser_srcDefId).n())
				.append(DFN.common_loginName.n(),"$"+DFN.sysDefRoleUser__roleUser.dot(DFN.common_loginName).n()));
		group.append(DFN.sysDefRoleUser__defId.n(),new org.bson.Document("$first","$"+DFN.sysDefRoleUser__defId.n()));
		group.append(DFN.sysDefRoleUser__role.n(),new org.bson.Document("$first","$"+DFN.sysDefRoleUser__role.n()));
		sql.add(new org.bson.Document("$group",group));

		com.mongodb.client.MongoCursor<org.bson.Document> cursor = mongoTemplate.getCollection(DBT.SYS_DEF_ROLE_USER.n()).aggregate(sql)
				.allowDiskUse(true).cursor();
		while (cursor.hasNext()){
			PrjGroupUser groupUser = new PrjGroupUser();
			org.bson.Document next = cursor.next();
			org.bson.Document idObject= (org.bson.Document)next.get(DFN.common__id.n());
			ObjectId prjId = StringUtil.toObjectId(idObject.get(DFN.prjInfo__prjId.n()));
			groupUser.setPrjId(prjId);
			String loginName = StringUtil.getNotNullStr(idObject.get(DFN.common_loginName.n()));
			groupUser.setLoginName(loginName);
			ObjectId groupId = StringUtil.toObjectId(next.get(DFN.sysDefRoleUser__defId.n()));
			groupUser.setGroupId(groupId);
			List role = StringUtil.to(next.get(DFN.sysDefRoleUser__role.n()),List.class);
			if (role != null){
				org.bson.Document roleObj = (org.bson.Document)role.get(0);
				groupUser.setRoleName(StringUtil.getNotNullStr(roleObj.get("roleName")));
			}
			result.add(groupUser);
		}

		return result;
	}

	@Override
	public Map<String,Integer> getWorkDaysByMonth(Set<String> yearMonthSet){
		List<org.bson.Document> sql = new ArrayList<>();
		org.bson.Document match = new org.bson.Document();
		match.append(DFN.SysCal.mh.n(),8);
		match.append(DFN.SysCal.isStd.n(),true);
		List<org.bson.Document> orList = new ArrayList<>();
		for (String ym: yearMonthSet){
			org.bson.Document orObj = new org.bson.Document();
			orObj.append(DFN.SysCal.date.n(),new org.bson.Document("$regex",ym));
			orList.add(orObj);
		}
		match.append("$or",orList);
		sql.add(new org.bson.Document("$match",match));

		org.bson.Document project = new org.bson.Document();
		List dbList = new ArrayList();
		dbList.add("$date");
		dbList.add(0);
		dbList.add(7);
		project.append("date",new org.bson.Document("$substrCP",dbList));
		sql.add(new org.bson.Document("$project",project));

		org.bson.Document group = new org.bson.Document();
		group.append("_id","$date");
		group.append("total",new org.bson.Document("$sum",1));
		sql.add(new org.bson.Document("$group",group));
		com.mongodb.client.MongoCursor<org.bson.Document> cursor = mongoTemplate.getCollection(DBT.SYS_CAL.n()).aggregate(sql)
				.allowDiskUse(true).cursor();
		Map<String,Integer> ymMap = new HashMap<>();
		while (cursor.hasNext()){
			org.bson.Document next = cursor.next();
			String ym = StringUtil.getNotNullStr(next.get("_id"));
			Integer total = StringUtil.toInteger(next.get("total"));
			ymMap.put(ym,total);
		}
		return ymMap;
	}

	@Override
	public void checkBeforeMonthEndOutGoing() throws Exception {

        String yearMonthDay = DateUtil.getMonthStart();//本月开始的第一天
		String yearMonth = yearMonthDay.substring(0,7);
        int workDays = sysCalDao.getWorkDays(yearMonth).size();
        //检测数量是否一致    一，
        this.checkRmsEmpDateSumCount(yearMonth);
        this.checkRmsEmpDateValidSumCount(yearMonth);
        this.checkRmsEmpDateOutGoingSumCount(yearMonth);
//        检查是否有为空的记录
		this.checkIsNull("projectId",yearMonthDay);
		this.checkIsNull("userId",yearMonthDay);
		this.checkIsNull("teamId",yearMonthDay);
		this.checkIsNull("prdTag",yearMonth);
		this.checkIsNull("budgetType",yearMonthDay);
//		//异常逻辑 校验
		this.checkIfHaveDataBeforeProjectStartDate( yearMonth);
		this.checkIfHaveDataGreatDepartDate(yearMonth,yearMonth.substring(0,4));
		this.checkIfHaveRedundanceData(yearMonth);
		this.checkIfHaveWorkHourAllocationDataInFreezeProject(yearMonth);
		this.checkIfHaveInAdvanceWorkHourAllocationDataInFreezeBudget(yearMonth);
		this.checkIfHaveBuIdIsNullData();
		this.checkIfHaveGreatworkDaysData(workDays);
    }

    //一，对比出账月antdb库与新版rms库，不一致报警
    //工时表的总数量是否对得上 \ 工时表中有效记录数是否能对得上 \ 工时表中出账数量是否能对得上\
    private  void checkRmsEmpDateSumCount(String yearMonth) throws Exception {
        com.linkus.common.web.CommonResult<Integer> integerCommonResult = null;
        try {
            integerCommonResult = rmsEmpDateFeignClient.rmsEmpDateSumCount(yearMonth);
        }catch (Exception e){
            log.error("新版rms库调用失败，原因：{}", "linkus-rms服务未生效:",e);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) from pro_emp_date where  emp_date >=  " );
        sql.append("('"+yearMonth+"' )");
        List<Integer> days = jdbcTemplate.queryForList(sql.toString(), Integer.class);
        int antdbCount = days.get(0);
        if( null !=integerCommonResult && antdbCount != (int)integerCommonResult.getData()){
			String content = "出账月antdb库与新版rms库的工时表的总数量 不一致 ;"+ "antdb库="+antdbCount+";新版rms库="+ (int)integerCommonResult.getData();
			log.info(content);
			this.setAlarmMails(content);
        }
    }
    private  void checkRmsEmpDateValidSumCount(String yearMonth) throws Exception {
        com.linkus.common.web.CommonResult<Integer> integerCommonResult = null;
        try {
            integerCommonResult = rmsEmpDateFeignClient.rmsEmpDateValidSumCount(yearMonth);
        }catch (Exception e){
            log.error("新版rms库调用失败，原因：{}", "linkus-rms服务未生效:",e);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) from pro_emp_date where  SUBSTR(emp_date, 1,7) =  " );
        sql.append("'"+yearMonth+"' ");
        sql.append("and is_valid = '1' ");
        List<Integer> days = jdbcTemplate.queryForList(sql.toString(), Integer.class);
        int antdbValidCount = days.get(0);
        if( null !=integerCommonResult && antdbValidCount != (int)integerCommonResult.getData()){
			String content = "出账月antdb库与新版rms库的工时表的有效记录数 不一致 ;"+ "antdb库="+antdbValidCount+";新版rms库="+ (int)integerCommonResult.getData();
			log.info(content);
			this.setAlarmMails(content);
        }
    }
    private  void checkRmsEmpDateOutGoingSumCount(String yearMonth) throws Exception {
        com.linkus.common.web.CommonResult<List<RmsEmpDateVo>> integerCommonResult = null;
        try {
            integerCommonResult = rmsEmpDateFeignClient.rmsEmpDateOutGoingSumCount(yearMonth);
        }catch (Exception e){
            log.error("新版rms库调用失败，原因：{}", "linkus-rms服务未生效:",e);
        }
        StringBuilder sql = new StringBuilder();
        sql.append("select count(*) from pro_emp_date where  SUBSTR(emp_date, 1,7) =  " );
        sql.append("'"+yearMonth+"' ");
        sql.append("and is_valid = '1' and employee_type <> 'Trainee' and emp_type is not null and mh_is_required='1'");
        List<Integer> days = jdbcTemplate.queryForList(sql.toString(), Integer.class);
        int antdbValidCount = days.get(0);
        if( null !=integerCommonResult && antdbValidCount != ((List<RmsEmpDateVo>)integerCommonResult.getData()).size()){
			String content = "出账月antdb库与新版rms库的工时表的出账数量 不一致 ;"+ "antdb库="+antdbValidCount+";新版rms库="+ (int)integerCommonResult.getData().size();
			log.info(content);
			this.setAlarmMails(content);
        }
    }

	private void setAlarmMails(String content) throws Exception {
		List<ItfMailInfo>  mailInfos = new ArrayList<>();
		ItfMailInfo mailInfo = new ItfMailInfo();
		mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
		mailInfo.setFromMailboxName("<EMAIL>");
		mailInfo.setToList(Arrays.asList(workHoursAbnormalMailToList.split(",")));
		mailInfo.setCcList(Arrays.asList(workHoursAbnormalMailccList.split(",")));
		mailInfo.setSubject("antdb工时数据异常校验");
		mailInfo.setSubsystem(DMPSubsystem.DOM.getCode());

		mailInfo.setContent(content);
		mailInfos.add(mailInfo);
		try {
			com.linkus.common.web.CommonResult<Void> commonResult =  mailFeignClient.sendMails(mailInfos);
			log.info("antdb工时数据异常校验发送成功",commonResult);
		}catch (Exception e){
			log.error("邮件未发送，原因：{}", "linkus-msg服务未生效:",e);
			throw e;
		}
	}

    //count>0报警
    //1,校验项目ID是否有为空的 \2,人员ID是否有为空的\3,小组ID是否有为空的\4,PRD下是否有产品标签为空的 \5,预算类型是否有为空的
    private  void checkIsNull(String type,String yearMonth) throws Exception {
        StringBuilder sql = new StringBuilder();
        if("projectId".equals(type)){
            sql.append("select count(*) from pro_emp_date where  emp_date >=  " );
            sql.append("('"+yearMonth+"' )");
            sql.append(" and pro_id  is  null ");
        }else if("userId".equals(type)){
            sql.append("select count(*) from pro_emp_date where  emp_date >=  " );
            sql.append("('"+yearMonth+"' )");
            sql.append(" and emp_id  is  null ");
        }else if("teamId".equals(type)){
            sql.append("select count(*) from pro_emp_date where  emp_date >=  " );
            sql.append("('"+yearMonth+"' )");
            sql.append(" and group_id  is  null ");
        }else if("prdTag".equals(type)){
            sql.append("select count(*) from pro_emp_date where  SUBSTR(emp_date, 1,7)=  " );
			sql.append("('"+yearMonth+"' )");//这里特殊取年月
            sql.append(" and product_codename is  null  and is_valid='1' and  sbu_id='173' ");
        } else if("budgetType".equals(type)){
            sql.append("select count(*) from pro_emp_date where  emp_date >=  " );
            sql.append("('"+yearMonth+"' )");
            sql.append(" and is_valid = '1' and emp_type is  null and employee_type <>'Trainee' and mh_is_required='1' ");
        }

        List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
        if (CollectionUtils.isEmpty(datas) || datas.get(0)==0){
            return ;
        }
		if("projectId".equals(type)){
			this.setAlarmMails("每月出账前验证，有项目ID为空的记录");
		}else if("userId".equals(type)){
			this.setAlarmMails("每月出账前验证，有人员ID为空的记录");
		}else if("teamId".equals(type)){
			this.setAlarmMails("每月出账前验证，有小组ID为空的记录");
		}else if("prdTag".equals(type)){
			this.setAlarmMails("每月出账前验证，有产品标签为空的记录");
		} else if("budgetType".equals(type)){
			this.setAlarmMails("每月出账前验证，有预算类型为空的记录");
		}

    }



    //集合 count>0报警
    //1,稽核当月是否存在项目开始日期之前的有效工时
	private void checkIfHaveDataBeforeProjectStartDate(String ym) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select\n" +
				"        count(*) \n" +
				"from\n" +
				"        pro_emp_date a\n" +
				"left join taskplan e on\n" +
				"        a.pro_id = e.id\n" +
				"left join employee w on\n" +
				"        a.emp_id = w.employeeid\n" +
				"left join sys_aipro y on \n" +
				"    e.projectcode=y.project_code\n" +
				"where\n" +
				"        a.emp_date like '"+ ym + "%'\n" +
				"        and a.emp_date < y.START_DATE\n" +
				"        and a.is_valid = '1' ");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("当月存在项目开始日期之前的有效工时");
	}
	// \2、人员已离职，但有比离职日期大的有效工时
	private void checkIfHaveDataGreatDepartDate(String ym,String year) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select\n" +
				"        count(*) \n" +
				"from\n" +
				"        pro_emp_date c \n" +
				"        left join  employee b on\n" +
				"        c.emp_id=b.employeeid\n" +
				"    left join sys_aiemp a  on \n" +
				"        a.PERSON_ID=b.hr_emp_id\n" +
				"        where \n" +
				"          c.emp_date like '"+ ym + "%'\n" +
				"          and c.is_valid='1'\n" +
				"          and a.termination_date like '"+ year + "%'\n" +
				"          and a.CURRENT_FLAG='N'\n" +
				"          and c.emp_date > a.termination_date");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("人员已离职，但有比离职日期大的有效工时");
	}
    // \3.查询当月是否有同一个人在同一个项目，同一天里，有多条有效的数据，此数据需要清除 \
	private void checkIfHaveRedundanceData(String ym) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select  count(*)  from\n" +
				"        (select * from\n" +
				"        (select\n" +
				"                a.emp_id,\n" +
				"                a.EMP_DATE,\n" +
				"                count(a.EMP_DATE)\n" +
				"        from\n" +
				"                pro_emp_date  a \n" +
				"        where\n" +
				"                is_valid = '1'\n" +
				"                and SUBSTR(a.emp_date,1,7) = '"+ ym + "%'\n" +
				"        group by\n" +
				"                a.emp_id,\n" +
				"                a.EMP_DATE)x\n" +
				"where\n" +
				"        count >1) y\n" +
				"        inner join employee b on\n" +
				"                y.emp_id = b.employeeid");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("查询当月是否有同一个人在同一个项目，同一天里，有多条有效的数据，此数据需要,???");
	}
    //4,查询已经冻结的项目，在固定月份是否有工时分配，正常冻结的项目不再允许分配工时
	private void checkIfHaveWorkHourAllocationDataInFreezeProject(String ym) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select\n" +
				"        count(a.project_code) \n" +
				"from\n" +
				"        pro_emp_date c  left join taskplan b on b.id=c.pro_id\n" +
				"   left join  sys_aipro a   on a.project_code=b.projectcode\n" +
				"where\n" +
				"        a.budget_status = 'FROZEN'\n" +
				"        and SUBSTR(c.emp_date, 1,7) >='"+ ym + "%'\n" +
				"        and c.is_valid='1'");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("在固定月份有工时分配，正常冻结的项目不再允许分配工时");
	}
    // 5.查询预算冻结后有预分配工时的项目\
	private void checkIfHaveInAdvanceWorkHourAllocationDataInFreezeBudget(String ym) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append(" select\n" +
				"         count(*) \n" +
				"from\n" +
				"        pro_emp_date a\n" +
				"left join taskplan e on\n" +
				"        a.pro_id = e.id\n" +
				"left join sys_aipro w on\n" +
				"        e.projectcode= w.project_code\n" +
				"left join employee b on\n" +
				"        a.emp_id= b.employeeid\n" +
				"where\n" +
				"        a.emp_date >= '"+ ym + "%'\n" +
				"        and w.budget_status='FROZEN'\n" +
				"        and a.is_valid='1'");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("预算冻结后有预分配工时的项目");
	}
    //6、查询视图里有没有bu_id为空的数据
	private void checkIfHaveBuIdIsNullData() throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select count(*) from VW_PRJ_USER_ITF where bu_id is null ");
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("视图里有bu_id为空的数据");
	}

    //7、查询视图里有没有天数大于当月的工作日的
	private void checkIfHaveGreatworkDaysData(int workDays) throws Exception {
		StringBuilder sql = new StringBuilder();
		sql.append("select count(*) from\n" +
				"        (\n" +
				"        select\n" +
				"                hr_user_id,\n" +
				"                count(hr_user_id)as a\n" +
				"        from\n" +
				"                VW_PRJ_USER_ITF\n" +
				"        group by\n" +
				"                hr_user_id)X\n" +
				"where\n" +
				"        x.a >"+ workDays);
		List<Integer> datas = jdbcTemplate.queryForList(sql.toString(), Integer.class);
		if(CollectionUtils.isEmpty(datas) || datas.get(0)==0){
			return;
		}
		this.setAlarmMails("视图里有天数大于当月的工作日的");
	}

}
