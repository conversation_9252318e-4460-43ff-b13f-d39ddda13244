package com.linkus.prj.service.impl;

import cn.hutool.core.map.MapUtil;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.BatchCondsUpsert;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.aggregationOperation.LookupLetPipelinesOperation;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.BsonTool;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.DoubleUtil;
import com.linkus.base.util.NumberUtils;
import com.linkus.base.util.StringUtil;
import com.linkus.mail.param.MailInfo;
import com.linkus.mail.service.IMailService;
import com.linkus.prj.constant.AbpConstant;
import com.linkus.prj.constant.PrjConstant;
import com.linkus.prj.dao.*;
import com.linkus.prj.model.*;
import com.linkus.prj.model.bo.DevMgtPrjInfoBo;
import com.linkus.prj.model.bo.PrjAbpOopBgtAllBO;
import com.linkus.prj.model.bo.PrjAbpOopBgtBO;
import com.linkus.prj.model.bo.PrjCostDeviationEfBgtBo;
import com.linkus.prj.model.bo.PrjCostDeviationEfBo;
import com.linkus.prj.model.bo.PrjCostDvtBgtBo;
import com.linkus.prj.model.bo.PrjCostDvtOriginalBgtBo;
import com.linkus.prj.model.bo.PrjDevMgtInfoBo;
import com.linkus.prj.model.bo.PrjDevMgtInfoForSubPrjBo;
import com.linkus.prj.model.vo.*;
import com.linkus.prj.service.IPrjBudgetService;
import com.linkus.prj.service.IPrjCostStatisticsService;
import com.linkus.prj.service.IPrjDvtMgtService;
import com.linkus.prj.service.IPrjInfoService;
import com.linkus.prj.vo.DevManageStatisVo;
import com.linkus.prj.vo.PrjCostStatisticsNewVo;
import com.linkus.prj.vo.PrjDvtMgtVo;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysDefRoleUserDao;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import com.mongodb.client.MongoCursor;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.LimitOperation;
import org.springframework.data.mongodb.core.aggregation.SkipOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfig;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("PrjDvtMgtServiceImpl")
public class PrjDvtMgtServiceImpl implements IPrjDvtMgtService {
    @Autowired
    private ISysDefRoleUserDao sysDefRoleUserDao;
    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IPrjInfoService prjInfoService;
    @Autowired
    private IPrjDvtMgtDao prjDvtMgtDao;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private IMailService mailService;
    @Resource
    private FreeMarkerConfig freeMarkerConfig;
    @Autowired
    private IPrjInfoDao prjInfoDao;
    @Autowired
    private AbpFrateCnfgDao abpFrateCnfgDao;
    @Autowired
    private ISysVerMgtDao sysVerMgtDao;
    @Autowired
    private IPrjBudgetService prjBudgetService;
    @Autowired
    private IPrjCostStatisticsService prjCostStatisticsService;
    @Autowired
    private IAbpOopDao abpOopDao;
    @Autowired
    private IPrjBudgetDao prjBudgetDao;
    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;

    private final static ObjectId DEFAULT_CC_USER_ID= new ObjectId("5a389f0510d00e056c73da0b");
    private final static ObjectId NI_XUE_CC_USER_ID= new ObjectId("5a389f0510d00e056c73e4ab");

    @Override
    public void sendPrjDvtMgtDataMail(List<ObjectId> devMgtIds, ObjectId prjDevMgtTypeId, TeSysUser loginUser){
        //查询数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.common__id, devMgtIds));
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid), prjDevMgtTypeId));
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            throw BusinessException.initExc("延期/超支数据为空");
        }
        //获取项目id
        List<ObjectId> prjIds = prjDvtMgtList.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
        //根据项目聚合
        Map<ObjectId, List<PrjDvtMgt>> dvtPrjMap = prjDvtMgtList.stream().collect(Collectors.groupingBy(dvt -> dvt.getPrj().getCid()));
        //查询项目集
        List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            throw BusinessException.initExc("项目集为空");
        }
        prjIds = prjInfoList.stream().map(TePrjInfo::getPrjId).collect(Collectors.toList());

        List<ObjectId> provIds = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(provIds)){
            throw BusinessException.initExc("项目集省份为空");
        }
        //根据项目经理聚合
        Map<ObjectId, List<ObjectId>> pmUserId2PrjSetInfoMap = prjInfoList.stream()
                .filter(prj -> prj.getPmUser() != null && prj.getPmUser().getUserId() != null)
                .collect(Collectors.groupingBy(prj -> prj.getPmUser().getUserId(),Collectors.mapping(TePrjInfo::getPrjId,Collectors.toList())));
        //项目经理id
        List<ObjectId> pmUserIds = new ArrayList<>();
        //查询项目管理员
        List<TeSysDefRoleUser> roleUserList = sysDefRoleUserService.getUsers(Arrays.asList(StringUtil.toObjectId(SysDefConstants.PRJADMIN_DEF_ID)), prjIds, null, SysDefTypeCodeName.PRJ);
        Map<ObjectId, ObjectId> prjAdminUserMap = null;
        Map<ObjectId, String> prjAdminMailBoxMap = null;
        if (CollectionUtils.isNotEmpty(roleUserList)){
            prjAdminUserMap = roleUserList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null).collect(Collectors.toMap(TeSysDefRoleUser::getDefId,role -> role.getRoleUser().getUserId(),(v1,v2) -> v2));
            List<ObjectId> userIds = roleUserList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)){
                List<TeSysUser> prjAdmins = sysUserService.getUsersByIds(userIds);
                if (CollectionUtils.isNotEmpty(prjAdmins)){
                    prjAdminMailBoxMap = prjAdmins.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, TeSysUser::getMailBox, (v1, v2) -> v2));
                }
            }
        }
        for (TePrjInfo prjInfo : prjInfoList){
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if (pmUser != null && pmUser.getUserId() != null){
                pmUserIds.add(pmUser.getUserId());
            }
        }
        if (CollectionUtils.isEmpty(pmUserIds)){
            throw BusinessException.initExc("收件人为空");
        }
        //查询收件人员信息
        List<TeSysUser> toListUser = sysUserService.getUsersByIds(pmUserIds);
        if (CollectionUtils.isEmpty(toListUser)){
            throw BusinessException.initExc("收件人为空");
        }
        //跟踪人id
        Set<ObjectId> ccListUserIdSet = new HashSet<>();
        ccListUserIdSet.add(DEFAULT_CC_USER_ID);
        ccListUserIdSet.add(NI_XUE_CC_USER_ID);
        //查询省份运营管理员
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
        List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(null, roleIds, provIds, SysDefTypeCodeName.ABP_PROV);
        Map<ObjectId, List<TeSysDefRoleUser>> operateAdminMap = null;
        if (CollectionUtils.isNotEmpty(operateAdmins)){
            List<ObjectId> roleUserIds = operateAdmins.stream().filter(roleUser -> roleUser.getRoleUser() != null && roleUser.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(roleUserIds)){
                ccListUserIdSet.addAll(roleUserIds);
            }
            operateAdminMap = operateAdmins.stream().filter(role -> role.getDefId() != null && role.getRoleUser() != null && role.getRoleUser().getUserId() != null)
                    .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId));
        }
        //省份PSO经理、省份PSO总监
        roleIds.clear();
        roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
        roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
        List<TeSysDefRoleUser> managerAndDirectorList = sysDefRoleUserDao.getSysUserRole(null,roleIds, provIds, null);
        Map<ObjectId, List<TeSysDefRoleUser>> managerAndDirectorMap = null;
        if (CollectionUtils.isNotEmpty(managerAndDirectorList)){
            List<ObjectId> roleUserIds = managerAndDirectorList.stream().filter(roleUser -> roleUser.getRoleUser() != null && roleUser.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(roleUserIds)){
                ccListUserIdSet.addAll(roleUserIds);
            }
            managerAndDirectorMap = managerAndDirectorList.stream().collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId));
        }

        //查询抄送人信息
        Map<ObjectId, TeSysUser> ccListMap = null;
        if (CollectionUtils.isNotEmpty(ccListUserIdSet)){
            List<TeSysUser> ccList = sysUserService.getUsersByIds(new ArrayList<>(ccListUserIdSet));
            if (CollectionUtils.isNotEmpty(ccList)){
                ccListMap = ccList.stream().collect(Collectors.toMap(TeSysUser::getId, Function.identity()));
            }
        }

        //收件人：项目经理
        //抄送人：省运营、跟踪人（若出现重复人员需要剔重）
        //邮件标题：项目管理系统(AI-PMS)：异常偏差反馈提醒
        List<MailInfo> mailInfoList = new ArrayList<>();
        Set<ObjectId> dvtIdSet = new HashSet<>();
        for (TeSysUser to : toListUser){
            String mailBox = to.getMailBox();
            if (StringUtil.isNull(mailBox)){
                continue;
            }
            PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
            mailVo.setPmUserName(to.getUserName());
            mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));
            //收件人
            List<String> toList = new ArrayList<>();
            toList.add(mailBox);
            //获取项目
            List<ObjectId> prjIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(pmUserId2PrjSetInfoMap.get(to.getId()))){
                prjIdList.addAll(pmUserId2PrjSetInfoMap.get(to.getId()));
            }
            if (CollectionUtils.isEmpty(prjIdList)){
                continue;
            }
            List<TePrjInfo> prjInfos = prjInfoList.stream().filter(prj -> prjIdList.contains(prj.getPrjId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(prjInfos)){
                continue;
            }
            //获取抄送人
            Set<String> ccSet = new HashSet<>();
            //数据
            List<PrjDvtMgtMailRcdInfoVo> rcdInfoVos = new ArrayList<>();
            for (TePrjInfo prjInfo : prjInfos){
                if (MapUtils.isNotEmpty(prjAdminUserMap) && MapUtils.isNotEmpty(prjAdminMailBoxMap)){
                    ObjectId prjAdminUserId = prjAdminUserMap.get(prjInfo.getPrjId());
                    if (prjAdminUserId != null && StringUtil.isNotNull(prjAdminMailBoxMap.get(prjAdminUserId))){
                        String prjAdminMailBox = prjAdminMailBoxMap.get(prjAdminUserId);
                        if (!toList.contains(prjAdminMailBox)){
                            toList.add(prjAdminMailBox);
                        }
                    }
                }
                //抄送人
                if (MapUtils.isNotEmpty(ccListMap)){
                    if(ccListMap.get(DEFAULT_CC_USER_ID) != null
                            && StringUtils.isNotEmpty(ccListMap.get(DEFAULT_CC_USER_ID).getMailBox())) {
                        ccSet.add(ccListMap.get(DEFAULT_CC_USER_ID).getMailBox());
                    }
                    if(ccListMap.get(NI_XUE_CC_USER_ID) != null
                            && StringUtils.isNotEmpty(ccListMap.get(NI_XUE_CC_USER_ID).getMailBox())) {
                        ccSet.add(ccListMap.get(NI_XUE_CC_USER_ID).getMailBox());
                    }
                    TeIdNameCn prov = prjInfo.getProv();
                    if (prov!= null){
                        //省份运营管理员
                        if (MapUtils.isNotEmpty(operateAdminMap) && CollectionUtils.isNotEmpty(operateAdminMap.get(prov.getCid()))){
                            List<TeSysDefRoleUser> sysDefRoleUserList = operateAdminMap.get(prov.getCid());
                            for (TeSysDefRoleUser roleUser : sysDefRoleUserList){
                                TeSysUser sysUser = ccListMap.get(roleUser.getRoleUser().getUserId());
                                if (sysUser != null && StringUtil.isNotNull(sysUser.getMailBox())){
                                    ccSet.add(sysUser.getMailBox());
                                }
                            }
                        }
                        //省份PSO经理、省份PSO总监
                        if (MapUtils.isNotEmpty(managerAndDirectorMap) && CollectionUtils.isNotEmpty(managerAndDirectorMap.get(prov.getCid()))){
                            List<TeSysDefRoleUser> sysDefRoleUserList = managerAndDirectorMap.get(prov.getCid());
                            for (TeSysDefRoleUser roleUser : sysDefRoleUserList){
                                TeSysUser sysUser = ccListMap.get(roleUser.getRoleUser().getUserId());
                                if (sysUser != null && StringUtil.isNotNull(sysUser.getMailBox())){
                                    ccSet.add(sysUser.getMailBox());
                                }
                            }
                        }

                    }
                }
                //获取进度
                List<PrjDvtMgt> prjDvtMgts = listMailPrjDvtMgt(dvtPrjMap, prjInfo);
                if (CollectionUtils.isNotEmpty(prjDvtMgts)){
                    for (PrjDvtMgt dvtMgt : prjDvtMgts){
                        dvtIdSet.add(dvtMgt.getId());
                        rcdInfoVos.add(convertMailInfo(prjInfo, dvtMgt));
                    }
                }
            }
            mailVo.setRcdInfoVos(rcdInfoVos);
            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(toList);
            if(CollectionUtils.isNotEmpty(ccSet)) {
                mailInfo.setCcList(new ArrayList<>(ccSet));
            }
            mailInfo.setSubject("异常偏差反馈提醒");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mailVo, prjDevMgtTypeId));
            mailInfoList.add(mailInfo);

        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
        if (CollectionUtils.isNotEmpty(dvtIdSet)){
            List<IDbCondition> updateConds = new ArrayList<>();
            updateConds.add(new DC_I<>(DFN.common__id, new ArrayList<>(dvtIdSet)));
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DFN.PrjDvtMgt.isMailed, true));
            prjDvtMgtDao.updateByConds(updateConds, updates);
        }
    }

    @Override
    public void sendPrjJwCostOverspendMail(List<ObjectId> devMgtIds, ObjectId prjDevMgtTypeId, TeSysUser loginUser) {

        //邮件正文：
        //XXXX，你好：
        //XX区XX项目（项目集编码/项目集名称）交维后成本超支，请及时登录“项目管理系统(AI-PMS)”进行超支原因反馈。
        //交维后成本超支清单（图2）
        //区域、省份、项目集名称、项目集编码、项目编码、项目经理、PSO总监、实际交维完成月份、YYYY年超支情况（人力投入(人月)、直接费用(K)、综合总成本(K)）、YY月超支情况（人力投入(人月)、直接费用(K)、综合总成本(K)）
        //查询数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.common__id, devMgtIds));
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid), prjDevMgtTypeId));
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            throw BusinessException.initExc("延期/超支数据为空");
        }
        //获取项目id
        List<ObjectId> prjIds = prjDvtMgtList.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
        //根据项目聚合
        Map<ObjectId, List<PrjDvtMgt>> dvtPrjMap = prjDvtMgtList.stream().collect(Collectors.groupingBy(dvt -> dvt.getPrj().getCid()));
        //查询项目集
        List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            throw BusinessException.initExc("项目集为空");
        }
        List<ObjectId> provIds = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(provIds)){
            throw BusinessException.initExc("项目集省份为空");
        }
        //查询省份信息
        List<SysDef> provDefs = sysDefService.getSysDefsByIds(provIds);
        if (CollectionUtils.isEmpty(provDefs)){
            throw BusinessException.initExc("项目集省份为空");
        }
        Map<ObjectId,TeIdNameCn> regionMap = new HashMap<>();
        for (SysDef prov : provDefs){
            List<TeIdNameCn> cndtItems = prov.getCndtItems();
            if (CollectionUtils.isNotEmpty(cndtItems)){
                for (TeIdNameCn cn : cndtItems){
                    if (PrjConstant.REGION.equals(cn.getCodeName())){
                        regionMap.put(prov.getId(),cn);
                    }
                }
            }
        }
        List<ObjectId> userIds = new ArrayList<>();
        //抄送人
        TeSysUser ccd = sysUserService.queryByLoginName(PrjConstant.AITECH_BSC_CCD_LOGIN_NAME);
        //项目经理id
        for (TePrjInfo prjInfo : prjInfoList){
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if (pmUser != null && pmUser.getUserId() != null){
                userIds.add(pmUser.getUserId());
            }
        }
        List<TeSysDefRoleUser> managerAndDirectorList = sysDefRoleUserDao.getSysUserRole(null,Collections.singleton(PrjConstant.DEF_ID_DIRECTOR_ROLE), provIds, null);
        if (CollectionUtils.isEmpty(managerAndDirectorList)){
           throw BusinessException.initExc("收件人为空");
        }
        List<ObjectId> toListUserIds = managerAndDirectorList.stream().filter(roleUser -> roleUser.getRoleUser() != null && roleUser.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(toListUserIds)){
            throw BusinessException.initExc("收件人为空");
        }
        Map<ObjectId, ObjectId> managerAndDirectorMap = managerAndDirectorList.stream()
                .filter(roleUser -> roleUser.getRoleUser() != null && roleUser.getRoleUser().getUserId() != null)
                .collect(Collectors.toMap(TeSysDefRoleUser::getDefId,roleUser -> roleUser.getRoleUser().getUserId(),(v1,v2) -> v2));
        //查询人员邮件
        userIds.addAll(toListUserIds);
        List<TeSysUser> userList = sysUserService.getUsersByIds(userIds);
        if (CollectionUtils.isEmpty(userList)){
            throw BusinessException.initExc("收件人为空");
        }
        Map<ObjectId, TeSysUser> userMap = userList.stream().collect(Collectors.toMap(TeSysUser::getId, Function.identity()));

        List<TeSysUser> toListUser = userList.stream().filter(user -> toListUserIds.contains(user.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(toListUser)){
            throw BusinessException.initExc("收件人为空");
        }
        //收件人：PSO总监
        //抄送人：项目经理、AITech-BSC-CCD
        //邮件标题：项目管理系统(AI-PMS)：大项目跟踪提醒
        List<MailInfo> mailInfoList = new ArrayList<>();
        Set<ObjectId> dvtIdSet = new HashSet<>();
        for (TePrjInfo prjInfo : prjInfoList){
            TeIdNameCn prov = prjInfo.getProv();
            if (prov == null || prov.getCid() == null && managerAndDirectorMap.get(prov.getCid()) == null){
                continue;
            }
            //获取收件人
            ObjectId toListId = managerAndDirectorMap.get(prov.getCid());
            TeSysUser toList = userMap.get(toListId);
            if (toList == null || StringUtil.isNull(toList.getMailBox())){
                continue;
            }
            PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
            mailVo.setPrjNameCode(StringUtil.getNotNullStr(prjInfo.getPrjCode()) + "/" + prjInfo.getPrjName());
            mailVo.setPsoSm(toList.getUserName());
            mailVo.setProv(StringUtil.getNotNullStr(prov.getName()));
            TeIdNameCn region = regionMap.get(prov.getCid());
            mailVo.setRegion(region == null ? "" : StringUtil.getNotNullStr(region.getName()));
            mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));
            //获取抄送人
            Set<String> ccSet = new HashSet<>();
            //抄送人
            if(ccd != null && StringUtils.isNotEmpty(ccd.getMailBox())) {
                ccSet.add(ccd.getMailBox());
            }
            TePrjInfo2User pmUser = prjInfo.getPmUser();
            if(pmUser != null && userMap.get(pmUser.getUserId()) != null
                    && StringUtils.isNotEmpty(userMap.get(pmUser.getUserId()).getMailBox())) {
                ccSet.add(userMap.get(pmUser.getUserId()).getMailBox());
            }
            //数据
            List<PrjDvtMgtMailRcdInfoVo> rcdInfoVos = new ArrayList<>();
            //获取进度
            List<PrjDvtMgt> prjDvtMgts = listMailPrjDvtMgt(dvtPrjMap, prjInfo);
            if (CollectionUtils.isNotEmpty(prjDvtMgts)){
                dvtIdSet.add(prjDvtMgts.get(0).getId());
                rcdInfoVos.add(convertMailInfos(prjInfo, prjDvtMgts.get(0)));
            }
            mailVo.setRcdInfoVos(rcdInfoVos);
            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(Collections.singletonList(toList.getMailBox()));
            if(CollectionUtils.isNotEmpty(ccSet)) {
                mailInfo.setCcList(new ArrayList<>(ccSet));
            }
            mailInfo.setSubject("大项目跟踪提醒");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mailVo, prjDevMgtTypeId));
            mailInfoList.add(mailInfo);
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
        if (CollectionUtils.isNotEmpty(dvtIdSet)){
            List<IDbCondition> updateConds = new ArrayList<>();
            updateConds.add(new DC_I<>(DFN.common__id, new ArrayList<>(dvtIdSet)));
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DFN.PrjDvtMgt.isMailed, true));
            prjDvtMgtDao.updateByConds(updateConds, updates);
        }
    }

    @Override
    public void deletePrjDvtMgt(List<ObjectId> ids,TeSysUser loginUser,String invalidDesc) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<ObjectId>(DFN.common__id,ids));
        List<UpdataData> updataDataList = new ArrayList<>();
        updataDataList.add(new UpdataData(DFN.common_isValid,false));
        updataDataList.add(new UpdataData(DFN.PrjDvtMgt.deleteUser,BsonTool.objectToBsonValue(loginUser.trans2User())));
        updataDataList.add(new UpdataData(DFN.PrjDvtMgt.deleteTime,new Date()));
        updataDataList.add(new UpdataData(DFN.PrjDvtMgt.invalidDesc,invalidDesc));
        prjDvtMgtDao.updateByConds(conds,updataDataList);
    }

    @Override
    public void updatePrjDvtMgt(ObjectId id, String type, String updata, ObjectId causeTypeId,String rectifyAction,String rectifyEndDate) {
        //查询数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common__id,id));
        conds.add(new DC_E(DFN.common_isValid,true));
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            throw BusinessException.initExc("数据不存在");
        }
        PrjDvtMgt prjDvtMgt = prjDvtMgtList.get(0);
        RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
        if (rcdInfo == null){
            rcdInfo = new RcdInfo();
        }
        TeIdNameCn typeCn = prjDvtMgt.getType();
        if (typeCn == null || typeCn.getCid() == null){
            throw BusinessException.initExc("类型不存在");
        }
        List<UpdataData> updataDataList = new ArrayList<>();
        //延期说明
        if (PrjConstant.PRJ_DVT_MGT_TYPE_DESC.equals(type)){
            if(!DateUtil.getLastMonth(DateUtil.DATE_MONTH_FOTMAT2).equals(prjDvtMgt.getYm()) && !typeCn.getCid().equals(PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID)){
                throw BusinessException.initExc("仅支持填写上月偏差说明！");
            }
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.desc,updata));
            //整改措施
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyAction,rectifyAction));
            //整改完成日期
            Date endDate = null;
            if (StringUtil.isNotNull(rectifyEndDate)){
                endDate =  DateUtil.parseDate(rectifyEndDate,DateUtil.DATE_FORMAT);
            }
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyEndDate,endDate));
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_NOTES.equals(type)){
            //备注
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.notes,updata));
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_RECTIFY_ACTION_REVIEWED.equals(type)){
            //PM整改措施-评审
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyActionReviewed,updata));
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_SYMPTOM.equals(type)){
            //病症
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.symptom,updata));
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_CAUSETYPE.equals(type)){
            if (causeTypeId != null){
                //原因大类
                SysDef causeType = sysDefService.getSysDefById(causeTypeId);
                if (causeType == null){
                    throw BusinessException.initExc("原因大类不存在");
                }
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeType,BsonTool.objectToBsonValue(causeType.trans2IdNameCn())));
            }else {
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeType,null));
            }
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_CAUSESUBTYPE.equals(type)){
            if (causeTypeId != null){
                //原因小类
                SysDef causeSubType = sysDefService.getSysDefById(causeTypeId);
                if (causeSubType == null){
                    throw BusinessException.initExc("病因不存在");
                }
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeSubType,BsonTool.objectToBsonValue(causeSubType.trans2IdNameCn())));
            }else {
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeSubType,null));
            }

        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_CAUSESUB2TYPE.equals(type)){
            if (causeTypeId != null){
                //药方
                SysDef causeSub2Type = sysDefService.getSysDefById(causeTypeId);
                if (causeSub2Type == null){
                    throw BusinessException.initExc("药方不存在");
                }
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeSub2Type,BsonTool.objectToBsonValue(causeSub2Type.trans2IdNameCn())));
            }else {
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.causeSub2Type,null));
            }

        }else if (SysDefConstants.PRJ_COST_STATISTICS_NEEDCHECK.equals(type)){
           rcdInfo.setNeedCheck(updata);
           updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rcdInfo,BsonTool.objectToBsonValue(rcdInfo)));
        }else if (SysDefConstants.PRJ_COST_STATISTICS_MGTACTION.equals(type)){
            rcdInfo.setMgtAction(updata);
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rcdInfo,BsonTool.objectToBsonValue(rcdInfo)));
        }else if (SysDefConstants.PRJ_COST_STATISTICS_IMPROVEITEM.equals(type)){
            rcdInfo.setImproveItem(updata);
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rcdInfo,BsonTool.objectToBsonValue(rcdInfo)));
        }else if (SysDefConstants.PRJ_COST_STATISTICS_MONTHRPTDESC.equals(type)){
            rcdInfo.setMonthRptDesc(updata);
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rcdInfo,BsonTool.objectToBsonValue(rcdInfo)));
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_RECTIFY_STATUS.equals(type)){
            if (causeTypeId != null){
                //状态
                SysDef rectifyStatus = sysDefService.getSysDefById(causeTypeId);
                if (rectifyStatus == null){
                    throw BusinessException.initExc("状态不存在");
                }
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyStatus,BsonTool.objectToBsonValue(rectifyStatus.trans2IdNameCn())));
            }else {
                updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyStatus,null));
            }
        }else if (PrjConstant.PRJ_DVT_MGT_TYPE_DESC_DETAIL.equals(type)){
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.desc,updata));
        }if (PrjConstant.PRJ_DVT_MGT_TYPE_RECTIFYACTION.equals(type)){
            //整改措施
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyAction,rectifyAction));
        }if (PrjConstant.PRJ_DVT_MGT_TYPE_RECTIFYENDDATE.equals(type)){
            //整改完成日期
            Date endDate = null;
            if (StringUtil.isNotNull(rectifyEndDate)){
                endDate =  DateUtil.parseDate(rectifyEndDate,DateUtil.DATE_FORMAT);
            }
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.rectifyEndDate,endDate));
        }
        prjDvtMgtDao.updateByConds(conds,updataDataList);
    }

    @Override
    public void updatePrjDvtMgt(PrjDvtMgtUpdateVo vo) {
        //查询数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common__id, vo.getId()));
        conds.add(new DC_E(DFN.common_isValid,true));
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            throw BusinessException.initExc("数据不存在");
        }

        List<UpdataData> updataDataList = new ArrayList<>();
        if (vo.getIsDelisted() != null){
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.isDelisted, vo.getIsDelisted()));
        }
        if (StringUtils.isNotEmpty(vo.getListReason())){
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.listReason, vo.getListReason()));
        }
        if (StringUtils.isNotEmpty(vo.getPlanDelistDate())){
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.planDelistDate, vo.getPlanDelistDate()));
        }
        if (CollectionUtils.isNotEmpty(vo.getProgress())){
            updataDataList.add(new UpdataData(DFN.PrjDvtMgt.progress, vo.getProgress()));
        }
        prjDvtMgtDao.updateMulti(Collections.singletonList(new BatchCondsUpsert(conds, updataDataList)));
    }

    @Override
    public Map<ObjectId, Long> countNotFillIn(String ym, TeSysUser loginUser) {
        List<ObjectId> prjIdList = listPmOrAdminPrjId(loginUser);
        if(CollectionUtils.isEmpty(prjIdList)){
            return null;
        }
        //查询进度偏差
        if (StringUtils.isEmpty(ym)){
            ym = DateUtil.getLastMonth(DateUtil.DATE_MONTH_FOTMAT2);
        }
        return prjDvtMgtDao.getPrjDevMgtTypeNumByYm(prjIdList, ym);
    }

    @Override
    public List<ObjectId> listPmOrAdminPrjId(TeSysUser loginUser){
        //查询当前用户是项目经理或项目管理员的哪些项目集
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(new ObjectId(SysDefConstants.PRJADMIN_DEF_ID));
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());

        List<TeSysDefRoleUser> pmOrPrjAdminUsers = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.PRJ);
        List<ObjectId> prjIdList = null;
        if (CollectionUtils.isNotEmpty(pmOrPrjAdminUsers)){
            prjIdList = pmOrPrjAdminUsers.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
        }
        //查询项目
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        //conds.add(new DC_E(DFN.prjInfo__isPrjSet, true));

        List<IDbCondition> orConds = new ArrayList<>();
        orConds.add(new DC_E(DFN.prjInfo__pmUser.dot(DFN.common_userId), loginUser.getId()));
        if (CollectionUtils.isNotEmpty(prjIdList)){
            orConds.add(new DC_I<>(DFN.prjInfo__prjId, prjIdList));
        }
        conds.add(new DC_OR(orConds));

        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.prjInfo__prjId);
        fieldNames.add(DFN.prjInfo__subPrjs);
        List<TePrjInfo> prjInfoList = prjInfoDao.findByFieldAndConds(conds, fieldNames);
        if(CollectionUtils.isEmpty(prjInfoList)){
            return null;
        }
        Set<ObjectId> finalPrjIdSet = prjInfoList.stream().map(s -> s.getPrjId()).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(prjInfoList)){
            List<ObjectId> collect = prjInfoList.stream()
                    .filter(tePrjInfo -> CollectionUtils.isNotEmpty(tePrjInfo.getSubPrjs()))
                    .map(TePrjInfo::getSubPrjs)
                    .flatMap(Collection::stream).filter(subPrj ->subPrj.getCid() != null)
                    .map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                finalPrjIdSet.addAll(collect);
            }
        }
        return new ArrayList<>(finalPrjIdSet);
    }

    @Override
    public PrjDvtMgt getLastMonthDesc(String ym, ObjectId prjId, ObjectId typeId, ObjectId rcdId) {
        //获取上月
        String lastMonth = DateUtil.getNextMonth(DateUtil.parseDate(ym, DateUtil.DATE_MONTH_FOTMAT2), -1);
        //查询进度偏差
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),prjId));
        conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid), typeId));
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.ym, lastMonth));
        if(rcdId != null) {
            conds.add(new DC_E(DFN.PrjDvtMgt.rcd.dot(DFN.common_cid), rcdId));
        }
        List<PrjDvtMgt> prjDvtMgts = prjDvtMgtDao.findByFieldAndConds(conds, null);
        return CollectionUtils.isEmpty(prjDvtMgts) ? null : prjDvtMgts.get(0);
    }

    /**
     * 获取邮件正文
     * @param mailVo
     * @param typeId
     * @return
     */
    protected String getMailContent(PrjDvtMgtMailVo mailVo, ObjectId typeId) {
        Template template = null;
        try {
            template = freeMarkerConfig.getConfiguration().getTemplate(PrjDvtMgtMailTemplateEnum.getMailTemplate(typeId),"utf-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        Writer out = new StringWriter();
        try {
            // 输出数据到模板中，生成文件。
            template.process(mailVo, out);
            out.close();
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toString();
    }

    protected void getAuth(PrjDvtMgtVo prjDvtMgtVo, TeSysUser loginUser,List<String> buCodeList,List<ObjectId> provIds,List<ObjectId> prjIds){
        //bu运营管理员权限
        boolean isBuAuth = false;
        //省份运营管理员或省份经理权限
        boolean isProvAuth = false;
        //项目经理或项目管理员权限
        boolean isPrjAuth = false;
        //角色
        List<ObjectId> roleIds = new ArrayList<>();
        roleIds.add(new ObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID));
        //人员id
        List<ObjectId> userIds = new ArrayList<>();
        userIds.add(loginUser.getId());
        //查询是否是bu运营管理员
        List<TeSysDefRoleUser> buPrjBudgetAdminUsers = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.AI_BU);
        if (CollectionUtils.isNotEmpty(buPrjBudgetAdminUsers)){
            isBuAuth = true;
            //获取buId
            List<ObjectId> buIdList = buPrjBudgetAdminUsers.stream().filter(role -> role.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(buIdList) && StringUtil.isNull(prjDvtMgtVo.getBuCode())){
                //查询bu信息
                List<SysDef> buDefList = sysDefService.getSysDefsByIds(buIdList);
                if (CollectionUtils.isNotEmpty(buDefList)){
                    //获取bucode
                    List<String> collect = buDefList.stream().filter(bu -> StringUtil.isNotNull(bu.getCodeName())).map(SysDef::getCodeName).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect)){
                        buCodeList.addAll(collect);
                    }
                }
            }
        }
        //不是bu运营管理员，查询省份权限
        if (!isBuAuth){
            //不是，查询省份运营管理员
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
            List<TeSysDefRoleUser> operateAdmins = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.ABP_PROV);
            if (CollectionUtils.isNotEmpty(operateAdmins)) {
                isProvAuth = true;
                List<ObjectId> provIdList = operateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(provIdList) && prjDvtMgtVo.getProvId() == null){
                    provIds.addAll(provIdList);
                }
            }
            //省份经理
            roleIds.clear();
            roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
            roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
            List<TeSysDefRoleUser> managerList = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.AI_BU);
            if (CollectionUtils.isNotEmpty(managerList)){
                isProvAuth = true;
                List<ObjectId> provIdList = managerList.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(provIdList) && prjDvtMgtVo.getProvId() == null){
                    provIds.addAll(provIdList);
                }
            }
        }

        //查询当前用户是项目经理或项目管理员的哪些项目或项目集
        roleIds.clear();
        roleIds.add(new ObjectId(SysDefConstants.PM_DEF_ID));
        roleIds.add(new ObjectId(SysDefConstants.PRJADMIN_DEF_ID));
        List<TeSysDefRoleUser> pmOrPrjAdminUsers = sysDefRoleUserDao.getSysUserRole(userIds, roleIds, null, SysDefTypeCodeName.PRJ);
        if (CollectionUtils.isNotEmpty(pmOrPrjAdminUsers)) {
            isPrjAuth = true;
            List<ObjectId> prjIdList = pmOrPrjAdminUsers.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(prjIdList)){
                prjIds.addAll(prjIdList);
            }
        }

        if (!isBuAuth && !isPrjAuth && !isProvAuth){
            throw BusinessException.initExc("你没有查询权限");
        }
    }

    protected List<DevMgtPrjInfoBo> listPrjInfo(PrjDvtMgtVo prjDvtMgtVo) {
        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where("isValid").ne(false).and("isPrjSet").is(true);
        List<Criteria> andConds = new ArrayList<>();
        List<Criteria> prjOrConds = new ArrayList<>();
        List<Criteria> andCond1 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getPrjIds())){
            prjOrConds.add(Criteria.where("prjId").in(prjDvtMgtVo.getPrjIds()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getBuCodeList())){
            andCond1.add(Criteria.where("sbuId").in(prjDvtMgtVo.getBuCodeList()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getProvIds())){
            andCond1.add(Criteria.where("prov.cid").in(prjDvtMgtVo.getProvIds()));
        }
        if (CollectionUtils.isNotEmpty(andCond1)){
            prjOrConds.add(new Criteria().andOperator(andCond1.toArray(new Criteria[andCond1.size()])));
        }
        if (CollectionUtils.isNotEmpty(prjOrConds)){
            andConds.add(new Criteria().orOperator(prjOrConds.toArray(new Criteria[prjOrConds.size()])));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getLevelIds())){
            criteria.and("level.cid").in(prjDvtMgtVo.getLevelIds());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjCode())){
            criteria.and("prjCode").regex(prjDvtMgtVo.getPrjCode());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjName())){
            criteria.and("prjName").regex(prjDvtMgtVo.getPrjName());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeStartDte()) && StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeEndDte())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeStartDte(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeEndDte(), DateUtil.DATE_FORMAT));
            criteria.and("addTime").gte(startDayByDate).lte(lastDayByDate);
        }
        List<Criteria> orOperator = new ArrayList<>();
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getTrackUserFilter())) {
            orOperator.add(Criteria.where("trackUser.loginName").regex(prjDvtMgtVo.getTrackUserFilter()));
            orOperator.add(Criteria.where("trackUser.userName").regex(prjDvtMgtVo.getTrackUserFilter()));
        }
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getPmUserFilter())) {
            orOperator.add(Criteria.where("pmUser.loginName").regex(prjDvtMgtVo.getPmUserFilter()));
            orOperator.add(Criteria.where("pmUser.userName").regex(prjDvtMgtVo.getPmUserFilter()));
        }
        if (CollectionUtils.isNotEmpty(orOperator)){
            andConds.add(new Criteria().orOperator(orOperator.toArray(new Criteria[orOperator.size()])));
        }
        if (CollectionUtils.isNotEmpty(andConds)){
            criteria.andOperator(andConds.toArray(new Criteria[andConds.size()]));
        }
        list.add(Aggregation.match(criteria));
        list.add(Aggregation.lookup("sysDef", "prov.cid", "_id", "prov"));
        list.add(Aggregation.unwind("prov", true));
        if (prjDvtMgtVo.getRegionId() != null){
            list.add(Aggregation.match(Criteria.where("prov.cndtItems.cid").is(prjDvtMgtVo.getRegionId())));
        }
        list.add(Aggregation.project()
            .andInclude("sbuId")
            .andInclude("prjId")
            .andInclude("prjName")
            .andInclude("prjCode")
            .andInclude("prov")
            .andInclude("pmUser")
            .andInclude("trackUser")
            .andInclude("level")
            .andInclude("addTime")
            .andInclude("subPrjs")
            .andExpression("{\"$first\":\"$prjBmks\"}").as("maxPrjBmk")
        );
        list.add(Aggregation.addFields()
            .addField("subPrjs").withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\"$eq\":{{\"$size\":{\"$ifNull\":{\"$subPrjs\",{}}}},0}},\n" +
                        "\tnull,\n" +
                        "\t{\"$filter\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\n" +
                        "\t\t\t\"$and\":{\n" +
                        "\t\t\t\t{\"$ne\":{\"$$item.isOrder\", true}}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}}\n" +
                        "}}")
                .addField("subOrders").withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\"$eq\":{{\"$size\":{\"$ifNull\":{\"$subPrjs\",{}}}},0}},\n" +
                        "\tnull,\n" +
                        "\t{\"$filter\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\n" +
                        "\t\t\t\"$and\":{\n" +
                        "\t\t\t\t{\"$eq\":{\"$$item.isOrder\",true}}\n" +
                        "\t\t\t }\n" +
                        "\t\t}\n" +
                        "\t}}\n" +
                        "}}")
            .build()
        );
        list.add(Aggregation.addFields()
                .addField("bmkPubTime").withValueOfExpression("maxPrjBmk.approveTime")
                .addField("subPrjCode").withValueOfExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t\"$$this.code\",\n" +
                        "\t\t\t\t\",\"\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        if (StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeStart()) && StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeEnd())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeStart(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeEnd(), DateUtil.DATE_FORMAT));
            list.add(Aggregation.match(Criteria.where("bmkPubTime").gte(startDayByDate).lte(lastDayByDate)));
        }
        list.add(Aggregation.lookup("abpOop", "subPrjs.code", "codeName", "abpOop"));
        list.add(Aggregation.addFields()
                .addField("abpOop").withValueOfExpression("{\n" +
                        "\t\"$filter\":{\n" +
                        "\t\t\"input\":\"$abpOop\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\"$and\":{\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isValid\", true}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isDeleted\", false}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.buCode\", \"$sbuId\"}}\n" +
                        "\t\t}}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        list.add(Aggregation.unwind("abpOop", true));
        list.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "abpOop.addTime")));
        list.add(Aggregation.group("abpOop.codeName", "abpOop.buCode")
            .first("sbuId").as("sbuId")
            .first("prjId").as("prjId")
            .first("prjName").as("prjName")
            .first("prjCode").as("prjCode")
            .first("prov").as("prov")
            .first("pmUser").as("pmUser")
            .first("trackUser").as("trackUser")
            .first("level").as("level")
            .first("addTime").as("addTime")
            .first("bmkPubTime").as("bmkPubTime")
            .first("subPrjCode").as("subPrjCode")
            .first("subOrders").as("subOrders")
            .first("abpOop").as("abpOop")
        );
        list.add(Aggregation.group("prjId")
                .first("sbuId").as("sbuId")
                .first("prjId").as("prjId")
                .first("prjName").as("prjName")
                .first("prjCode").as("prjCode")
                .first("prov").as("prov")
                .first("pmUser").as("pmUser")
                .first("trackUser").as("trackUser")
                .first("level").as("level")
                .first("addTime").as("addTime")
                .first("bmkPubTime").as("bmkPubTime")
                .first("subPrjCode").as("subPrjCode")
                .first("subOrders.code").as("subOrders")
                .push("abpOop.linkedOrderCode").as("linkedOrderCodes")
        );
        list.add(Aggregation.addFields()
                .addField("subOrders").withValueOfExpression("{ \"$concatArrays\": {\"$subOrders\", \"$linkedOrderCodes\"} }")
                .build()
        );
        list.add(Aggregation.addFields()
                .addField("subOrderCode").withValueOfExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$subOrders\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t\"$$this\",\n" +
                        "\t\t\t\t\",\"\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getSubPrjCode())){
            list.add(Aggregation.match(Criteria.where("subPrjCode").regex(prjDvtMgtVo.getSubPrjCode())));
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getOrderCode())){
            list.add(Aggregation.match(Criteria.where("subOrderCode").regex(prjDvtMgtVo.getOrderCode())));
        }

        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return mongoTemplate.aggregate(aggregation, DBT.PRJINFO.n(), DevMgtPrjInfoBo.class).getMappedResults();
    }

    /**
     * 转换省份、大区、区域数据
     * @param prov
     * @param vo
     */
    protected void convertToProvAndRegion(DevMgtPrjInfoBo.Prov prov, PrjDvtMgtCostOverEfVo vo) {
        if(prov == null){
            return;
        }
        vo.setProv(prov.getDefName());
        vo.setProvId(prov.getId());
        if(CollectionUtils.isEmpty(prov.getCndtItems())) {
            return;
        }
        for (TeIdNameCn item : prov.getCndtItems()) {
            if(PrjConstant.BIG_REGION.equals(item.getCodeName())){
                vo.setBigRegion(item.getName());
            }else if(PrjConstant.REGION.equals(item.getCodeName())){
                vo.setRegion(item.getName());
            }else if(PrjConstant.ENGDEPT.equals(item.getCodeName())){
                vo.setEngDept(item.getName());
            }
        }
    }

    protected double getFeeStandard(double directFee, double allEmpFee) {
        return directFee / (NumberUtils.equalZero(allEmpFee)? 1d : allEmpFee) * 100;
    }

    protected double getEfFeeStandard(double directFee, double allEmpFee) {
        return NumberUtils.equalZero(allEmpFee) ? directFee : directFee / allEmpFee * 100;
    }

    protected double getDiffRateByBigDecimal(double num1, double num2) {
        double diffRate;
        if(NumberUtils.equalZero(num1)){
            diffRate = 0d;
        }else if(NumberUtils.equalZero(num2)){
            diffRate = 100d;
        }else{
            diffRate = num1 / num2 * 100;
        }
        return BigDecimalUtils.getDoubleHalfNum(diffRate, 2);
    }

    protected double getDiffRate(double bmkFeeStandard, double actualFeeStandard) {
        double diffRate;
        if(NumberUtils.equalZero(actualFeeStandard - bmkFeeStandard)){
            diffRate = 0d;
        }else if(NumberUtils.equalZero(bmkFeeStandard)){
            diffRate = 100d;
        }else{
            diffRate = (actualFeeStandard - bmkFeeStandard) / bmkFeeStandard * 100;
        }
        return diffRate;
    }

    protected PrjDevMgtInfoBo listPrjDevMgtInfo(PrjDvtMgtVo prjDvtMgtVo) {
        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where("isValid").ne(false).and("isPrjSet").is(true);
        List<Criteria> andConds = new ArrayList<>();
        List<Criteria> prjOrConds = new ArrayList<>();
        List<Criteria> andCond1 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getPrjIds()) && StringUtil.isNull(prjDvtMgtVo.getPsoSmFilter())){
            prjOrConds.add(Criteria.where("prjId").in(prjDvtMgtVo.getPrjIds()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getBuCodeList())){
            andCond1.add(Criteria.where("sbuId").in(prjDvtMgtVo.getBuCodeList()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getProvIds())){
            andCond1.add(Criteria.where("prov.cid").in(prjDvtMgtVo.getProvIds()));
        }
        if (CollectionUtils.isNotEmpty(andCond1)){
            prjOrConds.add(new Criteria().andOperator(andCond1.toArray(new Criteria[andCond1.size()])));
        }
        if (CollectionUtils.isNotEmpty(prjOrConds)){
            andConds.add(new Criteria().orOperator(prjOrConds.toArray(new Criteria[prjOrConds.size()])));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getLevelIds())){
            criteria.and("level.cid").in(prjDvtMgtVo.getLevelIds());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjCode())){
            criteria.and("prjCode").regex(prjDvtMgtVo.getPrjCode());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjName())){
            criteria.and("prjName").regex(prjDvtMgtVo.getPrjName());
        }
        if(StringUtil.isNotNull(prjDvtMgtVo.getStatusName())){
            criteria.and("status.name").regex(prjDvtMgtVo.getStatusName());
        }
        if(StringUtil.isNotNull(prjDvtMgtVo.getProjectStatusPom())){
            criteria.and("projectStatusPom").regex(prjDvtMgtVo.getProjectStatusPom());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeStartDte()) && StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeEndDte())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeStartDte(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeEndDte(), DateUtil.DATE_FORMAT));
            criteria.and("addTime").gte(startDayByDate).lte(lastDayByDate);
        }
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getTrackUserFilter())) {
            List<Criteria> orOperator = new ArrayList<>();
            orOperator.add(Criteria.where("trackUser.loginName").regex(prjDvtMgtVo.getTrackUserFilter()));
            orOperator.add(Criteria.where("trackUser.userName").regex(prjDvtMgtVo.getTrackUserFilter()));
            andConds.add(new Criteria().orOperator(orOperator.toArray(new Criteria[orOperator.size()])));
        }
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getPmUserFilter())) {
            List<Criteria> orOperator = new ArrayList<>();
            orOperator.add(Criteria.where("pmUser.loginName").regex(prjDvtMgtVo.getPmUserFilter()));
            orOperator.add(Criteria.where("pmUser.userName").regex(prjDvtMgtVo.getPmUserFilter()));
            andConds.add(new Criteria().orOperator(orOperator.toArray(new Criteria[orOperator.size()])));
        }
        if (CollectionUtils.isNotEmpty(andConds)){
            criteria.andOperator(andConds.toArray(new Criteria[andConds.size()]));
        }
        list.add(Aggregation.match(criteria));
        list.add(Aggregation.lookup("sysDef", "prov.cid", "_id", "prov"));
        list.add(Aggregation.unwind("prov", true));
        if (prjDvtMgtVo.getRegionId() != null){
            list.add(Aggregation.match(Criteria.where("prov.cndtItems.cid").is(prjDvtMgtVo.getRegionId())));
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getEngDept())){
            list.add(Aggregation.match(Criteria.where("prov.cndtItems.name").regex(prjDvtMgtVo.getEngDept())));
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getBigRegionName())){
            list.add(Aggregation.match(Criteria.where("prov.cndtItems").elemMatch(Criteria.where("codeName")
                    .is("abpBigRegion").and("name").regex(prjDvtMgtVo.getBigRegionName()))));
        }
        list.add(Aggregation.project()
                .andInclude("sbuId")
                .andInclude("prjId")
                .andInclude("prjName")
                .andInclude("prjCode")
                .andInclude("netSaleAmt")
                .andInclude("prov")
                .andInclude("pmUser")
                .andInclude("psoSm")
                .andInclude("trackUser")
                .andInclude("level")
                .andInclude("addTime")
                .andInclude("subPrjs")
                .andInclude("projectStatusPom")
                .andInclude("actualOmDate")
                .andExpression("status.name").as("statusName")
                .andExpression("{\"$first\":\"$prjBmks\"}").as("maxPrjBmk")
                .andExpression("{\"$last\":\"$prjBmks\"}").as("lastPrjBmk")
        );
        list.add(Aggregation.addFields()
                .addField("subPrjs").withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\"$eq\":{{\"$size\":{\"$ifNull\":{\"$subPrjs\",{}}}},0}},\n" +
                        "\tnull,\n" +
                        "\t{\"$filter\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\n" +
                        "\t\t\t\"$and\":{\n" +
                        "\t\t\t\t{\"$ne\":{\"$$item.isOrder\", true}}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}}\n" +
                        "}}")
                .addField("subOrders").withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\"$eq\":{{\"$size\":{\"$ifNull\":{\"$subPrjs\",{}}}},0}},\n" +
                        "\tnull,\n" +
                        "\t{\"$filter\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\n" +
                        "\t\t\t\"$and\":{\n" +
                        "\t\t\t\t{\"$eq\":{\"$$item.isOrder\",true}}\n" +
                        "\t\t\t }\n" +
                        "\t\t}\n" +
                        "\t}}\n" +
                        "}}")
                .build()
        );
        list.add(Aggregation.addFields()
                .addField("bmkPubTime").withValueOfExpression("maxPrjBmk.approveTime")
                .addField("bmkGm").withValueOfExpression("lastPrjBmk.gm")
                .addField("subPrjCode").withValueOfExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$subPrjs\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t\"$$this.code\",\n" +
                        "\t\t\t\t\",\"\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        if (StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeStart()) && StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeEnd())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeStart(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeEnd(), DateUtil.DATE_FORMAT));
            list.add(Aggregation.match(Criteria.where("bmkPubTime").gte(startDayByDate).lte(lastDayByDate)));
        }
        list.add(Aggregation.lookup("abpOop", "subPrjs.code", "codeName", "abpOop"));
        list.add(Aggregation.addFields()
                .addField("abpOop").withValueOfExpression("{\n" +
                        "\t\"$filter\":{\n" +
                        "\t\t\"input\":\"$abpOop\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\"$and\":{\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isValid\", true}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isDeleted\", false}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.buCode\", \"$sbuId\"}}\n" +
                        "\t\t}}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        list.add(Aggregation.unwind("abpOop", true));
        list.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "abpOop.addTime")));
        list.add(Aggregation.group("abpOop.codeName", "abpOop.buCode")
                .first("sbuId").as("sbuId")
                .first("prjId").as("prjId")
                .first("prjName").as("prjName")
                .first("prjCode").as("prjCode")
                .first("netSaleAmt").as("netSaleAmt")
                .first("statusName").as("statusName")
                .first("prov").as("prov")
                .first("pmUser").as("pmUser")
                .first("psoSm").as("psoSm")
                .first("trackUser").as("trackUser")
                .first("level").as("level")
                .first("actualOmDate").as("actualOmDate")
                .first("projectStatusPom").as("projectStatusPom")
                .first("addTime").as("addTime")
                .first("bmkPubTime").as("bmkPubTime")
                .first("bmkGm").as("bmkGm")
                .first("subPrjCode").as("subPrjCode")
                .first("subOrders").as("subOrders")
                .first("abpOop").as("abpOop")
        );
        list.add(Aggregation.group("prjId")
                .first("sbuId").as("sbuId")
                .first("prjId").as("prjId")
                .first("prjName").as("prjName")
                .first("prjCode").as("prjCode")
                .first("netSaleAmt").as("netSaleAmt")
                .first("statusName").as("statusName")
                .first("prov").as("prov")
                .first("actualOmDate").as("actualOmDate")
                .first("projectStatusPom").as("projectStatusPom")
                .first("pmUser").as("pmUser")
                .first("psoSm").as("psoSm")
                .first("trackUser").as("trackUser")
                .first("level").as("level")
                .first("addTime").as("addTime")
                .first("bmkPubTime").as("bmkPubTime")
                .first("bmkGm").as("bmkGm")
                .first("subPrjCode").as("subPrjCode")
                .first("subOrders.code").as("subOrders")
                .push("abpOop.linkedOrderCode").as("linkedOrderCodes")
        );
        list.add(Aggregation.addFields()
                .addField("subOrders").withValueOfExpression("{ \"$concatArrays\": {\"$subOrders\", \"$linkedOrderCodes\"} }")
                .build()
        );
        list.add(Aggregation.addFields()
                .addField("subOrderCode").withValueOfExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$subOrders\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t\"$$this\",\n" +
                        "\t\t\t\t\",\"\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getSubPrjCode())){
            list.add(Aggregation.match(Criteria.where("subPrjCode").regex(prjDvtMgtVo.getSubPrjCode())));
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getOrderCode())){
            list.add(Aggregation.match(Criteria.where("subOrderCode").regex(prjDvtMgtVo.getOrderCode())));
        }
        list.add(Aggregation.sort(Sort.by(Sort.Direction.ASC, "prov.defNo")));
        if (prjDvtMgtVo.getTypeId() != null){
            list.add(Aggregation.lookup("prjDvtMgt", "prjId", "prj.cid", "prjDvtMgt"));
            list.add(Aggregation.unwind("prjDvtMgt", false));
        }
        Criteria prjDvtMgtMatch = Criteria.where("prjDvtMgt.isValid").is(true)
                .and("prjDvtMgt.ym").is(prjDvtMgtVo.getYm()).and("prjDvtMgt.type.cid").is(prjDvtMgtVo.getTypeId());
        if (StringUtil.isNotNull(prjDvtMgtVo.getDesc())){
            prjDvtMgtMatch.and("prjDvtMgt.desc").regex(prjDvtMgtVo.getDesc());
        } else if (StringUtil.isNotNull(prjDvtMgtVo.getIsFillIn())){
            if(PrjConstant.DEF_YES.equals(prjDvtMgtVo.getIsFillIn())){
                prjDvtMgtMatch.and("prjDvtMgt.desc").ne(null).and("prjDvtMgt.rectifyAction").ne(null).and("prjDvtMgt.rectifyEndDate").ne(null);
            }else{
                prjDvtMgtMatch.orOperator(Criteria.where("prjDvtMgt.desc").is(null),
                        Criteria.where("prjDvtMgt.rectifyAction").is(null),Criteria.where("prjDvtMgt.rectifyEndDate").is(null));
            }
        }
        if(prjDvtMgtVo.getIsMailed() != null){
            if(prjDvtMgtVo.getIsMailed()){
                prjDvtMgtMatch.and("prjDvtMgt.isMailed").is(true);
            }else{
                prjDvtMgtMatch.and("prjDvtMgt.isMailed").ne(true);
            }
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getNotes())){
            prjDvtMgtMatch.and("prjDvtMgt.notes").regex(prjDvtMgtVo.getNotes());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getTaskNameFilter())){
            prjDvtMgtMatch.and("prjDvtMgt.rcd.name").regex(prjDvtMgtVo.getTaskNameFilter());
        }

        if (StringUtil.isNotNull(prjDvtMgtVo.getPlanEndTimeEnd()) && StringUtil.isNotNull(prjDvtMgtVo.getPlanEndTimeStart())){
            //获取一天的开始结束日期
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPlanEndTimeStart(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPlanEndTimeEnd(), DateUtil.DATE_FORMAT));
            prjDvtMgtMatch.and("prjDvtMgt.rcdInfo.ped").gte(startDayByDate).lt(lastDayByDate);
        }
        //原因大类
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getCauseTypeId()) && ObjectId.isValid(prjDvtMgtVo.getCauseTypeId())){
            prjDvtMgtMatch.and("prjDvtMgt.causeType.cid").is(StringUtil.toObjectId(prjDvtMgtVo.getCauseTypeId()));
        }else if(prjDvtMgtVo.getCauseTypeId() != null){
            prjDvtMgtMatch.and("prjDvtMgt.causeType.cid").is(null);
        }
        //原因小类
        if (StringUtil.isNotNull(prjDvtMgtVo.getCauseSubTypeId())){
            prjDvtMgtMatch.and("prjDvtMgt.causeSubType.cid").is(prjDvtMgtVo.getCauseSubTypeId());
        }
        if (prjDvtMgtVo.getCauseSub2TypeId() != null){
            prjDvtMgtMatch.and("prjDvtMgt.causeSub2Type.cid").is(prjDvtMgtVo.getCauseSub2TypeId());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getHasAnalysis())){
            List<Criteria> hasAnalysis = new ArrayList<>();
            if ("是".equals(prjDvtMgtVo.getHasAnalysis())){
                prjDvtMgtMatch.and("prjDvtMgt.causeType").ne(null).and("prjDvtMgt.causeSubType").ne(null).and("prjDvtMgt.causeSub2Type").ne(null);
            }else if ("否".equals(prjDvtMgtVo.getHasAnalysis())){
                List<Criteria> orOperator = new ArrayList<>();
                orOperator.add(Criteria.where("prjDvtMgt.causeType").is(null));
                orOperator.add(Criteria.where("prjDvtMgt.causeSubType").is(null));
                orOperator.add(Criteria.where("prjDvtMgt.causeSub2Type").is(null));
                prjDvtMgtMatch.orOperator(orOperator.toArray(new Criteria[orOperator.size()]));
            }
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getSymptom())){
            prjDvtMgtMatch.and("prjDvtMgt.symptom").regex(prjDvtMgtVo.getSymptom());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getMgtAction())){
            prjDvtMgtMatch.and("prjDvtMgt.rcdInfo.mgtAction").is(prjDvtMgtVo.getMgtAction());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getColorSign())){
            prjDvtMgtMatch.and("prjDvtMgt.colorSign").is(prjDvtMgtVo.getColorSign());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getRectifyStatus())){
            prjDvtMgtMatch.and("prjDvtMgt.rectifyStatus.name").regex(prjDvtMgtVo.getRectifyStatus());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getNeedCheck())){
            prjDvtMgtMatch.and("prjDvtMgt.rcdInfo.needCheck").regex(prjDvtMgtVo.getNeedCheck());
        }
        if (prjDvtMgtVo.getTypeId() != null){
            list.add(Aggregation.match(prjDvtMgtMatch));
        }
        GroupOperation count = Aggregation.group().count().as("count");
        list.add(Aggregation.sort(Sort.Direction.ASC,"prov.defNo","prjCode"));
        SkipOperation skip = null;
        if(prjDvtMgtVo.getPageSize() != null){
            skip = Aggregation.skip((prjDvtMgtVo.getPageIndex() == null ? 0L : (long) (prjDvtMgtVo.getPageIndex() * prjDvtMgtVo.getPageSize())));
            LimitOperation limit = Aggregation.limit(prjDvtMgtVo.getPageSize());
            list.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        }else{
            skip = Aggregation.skip((prjDvtMgtVo.getPageIndex() == null ? 0L : (long)prjDvtMgtVo.getPageIndex()));
            list.add(Aggregation.facet(count).as("count").and(skip).as("data"));
        }
        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return mongoTemplate.aggregate(aggregation, DBT.PRJINFO.n(), PrjDevMgtInfoBo.class).getUniqueMappedResult();
    }

    protected List<PrjDevMgtInfoForSubPrjBo> listPrjDevMgtInfoForSubPrj(PrjDvtMgtVo prjDvtMgtVo) {

        List<ObjectId> orderPrjTypeIds = Arrays.asList(AbpConstant.FORECAST_ORDER_ID, AbpConstant.FUTURE_FORECAST_ORDER_ID);
        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where("isValid").ne(false).and("isPrjSet").is(true);
//        criteria.and("prjCode").is("Other-HNQ-JS-1NJ224707A");
        List<Criteria> andConds = new ArrayList<>();
        List<Criteria> prjOrConds = new ArrayList<>();
        List<Criteria> andCond1 = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getPrjIds())){
            prjOrConds.add(Criteria.where("prjId").in(prjDvtMgtVo.getPrjIds()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getBuCodeList())){
            andCond1.add(Criteria.where("sbuId").in(prjDvtMgtVo.getBuCodeList()));
        }
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getProvIds())){
            andCond1.add(Criteria.where("prov.cid").in(prjDvtMgtVo.getProvIds()));
        }
        if (CollectionUtils.isNotEmpty(andCond1)){
            prjOrConds.add(new Criteria().andOperator(andCond1.toArray(new Criteria[andCond1.size()])));
        }
        if (CollectionUtils.isNotEmpty(prjOrConds)){
            andConds.add(new Criteria().orOperator(prjOrConds.toArray(new Criteria[prjOrConds.size()])));
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjCode())){
            criteria.and("prjCode").regex(prjDvtMgtVo.getPrjCode());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getPrjName())){
            criteria.and("prjName").regex(prjDvtMgtVo.getPrjName());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeStartDte()) && StringUtil.isNotNull(prjDvtMgtVo.getPrjAddTimeEndDte())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeStartDte(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPrjAddTimeEndDte(), DateUtil.DATE_FORMAT));
            criteria.and("addTime").gte(startDayByDate).lte(lastDayByDate);
        }
        List<Criteria> orOperator = new ArrayList<>();
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getTrackUserFilter())) {
            orOperator.add(Criteria.where("trackUser.loginName").regex(prjDvtMgtVo.getTrackUserFilter()));
            orOperator.add(Criteria.where("trackUser.userName").regex(prjDvtMgtVo.getTrackUserFilter()));
        }
        if (CollectionUtils.isNotEmpty(orOperator)){
            andConds.add(new Criteria().orOperator(orOperator.toArray(new Criteria[orOperator.size()])));
        }
        if (CollectionUtils.isNotEmpty(andConds)){
            criteria.andOperator(andConds.toArray(new Criteria[andConds.size()]));
        }
        list.add(Aggregation.match(criteria));
        list.add(Aggregation.lookup("sysDef", "prov.cid", "_id", "prov"));
        list.add(Aggregation.unwind("prov", true));
        if (prjDvtMgtVo.getRegionId() != null){
            list.add(Aggregation.match(Criteria.where("prov.cndtItems.cid").is(prjDvtMgtVo.getRegionId())));
        }
        list.add(Aggregation.unwind("subPrjs", false));
        list.add(Aggregation.lookup("prjInfo", "subPrjs.cid", "prjId", "subPrjs"));
        list.add(Aggregation.unwind("subPrjs", false));
        Criteria subPrjMatch = new Criteria();
        if(CollectionUtils.isNotEmpty(prjDvtMgtVo.getLevelIds())){
            subPrjMatch.and("subPrjs.level.cid").in(prjDvtMgtVo.getLevelIds());
        }
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getPmUserFilter())) {
            List<Criteria> subPrjOrOperator = new ArrayList<>();
            subPrjOrOperator.add(Criteria.where("subPrjs.pmUser.loginName").regex(prjDvtMgtVo.getPmUserFilter()));
            subPrjOrOperator.add(Criteria.where("subPrjs.pmUser.userName").regex(prjDvtMgtVo.getPmUserFilter()));
            subPrjMatch.orOperator(subPrjOrOperator.toArray(new Criteria[subPrjOrOperator.size()]));
        }
        if(prjDvtMgtVo.getProjectStatusPom() != null){
            subPrjMatch.and("subPrjs.projectStatusPom").regex(prjDvtMgtVo.getProjectStatusPom());
        }
        list.add(Aggregation.match(subPrjMatch));
        list.add(Aggregation.lookup("abpOop", "subPrjs.prjCode", "codeName", "abpOop"));
        list.add(Aggregation.addFields()
                .addField("abpOop").withValueOfExpression("{\n" +
                        "\t\"$filter\":{\n" +
                        "\t\t\"input\":\"$abpOop\",\n" +
                        "\t\t\"as\":\"item\",\n" +
                        "\t\t\"cond\":{\"$and\":{\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isValid\", true}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.isDeleted\", false}},\n" +
                        "\t\t\t{\"$eq\":{\"$$item.buCode\", \"$sbuId\"}}\n" +
                        "\t\t}}\n" +
                        "\t}\n" +
                        "}")
                .build()
        );
        list.add(Aggregation.unwind("abpOop", true));
        list.add(Aggregation.project()
                .andInclude("sbuId")
                .andInclude("prjId")
                .andInclude("prjName")
                .andInclude("prjCode")
                .andExpression("prov._id").as("provId")
                .andExpression("prov.defName").as("provName")
                .andExpression("prov.defNo").as("provDefNo")
                .andExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$prov.cndtItems\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t{\"$cond\": {\n" +
                        "\t\t\t\t   {\"$eq\": {\"$$this.codeName\", \"abpRegion\"}},\n" +
                        "\t\t\t\t   \"$$this.name\",\n" +
                        "\t\t\t\t   \"\"\n" +
                        "\t\t\t\t}}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}").as("regionName")
                .andExpression("{\n" +
                        "\t\"$reduce\":{\n" +
                        "\t\t\"input\":\"$prov.cndtItems\",\n" +
                        "\t\t\"initialValue\": \"\",\n" +
                        "\t\t\"in\": {\n" +
                        "\t\t\t\"$concat\": {\n" +
                        "\t\t\t\t\"$$value\",\n" +
                        "\t\t\t\t{\"$cond\": {\n" +
                        "\t\t\t\t   {\"$eq\": {\"$$this.codeName\", \"abpBigRegion\"}},\n" +
                        "\t\t\t\t   \"$$this.name\",\n" +
                        "\t\t\t\t   \"\"\n" +
                        "\t\t\t\t}}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}").as("bigRegionName")
                .andInclude("trackUser")
                .andInclude("addTime")
                .andExpression("{\"$first\":\"$prjBmks\"}").as("firstPrjBmk")
                .andExpression("pmUser").as("pmUser")
                .andExpression("level").as("level")
                .andExpression("subPrjs.prjId").as("subPrjId")
                .andExpression("{\"$cond\":{\n" +
                        "\t{\"$in\":{\"$abpOop.type.cid\", [0]}},\n" +
                        "\tnull,\n" +
                        "\t\"$subPrjs.prjCode\"\n" +
                        "}}", orderPrjTypeIds).as("subPrjCode")
                .andExpression("subPrjs.prjName").as("subPrjName")
                .andExpression("subPrjs.projectStatusPom").as("subPomStatus")
                .andExpression("{\"$cond\":{\n" +
                        "\t{\"$in\":{\"$abpOop.type.cid\", [0]}},\n" +
                        "\t\"$abpOop.codeName\",\n" +
                        "\t\"$abpOop.linkedOrderCode\"\n" +
                        "}}", orderPrjTypeIds).as("subOrderCode")
                .andExpression("abpOop.addTime").as("oopAddTime")
        );
        list.add(Aggregation.addFields()
                .addField("bmkPubTime").withValueOfExpression("firstPrjBmk.approveTime")
                .build()
        );

        Criteria match = new Criteria();
        if (StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeStart()) && StringUtil.isNotNull(prjDvtMgtVo.getApproveTimeEnd())){
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeStart(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getApproveTimeEnd(), DateUtil.DATE_FORMAT));
            match.and("bmkPubTime").gte(startDayByDate).lte(lastDayByDate);
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getSubPrjCode())){
            match.and("subPrjCode").regex(prjDvtMgtVo.getSubPrjCode());
        }
        if(StringUtils.isNotEmpty(prjDvtMgtVo.getOrderCode())){
            match.and("subOrderCode").regex(prjDvtMgtVo.getOrderCode());
        }
        list.add(Aggregation.match(match));

        list.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "oopAddTime")));
        list.add(Aggregation.group("subPrjId")
                .first("sbuId").as("sbuId")
                .first("prjId").as("prjId")
                .first("prjName").as("prjName")
                .first("prjCode").as("prjCode")
                .first("provId").as("provId")
                .first("provName").as("provName")
                .first("provDefNo").as("provDefNo")
                .first("regionName").as("regionName")
                .first("bigRegionName").as("bigRegionName")
                .first("trackUser").as("trackUser")
                .first("addTime").as("addTime")
                .first("bmkPubTime").as("bmkPubTime")
                .first("pmUser").as("pmUser")
                .first("level").as("level")
                .first("subPrjId").as("subPrjId")
                .first("subPrjCode").as("subPrjCode")
                .first("subPrjName").as("subPrjName")
                .first("subPomStatus").as("subPomStatus")
                .first("subOrderCode").as("subOrderCode")
        );
        list.add(Aggregation.sort(Sort.by(Sort.Direction.ASC, "provDefNo")));
        list.add(Aggregation.lookup("prjDvtMgt", "subPrjId", "prj.cid", "prjDvtMgt"));
        list.add(Aggregation.unwind("prjDvtMgt", false));
        Criteria prjDvtMgtMatch = Criteria.where("prjDvtMgt.isValid").is(true)
                .and("prjDvtMgt.ym").is(prjDvtMgtVo.getYm()).and("prjDvtMgt.type.cid").is(prjDvtMgtVo.getTypeId());
        if (StringUtil.isNotNull(prjDvtMgtVo.getDesc())){
            prjDvtMgtMatch.and("prjDvtMgt.desc").regex(prjDvtMgtVo.getDesc());
        } else if (StringUtil.isNotNull(prjDvtMgtVo.getIsFillIn())){
            if(PrjConstant.DEF_YES.equals(prjDvtMgtVo.getIsFillIn())){
                prjDvtMgtMatch.and("prjDvtMgt.desc").ne(null);
            }else{
                prjDvtMgtMatch.and("prjDvtMgt.desc").is(null);
            }
        }
        if(prjDvtMgtVo.getIsMailed() != null){
            if(prjDvtMgtVo.getIsMailed()){
                prjDvtMgtMatch.and("prjDvtMgt.isMailed").is(true);
            }else{
                prjDvtMgtMatch.and("prjDvtMgt.isMailed").ne(true);
            }
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getNotes())){
            prjDvtMgtMatch.and("prjDvtMgt.notes").regex(prjDvtMgtVo.getNotes());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getTaskNameFilter())){
            prjDvtMgtMatch.and("prjDvtMgt.rcd.name").regex(prjDvtMgtVo.getTaskNameFilter());
        }

        if (StringUtil.isNotNull(prjDvtMgtVo.getPlanEndTimeEnd()) && StringUtil.isNotNull(prjDvtMgtVo.getPlanEndTimeStart())){
            //获取一天的开始结束日期
            Date startDayByDate = DateUtil.getStartDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPlanEndTimeStart(), DateUtil.DATE_FORMAT));
            Date lastDayByDate = DateUtil.getLastDayByDate(DateUtil.parseDate(prjDvtMgtVo.getPlanEndTimeEnd(), DateUtil.DATE_FORMAT));
            prjDvtMgtMatch.and("prjDvtMgt.rcdInfo.ped").gte(startDayByDate).lt(lastDayByDate);
        }
        //原因大类
        if (StringUtils.isNotEmpty(prjDvtMgtVo.getCauseTypeId()) && ObjectId.isValid(prjDvtMgtVo.getCauseTypeId())){
            prjDvtMgtMatch.and("prjDvtMgt.causeType.cid").is(StringUtil.toObjectId(prjDvtMgtVo.getCauseTypeId()));
        }else if(prjDvtMgtVo.getCauseTypeId() != null){
            prjDvtMgtMatch.and("prjDvtMgt.causeType.cid").is(null);
        }
        //原因小类
        if (StringUtil.isNotNull(prjDvtMgtVo.getCauseSubTypeId())){
            prjDvtMgtMatch.and("prjDvtMgt.causeSubType.cid").is(prjDvtMgtVo.getCauseSubTypeId());
        }
        if (prjDvtMgtVo.getCauseSub2TypeId() != null){
            prjDvtMgtMatch.and("prjDvtMgt.causeSub2Type.cid").is(prjDvtMgtVo.getCauseSub2TypeId());
        }
        if (StringUtil.isNotNull(prjDvtMgtVo.getSymptom())){
            prjDvtMgtMatch.and("prjDvtMgt.symptom").regex(prjDvtMgtVo.getSymptom());
        }
        //人力偏差情况
        if (StringUtil.isNotNull(prjDvtMgtVo.getHrDvtDesc())){
            prjDvtMgtMatch.and("prjDvtMgt.rcdInfo.hrDvtDesc").is(prjDvtMgtVo.getHrDvtDesc());
        }
        list.add(Aggregation.match(prjDvtMgtMatch));
        GroupOperation count = Aggregation.group().count().as("count");
        SkipOperation skip = Aggregation.skip((prjDvtMgtVo.getPageIndex() == null ? 0L : (long) prjDvtMgtVo.getPageIndex()));
        if(prjDvtMgtVo.getPageSize() != null){
            LimitOperation limit = Aggregation.limit(prjDvtMgtVo.getPageSize());
            list.add(Aggregation.facet(count).as("count").and(skip, limit).as("data"));
        }else{
            list.add(Aggregation.facet(count).as("count").and(skip).as("data"));
        }

        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        return mongoTemplate.aggregate(aggregation, DBT.PRJINFO.n(), PrjDevMgtInfoForSubPrjBo.class).getMappedResults();

    }
    private PrjDvtMgtMailRcdInfoVo convertMailInfos(TePrjInfo prjInfo, PrjDvtMgt dvtMgt){
        PrjDvtMgtMailRcdInfoVo rcdInfoVo = new PrjDvtMgtMailRcdInfoVo();
        rcdInfoVo.setPrjCode(prjInfo.getPrjCode());
        rcdInfoVo.setPrjName(prjInfo.getPrjName());
        StringBuilder subPrjCode = new StringBuilder();
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isNotEmpty(subPrjs)){
            for (TePrjInfoSubPrj subPrj : subPrjs){
                if (StringUtil.isNotNull(subPrj.getCode())){
                    subPrjCode.append(subPrj.getCode()+"、");
                }
            }
        }
        if (StringUtil.isNotNull(subPrjCode)){
            subPrjCode = subPrjCode.deleteCharAt(subPrjCode.lastIndexOf("、"));
        }
        rcdInfoVo.setSubPrjCode(subPrjCode.toString());
        rcdInfoVo.setPmUser(prjInfo.getPmUser() == null ? "" : StringUtil.getNotNullStr(prjInfo.getPmUser().getUserName()));
        Date actualOmDate = prjInfo.getActualOmDate();
        rcdInfoVo.setActualOmDate(actualOmDate == null ? "" : DateUtil.formatDate2Str(actualOmDate,DateUtil.DATE_MONTH_FOTMAT2));
        String ym = dvtMgt.getYm();
        if (StringUtil.isNotNull(ym)){
            String[] split = ym.split("-");
            if (split.length == 2){
                rcdInfoVo.setYear(split[0]);
                rcdInfoVo.setMonth(split[1]);
            }
        }
        RcdInfo rcdInfo = dvtMgt.getRcdInfo();
        rcdInfoVo.setTyosManMonth(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTyosManMonth(),2));
        rcdInfoVo.setTyosDirectFee(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTyosDirectFee(),2));
        rcdInfoVo.setTyossSumFee(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTyossSumFee(),2));
        rcdInfoVo.setTmosManMonth(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTmosManMonth(),2));
        rcdInfoVo.setTmosDirectFee(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTmosDirectFee(),2));
        rcdInfoVo.setTmossSumFee(rcdInfo == null ? 0d : BigDecimalUtils.getDoubleHalfNum(rcdInfo.getTmossSumFee(),2));
        return rcdInfoVo;
    }
    protected PrjDvtMgtMailRcdInfoVo convertMailInfo(TePrjInfo prjInfo, PrjDvtMgt dvtMgt){
        PrjDvtMgtMailRcdInfoVo rcdInfoVo = new PrjDvtMgtMailRcdInfoVo();
        rcdInfoVo.setPrjCode(prjInfo.getPrjCode());
        rcdInfoVo.setPrjName(prjInfo.getPrjName());
        rcdInfoVo.setRcdName(dvtMgt.getRcd() == null ? "" : dvtMgt.getRcd().getName());
        RcdInfo rcdInfo = dvtMgt.getRcdInfo();
        if(dvtMgt.getRcdInfo() == null){
            rcdInfoVo.setPed(null);
            rcdInfoVo.setBmkFeeStandard(0d);
            rcdInfoVo.setActualFeeStandard(0d);
            rcdInfoVo.setDiff(0d);
            rcdInfoVo.setDiffRate(0d);

            rcdInfoVo.setBmkTravelFeeStandard(0d);
            rcdInfoVo.setActualTravelFeeStandard(0d);
            rcdInfoVo.setTravelDiff(0d);
            rcdInfoVo.setTravelDiffRate(0d);

            rcdInfoVo.setBmkDiningFeeStandard(0d);
            rcdInfoVo.setActualDiningFeeStandard(0d);
            rcdInfoVo.setDiningDiff(0d);
            rcdInfoVo.setDiningDiffRate(0d);

            rcdInfoVo.setBmkOtherFeeStandard(0d);
            rcdInfoVo.setActualOtherFeeStandard(0d);
            rcdInfoVo.setOtherDiff(0d);
            rcdInfoVo.setOtherDiffRate(0d);

            //基准累计人力资源（人月）
            rcdInfoVo.setBlAccuManMonth(0d);
            //实际累计人力资源（人月）
            rcdInfoVo.setActAccuManMonth(0d);
            //累计人力资源(实际VS基准)(实际-基准）
            rcdInfoVo.setAccuManMonthDiff(0d);
            //累计人力资源（偏差率）（实际-基准）/基准
            rcdInfoVo.setAccuManMonthDiffRate(0d);

            //基准累计差旅费（K）
            rcdInfoVo.setBlAccuTravelFee(0d);
            //实际累计差旅费（K）
            rcdInfoVo.setActAccuTravelFee(0d);
            //累计差旅费(实际VS基准)
            rcdInfoVo.setAccuTravelFeeDiff(0d);
            //累计差旅费（偏差率）
            rcdInfoVo.setAccuTravelFeeDiffRate(0d);

            //基准累计餐费（K）
            rcdInfoVo.setBlAccuDiningFee(0d);
            //实际累计餐费（K）
            rcdInfoVo.setActAccuDiningFee(0d);
            //累计餐费（实际VS基准）
            rcdInfoVo.setAccuDiningFeeDiff(0d);
            //累计餐费（偏差率）
            rcdInfoVo.setAccuDiningFeeDiffRate(0d);

            //基准累计其它费（K）
            rcdInfoVo.setBlAccuOtherFee(0d);
            //实际累计其它费（K）
            rcdInfoVo.setActAccuOtherFee(0d);
            //累计其它费（实际VS基准）
            rcdInfoVo.setAccuOtherFeeDiff(0d);
            //累计其它费（偏差率）
            rcdInfoVo.setAccuOtherFeeDiffRate(0d);

            //基准累计人月均差旅费（K）
            rcdInfoVo.setBlAccuTravelMonthAvgFee(0d);
            //实际累计人月均差旅费（K）
            rcdInfoVo.setActAccuTravelMonthAvgFee(0d);
            //累计人月均差旅费(实际VS基准)
            rcdInfoVo.setAccuTravelMonthAvgFeeDiff(0d);
            //累计人月均差旅费（偏差率）
            rcdInfoVo.setAccuTravelMonthAvgFeeDiffRate(0d);

            //基准累计人月均餐费（K）
            rcdInfoVo.setBlAccuDiningMonthAvgFee(0d);
            //实际累计人月均餐费（K）
            rcdInfoVo.setActAccuDiningMonthAvgFee(0d);
            //累计人月均餐费(实际VS基准)
            rcdInfoVo.setAccuDiningMonthAvgFeeDiff(0d);
            //累计人月均餐费（偏差率）
            rcdInfoVo.setAccuDiningMonthAvgFeeDiffRate(0d);

            //基准累计人月均其它费（K）
            rcdInfoVo.setBlAccuMonthAvgOtherFee(0d);
            //实际累计人月均其它费（K）
            rcdInfoVo.setActAccuOtherMonthAvgFee(0d);
            //累计人月均其它费(实际VS基准)
            rcdInfoVo.setAccuOtherMonthAvgFeeDiff(0d);
            //累计人月均其它费（偏差率）
            rcdInfoVo.setAccuOtherMonthAvgFeeDiffRate(0d);
        }else{
            rcdInfoVo.setPed(DateUtil.formatDate2Str(dvtMgt.getRcdInfo().getPed(),DateUtil.DATE_FORMAT));
            // 基准费用标准-全周期（元）：直接费用/人工费 (人工费 = 正式人工+外包人工+技术分包)
            double bmkFeeStandard = getFeeStandard(DoubleUtil.getNotNull(dvtMgt.getRcdInfo().getBlDirectFee()),
                    DoubleUtil.getNotNull(dvtMgt.getRcdInfo().getBlRgFee()));
            // 实际费用标准-截止统计月（元）：直接费用/人工费 人工费 = 正式人工+外包人工+技术分包)
            double actualFeeStandard = getFeeStandard(DoubleUtil.getNotNull(dvtMgt.getRcdInfo().getActDirectFee()),
                    DoubleUtil.getNotNull(dvtMgt.getRcdInfo().getActRgFee())+DoubleUtil.getNotNull(dvtMgt.getRcdInfo().getActTechSubFee()));
            // 差值（元）：
            double diff = actualFeeStandard - bmkFeeStandard;
            // 费用标准-偏差率：（实际-计划）/计划
            double diffRate = getDiffRate(bmkFeeStandard, actualFeeStandard);
            rcdInfoVo.setBmkFeeStandard(BigDecimalUtils.getDoubleHalfNum(bmkFeeStandard, 2));
            rcdInfoVo.setActualFeeStandard(BigDecimalUtils.getDoubleHalfNum(actualFeeStandard, 2));
            rcdInfoVo.setDiff(BigDecimalUtils.getDoubleHalfNum(diff, 2));
            rcdInfoVo.setDiffRate(BigDecimalUtils.getDoubleHalfNum(diffRate, 2));

            double bmkTravelFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getBlAccuTravelFee()), DoubleUtil.getNotNull(rcdInfo.getBlRgFee())+DoubleUtil.getNotNull(rcdInfo.getBlTechSubFee()));
            double actualTravelFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getActAccuTravelFee()), DoubleUtil.getNotNull(rcdInfo.getActRgFee()) + DoubleUtil.getNotNull(rcdInfo.getActTechSubFee()));
            double travelDiff = actualTravelFeeStandard - bmkTravelFeeStandard;
            double travelDiffRate = getDiffRate(bmkTravelFeeStandard, actualTravelFeeStandard);

            rcdInfoVo.setBmkTravelFeeStandard(BigDecimalUtils.getDoubleHalfNum(bmkTravelFeeStandard, 2));
            rcdInfoVo.setActualTravelFeeStandard(BigDecimalUtils.getDoubleHalfNum(actualTravelFeeStandard, 2));
            rcdInfoVo.setTravelDiff(BigDecimalUtils.getDoubleHalfNum(travelDiff, 2));
            rcdInfoVo.setTravelDiffRate(BigDecimalUtils.getDoubleHalfNum(travelDiffRate, 2));

            double bmkDiningFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getBlAccuDiningFee()), DoubleUtil.getNotNull(rcdInfo.getBlRgFee())+DoubleUtil.getNotNull(rcdInfo.getBlTechSubFee()));
            double actualDiningFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getActAccuDiningFee()), DoubleUtil.getNotNull(rcdInfo.getActRgFee()) + DoubleUtil.getNotNull(rcdInfo.getActTechSubFee()));
            double diningDiff = actualDiningFeeStandard - bmkDiningFeeStandard;
            double diningDiffRate = getDiffRate(bmkDiningFeeStandard, actualDiningFeeStandard);

            rcdInfoVo.setBmkDiningFeeStandard(BigDecimalUtils.getDoubleHalfNum(bmkDiningFeeStandard, 2));
            rcdInfoVo.setActualDiningFeeStandard(BigDecimalUtils.getDoubleHalfNum(actualDiningFeeStandard, 2));
            rcdInfoVo.setDiningDiff(BigDecimalUtils.getDoubleHalfNum(diningDiff, 2));
            rcdInfoVo.setDiningDiffRate(BigDecimalUtils.getDoubleHalfNum(diningDiffRate, 2));

            double bmkOtherFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getBlAccuOtherFee()), DoubleUtil.getNotNull(rcdInfo.getBlRgFee())+DoubleUtil.getNotNull(rcdInfo.getBlTechSubFee()));
            double actualOtherFeeStandard = getFeeStandard(DoubleUtil.getNotNull(rcdInfo.getActAccuOtherFee()), DoubleUtil.getNotNull(rcdInfo.getActRgFee()) + DoubleUtil.getNotNull(rcdInfo.getActTechSubFee()));
            double otherDiff = actualOtherFeeStandard - bmkOtherFeeStandard;
            double otherDiffRate = getDiffRate(bmkOtherFeeStandard, actualOtherFeeStandard);

            rcdInfoVo.setBmkOtherFeeStandard(BigDecimalUtils.getDoubleHalfNum(bmkOtherFeeStandard, 2));
            rcdInfoVo.setActualOtherFeeStandard(BigDecimalUtils.getDoubleHalfNum(actualOtherFeeStandard, 2));
            rcdInfoVo.setOtherDiff(BigDecimalUtils.getDoubleHalfNum(otherDiff, 2));
            rcdInfoVo.setOtherDiffRate(BigDecimalUtils.getDoubleHalfNum(otherDiffRate, 2));

            //基准累计人力资源（人月）
            rcdInfoVo.setBlAccuManMonth(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuManMonth(),2));
            //实际累计人力资源（人月）
            rcdInfoVo.setActAccuManMonth(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuManMonth(),2));
            //累计人力资源(实际VS基准)(实际-基准）
            double mixedEmpDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuManMonth(),rcdInfo.getBlAccuManMonth(),2);
            rcdInfoVo.setAccuManMonthDiff(mixedEmpDiff);
            //累计人力资源（偏差率）（实际-基准）/基准
            double mixedEmpDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuManMonth()) ?
                    (NumberUtils.equalZero(mixedEmpDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(mixedEmpDiff,rcdInfo.getBlAccuManMonth(),2);
            rcdInfoVo.setAccuManMonthDiffRate(mixedEmpDiffRate);

            //基准累计差旅费（K）
            rcdInfoVo.setBlAccuTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuTravelFee(),2));
            //实际累计差旅费（K）
            rcdInfoVo.setActAccuTravelFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuTravelFee(),2));
            //累计差旅费(实际VS基准)
            double travelFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuTravelFee(),rcdInfo.getBlAccuTravelFee(),2);
            rcdInfoVo.setAccuTravelFeeDiff(travelFeeDiff);
            //累计差旅费（偏差率）
            double travelFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuTravelFee()) ?
                    (NumberUtils.equalZero(travelFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(travelFeeDiff,rcdInfo.getBlAccuTravelFee(),2);
            rcdInfoVo.setAccuTravelFeeDiffRate(travelFeeDiffRate);

            //基准累计餐费（K）
            rcdInfoVo.setBlAccuDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuDiningFee(),2));
            //实际累计餐费（K）
            rcdInfoVo.setActAccuDiningFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuDiningFee(),2));
            //累计餐费（实际VS基准）
            double diningFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuDiningFee(),rcdInfo.getBlAccuDiningFee(),2);
            rcdInfoVo.setAccuDiningFeeDiff(diningFeeDiff);
            //累计餐费（偏差率）
            double diningFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuDiningFee()) ?
                    (NumberUtils.equalZero(diningFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(diningFeeDiff,rcdInfo.getBlAccuDiningFee(),2);
            rcdInfoVo.setAccuDiningFeeDiffRate(diningFeeDiffRate);

            //基准累计其它费（K）
            rcdInfoVo.setBlAccuOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getBlAccuOtherFee(),2));
            //实际累计其它费（K）
            rcdInfoVo.setActAccuOtherFee(BigDecimalUtils.getDoubleHalfNum(rcdInfo.getActAccuOtherFee(),2));
            //累计其它费（实际VS基准）
            double otherFeeDiff = BigDecimalUtils.subtractDouble(rcdInfo.getActAccuOtherFee(),rcdInfo.getBlAccuOtherFee(),2);
            rcdInfoVo.setAccuOtherFeeDiff(otherFeeDiff);
            //累计其它费（偏差率）
            double otherFeeDiffRate = NumberUtils.equalZeroOrNull(rcdInfo.getBlAccuOtherFee()) ?
                    (NumberUtils.equalZero(otherFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(otherFeeDiff,rcdInfo.getBlAccuOtherFee(),2);
            rcdInfoVo.setAccuOtherFeeDiffRate(otherFeeDiffRate);

            //基准累计人月均差旅费（K）
            double blAccuTravelMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuTravelFee() == null ? 0d : rcdInfo.getBlAccuTravelFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            rcdInfoVo.setBlAccuTravelMonthAvgFee(blAccuTravelMonthAvgFee);
            //实际累计人月均差旅费（K）
            double actAccuTravelMonthAvgFee =BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuTravelFee() == null ? 0d : rcdInfo.getActAccuTravelFee()*1000,rcdInfo.getActAccuManMonth(),2);
            rcdInfoVo.setActAccuTravelMonthAvgFee(actAccuTravelMonthAvgFee);
            //累计人月均差旅费(实际VS基准)
            double accuTravelMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuTravelMonthAvgFee,blAccuTravelMonthAvgFee,2);
            rcdInfoVo.setAccuTravelMonthAvgFeeDiff(accuTravelMonthAvgFeeDiff);
            //累计人月均差旅费（偏差率）
            double accuTravelMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuTravelMonthAvgFee) ?
                    (NumberUtils.equalZero(accuTravelMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuTravelMonthAvgFeeDiff,blAccuTravelMonthAvgFee,2);
            rcdInfoVo.setAccuTravelMonthAvgFeeDiffRate(accuTravelMonthAvgFeeDiffRate);

            //基准累计人月均餐费（K）
            double blAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuDiningFee() == null ? 0d : rcdInfo.getBlAccuDiningFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            rcdInfoVo.setBlAccuDiningMonthAvgFee(blAccuDiningMonthAvgFee);
            //实际累计人月均餐费（K）
            double actAccuDiningMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuDiningFee() == null ? 0d : rcdInfo.getActAccuDiningFee()*1000,rcdInfo.getActAccuManMonth(),2);
            rcdInfoVo.setActAccuDiningMonthAvgFee(actAccuDiningMonthAvgFee);
            //累计人月均餐费(实际VS基准)
            double accuDiningMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuDiningMonthAvgFee,blAccuDiningMonthAvgFee,2);
            rcdInfoVo.setAccuDiningMonthAvgFeeDiff(accuDiningMonthAvgFeeDiff);
            //累计人月均餐费（偏差率）
            double accuDiningMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuDiningMonthAvgFee) ?
                    (NumberUtils.equalZero(accuDiningMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuDiningMonthAvgFeeDiff,blAccuDiningMonthAvgFee,2);
            rcdInfoVo.setAccuDiningMonthAvgFeeDiffRate(accuDiningMonthAvgFeeDiffRate);

            //基准累计人月均其它费（K）
            double blAccuMonthAvgOtherFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getBlAccuOtherFee() == null ? 0d : rcdInfo.getBlAccuOtherFee()*1000,rcdInfo.getBlAccuManMonth(),2);
            rcdInfoVo.setBlAccuMonthAvgOtherFee(blAccuMonthAvgOtherFee);
            //实际累计人月均其它费（K）
            double actAccuOtherMonthAvgFee = BigDecimalUtils.divideDoubleWithZero(rcdInfo.getActAccuOtherFee() == null ? 0d : rcdInfo.getActAccuOtherFee()*1000,rcdInfo.getActAccuManMonth(),2);
            rcdInfoVo.setActAccuOtherMonthAvgFee(actAccuOtherMonthAvgFee);
            //累计人月均其它费(实际VS基准)
            double accuOtherMonthAvgFeeDiff = BigDecimalUtils.subtractDouble(actAccuOtherMonthAvgFee,blAccuMonthAvgOtherFee,2);
            rcdInfoVo.setAccuOtherMonthAvgFeeDiff(accuOtherMonthAvgFeeDiff);
            //累计人月均其它费（偏差率）
            double accuOtherMonthAvgFeeDiffRate = NumberUtils.equalZeroOrNull(blAccuMonthAvgOtherFee) ?
                    (NumberUtils.equalZero(accuOtherMonthAvgFeeDiff) ? 0d : 100d) : BigDecimalUtils.getDoubleDividePercent(accuOtherMonthAvgFeeDiff,blAccuMonthAvgOtherFee,2);
            rcdInfoVo.setAccuOtherMonthAvgFeeDiffRate(accuOtherMonthAvgFeeDiffRate);


        }
        return rcdInfoVo;
    }

    protected List<TePrjInfo> getPrjSetList(String sbuId, List<ObjectId> subPrjIds){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_E(DFN.prjInfo__isPrjSet,true));
//        conds.add(new DC_E(DFN.prjInfo__prjCode,"BOMC-BJQ-SX-1BJ225830B"));
        if (StringUtil.isNotNull(sbuId)){
            conds.add(new DC_E(DFN.prjInfo__sbuId,sbuId));
        }
        conds.add(new DC_E(DFN.prjInfo__prjId,null,true));
        conds.add(new DC_I<ObjectId>(DFN.prjInfo__status.dot(DFN.common_cid),Arrays.asList(SysDefConstants.PRJ_STATUS_SUSPEND_ID,PrjConstant.PRJ_STATUS_CANCEL_ID),true));
        conds.add(new DC_I<ObjectId>(DFN.prjInfo__subPrjs.dot(DFN.common_cid),subPrjIds));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.prjInfo__prjId);
        fieldNameList.add(DFN.prjInfo__subPrjs);
        fieldNameList.add(DFN.prjInfo__prjBmks);
        fieldNameList.add((DFN.prjInfo__prov));
        fieldNameList.add(DFN.prjInfo__bigRegion);
        fieldNameList.add(DFN.prjInfo__prjName);
        fieldNameList.add(DFN.prjInfo__prjCode);
        fieldNameList.add(DFN.prjInfo__level);
        fieldNameList.add(DFN.prjInfo__pmUser);
        fieldNameList.add(DFN.prjInfo__trackUser);
        fieldNameList.add(DFN.prjInfo__addTime);
        fieldNameList.add(DFN.prjInfo__status);
        List<TePrjInfo> prjInfoList = prjInfoDao.findByFieldAndConds(conds, fieldNameList);
        return prjInfoList;
    }


    protected List<PrjDvtMgt> listMailPrjDvtMgt(Map<ObjectId, List<PrjDvtMgt>> dvtPrjMap, TePrjInfo prjInfo){
        return dvtPrjMap.get(prjInfo.getPrjId());
    }

    protected Map<ObjectId, Map<String, Double>> getPrjMessyInfoMapByPrjIds(List<ObjectId> prjIdList, String bgtYm){
        if(CollectionUtils.isEmpty(prjIdList)){
            return Collections.emptyMap();
        }
        List<TePrjBudget> messyInfoList = prjBudgetService.getPrjBudgetsWithMessyInfo(prjIdList);
        return getPrjMessyInfoMap(messyInfoList, bgtYm);
    }

    protected Map<ObjectId, Map<String, Double>> getPrjMessyInfoMap(List<TePrjBudget> messyInfoList,String bgtYm){
        Map<ObjectId, Map<String, Double>> messyMap = new HashMap<>();
        //根据项目进行划分数据
        Map<ObjectId, List<TePrjBudget>> prjBudgetMap = messyInfoList.stream().collect(Collectors.groupingBy(TePrjBudget::getPrjId));
        for(ObjectId prjId : prjBudgetMap.keySet()) {
            double travelFee = 0d;
            double diningFee = 0d;
            double otherFee = 0d;
            double empMonth = 0d;
            double osEmpMonth = 0d;
            List<TePrjBudget> prjBudgetList = prjBudgetMap.get(prjId);
            if (CollectionUtils.isNotEmpty(prjBudgetList)){
                for (TePrjBudget prjBudget : prjBudgetList){
                    if(CollectionUtils.isNotEmpty(prjBudget.getMessyFilledInfo()) && bgtYm.compareTo(prjBudget.getYm()) >= 0) {
                        for(TePrjBudget2MessyFilledInfo messyInfo : prjBudget.getMessyFilledInfo()) {
                            travelFee += messyInfo.getTravelFee();
                            diningFee += messyInfo.getDiningFee();
                            otherFee += messyInfo.getOtherFee();
                            empMonth += messyInfo.getEmpNum();
                            osEmpMonth += messyInfo.getOsEmpNum();
                        }
                    }
                }
            }
            Map<String, Double> messyInfoMap = new HashMap<>();
            messyInfoMap.put("travelFee", travelFee);
            messyInfoMap.put("diningFee", diningFee);
            messyInfoMap.put("otherFee", otherFee);
            messyInfoMap.put("empMonth", empMonth);
            messyInfoMap.put("osEmpMonth", osEmpMonth);
            messyInfoMap.put("mixedEmpMonth", empMonth+osEmpMonth);
            messyMap.put(prjId, messyInfoMap);
        }
        return messyMap;
    }

    protected Map<ObjectId, Map<String, Map<String, Double>>> getPrjMessyInfoMapByPrjIds(List<ObjectId> prjIdList, String bgtYm, String bgtPreYm){
        if(CollectionUtils.isEmpty(prjIdList)){
            return Collections.emptyMap();
        }
        List<TePrjBudget> messyInfoList = prjBudgetService.getPrjBudgetsWithMessyInfo(prjIdList);
        return getPrjMessyInfoMap(messyInfoList, bgtYm, bgtPreYm);
    }
    protected Map<ObjectId, Map<String, Map<String, Double>>> getPrjMessyInfoMap(List<TePrjBudget> messyInfoList,String bgtYm,String bgtPreYm){
        Map<ObjectId, Map<String, Map<String, Double>>> messyMap = new HashMap<>();
        //根据项目进行划分数据
        Map<ObjectId, List<TePrjBudget>> prjBudgetMap = messyInfoList.stream().collect(Collectors.groupingBy(TePrjBudget::getPrjId));
        for(ObjectId prjId : prjBudgetMap.keySet()) {
            double travelFee = 0d;
            double diningFee = 0d;
            double otherFee = 0d;
            double empMonth = 0d;
            double osEmpMonth = 0d;
            double preTravelFee = 0d;
            double preDiningFee = 0d;
            double preOtherFee = 0d;
            double preEmpMonth = 0d;
            double preOsEmpMonth = 0d;
            List<TePrjBudget> prjBudgetList = prjBudgetMap.get(prjId);
            if (CollectionUtils.isNotEmpty(prjBudgetList)){
                for (TePrjBudget prjBudget : prjBudgetList){
                    if(CollectionUtils.isEmpty(prjBudget.getMessyFilledInfo())){
                        continue;
                    }
                    if(bgtYm.compareTo(prjBudget.getYm()) >= 0) {
                        for(TePrjBudget2MessyFilledInfo messyInfo : prjBudget.getMessyFilledInfo()) {
                            travelFee += messyInfo.getTravelFee();
                            diningFee += messyInfo.getDiningFee();
                            otherFee += messyInfo.getOtherFee();
                            empMonth += messyInfo.getEmpNum();
                            osEmpMonth += messyInfo.getOsEmpNum();
                        }
                    }
                    if(bgtPreYm.compareTo(prjBudget.getYm()) >= 0) {
                        for(TePrjBudget2MessyFilledInfo messyInfo : prjBudget.getMessyFilledInfo()) {
                            preTravelFee += messyInfo.getTravelFee();
                            preDiningFee += messyInfo.getDiningFee();
                            preOtherFee += messyInfo.getOtherFee();
                            preEmpMonth += messyInfo.getEmpNum();
                            preOsEmpMonth += messyInfo.getOsEmpNum();
                        }
                    }
                }
            }
            Map<String, Double> messyInfoMap = new HashMap<>();
            messyInfoMap.put("travelFee", travelFee);
            messyInfoMap.put("diningFee", diningFee);
            messyInfoMap.put("otherFee", otherFee);
            messyInfoMap.put("empMonth", empMonth);
            messyInfoMap.put("osEmpMonth", osEmpMonth);
            messyInfoMap.put("mixedEmpMonth", empMonth+osEmpMonth);
            Map<String, Double> preMessyInfoMap = new HashMap<>();
            preMessyInfoMap.put("travelFee", preTravelFee);
            preMessyInfoMap.put("diningFee", preDiningFee);
            preMessyInfoMap.put("otherFee", preOtherFee);
            preMessyInfoMap.put("empMonth", preEmpMonth);
            preMessyInfoMap.put("osEmpMonth", preOsEmpMonth);
            preMessyInfoMap.put("mixedEmpMonth", preEmpMonth+preOsEmpMonth);
            Map<String, Map<String, Double>> type2MessyMap = new HashMap<>();
            type2MessyMap.put("preYm", preMessyInfoMap);
            type2MessyMap.put("currentYm", messyInfoMap);
            messyMap.put(prjId, type2MessyMap);
        }
        return messyMap;
    }

    /**
     * 根据BU和省份取到最新的费率
     * @param subId
     * @param provIds
     * @return
     */
    protected Map<ObjectId, Map<ObjectId, Double>> getAbpFeeRate(String subId, List<ObjectId> provIds){
        List<IDbCondition> conds=new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysVerMgt__isLocked, true));
        conds.add(new DC_E(DbFieldName.sysVerMgt__verMgtType.dot(DbFieldName.common_cid), SysDefConstants.ABP_PLAN_FRATE_CONFIG_VER));
        conds.add(new DC_E(DbFieldName.sysVerMgt__srcDef.dot(DbFieldName.common_cn), subId));
        List<DbFieldName> fieldNames=new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_addTime);
        fieldNames.add(DbFieldName.sysVerMgt__srcDef);
        Sort sort = Sort.by(Sort.Direction.DESC, DbFieldName.common_addTime.n());
        List<TeSysVerMgt> verMgtList = sysVerMgtDao.findByFieldAndConds(conds, fieldNames, sort);
        if(CollectionUtils.isEmpty(verMgtList)){
            return Collections.emptyMap();
        }

        conds.clear();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.abpFrateCnfg__frateVer.dot(DFN.common_cid), verMgtList.get(0).getId()));
        conds.add(new DC_I<>(DFN.abpFrateCnfg__type.dot(DFN.common_cid),
                Arrays.asList(SysDefConstants.FEE_RATE_TYPE_ID_STATION_GO_BACK_FEE,SysDefConstants.FEE_RATE_TYPE_ID_LOCAL_TRAVEL_FEE)));
        conds.add(new DC_I<>(DFN.abpFrateCnfg__mainDef.dot(DFN.common_cid), provIds));
        fieldNames.clear();
        fieldNames.add(DFN.abpFrateCnfg__type);
        fieldNames.add(DFN.abpFrateCnfg__mainDef);
        fieldNames.add(DFN.abpFrateCnfg__value);
        List<AbpFrateCnfg> feeRateList = abpFrateCnfgDao.findByFieldAndConds(conds, fieldNames);
        if(CollectionUtils.isEmpty(feeRateList)){
            return Collections.emptyMap();
        }
        Map<ObjectId, Map<ObjectId, Double>> provId2TypeId2ValueMap = new HashMap<>();
        for (AbpFrateCnfg abpFrateCnfg : feeRateList) {
            ObjectId provId = abpFrateCnfg.getMainDef().getCid();
            ObjectId typeId = abpFrateCnfg.getType().getCid();
            double value = DoubleUtil.getNotNull(abpFrateCnfg.getValue());
            Map<ObjectId, Double> typeId2ValueMap = provId2TypeId2ValueMap.getOrDefault(provId, new HashMap<>());
            typeId2ValueMap.put(typeId, value);
            provId2TypeId2ValueMap.put(provId, typeId2ValueMap);
        }
        return provId2TypeId2ValueMap;
    }

    /**
     * 获取项目信息
     * @param prjCodeList
     * @param prjCode2PomStatusMap
     */
    protected void getSubPrjInfo(List<String> prjCodeList, Map<String, String> prjCode2PomStatusMap){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_I<>(DFN.prjInfo__prjCode, prjCodeList));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.prjInfo__prjCode);
        fieldNames.add(DFN.prjInfo__projectStatusPom);
        List<TePrjInfo> prjInfoList = prjInfoDao.findByFieldAndConds(conds, fieldNames);
        if(CollectionUtils.isEmpty(prjInfoList)){
            return;
        }
        for (TePrjInfo prjInfo : prjInfoList) {
            prjCode2PomStatusMap.put(prjInfo.getPrjCode(), prjInfo.getProjectStatusPom());
        }
    }

    /**
     * 根据BU和编码，查询项目编码和订单编码
     * @param sbuId
     * @param prjCodes
     * @param prjCode2OrderCodeMap
     * @return
     */
    protected List<String> listAbpCode(String sbuId, List<String> prjCodes, Map<String, String> prjCode2OrderCodeMap) {
        List<AggregationOperation> list = new ArrayList<>();
        list.add(Aggregation.match(Criteria.where("isValid").is(true).and("isDeleted").is(false)
                .and("buCode").is(sbuId).and("codeName").in(prjCodes)));
        list.add(Aggregation.project()
            .andInclude("codeName")
            .andInclude("linkedOrderCode")
            .andInclude("addTime")
        );
        list.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "addTime")));
        list.add(Aggregation.group("codeName")
            .first("codeName").as("codeName")
            .first("linkedOrderCode").as("linkedOrderCode")
        );
        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        Iterator<Map> cursor = mongoTemplate.aggregate(aggregation, DBT.ABP_OOP.n(), Map.class).iterator();
        List<String> prjCodeList = new ArrayList<>();
        while (cursor.hasNext()){
            Map result = cursor.next();
            String codeName = StringUtil.to(result.get("codeName"), String.class);
            String linkedOrderCode = StringUtil.to(result.get("linkedOrderCode"), String.class);
            if(StringUtils.isEmpty(codeName)){
                continue;
            }
            prjCodeList.add(codeName);
            if(StringUtils.isNotEmpty(linkedOrderCode)){
                prjCodeList.add(linkedOrderCode);
                prjCode2OrderCodeMap.put(codeName, linkedOrderCode);
            }
        }
        return prjCodeList;
    }

    protected Map<String, PrjAbpOopBgtBO> getAbpOopBgt(String sbuId, List<String> prjCodes, String ym,
                                                       Map<String, String> prjCode2PomStatusMap,
                                                       Map<String, String> prjCode2OrderCodeMap) {
        return getAbpOopBgt(sbuId, prjCodes, ym, prjCode2PomStatusMap, prjCode2OrderCodeMap, false);
    }

    /**
     * 获取经营计划的收入和成本信息
     * @param sbuId
     * @param prjCodes
     * @param ym
     * @param prjCode2PomStatusMap
     * @param prjCode2OrderCodeMap
     * @param isAllCycle
     * @return
     */
    protected Map<String, PrjAbpOopBgtBO> getAbpOopBgt(String sbuId, List<String> prjCodes, String ym,
                                                       Map<String, String> prjCode2PomStatusMap,
                                                       Map<String, String> prjCode2OrderCodeMap, Boolean isAllCycle) {
        List<AggregationOperation> list = new ArrayList<>();
        list.add(Aggregation.match(Criteria.where("buCode").is(sbuId)
                .and("planVer.cid").ne(null)
                .and("codeName").in(prjCodes)));
        list.add(Aggregation.lookup("sysVerMgt", "planVer.cid", "_id", "planVer"));
        list.add(Aggregation.unwind("planVer", false));
        list.add(Aggregation.project()
            .andExpression("_id").as("oopId")
            .andInclude("codeName", "isValid", "isDeleted")
            .andExpression("planVer.year").as("planVerYear")
            .andExpression("planVer.verNo").as("planVerNo")
            .andExpression("planVer._id").as("planVerId")
        );
        list.add(Aggregation.sort(Sort.by(Sort.Direction.DESC, "planVerYear", "planVerNo")));
        list.add(Aggregation.group("codeName")
                .first("isValid").as("isValid")
                .first("isDeleted").as("isDeleted")
                .first("oopId").as("oopId")
                .first("codeName").as("codeName")
                .first("planVerId").as("planVerId")
        );
        list.add(Aggregation.match(Criteria.where("isValid").is(true)
            .and("isDeleted").is(false)));
        //list.add(Aggregation.lookup("abpOopBgt", "oopId", "oop.cid", "abpOopBgt"));

        Document matchCondition = new Document("$expr",
                new Document("$and",Arrays.asList(
                        new Document("$eq", Arrays.asList("$oop.cid", "$$oop_id")),
                        new Document("$eq", Arrays.asList("$isValid", true)),
                        new Document("$eq", Arrays.asList(new Document("$ifNull",Arrays.asList("$pl", 0)),0))
                )));
        Document projectCondition = new Document("ym", 1);
        projectCondition.append("rgInfo", 1);
        projectCondition.append("isActualized", 1);
        projectCondition.append("income", 1);
        projectCondition.append("jslfbFee", 1);
        projectCondition.append("travelFee", 1);
        projectCondition.append("diningFee", 1);
        projectCondition.append("otherFee", 1);
        Document[] pipeline = {
                new Document("$match", matchCondition),
                new Document("$project", projectCondition)
        };
        list.add(LookupLetPipelinesOperation.lookup("abpOopBgt",
                new Document("oop_id","$oopId"),pipeline,"abpOopBgt"));
//        list.add(Aggregation.addFields()
//                .addField("abpOopBgt").withValueOfExpression("{\n" +
//                        "\t\"$filter\":{\n" +
//                        "\t\t\"input\":\"$abpOopBgt\",\n" +
//                        "\t\t\"as\":\"item\",\n" +
//                        "\t\t\"cond\":{\n" +
//                        "\t\t\t\"$and\": {\n" +
//                        "\t\t\t\t{\"$eq\":{\"$$item.isValid\", true}},\n" +
//                        "\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$item.pl\", 0}}, 0}}\n" +
//                        "\t\t\t}\n" +
//                        "\t\t}\n" +
//                        "\t}\n" +
//                        "}")
//                .build()
//        );
        list.add(Aggregation.unwind("abpOopBgt", true));
        list.add(Aggregation.addFields()
                .addField("rgInfo").withValueOfExpression("{\"$reduce\":{\n" +
                        "\t\"input\":\"$abpOopBgt.rgInfo\",\n" +
                        "\t\"initialValue\":{\"empNum\":0.0,\"osEmpNum\":0.0,\"empFee\":0.0,\"osEmpFee\":0.0,\"traineeFee\":0.0},\n" +
                        "\t\"in\": {\n" +
                        "\t\t\"empNum\":{\"$sum\": {                    \n" +
                        "\t\t\t\"$$value.empNum\",                    \n" +
                        "\t\t\t{\"$cond\":{                        \n" +
                        "\t\t\t\t{\"$and\":{                            \n" +
                        "\t\t\t\t\t{\"$eq\":{\"$$this.empType.cid\",[0]}},                            \n" +
                        "\t\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$this.resSrc.cid\", 0}}, 0}}                        \n" +
                        "\t\t\t\t}},                        \n" +
                        "\t\t\t\t\"$$this.rgNum\",                       \n" +
                        "\t\t\t\t0.0                    \n" +
                        "\t\t\t}}                \n" +
                        "\t\t}},\n" +
                        "\t\t\"osEmpNum\":{\"$sum\": {\n" +
                        "\t\t\t\"$$value.osEmpNum\",\n" +
                        "\t\t\t{\"$cond\":{\n" +
                        "\t\t\t\t{\"$and\":{\n" +
                        "\t\t\t\t\t{\"$eq\":{\"$$this.empType.cid\",[1]}},\n" +
                        "\t\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$this.resSrc.cid\", 0}}, 0}}\n" +
                        "\t\t\t\t}},\n" +
                        "\t\t\t\t\"$$this.rgNum\",\n" +
                        "\t\t\t\t0.0\n" +
                        "\t\t\t}}\n" +
                        "\t\t}},\n" +
                        "\t\t\"empFee\":{\"$sum\": {                    \n" +
                        "\t\t\t\"$$value.empFee\",                    \n" +
                        "\t\t\t{\"$cond\":{                        \n" +
                        "\t\t\t\t{\"$and\":{                            \n" +
                        "\t\t\t\t\t{\"$eq\":{\"$$this.empType.cid\",[2]}},                            \n" +
                        "\t\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$this.resSrc.cid\", 0}}, 0}}                        \n" +
                        "\t\t\t\t}},                        \n" +
                        "\t\t\t\t\"$$this.rgFee\",                       \n" +
                        "\t\t\t\t0.0                    \n" +
                        "\t\t\t}}                \n" +
                        "\t\t}},\n" +
                        "\t\t\"osEmpFee\":{\"$sum\": {\n" +
                        "\t\t\t\"$$value.osEmpFee\",\n" +
                        "\t\t\t{\"$cond\":{\n" +
                        "\t\t\t\t{\"$and\":{\n" +
                        "\t\t\t\t\t{\"$eq\":{\"$$this.empType.cid\",[3]}},\n" +
                        "\t\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$this.resSrc.cid\", 0}}, 0}}\n" +
                        "\t\t\t\t}},\n" +
                        "\t\t\t\t\"$$this.rgFee\",\n" +
                        "\t\t\t\t0.0\n" +
                        "\t\t\t}}\n" +
                        "\t\t}},\n" +
                        "\t\t\"traineeFee\":{\"$sum\": {\n" +
                        "\t\t\t\"$$value.traineeFee\",\n" +
                        "\t\t\t{\"$cond\":{\n" +
                        "\t\t\t\t{\"$and\":{\n" +
                        "\t\t\t\t\t{\"$eq\":{\"$$this.empType.cid\",[4]}},\n" +
                        "\t\t\t\t\t{\"$eq\":{{\"$ifNull\":{\"$$this.resSrc.cid\", 0}}, 0}}\n" +
                        "\t\t\t\t}},\n" +
                        "\t\t\t\t\"$$this.rgFee\",\n" +
                        "\t\t\t\t0.0\n" +
                        "\t\t\t}}\n" +
                        "\t\t}}\n" +
                        "\t}\n" +
                        "}}", SysDefConstants.REGULAR_EMPLOYEE_ID, SysDefConstants.OUTSOURCED_STAFF_ID,
                        SysDefConstants.REGULAR_EMPLOYEE_ID, SysDefConstants.OUTSOURCED_STAFF_ID,SysDefConstants.TRAINEE_ID)
                .build()
        );
        list.add(Aggregation.project()
                .andInclude("codeName")
                .andInclude("planVerId")
                .andExpression("abpOopBgt.ym").as("ym")
                .andExpression("abpOopBgt.isActualized").as("isActualized")
                .andExpression("abpOopBgt.income.amt").as("income")
                .andExpression("abpOopBgt.jslfbFee").as("jslfbFee")
                .andExpression("abpOopBgt.travelFee").as("travelFee")
                .andExpression("abpOopBgt.diningFee").as("diningFee")
                .andExpression("abpOopBgt.otherFee").as("otherFee")
                .andExpression("rgInfo.empNum").as("empNum")
                .andExpression("rgInfo.osEmpNum").as("osEmpNum")
                .andExpression("rgInfo.empFee").as("empFee")
                .andExpression("rgInfo.osEmpFee").as("osEmpFee")
                .andExpression("rgInfo.traineeFee").as("traineeFee")
        );

        List<AggregationOperation> actualList = new ArrayList<>();
        actualList.add(Aggregation.match(Criteria.where("ym").lte(ym).and("isActualized").is(true)));
        actualList.add(Aggregation.group("codeName")
                .first("codeName").as("codeName")
                .first("planVerId").as("planVerId")
                .sum("income").as("income")
                .sum("empNum").as("empNum")
                .sum("osEmpNum").as("osEmpNum")
                .sum("empFee").as("empFee")
                .sum("osEmpFee").as("osEmpFee")
                .sum("traineeFee").as("traineeFee")
                .sum("jslfbFee").as("jslfbFee")
                .sum("travelFee").as("travelFee")
                .sum("diningFee").as("diningFee")
                .sum("otherFee").as("otherFee")
        );

        List<AggregationOperation> allCycleList = new ArrayList<>();
        allCycleList.add(Aggregation.group("codeName")
                .first("codeName").as("codeName")
                .first("planVerId").as("planVerId")
                .sum("income").as("income")
                .sum("empNum").as("empNum")
                .sum("osEmpNum").as("osEmpNum")
                .sum("empFee").as("empFee")
                .sum("osEmpFee").as("osEmpFee")
                .sum("traineeFee").as("traineeFee")
                .sum("jslfbFee").as("jslfbFee")
                .sum("travelFee").as("travelFee")
                .sum("diningFee").as("diningFee")
                .sum("otherFee").as("otherFee")
        );
        list.add(Aggregation.facet(actualList.toArray(new AggregationOperation[actualList.size()])).as("actualList")
                .and(allCycleList.toArray(new AggregationOperation[allCycleList.size()])).as("allCycleList"));
        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        Iterator<PrjAbpOopBgtAllBO> cursor = mongoTemplate.aggregate(aggregation, DBT.ABP_OOP.n(), PrjAbpOopBgtAllBO.class).iterator();
        Map<String, PrjAbpOopBgtBO> prjCode2BgtMap = new HashMap<>();
        Map<String, Double> subPrjNetSaleAmtMap = getSubPrjNetSaleAmt(prjCodes);
        while (cursor.hasNext()){
            PrjAbpOopBgtAllBO allBO = cursor.next();
            List<PrjAbpOopBgtBO> allCycleBgtList = allBO.getAllCycleList();
            List<PrjAbpOopBgtBO> actualBgtList = allBO.getActualList();
            Map<String, PrjAbpOopBgtBO> prjCode2AllCycleBgtMap = allCycleBgtList.stream().collect(Collectors.toMap(s -> s.getCodeName(), Function.identity()));
            Map<String, PrjAbpOopBgtBO> prjCode2ActualBgtMap = actualBgtList.stream().collect(Collectors.toMap(s -> s.getCodeName(), Function.identity()));
            for (String codeName : prjCode2AllCycleBgtMap.keySet()) {
                String orderCode = prjCode2OrderCodeMap.get(codeName);
                String pomStatus = prjCode2PomStatusMap.get(codeName);
                PrjAbpOopBgtBO bgtBO = null;
                PrjAbpOopBgtBO orderBgtBO = null;
                if(BooleanUtils.isFalse(isAllCycle) &&
                        (PrjConstant.PROJECT_STATUS_NAME.equals(pomStatus)
                                || PrjConstant.PROJECT_STATUS_NAME_CLOSE.equals(pomStatus))){
                    bgtBO = prjCode2ActualBgtMap.get(codeName);
                    if (bgtBO != null){
                        if(StringUtils.isNotEmpty(orderCode)) {
                            orderBgtBO = prjCode2ActualBgtMap.get(orderCode);
                        }
                        sumPrjAndOrderBgt(bgtBO, orderBgtBO, true);
                    }
                }else{
                    bgtBO = prjCode2AllCycleBgtMap.get(codeName);
                    if (bgtBO != null){
                        if(StringUtils.isNotEmpty(orderCode)) {
                            orderBgtBO = prjCode2AllCycleBgtMap.get(orderCode);
                        }
                        double netSaleAmt = subPrjNetSaleAmtMap == null ? 0d : DoubleUtil.getNotNull(subPrjNetSaleAmtMap.get(codeName));
                        bgtBO.setIncome(netSaleAmt);
                        sumPrjAndOrderBgt(bgtBO, orderBgtBO, false);
                    }
                }
                prjCode2BgtMap.put(codeName, bgtBO);
            }
        }
        return prjCode2BgtMap;
    }
    protected Map<String,Double> getSubPrjNetSaleAmt(List<String> prjCodes){
        Map<String,Double> result = new HashMap<>();
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_E(DFN.prjInfo__isPrjSet,true));
        conds.add(new DC_I<String>(DFN.prjInfo__subPrjs.dot(DFN.common_code),prjCodes));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.prjInfo__subPrjs);
        List<TePrjInfo> prjInfos = prjInfoDao.findByFieldAndConds(conds, fieldNameList);
        if (CollectionUtils.isNotEmpty(prjInfos)){
            result = prjInfos.stream().map(TePrjInfo::getSubPrjs).flatMap(Collection::stream).filter(sub -> sub.getNetSaleAmt() != null)
                    .collect(Collectors.toMap(TePrjInfoSubPrj::getCode,TePrjInfoSubPrj::getNetSaleAmt,(v1,v2) -> v2));
        }
        return result;
    }
    /**
     * 合计项目和订单的值
     * @param bgtBO
     * @param orderBgtBO
     */
    private void sumPrjAndOrderBgt(PrjAbpOopBgtBO bgtBO, PrjAbpOopBgtBO orderBgtBO, Boolean isCalcIncome) {
        if(orderBgtBO != null && bgtBO.getPlanVerId().equals(orderBgtBO.getPlanVerId())){
            if(BooleanUtils.isTrue(isCalcIncome)) {
                bgtBO.setIncome(DoubleUtil.getNotNull(bgtBO.getIncome()) + DoubleUtil.getNotNull(orderBgtBO.getIncome()));
            }
            bgtBO.setEmpNum(DoubleUtil.getNotNull(bgtBO.getEmpNum()) + DoubleUtil.getNotNull(orderBgtBO.getEmpNum()));
            bgtBO.setOsEmpNum(DoubleUtil.getNotNull(bgtBO.getOsEmpNum()) + DoubleUtil.getNotNull(orderBgtBO.getOsEmpNum()));
            bgtBO.setEmpFee(DoubleUtil.getNotNull(bgtBO.getEmpFee()) + DoubleUtil.getNotNull(orderBgtBO.getEmpFee()));
            bgtBO.setOsEmpFee(DoubleUtil.getNotNull(bgtBO.getOsEmpFee()) + DoubleUtil.getNotNull(orderBgtBO.getOsEmpFee()));
            bgtBO.setTraineeFee(DoubleUtil.getNotNull(bgtBO.getTraineeFee()) + DoubleUtil.getNotNull(orderBgtBO.getTraineeFee()));
            bgtBO.setJslfbFee(DoubleUtil.getNotNull(bgtBO.getJslfbFee()) + DoubleUtil.getNotNull(orderBgtBO.getJslfbFee()));
            bgtBO.setTravelFee(DoubleUtil.getNotNull(bgtBO.getTravelFee()) + DoubleUtil.getNotNull(orderBgtBO.getTravelFee()));
            bgtBO.setDiningFee(DoubleUtil.getNotNull(bgtBO.getDiningFee()) + DoubleUtil.getNotNull(orderBgtBO.getDiningFee()));
            bgtBO.setOtherFee(DoubleUtil.getNotNull(bgtBO.getOtherFee()) + DoubleUtil.getNotNull(orderBgtBO.getOtherFee()));
        }
        bgtBO.setAllCostFee(DoubleUtil.getNotNull(bgtBO.getEmpFee())
                + DoubleUtil.getNotNull(bgtBO.getOsEmpFee())
                + DoubleUtil.getNotNull(bgtBO.getTraineeFee())
                + DoubleUtil.getNotNull(bgtBO.getJslfbFee())
                + DoubleUtil.getNotNull(bgtBO.getTravelFee())
                + DoubleUtil.getNotNull(bgtBO.getDiningFee())
                + DoubleUtil.getNotNull(bgtBO.getOtherFee())
        );
    }

    /**
     * 计算ABCGH和EF项目的值
     * @param bgtBo
     * @param prjId2MessyInfoMap
     * @param provId2TypeId2ValueMap
     * @param prjCode2BgtMap
     * @return
     */
    protected PrjCostDvtBgtBo calcBgtInfo(PrjCostDeviationEfBgtBo bgtBo,
                                          Map<ObjectId, Map<String, Map<String, Double>>> prjId2MessyInfoMap,
                                          Map<ObjectId, Map<ObjectId, Double>> provId2TypeId2ValueMap,
                                          Map<String, PrjAbpOopBgtBO> prjCode2BgtMap,
                                          Map<String, Double> subPrjNetSaleAmtMap,
                                          Map<String, String> prjCode2PomStatusMap
    ) {
        ObjectId prjId = bgtBo.getPrjId();
        ObjectId provId = bgtBo.getProvId();
        List<String> subPrjCodeList = bgtBo.getSubPrjs();

        double abpAllCostFee = 0d;
        double abpIncome = 0d;
        if (CollectionUtils.isNotEmpty(subPrjCodeList) && MapUtils.isNotEmpty(prjCode2BgtMap)) {
            for (String subPrjCode : subPrjCodeList) {
                String pomStatus = prjCode2PomStatusMap.get(subPrjCode);
                if (!PrjConstant.PROJECT_STATUS_NAME.equals(pomStatus) && !PrjConstant.PROJECT_STATUS_NAME_CLOSE.equals(pomStatus)){
                    abpIncome += DoubleUtil.getNotNull(subPrjNetSaleAmtMap.get(subPrjCode));
                }
                PrjAbpOopBgtBO abpBgtBO = prjCode2BgtMap.get(subPrjCode);
                if (abpBgtBO != null) {
                    abpAllCostFee += DoubleUtil.getNotNull(abpBgtBO.getAllCostFee());
                    if (PrjConstant.PROJECT_STATUS_NAME.equals(pomStatus) || PrjConstant.PROJECT_STATUS_NAME_CLOSE.equals(pomStatus)){
                        abpIncome += DoubleUtil.getNotNull(abpBgtBO.getIncome());
                    }
                }
            }
        }

        // 费率表中配置的人月均差旅交通费、人月均市内交通费
        double sumTravelCarFeePem = 0d;
        double sumCityCarFeePerEmp = 0d;
        if (MapUtils.isNotEmpty(provId2TypeId2ValueMap) && MapUtils.isNotEmpty(provId2TypeId2ValueMap.get(provId))) {
            Map<ObjectId, Double> typeId2ValueMap = provId2TypeId2ValueMap.get(provId);
            sumTravelCarFeePem = DoubleUtil.getNotNull(typeId2ValueMap.get(SysDefConstants.FEE_RATE_TYPE_ID_STATION_GO_BACK_FEE));
            sumCityCarFeePerEmp = DoubleUtil.getNotNull(typeId2ValueMap.get(SysDefConstants.FEE_RATE_TYPE_ID_LOCAL_TRAVEL_FEE));
        }

        // 项目集拆分费用
        double messyTravelFee = 0d;
        double messyDiningFee = 0d;
        double messyOtherFee = 0d;
        double messyEmpMonth = 0d;
        double messyOsEmpMonth = 0d;
        double messyMixedEmpMonth = 0d;
        double preMessyTravelFee = 0d;
        double preMessyDiningFee = 0d;
        double preMessyOtherFee = 0d;
        double preMessyEmpMonth = 0d;
        double preMessyOsEmpMonth = 0d;
        double preMessyMixedEmpMonth = 0d;
        if(MapUtil.isNotEmpty(prjId2MessyInfoMap) && MapUtil.isNotEmpty(prjId2MessyInfoMap.get(prjId))){
            Map<String, Map<String, Double>> messyInfoMap = prjId2MessyInfoMap.get(prjId);
            Map<String, Double> preYmMessyInfoMap = messyInfoMap.get("preYm");
            Map<String, Double> currentYmMessyInfoMap = messyInfoMap.get("currentYm");
            messyTravelFee = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("travelFee"));
            messyDiningFee = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("diningFee"));
            messyOtherFee = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("otherFee"));
            messyEmpMonth = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("empMonth"));
            messyOsEmpMonth = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("osEmpMonth"));
            messyMixedEmpMonth = DoubleUtil.getNotNull(currentYmMessyInfoMap.get("mixedEmpMonth"));
            preMessyTravelFee = DoubleUtil.getNotNull(preYmMessyInfoMap.get("travelFee"));
            preMessyDiningFee = DoubleUtil.getNotNull(preYmMessyInfoMap.get("diningFee"));
            preMessyOtherFee = DoubleUtil.getNotNull(preYmMessyInfoMap.get("otherFee"));
            preMessyEmpMonth = DoubleUtil.getNotNull(preYmMessyInfoMap.get("empMonth"));
            preMessyOsEmpMonth = DoubleUtil.getNotNull(preYmMessyInfoMap.get("osEmpMonth"));
            preMessyMixedEmpMonth = DoubleUtil.getNotNull(preYmMessyInfoMap.get("mixedEmpMonth"));
        }

        double actualAllEmpNumSubMessy = bgtBo.getActualAllEmpNum() - messyMixedEmpMonth;
        double actualEmpMonthSubMessy = bgtBo.getActualEmpMonth() - messyEmpMonth;
        double actualOsEmpMonthSubMessy = bgtBo.getActualOsEmpMonth() - messyOsEmpMonth;
        double actualDirectFeeSubMessy = bgtBo.getActualDirectFee() - messyTravelFee - messyDiningFee - messyOtherFee;
        double actualTravelFeeSubMessy = bgtBo.getActualTravelFee() - messyTravelFee;
        double actualDiningFeeSubMessy = bgtBo.getActualDiningFee() - messyDiningFee;
        double actualOtherFeeSubMessy = bgtBo.getActualOtherFee() - messyOtherFee;

        double preYmActualAllEmpNumSubMessy = bgtBo.getLastActualAllEmpMonth() - preMessyMixedEmpMonth;
        double preYmActualTravelFeeSubMessy = bgtBo.getLastActualTravelFee() - preMessyTravelFee;
        double preYmActualDiningFeeSubMessy = bgtBo.getLastActualDiningFee() - preMessyDiningFee;
        double preYmActualOtherFeeSubMessy = bgtBo.getLastActualOtherFee() - preMessyOtherFee;

        //累计基准差旅人月数
        double blTravelMm = bgtBo.getBlTravelMm();
        //累计实际差旅人月数
        double actTravelMm = bgtBo.getActTravelMm();
        //累计基准差旅住宿费
        double blTravelHotelFee = bgtBo.getBlTravelHotelFee();
        //累计实际差旅住宿费
        double actTravelHotelFee = bgtBo.getActTravelHotelFee();
        //累计基准差旅交通费
        double blTravelCityCarFee = bgtBo.getBlTravelCityCarFee();
        //累计实际差旅交通费
        double actTravelCityCarFee = bgtBo.getActTravelCityCarFee();
        //累计基准市内交通费
        double blCityCarFee = bgtBo.getBlCityCarFee();
        //累计实际市内交通费
        double actCityCarFee = bgtBo.getActCityCarFee();

        //累计人月均差旅住宿费：累计差旅住宿费/累计差旅人月数
        double blHumanMonthTravelHotelFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(blTravelHotelFee,blTravelMm,2);//累计基准人月均差旅住宿费
        double actHumanMonthTravelHotelFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(actTravelHotelFee,actTravelMm,2);//累计实际人月均差旅住宿费

        //累计人月均差旅交通费：累计差旅交通费/累计差旅人月数
        double blHumanMonthTravelCityCarFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(blTravelCityCarFee,blTravelMm,2);//累计基准人月均差旅交通费
        double actHumanMonthTravelCityCarFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(actTravelCityCarFee,actTravelMm,2);//累计实际人月均差旅交通费

        //累计人月均市内交通费：累计市内交通费/（累计总人月数-累计差旅人月数）
        double blHumanMonthCityCarFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(blCityCarFee,bgtBo.getSumAllEmpNum() - blTravelMm,2);//累计基准人月均市内交通费
        double actHumanMonthCityCarFeeAvgTotal = BigDecimalUtils.divideDoubleWithZero(actCityCarFee,actualAllEmpNumSubMessy - actTravelMm,2);//累计实际人月均市内交通费

        bgtBo.setActualGm(BigDecimalUtils.getDoubleHalfNum(getFeeStandard(abpIncome - abpAllCostFee, abpIncome), 2));
        bgtBo.setBlTravelHotelFee(blHumanMonthTravelHotelFeeAvgTotal);
        bgtBo.setActTravelHotelFee(actHumanMonthTravelHotelFeeAvgTotal);
        bgtBo.setSumTravelCarFeePem(blHumanMonthTravelCityCarFeeAvgTotal);
        bgtBo.setSumCityCarFeePerEmp(blHumanMonthCityCarFeeAvgTotal);
        bgtBo.setTravelCarFeePem(actHumanMonthTravelCityCarFeeAvgTotal);
        bgtBo.setCityCarFeePerEmp(actHumanMonthCityCarFeeAvgTotal);

        bgtBo.setActualAllEmpNumSubMessy(actualAllEmpNumSubMessy);
        bgtBo.setActualEmpMonthSubMessy(actualEmpMonthSubMessy);
        bgtBo.setActualOsEmpMonthSubMessy(actualOsEmpMonthSubMessy);
        bgtBo.setActualDirectFeeSubMessy(actualDirectFeeSubMessy);
        bgtBo.setActualTravelFeeSubMessy(actualTravelFeeSubMessy);
        bgtBo.setActualDiningFeeSubMessy(actualDiningFeeSubMessy);
        bgtBo.setActualOtherFeeSubMessy(actualOtherFeeSubMessy);

        bgtBo.setPreYmActualAllEmpNumSubMessy(preYmActualAllEmpNumSubMessy);
        bgtBo.setPreYmActualTravelFeeSubMessy(preYmActualTravelFeeSubMessy);
        bgtBo.setPreYmActualDiningFeeSubMessy(preYmActualDiningFeeSubMessy);
        bgtBo.setPreYmActualOtherFeeSubMessy(preYmActualOtherFeeSubMessy);
        return calcBgtInfo(bgtBo);
    }

    /**
     * 计算ABCGH和EF项目的值
     * @param bgtBo
     */
    protected PrjCostDvtBgtBo calcBgtInfo(PrjCostDeviationEfBgtBo bgtBo) {
        // 原始数据=======================================================================
        double forecastIncome = bgtBo.getForecastIncome();
        double sumAllEmpNum = bgtBo.getSumAllEmpNum();
        double sumEmpNum = bgtBo.getSumEmpNum();
        double osEmpNum = bgtBo.getOsEmpNum();
        double sumAllEmpFee = bgtBo.getSumAllEmpFee();
        double sumEmpFee = bgtBo.getSumEmpFee();
        double sumOsEmpFee = bgtBo.getSumOsEmpFee();
        double sumTraineeFee = bgtBo.getSumTraineeFee();
        double jslfbFee = bgtBo.getJslfbFee();
        double sumDirectFee = bgtBo.getSumDirectFee();
        double sumTravelFee = bgtBo.getSumTravelFee();
        double sumDiningFee = bgtBo.getSumDiningFee();
        double sumOtherFee = bgtBo.getSumOtherFee();
        double blTravelMmTotal = bgtBo.getBlTravelMm();

        double actualIncome = bgtBo.getActualIncome();
        double actualAllEmpNum = bgtBo.getActualAllEmpNumSubMessy();
        double actualEmpMonth = bgtBo.getActualEmpMonthSubMessy();
        double actualOsEmpMonth = bgtBo.getActualOsEmpMonthSubMessy();
        double actualAllEmpFee = bgtBo.getActualAllEmpFee();
        double actualEmpFee = bgtBo.getActualEmpFee();
        double actualOsEmpFee = bgtBo.getActualOsEmpFee();
        double actualTraineeFee = bgtBo.getActualTraineeFee();
        double actualJslfbFee = bgtBo.getActualJslfbFee();
        double actualDirectFee = bgtBo.getActualDirectFeeSubMessy();
        double actualTravelFee = bgtBo.getActualTravelFeeSubMessy();
        double actualDiningFee = bgtBo.getActualDiningFeeSubMessy();
        double actualOtherFee = bgtBo.getActualOtherFeeSubMessy();
        double actualServiceFee = bgtBo.getActServiceFee();
        double actTravelMmTotal = bgtBo.getActTravelMm();

        double lastSumAllEmpNum = bgtBo.getLastSumAllEmpNum();
        double lastSumTravelFee = bgtBo.getLastSumTravelFee();
        double lastSumDiningFee = bgtBo.getLastSumDiningFee();
        double lastSumOtherFee = bgtBo.getLastSumOtherFee();
        double lastActualAllEmpMonth = bgtBo.getPreYmActualAllEmpNumSubMessy();
        double lastActualTravelFee = bgtBo.getPreYmActualTravelFeeSubMessy();
        double lastActualDiningFee = bgtBo.getPreYmActualDiningFeeSubMessy();
        double lastActualOtherFee = bgtBo.getPreYmActualOtherFeeSubMessy();

        double sumGm = bgtBo.getSumGm();
        double actualGm = bgtBo.getActualGm();

        double blTravelHotelFeePem = bgtBo.getBlTravelHotelFee();
        double actTravelHotelFeePem = bgtBo.getActTravelHotelFee();
        double sumTravelCarFeePem = bgtBo.getSumTravelCarFeePem();
        double sumCityCarFeePerEmp = bgtBo.getSumCityCarFeePerEmp();
        double actualTravelCarFeePem = bgtBo.getTravelCarFeePem();
        double actualCityCarFeePerEmp = bgtBo.getCityCarFeePerEmp();
        // 计算之后的数据=======================================================================
        double bmkAllEmpNum = BigDecimalUtils.getDoubleHalfNum(sumAllEmpNum, 2);
        double bmkEmpNum = BigDecimalUtils.getDoubleHalfNum(sumEmpNum, 2);
        double bmkOsEmpNum = BigDecimalUtils.getDoubleHalfNum(osEmpNum, 2);
        double actAllEmpNum = BigDecimalUtils.getDoubleHalfNum(actualAllEmpNum, 2);
        double actEmpNum = BigDecimalUtils.getDoubleHalfNum(actualEmpMonth, 2);
        double actOsEmpNum = BigDecimalUtils.getDoubleHalfNum(actualOsEmpMonth, 2);
        double empNumDiff = BigDecimalUtils.getDoubleHalfNum(actAllEmpNum - bmkAllEmpNum, 2);
        double empNumDiffRate = getDiffRateByBigDecimal(empNumDiff, bmkAllEmpNum);

        double bmkAllEmpFee = BigDecimalUtils.getDoublePercentHalfNum(sumAllEmpFee, 2);
        double actAllEmpFee = BigDecimalUtils.getDoublePercentHalfNum(actualAllEmpFee, 2);
        double allEmpFeeDiff = BigDecimalUtils.getDoubleHalfNum(actAllEmpFee - bmkAllEmpFee, 2);
        double bmkEmpFee = BigDecimalUtils.getDoublePercentHalfNum(sumEmpFee, 2);
        double actEmpFee = BigDecimalUtils.getDoublePercentHalfNum(actualEmpFee, 2);
        double bmkOsEmpFee = BigDecimalUtils.getDoublePercentHalfNum(sumOsEmpFee, 2);
        double actOsEmpFee = BigDecimalUtils.getDoublePercentHalfNum(actualOsEmpFee, 2);
        double bmkTraineeFee = BigDecimalUtils.getDoublePercentHalfNum(sumTraineeFee, 2);
        double actTraineeFee = BigDecimalUtils.getDoublePercentHalfNum(actualTraineeFee, 2);
        double bmkJslfbFee = BigDecimalUtils.getDoublePercentHalfNum(jslfbFee, 2);
        double actJslfbFee = BigDecimalUtils.getDoublePercentHalfNum(actualJslfbFee, 2);

        double bmkDirectFee = BigDecimalUtils.getDoublePercentHalfNum(sumDirectFee, 2);
        double actDirectFee = BigDecimalUtils.getDoublePercentHalfNum(actualDirectFee, 2);
        double directFeeDiff = BigDecimalUtils.getDoubleHalfNum(actDirectFee - bmkDirectFee, 2);
        double directFeeDiffRate = getDiffRateByBigDecimal(directFeeDiff, bmkDirectFee);
        double bmkTravelFee = BigDecimalUtils.getDoublePercentHalfNum(sumTravelFee, 2);
        double actTravelFee = BigDecimalUtils.getDoublePercentHalfNum(actualTravelFee, 2);
        double travelFeeDiff = BigDecimalUtils.getDoubleHalfNum(actTravelFee - bmkTravelFee, 2);
        double travelFeeDiffRate = getDiffRateByBigDecimal(travelFeeDiff, bmkTravelFee);
        double bmkTravelFeePerAvg = BigDecimalUtils.doubleDivide(sumTravelFee, blTravelMmTotal, 2);
        double actTravelFeePerAvg = BigDecimalUtils.doubleDivide(actualTravelFee, actTravelMmTotal, 2);
        double travelFeePerAvgDiff = BigDecimalUtils.getDoubleHalfNum(actTravelFeePerAvg - bmkTravelFeePerAvg, 2);

        double travelFeePerAvgDiffRate = getDiffRateByBigDecimal(travelFeePerAvgDiff, bmkTravelFeePerAvg);
        double bmkDiningFee = BigDecimalUtils.getDoublePercentHalfNum(sumDiningFee, 2);
        double actDiningFee = BigDecimalUtils.getDoublePercentHalfNum(actualDiningFee, 2);
        double diningFeeDiff = BigDecimalUtils.getDoubleHalfNum(actDiningFee - bmkDiningFee, 2);
        double diningFeeDiffRate = getDiffRateByBigDecimal(diningFeeDiff, bmkDiningFee);
        double bmkDiningFeePerAvg = BigDecimalUtils.doubleDivide(sumDiningFee, bmkAllEmpNum, 2);
        double actDiningFeePerAvg = BigDecimalUtils.doubleDivide(actualDiningFee, actAllEmpNum, 2);
        double diningFeePerAvgDiff = BigDecimalUtils.getDoubleHalfNum(actDiningFeePerAvg - bmkDiningFeePerAvg, 2);
        double diningFeePerAvgDiffRate = getDiffRateByBigDecimal(diningFeePerAvgDiff, bmkDiningFeePerAvg);
        double bmkOtherFee = BigDecimalUtils.getDoublePercentHalfNum(sumOtherFee, 2);
        double actOtherFee = BigDecimalUtils.getDoublePercentHalfNum(actualOtherFee, 2);
        double actServiceFee = BigDecimalUtils.getDoublePercentHalfNum(actualServiceFee, 2);
        double otherFeeDiff = BigDecimalUtils.getDoubleHalfNum(actOtherFee - bmkOtherFee, 2);
        double otherFeeDiffRate = getDiffRateByBigDecimal(otherFeeDiff, bmkOtherFee);
        double bmkOtherFeePerAvg = BigDecimalUtils.doubleDivide(sumOtherFee, bmkAllEmpNum, 2);
        double actOtherFeePerAvg = BigDecimalUtils.doubleDivide(actualOtherFee, actAllEmpNum, 2);
        double otherFeePerAvgDiff = BigDecimalUtils.getDoubleHalfNum(actOtherFeePerAvg - bmkOtherFeePerAvg, 2);
        double otherFeePerAvgDiffRate = getDiffRateByBigDecimal(otherFeePerAvgDiff, bmkOtherFeePerAvg);

        double humanMonthTravelHotelFeeAvgDiffTotal = BigDecimalUtils.getDoubleHalfNum(actTravelHotelFeePem - blTravelHotelFeePem,2);//累计人月均差旅住宿费偏差
        double humanMonthTravelHotelFeeAvgDiffRateTotal = getDiffRateByBigDecimal(humanMonthTravelHotelFeeAvgDiffTotal, blTravelHotelFeePem);//累计人月均差旅住宿费偏差率

        double bmkTravelCarFeePem = BigDecimalUtils.getDoubleHalfNum(sumTravelCarFeePem, 2);
        double actTravelCarFeePem = BigDecimalUtils.getDoubleHalfNum(actualTravelCarFeePem, 2);
        double travelCarFeePemDiff = BigDecimalUtils.getDoubleHalfNum(actTravelCarFeePem - bmkTravelCarFeePem, 2);
        double travelCarFeePemDiffRate = getDiffRateByBigDecimal(travelCarFeePemDiff, bmkTravelCarFeePem);

        double bmkCityCarFeePerEmp = BigDecimalUtils.getDoubleHalfNum(sumCityCarFeePerEmp, 2);
        double actCityCarFeePerEmp = BigDecimalUtils.getDoubleHalfNum(actualCityCarFeePerEmp, 2);
        double cityCarFeePerEmpDiff = BigDecimalUtils.getDoubleHalfNum(actCityCarFeePerEmp - bmkCityCarFeePerEmp, 2);
        double cityCarFeePerEmpDiffRate = getDiffRateByBigDecimal(cityCarFeePerEmpDiff, bmkCityCarFeePerEmp);

        double bmkTravelFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(sumTravelFee, sumAllEmpFee), 2);
        double actTravelFeeStandard =  BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(actualTravelFee, bgtBo.getActualAllEmpFee()), 2);
        double travelFeeStandardDiff = BigDecimalUtils.getDoubleHalfNum(actTravelFeeStandard - bmkTravelFeeStandard, 2);
        double travelFeeStandardDiffRate = BigDecimalUtils.getDoubleHalfNum(getDiffRateByBigDecimal(travelFeeStandardDiff, bmkTravelFeeStandard), 2);

        double bmkDiningFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(sumDiningFee, sumAllEmpFee), 2);
        double actDiningFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(actualDiningFee, actualAllEmpFee), 2);
        double diningFeeStandardDiff = BigDecimalUtils.getDoubleHalfNum(actDiningFeeStandard - bmkDiningFeeStandard, 2);
        double diningFeeStandardDiffRate = BigDecimalUtils.getDoubleHalfNum(getDiffRateByBigDecimal(diningFeeStandardDiff, bmkDiningFeeStandard), 2);

        double bmkOtherFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(sumOtherFee, sumAllEmpFee), 2);
        double actOtherFeeStandard =  BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(actualOtherFee, actualAllEmpFee), 2);
        double otherFeeStandardDiff = BigDecimalUtils.getDoubleHalfNum(actOtherFeeStandard - bmkOtherFeeStandard, 2);
        double otherFeeStandardDiffRate = BigDecimalUtils.getDoubleHalfNum(getDiffRateByBigDecimal(otherFeeStandardDiff, bmkOtherFeeStandard), 2);

        double bmkDirectFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(sumDirectFee, sumAllEmpFee), 2);
        double actDirectFeeStandard = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(actualDirectFee, actualAllEmpFee), 2);
        double directFeeStandardDiff = BigDecimalUtils.getDoubleHalfNum(actDirectFeeStandard - bmkDirectFeeStandard, 2);
        double directFeeStandardDiffRate = BigDecimalUtils.getDoubleHalfNum(getDiffRateByBigDecimal(directFeeStandardDiff, bmkDirectFeeStandard), 2);

        double bmkAllCost = BigDecimalUtils.getDoubleHalfNum(bmkAllEmpFee + bmkDirectFee, 2);
        double actAllCost = BigDecimalUtils.getDoubleHalfNum(actAllEmpFee + actDirectFee, 2);
        double allCostDiff = BigDecimalUtils.getDoubleHalfNum(actAllCost - bmkAllCost, 2);

        double allEmpNumDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(actualAllEmpNum - lastActualAllEmpMonth, 2);
        double diningFeeDiffLastMoth = BigDecimalUtils.subtractDouble(BigDecimalUtils.getDoublePercentHalfNum(actualDiningFee,2),
                 BigDecimalUtils.getDoublePercentHalfNum(lastActualDiningFee, 2), 2);
        double travelFeeDiffLastMoth = BigDecimalUtils.subtractDouble(BigDecimalUtils.getDoublePercentHalfNum(actualTravelFee,2),
                BigDecimalUtils.getDoublePercentHalfNum(lastActualTravelFee, 2), 2);
        double otherFeeDiffLastMoth = BigDecimalUtils.subtractDouble(BigDecimalUtils.getDoublePercentHalfNum(actualOtherFee,2),
                BigDecimalUtils.getDoublePercentHalfNum(lastActualOtherFee, 2), 2);

        double lastActTravelFeePerAvg = BigDecimalUtils.doubleDivide(lastActualTravelFee, lastActualAllEmpMonth, 2);
        double travelFeePerAvgDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(actTravelFeePerAvg - lastActTravelFeePerAvg, 2);

        double lastActDiningFeePerAvg = BigDecimalUtils.doubleDivide(lastActualDiningFee, lastActualAllEmpMonth, 2);
        double diningFeePerAvgDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(actDiningFeePerAvg - lastActDiningFeePerAvg, 2);

        double lastActOtherFeePerAvg = BigDecimalUtils.doubleDivide(lastActualOtherFee, lastActualAllEmpMonth, 2);
        double otherFeePerAvgDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(actOtherFeePerAvg - lastActOtherFeePerAvg, 2);

        double lastEmpNumDiff = BigDecimalUtils.getDoubleHalfNum(lastActualAllEmpMonth - lastSumAllEmpNum, 2);
        double lastEmpNumDiffRate = getDiffRateByBigDecimal(lastEmpNumDiff, lastSumAllEmpNum);
        double allEmpNumDiffRateLastMoth = BigDecimalUtils.getDoubleHalfNum(empNumDiffRate - lastEmpNumDiffRate, 2);

        Double lastActDiningFee = BigDecimalUtils.getDoublePercentHalfNum(lastActualDiningFee, 2);
        Double lastBmkDiningFee = BigDecimalUtils.getDoublePercentHalfNum(lastSumDiningFee, 2);
        double lastDiningFeeDiff = BigDecimalUtils.subtractDouble(lastActDiningFee, lastBmkDiningFee, 2);
        double lastDiningFeeDiffRate = getDiffRateByBigDecimal(lastDiningFeeDiff, lastBmkDiningFee);
        double diningFeeDiffRateLastMoth = BigDecimalUtils.getDoubleHalfNum(diningFeeDiffRate - lastDiningFeeDiffRate, 2);
        double lastBmkDiningFeePerAvg = BigDecimalUtils.doubleDivide(lastSumDiningFee, lastSumAllEmpNum, 2);
        double lastDiningFeePerAvgDiffRate = getDiffRateByBigDecimal(lastActDiningFeePerAvg - lastBmkDiningFeePerAvg, lastBmkDiningFeePerAvg);
        double diningFeePerAvgRateDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(diningFeePerAvgDiffRate - lastDiningFeePerAvgDiffRate, 2);

        Double lastActTravelFee = BigDecimalUtils.getDoublePercentHalfNum(lastActualTravelFee, 2);
        Double lastBmkTravelFee = BigDecimalUtils.getDoublePercentHalfNum(lastSumTravelFee, 2);
        double lastTravelFeeDiff = BigDecimalUtils.subtractDouble(lastActTravelFee, lastBmkTravelFee, 2);
        double lastTravelFeeDiffRate = getDiffRateByBigDecimal(lastTravelFeeDiff, lastBmkTravelFee);
        double travelFeeDiffRateLastMoth = BigDecimalUtils.getDoubleHalfNum(travelFeeDiffRate - lastTravelFeeDiffRate, 2);
        double lastBmkTravelFeePerAvg = BigDecimalUtils.doubleDivide(lastSumTravelFee, lastSumAllEmpNum, 2);

        double lastTravelFeePerAvgDiffRate = getDiffRateByBigDecimal(lastActTravelFeePerAvg - lastBmkTravelFeePerAvg, lastBmkTravelFeePerAvg);
        double travelFeePerAvgRateDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(travelFeePerAvgDiffRate - lastTravelFeePerAvgDiffRate, 2);

        Double lastActOtherFee = BigDecimalUtils.getDoublePercentHalfNum(lastActualOtherFee, 2);
        Double lastBmkOtherFee = BigDecimalUtils.getDoublePercentHalfNum(lastSumOtherFee, 2);
        double lastOtherFeeDiff = BigDecimalUtils.subtractDouble(lastActOtherFee, lastBmkOtherFee, 2);
        double lastOtherFeeDiffRate = getDiffRateByBigDecimal(lastOtherFeeDiff, lastBmkOtherFee);
        double otherFeeDiffRateLastMoth = BigDecimalUtils.getDoubleHalfNum(otherFeeDiffRate - lastOtherFeeDiffRate, 2);
        double lastBmkOtherFeePerAvg = BigDecimalUtils.doubleDivide(lastSumOtherFee, lastSumAllEmpNum, 2);
        double lastOtherFeePerAvgDiffRate = getDiffRateByBigDecimal(lastActOtherFeePerAvg - lastBmkOtherFeePerAvg, lastBmkOtherFeePerAvg);
        double otherFeePerAvgRateDiffLastMoth = BigDecimalUtils.getDoubleHalfNum(otherFeePerAvgDiffRate - lastOtherFeePerAvgDiffRate, 2);

        double bmkGm = BigDecimalUtils.getDoubleHalfNum(sumGm, 2);
        double actGm = actualGm;

        // 基准效能因子-全周期：项目集基准百元人工-收入
        double bmkIncomePerHundred = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(forecastIncome, sumAllEmpFee), 2);
        // 实际效能因子-截止统计月：项目集实际百元人工-收入
        double actIncomePerHundred = BigDecimalUtils.getDoubleHalfNum(getEfFeeStandard(actualIncome, actualAllEmpFee), 2);

        PrjCostDvtOriginalBgtBo originalBgtBo = PrjCostDvtOriginalBgtBo.builder()
                .forecastIncome(forecastIncome)
                .sumAllEmpNum(sumAllEmpNum)
                .sumEmpNum(sumEmpNum)
                .osEmpNum(osEmpNum)
                .sumAllEmpFee(sumAllEmpFee)
                .sumEmpFee(sumEmpFee)
                .sumOsEmpFee(sumOsEmpFee)
                .sumTraineeFee(sumTraineeFee)
                .jslfbFee(jslfbFee)
                .sumDirectFee(sumDirectFee)
                .sumTravelFee(sumTravelFee)
                .sumDiningFee(sumDiningFee)
                .sumOtherFee(sumOtherFee)
                .blTravelHotelFee(blTravelHotelFeePem)
                .sumTravelCarFeePem(sumTravelCarFeePem)
                .sumCityCarFeePerEmp(sumCityCarFeePerEmp)
                .actualIncome(actualIncome)
                .actualAllEmpNum(actualAllEmpNum)
                .actualEmpMonth(actualEmpMonth)
                .actualOsEmpMonth(actualOsEmpMonth)
                .actualOsEmpMonth(actualOsEmpMonth)
                .actualAllEmpFee(actualAllEmpFee)
                .actualEmpFee(actualEmpFee)
                .actualOsEmpFee(actualOsEmpFee)
                .actualTraineeFee(actualTraineeFee)
                .actualJslfbFee(actualJslfbFee)
                .actualDirectFee(actualDirectFee)
                .actualTravelFee(actualTravelFee)
                .actualDiningFee(actualDiningFee)
                .actualOtherFee(actualOtherFee)
                .actualServiceFee(actualServiceFee)
                .actTravelHotelFee(actTravelHotelFeePem)
                .actualTravelCarFeePem(actualTravelCarFeePem)
                .actualCityCarFeePerEmp(actualCityCarFeePerEmp)
                .lastSumAllEmpNum(lastSumAllEmpNum)
                .lastSumTravelFee(lastSumTravelFee)
                .lastSumDiningFee(lastSumDiningFee)
                .lastSumOtherFee(lastSumOtherFee)
                .lastActualAllEmpMonth(lastActualAllEmpMonth)
                .lastActualTravelFee(lastActualTravelFee)
                .lastActualDiningFee(lastActualDiningFee)
                .lastActualOtherFee(lastActualOtherFee)
                .bmkGm(sumGm)
                .actGm(actualGm)
                .blTravelMm(blTravelMmTotal)
                .actTravelMm(actTravelMmTotal)
                .build();

        return PrjCostDvtBgtBo.builder()
                .bmkAllEmpNum(bmkAllEmpNum)
                .bmkEmpNum(bmkEmpNum)
                .bmkOsEmpNum(bmkOsEmpNum)
                .actualAllEmpNum(actAllEmpNum)
                .actualEmpNum(actEmpNum)
                .actualOsEmpNum(actOsEmpNum)
                .empNumDiff(empNumDiff)
                .empNumDiffRate(empNumDiffRate)
                .bmkAllEmpFee(bmkAllEmpFee)
                .actualAllEmpFee(actAllEmpFee)
                .allEmpFeeDiff(allEmpFeeDiff)
                .bmkEmpFee(bmkEmpFee)
                .actualEmpFee(actEmpFee)
                .bmkOsEmpFee(bmkOsEmpFee)
                .actualOsEmpFee(actOsEmpFee)
                .bmkTraineeFee(bmkTraineeFee)
                .actTraineeFee(actTraineeFee)
                .bmkJslfbFee(bmkJslfbFee)
                .actualJslfbFee(actJslfbFee)
                .bmkDirectFee(bmkDirectFee)
                .actualDirectFee(actDirectFee)
                .directFeeDiff(directFeeDiff)
                .directFeeDiffRate(directFeeDiffRate)
                .bmkTravelFee(bmkTravelFee)
                .actualTravelFee(actTravelFee)
                .travelFeeDiff(travelFeeDiff)
                .travelFeeDiffRate(travelFeeDiffRate)
                .bmkTravelFeePerAvg(bmkTravelFeePerAvg)
                .actualTravelFeePerAvg(actTravelFeePerAvg)
                .travelFeePerAvgDiff(travelFeePerAvgDiff)
                .travelFeePerAvgDiffRate(travelFeePerAvgDiffRate)
                .bmkDiningFee(bmkDiningFee)
                .actualDiningFee(actDiningFee)
                .diningFeeDiff(diningFeeDiff)
                .diningFeeDiffRate(diningFeeDiffRate)
                .bmkDiningFeePerAvg(bmkDiningFeePerAvg)
                .actualDiningFeePerAvg(actDiningFeePerAvg)
                .diningFeePerAvgDiff(diningFeePerAvgDiff)
                .diningFeePerAvgDiffRate(diningFeePerAvgDiffRate)
                .bmkOtherFee(bmkOtherFee)
                .actualOtherFee(actOtherFee)
                .actualServiceFee(actServiceFee)
                .otherFeeDiff(otherFeeDiff)
                .otherFeeDiffRate(otherFeeDiffRate)
                .bmkOtherFeePerAvg(bmkOtherFeePerAvg)
                .actualOtherFeePerAvg(actOtherFeePerAvg)
                .otherFeePerAvgDiff(otherFeePerAvgDiff)
                .otherFeePerAvgDiffRate(otherFeePerAvgDiffRate)
                .blTravelHotelFeePem(blTravelHotelFeePem)
                .actTravelHotelFeePem(actTravelHotelFeePem)
                .travelHotelFeePemDiff(humanMonthTravelHotelFeeAvgDiffTotal)
                .travelHotelFeePemDiffRate(humanMonthTravelHotelFeeAvgDiffRateTotal)
                .bmkTravelCarFeePem(bmkTravelCarFeePem)
                .actualTravelCarFeePem(actTravelCarFeePem)
                .travelCarFeePemDiff(travelCarFeePemDiff)
                .travelCarFeePemDiffRate(travelCarFeePemDiffRate)
                .bmkCityCarFeePerEmp(bmkCityCarFeePerEmp)
                .actualCityCarFeePerEmp(actCityCarFeePerEmp)
                .cityCarFeePerEmpDiff(cityCarFeePerEmpDiff)
                .cityCarFeePerEmpDiffRate(cityCarFeePerEmpDiffRate)
                .bmkTravelFeeStandard(bmkTravelFeeStandard)
                .actualTravelFeeStandard(actTravelFeeStandard)
                .travelFeeStandardDiff(travelFeeStandardDiff)
                .travelFeeStandardDiffRate(travelFeeStandardDiffRate)
                .bmkDiningFeeStandard(bmkDiningFeeStandard)
                .actualDiningFeeStandard(actDiningFeeStandard)
                .diningFeeStandardDiff(diningFeeStandardDiff)
                .diningFeeStandardDiffRate(diningFeeStandardDiffRate)
                .bmkOtherFeeStandard(bmkOtherFeeStandard)
                .actualOtherFeeStandard(actOtherFeeStandard)
                .otherFeeStandardDiff(otherFeeStandardDiff)
                .otherFeeStandardDiffRate(otherFeeStandardDiffRate)
                .bmkDirectFeeStandard(bmkDirectFeeStandard)
                .actualDirectFeeStandard(actDirectFeeStandard)
                .directFeeStandardDiff(directFeeStandardDiff)
                .directFeeStandardDiffRate(directFeeStandardDiffRate)
                .bmkAllCost(bmkAllCost)
                .actualAllCost(actAllCost)
                .allCostDiff(allCostDiff)
                .allEmpNumDiffLastMoth(allEmpNumDiffLastMoth)
                .diningFeeDiffLastMoth(diningFeeDiffLastMoth)
                .travelFeeDiffLastMoth(travelFeeDiffLastMoth)
                .otherFeeDiffLastMoth(otherFeeDiffLastMoth)
                .travelFeePerAvgDiffLastMoth(travelFeePerAvgDiffLastMoth)
                .diningFeePerAvgDiffLastMoth(diningFeePerAvgDiffLastMoth)
                .otherFeePerAvgDiffLastMoth(otherFeePerAvgDiffLastMoth)
                .allEmpNumDiffRateLastMoth(allEmpNumDiffRateLastMoth)
                .diningFeeDiffRateLastMoth(diningFeeDiffRateLastMoth)
                .travelFeePerAvgRateDiffLastMoth(travelFeePerAvgRateDiffLastMoth)
                .diningFeePerAvgRateDiffLastMoth(diningFeePerAvgRateDiffLastMoth)
                .otherFeePerAvgRateDiffLastMoth(otherFeePerAvgRateDiffLastMoth)
                .bmkGm(bmkGm)
                .actualGm(actGm)
                .bmkIncomePerHundred(bmkIncomePerHundred)
                .actualIncomePerHundred(actIncomePerHundred)
                .originalBgtBo(originalBgtBo)
                .build();
    }
    protected Map<String,Double> checkIsOver(PrjCostStatisticsNewVo vo){
        Map<String,Double> result = new HashMap<>();
        result.put("actAccuTravelFee",vo.getRealTravelFeeGrandTotal());
        result.put("actAccuDiningFee",vo.getRealDiningFeeGrandTotal());
        result.put("actAccuOtherFee",vo.getRealOtherFeeGrandTotal());
        result.put("empNumDiff",vo.getHumanResGrandTotalDiff());
        result.put("empNumDiffRate",vo.getHumanResGrandTotalDiffRate());
        result.put("travelFeeDiff",vo.getTravelFeeGrandTotalDiff());
        result.put("travelFeeDiffRate",vo.getTravelFeeGrandTotalDiffRate());
        result.put("diningFeeDiff",vo.getDiningFeeGrandTotalDiff());
        result.put("diningFeeDiffRate",vo.getDiningFeeGrandTotalDiffRate());
        result.put("otherFeeDiff",vo.getOtherFeeGrandTotalDiff());
        result.put("otherFeeDiffRate",vo.getOtherFeeGrandTotalDiffRate());
        result.put("diningFeePerAvgDiffRate",vo.getDiningFeeGrandMonDiffRate());
        result.put("travelFeePerAvgDiffRate",vo.getTravelFeeGrandMonDiffRate());
        result.put("otherFeePerAvgDiffRate",vo.getOtherFeeGrandMonDiffRate());
        return result;
    }
    protected Map<ObjectId,Map<String,PrjCostStatisticsNewVo>> getMonthBeforeLastDatas(List<ObjectId> prjIds){
        Map<ObjectId,Map<String,PrjCostStatisticsNewVo>> result = new HashMap<>();
       List<PrjCostStatisticsNewVo> prjCostStatDataNew = prjCostStatisticsService.getPrjCostStatDataNew(prjIds, null, null, null, null, null, null);
        if (CollectionUtils.isEmpty(prjCostStatDataNew)){
            return result;
        }
        result = prjCostStatDataNew.stream().collect(Collectors.groupingBy(PrjCostStatisticsNewVo::getPrjId,Collectors.toMap(PrjCostStatisticsNewVo::getYm,Function.identity(),(k1,k2) -> k2)));
        return result;
    }

    protected List<PrjDvtMgt> getLastMonthPrjDvtMgts(String ym ,List<ObjectId> prjIds,ObjectId typeId){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        if (typeId != null){
            conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid),typeId));
        }
        conds.add(new DC_I<ObjectId>(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),prjIds));
        conds.add(new DC_E(DFN.PrjDvtMgt.ym,ym));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.PrjDvtMgt.prj);
        fieldNameList.add(DFN.PrjDvtMgt.desc);
        fieldNameList.add(DFN.PrjDvtMgt.rcdInfo);
        List<PrjDvtMgt> prjDvtMgts = prjDvtMgtDao.findByFieldAndConds(conds, fieldNameList);
        return prjDvtMgts;
    }

    /**
     * 获取E、F项目的超支情况
     * @param sbuId
     * @param ym
     * @param lastYm
     * @param prjLevelIdList
     * @param statusIdList
     * @return
     */
    protected List<PrjCostDeviationEfBgtBo> listPrjCostDeviation(String sbuId, String ym, String lastYm,
                                                                 List<ObjectId> prjLevelIdList, List<ObjectId> statusIdList) {
        List<AggregationOperation> list = new ArrayList<>();
        Criteria criteria = Criteria.where("isValid").ne(false).and("isPrjSet").is(true)
//                .and("prjCode").is("BOMC-BJQ-SX-1BJ225830B")
                .and("sbuId").is(sbuId);
        if (CollectionUtils.isNotEmpty(prjLevelIdList)){
            criteria.and("level.cid").in(prjLevelIdList);
        }
        if (CollectionUtils.isNotEmpty(statusIdList)){
            criteria.and("status.cid").in(statusIdList);
        }else {
            criteria.and("status.cid").nin(Arrays.asList(SysDefConstants.PRJ_STATUS_SUSPEND_ID,PrjConstant.PRJ_STATUS_CANCEL_ID));
        }
        list.add(Aggregation.match(criteria));
        list.add(Aggregation.project()
                .andInclude("prjId")
                .andInclude("prjCode")
                .andInclude("prjName")
                .andExpression("status.name").as("statusName")
                .andExpression("level.cid").as("levelId")
                .andExpression("subPrjs.code").as("subPrjs")
                .andExpression("prov.cid").as("provId")
                .andExpression("{\"$last\":\"$prjBmks\"}").as("maxPrjBmk")
        );
        list.add(Aggregation.addFields()
//                .addField("sumGm").withValue("maxPrjBmk.gm")
                .addField("maxVerSubmitTime").withValueOfExpression("maxPrjBmk.approveTime")
                .addField("sumGm").withValueOfExpression("maxPrjBmk.gm")
                .addField("goalSubPrjMs").withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\n" +
                        "\t\t\"$eq\":{\n" +
                        "\t\t\t{\"$size\":{\"$ifNull\":{\"$maxPrjBmk.goalSubPrjMs\", {}}}},\n" +
                        "\t\t\t0\n" +
                        "\t\t}\n" +
                        "\t},\n" +
                        "\tnull,\n" +
                        "\t{\n" +
                        "\t\t\"$filter\":{\n" +
                        "\t\t\t\"input\":\"$maxPrjBmk.goalSubPrjMs\",\n" +
                        "\t\t\t\"as\":\"item\",\n" +
                        "\t\t\t\"cond\":{\n" +
                        "\t\t\t\t\"$and\":{\n" +
                        "\t\t\t\t\t{\n" +
                        "\t\t\t\t\t\t\"$eq\":{\n" +
                        "\t\t\t\t\t\t\t\"$$item.prj.cid\",\n" +
                        "\t\t\t\t\t\t\t\"$prjId\"\n" +
                        "\t\t\t\t\t\t}\n" +
                        "\t\t\t\t\t}\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}}")
                .build());
        list.add(Aggregation.addFields()
                .addField("forecastIncome")
                .withValueOfExpression("{\"$reduce\":{\n" +
                        "\t\"input\":\"$goalSubPrjMs\",\n" +
                        "\t\"initialValue\":0.0,\n" +
                        "\t\"in\": {\n" +
                        "\t\t\"$sum\": {\"$$value\",{\"$ifNull\":{\"$$this.forecastIncome\", 0.0}}}\n" +
                        "\t}\n" +
                        "}}")
                .build());
        list.add(Aggregation.match(Criteria.where("maxPrjBmk.statusId").is(PrjConstant.DEF_ID_SPECIAL_STATUS_CLOSED)));
        list.add(Aggregation.lookup("prjBudget", "prjId", "prjId", "prjBudget"));
        list.add(Aggregation.addFields()
                .addField("prjBudget")
                .withValueOfExpression("{\"$cond\":{\n" +
                        "\t{\n" +
                        "\t\t\"$eq\":{\n" +
                        "\t\t\t{\"$size\":\"$prjBudget\"},\n" +
                        "\t\t\t0\n" +
                        "\t\t}\n" +
                        "\t},\n" +
                        "\tnull,\n" +
                        "\t{\n" +
                        "\t\t\"$filter\":{\n" +
                        "\t\t\t\"input\":\"$prjBudget\",\n" +
                        "\t\t\t\"as\":\"item\",\n" +
                        "\t\t\t\"cond\":{\n" +
                        "\t\t\t\t\"$and\":{\n" +
                        "\t\t\t\t\t{\n" +
                        "\t\t\t\t\t\t\"$ne\":{\n" +
                        "\t\t\t\t\t\t\t\"$$item.isValid\",\n" +
                        "\t\t\t\t\t\t\tfalse\n" +
                        "\t\t\t\t\t\t}\n" +
                        "\t\t\t\t\t},\n" +
                        "\t\t\t\t\t{\n" +
                        "\t\t\t\t\t\t\"$eq\":{\n" +
                        "\t\t\t\t\t\t\t\"$$item.prjBudgetVerId\",\n" +
                        "\t\t\t\t\t\t\t\"$maxPrjBmk.prjBudgetVerId\"\n" +
                        "\t\t\t\t\t\t}\n" +
                        "\t\t\t\t\t}\n" +
                        "\t\t\t\t}\n" +
                        "\t\t\t}\n" +
                        "\t\t}\n" +
                        "\t}\n" +
                        "}}")
                .build());

        list.add(Aggregation.match(Criteria.where("prjBudget.ym").gte(ym)));
        list.add(Aggregation.unwind("prjBudget"));
        list.add(Aggregation.project()
                .andInclude("prjId")
                .andInclude("prjCode")
                .andInclude("prjName")
                .andInclude("statusName")
                .andInclude("subPrjs")
                .andInclude("provId")
                .andInclude("levelId")
                .andInclude("forecastIncome")
                .andInclude("sumGm")
                .andInclude("maxVerSubmitTime")
                .andExpression("prjBudget.ym").as("ym")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumEmpNum\", 0.0}}").as("sumEmpNum")
                .andExpression("{\"$ifNull\":{\"$prjBudget.osEmpNum\", 0.0}}").as("osEmpNum")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumEmpFee\", 0.0}}").as("sumEmpFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumOsEmpFee\", 0.0}}").as("sumOsEmpFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumTraineeFee\", 0.0}}").as("sumTraineeFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.jslfbFee\", 0.0}}").as("jslfbFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumDirectFee\", 0.0}}").as("sumDirectFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumTravelFee\", 0.0}}").as("sumTravelFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumDiningFee\", 0.0}}").as("sumDiningFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.sumOtherFee\", 0.0}}").as("sumOtherFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.blTravelMm\", 0.0}}").as("blTravelMm")
                .andExpression("{\"$ifNull\":{\"$prjBudget.blTravelHotelFee\", 0.0}}").as("blTravelHotelFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.blTravelCityCarFee\", 0.0}}").as("blTravelCityCarFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.blCityCarFee\", 0.0}}").as("blCityCarFee")

                .andExpression("{\"$ifNull\":{\"$prjBudget.actualIncome\", 0.0}}").as("actualIncome")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualEmpMonth\", 0.0}}").as("actualEmpMonth")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualOsEmpMonth\", 0.0}}").as("actualOsEmpMonth")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualEmpFee\", 0.0}}").as("actualEmpFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualOsEmpFee\", 0.0}}").as("actualOsEmpFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualTraineeFee\", 0.0}}").as("actualTraineeFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualJslfbFee\", 0.0}}").as("actualJslfbFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualDirectFee\", 0.0}}").as("actualDirectFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualTravelFee\", 0.0}}").as("actualTravelFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualDiningFee\", 0.0}}").as("actualDiningFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actualOtherFee\", 0.0}}").as("actualOtherFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actServiceFee\", 0.0}}").as("actServiceFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.travelCarFeePem\", 0.0}}").as("travelCarFeePem")
                .andExpression("{\"$ifNull\":{\"$prjBudget.cityCarFeePerEmp\", 0.0}}").as("cityCarFeePerEmp")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actTravelMm\", 0.0}}").as("actTravelMm")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actTravelHotelFee\", 0.0}}").as("actTravelHotelFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actTravelCityCarFee\", 0.0}}").as("actTravelCityCarFee")
                .andExpression("{\"$ifNull\":{\"$prjBudget.actCityCarFee\", 0.0}}").as("actCityCarFee")
        );

        list.add(Aggregation.addFields()
                .addField("sumAllEmpFee").withValueOfExpression("{\"$add\":{\"$sumEmpFee\", \"$sumOsEmpFee\", \"$sumTraineeFee\", \"$jslfbFee\"}}")
                .addField("sumAllEmpNum").withValueOfExpression("{\"$add\":{\"$sumEmpNum\", \"$osEmpNum\"}}")
                .addField("actualAllEmpFee").withValueOfExpression("{\"$add\":{\"$actualEmpFee\", \"$actualOsEmpFee\", \"$actualTraineeFee\", \"$actualJslfbFee\"}}")
                .addField("actualAllEmpNum").withValueOfExpression("{\"$add\":{\"$actualEmpMonth\", \"$actualOsEmpMonth\"}}")
                .build()
        );

        List<AggregationOperation> ymSqlList = new ArrayList<>();
        ymSqlList.add(Aggregation.match(Criteria.where("ym").lte(ym)));
        ymSqlList.add(Aggregation.sort(Sort.Direction.DESC, "ym"));
        ymSqlList.add(Aggregation.group("prjId")
                .first("prjId").as("prjId")
                .first("prjCode").as("prjCode")
                .first("prjName").as("prjName")
                .first("statusName").as("statusName")
                .first("subPrjs").as("subPrjs")
                .first("provId").as("provId")
                .first("levelId").as("levelId")
                .first("forecastIncome").as("forecastIncome")
                .first("sumGm").as("sumGm")
                .first("maxVerSubmitTime").as("maxVerSubmitTime")
                .first("actualEmpFee").as("curYmActualEmpFee")
                .first("actualOsEmpFee").as("curYmActualOsEmpFee")
                .first("actualJslfbFee").as("curYmActualJslfbFee")
                .first("actualTravelFee").as("curYmActualTravelFee")
                .first("actualDiningFee").as("curYmActualDiningFee")
                .first("actualOtherFee").as("curYmActualOtherFee")
                .first("actualAllEmpNum").as("curYmActualAllEmpNum")
                .sum("sumAllEmpNum").as("sumAllEmpNum")
                .sum("sumEmpNum").as("sumEmpNum")
                .sum("osEmpNum").as("osEmpNum")
                .sum("sumAllEmpFee").as("sumAllEmpFee")
                .sum("sumEmpFee").as("sumEmpFee")
                .sum("sumOsEmpFee").as("sumOsEmpFee")
                .sum("sumTraineeFee").as("sumTraineeFee")
                .sum("jslfbFee").as("jslfbFee")
                .sum("sumDirectFee").as("sumDirectFee")
                .sum("sumTravelFee").as("sumTravelFee")
                .sum("sumDiningFee").as("sumDiningFee")
                .sum("sumOtherFee").as("sumOtherFee")
                .sum("actualIncome").as("actualIncome")
                .sum("actualAllEmpNum").as("actualAllEmpNum")
                .sum("actualEmpMonth").as("actualEmpMonth")
                .sum("actualOsEmpMonth").as("actualOsEmpMonth")
                .sum("actualAllEmpFee").as("actualAllEmpFee")
                .sum("actualEmpFee").as("actualEmpFee")
                .sum("actualOsEmpFee").as("actualOsEmpFee")
                .sum("actualTraineeFee").as("actualTraineeFee")
                .sum("actualJslfbFee").as("actualJslfbFee")
                .sum("actualDirectFee").as("actualDirectFee")
                .sum("actualTravelFee").as("actualTravelFee")
                .sum("actualDiningFee").as("actualDiningFee")
                .sum("actualOtherFee").as("actualOtherFee")
                .sum("actServiceFee").as("actServiceFee")
                .sum("blTravelMm").as("blTravelMm")
                .sum("blTravelHotelFee").as("blTravelHotelFee")
                .sum("blTravelCityCarFee").as("blTravelCityCarFee")
                .sum("blCityCarFee").as("blCityCarFee")
                .sum("actTravelMm").as("actTravelMm")
                .sum("actTravelHotelFee").as("actTravelHotelFee")
                .sum("actTravelCityCarFee").as("actTravelCityCarFee")
                .sum("actCityCarFee").as("actCityCarFee")
                .first("travelCarFeePem").as("travelCarFeePem")
                .first("cityCarFeePerEmp").as("cityCarFeePerEmp")
        );

        List<AggregationOperation> lastYmSqlList = new ArrayList<>();
        lastYmSqlList.add(Aggregation.match(Criteria.where("ym").lte(lastYm)));
        lastYmSqlList.add(Aggregation.group("prjId")
                .first("prjId").as("prjId")
                .sum("sumAllEmpNum").as("lastSumAllEmpNum")
                .sum("sumTravelFee").as("lastSumTravelFee")
                .sum("sumDiningFee").as("lastSumDiningFee")
                .sum("sumOtherFee").as("lastSumOtherFee")
                .sum("actualAllEmpNum").as("lastActualAllEmpMonth")
                .sum("actualTravelFee").as("lastActualTravelFee")
                .sum("actualDiningFee").as("lastActualDiningFee")
                .sum("actualOtherFee").as("lastActualOtherFee"));

        list.add(Aggregation.facet(ymSqlList.toArray(new AggregationOperation[ymSqlList.size()])).as("ymSqlList")
                .and(lastYmSqlList.toArray(new AggregationOperation[lastYmSqlList.size()])).as("lastYmSqlList"));
        Aggregation aggregation = Aggregation.newAggregation(list).withOptions(AggregationOptions.builder().allowDiskUse(true).build());
        Iterator<PrjCostDeviationEfBo> cursor = mongoTemplate.aggregate(aggregation, DBT.PRJINFO.n(), PrjCostDeviationEfBo.class).iterator();
        Map<ObjectId, PrjCostDeviationEfBgtBo> prjId2BgtInfoMap = new HashMap<>();
        while (cursor.hasNext()){
            PrjCostDeviationEfBo bo = cursor.next();
            for (PrjCostDeviationEfBgtBo ymBo : bo.getYmSqlList()) {
                prjId2BgtInfoMap.put(ymBo.getPrjId(), ymBo);
            }
            for (PrjCostDeviationEfBgtBo lastYmBo : bo.getLastYmSqlList()) {
                PrjCostDeviationEfBgtBo finalBo = prjId2BgtInfoMap.get(lastYmBo.getPrjId());
                if (finalBo == null){
                    prjId2BgtInfoMap.put(lastYmBo.getPrjId(), lastYmBo);
                }else{
                    finalBo.setLastSumAllEmpNum(lastYmBo.getLastSumAllEmpNum());
                    finalBo.setLastSumTravelFee(lastYmBo.getLastSumTravelFee());
                    finalBo.setLastSumDiningFee(lastYmBo.getLastSumDiningFee());
                    finalBo.setLastSumOtherFee(lastYmBo.getLastSumOtherFee());

                    finalBo.setLastActualAllEmpMonth(lastYmBo.getLastActualAllEmpMonth());
                    finalBo.setLastActualTravelFee(lastYmBo.getLastActualTravelFee());
                    finalBo.setLastActualDiningFee(lastYmBo.getLastActualDiningFee());
                    finalBo.setLastActualOtherFee(lastYmBo.getLastActualOtherFee());
                }
            }
        }
        if(MapUtils.isEmpty(prjId2BgtInfoMap)){
            return Collections.emptyList();
        }
        return new ArrayList<>(prjId2BgtInfoMap.values());
    }

    protected PrjCostDvtBgtBo getCalcBgtBo(RcdInfo rcdInfo){
        double forecastIncome = DoubleUtil.getNotNull(rcdInfo.getBlIncome());
        double sumEmpNum = DoubleUtil.getNotNull(rcdInfo.getBlEmp());
        double osEmpNum = DoubleUtil.getNotNull(rcdInfo.getBlOutsrc());
        double sumAllEmpNum = sumEmpNum + osEmpNum;

        double sumEmpFee = DoubleUtil.getNotNull(rcdInfo.getBlEmpFee());
        double sumOsEmpFee = DoubleUtil.getNotNull(rcdInfo.getBlOutsrcFee());
        double sumTraineeFee = DoubleUtil.getNotNull(rcdInfo.getBlTraineeFee());
        double jslfbFee = DoubleUtil.getNotNull(rcdInfo.getBlTechSubFee());
        double sumAllEmpFee = sumEmpFee + sumOsEmpFee + sumTraineeFee + jslfbFee;

        double sumDirectFee = DoubleUtil.getNotNull(rcdInfo.getBlDirectFee());
        double sumTravelFee = DoubleUtil.getNotNull(rcdInfo.getBlAccuTravelFee());
        double sumDiningFee = DoubleUtil.getNotNull(rcdInfo.getBlAccuDiningFee());
        double sumOtherFee = DoubleUtil.getNotNull(rcdInfo.getBlAccuOtherFee());

        double actualEmpMonthSubMessy = DoubleUtil.getNotNull(rcdInfo.getActEmp());
        double actualOsEmpMonthSubMessy = DoubleUtil.getNotNull(rcdInfo.getActOutsrc());
        double actualAllEmpNumSubMessy = actualEmpMonthSubMessy + actualOsEmpMonthSubMessy;

        double actualIncome = DoubleUtil.getNotNull(rcdInfo.getActIncome());
        double actualEmpFee = DoubleUtil.getNotNull(rcdInfo.getActEmpFee());
        double actualOsEmpFee = DoubleUtil.getNotNull(rcdInfo.getActOutsrcFee());
        double actualTraineeFee = DoubleUtil.getNotNull(rcdInfo.getActTraineeFee());
        double actualJslfbFee = DoubleUtil.getNotNull(rcdInfo.getActTechSubFee());
        double actualAllEmpFee = actualEmpFee + actualOsEmpFee + actualTraineeFee +actualJslfbFee;

        double actualDirectFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActDirectFee());
        double actualTravelFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActAccuTravelFee());
        double actualDiningFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActAccuDiningFee());
        double actualOtherFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActAccuOtherFee());

        double lastActualAllEmpMonthSubMessy = DoubleUtil.getNotNull(rcdInfo.getPreMonthActEmp());
        double lastActualTravelFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActTravelFeePreMonth());
        double lastActualDiningFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getPreMonthActDiningFee());
        double lastActualOtherFeeSubMessy = DoubleUtil.getNotNull(rcdInfo.getActOtherFeePreMonth());
        double lastSumAllEmpNum = DoubleUtil.getNotNull(rcdInfo.getPreMonthBlEmp());
        double lastSumTravelFee = DoubleUtil.getNotNull(rcdInfo.getBlTravelFeePreMonth());
        double lastSumDiningFee = DoubleUtil.getNotNull(rcdInfo.getPreMonthBlDiningFe());
        double lastSumOtherFee = DoubleUtil.getNotNull(rcdInfo.getBlOtherFeePreMonth());

        double actServiceFee = DoubleUtil.getNotNull(rcdInfo.getActServiceFee());
        double sumTravelCarFeePem = DoubleUtil.getNotNull(rcdInfo.getBlTravelCarFeePerEmp());
        double sumCityCarFeePerEmp = DoubleUtil.getNotNull(rcdInfo.getBlCityCarFeePerEmp());
        double travelCarFeePem = DoubleUtil.getNotNull(rcdInfo.getActTravelCarFeePerEmp());
        double cityCarFeePerEmp = DoubleUtil.getNotNull(rcdInfo.getActCityCarFeePerEmp());
        double blGm = DoubleUtil.getNotNull(rcdInfo.getBlGm());
        double actGm = DoubleUtil.getNotNull(rcdInfo.getActGm());

        PrjCostDeviationEfBgtBo bgtBo = new PrjCostDeviationEfBgtBo();
        bgtBo.setForecastIncome(forecastIncome);
        bgtBo.setSumAllEmpNum(sumAllEmpNum);bgtBo.setSumEmpNum(sumEmpNum);bgtBo.setOsEmpNum(osEmpNum);
        bgtBo.setSumAllEmpFee(sumAllEmpFee);
        bgtBo.setSumEmpFee(sumEmpFee);
        bgtBo.setSumOsEmpFee(sumOsEmpFee);
        bgtBo.setSumTraineeFee(sumTraineeFee);
        bgtBo.setJslfbFee(jslfbFee);
        bgtBo.setSumDirectFee(sumDirectFee);bgtBo.setSumTravelFee(sumTravelFee);bgtBo.setSumDiningFee(sumDiningFee);bgtBo.setSumOtherFee(sumOtherFee);
        bgtBo.setActualIncome(actualIncome);
        bgtBo.setActualAllEmpNumSubMessy(actualAllEmpNumSubMessy);bgtBo.setActualEmpMonthSubMessy(actualEmpMonthSubMessy);bgtBo.setActualOsEmpMonthSubMessy(actualOsEmpMonthSubMessy);
        bgtBo.setActualAllEmpFee(actualAllEmpFee);
        bgtBo.setActualEmpFee(actualEmpFee);
        bgtBo.setActualOsEmpFee(actualOsEmpFee);
        bgtBo.setActualTraineeFee(actualTraineeFee);
        bgtBo.setActualJslfbFee(actualJslfbFee);
        bgtBo.setActualDirectFeeSubMessy(actualDirectFeeSubMessy);bgtBo.setActualTravelFeeSubMessy(actualTravelFeeSubMessy);bgtBo.setActualDiningFeeSubMessy(actualDiningFeeSubMessy);bgtBo.setActualOtherFeeSubMessy(actualOtherFeeSubMessy);
        bgtBo.setActServiceFee(actServiceFee);
        bgtBo.setBlTravelHotelFee(DoubleUtil.getNotNull(rcdInfo.getBlTravelHotelFeePerEmp()));
        bgtBo.setActTravelHotelFee(DoubleUtil.getNotNull(rcdInfo.getActTravelHotelFeePerEmp()));
        bgtBo.setSumTravelCarFeePem(sumTravelCarFeePem);
        bgtBo.setSumCityCarFeePerEmp(sumCityCarFeePerEmp);
        bgtBo.setTravelCarFeePem(travelCarFeePem);
        bgtBo.setCityCarFeePerEmp(cityCarFeePerEmp);

        bgtBo.setLastSumAllEmpNum(lastSumAllEmpNum);
        bgtBo.setLastSumTravelFee(lastSumTravelFee);
        bgtBo.setLastSumDiningFee(lastSumDiningFee);
        bgtBo.setLastSumOtherFee(lastSumOtherFee);
        bgtBo.setPreYmActualAllEmpNumSubMessy(lastActualAllEmpMonthSubMessy);
        bgtBo.setPreYmActualTravelFeeSubMessy(lastActualTravelFeeSubMessy);
        bgtBo.setPreYmActualDiningFeeSubMessy(lastActualDiningFeeSubMessy);
        bgtBo.setPreYmActualOtherFeeSubMessy(lastActualOtherFeeSubMessy);
        bgtBo.setSumGm(blGm);
        bgtBo.setActualGm(actGm);
        bgtBo.setBlTravelMm(DoubleUtil.getNotNull(rcdInfo.getBlTravelMm()));
        bgtBo.setActTravelMm(DoubleUtil.getNotNull(rcdInfo.getActTravelMm()));
        return calcBgtInfo(bgtBo);
    }

    protected List<PrjDvtMgt> getPrjDvtMgtDatas(List<ObjectId> prjIds,String ym,ObjectId typeId){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid),typeId));
        conds.add(new DC_I<ObjectId>(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),prjIds));
        conds.add(new DC_E(DFN.PrjDvtMgt.ym,ym));
        return prjDvtMgtDao.findByFieldAndConds(conds, null);
    }

    protected List<PrjDvtMgt> listPrjDvtMgtInfo(List<ObjectId> prjIds, String ym, ObjectId typeId, List<DbFieldName> fieldNames){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.type.dot(DFN.common_cid),typeId));
        conds.add(new DC_I<>(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),prjIds));
        conds.add(new DC_E(DFN.PrjDvtMgt.ym,ym));
        return prjDvtMgtDao.findByFieldAndConds(conds, fieldNames);
    }

    /**
     * <AUTHOR> @Description 偏差管理统计
     * @Date 15:23 2024/1/19
     * @Param
     * @return
     **/
    @Override
    public List<DevManageStatisVo> devManageStatis(String ym) {
        //偏差类型
        List<ObjectId> typeIds = new ArrayList<>();
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID);
        typeIds.add(PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID);
        //查询偏差管理数据
        List<IDbCondition> conds = new ArrayList<>();
        //conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),null,true));
        conds.add(new DC_E(DFN.PrjDvtMgt.ym,ym));
        conds.add(new DC_I<ObjectId>(DFN.PrjDvtMgt.type.dot(DFN.common_cid),typeIds));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.PrjDvtMgt.type);
        fieldNameList.add(DFN.common_isValid);
        fieldNameList.add(DFN.PrjDvtMgt.prj);
        fieldNameList.add(DFN.PrjDvtMgt.causeType);
        fieldNameList.add(DFN.PrjDvtMgt.causeSubType);
        fieldNameList.add(DFN.PrjDvtMgt.causeSub2Type);
        fieldNameList.add(DFN.PrjDvtMgt.notes);
        List<PrjDvtMgt> prjDvtMgtListAll = prjDvtMgtDao.findByFieldAndConds(conds, fieldNameList);
        if (CollectionUtils.isEmpty(prjDvtMgtListAll)){
            return null;
        }
        //获取有效数据
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtListAll.stream().filter(mgt -> BooleanUtils.isTrue(mgt.getIsValid())).collect(Collectors.toList());
        //获取prjId
        List<ObjectId> prjIds = prjDvtMgtListAll.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
        List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            return null;
        }
        //获取以删除数据
        List<PrjDvtMgt> deleteDatas = prjDvtMgtListAll.stream().filter(mgt -> BooleanUtils.isNotTrue(mgt.getIsValid())).collect(Collectors.toList());
        Map<ObjectId,List<ObjectId>> isDeleteDatas = getIsDeleteDatas(deleteDatas);

        //获取省份id
        List<ObjectId> provIds = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
        List<TeSysDefRoleUser> trackUsers = null;
        if (CollectionUtils.isNotEmpty(provIds)){
            trackUsers = getTrackUsers(provIds);
        }
        List<SysDef> provList = sysDefService.getSysDefsByIds(provIds);
        if (CollectionUtils.isEmpty(provList)){
            return null;
        }
        //获取区域id
        List<ObjectId> regionIds = new ArrayList<>();
        for (SysDef prov : provList){
            List<TeIdNameCn> cndtItems = prov.getCndtItems();
            if (CollectionUtils.isNotEmpty(cndtItems)){
                boolean present = cndtItems.stream().filter(cn -> PrjConstant.REGION.equals(cn.getCodeName()) && cn.getCid() != null).findFirst().isPresent();
                if (present){
                    ObjectId regionId = cndtItems.stream().filter(cn -> PrjConstant.REGION.equals(cn.getCodeName()) && cn.getCid() != null).findFirst().get().getCid();
                    if (!regionIds.contains(regionId)){
                        regionIds.add(regionId);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(regionIds)){
            return null;
        }
        Map<ObjectId,List<ObjectId>> regionProvMap = new HashMap<>();
        for (ObjectId regionId : regionIds){
            List<ObjectId> provIdList = new ArrayList<>();
            for (SysDef prov : provList){
                List<TeIdNameCn> cndtItems = prov.getCndtItems();
                if (CollectionUtils.isNotEmpty(cndtItems)){
                    boolean present = cndtItems.stream().filter(cn -> regionId.equals(cn.getCid())).findFirst().isPresent();
                   if (present){
                       provIdList.add(prov.getId());
                   }
                }
            }
            regionProvMap.put(regionId,provIdList);
        }
        List<SysDef> regionList = sysDefService.getSysDefsByIds(regionIds);
        if (CollectionUtils.isEmpty(regionList)){
            return null;
        }
        List<DevManageStatisVo> result = new ArrayList<>();
        int EFNotAnalysisTotal = 0;
        int EFTotal = 0;
        int ABCGNotAnalysisTotal = 0;
        int ABCGTotal = 0;

        int progressDelayABCGTotal = 0;
        int costDevABCGTotal = 0;
        int implPlanDevABCGTotal = 0;
        int effectDevABCGTotal = 0;
        int progressDelayABCGNotAnalysisTotal = 0;
        int costDevABCGNotAnalysisTotal = 0;
        int implPlanDevABCGNotAnalysisTotal = 0;
        int effectDevABCGNotAnalysisTotal = 0;

        int progressDelayEFTotal = 0;
        int costDevEFTotal = 0;
        int implPlanDevEFTotal = 0;
        int effectDevEFTotal = 0;
        int progressDelayEFNotAnalysisTotal = 0;
        int costDevEFNotAnalysisTotal = 0;
        int implPlanDevEFNotAnalysisTotal = 0;
        int effectDevEFNotAnalysisTotal = 0;
        int deleteTotal = 0;
        List<ObjectId> deleteTotalIds = new ArrayList<>();
        for (SysDef region : regionList){
            List<ObjectId> provIdLists = regionProvMap.get(region.getId());
            if (CollectionUtils.isEmpty(provIdLists)){
                continue;
            }
            //获取区域下的项目
            List<TePrjInfo> prjInfos = prjInfoList.stream().filter(prj -> prj.getProv() != null && provIdLists.contains(prj.getProv().getCid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(prjInfos)){
                continue;
            }
            List<ObjectId> prjIdABCGList = prjInfos.stream().filter(prj -> prj.getLevel() != null &&
                    (PrjConstant.PRJ_LEVEL_LIST_A_B.contains(prj.getLevel().getCid()) || PrjConstant.PRJ_LEVEL_LIST_C_G.contains(prj.getLevel().getCid()))).map(TePrjInfo::getPrjId).collect(Collectors.toList());
            List<ObjectId> prjIdEFList = prjInfos.stream().filter(prj -> prj.getLevel() != null && PrjConstant.PRJ_LEVEL_LIST_E_F.contains(prj.getLevel().getCid())).map(TePrjInfo::getPrjId).collect(Collectors.toList());

            //根据项目获取偏差数据
            List<PrjDvtMgt> prjDvtMgtsABCG = prjDvtMgtList.stream().filter(dvt -> prjIdABCGList.contains(dvt.getPrj().getCid())).collect(Collectors.toList());
            List<PrjDvtMgt> prjDvtMgtsEF = prjDvtMgtList.stream().filter(dvt -> prjIdEFList.contains(dvt.getPrj().getCid())).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(prjDvtMgtsABCG) && CollectionUtils.isEmpty(prjDvtMgtsEF)){
                continue;
            }
            List<TeIdNameCn> cndtItems = region.getCndtItems();
            String bigRegionName = "";
            if (CollectionUtils.isNotEmpty(cndtItems)){
                boolean present = cndtItems.stream().filter(cn -> PrjConstant.BIG_REGION.equals(cn.getCodeName())).findFirst().isPresent();
                if (present){
                    TeIdNameCn bigRegion = cndtItems.stream().filter(cn -> PrjConstant.BIG_REGION.equals(cn.getCodeName())).findFirst().get();
                    bigRegionName = StringUtil.getNotNullStr(bigRegion.getName());
                }
            }
            DevManageStatisVo vo = new DevManageStatisVo();
            vo.setBigRegionName(bigRegionName);
            vo.setRegionName(StringUtil.getNotNullStr(region.getDefName()));
            List<ObjectId> provIdList = prjInfos.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).map(prj -> prj.getProv().getCid()).collect(Collectors.toList());
            StringBuilder trackUserABCG = new StringBuilder();
            StringBuilder trackUserEF = new StringBuilder();
            if (CollectionUtils.isNotEmpty(provIdList) && CollectionUtils.isNotEmpty(trackUsers)){
                List<TeSysDefRoleUser> trackUserList = trackUsers.stream().filter(roleUser -> provIdList.contains(roleUser.getDefId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(trackUserList)){
                    for (TeSysDefRoleUser trackUser : trackUserList){
                        List<TeIdNameCn> orDefs = trackUser.getOrDefs();
                        if (CollectionUtils.isNotEmpty(orDefs)){
                            List<ObjectId> levelIds = orDefs.stream().filter(teIdNameCn -> teIdNameCn.getCid() != null).map(TeIdNameCn::getCid).collect(Collectors.toList());
                            if (levelIds.contains(PrjConstant.PRJ_LEVEL_A)
                                    || levelIds.contains(PrjConstant.PRJ_LEVEL_A_PLUS)
                                    || levelIds.contains(PrjConstant.PRJ_LEVEL_B)
                                    || levelIds.contains(PrjConstant.PRJ_LEVEL_B_PLUS)
                                    || levelIds.contains(PrjConstant.PRJ_LEVEL_C)
                                    || levelIds.contains(PrjConstant.PRJ_LEVEL_G)
                            ){
                                TeSysDefRoleUser2User roleUser = trackUser.getRoleUser();
                                if (!trackUserABCG.toString().contains(roleUser.getUserName())){
                                    trackUserABCG.append(StringUtil.getNotNullStr(roleUser.getUserName()+"、"));
                                }
                            }
                            if (levelIds.contains(PrjConstant.PRJ_LEVEL_E) || levelIds.contains(PrjConstant.PRJ_LEVEL_F)){
                                TeSysDefRoleUser2User roleUser = trackUser.getRoleUser();
                                if (!trackUserEF.toString().contains(roleUser.getUserName())){
                                    trackUserEF.append(StringUtil.getNotNullStr(roleUser.getUserName()+"、"));
                                }
                            }
                        }
                    }
                }
            }
            //跟踪人ABCG
            vo.setTrackUserABCG(StringUtil.isNotNull(trackUserABCG.toString()) ? trackUserABCG.deleteCharAt(trackUserABCG.lastIndexOf("、")).toString() : "");
            //跟踪人EF
            vo.setTrackUserEF(StringUtil.isNotNull(trackUserEF.toString()) ? trackUserEF.deleteCharAt(trackUserEF.lastIndexOf("、")).toString() : "");

            int progressDelayABCG = 0;
            int costDevABCG = 0;
            int implPlanDevABCG = 0;
            int effectDevABCG = 0;
            int progressDelayABCGNotAnalysis = 0;
            int costDevABCGNotAnalysis = 0;
            int implPlanDevABCGNotAnalysis = 0;
            int effectDevABCGNotAnalysis = 0;
            for (PrjDvtMgt prjDvtMgt : prjDvtMgtsABCG){
                TeIdNameCn type = prjDvtMgt.getType();
                TeIdNameCn causeType = prjDvtMgt.getCauseType();
                TeIdNameCn causeSubType = prjDvtMgt.getCauseSubType();
                TeIdNameCn causeSub2Type = prjDvtMgt.getCauseSub2Type();
                if (PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID.equals(type.getCid())){
                    progressDelayABCG++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        progressDelayABCGNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID.equals(type.getCid())){
                    costDevABCG++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        costDevABCGNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID.equals(type.getCid())){
                    implPlanDevABCG++;
                    if (causeType == null  && StringUtil.isNull(prjDvtMgt.getNotes())){
                        implPlanDevABCGNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID.equals(type.getCid())){
                    effectDevABCG++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        effectDevABCGNotAnalysis++;
                    }
                }
            }
            //合计ABCG
            int ABCGNotAnalysis = progressDelayABCGNotAnalysis+costDevABCGNotAnalysis+implPlanDevABCGNotAnalysis+effectDevABCGNotAnalysis;
            ABCGNotAnalysisTotal += ABCGNotAnalysis;
            ABCGTotal += prjDvtMgtsABCG.size();
            vo.setTotalABCG(ABCGNotAnalysis+"/"+ prjDvtMgtsABCG.size());
            int progressDelayEF = 0;
            int costDevEF = 0;
            int implPlanDevEF = 0;
            int effectDevEF = 0;
            int progressDelayEFNotAnalysis = 0;
            int costDevEFNotAnalysis = 0;
            int implPlanDevEFNotAnalysis = 0;
            int effectDevEFNotAnalysis = 0;
            for (PrjDvtMgt prjDvtMgt : prjDvtMgtsEF){
                TeIdNameCn type = prjDvtMgt.getType();
                TeIdNameCn causeType = prjDvtMgt.getCauseType();
                TeIdNameCn causeSubType = prjDvtMgt.getCauseSubType();
                TeIdNameCn causeSub2Type = prjDvtMgt.getCauseSub2Type();
                if (PrjConstant.PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID.equals(type.getCid())){
                    progressDelayEF++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        progressDelayEFNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID.equals(type.getCid())){
                    costDevEF++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        costDevEFNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID.equals(type.getCid())){
                    implPlanDevEF++;
                    if (causeType == null && StringUtil.isNull(prjDvtMgt.getNotes())){
                        implPlanDevEFNotAnalysis++;
                    }
                }else if (PrjConstant.PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID.equals(type.getCid())){
                    effectDevEF++;
                    if ((causeSubType == null || causeType == null || causeSub2Type == null) && StringUtil.isNull(prjDvtMgt.getNotes())){
                        effectDevEFNotAnalysis++;
                    }
                }
            }
            //合计EF
            int EFNotAnalysis= progressDelayEFNotAnalysis+costDevEFNotAnalysis+implPlanDevEFNotAnalysis+effectDevEFNotAnalysis;
            EFNotAnalysisTotal += EFNotAnalysis;
            EFTotal += prjDvtMgtsEF.size();
            vo.setTotalEF(EFNotAnalysis+"/"+prjDvtMgtsEF.size());
            //进度延期ABCG
            vo.setProgressDelayABCG(progressDelayABCGNotAnalysis+"/"+progressDelayABCG);
            progressDelayABCGTotal += progressDelayABCG;
            progressDelayABCGNotAnalysisTotal += progressDelayABCGNotAnalysis;
            //进度延期EF
            vo.setProgressDelayEF(progressDelayEFNotAnalysis+"/"+progressDelayEF);
            progressDelayEFTotal += progressDelayEF;
            progressDelayEFNotAnalysisTotal += progressDelayEFNotAnalysis;
            //成本偏差ABCG
            vo.setCostDevABCG(costDevABCGNotAnalysis+"/"+costDevABCG);
            costDevABCGTotal += costDevABCG;
            costDevABCGNotAnalysisTotal += costDevABCGNotAnalysis;
            //成本偏差EF
            vo.setCostDevEF(costDevEFNotAnalysis+"/"+costDevEF);
            costDevEFTotal += costDevEF;
            costDevEFNotAnalysisTotal += costDevEFNotAnalysis;
            //实施计划偏差ABCG
            vo.setImplPlanDevABCG(implPlanDevABCGNotAnalysis+"/"+implPlanDevABCG);
            implPlanDevABCGTotal += implPlanDevABCG;
            implPlanDevABCGNotAnalysisTotal += implPlanDevABCGNotAnalysis;
            //实施计划偏差EF
            vo.setImplPlanDevEF(implPlanDevEFNotAnalysis+"/"+implPlanDevEF);
            implPlanDevEFTotal += implPlanDevEF;
            implPlanDevEFNotAnalysisTotal += implPlanDevEFNotAnalysis;
            //效能偏差ABCG
            vo.setEffectDevABCG(effectDevABCGNotAnalysis+"/"+effectDevABCG);
            effectDevABCGTotal += effectDevABCG;
            effectDevABCGNotAnalysisTotal += effectDevABCGNotAnalysis;
            //效能偏差EF
            vo.setEffectDevEF(effectDevEFNotAnalysis+"/"+effectDevEF);
            effectDevEFTotal += effectDevEF;
            effectDevEFNotAnalysisTotal += effectDevEFNotAnalysis;
            //已删除
            if (MapUtils.isNotEmpty(isDeleteDatas)){
                List<ObjectId> deleteIds = new ArrayList<>();
                for (ObjectId provId : isDeleteDatas.keySet()){
                    if (provIdLists.contains(provId)){
                        deleteIds.addAll(isDeleteDatas.get(provId));
                    }
                }
                vo.setIsDelete(deleteIds.size());
                deleteTotal += deleteIds.size();
                deleteTotalIds.addAll(deleteIds);
                vo.setDvtMgtIds(deleteIds);
            }

            result.add(vo);
        }
        result.sort(new Comparator<DevManageStatisVo>() {
            @Override
            public int compare(DevManageStatisVo o1, DevManageStatisVo o2) {
                return o1.getBigRegionName().compareTo(o2.getBigRegionName());
            }
        });
        DevManageStatisVo totalVo = new DevManageStatisVo();
        totalVo.setBigRegionName("总计");
        totalVo.setTotalABCG(ABCGNotAnalysisTotal +"/"+ ABCGTotal);
        totalVo.setTotalEF(EFNotAnalysisTotal +"/"+ EFTotal);
        totalVo.setProgressDelayABCG(progressDelayABCGNotAnalysisTotal +"/"+progressDelayABCGTotal);
        totalVo.setProgressDelayEF(progressDelayEFNotAnalysisTotal+"/"+progressDelayEFTotal);
        totalVo.setCostDevABCG(costDevABCGNotAnalysisTotal+"/"+costDevABCGTotal);
        totalVo.setCostDevEF(costDevEFNotAnalysisTotal+"/"+costDevEFTotal);
        totalVo.setImplPlanDevABCG(implPlanDevABCGNotAnalysisTotal+"/"+implPlanDevABCGTotal);
        totalVo.setImplPlanDevEF(implPlanDevEFNotAnalysisTotal+"/"+implPlanDevEFTotal);
        totalVo.setEffectDevABCG(effectDevABCGNotAnalysisTotal+"/"+effectDevABCGTotal);
        totalVo.setEffectDevEF(effectDevEFNotAnalysisTotal+"/"+effectDevEFTotal);
        totalVo.setIsDelete(deleteTotal);
        totalVo.setDvtMgtIds(deleteTotalIds);
        result.add(totalVo);
        return result;
    }
    //查询已经删除的数据
    private Map<ObjectId,List<ObjectId>> getIsDeleteDatas(List<PrjDvtMgt> prjDvtMgtList){
        Map<ObjectId,List<ObjectId>> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(prjDvtMgtList)){
            List<ObjectId> prjIds = prjDvtMgtList.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
            List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
            if (CollectionUtils.isNotEmpty(prjInfoList)){
                Map<ObjectId, Set<ObjectId>> provPrjMap = prjInfoList.stream().filter(prj -> prj.getProv() != null && prj.getProv().getCid() != null).collect(Collectors.groupingBy(prj -> prj.getProv().getCid(),Collectors.mapping(TePrjInfo::getPrjId,Collectors.toSet())));
                for (ObjectId provId : provPrjMap.keySet()){
                    Set<ObjectId> prjIdSet = provPrjMap.get(provId);
                    List<ObjectId> prjDvtIds = prjDvtMgtList.stream().filter(dvt -> prjIdSet.contains(dvt.getPrj().getCid())).map(PrjDvtMgt::getId).collect(Collectors.toList());
                    result.put(provId,prjDvtIds);
                }
            }
        }
        return result;
    }
    //查询跟踪人信息
    private List<TeSysDefRoleUser> getTrackUsers(List<ObjectId> provIds){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_I<ObjectId>(DFN.sysDefRoleUser__defId,provIds));
        conds.add(new DC_E(DFN.sysDefRoleUser__role.dot(DFN.sysDefRoleUser__roleId),StringUtil.toObjectId(SysDefConstants.TRACK_PSN_DEF_ID)));
        conds.add(new DC_E(DFN.sysDefRoleUser_defType.dot(DFN.sysDef__defTypeCodeName),SysDefTypeCodeName.ABP_PROV.getValue()));
        conds.add(new DC_E(DFN.sysDefRoleUser__roleUser.dot(DFN.common_userName),null,true));
        return sysDefRoleUserDao.findByFieldAndConds(conds, null);
    }

    /**
     * <AUTHOR> @Description 获取以删除数据
     * @Date 11:08 2024/1/22
     * @Param
     * @return
     **/
    @Override
    public List<PrjDvtMgtVo> getIsDeleteDatasInfo(List<ObjectId> deleteIds) {
        //获取已经删除的数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<ObjectId>(DFN.common__id,deleteIds));
        conds.add(new DC_E(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),null,true));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.PrjDvtMgt.type);
        fieldNameList.add(DFN.PrjDvtMgt.prj);
        fieldNameList.add(DFN.PrjDvtMgt.deleteUser);
        fieldNameList.add(DFN.PrjDvtMgt.deleteTime);
        fieldNameList.add(DFN.PrjDvtMgt.invalidDesc);
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, fieldNameList);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            return null;
        }
        //获取prjId
        List<ObjectId> prjIds = prjDvtMgtList.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
        List<TePrjInfo> prjInfoList = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(prjInfoList)){
            return null;
        }
        //获取区域id
        List<ObjectId> regionIds = prjInfoList.stream().filter(prj -> prj.getBigRegion() != null && prj.getBigRegion().getCid() != null).map(prj -> prj.getBigRegion().getCid()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(regionIds)){
            return null;
        }
        //根据区域划分数据
        Map<ObjectId, List<TePrjInfo>> prjRegionMap = prjInfoList.stream().filter(prj -> prj.getBigRegion() != null && prj.getBigRegion().getCid() != null).collect(Collectors.groupingBy(prj -> prj.getBigRegion().getCid()));
        List<SysDef> regionList = sysDefService.getSysDefsByIds(regionIds);
        if (CollectionUtils.isEmpty(regionList)){
            return null;
        }
        List<PrjDvtMgtVo> vos = new ArrayList<>();
        for (SysDef region : regionList){
            //获取区域下的项目
            List<TePrjInfo> prjInfos = prjRegionMap.get(region.getId());
            if (CollectionUtils.isEmpty(prjInfos)){
                continue;
            }
            Map<ObjectId, TePrjInfo> prjInfoMap = prjInfos.stream().collect(Collectors.toMap(TePrjInfo::getPrjId, Function.identity(), (v1, v2) -> v2));
            List<ObjectId> prjIdList = prjInfos.stream().map(TePrjInfo::getPrjId).collect(Collectors.toList());
            List<PrjDvtMgt> prjDvtMgts = prjDvtMgtList.stream().filter(dvt -> prjIdList.contains(dvt.getPrj().getCid())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(prjDvtMgts)){
                continue;
            }
            for (PrjDvtMgt prjDvtMgt : prjDvtMgts){
                PrjDvtMgtVo vo = new PrjDvtMgtVo();
                List<TeIdNameCn> cndtItems = region.getCndtItems();
                String bigRegionName = "";
                if (CollectionUtils.isNotEmpty(cndtItems)){
                    boolean present = cndtItems.stream().filter(cn -> PrjConstant.BIG_REGION.equals(cn.getCodeName())).findFirst().isPresent();
                    if (present){
                        TeIdNameCn bigRegion = cndtItems.stream().filter(cn -> PrjConstant.BIG_REGION.equals(cn.getCodeName())).findFirst().get();
                        bigRegionName = StringUtil.getNotNullStr(bigRegion.getName());
                    }
                }
                vo.setBigRegionName(bigRegionName);
                vo.setRegionName(region.getDefName());
                TePrjInfo prjInfo = prjInfoMap.get(prjDvtMgt.getPrj().getCid());
                vo.setProvName(prjInfo.getProv() == null ? "" : StringUtil.getNotNullStr(prjInfo.getProv().getName()));
                vo.setLevelName(prjInfo.getLevel() == null ? "" : prjInfo.getLevel().getName());
                vo.setTypeName(prjDvtMgt.getType() == null ? "" : prjDvtMgt.getType().getName());
                vo.setPrjCode(prjInfo.getPrjCode());
                vo.setPrjName(prjInfo.getPrjName());
                vo.setDeleteUser(prjDvtMgt.getDeleteUser());
                vo.setDeleteTime(prjDvtMgt.getDeleteTime() == null ? "" : DateUtil.formatDate2Str(prjDvtMgt.getDeleteTime(),DateUtil.DATE_FORMAT));
                vo.setInvalidDesc(prjDvtMgt.getInvalidDesc());
                vos.add(vo);
            }
        }
        return vos;
    }
    @Override
    public Map<ObjectId,Map<String,Double>> getIncomeAndRgfee(List<TePrjInfo> prjSetList,List<TePrjInfo> subPrjList,
                                                               List<String> prjSetCodeList,Map<ObjectId, List<TePrjBudget>> prjBudgetMap,
                                                               Map<ObjectId,TePrjInfoPrjBmks> maxPrjBmkMap, String sbuId,String bgtYm
    ){
        if (StringUtil.isNull(sbuId)){
            throw BusinessException.initExc("当前BU为空！");
        }
        // 获取bu的最大出账月份
        Map<String, String> buCode2MaxAccountYm = abpOopDao.queryMaxActualizedYmByBuCode(Arrays.asList(sbuId));
        //最后一天
        String maxAccountYm = "";
        if (MapUtils.isNotEmpty(buCode2MaxAccountYm) && StringUtil.isNotNull(buCode2MaxAccountYm.get(sbuId))){
            maxAccountYm =DateUtil.formatDate2Str(DateUtil.getLastDayByMonth(DateUtil.parseDate(buCode2MaxAccountYm.get(sbuId),DateUtil.DATE_MONTH_FOTMAT)),DateUtil.DATE_FORMAT);
        }
        Map<ObjectId,Map<String,Double>> result = new HashMap<>();

        Map<ObjectId, TePrjInfo> subPrjMap = null;
        List<String> subPrjAndContractPrjCodes = new ArrayList<>();
        List<String> parentPrjCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(subPrjList)){
            subPrjMap = subPrjList.stream().collect(Collectors.toMap(TePrjInfo::getPrjId, Function.identity()));
            for (TePrjInfo subPrj : subPrjList){
                if (StringUtil.isNotNull(subPrj.getPrjCode())){
                    subPrjAndContractPrjCodes.add(subPrj.getPrjCode());
                }
                if (StringUtil.isNotNull(subPrj.getParentProjectCode())){
                    parentPrjCodes.add(subPrj.getParentProjectCode());
                }
            }
        }

        Map<String, TePrjInfo> contractPrjMap = null;
        Map<String, ObjectId> contractPrjSetMap = new HashMap<>();
        //合同项目编码
        List<String> contractPrjCodes = null;
        //获取合同项目
        if (CollectionUtils.isNotEmpty(parentPrjCodes)){
            List<TePrjInfo> contractPrjs = getContractPrjs(parentPrjCodes);
            if (CollectionUtils.isNotEmpty(contractPrjs)){
                //划分项目数据
                contractPrjMap = contractPrjs.stream().collect(Collectors.toMap(TePrjInfo::getPrjCode, Function.identity(), (v1, v2) -> v2));
                //合同项目编码
                contractPrjCodes = contractPrjs.stream().map(TePrjInfo::getPrjCode).collect(Collectors.toList());
                //获取合同项目所在项目集
                List<TePrjInfo> contractPrjSetList = getContractPrjSet(contractPrjCodes);
                if (CollectionUtils.isNotEmpty(contractPrjSetList)){
                    for (String contractPrjCode : contractPrjCodes){
                        for (TePrjInfo contractPrjSet : contractPrjSetList){
                            List<TePrjInfoSubPrj> subPrjs = contractPrjSet.getSubPrjs();
                            boolean present = subPrjs.stream().filter(subPrj -> contractPrjCode.equals(subPrj.getCode())).findFirst().isPresent();
                            if (present){
                                contractPrjSetMap.put(contractPrjCode,contractPrjSet.getPrjId());
                            }
                        }
                    }
                }
                subPrjAndContractPrjCodes.addAll(contractPrjCodes);
            }
        }

        // 获取子项目的pom状态
        Map<String, String> prjCode2PomStatusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(subPrjAndContractPrjCodes)){
            getSubPrjInfo(subPrjAndContractPrjCodes, prjCode2PomStatusMap);
        }

        // 根据BU和省份取到最新的费率
        Map<String, String> prjCode2OrderCodeMap = new HashMap<>();
        // 根据BU和编码，查询项目编码和订单编码
        List<String> prjAndOrderCodeList = abpOopDao.getPrjCode2OrderCodeInfo(prjSetCodeList, prjCode2OrderCodeMap, null);
        if (CollectionUtils.isNotEmpty(contractPrjCodes)){
            prjAndOrderCodeList.addAll(contractPrjCodes);
        }
        // 查询abp的实际收入和成本
        Map<String, PrjAbpOopBgtBO> prjCode2BgtMap = getAbpOopBgt(sbuId, prjAndOrderCodeList, bgtYm, prjCode2PomStatusMap, prjCode2OrderCodeMap);
        for (TePrjInfo prjInfo : prjSetList){
            Map<String,Double> incomeAndRgfeeMap = new HashMap<>();
            ObjectId prjId = prjInfo.getPrjId();
            if (MapUtils.isEmpty(prjBudgetMap) || CollectionUtils.isEmpty(prjBudgetMap.get(prjId))){
                continue;
            }
            List<TePrjBudget> tePrjBudgets = prjBudgetMap.get(prjId);
            //排序
            tePrjBudgets.sort(new Comparator<TePrjBudget>() {
                @Override
                public int compare(TePrjBudget o1, TePrjBudget o2) {
                    return o1.getYm().compareTo(o2.getYm());
                }
            });
            //预测收入
            double forecastIncome = 0d;
            if (MapUtils.isNotEmpty(maxPrjBmkMap)){
                TePrjInfoPrjBmks maxPrjBmk = maxPrjBmkMap.get(prjId);
                List<GoalSubPrjMs> goalSubPrjMs = maxPrjBmk.getGoalSubPrjMs();
                if (CollectionUtils.isNotEmpty(goalSubPrjMs)){
                    boolean present = goalSubPrjMs.stream().filter(go -> go.getPrj() != null && prjId.equals(go.getPrj().getCid()) && go.getForecastIncome() != null).findFirst().isPresent();
                    if (present){
                        forecastIncome = goalSubPrjMs.stream().filter(go -> go.getPrj() != null && prjId.equals(go.getPrj().getCid()) && go.getForecastIncome() != null).mapToDouble(GoalSubPrjMs::getForecastIncome).sum();
                    }
                }
            }
            double blIncome = BigDecimalUtils.getDoublePercentHalfNum(forecastIncome,2);//基准收入
            double blRgFee = 0d;//基准人工费

            double blEmpFee = 0d;
            double blOsEmpFee = 0d;
            double blTraineeFee = 0d;
            double blJsfbFee = 0d;

            double blTravelFee = 0d;
            double blDiningFee = 0d;
            double blOtherFee = 0d;

            double blEmpNum = 0d;
            double blOsEmpNum = 0d;
            double sumAllEmpAndDirectFee = 0d;
            for (TePrjBudget budget : tePrjBudgets){
                blRgFee += (budget.getSumEmpFee() + budget.getSumOsEmpFee() + budget.getSumTraineeFee() +(budget.getJslfbFee() == null ? 0d : budget.getJslfbFee()));
                sumAllEmpAndDirectFee+= (budget.getSumAllEmpFee()+budget.getSumDirectFee());

                blEmpFee += budget.getSumEmpFee();
                blOsEmpFee += budget.getSumOsEmpFee();
                blTraineeFee += budget.getSumTraineeFee();
                blJsfbFee += DoubleUtil.getNotNull(budget.getJslfbFee());
                blTravelFee += budget.getSumTravelFee();
                blDiningFee += budget.getSumDiningFee();
                blOtherFee += budget.getSumOtherFee();
                blEmpNum += budget.getSumEmpNum();
                blOsEmpNum += budget.getOsEmpNum();
            }
            blRgFee = BigDecimalUtils.getDoublePercentHalfNum(blRgFee,2);
            double actIncome = 0d;//实际收入
            double actRgFee = 0d;//实际人工费
            double allCostFee = 0d;
            double allIncome = 0d;

            double actEmpFee = 0d;
            double actOsEmpFee = 0d;
            double actTraineeFee = 0d;
            double actJsfbFee = 0d;

            double actTravelFee = 0d;
            double actDiningFee = 0d;
            double actOtherFee = 0d;

            double actEmpNum = 0d;
            double actOsEmpNum = 0d;
            List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
            if (CollectionUtils.isNotEmpty(subPrjs) && MapUtils.isNotEmpty(subPrjMap)){
                for (TePrjInfoSubPrj subPrj : subPrjs){
                    TePrjInfo subPrjInfo = subPrjMap.get(subPrj.getCid());
                    if (subPrjInfo != null && StringUtil.isNotNull(subPrjInfo.getPrjCode())){
                        String oaPrjCloseDate = subPrjInfo.getOaPrjCloseDate();
                        //子项目的parentCode
                        String parentProjectCode = subPrjInfo.getParentProjectCode();
                        boolean isSamePrjSet = false;
                        if (StringUtil.isNotNull(parentProjectCode) && MapUtils.isNotEmpty(contractPrjMap)
                                && contractPrjMap.get(parentProjectCode) != null){
                            //合同项目
                            TePrjInfo contractPrj = contractPrjMap.get(parentProjectCode);
                            //合同项目所在项目集
                            ObjectId contractPrjSetId = contractPrjSetMap.get(contractPrj.getPrjCode());
                            isSamePrjSet = prjId.equals(contractPrjSetId) ? true : false;
                        }
                        PrjAbpOopBgtBO prjAbpOopBgtBO = prjCode2BgtMap.get(subPrjInfo.getPrjCode());
                        if (prjAbpOopBgtBO != null){
                            actRgFee +=  BigDecimalUtils.getDoublePercentHalfNum(DoubleUtil.getNotNull(prjAbpOopBgtBO.getEmpFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getOsEmpFee())+ DoubleUtil.getNotNull(prjAbpOopBgtBO.getTraineeFee()) + DoubleUtil.getNotNull(prjAbpOopBgtBO.getJslfbFee()),2);
                            allCostFee += (DoubleUtil.getNotNull(prjAbpOopBgtBO.getEmpFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getOsEmpFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getTraineeFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getJslfbFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getTravelFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getDiningFee())
                                    + DoubleUtil.getNotNull(prjAbpOopBgtBO.getOtherFee()));

                            actEmpFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getEmpFee());
                            actOsEmpFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getOsEmpFee());
                            actTraineeFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getTraineeFee());
                            actJsfbFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getJslfbFee());
                            actTravelFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getEmpFee());
                            actDiningFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getDiningFee());
                            actOtherFee += DoubleUtil.getNotNull(prjAbpOopBgtBO.getOtherFee());
                            actEmpNum += DoubleUtil.getNotNull(prjAbpOopBgtBO.getEmpNum());
                            actOsEmpNum += DoubleUtil.getNotNull(prjAbpOopBgtBO.getOsEmpNum());
                        }

                        String pomStatus = prjCode2PomStatusMap.get(subPrjInfo.getPrjCode());
                        if ((!PrjConstant.PROJECT_STATUS_NAME.equals(pomStatus) && !PrjConstant.PROJECT_STATUS_NAME_CLOSE.equals(pomStatus)) ||
                                (PrjConstant.PROJECT_STATUS_NAME_CLOSE_CANCEL.contains(pomStatus) && StringUtil.isNotNull(oaPrjCloseDate) && oaPrjCloseDate.compareTo(maxAccountYm) > 0)
                        ){
                            actIncome +=  BigDecimalUtils.getDoublePercentHalfNum(subPrj.getNetSaleAmt(),2);
                            allIncome += DoubleUtil.getNotNull(subPrj.getNetSaleAmt());
                        }else if (PrjConstant.PROJECT_STATUS_NAME_CLOSE_CANCEL.contains(pomStatus) && PrjConstant.PROJECT_FORM_SPLIT.equals(subPrjInfo.getProjectForm()) && !isSamePrjSet
                                && StringUtil.isNotNull(oaPrjCloseDate) && oaPrjCloseDate.compareTo(maxAccountYm) <= 0 && prjAbpOopBgtBO != null){
                            //若prjInfo.projectStatusPom =  CANCEL 或 CLOSE，且 "projectForm" ="SPLIT"，且该子项目的合同项目与当前项目不在同一个项目集中，
                            // 则需要取当前项目及其合同项目的ABP已实例化月收入（abpOopBgt.income）
                            PrjAbpOopBgtBO parentBo = prjCode2BgtMap.get(parentProjectCode);
                            actIncome += (parentBo == null ? BigDecimalUtils.getDoublePercentHalfNum(prjAbpOopBgtBO.getIncome(),2) :
                                    BigDecimalUtils.getDoublePercentHalfNum(prjAbpOopBgtBO.getIncome(),2) +
                                            BigDecimalUtils.getDoublePercentHalfNum(parentBo.getIncome(),2));
                            allIncome +=DoubleUtil.getNotNull(prjAbpOopBgtBO.getIncome());
                        }else if (prjAbpOopBgtBO != null){
                            actIncome +=  BigDecimalUtils.getDoublePercentHalfNum(prjAbpOopBgtBO.getIncome(),2);
                            allIncome +=DoubleUtil.getNotNull(prjAbpOopBgtBO.getIncome());
                        }
                    }
                }
            }

            incomeAndRgfeeMap.put("forecastIncome",forecastIncome);
            incomeAndRgfeeMap.put("blIncome",blIncome);
            incomeAndRgfeeMap.put("blRgFee",blRgFee);
            incomeAndRgfeeMap.put("sumAllEmpAndDirectFee",sumAllEmpAndDirectFee);
            incomeAndRgfeeMap.put("actIncome",actIncome);
            incomeAndRgfeeMap.put("actRgFee",actRgFee);
            incomeAndRgfeeMap.put("allCostFee",allCostFee);
            incomeAndRgfeeMap.put("allIncome",allIncome);

            incomeAndRgfeeMap.put("blEmpFee",blEmpFee);
            incomeAndRgfeeMap.put("blOsEmpFee",blOsEmpFee);
            incomeAndRgfeeMap.put("blTraineeFee",blTraineeFee);
            incomeAndRgfeeMap.put("blJsfbFee",blJsfbFee);
            incomeAndRgfeeMap.put("blTravelFee",blTravelFee);
            incomeAndRgfeeMap.put("blDiningFee",blDiningFee);
            incomeAndRgfeeMap.put("blOtherFee",blOtherFee);
            incomeAndRgfeeMap.put("blEmpNum",blEmpNum);
            incomeAndRgfeeMap.put("blOsEmpNum",blOsEmpNum);

            incomeAndRgfeeMap.put("actEmpFee",actEmpFee);
            incomeAndRgfeeMap.put("actOsEmpFee",actOsEmpFee);
            incomeAndRgfeeMap.put("actTraineeFee",actTraineeFee);
            incomeAndRgfeeMap.put("actJsfbFee",actJsfbFee);
            incomeAndRgfeeMap.put("actTravelFee",actTravelFee);
            incomeAndRgfeeMap.put("actDiningFee",actDiningFee);
            incomeAndRgfeeMap.put("actOtherFee",actOtherFee);
            incomeAndRgfeeMap.put("actEmpNum",actEmpNum);
            incomeAndRgfeeMap.put("actOsEmpNum",actOsEmpNum);
            result.put(prjId,incomeAndRgfeeMap);
        }
        return result;
    }

    protected List<TePrjInfo> getContractPrjs(List<String> parentProjectCode){
        //合同项目获取方式： 子项目的parentProjectCode，对应的项目，若该父项目的 "projectForm" : "CONTRACT"，则认为是其合同项目
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_I<String>(DFN.prjInfo__prjCode,parentProjectCode));
        conds.add(new DC_E(DFN.prjInfo__projectForm,PrjConstant.PROJECT_FORM_CONTRACT));
        List<TePrjInfo> contractPrjList = prjInfoDao.findByFieldAndConds(conds, null);
        return contractPrjList;
    }
    //获取合同项目所在项目集
    protected List<TePrjInfo> getContractPrjSet(List<String> contractPrjCodes){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,false,true));
        conds.add(new DC_I<String>(DFN.prjInfo__subPrjs.dot(DFN.common_code),contractPrjCodes));
        conds.add(new DC_E(DFN.prjInfo__isPrjSet,true));
        List<TePrjInfo> contractPrjSets = prjInfoDao.findByFieldAndConds(conds, null);
        return contractPrjSets;
    }
    protected List<TePrjBudget> getPrjBudgetList(List<ObjectId> prjIds,List<ObjectId> prjBudgetVerIds){
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_I<ObjectId>(DFN.prjBudget_prjId,prjIds));
        conds.add(new DC_I<ObjectId>(DFN.prjBudget_VerId,prjBudgetVerIds));
        conds.add(new DC_E(DFN.prjBudget_ym,null,true));
        List<DbFieldName> fieldNameList = new ArrayList<>();
        fieldNameList.add(DFN.prjBudget_prjId);
        fieldNameList.add(DFN.prjBudget_ym);
        fieldNameList.add(DFN.prjBudget_jslfbFee);
        fieldNameList.add(DFN.prjBudget_sumEmpFee);
        fieldNameList.add(DFN.prjBudget_sumOsEmpFee);
        fieldNameList.add(DFN.prjBudget_sumAllEmpFee);
        fieldNameList.add(DFN.prjBudget_sumDirectFee);
        fieldNameList.add(DFN.prjBudget_actualIncome);
        fieldNameList.add(DFN.prjBudget_actualAllEmpFee);
        fieldNameList.add(DFN.prjBudget_actualJslfbFee);
        fieldNameList.add(DFN.prjBudget_actualDirectFee);
        fieldNameList.add(DFN.prjBudget_sumTraineeFee);
        fieldNameList.add(DFN.prjBudget_actualTraineeFee);
        List<TePrjBudget> prjBudgetList = prjBudgetDao.findByFieldAndConds(conds, fieldNameList);
        return prjBudgetList;
    }

    @Override
    public void sendPrjDelaySignMail(String sbuId,List<ObjectId> devMgtIds) {
        //查询偏差数据
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DFN.common__id, devMgtIds));
        conds.add(new DC_E(DFN.common_isValid,true));
        if (StringUtil.isNotNull(sbuId)){
            conds.add(new DC_E(DbFieldName.PrjDvtMgt.buCode,sbuId));
        }
        conds.add(new DC_E(DFN.PrjDvtMgt.prj.dot(DFN.common_cid),null,true));
        List<PrjDvtMgt> prjDvtMgtList = prjDvtMgtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(prjDvtMgtList)){
            throw BusinessException.initExc("偏差数据为空");
        }
        List<String> buCodeList = prjDvtMgtList.stream().filter(dvt -> StringUtil.isNull(dvt.getBuCode())).map(PrjDvtMgt::getBuCode).collect(Collectors.toList());
        Map<ObjectId, PrjDvtMgt> prjDvtMgtMap = prjDvtMgtList.stream().collect(Collectors.toMap(dvt -> dvt.getPrj().getCid(), Function.identity(),(v1,v2) -> v2));
        //获取项目id
        List<ObjectId> prjIds = prjDvtMgtList.stream().map(dvt -> dvt.getPrj().getCid()).collect(Collectors.toList());
        List<TePrjInfo> tePrjInfos = prjInfoService.queryPrjInfoByPrjdefId(prjIds);
        if (CollectionUtils.isEmpty(tePrjInfos)){
            throw BusinessException.initExc("项目数据为空");
        }
        tePrjInfos = tePrjInfos.stream().filter(prj -> prj.getPmUser() != null && prj.getPmUser().getUserId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tePrjInfos)){
            throw BusinessException.initExc("项目数据为空");
        }
        List<String> prjCodes = tePrjInfos.stream().map(TePrjInfo::getPrjCode).collect(Collectors.toList());
        //查询abpoop数据
        conds.clear();
        conds.add(new DC_E(DFN.common_isValid,true));
        conds.add(new DC_E(DFN.AbpOop.isDeleted,false));
        if (CollectionUtils.isNotEmpty(buCodeList)){
            conds.add(new DC_I<String>(DFN.AbpOop.buCode,buCodeList));
        }
        conds.add(new DC_I<String>(DFN.common_cn,prjCodes));
        conds.add(new DC_E(DFN.AbpOop.pm.dot(DFN.common_userId),null,true));
        conds.add(new DC_E(DFN.common_addTime,null,true));
        List<TeAbpOop> abpOopList = abpOopDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(abpOopList)){
            throw BusinessException.initExc("oop数据为空");
        }
        Map<String, Optional<TeAbpOop>> abpOopMapByPrjCode = abpOopList.stream().collect(Collectors.groupingBy(TeAbpOop::getCodeName, Collectors.maxBy((v1,v2) -> v1.getAddTime().compareTo(v2.getAddTime()))));
        //获取省份
        List<ObjectId> provIds = new ArrayList<>();
        //项目经理
        List<ObjectId> pmUserIds = new ArrayList<>();
        //查询项目管理员
        List<TeSysDefRoleUser> roleUserList = sysDefRoleUserService.getUsers(Arrays.asList(StringUtil.toObjectId(SysDefConstants.PRJADMIN_DEF_ID)), prjIds, null, SysDefTypeCodeName.PRJ);
        Map<ObjectId, ObjectId> prjAdminMap = null;
        Map<ObjectId, String> prjAdminMailBoxMap = null;
        if (CollectionUtils.isNotEmpty(roleUserList)){
            List<ObjectId> prjAdminUserIds = roleUserList.stream().filter(role -> role.getRoleUser() != null && role.getRoleUser().getUserId() != null).map(role -> role.getRoleUser().getUserId()).collect(Collectors.toList());
            prjAdminMap = roleUserList.stream().filter(role -> role.getRoleUser() != null
                    && role.getRoleUser().getUserId() != null).collect(Collectors.toMap(role -> role.getDefId(), role -> role.getRoleUser().getUserId(), (v1, v2) -> v2));
            if (CollectionUtils.isNotEmpty(prjAdminUserIds)){
                List<TeSysUser> prjAdmins = sysUserService.getUsersByIds(prjAdminUserIds);
                if (CollectionUtils.isNotEmpty(prjAdmins)){
                    prjAdminMailBoxMap = prjAdmins.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, TeSysUser::getMailBox, (v1, v2) -> v2));
                }
            }
        }
        Map<ObjectId,List<String>> pm2PrjCodeMap = new HashMap<>();
        Map<ObjectId,List<ObjectId>> pm2ProvMap = new HashMap<>();
        for (TePrjInfo prjInfo : tePrjInfos){
            String prjCode = prjInfo.getPrjCode();
            TePrjInfo2User pm = prjInfo.getPmUser();
            pmUserIds.add(pm.getUserId());
            List<String> prjCodeList = pm2PrjCodeMap.get(pm.getUserId());
            if (CollectionUtils.isEmpty(prjCodeList)){
                prjCodeList = new ArrayList<>();
            }
            prjCodeList.add(prjCode);
            pm2PrjCodeMap.put(pm.getUserId(),prjCodeList);
            /*if (MapUtils.isNotEmpty(prjAdminMap) && prjAdminMap.get(prjInfo.getPrjId()) != null){
                ObjectId prjAdminUserId = prjAdminMap.get(prjInfo.getPrjId());
                List<String> stringList = pm2PrjCodeMap.getOrDefault(prjAdminUserId, new ArrayList<>());
                stringList.add(prjCode);
                pm2PrjCodeMap.put(prjAdminUserId,stringList);
            }*/
            if (abpOopMapByPrjCode.get(prjCode) != null){
                TeAbpOop teAbpOop = abpOopMapByPrjCode.get(prjCode).get();
                TeIdNameCn prov = teAbpOop.getProv();
                if (prov != null && prov.getCid() != null){
                    provIds.add(prov.getCid());
                    List<ObjectId> provIdList = pm2ProvMap.get(pm.getUserId());
                    if (CollectionUtils.isEmpty(provIdList)){
                        provIdList = new ArrayList<>();
                    }
                    provIdList.add(prov.getCid());
                    pm2ProvMap.put(pm.getUserId(),provIdList);
                }
            }
        }
        //查询项目经理
        List<TeSysUser> pmUserList = sysUserService.getUsersByIds(pmUserIds);
        if (CollectionUtils.isEmpty(pmUserList)){
            return;
        }
        Map<ObjectId, TeSysUser> toListMap = pmUserList.stream().filter(user -> StringUtil.isNotNull(user.getMailBox())).collect(Collectors.toMap(TeSysUser::getId, Function.identity()));
        if (MapUtils.isEmpty(toListMap)){
            return;
        }
        List<String> loginNames = new ArrayList<>();
        //省份PSO经理、省份PSO总监
        List<ObjectId> roleIds = new ArrayList<ObjectId>();
        roleIds.add(PrjConstant.DEF_ID_MANAGER_ROLE);
        List<TeSysDefRoleUser> managerList = sysDefRoleUserService.getUsers(roleIds, provIds, null);
        Map<ObjectId, List<String>> managerMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(managerList)){
            List<String> collect = managerList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName())).map(user -> user.getRoleUser().getLoginName()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                loginNames.addAll(collect);
            }
            managerMap = managerList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName()))
                    .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId,Collectors.mapping(user -> user.getRoleUser().getLoginName(),Collectors.toList())));
        }
        //省份PSO总监
        roleIds.clear();
        roleIds.add(PrjConstant.DEF_ID_DIRECTOR_ROLE);
        List<TeSysDefRoleUser> directorList = sysDefRoleUserService.getUsers(roleIds, provIds, null);
        Map<ObjectId, List<String>> directorMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(directorList)){
            List<String> collect = directorList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName())).map(user -> user.getRoleUser().getLoginName()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                loginNames.addAll(collect);
            }
            directorMap = directorList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName()))
                    .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId,Collectors.mapping(user -> user.getRoleUser().getLoginName(),Collectors.toList())));
        }
        //查询省份运营管理员
        roleIds.clear();
        roleIds.add(PrjConstant.DEF_ID_BU_ROLE);
        List<TeSysDefRoleUser> operateAdminList = sysDefRoleUserService.getUsers(roleIds, provIds, SysDefTypeCodeName.ABP_PROV);
        Map<ObjectId, List<String>> operateAdminListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(operateAdminList)){
            List<String> collect = operateAdminList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName())).map(user -> user.getRoleUser().getLoginName()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)){
                loginNames.addAll(collect);
            }
            operateAdminListMap = operateAdminList.stream().filter(user -> user.getRoleUser() != null && StringUtil.isNotNull(user.getRoleUser().getLoginName()))
                    .collect(Collectors.groupingBy(TeSysDefRoleUser::getDefId,Collectors.mapping(user -> user.getRoleUser().getLoginName(),Collectors.toList())));

        }
        List<String> otherLoginNames = Arrays.asList("zhanglw5", "nixue", "luoyl", "make", "zhaobh", "lihf3");
        loginNames.addAll(otherLoginNames);
        List<TeSysUser> otherUserList = sysUserService.getUsersByLoginNames(loginNames);

        Set<ObjectId> dvtIdSet = new HashSet<>();
        List<MailInfo> mailInfoList = new ArrayList<>();
        for (TeSysUser pmUser : pmUserList){
            if (StringUtil.isNull(pmUser.getMailBox())){
                continue;
            }
            List<String> prjCodeList = pm2PrjCodeMap.get(pmUser.getId());
            if (CollectionUtils.isEmpty(prjCodeList)){
                continue;
            }
            List<TePrjInfo> prjInfoList = tePrjInfos.stream().filter(prj -> prjCodeList.contains(prj.getPrjCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(prjInfoList)){
                continue;
            }
            List<String> toList = new ArrayList<>();
            toList.add(pmUser.getMailBox());
            List<PrjDvtMgtMailRcdInfoVo> rcdInfoVos = new ArrayList<>();
            for (TePrjInfo prjInfo : prjInfoList){
                if (MapUtils.isNotEmpty(prjAdminMap) && MapUtils.isNotEmpty(prjAdminMailBoxMap)){
                    ObjectId prjAdminUserId = prjAdminMap.get(prjInfo.getPrjId());
                    if (prjAdminUserId != null && StringUtil.isNotNull(prjAdminMailBoxMap.get(prjAdminUserId))){
                        String prjAdminMailBox = prjAdminMailBoxMap.get(prjAdminUserId);
                        if (!toList.contains(prjAdminMailBox)){
                            toList.add(prjAdminMailBox);
                        }
                    }
                }
                PrjDvtMgt prjDvtMgt = prjDvtMgtMap.get(prjInfo.getPrjId());
                if (prjDvtMgt == null){
                    continue;
                }
                dvtIdSet.add(prjDvtMgt.getId());
                PrjDvtMgtMailRcdInfoVo vo = new PrjDvtMgtMailRcdInfoVo();
                vo.setPrjCode(prjInfo.getPrjCode());
                vo.setPrjName(prjInfo.getPrjName());
                vo.setOaPrjStartDate(prjInfo.getOaPrjStartDate());
                RcdInfo rcdInfo = prjDvtMgt.getRcdInfo();
                vo.setProjectedDays(rcdInfo == null ? null : rcdInfo.getProjectedDays());
                rcdInfoVos.add(vo);
            }
            //省份id
            List<ObjectId> provIdList = pm2ProvMap.get(pmUser.getId());
            //抄送人
            Set<String> reveiceLoginNames = new HashSet<>();
            reveiceLoginNames.addAll(otherLoginNames);
            if (CollectionUtils.isNotEmpty(provIdList)){
                for (ObjectId provId : provIdList){
                    List<String> managerLoginNames = managerMap.get(provId);
                    if (CollectionUtils.isNotEmpty(managerLoginNames)){
                        reveiceLoginNames.addAll(managerLoginNames);
                    }
                    List<String> directorLoginNames = directorMap.get(provId);
                    if (CollectionUtils.isNotEmpty(directorLoginNames)){
                        reveiceLoginNames.addAll(directorLoginNames);
                    }
                    List<String> operateAdminLoginNames = operateAdminListMap.get(provId);
                    if (CollectionUtils.isNotEmpty(operateAdminLoginNames)){
                        reveiceLoginNames.addAll(operateAdminLoginNames);
                    }
                }
            }
            //抄送人邮箱
            List<String> mailBoxs = otherUserList.stream().filter(user -> reveiceLoginNames.contains(user.getLoginName())
                    && StringUtil.isNotNull(user.getMailBox())).map(TeSysUser::getMailBox).collect(Collectors.toList());

            PrjDvtMgtMailVo mailVo = new PrjDvtMgtMailVo();
            mailVo.setPmUserName(pmUser.getUserName());
            mailVo.setSendTime(DateUtil.getCurrentTime(DateUtil.DATE_FORMAT));

            mailVo.setRcdInfoVos(rcdInfoVos);
            MailInfo mailInfo = new MailInfo();
            mailInfo.setServerType(MailInfo.MAIL_COMMON_SERVER_TYPE);
            mailInfo.setFromMailboxName("<EMAIL>");
            mailInfo.setToList(toList);
            mailInfo.setCcList(mailBoxs);
            mailInfo.setSubject("异常偏差反馈提醒");
            mailInfo.setSubsystem(DMPSubsystem.PMS);
            mailInfo.setContent(getMailContent(mailVo, PrjConstant.PRJ_DVT_MGT_DELAY_SIGN_ID));
            mailInfoList.add(mailInfo);
        }
        if (CollectionUtils.isNotEmpty(mailInfoList)){
            mailService.sendMails(mailInfoList);
        }
        if (CollectionUtils.isNotEmpty(dvtIdSet)){
            List<IDbCondition> updateConds = new ArrayList<>();
            updateConds.add(new DC_I<>(DFN.common__id, new ArrayList<>(dvtIdSet)));
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DFN.PrjDvtMgt.isMailed, true));
            prjDvtMgtDao.updateByConds(updateConds, updates);
        }
    }
    //查询该类偏差的上一次的“首版计划完成日期”
    protected Map<ObjectId,String> getFirstVerPlanEndDate(List<ObjectId> prjIds,String ym,ObjectId typeId){
        Map<ObjectId,String> firstVerPlanEndDateMap = new HashMap<>();
        List<Document> sql = new ArrayList<>();
        Document match = new Document();
        match.append(DFN.common_isValid.n(),true);
        match.append(DbFieldName.PrjDvtMgt.prj.dot(DFN.common_cid).n(),new Document("$in",prjIds));
        match.append(DbFieldName.PrjDvtMgt.ym.n(),new Document("$lt",ym));
        match.append(DbFieldName.PrjDvtMgt.type.dot(DFN.common_cid).n(),typeId);
        match.append(DbFieldName.PrjDvtMgt.rcdInfo.dot(DbFieldName.PrjDvtMgt.firstVerPlanEndDate).n(),new Document("$nin",Arrays.asList("",null)));
        sql.add(new Document("$match",match));
        sql.add(new Document("$sort",new Document("ym",-1)));
        sql.add(new Document("$group",new Document("_id","$prj.cid").append("firstVerPlanEndDate",new Document("$first","$rcdInfo.firstVerPlanEndDate"))));
        MongoCursor<Document> cursor =mongoTemplate.getCollection(DBT.PRJ_DVT_MGT.n()).aggregate(sql, Document.class).allowDiskUse(true).cursor();
        while (cursor.hasNext()){
            Document next = cursor.next();
            ObjectId prjId = next.getObjectId("_id");
            String firstVerPlanEndDate = next.getString("firstVerPlanEndDate");
            firstVerPlanEndDateMap.put(prjId,firstVerPlanEndDate);
        }
        return firstVerPlanEndDateMap;
    }
}
