package com.linkus.prj.constant;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import org.bson.types.ObjectId;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PrjConstant {
	//考评统计之考评状态
	public static String PRJ_KPSTATUS_NOREVIEW = "未考评";
	public static String PRJ_KPSTATUS_PROCESSREVIEW = "过程考评";
	public static String PRJ_KPSTATUS_PROCESSREVIEW_CODENAME = "processReview";
	public static String PRJ_KPSTATUS_PRELIMINARYREVIEW = "初评";
	public static String PRJ_KPSTATUS_FINALREVIEW = "终评/结果";
		
	// 二级计划任务标识
	public static String PRJ_TASKFLAG_JD = "阶段任务";
	public static String PRJ_TASKFLAG_HS = "行首子任务";
	public static String PRJ_TASKFLAG_NHS = "非行首子任务";

	// 干系人姓名
	public static String PRJ_MATTERPSNS_NAME = "* 姓名";
	// 干系人NT账号
	public static String PRJ_MATTERPSNS_LOGIN_NAME = "NT账号（亚信侧人员必填）";
	// 干系人电话
	public static String PRJ_MATTERPSNS_PHONE = "电话";
	// 干系人E-MAIL
	public static String PRJ_MATTERPSNS_MAILBOX = "邮箱";
	// 干系人重要性
	public static ObjectId PRJ_MATTERPSNS_IMPORTLEVEL = new ObjectId("5df72642c56a424c334704e0");
	public static String PRJ_MATTERPSNS_IMPORTLEVEL1 = "重要性";
	// 干系人部门
	public static String PRJ_MATTERPSNS_DEPT = "部门（亚信侧人员必填）";
	// 干系人职务
	public static String PRJ_MATTERPSNS_POSITION = "职务";
	// 干系人所属公司
	public static String PRJ_MATTERPSNS_COMPANY = "归属省份（客户侧人员必填）";
	// 干系人参与度
	public static ObjectId PRJ_MATTERPSNS_JOINLEVEL = new ObjectId("5df7266bc56a424c334704e1");
	public static String PRJ_MATTERPSNS_JOINLEVEL1 = "参与度";
	// 干系人立场
	public static ObjectId PRJ_MATTERPSNS_STANDPOINT = new ObjectId("5df72694c56a424c334704e2");
	public static String PRJ_MATTERPSNS_STANDPOINT1 = "立场";
	// 干系人个人诉求或期望
	public static String PRJ_MATTERPSNS_EXPECT = "个人诉求或期望";
	// 干系人负责内容
	public static String PRJ_MATTERPSNS_DUTY = "负责内容";
	// 干系人个人工作特点
	public static String PRJ_MATTERPSNS_TRAIT = "个人工作特点";
	// 干系人个人爱好
	public static String PRJ_MATTERPSNS_HOBBY = "个人爱好";
	// 干系人沟通机制
	public static String PRJ_MATTERPSNS_TALKCHANNEL = "沟通机制";
	// 干系人备注
	public static String PRJ_MATTERPSNS_NOTE = "备注";

	// 干系人类型
	public static ObjectId PRJ_MATTERPSNS_TYPE = new ObjectId("5df725bbc56a424c334704df");
	public static String PRJ_MATTERPSNS_TYPE1 = "* 干系人类型";
	public static String PRJ_MATTERPSNS_TYPE_NAME = "亚信侧";
	public static String PRJ_MATTERPSNS_CUSTOMER_TYPE_NAME = "客户侧";
	// 省份字段
	public static String EDIT_TYPE_PROVINCE = "province";
	// 省份字段
	public static String EDIT_TYPE_LEVEL = "level";
	// 项目总监
	public static String EDIT_TYPE_GM = "Gm";
	// Pso总监
	public static String EDIT_TYPE_PSO = "Pso";
	// 项目周期
	public static String EDIT_TYPE_TIME_RANGE = "TimeRange";
	public static String EDIT_TYPE_SUBPRJ_TIME_RANGE = "subPrjTimeRange";
	// Srd总监
	public static String EDIT_TYPE_SRD = "Srd";
	// Srd资源部门变更
	public static String EDIT_TYPE_SRD_List = "SrdList";
	// 添加子项目清单
	public static String EDIT_TYPE_SUB_PRJS = "SubPrjs";
	// 删除子项目清单
	public static String EDIT_TYPE_DELETE_SUB_PRJS = "deleteSubPrjs";
	// 修改项目背景
	public static String EDIT_TYPE_BACKGROUND = "background";
	// 修改GM影响说明
	public static String EDIT_TYPE_GM_EFFECT = "gmEffect";
	// 修改成本
	public static String EDIT_TYPE_GM_COST = "cost";
	// 修改项目管理类型
	public static String EDIT_TYPE_PRJ_MGT_TYPE = "prjMgtType";
	// 项目省份等级--A
	public static ObjectId PRJ_LEVEL_A = new ObjectId("5c6e3917900add501a141902");
	// 项目省份等级--A+
	public static ObjectId PRJ_LEVEL_A_PLUS = new ObjectId("5c88a3b6900add501a14195c");
	// 项目省份等级--B
	public static ObjectId PRJ_LEVEL_B = new ObjectId("5c6e3961900add501a141903");
	// 项目省份等级--B+
	public static ObjectId PRJ_LEVEL_B_PLUS = new ObjectId("5c88a3d0900add501a14195d");
	// 项目省份等级--C
	public static ObjectId PRJ_LEVEL_C = new ObjectId("5c6e397f900add501a141904");
	// 项目省份等级--D
	public static ObjectId PRJ_LEVEL_D = new ObjectId("5c88a3ed900add501a14195e");
	// 项目等级 - E
	public static ObjectId PRJ_LEVEL_E = new ObjectId("6389d15d64586deda477a1bc");
	// 项目等级 - F
	public static ObjectId PRJ_LEVEL_F = new ObjectId("6389d16d64586deda477a386");
	// 项目等级 - G
	public static ObjectId PRJ_LEVEL_G = new ObjectId("6389d17d64586deda477a6c3");
	// 项目等级 - X
	public static ObjectId PRJ_LEVEL_X = new ObjectId("634fae4d95f12ffd0ba1cd8a");
	public static List<ObjectId> PRJ_LEVEL_LIST_A_B_C_E_F_G = Arrays.asList(
			new ObjectId("5c6e3917900add501a141902"),
			new ObjectId("5c88a3b6900add501a14195c"),
			new ObjectId("5c6e3961900add501a141903"),
			new ObjectId("5c88a3d0900add501a14195d"),
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("6389d15d64586deda477a1bc"),
			new ObjectId("6389d16d64586deda477a386"),
			new ObjectId("6389d17d64586deda477a6c3")
	);
    public static List<ObjectId> PRJ_LEVEL_LIST_A_B = Arrays.asList(
            new ObjectId("5c6e3917900add501a141902"),
            new ObjectId("5c88a3b6900add501a14195c"),
            new ObjectId("5c6e3961900add501a141903"),
            new ObjectId("5c88a3d0900add501a14195d")
    );
	public static List<ObjectId> PRJ_LEVEL_LIST_A_B_C = Arrays.asList(
			new ObjectId("5c6e3917900add501a141902"),
			new ObjectId("5c88a3b6900add501a14195c"),
			new ObjectId("5c6e3961900add501a141903"),
			new ObjectId("5c88a3d0900add501a14195d"),
			new ObjectId("5c6e397f900add501a141904")
	);
    public static List<ObjectId> PRJ_LEVEL_LIST_C_D = Arrays.asList(
            new ObjectId("5c6e397f900add501a141904"),
            new ObjectId("5c88a3ed900add501a14195e")
    );
    public static List<ObjectId> PRJ_LEVEL_LIST_E_F_G = Arrays.asList(
            new ObjectId("6389d15d64586deda477a1bc"),
            new ObjectId("6389d16d64586deda477a386"),
            new ObjectId("6389d17d64586deda477a6c3")
    );
	public static List<ObjectId> PRJ_LEVEL_LIST_C_E_F_G = Arrays.asList(
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("6389d15d64586deda477a1bc"),
			new ObjectId("6389d16d64586deda477a386"),
			new ObjectId("6389d17d64586deda477a6c3")
	);
	public static List<ObjectId> PRJ_LEVEL_LIST_E_F = Arrays.asList(
			new ObjectId("6389d15d64586deda477a1bc"),
			new ObjectId("6389d16d64586deda477a386")
	);
	public static List<ObjectId> PRJ_LEVEL_LIST_C_D_G = Arrays.asList(
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("5c88a3ed900add501a14195e"),
			new ObjectId("6389d17d64586deda477a6c3")
	);
	public static List<ObjectId> PRJ_LEVEL_LIST_C_G = Arrays.asList(
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("6389d17d64586deda477a6c3")
	);
	public static List<ObjectId> PRJ_LEVEL_LIST_C_D_E_F_G = Arrays.asList(
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("5c88a3ed900add501a14195e"),
			new ObjectId("6389d15d64586deda477a1bc"),
			new ObjectId("6389d16d64586deda477a386"),
			new ObjectId("6389d17d64586deda477a6c3")
	);
	public static List<ObjectId> PRJ_LEVEL_LIST_A_B_C_G = Arrays.asList(
			new ObjectId("5c6e3917900add501a141902"),
			new ObjectId("5c88a3b6900add501a14195c"),
			new ObjectId("5c6e3961900add501a141903"),
			new ObjectId("5c88a3d0900add501a14195d"),
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("6389d17d64586deda477a6c3")
	);

	public static List<ObjectId> PRJ_LEVEL_LIST_A_B_C_E = Arrays.asList(
			new ObjectId("5c6e3917900add501a141902"),
			new ObjectId("5c88a3b6900add501a14195c"),
			new ObjectId("5c6e3961900add501a141903"),
			new ObjectId("5c88a3d0900add501a14195d"),
			new ObjectId("5c6e397f900add501a141904"),
			new ObjectId("6389d15d64586deda477a1bc")
	);
	// 项目基准版本-Cid
	public static ObjectId PRJ_BMK_VER_CID = new ObjectId("5c6fa61f900add501a14190f");
	// 项目基准版本-名称
	public static String PRJ_BMK_VER_NAME = "项目基准版本";
	// 项目基准版本-codeName
	public static String PRJ_BMK_VER_CODENAME = "prjBmkVer";
	// 初始化基准项目操作
	public static String PRJ_BMK_INIT_OPERATION = "prjInit";

	// 项目范围基准版本-Cid
	public static ObjectId PRJ_SCOPE_VER_CID = new ObjectId("5c6fa728900add501a141913");
	// 项目范围基准版本-名称
	public static String PRJ_SCOPE_VER_NAME = "项目范围基准版本";
	// 项目范围基准版本-codeName
	public static String PRJ_SCOPE_VER_CODENAME = "prjScopeVer";

	// 项目成本基准版本-Cid
	public static ObjectId PRJ_BUDGET_VER_CID = new ObjectId("5c6fa69f900add501a141910");
	// 项目成本基准版本-名称
	public static String PRJ_BUDGET_VER_NAME = "项目成本基准版本";
	// 项目成本基准版本-codeName
	public static String PRJ_BUDGET_VER_CODENAME = "prjBudgetVer";

	// 项目里程碑基准版本-Cid
	public static ObjectId PRJ_MSPLAN_VER_CID = new ObjectId("5c6fa6cf900add501a141911");
	// 项目里程碑基准版本-名称
	public static String PRJ_MSPLAN_VER_NAME = "项目里程碑基准版本";
	// 项目里程碑基准版本-codeName
	public static String PRJ_MSPLAN_VER_CODENAME = "prjMsPlanVer";

	// 项目质量基准版本-Cid
	public static ObjectId PRJ_QUALITY_VER_CID = new ObjectId("5c70acad900add501a141914");
	// 项目质量基准版本-名称
	public static String PRJ_QUALITY_VER_NAME = "项目质量基准版本";
	// 项目质量基准版本-codeName
	public static String PRJ_QUALITY_VER_CODENAME = "prjQualityVer";

	// 特殊状态-已关闭
	public static final ObjectId DEF_ID_SPECIAL_STATUS_CLOSED = new ObjectId("5c304774900add501a1414a0");
	// 特殊状态-已取消
	public static final ObjectId DEF_ID_SPECIAL_STATUS_CANCELLED = new ObjectId("5c30477d900add501a1414a1");

	// 里程碑任务--Cid
	public static ObjectId MILESTONE_CID = new ObjectId("5a557901ba4014a42391faad");
	// 里程碑任务--名称
	public static String MILESTONE_NAME = "里程碑";
	// 里程碑任务--CodeName
	public static String MILESTONE_CODENAME = "msTask";
	// TASKROLEUSER defId
	public static ObjectId TASKROLEUSER_ROLE_DEFID = new ObjectId("5a3a19e4ba4014a42391fa11");
	// TASKROLEUSER Name
	public static String TASKROLEUSER_ROLE_NAME = "负责人";

	public static String PRJ_QUALITY_PARA = "prjQualityPara";
	
	// BSC项目集EFG等级质量基准参数：考核报告（默认，参考客户考核报告的扣分情况）、项目故障（默认，参考BU运营管理部门的故障通告情况）
	public static List<ObjectId> PRJ_QUALITY_PARA_OF_BSC_LEVEL_EFG = Arrays.asList(new ObjectId("6389a57a64586deda47232b0"), new ObjectId("6389a59764586deda472383c"));

	public static List<ObjectId> PRJ_QUALITY_PARA_ID = Arrays.asList(new ObjectId("662db0116658b395918bccc4"), new ObjectId("662dafe06658b395918bc9f3"), new ObjectId("662daf9a6658b395918bc684"));

	public static ObjectId PRJ_QUALITY_FIRST_PAY_ID = new ObjectId("5c6f817d900add501a14190d");
	// 项目质量--value
	public static String PRJ_QUALITY_VALUE = "value";
	// 项目质量--desc
	public static String PRJ_QUALITY_DESC = "desc";
	// 项目质量--desc
	public static String PRJ_SCOPE = "prjScope";

	// 序号
	public static String PRJ_NO = "序号";
	// 一级功能
	public static String PRJ_ONE_FUNDATION = "一级功能";
	// 二级功能
	public static String PRJ_TWO_FUNDATION = "二级功能";
	// 三级功能
	public static String PRJ_THREE_FUNDATION = "三级功能";

	// 项目范围提交
	public static String PRJ_SCOPE_VER = "prjScopeVer";
	// 项目成本提交
	public static String PRJ_BUDGER_VER = "prjBudgetVer";
	public static String PRJ_BUDGET_VER_NEW = "newPrjBudgetVer";
	// 项目进度
	public static String PRJ_MS_PLAN_VER = "prjMsPlanVer";
	// 项目质量
	public static String PRJ_QUALITY_VER = "prjQualityVer";
	// 提交项目基准操作
	public static String PRJ_SUBMIT_PRJ_BASE_OPRT = "submitPrjBaseOprt";

	public static String PRJ_SCOPE_VER_IS_SUBMITTED = "prjScopeVerIsSubmitted";

	public static String PRJ_BUDGET_VER_IS_SUBMITTED = "prjBudgetVerIsSubmitted";

	public static String PRJ_MS_PLAN_VER_IS_SUBMITTED = "prjMsPlanVerIsSubmitted";

	public static String PRJ_QUALITY_VER_IS_SUBMITTED = "prjQualityVerIsSubmitted";

	// 公司统一项目模板
	public static ObjectId CORPORATE_UNIFIED_PROJECT_TEMPLATE = new ObjectId("5c8600a2900add501a14194e");

	public static ObjectId PRJ_SUBMIT_NODE = new ObjectId("5c501fef900add501a1418c6");

	// 初始化项目基准
	public static ObjectId INIT_PRJ_BENCHMARKS = new ObjectId("5c70b22c900add501a141915");
	// CCD监军府审核版本
	public static ObjectId CCD_SUPERVISORY_MILITARY_GOVERNMENT_AUDIT_VER = new ObjectId("5f6853754955e65618e232cf");
	// 项目经理提交基准
	public static ObjectId PRJ_MANAGER_SUBMIT_BENCHMARKS = new ObjectId("5c501fef900add501a1418c6");
	public static String PRJ_MANAGER_SUBMIT_BENCHMARKS_NAME = "项目经理提交基准";
	// CCD监军府审核基准
	public static ObjectId CCD_SUPERVISORY_MILITARY_GOVERNMENT_AUDIT_BENCHMARKS = new ObjectId(
			"5c500631900add501a1418bf");
	// PSO/SRD责任总监审批
	public static ObjectId PSO_SRD_RESPONSIBILITY_DIRECTOR_APPROVAL = new ObjectId("5c500651900add501a1418c0");
	// CCD监军府审核并通告
	public static ObjectId CCD_SUPERVISORY_MILITARY_GOVERNMENT_INSPECTION_AND_NOTIFICATION = new ObjectId(
			"5c500684900add501a1418c1");
	//过程考评
	public static ObjectId PRJ_PROCESS_REVIEW_ID = new ObjectId("5d1ea02d900add501a1425a4");
	//过程考评-阶段性进展
	public static ObjectId PRJ_PROCESS_Review_Phase_Schedules = new ObjectId("5d1ea0f7900add501a1425a5");
	//过程考评-管理规范化
	public static ObjectId PRJ_PROCESS_Review_MgtStandard = new ObjectId("5d1ea15d900add501a1425a6");
	//过程考评-成本偏差率
	public static ObjectId PRJ_PROCESS_Review_CostDev = new ObjectId("5d1ea1ee900add501a1425a8");

	//过程考评-阶段性进展
	public static ObjectId PRJ_PROCESS_Review_Phase_Schedules_New = new ObjectId("6674e3744bdbf85140a08fd4");
	//过程考评-管理规范化
	public static ObjectId PRJ_PROCESS_Review_MgtStandard_New = new ObjectId("6674e3744bdbf85140a08fd5");
	//过程考评-成本偏差率
	public static ObjectId PRJ_PROCESS_Review_CostDev_New = new ObjectId("6674e3744bdbf85140a08fd6");


	public static ObjectId PRJ_Preliminary_Review_Id = new ObjectId("5d1f09f3900add501a1425ab");

	public static String PRJ_Preliminary_Review_CodeName = "prjPreliminaryReview";

	public static String PRJ_Preliminary_Review_Name = "项目初评";

	//旧：舍弃不再用
	// 项目初评-项目过程
	public static ObjectId PRJ_Preliminary_Review_Process = new ObjectId("5d1f0a5b900add501a1425ac");
	// 项目初评-进度
	public static ObjectId PRJ_Preliminary_Review_Schedule = new ObjectId("5d1f0b09900add501a1425af");
	// 项目初评-成本
	public static ObjectId PRJ_Preliminary_Review_Cost = new ObjectId("5d1f0b22900add501a1425b0");
	// 项目初评-质量
	public static ObjectId PRJ_Preliminary_Review_Quality = new ObjectId("5d1f0b3e900add501a1425b1");
	// 项目初评-客户满意度
	public static ObjectId PRJ_Preliminary_Review_CustomerSatisfaction = new ObjectId("5d1f0c1b900add501a1425b2");
	// 项目初评-团队流失率
	public static ObjectId PRJ_Preliminary_Review_TeamOutflow = new ObjectId("5d1f0c92900add501a1425b3");
	// 项目初评-项目价值
	public static ObjectId PRJ_Preliminary_Review_Prj_Value = new ObjectId("6672aaf4d70673a94a34422d");
	// 项目初评-项目故障
	public static ObjectId PRJ_Preliminary_Review_Prj_Defect = new ObjectId("6672ab26d70673a94a3445e0");

	//新：项目初评和项目终评使用相同考评项
	// 项目考评-项目过程
	public static ObjectId PRJ_Review_Process_NEW = new ObjectId("6674e3744bdbf85140a08fd7");
	// 项目考评-进度
	public static ObjectId PRJ_Review_Schedule_NEW = new ObjectId("6674e3754bdbf85140a08fd8");
	// 项目考评-成本
	public static ObjectId PRJ_Review_Cost_NEW = new ObjectId("6674e3754bdbf85140a08fd9");
	// 项目考评-质量
	public static ObjectId PRJ_Review_Quality_NEW = new ObjectId("6674e3754bdbf85140a08fda");
	// 项目考评-范围
	public static ObjectId PRJ_Review_Scope_NEW = new ObjectId("6674e3754bdbf85140a08fdc");
	// 项目考评-效能
	public static ObjectId PRJ_Review_Efficiency_NEW = new ObjectId("6674e3764bdbf85140a08fe1");
	// 项目考评-客户满意度
	public static ObjectId PRJ_Review_CustomerSatisfaction_NEW = new ObjectId("6674e3764bdbf85140a08fe2");
	// 项目考评-项目价值
	public static ObjectId PRJ_Review_Prj_Value_NEW = new ObjectId("6674e3774bdbf85140a08fe6");
	// 项目考评-项目故障
	public static ObjectId PRJ_Review_Prj_Defect_NEW = new ObjectId("6674e3774bdbf85140a08fe7");
	// 项目考评-项目投诉
	public static ObjectId PRJ_Review_Prj_Complain_NEW = new ObjectId("67d23f2aec5e633697b1a232");
	// 项目考评-数字化生产
	public static ObjectId PRJ_Review_Digital_Production_NEW = new ObjectId("67d23f79ec5e633697b1ac2e");
	// 项目考评-基准效能
	public static ObjectId PRJ_Review_Bmk_Effect_NEW = new ObjectId("66821692d70673a94a2419bd");
	// 项目考评-实际效能
	public static ObjectId PRJ_Review_Act_Effect_NEW = new ObjectId("668216a1d70673a94a241bfd");
	// 项目考评-进度—里程碑
	public static ObjectId PRJ_Review_Milestone_NEW = new ObjectId("668216cfd70673a94a2420d2");

	public static ObjectId PRJ_Final_Review_Id = new ObjectId("5d209023900add501a1425b4");

	public static String PRJ_Final_Review_CodeName = "prjFinalReview";

	public static String PRJ_Final_Review_Name = "项目终评";
	//费用类型
	public static String PRJ_Fee_Type_DirectCostName = "directCost";//直接费用
	
	public static String PRJ_Fee_Type_TravelFeeName = "travelFee";//差旅
	
	public static String PRJ_Fee_Type_DiningFeeName = "diningFee";//餐饮
	
	public static String PRJ_Fee_Type_OtherFeeName = "otherFee";//其它

	//旧：舍弃不再用
	// 项目终评-项目过程
	public static ObjectId PRJ_Final_Review_Process = new ObjectId("5d20a37e0cdcf7d12ddcb4bc");
	// 项目终评-进度
	public static ObjectId PRJ_Final_Review_Schedule = new ObjectId("5d20a37e0cdcf7d12ddcb4bd");
	// 项目终评-成本
	public static ObjectId PRJ_Final_Review_Cost = new ObjectId("5d20a37e0cdcf7d12ddcb4be");
	// 项目终评-质量
	public static ObjectId PRJ_Final_Review_Quality = new ObjectId("5d20a37e0cdcf7d12ddcb4bf");
	// 项目终评-范围
	public static ObjectId PRJ_Final_Review_Scope = new ObjectId("5d20a37e0cdcf7d12ddcb4c0");
	// 项目终评-客户满意度
	public static ObjectId PRJ_Final_Review_CustomerSatisfaction = new ObjectId("5d20a37e0cdcf7d12ddcb4c1");
	// 项目终评-团队流失率
	public static ObjectId PRJ_Final_Review_TeamOutflow = new ObjectId("5d20a37e0cdcf7d12ddcb4c2");
	// 项目终评-项目价值
	public static ObjectId PRJ_Final_Review_Prj_Value = new ObjectId("6672ac74d70673a94a346022");
	// 项目终评-项目故障
	public static ObjectId PRJ_Final_Review_Prj_Defect = new ObjectId("6672acdbd70673a94a34660e");

	public static final String PRJ_EMP_FEE_PER_DAY = "empFeePerDay";

	public static final String PRJ_AVERAGE_WORKDAYS_PER_MONTH = "averageWorkDaysPerMonth";

	public static final double PRJ_PROGRESS_ZERO_POINT = 0d;
	public static final double PRJ_PROGRESS_NORMAL_POINT = 5d;
	public static final String PRJ_PROGRESS_NORMAL_DESC = "正常";
	public static final double PRJ_PROGRESS_SLIGHTDELAY_POINT = 10d;
	public static final String PRJ_PROGRESS_SLIGHTDELAY_DESC = "略有延迟";
	public static final double PRJ_PROGRESS_DELAY_POINT = 30d;
	public static final String PRJ_PROGRESS_DELAY_DESC = "延迟";
	public static final double PRJ_PROGRESS_SEVEREDELAY_POINT = 50d;
	public static final String PRJ_PROGRESS_SEVEREDELAY_DESC = "严重延迟";

	public static final String PRJ_POC_TARGET_CURYEAREND_DEFAULT = "当年底POC目标（默认值）";
	public static final String PRJ_POC_TARGET_CURYEAREND_REGULATION = "当年底POC目标（调控值）";
	public static final String PRJ_POC_TARGET_GOLINEPARERYM_DEFAULT = "上线报告签署当月POC目标（默认值）";
	public static final String PRJ_POC_TARGET_GOLINEPARERYM_REGULATION = "上线报告签署当月POC目标（调控值）";

	public static final ObjectId PRJ_BIGREGION_PAK_ID = new ObjectId("5de8a409c56a424c334704a5");
	public static final ObjectId PRJ_ABP_BIGREGION_PAK_ID = new ObjectId("62e8d2f13db01364cdf11364");
	public static final String PRJ_TYPE_PROVFORPRJ = "provForPrj";

	// 项目报告模板1
	public static final String PROJECT_REPORT_EXPORT_TEMPLATE1 = "prjReportWeek.html";
	
	// 项目报告模板2(无加班强度)
	public static final String PROJECT_REPORT_EXPORT_TEMPLATE2 = "prjReportMonth.html";
	
	// 项目月报
	public static final String PROJECT_MONTHLY_REPORT = "项目月报";

	// 获取工时内容填写, 倒推月份个数定义配置ID
	public static final ObjectId ALLOWED_MONTH_NUM_SYSDEFID = new ObjectId("5e4e01cbc56a424c33470623");
	
	public static final ObjectId PRJ_STATUS_FIELD_ID = new ObjectId("5dc3c0dac56a424c33470464");

	public static final String PRJ_STATUS_CODENAME = "status";
	
	//项目健康度：10个存量的项目，给定质量分4.5分计算健康度
	public static final ObjectId[] PRJ_QUALITY_HEALTH_SPECIAL_IDS = new ObjectId[] {
		new ObjectId("5dc38ebee0ee7751405220bf"), //甘肃移动2019年CRM扩容
		new ObjectId("5dc27a0be0ee775140521db5"), //咪咕一级交易子系统二期
		new ObjectId("5ce8788ae0ee7777b935cffa"), //江西移动19年BOSS系统扩容工程软件采购
		new ObjectId("5dc26ca3e0ee775140521d12"), //2018年中移在线通信云交付
		new ObjectId("5d383a98d93fa83410a5cc74"), //江西移动2019年CRM系统扩容工程软件采购
		new ObjectId("5dc923d7e0ee775d89506753"), //浙江移动中台赋能提升
		new ObjectId("5dd4f9e1e0ee77720bc9e680"), //集中化大数据平台项目二期
		new ObjectId("5dd4fa33e0ee77720bc9e687"), //辽宁移动2019年CRM扩容
		new ObjectId("5dd4f97ce0ee77720bc9e67a"), //陕西移动2019年BOSS扩容
		new ObjectId("5dd4f5cce0ee77720bc9e63c"), //陕西移动2019年CRM扩容
		new ObjectId("5cbee002e0ee7767351d3160"), //北京移动CRM中心化
		new ObjectId("5cd9f894e0ee774862efefcb"),  //咪咕公司19年MIGUNE五期能力平台工程
		new ObjectId("5e702d69e0ee772e6a2eaa87")   //5G-SA（贵州）
	};
	public static final double PRJ_QUALITY_HEALTH_SPECIAL_POINT = 4.5d;
	//项目集状态 已立项
	public static final ObjectId PRJ_STATUS_APPROVAL = new ObjectId("5dc3c246c56a424c33470468");
	//项目集状态 已发布基准
	public static final ObjectId PRJ_STATUS_PUBLISH = new ObjectId("5dc3c256c56a424c33470469");
	public static final String PRJ_STATUS_PUBLISH_NAME = "已发布基准";
	//项目集状态 已上线
	public static final ObjectId PRJ_STATUS_ONLINE = new ObjectId("5dc3c266c56a424c3347046a");
	public static final String PRJ_STATUS_ONLINE_NAME = "已上线";
	//项目集状态 售前对接
	public static final ObjectId PRJ_STATUS_ID_SALE_PRE = new ObjectId("5dc3c1f4c56a424c33470466");
	public static final String PRJ_STATUS_NAME_SALE_PRE = "售前对接";
	//项目集状态 已签署上线报告
	public static final ObjectId PRJ_STATUS_ONLINE_REPORT = new ObjectId("5dc3c290c56a424c3347046b");

	//项目集状态为“售前对接”、“项目经理任命”、“已立项”三种状态时，不进行健康度的计算
	public static final ObjectId[] PRJ_STATUS_IDS_NOTCALC_HEALTH = new ObjectId[] {
		new ObjectId("5dc3c234c56a424c33470467"), //项目经理任命
		new ObjectId("5dc3c1f4c56a424c33470466"), //售前对接
		new ObjectId("5dc3c246c56a424c33470468")  //已立项
	};
	
	public static final int PRJ_SCOPE_IMPORT_ROWNUM = 15000; //PRJ SCOPE Excel导入，行限制放开到15000，其他是8000
	
	public static final int PRJ_SCOPE_IMPORT_COLNUM = 30;
	
	public static final String PRJ_SCOPE_IMPORTFILE_FORMAT_ERROR = "Excel数据异常，行数应不超过"+ PRJ_SCOPE_IMPORT_ROWNUM +
			"行，列数应不超过"+ PRJ_SCOPE_IMPORT_COLNUM +"列，请参照模板删除无效的行或者列后再次导入!";
	
	public static final int PRJ_SCOPE_FIRST_PAGE_SIZE = 50;
	public static final int PRJ_SCOPE_PAGE_SIZE = 500;
	
	public static final String PSO_DPRT_CODE = "PSO";
	public static final String PRJ_BUDGET_OS_COSTRATE_TYPE = "921355";
	public static final String PRJ_BUDGET_TRAINEE_COSTRATE_TYPE = "921257";
	public static final String PRJ_BUDGET_TRAINEE_CNFG_ITEM_ID = "921251";
	
	public static final ObjectId PRJ_TASK_PROGRESS_INDICATE_PARA = new ObjectId("5df740f4c56a424c334704e4");

	public static final String ZHEJIANG_PROV = "zhejiang";
	public static final Map<String, String> PRJ_PROV_CUSTSDEFS = new HashMap<String, String>() {
		private static final long serialVersionUID = 1L;
		{
			put("zhejiang", "浙江");
		}
	};
	public static final Map<ObjectId, String> PRJ_PROV_CUSTDEFRELS = new HashMap<ObjectId, String>(){
		private static final long serialVersionUID = 1L;
		{
			put(new ObjectId("5c6e08962f2413bbe5ab86fe"), "zhejiang"); //浙江1->浙江
			put(new ObjectId("5ee777b64ace67ec094961b1"), "zhejiang"); //浙江2->浙江
		}
	};
	public static final Map<ObjectId, String> PRJ_PROV_CUSTDEFRELS_ABP = new HashMap<ObjectId, String>(){
		private static final long serialVersionUID = 1L;
		{
			put(new ObjectId("629090694008dd3e7548bad3"), "zhejiang"); //浙江1->浙江
			put(new ObjectId("629090694008dd3e7548bad4"), "zhejiang"); //浙江2->浙江
		}
	};
	
	public static final String ACCOUNT_SYSTEM_LINKUS = "linkus";
	public static final String ACCOUNT_SYSTEM_ABP = "abp";
	public static final String PRJ_BUDGET_USE_ABP = "useAbp";
	
	public static final String PRJ_OSEMPINFO_MANDAY = "mdNum";
	public static final String PRJ_OSEMPINFO_MANMON = "mmNum";
	public static final String PRJ_OSEMPINFO_EMPFEE = "empFee";

	public static final String USER_CUST_PARA = "userCustPara";
	
	/**
	 * 项目管理类型
	 */
	// 直管
	public static final ObjectId DIRECT_CONTROL = new ObjectId("5ef311414ace67ec09496224");
	
	// 监管
	public static final ObjectId MONITOR = new ObjectId("5ef3116e4ace67ec09496225");
	//非建设类
	public static final ObjectId NOT_CONSTRUCT = new ObjectId("6389d37864586deda477ccde");
	
	// 已发布的基准
	public static final ObjectId PRJ_BMK_STATUSID = new ObjectId("5c304774900add501a1414a0");
	
	public static final ObjectId PRJ_SYSTEM_DEF_ID = new ObjectId("5f2a79014ace67ec094963c0");
	public static final String PRJ_SYSTEM_DEF_CODE = "prjSystem";
	public static final ObjectId PRJ_MODULE_DEF_ID = new ObjectId("5f2a79274ace67ec094963c1");
	public static final String PRJ_MODULE_DEF_CODE = "prjModule";
	public static final String SYSDEFCNFG_SYSTEM_MODULE_TYPE = "prjSystemModule";

	public static ObjectId PRJ_MAINT_PLAN_DEF_ID = new ObjectId("5f572e394ace67ec09496558");
	public static String PRJ_MAINT_PLAN_VER = "prjMaintPlanVer";
	public static String PRJ_MAINT_PLAN_NAME = "项目交维计划版本";

	public static ObjectId PRJ_SALES2PM_PLAN_DEF_ID = new ObjectId("622ef64bb735103d42a2f894");
	public static String PRJ_SALES2PM_PLAN_VER = "prjSales2PmPlanVer";
	public static String PRJ_SALES2PM_PLAN_NAME = "项目交施计划版本";

	public static ObjectId MAINT_PLAN_TASK_TYPE_DEF_ID = new ObjectId("5f58595f4ace67ec09496577");

	public static ObjectId MAINT_PLAN_DOC_TYPE_DEF_ID = new ObjectId("5f5846d44ace67ec09496572");

	public static ObjectId FLOW_CMC_DEF_ID = new ObjectId("5f587b1e4ace67ec09496581");

	public static ObjectId FLOW_BSC_DEF_ID = new ObjectId("622ab8b9b735103d429a84db");

	public static ObjectId CMC_DEF_ID = new ObjectId("5a70111329974f7cabe9115d");
	public static ObjectId CMC_REPORT_DEF_ID = new ObjectId("5f587d3e4ace67ec09496585");

	public static ObjectId PRJ_OVER_ALL_PLAN_DEF_ID = new ObjectId("5f5833da4ace67ec0949656c");
	public static String PRJ_OVER_ALL_PLAN = "overallPlan";
	public static String PRJ_OVER_ALL_PLAN_NAME = "总体计划";

	public static ObjectId PRJ_DOCUMENT_PLAN_DEF_ID = new ObjectId("5f5834a34ace67ec09496570");
	public static String PRJ_DOCUMENT_PLAN_PLAN = "documentPlan";
	public static String PRJ_DOCUMENT_PLAN_NAME = "文档计划";

	public static ObjectId PRJ_PERSON_LEAVE_PLAN_DEF_ID = new ObjectId("5f58346e4ace67ec0949656f");
	public static String PRJ_PERSON_LEAVE_PLAN_PLAN = "personLeavePlan";
	public static String PRJ_PERSON_LEAVE_PLAN_NAME = "人员撤离计划";

	public static ObjectId PRJ_TRAINING_PLAN_DEF_ID = new ObjectId("5f58340d4ace67ec0949656d");
	public static String PRJ_TRAINING_PLAN= "trainingPlan";
	public static String PRJ_TRAINING_PLAN_NAME = "培训计划";

	public static ObjectId PRJ_BASELINE_ACHIEVED_PROGRESS_DEF_ID = new ObjectId("5f65cb074ace67ec094965eb");
	public static String PRJ_BASELINE_ACHIEVED_PROGRESS= "baselineAchievedProgress";
	public static String PRJ_BASELINE_ACHIEVED_PROGRESS_NAME = "项目基准达成情况";

	public static ObjectId PRJ_SCOPE_PROGRESS_DEF_ID = new ObjectId("5f5f074b4ace67ec094965a3");
	public static String PRJ_SCOPE_PROGRESS= "scopeProgress";
	public static String PRJ_SCOPE_PROGRESS_NAME = "项目建设范围完成情况";

	public static ObjectId PRJ_REMAINED_REQ_HANDLE_PLAN_DEF_ID = new ObjectId("5f5834374ace67ec0949656e");

	public static ObjectId PRJ_COMPLAIN_ACTUAL_PLAN_DEF_ID = new ObjectId("5f5834374ace67ec0949656e");

	public static ObjectId PRJ_FAULT_ACTUAL_PLAN_DEF_ID = new ObjectId("5f5f08c14ace67ec094965a5");

	public static ObjectId HAND_OVER_PSN_DEF_ID = new ObjectId("5f5753b04ace67ec0949655f");
	public static ObjectId TAKE_OVER_PSN_DEF_ID = new ObjectId("5f5753cc4ace67ec09496560");
	public static ObjectId RESP_DEF_ID = new ObjectId("5bc836ab900add501a1412d1");

	public static ObjectId PRJ_PROCESS_CONTROL_PLAN_VER_DEF_ID = new ObjectId("5f5f41054ace67ec094965ab");
	public static String PRJ_PROCESS_CONTROL_PLANVER= "prjProcessControlPlanVer";
	public static String PRJ_PROCESS_CONTROL_PLAN_VER_NAME= "项目过程管控计划版本";

	public static ObjectId PRJ_PLAN_DEF_ID= new ObjectId("5f587c8f4ace67ec09496582");
	public static ObjectId PRJ_TRANSFER_DEF_ID= new ObjectId("622ab996b735103d429b47f3");
	public static ObjectId PRJ_PLAN_REPORT_DEF_ID= new ObjectId("5f587d794ace67ec09496586");

	public static String PRJ_CREATE_TIME= "prjCreateTime";
	public static String SM_APPROVED_TIME= "smApprovedTime";
	public static String LATEST_BASELINE_ONLINE_TIME= "latestBaselineOnlineTime";
	public static String LATEST_BASELINE_ONLINE_RPT_TIME= "latestBaselineOnlineRptTime";
	public static String FIRST_BASELINE_APPROVED_TIME= "firstBaselineApprovedTime";
	public static String BASE_LINE_LAST_SUBMIT_TIME= "baselineLastSubmitTime";
	public static String LAST_SM_APPROVED_TIME= "lastSmApprovedTime";
	public static String FIRST_QUALITY_HEALTH_DATE= "firstQualityHealthDate";
	public static String ACTUAL_MAINTAIN_MONTH= "actualMaintainMonth";
	public static String OM_RPT_APPROVED_TIME= "omRptApprovedTime";//交维报告审批通过时间

	public final static String SPV_CN_QUALITY 	= "quality";
	public final static String PRJ_LEVEL 	= "prjLevel";
	public final static String ACTUAL_ONLINE_MONTH_END_DATE = "actualOnlineMonthEndDate";

	public final static ObjectId PRJ_LEVEL_DEF_ID = new ObjectId("5f60665d4ace67ec094965bb");
	public final static ObjectId PRJ_DOC_DEF_ID = new ObjectId("5f6066ef4ace67ec094965c0");
	public final static ObjectId CORE_MILE_STONE_DEF_ID = new ObjectId("5f6066a34ace67ec094965bd");
	public final static String CORE_MILE_STONE 		= "coreMilestone";
	public final static String CORE_MILE_STONE_NAME = "核心里程碑";

	public final static ObjectId PRJ_WEEKLY_RPT_DEF_ID  = new ObjectId("5f6066c04ace67ec094965be");
	public final static String PRJ_WEEKLY_RPT 	= "prjWeeklyRpt";
	public final static String PRJ_WEEKLY_RPT_NAME 	= "项目周报";

	public final static ObjectId PRJ_MONTHLY_RPT_DEF_ID = new ObjectId("5f6066d84ace67ec094965bf");
	public final static String PRJ_MONTHLY_RPT 	= "prjMonthlyRpt";
	public final static String PRJ_MONTHLY_RPT_NAME = "项目月报";

	public final static String TARGET_MILE_STONE 	= "targetMilestone";
	public final static ObjectId TARGET_MILE_STONE_ID 	= new ObjectId("5f60668b4ace67ec094965bc");
	public final static String TARGET_MILE_STONE_NAME 	= "目标里程碑";

	public final static ObjectId PRJ_EXECUTE_ID 	= new ObjectId("5f94205a4396e16e7e92dea8");

	public final static String PROCESS_CONTROL_PLAN_TASK_TYPE 	= "processControlPlanTaskType";
	public final static ObjectId PROCESS_CONTROL_PLAN_TASK_TYPE_DEF_ID 	= new ObjectId("5f6065874ace67ec094965ba");
	public final static ObjectId PRJ_PHASE_DEF_ID 	= new ObjectId("5f941f064396e16e7e92dea5");
	public final static ObjectId REFER_TIME_TYPE_DEF_ID 	= new ObjectId("5f605f234ace67ec094965b3");
	public final static ObjectId ACTUAL_END_TIME_TYPE_DEF_ID 	= new ObjectId("5f969c974396e16e7e92deb5");

	public final static ObjectId DEF_ID__NODE_CCD_REPORT_ID = new ObjectId("5f587dfe4ace67ec09496589");
	public final static ObjectId DEF_ID__NODE_SRD_PSO_REPORT = new ObjectId("5f587dd84ace67ec09496588");
	public final static ObjectId DEF_ID_CCD = new ObjectId("5f587da24ace67ec09496587");
	public final static ObjectId DELIVEY_PLAN_DEF_ID_CCD = new ObjectId("5f587cbc4ace67ec09496583");
	public final static ObjectId DELIVEY_PLAN_DEF_ID_SRD_PSO_ID = new ObjectId("5f587cec4ace67ec09496584");
	public final static ObjectId TRANSFER_DEF_ID_SRD_ID = new ObjectId("622aba3fb735103d429bcf55");
	public final static ObjectId TRANSFER_DEF_ID_PSO_ID = new ObjectId("622aba2db735103d429bc057");
	public final static ObjectId TRANSFER_DEF_ID_CCD_ID = new ObjectId("622aba48b735103d429bd5fb");
	public final static ObjectId TRANSFER_DEF_ID_CCD_ID2 = new ObjectId("622aba36b735103d429bc766");

	public final static String DELIVEY_PLAN_DEF_CCD_NAME = "项目管理部审核交维计划";
	public final static String DELIVEY_PLAN_DEF_SRD_PSO_NAME ="区域/产品部总监/经理审批交维计划";
	public final static String DEF_ID_CCD_NAME ="项目管理部审核交维报告";
	public final static String DEF_ID__NODE_SRD_PSO_REPORT_NAME ="区域/产品部总监/经理审批交维报告";
	public final static String DEF_ID__NODE_CCD_REPORT__NAME ="项目管理部审核并通告交维报告";

	// OBC的BUId
	public static String PRJ_BU_OBC = "169";
	// DCU的BUCode
	public static String PRJ_BU_DCU = "168";

	// 项目管理部审核版本
	public static final String DEF_ID_OBC_BUDGET_AUDIT_NODE_VER_NAME = "项目管理部审核版本";

	// 项目管理部审核基准
	public static final String DEF_ID_OBC_BUDGET_AUDIT_NODE_JJF_NAME = "项目管理部审核基准";

	// 区域/产品部责任总监审批
	public static final String DEF_ID_OBC_BUDGET_AUDIT_NODE_SRD_PSO_NAME = "区域/产品部总监/经理审批";

	// 项目管理部审核并通告
	public static final String DEF_ID_OBC_BUDGET_AUDIT_NODE_JJF2_NAME = "项目管理部审核并通告";

    /**
     * 健康度-进度
     */
    public static ObjectId PRJ_HEALTHY_SCHEDULE = new ObjectId("5e38e8adc56a424c334705ce");
    /**
     * 健康度-成本
     */
    public static ObjectId PRJ_HEALTHY_COST = new ObjectId("5e38e8e8c56a424c334705cf");
    /**
     * 健康度-质量
     */
    public static ObjectId PRJ_HEALTHY_QUALITY = new ObjectId("5e38e903c56a424c334705d0");

    /**
     * 健康度没有数据得0分
     */
    public static String PRJ_HEALTHY_NO_DATA = "未查到相关数据，得0分；";

    // 成本统计类型定义
    public static final String HR_RSC = "hrRsc";
    public static final String THIS_MONTH_HR_RSC = "thisMonthHrRsc";
    public static final String THIS_MONTH_EMP = "thisMonthEmp";
    public static final String THIS_MONTH_OS_EMP = "thisMonthOsEmp";
    public static final String THIS_MONTH_TRAINEE = "thisMonthTrainee";
    public static final String SUM_HR_RSC = "sumHrRsc";
    public static final String BIZ_TRIP_FEE = "bizTripFee";
    public static final String THIS_MONTH_BIZ_TRIP_FEE = "thisMonthBizTripFee";
    public static final String SUM_BIZ_TRIP_FEE = "sumBizTripFee";
    public static final String SUM_AVERAGE_BIZ_TRIP_FEE = "sumAverageBizTripFee";
    public static final String DINING_FEE = "diningFee";
    public static final String THIS_MONTH_DINING_FEE = "thisMonthDiningFee";
    public static final String SUM_DINING_FEE = "sumDiningFee";
    public static final String SUM_AVERAGE_OVERTIME_DINING_FEE = "sumAverageOvertimeDiningFee";
    public static final String OTHER_FEE = "otherFee";
    public static final String THIS_MONTH_OTHER_FEE = "thisMonthOtherFee";
    public static final String SUM_OTHER_FEE = "sumOtherFee";
    public static final String SUM_AVERAGE_OTHER_FEE = "sumAverageOtherFee";
    public static final String PRJ_COST = "prjCost";
    public static final String THIS_MONTH_DIRECT_FEE = "thisMonthDirectFee";
    public static final String SUM_DIRECT_FEE = "sumDirectFee";
    public static final String SUM_EMP_FEE = "sumEmpFee";
    public static final String SUM_OS_EMP_FEE = "sumOsEmpFee";
    public static final String SUM_TOTAL_FEE = "sumTotalFee";
	public static final String SUM_JSLFB_FEE = "sumJslfbFee";
	public static final String SUM_TRAINEE_FEE = "sumTraineeFee";
	public static final String SUM_ALL_EMP_FEE = "sumAllEmpFee";
    public static final String YEAR_HR_RSC_DINING_FEE = "yearHrRscDiningFee";
    public static final String SUM_YEAR_HR_RSC = "sumYearHrRsc";
    public static final String SUM_YEAR_DINING_FEE = "sumYearDiningFee";
    public static final String SUM_YEAR_AVERAGE_DINING_FEE = "sumYearAverageDiningFee";
	public static final String TRAVEL_HOTEL_FEE_PED= "travelHotelFeePed"; //人日均差旅住宿费，
	public static final String TRAVEL_CAR_FEE_PEM= "travelCarFeePem"; //人月均差旅交通费
	public static final String TRAVEL_AIR_FEE_PEM="travelAirFeePem"; //人月均差旅机票费
	public static final String DINING_FEE_PEM ="diningFeePem"; //人月均误餐费
	public static final String URBAN_CAR_FEE_PED ="urbanCarFeePed"; //人日均市内交通费
	public static final String OFFICE_FEE_PEM ="officeFeePem"; //人月均办公用品费
	public static final String TOOL_FEE_PEM ="toolFeePem"; //人月均工具材料费
	public static final String HOUSE_RENT_FEE_PEM ="houseRentFeePem"; //人月均房屋租赁费

	public static final String TRAVEL_MM = "travelMm";//差旅人月数
	public static final String TRAVEL_HOTEL_FEE = "travelHotelFee";//差旅住宿费
	public static final String TRAVEL_PLAN_TICKET_FEE= "travelPlaneTicketFee"; //差旅机票费，
	public static final String TRAVEL_SUBSIDY_FEE= "travelSubsidyFee"; //差旅补助
	public static final String TRAVEL_CITY_CAR_FEE="travelCityCarFee"; //差旅交通费
	public static final String TRAVEL_OTHER_FEE ="travelOtherFee"; //差旅其它费
	public static final String CITY_CAR_FEE ="cityCarFee"; //市内交通费
	public static final String HUMAN_MONTH_TRAVEL_HOTEL_FEE_AVG ="humanMonthTravelHotelFeeAvg"; //人月均差旅住宿费
	public static final String HUMAN_MONTH_TRAVEL_CITY_CAR_FEE_AVG ="humanMonthTravelCityCarFeeAvg"; //人月均差旅交通费
	public static final String HUMAN_MONTH_CITY_CAR_FEE_AVG ="humanMonthCityCarFeeAvg"; //人月均市内交通费

	public static final String DIRECT_FEE_AUDIT = "directFeeAudit";

	public static final ObjectId TRAVEL_HOTEL_FEE_PED_ID= new ObjectId("63620e2b95f12ffd0ba3da66"); //人日均差旅住宿费，
	public static final ObjectId TRAVEL_CAR_FEE_PEM_ID=new ObjectId("63620e5295f12ffd0ba3da75"); //人月均差旅交通费
	public static final ObjectId TRAVEL_AIR_FEE_PEM_ID=new ObjectId("63620eb895f12ffd0ba3da89"); //人月均差旅机票费
	public static final ObjectId DINING_FEE_PEM_ID =new ObjectId("63620ed895f12ffd0ba3da97"); //人月均误餐费
	public static final ObjectId URBAN_CAR_FEE_PED_ID =new ObjectId("63620f0295f12ffd0ba3daa9"); //人日均市内交通费
	public static final ObjectId OFFICE_FEE_PEM_ID =new ObjectId("63620f2195f12ffd0ba3daaf"); //人月均办公用品费
	public static final ObjectId TOOL_FEE_PEM_ID =new ObjectId("63620f4595f12ffd0ba3dabe"); //人月均工具材料费
	public static final ObjectId HOUSE_RENT_FEE_PEM_ID =new ObjectId("63620f6a95f12ffd0ba3dac5"); //人月均房屋租赁费

    public static final ObjectId PRJ_STH_TYPE_CID = new ObjectId("5df75873c56a424c334704e6");
	public static final ObjectId PRJ_STH_CUSTOMER_TYPE_CID = new ObjectId("5df7585cc56a424c334704e5");
	public static final ObjectId PRJ_STH_OTHER_TYPE_CID = new ObjectId("5df75885c56a424c334704e7");

	//pm能力评估，分类名称
	public static final String PRJ_GRADE_NAME_SELF = "管理自己";
	public static final String PRJ_GRADE_NAME_TEAM = "管理团队";
	public static final String PRJ_GRADE_NAME_WORK = "管理工作";
	public static final String PRJ_GRADE_NAME_SPECIAL_ABILITY = "专业能力";
	public static final String PRJ_GRADE_NAME_COMMON_ABILITY = "通用能力";
	public static final String PRJ_GRADE_NAME_BUSINESS_ABILITY = "业务能力";
	public static final String PRJ_GRADE_NAME_ACTUAL_COMBAT_ABILITY = "实战能力";
	public static final String PRJ_GRADE_NAME_SELF_DISCIPLINE = "自律守信";
	public static final String PRJ_GRADE_NAME_ASSUME = "勇于担当";
	public static final String PRJ_GRADE_NAME_JUST = "客观公正";
	public static final String PRJ_GRADE_NAME_ACTIVE = "积极主动";
	public static final String PRJ_GRADE_NAME_TARGET = "目标管理";
	public static final String PRJ_GRADE_NAME_CONSENSUS = "建立共识";
	public static final String PRJ_GRADE_NAME_COOPERATE = "开放协作";
	public static final String PRJ_GRADE_NAME_CUSTOMER = "关注客户";
	public static final String PRJ_GRADE_NAME_PLAN = "计划管理";
	public static final String PRJ_GRADE_NAME_COMMUNICATE = "沟通管理";
	public static final String PRJ_GRADE_NAME_RISK = "风险管理";
	public static final String PRJ_GRADE_NAME_DECISION = "决策实施";
	public static final String PRJ_GRADE_NAME_Internal = "内部协同";

	public static final String PRJ_GRADE_NAME_INTEGRATE = "整合管理";
	public static final String PRJ_GRADE_NAME_RANGE = "范围管理";
	public static final String PRJ_GRADE_NAME_PROGRESS = "进度管理";
	public static final String PRJ_GRADE_NAME_COST = "成本管理";
	public static final String PRJ_GRADE_NAME_QUALITY = "质量管理";
	public static final String PRJ_GRADE_NAME_MANAGE_TEAM = "团队管理";
	public static final String PRJ_GRADE_NAME_STAKE_HOLDER = "干系人管理";
	public static final String PRJ_GRADE_NAME_TOOL = "工具使用能力";
	public static final String PRJ_GRADE_NAME_OVERALL = "大局观";
	public static final String PRJ_GRADE_NAME_MOOD = "情绪管理";
	public static final String PRJ_GRADE_NAME_PRSSSURE = "承压能力";
	public static final String PRJ_GRADE_NAME_EXECUTE = "执行力";
	public static final String PRJ_GRADE_NAME_TEAM_AFFECT = "团队影响力";
	public static final String PRJ_GRADE_NAME_COMPETENCY = "能力胜任度";

	public static final String PRJ_LEVEL1_PLAN_VER = "prjLevel1PlanVer";
	public static final String PRJ_LEVEL2_PLAN_VER = "prjLevel2PlanVer";

	public static final String DVT_EXP_HR_FEE = "hrFee";
	public static final String DVT_EXP_TRIP_FEE = "tripFee";
	public static final String DVT_EXP_DINING_FEE = "diningFee";
	public static final String DVT_EXP_OTHER_FEE = "otherFee";

    /**
     * aipms：OA权限
     */
    public static final String OA_AIPMS_AUTH = "ai-dmp-pms:ai-dmp-pms";

    /**
     * aipms：直接费用状态-在途
     */
    public static final String DIRECT_FEE_SOURCE_TYPE_ON_THE_WAY = "在途";

    /**
     * aipms：直接费用状态-已入账
     */
    public static final String DIRECT_FEE_SOURCE_TYPE_PAID = "已入账";

    /**
     * aipms：直接费用类型-差旅
     */
    public static final String DIRECT_FEE_TYPE_TRAVEL = "差旅费";

    /**
     * aipms：直接费用类型-餐费
     */
    public static final String DIRECT_FEE_TYPE_DINNER = "餐费";

    /**
     * aipms：直接费用类型-其他费
     */
    public static final String DIRECT_FEE_TYPE_OTHER = "其他费";

    /**
     * aipms：OA报销来源类型-商旅
     */
    public static final String OA_EXPENSES_TYPE_SL = "商旅";

    /**
     * aipms：OA报销来源类型-报账
     */
    public static final String OA_EXPENSES_TYPE_BZ = "报账";

    /**
     * 项目管理类型-建设
     */
    public static final ObjectId PRJ_MGT_TYPE_JS = new ObjectId("624fa0ebecd9cb60067a9707");
    /**
     * 项目管理类型-运营
     */
    public static final ObjectId PRJ_MGT_TYPE_YY = new ObjectId("624fa116ecd9cb60067a97e1");
    /**
     * 项目管理类型-运维
     */
    public static final ObjectId PRJ_MGT_TYPE_YW = new ObjectId("624fa13aecd9cb60067a989c");
    /**
     * 项目里程碑模板
     */
    public static final ObjectId DEF_ID_PRJ_MILE_POST_TEMP = new ObjectId("624fa87fb735103d42cfb8df");

	public static final ObjectId PRJ_FILE_BASE_ID = new ObjectId("5a3a36ebba4014a42391fa16");

	public static final String PRJ_TASK_NAME_PMUSER_APPIONT = "项目经理任命";
	public static final String PRJ_TASK_NAME_PRJ_DELIVER = "项目交施";
	public static final String PRJ_TASK_NAME_PRJ_BASICINFO = "项目基准审批完成";
	public static final String PRJ_TASK_NAME_PRJ_ONLINE = "项目集-项目上线";
	public static final String PRJ_TASK_NAME_PRJ_CHECK = "项目集-项目验收";
	public static final String PRJ_TASK_NAME_PRJ_DELIVER_REPORT = "交维报告";
	public static final String PRJ_TASK_NAME_CHECK_REPORT = "验收报告";

	public static final String AI_PRJ_TYPE_PRE_SALE = "售前项目";

	public static final String PREPARE = "准备";
	public static final String DELIVER = "交施";
	public static final String BASICINFO = "基准";
	public static final String ONLINE = "上线";
	public static final String DELIVER_DIMEN = "交维";
	public static final String CHECK = "验收";
	public static final String PRE_CHECK = "初验";
	public static final String FINAL_CHECK = "终验";

	/**
	 * progressIndicate字段信息
	 */
	public static final String PRJ_PROGRESS_NORMAL_DEF_NAME = "正常完成";

	/**
	 * BU工时接口人角色ID
	 */
	public static final ObjectId BU_MD_RESP_ROLE_ID = new ObjectId("62cf83baf7a143213a9cfdbc");

    /**
     * ABP与基准对比--科目
     */
	public static final String[] ABP_VS_LINKUS_ITEMS = new String []{"资源(人月)", "正式", "外包", "实习", "直接费用（￥K）", "差旅", "餐费", "其它"};
    /**
     * ABP与基准对比--科目
     */
	public static final String ABP_VS_LINKUS_ITEMS_SUM_EMP_MONTH = "资源(人月)";
    /**
     * ABP与基准对比--科目
     */
	public static final String ABP_VS_LINKUS_ITEMS_EMP_MONTH = "正式";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_OS_EMP_MONTH = "外包";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_TRAINEE_EMP_MONTH = "实习";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_DIRECT_FEE = "直接费用（￥K）";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_TRAVEL_FEE = "差旅";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_DINING_FEE = "餐费";
    /**
     * ABP与基准对比--科目
     */
    public static final String ABP_VS_LINKUS_ITEMS_OTHER_FEE = "其它";

    public static final String PROV_PRJ_STD_FEE_CNFG = "provPrjStdFeeCnfg";

    public static final ObjectId BIG_PRJ_ID = new ObjectId("63620da695f12ffd0ba3da45");
	public static final ObjectId ALL_PRJ_ID = new ObjectId("63620d6195f12ffd0ba3da32");



	//经理
	public static final ObjectId DEF_ID_MANAGER_ROLE = new ObjectId("6388750964586deda464fb9b");
	//总监
	public static final ObjectId DEF_ID_DIRECTOR_ROLE = new ObjectId("6388751864586deda464fd58");
	//事业部领导
	public static final ObjectId DEF_ID_LEADER_ROLE = new ObjectId("6388752864586deda464fe64");
	//BU运营管理员
	public static final ObjectId DEF_ID_BU_ROLE = new ObjectId("5c501a88900add501a1418c2");
	// ABP管理员
	public final static ObjectId ROLE_ABP_ADMIN_ID = new ObjectId("62a69c9c66a6538180672f94");
	//项目考评管理员
	public final static ObjectId DEF_ID_PRJ_EVAL_ADMIN_ID = new ObjectId("681db5e3fb03e0e1739024a4");
	//项目运营总监
	public final static ObjectId DEF_ID_PRJ_OPRT_DIR_ID = new ObjectId("681db4b6fb03e0e1738fed29");
	//特战队
	public final static ObjectId DEF_ID_ELITE_UNIT_ID = new ObjectId("681db670fb03e0e173903834");

	//预测收入
	public static final String PREDICTED_INCOME = "predictedIncome";
	public static final String PMS_PRJ_LEVEL3_KEY_PRJ_INDICATOR_CNFG = "pmsPrjLevel3KeyPrjIndicatorCnfg";
	public static final ObjectId GM_ID = new ObjectId("637f27d464586deda4d72a69");
	public static final ObjectId INCOME_PER_HUNDRED_ID = new ObjectId("637f27f764586deda4d72cb7");
	public static final ObjectId DIRECT_FEE_PER_HUNDRED_ID = new ObjectId("637f280d64586deda4d72db9");

	//批量导入项目集，模板列名
	public static String PRJ_SET_INFO_COLU_BU = "* 所属BU";
	public static String PRJ_SET_INFO_COLU_PRJ_CODE = "* 项目集编码";
	public static String PRJ_SET_INFO_COLU_PRJ_NAME = "* 项目集名称";
	public static String PRJ_SET_INFO_COLU_LEVEL = "* 项目等级";
	public static String PRJ_SET_INFO_COLU_STATUS = "* 项目集状态";
	public static String PRJ_SET_INFO_COLU_PRJ_MGTTYPE = "* 项目管理类型";
	public static String PRJ_SET_INFO_COLU_PEJ_BIZTYPE = "业务类型";
	public static String PRJ_SET_INFO_COLU_PRJ_SIGN_UP_STATUS = "签约状态";
	public static String PRJ_SET_INFO_COLU_PM_USER = "* 项目经理";
	public static String PRJ_SET_INFO_COLU_PRJ_ADMIN = "项目管理员";
	public static String PRJ_SET_INFO_COLU_SM_USER = "项目督导/导师";
	public static String PRJ_SET_INFO_COLU_TRACK_USER = "跟踪人";
	public static String PRJ_SET_INFO_COLU_PSO_SM = "PSO总监/经理";
	public static String PRJ_SET_INFO_COLU_PROV = "* 项目省份";
	public static String PRJ_SET_INFO_COLU_YEAR = "归属年份";
	public static String PRJ_SET_INFO_COLU_SUB_PRJS = "子项目清单";

	//批量导入项目集更新，模板列名
	public static String PRJ_SET_INFO_COLU_PRJ_CODE_UPDATE = "项目集编码";
	public static String PRJ_SET_INFO_COLU_LEVEL_UPDATE = "项目等级";
	public static String PRJ_SET_INFO_COLU_STATUS_UPDATE = "项目集状态";
	public static String PRJ_SET_INFO_COLU_PRJ_MGTTYPE_UPDATE = "管理类型";
	public static String PRJ_SET_INFO_COLU_PEJ_BIZTYPE_UPDATE = "业务类型";
	public static String PRJ_SET_INFO_COLU_PRJ_ADMIN_UPDATE = "项目管理员";
	public static String PRJ_SET_INFO_COLU_SM_USER_UPDATE = "项目督导/导师";
	public static String PRJ_SET_INFO_COLU_TRACK_USER_UPDATE = "跟踪人";
	public static String PRJ_SET_INFO_COLU_PSO_SM_UPDATE = "PSO总监/经理";
	public static String PRJ_SET_INFO_COLU_BIGREGION_UPDATE = "区域";
	public static String PRJ_SET_INFO_COLU_PROV_UPDATE = "省份";
	public static String PRJ_SET_INFO_COLU_ACTUAL_ONLINE_DATE_UPDATE = "实际上线月份";
	public static String PRJ_SET_INFO_COLU_ACTUAL_ONLINE_RPT_DATE_UPDATE = "签署上线报告时间";
	public static String PRJ_SET_INFO_COLU_ACTUAL_OM_DATE = "交维完成月份";
	public static String PRJ_SET_INFO_COLU_YEAR_UPDATE = "归属年份";
	public static String PRJ_SET_INFO_COLU_IS_DIRECT_CTRL = "监管类型";
	public static String PRJ_SET_INFO_COLU_QualitySmUser = "质量总监";
	public static String PRJ_SET_INFO_COLU_PreSaleUser = "售前";
	public static String PRJ_SET_INFO_COLU_RptIsRequired = "是否生成周月报";

	public static ObjectId SIGN_UP_STATUS_ID = new ObjectId("5dc3c103c56a424c33470465");

	public static final Integer LOGIN_TEST_RCD_EXAM_NUM = 3;


	public final static ObjectId ABP_RES_SRC_DEF_ID = new ObjectId("627e0c02ecd9cb60065a8aaa");

    /**
     * 项目等级申请
     */
    public static final ObjectId DEF_ID_PRJ_LEVEL_APPROVE = new ObjectId("63abed1c64586deda4087791");
    public static final String DEF_NAME_PRJ_LEVEL_APPROVE = "项目等级申请";
    public static final String DEF_CODE_NAME_PRJ_LEVEL_APPROVE = "prjLevelApply";
    /**
     * 待审批
     */
    public static final ObjectId DEF_ID_STATUS_PENDING = new ObjectId("5dff67bac56a424c33470503");
    public static final String DEF_NAME_STATUS_PENDING = "待审批";
    public static final String DEF_CODE_NAME_STATUS_PENDING = "pending";
    /**
     * 审批通过
     */
    public static final ObjectId DEF_ID_STATUS_APPROVED = new ObjectId("5dff6a4bc56a424c33470504");
    public static final String DEF_NAME_STATUS_APPROVED = "审批通过";
    public static final String DEF_CODE_NAME_STATUS_APPROVED = "isApproved";

    /**
     * 待PSO经理审批
     */
    public static final ObjectId DEF_ID_APPROVE_PENDING_PSO_MANAGER = new ObjectId("63abf34c64586deda408fd02");
    public static final String DEF_NAME_APPROVE_PENDING_PSO_MANAGER = "待PSO经理审批";

    /**
     * 待BU运营管理员审批
     */
    public static final ObjectId DEF_ID_APPROVE_PENDING_BU_ADMIN = new ObjectId("63abf37864586deda4090437");
    public static final String DEF_NAME_APPROVE_PENDING_BU_ADMIN = "待BU运营管理员审批";

    /**
     * 管理调级审批
     */
    public static final ObjectId DEF_ID_APPROVE_MANAGER_ADJUST= new ObjectId("6481378a64586deda40c3e41");
    public static final String DEF_NAME_APPROVE_MANAGER_ADJUST = "管理调级审批";

    /**
     * ABP经营计划版本
     */
    public static final ObjectId ABP_BUSINESS_PLAN_VERSION_ID = new ObjectId("62a2fb15fcef250ed0384a6f");
    public static final String ABP_BUSINESS_PLAN_VERSION_NAME = "ABP经营计划版本";
    public static final String ABP_BUSINESS_PLAN_VERSION_CODE_NAME = "abpPlanVer";

	public final static ObjectId DEF_BU_ID = new ObjectId("5a6ffe7829974f7cabe91159");
	public final static String DEF_BU_NAME = "亚信BU";
	public final static String DEF_BU_CODE_NAME = "aiBu";

    public final static ObjectId ABP_PROV_ID = new ObjectId("627e0c41ecd9cb60065a8dde");
    public final static String ABP_PROV_NAME = "ABP省份";
    public final static String ABP_PROV_CODE_NAME = "abpProv";

    public final static String APPROVAL_LEVEL_UN_GRADE = "未分类";
    public final static String APPROVAL_LEVEL_PSO = "PSO经理审批";
    public final static String APPROVAL_LEVEL_AUTH = "运营审批";
    public final static String APPROVAL_LEVEL_GRADEED = "已分类";

    public final static String APPROVAL_LEVEL_STATUS_UN_GRADE = "未提交";
    public final static String APPROVAL_LEVEL_STATUS_PSO = "待PSO经理审批";
    public final static String APPROVAL_LEVEL_STATUS_AUTH = "待BU运营审批";
    public final static String APPROVAL_LEVEL_STATUS_GRADEED = "分类完成";

	//批量导入省份人员角色更新，模板列名
	public static String PROV_ROLE_USER_PROV_UPDATE = "省份";
	public static String PROV_ROLE_USER_PSO_MANAGER_UPDATE = "PSO经理";
	public static String PROV_ROLE_USER_PSO_DIRECTOR_UPDATE = "PSO总监";
	public static String PROV_ROLE_USER_OPERATE_ADMIN_UPDATE = "运营管理员";

	public static String PROV_ROLE_USER_QUALITY_SYSTEM_TRACKER_UPDATE = "质量跟踪人";
	public static String PROV_ROLE_USER_PRE_SALES_UPDATE = "售前";

    public static String DEFAULT_COST_RATA_TYPE = "pmsBuEmpCostPerEmpDay";
    public static String DEFAULT_COST_RATA_TYPE_EMP = "emp";
    public static String DEFAULT_COST_RATA_TYPE_OS_EMP = "empOs";

    public final static String REGION = "abpRegion";
    public final static String BIG_REGION = "abpBigRegion";
	public final static String ENGDEPT = "abpEngDept";

	public final static String ENGDEPT_Director = "engDeptDirector";

	/**
	 * 工程项目
	 */
	public static final String RD_PRJ_NAME = "工程项目";

	/**
	 * oaStatus状态1
	 */
	public static final String OA_STATUS_CODE_ONE = "1000";

	/**
	 * oaStatus状态2
	 */
	public static final String OA_STATUS_CODE_TWO = "1001";

	/**
	 * oaStatus状态3
	 */
	public static final String OA_STATUS_CODE_THREE = "1060";

	public static String AITECH_BSC_CCD_LOGIN_NAME = "AITECH-BSC-CCD";
	public static String PROJECT_FRAME_NAME= "FRAME";
	public static String PROJECT_STATUS_NAME = "CANCEL";
	public static String PROJECT_STATUS_NAME_CLOSE = "CLOSE";
	public static List<String> PROJECT_STATUS_NAME_CLOSE_CANCEL =
			Arrays.asList("CANCEL","CLOSE");

	public static String PROJECT_FORM_SPLIT = "SPLIT";
	public static String PROJECT_FORM_CONTRACT = "CONTRACT";

	//浙江区4省：浙江中台、浙江拓展、浙江数政、浙江数融
	//在线=在线省端+在线数智+在线平台+在线销售+在线服务+在线营销，（不含在线专家）
	//中移智运&管信=中移智运+中移管信
	public static List<ObjectId> PROV_ID_ZJ = Arrays.asList(
			new ObjectId("63e627351d740fa6041e41c9"),
			new ObjectId("63e627681d740fa6041e588f"),
			new ObjectId("63e627b61d740fa6041e7216"),
			new ObjectId("63e627941d740fa6041e6ca0"),
			new ObjectId("629090694008dd3e7548bae0"),
			new ObjectId("63e628701d740fa6041e9a73"),
			new ObjectId("669f5bfccddb0c64b08800b2"),
			new ObjectId("669f5c2dcddb0c64b088e414"),
			new ObjectId("669f5c59cddb0c64b089183d"),
			new ObjectId("669f5c7ecddb0c64b08946a6"),
			new ObjectId("669f5ddfcddb0c64b08b67c7"),
			new ObjectId("669f5d6fcddb0c64b08acbdd")
	);

	public static final ObjectId PRJ_BUDGET_PARA_ID = new ObjectId("5c66539b900add501a1418e9");

	public static String GO_LINE_DATE = "goLineDate";
	public static String GO_LINE_PAPER_DATE = "goLinePaperDate";
	public static String TO_MAIN_TAIN_DATE = "toMaintainDate";
	public static ObjectId PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_ID = new ObjectId("64df14dd64586deda49dc811");
	public static String PRJ_DVT_MGT_TYPE_PROGRESS_DELAY_NAME = "进度延期";

	public static ObjectId PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_ID = new ObjectId("64decf7964586deda49b59d0");
	public static String PRJ_DVT_MGT_TYPE_EFFECT_DEVIATION_NAME = "效能偏差";

    public static ObjectId PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_ID = new ObjectId("64decf4f64586deda49b4897");
    public static String PRJ_DVT_MGT_TYPE_COST_DEVIATION_EF_NAME = "成本偏差(EF)";

    public static ObjectId PRJ_DVT_MGT_TYPE_IMPL_PLAN_ID = new ObjectId("64decf6564586deda49b52d0");
    public static String PRJ_DVT_MGT_TYPE_IMPL_PLAN_NAME = "实施计划偏差";

	public static ObjectId PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_ID = new ObjectId("64decf1564586deda49b3565");
	public static String PRJ_DVT_MGT_TYPE_COST_DEVIATION_ABCG_NAME = "成本偏差(ABCG)";

	public static ObjectId PRJ_DVT_MGT_TYPE_BEYOND_BASELINE_MANAGE_ID = new ObjectId("64decf8c64586deda49b5dd5");
	public static String PRJ_DVT_MGT_TYPE_BEYOND_BASELINE_MANAGE_NAME = "超基线管理";

	public static ObjectId PRJ_DVT_MGT_TYPE_ORDER_DEVIATION_ID = new ObjectId("65fbced36658b395915481a6");
	public static String PRJ_DVT_MGT_TYPE_ORDER_DEVIATION_NAME = "订单偏差";

	public static ObjectId PRJ_DVT_MGT_JW_COST_OVERSPEND_ID = new ObjectId("661e458f6658b39591ed81be");
	public static String PRJ_DVT_MGT_JW_COST_OVERSPEND_NAME = "交维后成本超支";

	public static ObjectId PRJ_DVT_MGT_DELAY_SIGN_ID = new ObjectId("66f1159c995c83dcd3f889c1");
	public static String PRJ_DVT_MGT_DELAY_SIGN_NAME = "签约延期";

	public static ObjectId PRJ_DVT_MGT_TYPE_COLOR_CARD_ID = new ObjectId("67176123995c83dcd3da4217");
	public static String PRJ_DVT_MGT_TYPE_COLOR_CARD_NAME = "色牌管理";

	public static ObjectId PRJ_DVT_MGT_TYPE_LOW_GM_ID = new ObjectId("67ac4dd4ec5e633697ab22a7");
	public static String PRJ_DVT_MGT_TYPE_LOW_GM_NAME = "低GM管理";


	public static String PRJ_DVT_MGT_TYPE_RECTIFY_ACTION_REVIEWED = "rectifyActionReviewed";
	public static String PRJ_DVT_MGT_TYPE_DESC = "desc";
	public static String PRJ_DVT_MGT_TYPE_NOTES = "notes";
	public static String PRJ_DVT_MGT_TYPE_CAUSETYPE = "causeType";
	public static String PRJ_DVT_MGT_TYPE_CAUSESUBTYPE = "causeSubType";
	public static String PRJ_DVT_MGT_TYPE_CAUSESUB2TYPE = "causeSub2Type";
	public static String PRJ_DVT_MGT_TYPE_SYMPTOM = "symptom";
	public static String PRJ_DVT_MGT_TYPE_RECTIFYACTION = "rectifyAction";
	public static String PRJ_DVT_MGT_TYPE_RECTIFYENDDATE = "rectifyEndDate";
	public static String PRJ_DVT_MGT_TYPE_RECTIFY_STATUS = "rectifyStatus";
	public static String PRJ_DVT_MGT_TYPE_DESC_DETAIL = "descDetail";

    public static String CONTROL_LEVEL_S_WARNING = "严重警示";
	public static String CONTROL_LEVEL_WARNING = "警示";
	public static String CONTROL_LEVEL_REMIND = "提醒";

    /**
     * 是
     */
    public static final String DEF_YES = "是";

    /**
     * 否
     */
    public static final String DEF_NO = "否";


    public static final String POM_STATUS_CLOSE = "CLOSE";
    public static final String POM_STATUS_CANCEL = "CANCEL";
    public static final String POM_STATUS_PAUSE = "PAUSE";
	public static final String POM_STATUS_ACTIVE = "ACTIVE";


    public static final String PRJ_BMS_STATUS_CLOSED = "已关闭";

	/**
	 * 项目集状态-已完成交维
	 */
    public static final ObjectId PRJ_STATUS_CROSS_DIMENSION_ID = new ObjectId("5dc3c2b3c56a424c3347046c");
	/**
	 * 项目集状态-已终验
	 */
    public static final ObjectId PRJ_STATUS_FINAL_INSPECTION_ID = new ObjectId("6125b31a952372e6ad1b66c2");
	/**
	 * 项目集状态-已验收
	 */
    public static final ObjectId PRJ_STATUS_ACCEPTED_ID = new ObjectId("6125b32d952372e6ad1b8a1c");
	public static final String PRJ_STATUS_ACCEPTED_NAME = "已验收";
	/**
	 * 项目集状态-已暂停
	 */
    public static final ObjectId PRJ_STATUS_PAUSED_ID = new ObjectId("61721c39abef7f8c4996e1d6");
	/**
	 * 项目集状态-已取消
	 */
	public static final ObjectId PRJ_STATUS_CANCEL_ID = new ObjectId("68707273cddb0c64b002a9a9");
	public static final String PRJ_STATUS_CANCEL_NAME = "已取消";

	/**
	 * 项目集状态-已关闭
	 */
	public static final ObjectId PRJ_STATUS_CLOSED_ID = new ObjectId("667238f3d70673a94a2ab897");
	public static final String PRJ_STATUS_CLOSED_NAME = "已关闭";

	public static final String PRJ_REVIEW_OPRT_TYPE_APPEAL = "扣分申诉";
	public static final String PRJ_REVIEW_OPRT_TYPE_CHECK = "扣分审核";
	public static final String PRJ_REVIEW_OPRT_TYPE_INIT = "初始化";
	public static final String PRJ_REVIEW_OPRT_TYPE_EDIT = "编辑";

	public final static String DEV_MGT_OVER = "超支";
	public final static String DEV_MGT_HEAD_BALANCE = "结余";
	public final static String DEV_MGT_HEAD_OTHER = "其它";

	public static String PROV_ROLE_USER_PROV = "省份";
	public static String PROV_ROLE_USER_LEVEL = "分类";
	public static String PROV_ROLE_USER_TRACKUSER = "跟踪人";

	//EF考评，项目考评项
	public static final ObjectId COST_DEVIATION_ID = new ObjectId("657fe17064586deda4602c45");
	public static final ObjectId TRAVEL_PRJ_REVIEW_ITEM_ID = new ObjectId("657fe22f64586deda460387d");
	public static final ObjectId DINING_PRJ_REVIEW_ITEM_ID = new ObjectId("657fe36564586deda46051ba");
	public static final ObjectId OTHER_PRJ_REVIEW_ITEM_ID = new ObjectId("657fe3eb64586deda4605b50");
	//EF考评，项目考评类型
	public static final ObjectId PROCESS_REVIEW_4EF_ID = new ObjectId("657fdfd364586deda4600d9a");
	// 过程考评EF
	public static final ObjectId PHASE_REVIEW_4EF_ID = new ObjectId("657fe02e64586deda460135e");
	//结果
	public static final ObjectId RESULT_REVIEW_4EF_ID = new ObjectId("657fe06564586deda4601a21");

	//阶段-子项-效能
	public static final ObjectId PHASE_REVIEW_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657fe6c064586deda4609549");
	//阶段-子项-效能-实际效能
	public static final ObjectId PHASE_REVIEW_ACT_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657fea0964586deda460c9e2");
	//阶段-子项-效能-基准效能
	public static final ObjectId PHASE_REVIEW_BMK_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657feaf664586deda460da20");
	//阶段-子项-进度
	public static final ObjectId PHASE_REVIEW_SCHEDULE_SUB_ITEM_ID = new ObjectId("657fe70564586deda46099f7");
	//阶段-子项-进度-里程碑
	public static final ObjectId PHASE_REVIEW_MILESTONE_SUB_ITEM_ID = new ObjectId("657feb8864586deda460e2de");
	//阶段-子项-成本
	public static final ObjectId PHASE_REVIEW_COST_SUB_ITEM_ID = new ObjectId("657fe78064586deda460a205");
	//阶段-子项-质量
	public static final ObjectId PHASE_REVIEW_QUALITY_SUB_ITEM_ID = new ObjectId("657fe79864586deda460a38c");
	//阶段-子项-满意度
	public static final ObjectId PHASE_REVIEW_SATISFAVTION_SUB_ITEM_ID = new ObjectId("657fe7b764586deda460a58d");

	//结果-子项-效能
	public static final ObjectId RESULT_REVIEW_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657fe8a5611f32bf74f12a85");
	//结果-子项-效能-实际效能
	public static final ObjectId RESULT_REVIEW_ACT_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657feb0e64586deda460db71");
	//结果-子项-效能-基准效能
	public static final ObjectId RESULT_REVIEW_BMK_EFFICIENCY_SUB_ITEM_ID = new ObjectId("657fe9eb64586deda460c819");
	//结果-子项-进度
	public static final ObjectId RESULT_REVIEW_SCHEDULE_SUB_ITEM_ID = new ObjectId("657fe8a5611f32bf74f12a86");
	//结果-子项-进度-里程碑
	public static final ObjectId RESULT_REVIEW_MILESTONE_SUB_ITEM_ID = new ObjectId("657febec64586deda460ea0b");
	//结果-子项-成本
	public static final ObjectId RESULT_REVIEW_COST_SUB_ITEM_ID = new ObjectId("657fe8a5611f32bf74f12a87");
	//结果-子项-质量
	public static final ObjectId RESULT_REVIEW_QUALITY_SUB_ITEM_ID = new ObjectId("657fe8a5611f32bf74f12a88");
	//结果-子项-满意度
	public static final ObjectId RESULT_REVIEW_SATISFAVTION_SUB_ITEM_ID = new ObjectId("657fe8a5611f32bf74f12a89");

	public static final String PROCESS_REVIEW_4EF_TYPE_CODENAME = "processReview4Ef";

	public static final ObjectId TAO_BAO_FOLDER_ID = new ObjectId("5f78944ce0ee77725cd895eb");
	public static final ObjectId TIAN_MAO_FOLDER_ID = new ObjectId("5f7897e7e0ee77725cd89638");


    public static final String PRJ_FEE_TYPE_TRAVEL_FEE = "项目差旅费";

    public static final String PRJ_FEE_TYPE_CITY_CAR_FEE = "项目市内交通费";
    public static final String EDIT_TYPE_QUALITY_SM_USER = "qualitySmUser";

    public static final String PRJ_QUALITY_NAME_1 = "缺陷逃逸率";
	public static final String PRJ_QUALITY_NAME_2 = "缺陷率";
	public static final String PRJ_QUALITY_NAME_3 = "代码违规密度";
	public static final String PRJ_QUALITY_NAME_4 = "项目范围完成情况";
	public static final String PRJ_QUALITY_NAME_5 = "试运行故障要求";

	//人月均差旅费
	public static final ObjectId TRAVEL_FEE_BASE_LINE_ID = new ObjectId("663adf4acddb0c64b00048ac");
	//人月均餐费
	public static final ObjectId DINING_FEE_BASE_LINE_ID = new ObjectId("663adf86cddb0c64b000a331");
	//人月均其它费
	public static final ObjectId OTHER_FEE_BASE_LINE_ID = new ObjectId("663adfafcddb0c64b000dec8");
	//人月均差旅住宿费
	public static final ObjectId BL_TRAVEL_HOTEL_FEE_BASE_LINE_ID = new ObjectId("663ae030cddb0c64b0019c52");
	//人月均差旅交通费
	public static final ObjectId BL_TRAVEL_CITY_CAR_FEE_BASE_LINE_ID = new ObjectId("663adfd4cddb0c64b001191d");
	//人月均市内交通费
	public static final ObjectId BL_CITY_CAR_FEE_BASE_LINE_ID = new ObjectId("663adff3cddb0c64b0013f0a");

	/**
	 * BU_BSC
	 */
	public static final  String BU_CODE_BSC = "185";

	//为ObjectId("664af05c6658b3959157ed3e")，PM月度项目考评评分，pmMonthPrjEvalScore
	public static final ObjectId PM_MONTH_PRJ_EVAL_SCORE_ID = new ObjectId("664af05c6658b3959157ed3e");
	public static final String PM_MONTH_PRJ_EVAL_SCORE_NAME = "PM月度项目考评评分";
	public static final String PM_MONTH_PRJ_EVAL_SCORE_CODENAME = "pmMonthPrjEvalScore";

	public static final String COLOR_SIGN_RED = "红";
	public static final String COLOR_SIGN_ORANGE = "橙";
	public static final String COLOR_SIGN_YELLOW = "黄";
	public static final List<String> COLOR_SIGN_NAMES = Arrays.asList("红","橙","黄");
	//重点项目得分
	public static final ObjectId DEPT_PRJ_PFM_ITEM_IMPORT_PRJ_ID = new ObjectId("665fccf26658b39591849508");
	public static final String DEPT_PRJ_PFM_ITEM_IMPORT_PRJ_Name = "重点项目得分";
	//非重点项目得分
	public static final ObjectId DEPT_PRJ_PFM_ITEM_NOT_IMPORT_PRJ_ID = new ObjectId("665fcd186658b39591849758");
	public static final String DEPT_PRJ_PFM_ITEM_NOT_IMPORT_PRJ_Name = "非重点项目得分";
	//超基线得分
	public static final ObjectId DEPT_PRJ_PFM_ITEM_BEYOND_BASELINE_ID = new ObjectId("665fcd376658b39591849979");
	public static final String DEPT_PRJ_PFM_ITEM_BEYOND_BASELINE_Name = "超基线得分";
	//体系执行力得分
	public static final ObjectId DEPT_PRJ_PFM_ITEM_SYSTEM_EXECUTE_ID = new ObjectId("665fcd5a6658b39591849b81");
	public static final String DEPT_PRJ_PFM_ITEM_SYSTEM_EXECUTE_Name = "体系执行力得分";
	//终身追责
	public static final ObjectId DEPT_PRJ_PFM_ITEM_LIFELONG_ID = new ObjectId("665fcdba6658b3959184a1b2");
	//价值交付
	public static final ObjectId DEPT_PRJ_PFM_ITEM_DELIVER_VALUE_ID = new ObjectId("665fcdd46658b3959184a439");
	//投诉追责
	public static final ObjectId DEPT_PRJ_PFM_ITEM_COMPLAINT_ID = new ObjectId("669f52307dbb51149861876a");
	//审计追责
	public static final ObjectId DEPT_PRJ_PFM_ITEM_AUDIT_ID = new ObjectId("67d38d7dec5e633697dd4e7d");
	//数字化生产
	public static final ObjectId DEPT_PRJ_PFM_ITEM_DIGITAL_ID = new ObjectId("67d38d8eec5e633697dd509b");

	public static final ObjectId DEPT_PRJ_PFM_ITEM_ISRCDEF_ID = new ObjectId("665fcb8f6658b39591847f21");

	public static final List<ObjectId> DEPT_PRJ_PFM_IDs = Arrays.asList(
			new ObjectId("6864da5dc935b270664cc847"),
			new ObjectId("6864db18c935b270664ce7d3"),
			new ObjectId("6864db81c935b270664cfaba"),
			new ObjectId("6864dcacc935b270664d276b"),
			new ObjectId("6864ddf2c935b270664d5fd3"),
			new ObjectId("6864de39c935b270664d6c71"),
			new ObjectId("665ecf6f6658b39591779105"));
	//BSC项目绩效考评-工程总监
	public static final ObjectId DEPT_PRJ_PFM_EngDeptDirector_ID = new ObjectId("6864da5dc935b270664cc847");
	//BSC部门绩效考评-工程部
	public static final ObjectId DEPT_PRJ_PFM_EngDept_ID = new ObjectId("6864db18c935b270664ce7d3");
	//BSC部门绩效考评-区域
	public static final ObjectId DEPT_PRJ_PFM_Region_ID = new ObjectId("6864db81c935b270664cfaba");
	//BSC项目绩效考评-SRD总监
	public static final ObjectId DEPT_PRJ_PFM_SrdDirector_ID = new ObjectId("6864dcacc935b270664d276b");
	//BSC部门绩效考评-SRD一级
	public static final ObjectId DEPT_PRJ_PFM_SrdLevel1_ID = new ObjectId("6864ddf2c935b270664d5fd3");
	//BSC部门绩效考评-SRD二级
	public static final ObjectId DEPT_PRJ_PFM_SrdLevel2_ID = new ObjectId("6864de39c935b270664d6c71");
	//BSC部门绩效考评-省份
	public static final ObjectId DEPT_PRJ_PFM_ID = new ObjectId("665ecf6f6658b39591779105");
	public static final String DEPT_PRJ_PFM_NAME = "BSC部门项目绩效考评";
	public static final String DEPT_PRJ_PFM_CODENAME = "bscDeptPrjPfm";
	public static final String ACCT_TYPE_SRD = "SRD";
	public static final String ACCT_TYPE_SRD_Director = "srdDirector";
	public static final String ACCT_TYPE_SRD_Level1 = "srdLevel1";
	public static final String SYSTEM = "system";
	public static final String MODULE = "module";
	public static final String ONLINE_TASK = "上线任务";
	public static final String FALLBACK_TASK = "回退任务";

	public static final String PRJ_REVIEW_TYPE_ITEM = "prjReviewTypeItem";

	public static final String LOST_POINT = "lostPoint";
	public static final String POINT_DESC = "pointDesc";

	//BSC项目总监绩效考评
	public static final ObjectId BSC_PM_DIRECTOR_PRJ_PFM_ID = new ObjectId("669e27347dbb51149854f798");
	public static final String BSC_PM_DIRECTOR_PRJ_PFM_NAME = "BSC项目总监绩效考评";
	public static final String BSC_PM_DIRECTOR_PRJ_PFM_CODENAME = "bscPmDirectorPrjPfm";

	public static final String SYSTEM_PREDICT_AUTH_BU = "isBuAuth";
	public static final String SYSTEM_PREDICT_AUTH_PSO_DIRECTOR = "isPsoDirectorAuth";
	public static final String SYSTEM_PREDICT_AUTH_PSO_MANAGE = "isPsoManageAuth";
	public static final String SYSTEM_PREDICT_AUTH_SRD = "isSrdAuth";
	public static final String SYSTEM_PREDICT_AUTH_PRJ_DIRECTOR = "isPrjDirectorAuth";

	public static final String SYSTEM_PREDICT_AUTH_PMUSER = "isPmUserAuth";
	//版本号
	public static final ObjectId PMS_VALUE_ITEM_VER_ID = new ObjectId("5c46d888e0ee77405a14a35a");
	//价值项交付分类
	public static final ObjectId PMS_VALUE_ITEM_CLASSIFY_ID = new ObjectId("66bb137c104e786f52a6f997");
	//价值子类
	public static final ObjectId PMS_VALUE_ITEM_SUB_VALUE_ID = new ObjectId("66bb13844d11cd11735d4dda");
	//价值项
	public static final ObjectId PMS_VALUE_ITEM_VALUE_ITEM_ID = new ObjectId("66bb138c104e786f52a6f999");
	//价值评判标准
	public static final ObjectId PMS_VALUE_ITEM_JUDGE_STAND_ID = new ObjectId("66bb13d74d11cd11735d4de3");
	//权重
	public static final ObjectId PMS_VALUE_ITEM_WEIGHT_ID = new ObjectId("66bb13e1bc2f11194d3c4da8");
	//是否可量化
	public static final ObjectId PMS_VALUE_ITEM_QUANTIZATION_ID = new ObjectId("66bb140237b7f351a5df2394");
	//积分标准
	public static final ObjectId PMS_VALUE_ITEM_INTEGRAL_ID = new ObjectId("66bb140906ba5953f22d0512");
	//评判阶段
	public static final ObjectId PMS_VALUE_ITEM_JUDGE_STAGE_ID = new ObjectId("66bb14514d11cd11735d4e01");
	//评判角色
	public static final ObjectId PMS_VALUE_ITEM_JUDGE_ROLE_ID = new ObjectId("66bb147a9fd4b8606e79a304");
	//评判人
	public static final ObjectId PMS_VALUE_ITEM_JUDGE_EMP_ID = new ObjectId("66bb14afbc2f11194d3c524c");
	//业务类型id
	public static final ObjectId PMS_VALUE_ITEM_BIZ_TYPE_ID = new ObjectId("66bb111f37b7f351a5df204a");
	//价值交付项状态
	public static final ObjectId PMS_VALUE_ITEM_STATUS_ID = new ObjectId("67779b06bc0f0b3441830bd5");
	//价值交付项状态-待售前确认
	public static final ObjectId PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID = new ObjectId("67779b06bc0f0b3441830bd7");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_BEFORE_SALE = new TeIdName(PMS_VALUE_ITEM_STATUS_BEFORE_SALE_ID,"待售前确认");
	//价值交付项状态-待修改
	public static final ObjectId PMS_VALUE_ITEM_STATUS_MODIFY_ID = new ObjectId("67779b06bc0f0b3441830bd8");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_MODIFY = new TeIdName(PMS_VALUE_ITEM_STATUS_MODIFY_ID,"待修改");
	//价值交付项状态-已确认
	public static final ObjectId PMS_VALUE_ITEM_STATUS_CONFIRM_ID = new ObjectId("67779b06bc0f0b3441830bd9");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_CONFIRM = new TeIdName(PMS_VALUE_ITEM_STATUS_CONFIRM_ID,"已确认");
	//价值交付项状态-PM自评
	public static final ObjectId PMS_VALUE_ITEM_STATUS_PM_ID = new ObjectId("67eb486dfee7ca335e25d482");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_PM = new TeIdName(PMS_VALUE_ITEM_STATUS_PM_ID,"PM自评");
	//价值交付项状态-售前评估
	public static final ObjectId PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id = new ObjectId("67eb4875345a6b2a5ca17294");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_PreSale_Evaluation = new TeIdName(PMS_VALUE_ITEM_STATUS_PreSale_Evaluation_Id,"售前评估");
	//价值交付项状态-评估通告
	public static final ObjectId PMS_VALUE_ITEM_STATUS_Evaluation_Notice_ID = new ObjectId("67eb487e07b49a22638b100a");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_Evaluation_Notice = new TeIdName(PMS_VALUE_ITEM_STATUS_Evaluation_Notice_ID,"评估通告");
	//价值交付项状态-已考评
	public static final ObjectId PMS_VALUE_ITEM_STATUS_Evaluated_ID = new ObjectId("67ede128576b1c14eddfa859");
	public static final TeIdName PMS_VALUE_ITEM_STATUS_Evaluated = new TeIdName(PMS_VALUE_ITEM_STATUS_Evaluated_ID,"已评估");
	//PM自评-是否达成
	public static final ObjectId PMS_VALUE_ITEM_Pm_Achieve_ID = new ObjectId("67eb48b0734017667ea0d747");
	//PM自评-未达成
	public static final ObjectId PMS_VALUE_ITEM_Pm_Not_Achieve_ID = new ObjectId("67eb48b0734017667ea0d74a");
	//PM自评-说明
	public static final ObjectId PMS_VALUE_ITEM_Pm_Describe_ID = new ObjectId("67eb48b9fee7ca335e25d485");
	//售前评估-是否达成
	public static final ObjectId PMS_VALUE_ITEM_PreSale_Achieve_ID = new ObjectId("67eb494692f18f162dee532e");
	//售前评估-未达成
	public static final ObjectId PMS_VALUE_ITEM_PreSale_Not_Achieve_ID = new ObjectId("67eb494692f18f162dee5331");
	//售前评估-说明
	public static final ObjectId PMS_VALUE_ITEM_PreSale_Describe_ID = new ObjectId("67eb495092f18f162dee5335");

	public static final List<Integer> monthList = Arrays.asList(1,2,3);

	public static final ObjectId psoResourceDeptId = new ObjectId("62fca7a5183dde504b3cb50e");
	public static final ObjectId CRMResourceDeptId = new ObjectId("62fca7a5183dde504b3cb510");
	public static final ObjectId IPRResourceDeptId = new ObjectId("62fca7a5183dde504b3cb515");

	public static final ObjectId PRJ_STATUS_PM_APPOINT_ID = new ObjectId("5dc3c234c56a424c33470467"); //项目经理任命
	//CRM,BILLING,IISC,ISAC,MSS,DADO,SP
	public static final List<ObjectId> SRD_DEF_IDS = Arrays.asList(
			new ObjectId("62fca7a5183dde504b3cb510"),
			new ObjectId("62fca7a5183dde504b3cb511"),
			new ObjectId("62fca7a5183dde504b3cb513"),
			new ObjectId("62fca7a5183dde504b3cb512"),
			new ObjectId("65a0fbc9cddb0c64b07c3e52"),
			new ObjectId("62fca7a5183dde504b3cb514"),
			new ObjectId("62fca7a5183dde504b3cb517")
	);

	public static final ObjectId PRJ_LEVEL_ID = new ObjectId("5c6e37f7900add501a141901");

	public final static TeIdNameCn EIP_ORG_STRUCTURE = new TeIdNameCn(new ObjectId("666115636658b3959195b309"),
			"EIP组织架构", "eipOrgStructure");

	/**
	 * 开发人员
	 */
	public final static ObjectId DEF_ID_USER_ROLE_DEV = new ObjectId("6114e83e952372e6ad1bdd89");
	/**
	 * 需求人员
	 */
	public final static ObjectId DEF_ID_USER_ROLE_BA = new ObjectId("6114e861952372e6ad1c1730");
	/**
	 * 测试人员
	 */
	public final static ObjectId DEF_ID_USER_ROLE_TEST = new ObjectId("6114e7e2952372e6ad1b6b68");

	/**
	 * 人员
	 */
	public final static String WORK_HOUR_SEARCH_TYPE_HUMAN = "human";
	/**
	 * 产品线工时-度量角色
	 */
	public final static String WORK_HOUR_SEARCH_TYPE_PL_EVAL_ROLE = "plEvalRole";
	/**
	 * 产品线工时-度量角色
	 */
	public final static String WORK_HOUR_SEARCH_TYPE_PL_EMPLOYEE_TYPE = "plEmployeeType";
	/**
	 * 产品线工时-度量角色
	 */
	public final static String WORK_HOUR_SEARCH_TYPE_PL_BAND_TYPE = "plBandType";
	public final static String IS_ON_THE_JOB_YES = "在职";
	public final static String IS_ON_THE_JOB_NO = "离职";

	public final static String PMS_BSC_PROV_MERGE_CNFG = "pmsBscProvMergeCnfg";
	public final static String PMS_BSC_LEVEL_MERGE_CNFG = "pmsBscPrjLevelMergeCnfg";

	public final static List<ObjectId> SPECIAL_BIZ_STATUS_IDS = Arrays.asList(
			SysDefConstants.DEF_ID_BIZ_STATUS_CANCELED,
			SysDefConstants.DEF_ID_BIZ_STATUS_POSITIVE_CLOSED,
			SysDefConstants.DEF_ID_BIZ_STATUS_DELETED,
			SysDefConstants.DEF_ID_BIZ_STATUS_HANGED,
			SysDefConstants.DEF_ID_BIZ_STATUS_REFUSED,
			SysDefConstants.DEF_ID_BIZ_STATUS_ALTEREDTOOTHERBIZ);

	public final static List<ObjectId> EXCLUDE_REGION_ENGDEPT_PROV_IDS = Arrays.asList(
			new ObjectId("64059be11d740fa6042afbd7"),//区域P1
			new ObjectId("63edfcfb1d740fa604bb3026"),//区域其他
			new ObjectId("64059cb91d740fa6042bcd0c"),//省份P1
			new ObjectId("63edfe351d740fa604bced69")//省份其他
	);

	public static final String PRE_SALES = "preSales";
	//售前人员角色id
	public static final ObjectId PRE_SALES_ROLE_ID = new ObjectId("6777b36e8a13e55b7772d68f");

	public static final String VALUE_ITEM_OPERATE_TYPE_REJECT = "reject";
	public static final String VALUE_ITEM_OPERATE_TYPE_CONFIRM = "confirm";
	public static final String VALUE_ITEM_OPERATE_TYPE_SUBMIT = "submit";
	public static final String VALUE_ITEM_OPERATE_TYPE_PmEval = "pmEval";//发起PM自评
	public static final String VALUE_ITEM_OPERATE_TYPE_PmSelfEval = "pmSelfEval";//PM自我评价
	public static final String VALUE_ITEM_OPERATE_TYPE_PmEvalSubmit = "pmEvalSubmit";//PM自评-提交
	public static final String VALUE_ITEM_OPERATE_TYPE_PreSaleSelfEval = "preSaleSelfEval";//售前评估自我评价
	public static final String VALUE_ITEM_OPERATE_TYPE_PreSaleEvalSubmit = "preSaleEvalSubmit";//售前评估-提交
	public static final String VALUE_ITEM_OPERATE_TYPE_EvalNotice = "evalNotice";//评估通告
	public static final ObjectId DEF_ID_PROGRESS_INDICATE_NORMAL_ENDED = new ObjectId("5df836ebc56a424c334704f1");
	public static final ObjectId DEF_ID_PROGRESS_INDICATE_DELAY_ENDED = new ObjectId("5df83723c56a424c334704f2");
	public static final ObjectId DEF_ID_PROGRESS_INDICATE_DELAY_NOT_ENDED = new ObjectId("5df8375bc56a424c334704f4");

	public static final String DEF_AGAIN = "again";
	public static final String DEF_SM_AGAIN = "smAgain";
	public static final String DEF_PRJ_ZERO_CLEAR = "prjZeroClear";
	public static final String DEF_SM_ZERO_CLEAR = "smZeroClear";

	public static ObjectId PRJ_REQ_CHG_MGT_DEF_ID = new ObjectId("67e25d8ffb03e0e1731b1b1d");
	public static ObjectId FLOW_PRJ_CHANGE_DEF_ID = new ObjectId("67d12884ec5e633697903dfa");
	//提交项目需求变更
	public static final ObjectId DEF_ID_PRJ_CHANGE_NODE_SUBMIT = new ObjectId("67d1291eec5e6336979051a1");

	//BU运营管理员审批
	public static final ObjectId DEF_ID_PRJ_CHANGE_NODE_BU = new ObjectId("67d129a3ec5e6336979064e2");
	//PSO/SRD经理/总监审批
	public static final ObjectId DEF_ID_PRJ_CHANGE_NODE_PSO_SRD = new ObjectId("67d129e5ec5e633697906ce0");
	//BU运营管理员审批发布备案
	public static final ObjectId DEF_ID_PRJ_CHANGE_NODE_BU_PUBLISH = new ObjectId("67d12a0dec5e633697907299");

	//启动绩效评分
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_START = new ObjectId("6821c393fb03e0e1732c0162");
	//提交绩效评分
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_SUBMIT = new ObjectId("6821c3aafb03e0e1732c0706");
	//计算&合规审核
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_CALCULATE = new ObjectId("6821c3defb03e0e1732c146b");
	//额度审核
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_QUOTA = new ObjectId("6821c418fb03e0e1732c2368");
	//项目经理确认
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_PM = new ObjectId("6821c433fb03e0e1732c2a88");
	//考评管理员审核
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_ADMIN = new ObjectId("6821c450fb03e0e1732c32ca");
	//事业部领导审核
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_LEADER = new ObjectId("6821c4a4fb03e0e1732c4892");
	//存档并通知
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_SAVE = new ObjectId("6821c4bcfb03e0e1732c4e90");
	//已审批结束
	public static final ObjectId DEF_ID_AMS_APPR_STATUS_END = new ObjectId("68243692fb03e0e17382750e");
	//BSC项目激励申请
	public static final ObjectId DEF_ID_BSC_PRJ_BONUS_APPLY = new ObjectId("68216447fb03e0e17312895c");
	/**
	 * 项目人员角色-项目经理
	 */
	public static final ObjectId PRJ_USER_ROLE_PM = new ObjectId("5a4ae684ba4014a42391fa91");
	/**
	 * 项目人员角色-开发经理
	 */
	public static final ObjectId PRJ_USER_ROLE_DEV_MANAGER = new ObjectId("6543658864586deda4461a48");
	/**
	 * 项目人员角色-测试经理
	 */
	public static final ObjectId PRJ_USER_ROLE_TEST_MANAGER = new ObjectId("6543655364586deda44616de");
	/**
	 * 项目人员角色-项目组长
	 */
	public static final ObjectId PRJ_USER_ROLE_GROUP_RESP = new ObjectId("5a45f2bdba4014a42391fa8d");
	/**
	 * 项目人员角色-技术经理
	 */
	public static final ObjectId PRJ_USER_ROLE_TECHNICAL_MANAGER = new ObjectId("5df2fa94c56a424c334704bd");

	/**
	 * 项目人员角色-工程师
	 */
	public static final ObjectId PRJ_USER_ROLE_ENGINEER = new ObjectId("654365ad64586deda4461d62");

	/**
	 * 项目助理
	 */
	public static final ObjectId PRJROLE_PMASSISTANT = new ObjectId("5df2fc06c56a424c334704c1");
	/**
	 * PMS项目组人员岗位-普通成员
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_EMP = new ObjectId("682442cffb03e0e17383b7ea");
	/**
	 * PMS项目组人员岗位-核心骨干
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_KEY_EMP = new ObjectId("68244265fb03e0e17383ae79");
	/**
	 * PMS项目组人员岗位-模块组长
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_MODULE_RESP = new ObjectId("682441eefb03e0e17383a5bb");
	/**
	 * PMS项目组人员岗位-大组长
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_BIG_GROUP_RESP = new ObjectId("68244153fb03e0e17383970b");
	/**
	 * PMS项目组人员岗位-技术经理
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_TECH_MGT = new ObjectId("68244011fb03e0e173837812");
	/**
	 * PMS项目组人员岗位-技术总监
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_TECH_SM = new ObjectId("68243f3ffb03e0e173836495");
	/**
	 * PMS项目组人员岗位-项目经理
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_PM = new ObjectId("68243f92fb03e0e173836d1f");
	/**
	 * PMS项目组人员岗位-项目总监
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_PRJ_SM = new ObjectId("68243af2fb03e0e17382e9ea");
	/**
	 * PMS项目组人员岗位-子项目经理
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_SUB_PM = new ObjectId("682440aefb03e0e1738389fe");
	/**
	 * PMS项目组人员岗位-技术专家
	 */
	public static final ObjectId PMS_PRJ_EMP_TITLE_TECH_EXPERT = new ObjectId("68244050fb03e0e173837f13");
	/**
	 * 度量体系类型：项目型
	 */
	public static final ObjectId MSMT_SYS_TYPE_PRJ = new ObjectId("67808aea8a13e55b777f7713");
	/**
	 * 度量指标
	 */
	public static final ObjectId EVALUATE_TYPE_PFM_MSMT_INDEX = new ObjectId("6784bcf48a13e55b77b79900");
	/**
	 * 工作量得分
	 */
	public static final String DEV_WORKLOAD = "DEV_WORKLOAD";
	/**
	 * 测试人员工作量得分
	 */
	public static final String TEST_WORKLOAD = "TEST_WORKLOAD";
	/**
	 * 需求人员工作量得分
	 */
	public static final String BA_WORKLOAD = "BA_WORKLOAD";

	/**
	 * 开发人员质量得分
	 */
	public static final String DEV_QUALITY = "DEV_QUALITY";
	/**
	 * 测试人员质量得分
	 */
	public static final String TEST_QUALITY = "TEST_QUALITY";
	/**
	 * 需求人员质量得分
	 */
	public static final String BA_QUALITY = "BA_QUALITY";

	public static final ObjectId PRJ_DIGITAL_TOOL_TYPE_ID_DMP = new ObjectId("6825866afb03e0e173a0b921");
	public static final ObjectId PRJ_DIGITAL_TOOL_TYPE_ID_NOT_DMP = new ObjectId("6825868dfb03e0e173a0bbd0");
	//剔除以下资源部门不计算，其他都参与
	//PSO,OTHERS,质量,CCD,PRD-平台,PRD-数据,PRD-网络,PRD-技术支持中心-IT,PRD-技术支持中心-云网
	//PRD-技术支持中心-数智,PRD-AI实验室,PRD-人工智能,PRD-数字孪生与GIS,SPU1-边缘智算,SPU2-可信数据流通,SPU3-5G专网
	public static final List<ObjectId> EXCLUDE_SRD_DEF_IDS = Arrays.asList(
			new ObjectId("62fca7a5183dde504b3cb50e"),
			new ObjectId("62fca7a5183dde504b3cb50f"),
			new ObjectId("62fca7a5183dde504b3cb519"),
			new ObjectId("62fca7a5183dde504b3cb51a"),
			new ObjectId("62fca7a5183dde504b3cb51f"),
			new ObjectId("62fca7a5183dde504b3cb521"),
			new ObjectId("62fca7a5183dde504b3cb522"),
			new ObjectId("65a0fc27cddb0c64b07cb22b"),
			new ObjectId("65a0fc3acddb0c64b07cd9a6"),
			new ObjectId("65a0fc4dcddb0c64b07cee5a"),
			new ObjectId("62fca7a5183dde504b3cb524"),
			new ObjectId("62fca7a5183dde504b3cb525"),
			new ObjectId("62fca7a5183dde504b3cb526"),
			new ObjectId("67f78e33cddb0c64b0e4014f"),
			new ObjectId("67f78e5fcddb0c64b0e461b7"),
			new ObjectId("67f78e76cddb0c64b0e49dd5")
	);
	//自然日
	public static final ObjectId cal_Day_Id = new ObjectId("68746a05c935b27066142a46");
	//工作日
	public static final ObjectId work_Day_Id = new ObjectId("68746a21c935b27066142e54");
}
