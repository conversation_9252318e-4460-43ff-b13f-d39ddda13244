package com.linkus.prjMd.vo;

import java.util.Map;

public class ResourceManageEmpDateInfosQuerymonthVo {
    private String jobCode;
    private String userName;
    private String loginName;
    private String employeeType;
    private String timeSheetFlag;
    private String ccName;
    private String sbuName;
    private Integer count;
    private Integer countPed;
    private Integer rn;
    private String hireDate;
    private String terminationDate;
    private String holiDayDate;
    private Integer oaId;
    private Map<String, Integer> noMap;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getHoliDayDate() {
        return holiDayDate;
    }

    public void setHoliDayDate(String holiDayDate) {
        this.holiDayDate = holiDayDate;
    }

    public Map<String, Integer> getNoMap() {
        return noMap;
    }

    public void setNoMap(Map<String, Integer> noMap) {
        this.noMap = noMap;
    }

    public Integer getOaId() {
        return oaId;
    }

    public void setOaId(Integer oaId) {
        this.oaId = oaId;
    }

    public Integer getCountPed() {
        return countPed;
    }

    public void setCountPed(Integer countPed) {
        this.countPed = countPed;
    }

    public String getHireDate() {
        return hireDate;
    }

    public String getTerminationDate() {
        return terminationDate;
    }

    public void setTerminationDate(String terminationDate) {
        this.terminationDate = terminationDate;
    }

    public void setHireDate(String hireDate) {
        this.hireDate = hireDate;
    }

    public Integer getRn() {
        return rn;
    }

    public void setRn(Integer rn) {
        this.rn = rn;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmployeeType() {
        return employeeType;
    }

    public void setEmployeeType(String employeeType) {
        this.employeeType = employeeType;
    }

    public String getTimeSheetFlag() {
        return timeSheetFlag;
    }

    public void setTimeSheetFlag(String timeSheetFlag) {
        this.timeSheetFlag = timeSheetFlag;
    }

    public String getCcName() {
        return ccName;
    }

    public void setCcName(String ccName) {
        this.ccName = ccName;
    }

    public String getSbuName() {
        return sbuName;
    }

    public void setSbuName(String sbuName) {
        this.sbuName = sbuName;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
