package com.linkus.mail.model.po;

import java.util.Date;
import java.util.List;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.mail.model.po.inner.MailAttachment;
import com.linkus.mail.model.po.inner.MailImage;


@Document(collection = "mailSended")
public class TeMailSended {
	
	@Id
	private ObjectId 				id;		
	private ObjectId 				mailToSendId;
	private ObjectId				bizId;							
	private TeUser 					mailSender;	
	private String 					mailFrom;		
	private String 					mailRecipient;								
	private String 					mailRecipientCc;
	private String 					mailSubject;
	private String 					mailContent;
	private Date 					toSendTime;
	private String 					sendHostAndIp;
	private Date 					sendTime;
	private List<MailImage>			images;
	private List<MailAttachment>	attachments;
	private String 					mailboxServerType;
	private String 					fromMailboxName;

	/**
	 * 子系统
	 * @see com.linkus.base.constants.DMPSubsystem
	 */
	private String                  subsystem;
	
	public ObjectId getId() {
		return id;
	}
	public void setId(ObjectId id) {
		this.id = id;
	}
	public ObjectId getBizId() {
		return bizId;
	}
	public void setBizId(ObjectId bizId) {
		this.bizId = bizId;
	}
	public TeUser getMailSender() {
		return mailSender;
	}
	public void setMailSender(TeUser mailSender) {
		this.mailSender = mailSender;
	}
	public String getMailRecipient() {
		return mailRecipient;
	}
	public void setMailRecipient(String mailRecipient) {
		this.mailRecipient = mailRecipient;
	}
	public String getMailRecipientCc() {
		return mailRecipientCc;
	}
	public void setMailRecipientCc(String mailRecipientCc) {
		this.mailRecipientCc = mailRecipientCc;
	}
	public String getMailSubject() {
		return mailSubject;
	}
	public void setMailSubject(String mailSubject) {
		this.mailSubject = mailSubject;
	}
	public String getMailContent() {
		return mailContent;
	}
	public void setMailContent(String mailContent) {
		this.mailContent = mailContent;
	}
	public Date getToSendTime() {
		return toSendTime;
	}
	public void setToSendTime(Date toSendTime) {
		this.toSendTime = toSendTime;
	}
	public String getSendHostAndIp() {
		return sendHostAndIp;
	}
	public void setSendHostAndIp(String sendHostAndIp) {
		this.sendHostAndIp = sendHostAndIp;
	}
	public Date getSendTime() {
		return sendTime;
	}
	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}
	public List<MailImage> getImages() {
		return images;
	}
	public void setImages(List<MailImage> images) {
		this.images = images;
	}
	public List<MailAttachment> getAttachments() {
		return attachments;
	}
	public void setAttachments(List<MailAttachment> attachments) {
		this.attachments = attachments;
	}
	public String getMailFrom() {
		return mailFrom;
	}
	public void setMailFrom(String mailFrom) {
		this.mailFrom = mailFrom;
	}
	public ObjectId getMailToSendId() {
		return mailToSendId;
	}
	public void setMailToSendId(ObjectId mailToSendId) {
		this.mailToSendId = mailToSendId;
	}
	public String getMailboxServerType() {
		return mailboxServerType;
	}
	public void setMailboxServerType(String mailboxServerType) {
		this.mailboxServerType = mailboxServerType;
	}
	public String getFromMailboxName() {
		return fromMailboxName;
	}
	public void setFromMailboxName(String fromMailboxName) {
		this.fromMailboxName = fromMailboxName;
	}

	public String getSubsystem() {
		return subsystem;
	}

	public void setSubsystem(String subsystem) {
		this.subsystem = subsystem;
	}
}
