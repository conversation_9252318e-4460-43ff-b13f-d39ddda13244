package com.linkus.mail.service;

import java.util.List;

import org.bson.types.ObjectId;

import com.linkus.mail.param.MailInfo;

public interface IMailService {

    /**
     * 发送邮件单个接口
     *
     * @param mail
     */
    ObjectId sendMail(MailInfo mail);

    /**
     * 发送邮件批量接口
     *
     * @param mailInfos
     */
    void sendMails(List<MailInfo> mailInfos);

//	/**
//	 * 设置邮件是否为草稿邮件标志
//	 * @param mailId
//	 * @param isDraft
//	 */
//	void setMailDraft(ObjectId mailId, boolean isDraft);

    /**
     * 从待发送获取邮件信息(根据 mailToSendId来查询)
     *
     * @param mailId
     * @return
     */
    MailInfo getMailToSend(ObjectId mailToSendId);

    /**
     * 从已发送获取邮件信息(根据 mailToSendId来查询)
     *
     * @param mailId
     * @return
     */
    MailInfo getMailSended(ObjectId mailToSendId);

//	/**
//	 * 将邮件从草稿箱设置为待发送
//	 * @param mailId
//	 */
//	void mailReadyToSend(ObjectId mailId);

//	/**
//	 * 更新待发送邮件，一般为草稿箱邮件
//	 *
//	 * @param mailToSendId
//	 * @param mailId
//	 */
//	void updateDraftMail(ObjectId mailToSendId, MailInfo mail);

    /**
     * @param mailContent
     * @return
     */
    MailInfo getFromBase64Content(String mailContent);
}
