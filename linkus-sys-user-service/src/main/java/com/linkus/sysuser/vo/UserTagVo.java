package com.linkus.sysuser.vo;

import com.linkus.base.db.mongo.model.TeIdNameStr;

import java.util.List;

public class UserTagVo {
    private TeIdNameStr posts;
    private TeIdNameStr personLevel;
    private TeIdNameStr communicate;
    private TeIdNameStr cooperate;
    private List<TeIdNameStr> prdSkills;
    private List<TeIdNameStr> devLangSkills;
    private List<TeIdNameStr> subPrdSkills;
    private List<TeIdNameStr> middleWareSkills;
    private List<TeIdNameStr> dbSkills;
    private List<TeIdNameStr> testSkills;
    
    private List<TeIdNameStr> planSkills;
    private List<TeIdNameStr> frameSkills;
    private List<TeIdNameStr> reqSkills;
    private List<TeIdNameStr> analysisSkills;
    private List<TeIdNameStr> ibmsSkills;
    private List<TeIdNameStr> officeSkills;
    private List<TeIdNameStr> hrSkills;

    public TeIdNameStr getPosts() {
        return posts;
    }

    public void setPosts(TeIdNameStr posts) {
        this.posts = posts;
    }

    public List<TeIdNameStr> getPrdSkills() {
        return prdSkills;
    }

    public void setPrdSkills(List<TeIdNameStr> prdSkills) {
        this.prdSkills = prdSkills;
    }

    public List<TeIdNameStr> getDevLangSkills() {
        return devLangSkills;
    }

    public void setDevLangSkills(List<TeIdNameStr> devLangSkills) {
        this.devLangSkills = devLangSkills;
    }

    public List<TeIdNameStr> getSubPrdSkills() {
        return subPrdSkills;
    }

    public void setSubPrdSkills(List<TeIdNameStr> subPrdSkills) {
        this.subPrdSkills = subPrdSkills;
    }

    public List<TeIdNameStr> getMiddleWareSkills() {
        return middleWareSkills;
    }

    public void setMiddleWareSkills(List<TeIdNameStr> middleWareSkills) {
        this.middleWareSkills = middleWareSkills;
    }

    public List<TeIdNameStr> getDbSkills() {
        return dbSkills;
    }

    public void setDbSkills(List<TeIdNameStr> dbSkills) {
        this.dbSkills = dbSkills;
    }

    public TeIdNameStr getPersonLevel() {
        return personLevel;
    }

    public void setPersonLevel(TeIdNameStr personLevel) {
        this.personLevel = personLevel;
    }

    public TeIdNameStr getCommunicate() {
        return communicate;
    }

    public void setCommunicate(TeIdNameStr communicate) {
        this.communicate = communicate;
    }

    public TeIdNameStr getCooperate() {
        return cooperate;
    }

    public void setCooperate(TeIdNameStr cooperate) {
        this.cooperate = cooperate;
    }

    public List<TeIdNameStr> getTestSkills() {
        return testSkills;
    }

    public void setTestSkills(List<TeIdNameStr> testSkills) {
        this.testSkills = testSkills;
    }

	public List<TeIdNameStr> getPlanSkills() {
		return planSkills;
	}

	public void setPlanSkills(List<TeIdNameStr> planSkills) {
		this.planSkills = planSkills;
	}

	public List<TeIdNameStr> getFrameSkills() {
		return frameSkills;
	}

	public void setFrameSkills(List<TeIdNameStr> frameSkills) {
		this.frameSkills = frameSkills;
	}

	public List<TeIdNameStr> getReqSkills() {
		return reqSkills;
	}

	public void setReqSkills(List<TeIdNameStr> reqSkills) {
		this.reqSkills = reqSkills;
	}

	public List<TeIdNameStr> getAnalysisSkills() {
		return analysisSkills;
	}

	public void setAnalysisSkills(List<TeIdNameStr> analysisSkills) {
		this.analysisSkills = analysisSkills;
	}

	public List<TeIdNameStr> getIbmsSkills() {
		return ibmsSkills;
	}

	public void setIbmsSkills(List<TeIdNameStr> ibmsSkills) {
		this.ibmsSkills = ibmsSkills;
	}

	public List<TeIdNameStr> getOfficeSkills() {
		return officeSkills;
	}

	public void setOfficeSkills(List<TeIdNameStr> officeSkills) {
		this.officeSkills = officeSkills;
	}

	public List<TeIdNameStr> getHrSkills() {
		return hrSkills;
	}

	public void setHrSkills(List<TeIdNameStr> hrSkills) {
		this.hrSkills = hrSkills;
	}
    
}
