package com.linkus.sysuser.vo;

import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.sysuser.model.TeKmToken;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


public class TeSysUserVo implements Serializable {

	private static final long serialVersionUID = 1L;

	private ObjectId _id;
	private ObjectId id;
	private Integer dmpId;
	private Integer oaId;
	private String loginName;
	private String userName;
	private String userNameAndLoginName;
	private String jobCode;
	private String mailBox;
	private Integer dmpUserTypeId;
	private String dmpUserType;
	private Integer dmpUserStatusId;
	private String dmpUserStatus;
	private Boolean isValid;
	private String bgId;
	private String bgName;
	private String sbuId;
	private String sbuName;
	private String ccId;
	private String ccName;
	private String orgName;
	private String employeeType;
	private String band;
	private String bandType;
	private String timeSheetFlag;
	//所在小组名称
	private String groupName;
	//所在小组id
	private String groupIdValue;
	private String roleCode;
	private List<String> roleCodes;
	//头像路径
	private String headImagePath;

	//角色ID
	private List<String> roleIds;
	private List<String> roleNames;
	// 用户描述
	private String desc;
	
	private String plName;
    private String name;
    private String sysDefUserInfoDesc;
    private String startDate;
    private String endDate;
    private List<TeKmToken> kmToken;
    private Boolean bizViewBasicInfoIsPacked;
    
    private String jobFamily;
	private String secondJobFamily;
	
	private String role;//角色
	private String operate;//操作权限

	private List<TeSysDefRoleUser> detailInfo;//用来存放员工加入项目日期和退出项目日期的详情

	private String ccNameCn;

	private TeUser manager;

	private ObjectId userId;

	private String  empWorkType;//办公方式
	private TeIdNameCn empWorkTypeCn;
	private String trainAdminCcList;
	private List<TeIdNameCn> cndtItems;
	private String valid;
	private Date approveDate;

	public String getValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid = valid;
	}


	public List<String> getRoleNames() {
		return roleNames;
	}

	public void setRoleNames(List<String> roleNames) {
		this.roleNames = roleNames;
	}

	public List<TeIdNameCn> getCndtItems() {
		return cndtItems;
	}

	public void setCndtItems(List<TeIdNameCn> cndtItems) {
		this.cndtItems = cndtItems;
	}

	public TeIdNameCn getEmpWorkTypeCn() {
		return empWorkTypeCn;
	}

	public void setEmpWorkTypeCn(TeIdNameCn empWorkTypeCn) {
		this.empWorkTypeCn = empWorkTypeCn;
	}

	public String getEmpWorkType() {
		return empWorkType;
	}

	public void setEmpWorkType(String empWorkType) {
		this.empWorkType = empWorkType;
	}

	public ObjectId getUserId() {
		return userId;
	}

	public void setUserId(ObjectId userId) {
		this.userId = userId;
	}

	public TeUser getManager() {
		return manager;
	}

	public void setManager(TeUser manager) {
		this.manager = manager;
	}

	public String getCcNameCn() {
		return ccNameCn;
	}

	public void setCcNameCn(String ccNameCn) {
		this.ccNameCn = ccNameCn;
	}

	public List<TeSysDefRoleUser> getDetailInfo() {
		return detailInfo;
	}

	public void setDetailInfo(List<TeSysDefRoleUser> detailInfo) {
		this.detailInfo = detailInfo;
	}

	public String getEmployeeType() {
		return employeeType;
	}

	public void setEmployeeType(String employeeType) {
		this.employeeType = employeeType;
	}

	public String getBand() {
		return band;
	}

	public void setBand(String band) {
		this.band = band;
	}

	public String getBandType() {
		return bandType;
	}

	public void setBandType(String bandType) {
		this.bandType = bandType;
	}

	public String getTimeSheetFlag() {
		return timeSheetFlag;
	}

	public void setTimeSheetFlag(String timeSheetFlag) {
		this.timeSheetFlag = timeSheetFlag;
	}
	
	public String getIdValue(){
		if(id==null){
			return null;
		}
		return id.toString();
	}

	public ObjectId getId() {
		return id;
	}

	public void setId(ObjectId id) {
		this.id = id;
	}

	public Integer getDmpId() {
		return dmpId;
	}

	public void setDmpId(Integer dmpId) {
		this.dmpId = dmpId;
	}

	public Integer getOaId() {
		return oaId;
	}

	public void setOaId(Integer oaId) {
		this.oaId = oaId;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getJobCode() {
		return jobCode;
	}

	public void setJobCode(String jobCode) {
		this.jobCode = jobCode;
	}

	public String getMailBox() {
		return mailBox;
	}

	public void setMailBox(String mailBox) {
		this.mailBox = mailBox;
	}

	public Integer getDmpUserTypeId() {
		return dmpUserTypeId;
	}

	public void setDmpUserTypeId(Integer dmpUserTypeId) {
		this.dmpUserTypeId = dmpUserTypeId;
	}

	public String getDmpUserType() {
		return dmpUserType;
	}

	public void setDmpUserType(String dmpUserType) {
		this.dmpUserType = dmpUserType;
	}

	public Integer getDmpUserStatusId() {
		return dmpUserStatusId;
	}

	public void setDmpUserStatusId(Integer dmpUserStatusId) {
		this.dmpUserStatusId = dmpUserStatusId;
	}

	public String getDmpUserStatus() {
		return dmpUserStatus;
	}

	public void setDmpUserStatus(String dmpUserStatus) {
		this.dmpUserStatus = dmpUserStatus;
	}

	public Boolean getIsValid() {
		return isValid;
	}

	public void setIsValid(Boolean isValid) {
		this.isValid = isValid;
	}

	public String getBgId() {
		return bgId;
	}

	public void setBgId(String bgId) {
		this.bgId = bgId;
	}

	public String getBgName() {
		return bgName;
	}

	public void setBgName(String bgName) {
		this.bgName = bgName;
	}

	public String getSbuId() {
		return sbuId;
	}

	public void setSbuId(String sbuId) {
		this.sbuId = sbuId;
	}

	public String getSbuName() {
		return sbuName;
	}

	public void setSbuName(String sbuName) {
		this.sbuName = sbuName;
	}

	public String getCcId() {
		return ccId;
	}

	public void setCcId(String ccId) {
		this.ccId = ccId;
	}

	public String getCcName() {
		return ccName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public void setCcName(String ccName) {
		this.ccName = ccName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public String getGroupIdValue() {
		return groupIdValue;
	}

	public void setGroupIdValue(String groupIdValue) {
		this.groupIdValue = groupIdValue;
	}

	public List<String> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<String> roleIds) {
		this.roleIds = roleIds;
	}

	public String getRoleCode() {
		return roleCode;
	}

	public void setRoleCode(String roleCode) {
		this.roleCode = roleCode;
	}  
	
	public String getHeadImagePath() {
		return headImagePath;
	}

	public void setHeadImagePath(String headImagePath) {
		this.headImagePath = headImagePath;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getPlName() {
		return plName;
	}

	public void setPlName(String plName) {
		this.plName = plName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSysDefUserInfoDesc() {
		return sysDefUserInfoDesc;
	}

	public void setSysDefUserInfoDesc(String sysDefUserInfoDesc) {
		this.sysDefUserInfoDesc = sysDefUserInfoDesc;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public List<TeKmToken> getKmToken() {
		return kmToken;
	}

	public void setKmToken(List<TeKmToken> kmToken) {
		this.kmToken = kmToken;
	}

	public Boolean getBizViewBasicInfoIsPacked() {
		return bizViewBasicInfoIsPacked;
	}

	public void setBizViewBasicInfoIsPacked(Boolean bizViewBasicInfoIsPacked) {
		this.bizViewBasicInfoIsPacked = bizViewBasicInfoIsPacked;
	}

	public String getJobFamily() {
		return jobFamily;
	}

	public void setJobFamily(String jobFamily) {
		this.jobFamily = jobFamily;
	}

	public String getSecondJobFamily() {
		return secondJobFamily;
	}

	public void setSecondJobFamily(String secondJobFamily) {
		this.secondJobFamily = secondJobFamily;
	}

	public String getUserNameAndLoginName() {
		return userNameAndLoginName;
	}

	public void setUserNameAndLoginName(String userNameAndLoginName) {
		this.userNameAndLoginName = userNameAndLoginName;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

	public String getOperate() {
		return operate;
	}

	public void setOperate(String operate) {
		this.operate = operate;
	}


	public String getTrainAdminCcList() {
		return trainAdminCcList;
	}

	public void setTrainAdminCcList(String trainAdminCcList) {
		this.trainAdminCcList = trainAdminCcList;
	}


	public List<String> getRoleCodes() {
		return roleCodes;
	}

	public void setRoleCodes(List<String> roleCodes) {
		this.roleCodes = roleCodes;
	}

	public Date getApproveDate() {
		return approveDate;
	}

	public void setApproveDate(Date approveDate) {
		this.approveDate = approveDate;
	}
}
