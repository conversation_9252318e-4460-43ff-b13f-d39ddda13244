package com.linkus.sysuser.vo;

import org.bson.types.ObjectId;

import java.util.List;

public class UserSkillVo {
    private ObjectId postId;
    private ObjectId personLevelId;
    private String jobCode;
    private List<SkillInfo> prdSkillIds;
    private List<SkillInfo> subPrdSkillIds;
    private List<SkillInfo> devLangSkillIds;
    private List<SkillInfo> middleWareSkillIds;
    private List<SkillInfo> dbSkillIds;
    private List<SkillInfo> testSkills;
    
    private List<SkillInfo> planSkillIds;
    private List<SkillInfo> frameSkillIds;
    private List<SkillInfo> reqSkillIds;
    private List<SkillInfo> analysisSkillIds;
    private List<SkillInfo> ibmsSkillIds;
    private List<SkillInfo> officeSkillIds;
    private List<SkillInfo> hrSkillIds;       
    
    private ObjectId communicateId;
    private ObjectId cooperateId;

    public ObjectId getPostId() {
        return postId;
    }

    public void setPostId(ObjectId postId) {
        this.postId = postId;
    }

    public List<SkillInfo> getPrdSkillIds() {
        return prdSkillIds;
    }

    public void setPrdSkillIds(List<SkillInfo> prdSkillIds) {
        this.prdSkillIds = prdSkillIds;
    }

    public List<SkillInfo> getSubPrdSkillIds() {
        return subPrdSkillIds;
    }

    public void setSubPrdSkillIds(List<SkillInfo> subPrdSkillIds) {
        this.subPrdSkillIds = subPrdSkillIds;
    }

    public List<SkillInfo> getDevLangSkillIds() {
        return devLangSkillIds;
    }

    public void setDevLangSkillIds(List<SkillInfo> devLangSkillIds) {
        this.devLangSkillIds = devLangSkillIds;
    }

    public List<SkillInfo> getMiddleWareSkillIds() {
        return middleWareSkillIds;
    }

    public void setMiddleWareSkillIds(List<SkillInfo> middleWareSkillIds) {
        this.middleWareSkillIds = middleWareSkillIds;
    }

    public List<SkillInfo> getDbSkillIds() {
        return dbSkillIds;
    }

    public void setDbSkillIds(List<SkillInfo> dbSkillIds) {
        this.dbSkillIds = dbSkillIds;
    }

    public String getJobCode() {
        return jobCode;
    }

    public void setJobCode(String jobCode) {
        this.jobCode = jobCode;
    }

    public ObjectId getPersonLevelId() {
        return personLevelId;
    }

    public void setPersonLevelId(ObjectId personLevelId) {
        this.personLevelId = personLevelId;
    }

    public List<SkillInfo> getTestSkills() {
        return testSkills;
    }

    public void setTestSkills(List<SkillInfo> testSkills) {
        this.testSkills = testSkills;
    }

    public List<SkillInfo> getPlanSkillIds() {
		return planSkillIds;
	}

	public void setPlanSkillIds(List<SkillInfo> planSkillIds) {
		this.planSkillIds = planSkillIds;
	}

	public List<SkillInfo> getFrameSkillIds() {
		return frameSkillIds;
	}

	public void setFrameSkillIds(List<SkillInfo> frameSkillIds) {
		this.frameSkillIds = frameSkillIds;
	}

	public List<SkillInfo> getReqSkillIds() {
		return reqSkillIds;
	}

	public void setReqSkillIds(List<SkillInfo> reqSkillIds) {
		this.reqSkillIds = reqSkillIds;
	}

	public List<SkillInfo> getAnalysisSkillIds() {
		return analysisSkillIds;
	}

	public void setAnalysisSkillIds(List<SkillInfo> analysisSkillIds) {
		this.analysisSkillIds = analysisSkillIds;
	}

	public List<SkillInfo> getIbmsSkillIds() {
		return ibmsSkillIds;
	}

	public void setIbmsSkillIds(List<SkillInfo> ibmsSkillIds) {
		this.ibmsSkillIds = ibmsSkillIds;
	}

	public List<SkillInfo> getOfficeSkillIds() {
		return officeSkillIds;
	}

	public void setOfficeSkillIds(List<SkillInfo> officeSkillIds) {
		this.officeSkillIds = officeSkillIds;
	}

	public List<SkillInfo> getHrSkillIds() {
		return hrSkillIds;
	}

	public void setHrSkillIds(List<SkillInfo> hrSkillIds) {
		this.hrSkillIds = hrSkillIds;
	}

	public ObjectId getCommunicateId() {
        return communicateId;
    }

    public void setCommunicateId(ObjectId communicateId) {
        this.communicateId = communicateId;
    }

    public ObjectId getCooperateId() {
        return cooperateId;
    }

    public void setCooperateId(ObjectId cooperateId) {
        this.cooperateId = cooperateId;
    }
}
