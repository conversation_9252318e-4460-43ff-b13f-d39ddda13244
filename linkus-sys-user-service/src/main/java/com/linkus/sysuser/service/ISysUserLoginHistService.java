package com.linkus.sysuser.service;

import java.util.Date;

import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.sysuser.model.TeSysUserLoginHist;

public interface ISysUserLoginHistService {

	/**
	 * 查询用户最近登录失败记录
	 * 
	 * @param loginName
	 * @return
	 */
	TeSysUserLoginHist queryLastLoginFailHist(String loginName);

	/**
	 * 查询用户最近登录成功记录
	 * 
	 * @param loginName
	 * @return
	 */
	TeSysUserLoginHist queryLastLoginSuccessHist(String loginName);

	/**
	 * 添加登录成功历史
	 * 
	 * @param loginName
	 * @param ipAddr
	 */
	void saveLoginSuccessHist(String loginName, String ipAddr);

	/**
	 * 添加登录失败历史
	 * 
	 * @param loginName
	 * @param failTimes
	 * @param failItem
	 * @param firstFailTime
	 * @param ipAddr
	 */
	void saveLoginFailHist(String loginName, Integer failTimes, TeIdNameCn failItem, Date firstFailTime, String ipAddr);

	/**
	 * 添加解锁记录
	 * 
	 * @param loginName
	 */
	void saveUnlockHist(String loginName, TeIdNameCn failItem);

	/**
	 * 添加受限页面访问ip历史
	 * @param ipAddr
	 */
	void saveVisitIpHist(String ipAddr);
}
