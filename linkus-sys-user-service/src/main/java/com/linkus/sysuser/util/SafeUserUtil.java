package com.linkus.sysuser.util;

import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.vo.SafeUserVo;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
public class SafeUserUtil {

    public static List<SafeUserVo> convertSafeUserInfo(List<TeSysUser> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }
        List<SafeUserVo> safeUserList = new ArrayList<>();
        for (TeSysUser user : userList) {
            SafeUserVo safeUser = convertSafeUserInfo(user);
            if (safeUser == null) {
                continue;
            }
            safeUserList.add(new SafeUserVo(user.getId(), user.getUserName(), user.getLoginName()));
        }
        return safeUserList;
    }

    public static SafeUserVo convertSafeUserInfo(TeSysUser user) {
        if (user == null) {
            return null;
        }
        return new SafeUserVo(user.getId(), user.getUserName(), user.getLoginName());
    }
}
