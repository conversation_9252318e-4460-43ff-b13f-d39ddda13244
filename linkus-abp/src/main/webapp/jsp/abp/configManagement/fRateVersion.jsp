<%@ page language="java"  pageEncoding="UTF-8"%>
<%
	String path = request.getContextPath();
	String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
	String ver = "201712251008";
	String selectedYear = (String)session.getAttribute("selectedYear");
%>
<!doctype html>
<html>
<head>
	<meta charset="utf-8">
	<title>费率版本</title>
	<base href="<%=basePath%>">

	<!-- 兼容ie -->
	<script src="../../00scripts/00lib/ie/browser.js"></script>
	<!-- 本地样式 -->
	<link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
	<link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
	<!-- jQuery -->
	<script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
	<script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

	<script src="../../00scripts/00lib/vue/vue.min.js"></script>

	<script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
	<link href="../../00scripts/00lib/iview/4.4.0/styles/iview.css" rel="stylesheet" type="text/css"/>

	<!-- 本地不知道啥 -->
	<link rel="stylesheet" href="../../01css-portal/icomoon/style.css" rel="stylesheet" type="text/css"/>

	<link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
	<link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>

	<link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
	<script src="../../01css/font/iconfont.js"></script>

	<!-- 本地路由 -->
	<script src="../../00scripts/location/location.js" type="text/javascript"></script>
	<script src="../../00scripts/location/verifyLogin.js"></script>
	<script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>


</head>
<body style="width:100%;height:100%;">

<div id="main" class="main" style="height: 100%; overflow: hidden;" v-cloak>
<div class="layout-content layout-container report_warp_height"
	 style="min-width: 1190px; width: 100%; height: 100%; position: relative">
<%--	<div class="blank-name bg-white" style="display: flex; align-items: baseline;">--%>
<%--		<h1 style="display: inline-block">费率版本</h1>--%>

<%--	</div>--%>

	<div class="dmp_report_warp filter_one no_title">
		<div class="filter_warp filter_col_3">
			<div class="view_button oprts" style="display: inline-block;">
				<i-button type="primary" @click="addModal=true" :disabled="empTag == false">新增费率版本</i-button>
<%--				<i-button type="primary" @click="selectValidModal=true" :disabled="empTag == false">选择生效版本</i-button>--%>
			</div>
		</div>

		<div class="data_area abp_new_table" ref="dataArea">
			<i-table
					:data="bpFRateVerList"
					:columns="columns"
					:height="tableHeight"
					:class="'table-noborder lineClamp2' + className.substr(1)"
					:disable-dblclick="true"
					size="big">
			</i-table>
		</div>
		<Spin fix v-if="spinShow">
			<Icon type="ios-loading" size=18 class="demo-spin-icon-load"></Icon>
			<div>正在添加费率版本 请稍后！</div>
		</Spin>

	</div>

	<!-- 导出 -->
	<iframe id="excelData" style="display:none;"></iframe>
</div>


	<Modal v-model="addModal" title="新增版本" width="500" @on-ok="addBpFRateVer" >

<%--		<i-Form :label-width="80">--%>
<%--			<Form-Item label="年份" >--%>
<%--				<date-picker :value="year" type="year" :editable="false" @on-change="onYearChange"--%>
<%--							 placeholder="请选择" format="yyyy" :clearable="false" style="width:90%" v-model="checkYear"></date-picker>--%>
<%--			</Form-Item>--%>
<%--		</i-Form>--%>

		<i-Input v-model="changeDesc" type="textarea" :rows="10" placeholder="请输入..."></i-Input>
	</Modal>
<%--	<Modal v-model="selectValidModal" title="选择生效版本" width="500" id="dataGrid">--%>
<%--		<i-Select v-model="selBpFRateVerId" filterable placeholder="请选择" >--%>
<%--			<i-Option v-for="ver in bpFRateVerList" :value="ver.verNo" :key="ver.verNo">{{ ver.verNo }}</i-Option>--%>
<%--		</i-Select>--%>
<%--		<div slot="footer">--%>
<%--			<i-Button type="primary" @click="updateVerionStatus">确定</i-Button>--%>
<%--			<i-Button type="primary" @click="selectValidModal = false">关闭</i-Button>--%>
<%--		</div>--%>
<%--	</Modal>--%>

	<Modal v-model="confirmModal" title="费率版本锁定" width="500" @on-ok="update" >
		<p>锁定后该版本数据将不可编辑和导入，且不可解锁，请确认！</p>
	</Modal>

</div>

</body>
</html>


<script>
	var nowDate = new Date();
	var nowYear = nowDate.getFullYear();
	var nextYear = nowYear + 1;
	var fvView =
			new Vue({
				el			:	'#main',
				data		: 	{
					queryDataTotal: 0,
					pageNum: 0,
					pageSize: 10,
					pageSizeOpts: [10, 15, 20],
					columns:   [],
					tableHeight: '',
					className: '.tableStyle',
					addBpFRateVerSave:false,
					loginUser 	    :   {},
					selectValidModal :	false,
					selectedMonth    :  false,
					confirmModal     :  false,
					empTag	    	:   false,
					verList         :   [],
					year	        :   '',
					selBpFRateVerId	:	'',
					sbuId     :  '',
					sbuName      :  '',
					id      :  '',
					name      :  '',
					loginName :  '',
					jobCode :  '',
					bpFRateVerList	:	[],
					checkYear       :   new Date(), // 展示时的默认值
					addModal		:	false,
					lockModal		:	false,
					unlockModal		:	false,
					changeDesc		:	'',
					changeYear      :	'',
					pageBuCode		:   null,
					updateData      :   [], // 模态框带不了数据
					isPMS           :	'',
					spinShow        :   false,
				},
				mounted		:	function(){
					var _this = this;
					_this.tableHeight = _this.$refs.dataArea.offsetHeight - 32;
					this.$nextTick(function(){
						_this.getCurrentYear()
						_this.generateColumns();
						_this.queryEmpRolePower();
						_this.loadCreateUser();
					});
				},

				created: function() {
					var sf = this;
					sf.isPMS = sf.getQueryString('isPMS');
				},
				methods		:	{
					getCurrentYear:function (){
						var sf = this;
						$.ajax({
							type:'get',
							url:linkus.location.abp + '/index/queryServerTime',
							success:function(res) {
								if(res.success){
									sf.year = res.data.currentYear;
									sf.queryBpFRateVerListByBuCode();
								}else{
									sf.$Message.warning(res.message);
								}
							},
							error:function(res){

							}
						});
					},

					//加载当前登录用户
					loadCreateUser:function(){
						var _this = this;
						$.ajax({
							url : linkus.location.prjuser+'/sysUserCtrl/queryByLoginName.action',
							type : 'post',
							// async: false,
							dataType : 'JSON',
							success : function(teSysUser) {
								_this.loginUser = teSysUser;
								_this.sbuId = teSysUser.sbuId;
								_this.sbuName = teSysUser.sbuName;
								_this.id = teSysUser.id;
								_this.name = teSysUser.userName;
								_this.loginName = teSysUser.loginName;
								_this.jobCode = teSysUser.jobCode;

							},
							error : function(data) {
								console.log("error:loadCreateUser");
							}
						});
					},

					'onYearChange'	:	function() {
						var _this = this;
					},

					'queryBpFRateVerListByBuCode'	:	function(){
						var _this = this;
						var sbuId = _this.sbuId;
						var sbuName = _this.sbuName;
						var year = _this.year;

						$.ajax({
							type	:	"post",
							url		: 	linkus.location.abp +'/configManage/queryFeeRateVerForAll.action',
							data	:	{
								'year'		:   year

							},
							success	: 	function(text) {
								_this.bpFRateVerList = text;
								if (_this.bpFRateVerList.length >= 1) {
									_this.selBpFRateVerId = _this.bpFRateVerList[0].verNo
								}
							},
							error	: 	function(text){
								_this.$Message.warning('加载费率版本出错');
							}
						});
					},

					lockVerion:	function(e) {
						var sf = this;
						sf.updateData = e;
						sf.confirmModal = true;
					},
					update	:	function() {
						var sf = this;
						sf.confirmModal = false;
						sf.updateVerionStatus(sf.updateData);
					},

					updateVerionStatus	:	function(e) {
						var _this = this;
						var year = _this.year;
						// 不传verNo 改为feeRateVerId
						var feeRateVerId 	= e.feeRateVerId;
						$.ajax({
							type	:	"post",
							url		: 	linkus.location.abp +'/configManage/updateFRateVer.action',
							// async	:	false,
							data	:	{
								// 'sbuId'		: 	sbuId,
								// 'sbuName'	: 	sbuName,
								'year'		:   year,
								'feeRateVerId'		:   feeRateVerId
							},
							success	: 	function(text) {
								_this.selectValidModal = false;
								_this.confirmModal = false;
								if (text.checkValid) {
									_this.$Message.success('版本锁定更新成功!');
									_this.queryBpFRateVerListByBuCode();
								} else {
									_this.$Message.warning(text.checkSuccessInfo);
								}

							},
							error	: 	function(text) {
								_this.selectValidModal = false;
								_this.confirmModal = false;
								_this.$Message.warning('版本锁定更新出错');
							}
						});
					},

					// 页面权限
					queryEmpRolePower	:	function() {
						var _this = this;
						$.ajax({
							type	:	"get",
							url		: 	linkus.location.abp + '/role/bu/admin',
							headers: {
								'Content-Type': 'application/json;charset=utf-8'
							},
							success	: 	function(text) {
								var data = text.data;
								if (data) {
									_this.empTag = true;
								}
							},
							error	: 	function(text) {
								_this.$Message.warning('查询权限出错,请联系管理员!');
							}
						});
					},
					
					'addBpFRateVer'	:	function() {
						var _this = this;
						var teUser = _this.loginUser;
						// teUser.year = new Date(_this.checkYear).getFullYear();
						teUser.changeDesc = _this.changeDesc;
						if(_this.addBpFRateVerSave){
							return;
						}
						_this.addBpFRateVerSave = true;
						$.ajax({
							type	:	"post",
							url		: 	linkus.location.abp + '/configManage/addFeeRateVer.action',
							dataType:   "json",
							contentType : "application/json;charset=utf-8", // 必须加这个不然报错
							data	:	JSON.stringify(teUser),
							beforeSend	:	function(){
								_this.addModal = false;
								_this.spinShow = true;
							},
							success	: 	function(result) {
								_this.spinShow = false;
								//如果更新失败，返回更新失败结果
								if (!!result.error) {
									_this.$Message.warning(result.errMsg);
									return;
								}
								_this.changeDesc = '';
								_this.$Message.success('新增成功!');
								_this.addBpFRateVerSave = false;
								_this.queryBpFRateVerListByBuCode();
							},
							error	: 	function(text){
								_this.spinShow = false;
								_this.addBpFRateVerSave = false;
								_this.$Message.warning('参数出错，请联系管理员');
							}
						});
					},															
					loadCurrentUser : function() {
						var sf = this;
						return $.ajax({
							url : prjUserHttpRequest + '/sysUserCtrl/queryByLoginName.action',
							type : 'post',
							dataType : 'JSON',
							async	   :  false,
						}).done(function(teSysUser) {
							sf.loginUser = teSysUser;
							sf.pageBuCode = data.sbuId;
						});
					},

					// 获取列
					generateColumns: function () {
						var sf = this;
						sf.columns = [
							{
								title: '序',
								key: 'index',
								width: 120
							},
							{
								title: '锁定操作',
								width: 200,
								key: 'locked',
								render:function (h,params) {
									if (params.row.locked) { // 锁定状态进行解锁
										return h('div', {
											// on: {
											// 	click: function() {
											// 		if (!sf.empTag) {
											// 			sf.$Message.warning('暂无权限操作!');
											// 			return;
											// 		}
											// 		// 解锁 判断是否绑定年度计划 若绑定则不给解锁
											// 		sf.updateVerionStatus(params.row);
											// 	}
											// }
										},[
											h('Icon', {
												props: {
													type: 'md-lock',
													size:'18',
													color: '#ed3f14'
												},
												style: {
													marginLeft: '6px'
												}
											}),
										]);
									} else {
										return h('div', {
											on: {
												click: function() {
													// 锁定费率版本
													if (!sf.empTag) {
														sf.$Message.warning('暂无权限操作!');
														return;
													}
													sf.lockVerion(params.row);
												}
											}
										},[
											h('Icon', {
												props: {
													type: 'md-unlock',
													size:'18',
													// color: '#ed3f14'
												},
												style: {
													marginLeft: '6px'
												}
											}),
										]);
									}
								},

							},
							{
								title: 'BU',
								key: 'buName',
								width: 200,
							},
							{
								title: '版本号',
								key: 'verNo',
								width: 200,
								render:function (h,params) {
									if (!!params.row.verNo) {
										return h('a', {
											on: {
												click: function () {
													sf.onSelectedRenderer(params.row);
												}
											}
										}, params.row.verNo);
									}
								}
							},

							{
								title: '更新说明',
								key: 'changeDesc',
							},
							{
								title: '更新人员',
								key: 'changePerson',
								width: 200,
								// 返回为对象
								render:function (h,params) {
									if (!!params.row.changePerson && !!params.row.changePerson.userName && params.row.changePerson.loginName) {
										return h('span', params.row.changePerson.userName + "/" + params.row.changePerson.loginName);
									}
								}
							},
							{
								title: '更新时间',
								key: 'changeTime',
								width: 200,
							},

						]
					},
					// 分页
					onPageNumChange: function(pageNum) {
						this.pageNum = pageNum - 1;
						this.queryTableList();
					},
					onPageSizeChange: function(pageSize) {
						this.pageNum = 0;
						this.pageSize = pageSize;
						this.queryTableList();
					},

					// 跳转连接
					onSelectedRenderer	:	function(e) {
						var sf = this;
						var feeRateVerId = e.feeRateVerId;
						var url = '';
						if (sf.isPMS == 'true') {
							url = linkus.location.rsc + '/02html/prj/abpFRateConfig.html' + '?verParamId=' + feeRateVerId
						} else {
							url = linkus.location.abp + '/jsp/abp/configManagement/fRateConfig.jsp' + '?verParamId=' + feeRateVerId
						}


						var wind = window.open(url, "_blank");
					},

					getQueryString:function(name) {
						var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
						var r = window.location.search.substr(1).match(reg);
						if (r != null) return unescape(r[2]);
						return null;
					},


				}
			});	
</script>