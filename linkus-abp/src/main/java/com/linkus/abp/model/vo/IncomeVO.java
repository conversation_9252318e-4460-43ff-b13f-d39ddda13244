package com.linkus.abp.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.bson.types.ObjectId;

/**
 * 收入视图对象
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel(value = "IncomeVO", description = "经营计划收入视图对象")
public class IncomeVO {

    /**
     * 经营计划预算id
     */
    @ApiModelProperty(value = "经营计划预算id")
    ObjectId abpOopBgtId;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    String ym;

    /**
     * 收入（单位k）
     */
    @ApiModelProperty(value = "收入（单位k）")
    String amt;

    /**
     * 确认收入节奏
     */
    @ApiModelProperty(value = "确认收入节奏，保留两位小数")
    String poc;

    @ApiModelProperty(value = "相关项目收入")
    String relatedPrjIncome;

    /**
     * 实际化
     */
    @ApiModelProperty(value = "是否实际化（true：禁止编辑）")
    Boolean isActual;

    @ApiModelProperty(value = "年限")
    String year;
}
