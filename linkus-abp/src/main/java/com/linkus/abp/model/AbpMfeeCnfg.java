package com.linkus.abp.model;

import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Document(collection = "abpMfeeCnfg")
public class AbpMfeeCnfg implements Serializable {
    private static final long serialVersionUID = 2482335696788240780L;

    private ObjectId id;
    private Boolean isValid;
    private String isActualCnfg;
    private String buCode;
    private TeIdNameCn mfeeType;
    private String year;
    private String ym;
    private TeIdNameCn cnfgItem;
    private List<ObjectId> pls;
    private Double value;
    private TeUser addUser;
    private Date addTime;

}
