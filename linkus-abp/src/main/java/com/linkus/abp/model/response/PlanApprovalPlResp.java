package com.linkus.abp.model.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PlanApprovalPlResp implements Serializable {
    private static final long serialVersionUID = -6419623800652097418L;

    private String areaName;
    private String bigAreaName;
    private String capitaCost;
    private String capitaNewOrderAmt;
    private String capitaProfit;
    private int cnt;
    private String convetHc;
    private String dailyCapitaDirectCost;
    private String directCost;
    private String directCostIncomeRatio;
    private String endYearBacklog;
    private String grossProfitRatio;
    private String income;
    private String incomeRatio;
    private String mfeeCost;
    private String netProfit;
    private String netProfitRatio;
    private String newOrderAmt;
    private String plName;
    private String proGrossProfit;
    private String provName;
    private int rn;
    private String totalCost;
    private String typeName;
}
