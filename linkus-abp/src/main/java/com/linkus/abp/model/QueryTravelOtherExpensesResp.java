package com.linkus.abp.model;

import lombok.Data;
import org.bson.types.ObjectId;

import java.io.Serializable;

/**
 * 查询差旅-其他费的返回类
 *
 * <AUTHOR>
 * @since 2022-11-30 19:57
 **/
@Data
public class QueryTravelOtherExpensesResp implements Serializable {
    private static final long serialVersionUID = 1557546882908331751L;

    private String bigRegionName;
    private String regionName;
    private String provName;
    private Double travelAllowance;
    private ObjectId travelAllowanceId;
    private Double roundTripFee;
    private ObjectId roundTripFeeId;
    private Double localTransportFee;
    private ObjectId localTransportFeeId;
    private Double hotelAccomFee;
    private ObjectId hotelAccomFeeId;
}
