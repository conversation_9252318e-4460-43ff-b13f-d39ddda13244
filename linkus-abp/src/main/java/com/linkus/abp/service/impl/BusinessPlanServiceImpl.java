package com.linkus.abp.service.impl;

import com.alibaba.fastjson.JSON;
import com.linkus.abp.dao.*;
import com.linkus.abp.manager.ServiceManager;
import com.linkus.abp.model.*;
import com.linkus.abp.model.request.AnnualPlanQueryReq;
import com.linkus.abp.model.request.PlanApprovalReq;
import com.linkus.abp.model.request.PlanFormulationReq;
import com.linkus.abp.model.request.PlanFormulationUpdateReq;
import com.linkus.abp.model.request.PrimSecRelReq;
import com.linkus.abp.model.request.QueryIncomeForecMsgReq;
import com.linkus.abp.model.request.QueryPrimOrSecReq;
import com.linkus.abp.model.response.PlanApprovalPlResp;
import com.linkus.abp.model.response.PlanApprovalProvResp;
import com.linkus.abp.model.response.PlanApprovalRepulseReq;
import com.linkus.abp.model.response.ProgressOverviewResp;
import com.linkus.abp.model.response.QueryIncomeForecMsgResp;
import com.linkus.abp.model.response.QueryIncomeForecProvResp;
import com.linkus.abp.model.response.QueryPlanResp;
import com.linkus.abp.model.response.QueryPlanTransferOutProvResp;
import com.linkus.abp.model.response.QueryPmProvRelResp;
import com.linkus.abp.model.response.*;
import com.linkus.abp.model.vo.PmsAbpOopVO;
import com.linkus.abp.service.BusinessPlanService;
import com.linkus.abp.service.FeeCalculationService;
import com.linkus.abp.service.OopService;
import com.linkus.abp.service.PlanningBgtService;
import com.linkus.abp.util.Constant;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.BatchCondsUpsert;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.*;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.pager.Pager;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.base.table.DbTableName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.*;
import com.linkus.itf.api.client.AiPpbFeignClient;
import com.linkus.itf.api.client.ItfPrjBgtStatFeignClient;
import com.linkus.itf.api.model.ItfAiPpb;
import com.linkus.itf.api.model.ItfPrjBgtStat;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.dao.SysDefCnfgDao;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.*;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysDefRoleUser;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.mongodb.bulk.BulkWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BusinessPlanServiceImpl implements BusinessPlanService {

    @Autowired
    private SysDefDao sysDefDao;
    @Autowired
    private ISysDefDao iSysDefDao;
    @Autowired
    private SysVerMgtDao sysVerMgtDao;
    @Autowired
    private AbpOopDao abpOopDao;
    @Autowired
    private AbpOprtDao abpOprtDao;
    @Autowired
    private AbpOopBgtDao abpOopBgtDao;
    @Autowired
    private AbpOrderDao abpOrderDao;
    @Autowired
    private ServiceManager serviceManager;
    @Autowired
    private SysDefRoleUserDao sysDefRoleUserDao;
    @Autowired
    private AbpMfeeCnfgDao abpMfeeCnfgDao;
    @Autowired
    private ISysCalDao sysCalDao;
    @Autowired
    private ISysUserDao sysUserDao;

    @Autowired
    private RedissonClient redissionClient;

    @Autowired
    private IPrjInfoDao prjInfoDao;
    @Autowired
    private ISysDefService sysDefService;

    @Autowired
    private SysDefCnfgDao sysDefCnfgDao;
    @Autowired
    private FeeCalculationService feeCalculationService;

    @Autowired
    private PlanningBgtService planningBgtService;

    @Autowired
    private OopService oopService;
    @Autowired
    private ISysDefRoleUserService sysDefRoleUserService;

    @Autowired
    protected MongoTemplate mongoTemplate;
    @Autowired
    private AiPpbFeignClient aiPpbFeignClient;
    @Autowired
    private ItfPrjBgtStatFeignClient prjBgtStatFeignClient;

    private static final List<String> DNC_PL_LIST = Arrays.asList("BD", "OSS", "D-OTHERS");
    private static final List<String> CMC_PL_LIST = Arrays.asList("CRM", "BILLING", "ISAC", "IISC", "BOMC", "IPR", "ATS", "SP", "BSC-OTHERS");
    private static final List<String> DNC_BIG_REGION = Arrays.asList("华北区", "华中区", "华东区", "华西区", "北区", "南区");
    private static final List<String> CMC_BIG_REGION = Arrays.asList("SVP1", "SVP2", "SVP3", "SVP4");
    //    private static final List<String> STATUS_LIST = Arrays.asList("计划编制", "省份提交+分摊", "收入预测");
    private static final List<String> STATUS_LIST = Arrays.asList("计划编制");
    private static final List<String> YM_LIST = Arrays.asList("1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月");

    public static final String PMS_PRJ_LEVEL3_KEY_PRJ_INDICATOR_CNFG = "pmsPrjLevel3KeyPrjIndicatorCnfg";
    public static final ObjectId GM_ID = new ObjectId("637f27d464586deda4d72a69");
    public static final ObjectId INCOME_PER_HUNDRED_ID = new ObjectId("637f27f764586deda4d72cb7");
    public static final ObjectId DIRECT_FEE_PER_HUNDRED_ID = new ObjectId("637f280d64586deda4d72db9");
    private static final String PM_IS_CHANGED = "已手工修改PM";
    private static final String PM_NOT_CHANGED = "未手工修改PM";


    private final static Map<String, Integer> COLUMN_INDEX_MAP_BSC = new HashMap<String, Integer>() {{
        put("periodPocBegin", 17);
        put("periodPocEnd", 23);
        put("periodIncomeBegin", 25);
        put("periodIncomeEnd", 36);
        put("totalPocBegin", 37);
        put("totalPocEnd", 48);
    }};
    private final static Map<String, Integer> COLUMN_INDEX_MAP_DNC = new HashMap<String, Integer>() {{
        put("periodPocBegin", 17);
        put("periodPocEnd", 23);
        put("periodIncomeBegin", 25);
        put("periodIncomeEnd", 36);
        put("totalPocBegin", 37);
        put("totalPocEnd", 48);
    }};
    private final static Map<String, Integer> COLUMN_INDEX_MAP_CTC = new HashMap<String, Integer>() {{
        put("periodPocBegin", 19);
        put("periodPocEnd", 31);
        put("periodIncomeBegin", 34);
        put("periodIncomeEnd", 45);
        put("totalPocBegin", 46);
        put("totalPocEnd", 57);
    }};

    @Override
    public List<PlanVersion> findVersionByBuCode(String buCode) {
        return serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
    }

    @Override
    public List<PlanVersion> findVersionByBuCode(String buCode, String year) {
        return serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID, year);
    }

    @Override
    public PlanVersion findLastVersionByBuCode(String buCode) {
        List<PlanVersion> planVersions = findVersionByBuCode(buCode);
        if (planVersions.isEmpty()) {
            return null;
        }
        return planVersions.get(0);
    }

    @Override
    public PlanVersion findLastVersionByBuCode(String buCode, String year) {
        List<PlanVersion> planVersions = findVersionByBuCode(buCode, year);
        if (planVersions.isEmpty()) {
            return null;
        }
        return planVersions.get(0);
    }

    @Override
    public List<PlanVersion> findAllVersionByBuCode(String buCode) {
        return serviceManager.findAllPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
    }

    @Override
    public void releaseNextVersion(ObjectId lastVersionId, TeSysUser user, String verDesc, ObjectId ratePlanVerId, String year) {
        //校验角色权限
        serviceManager.queryBuAdminByUser(user);
        //发布下一版本的时候，不能同步数据，不然数据会出现问题，所以发布下一版本的锁和同步数据的锁得一致
        RLock lock = redissionClient.getLock("startSync");
        boolean flag;
        try {
            flag = lock.tryLock();
            if (flag){
                TeUser addUser = new TeUser();
                addUser.setUserName(user.getUserName());
                addUser.setUserId(user.getId());
                addUser.setJobCode(user.getJobCode());
                addUser.setLoginName(user.getLoginName());
                SysVerMgt sysVerMgt;
                if (lastVersionId == null) {
                    // 1.发起年度版本
                    //查询当年的版本是否存在
//                    Date date = new Date();
//                    int year = DateUtil.getYear(date);
                    List<SysVerMgt> sysVerMgts=sysVerMgtDao.queryDataByYearAndVerMgtType(String.valueOf(year),user.getSbuId(),Constant.ABP_BUSINESS_PLAN_VERSION_ID);
                    if (!CollectionUtils.isEmpty(sysVerMgts)){
                        throw new BaseException("当年版本已存在，请不要重复发起年度版本");
                    }
                    sysVerMgt = releaseAnnualVersion(user, verDesc, addUser, ratePlanVerId, year);
                } else {
                    // 2.发起下一版本
                    sysVerMgt = releaseNextVersion(lastVersionId, verDesc, addUser, ratePlanVerId, year);
                }
                // 计划编制中：每次发起一个新的版本时，系统自动将所有省份的状态都置为“计划编制中”
                updateProgressStatus(user, sysVerMgt);
                if (lastVersionId == null) {
                    return;
                }
                // 3.发起下一版本时，需要将abpOop和abpOopBgt数据复制，并改成当前版本再次插入
                copyPreviousVersionData(lastVersionId, sysVerMgt);
                // 4.本版本数据的主辅信息subOops中的cid需得更新
                updateSubOops(sysVerMgt);
                // 5.计算人工费、直接费用、事业部分摊、区域分摊、销售费用
                planningBgtService.syncBudgetInfo(sysVerMgt.getId());
//                feeCalculationService.calcBuHcFee(sysVerMgt.getId(), ratePlanVerId);
//                feeCalculationService.calcAreaHcFee(sysVerMgt.getId(), ratePlanVerId);
//                feeCalculationService.calcSalesFee(sysVerMgt.getId(), ratePlanVerId);
                feeCalculationService.calculateSharedCosts(sysVerMgt.getId(),null);
            }else {
                throw BusinessException.initExc("正在发起下一版本,请稍后!");
            }
        } finally {
            if (lock!=null&&lock.isLocked()&&lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    private void updateSubOops(SysVerMgt sysVerMgt) {
        int curPage=0;
        int pageSize=100;
        while (true){
            //查询有辅项目的项目信息
            List<IDbCondition> conds=new ArrayList<>();
            conds.add(new DC_E(DbFieldName.common_isValid,true));
            conds.add(new DC_E(DbFieldName.AbpOop.subOops,null,true));
            conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid),sysVerMgt.getId()));
            List<DbFieldName> fieldNames=new ArrayList<>();
            fieldNames.add(DbFieldName.common__id);
            fieldNames.add(DbFieldName.AbpOop.subOops);
            List<AbpOop> abpOops = abpOopDao.findByFieldAndConds(conds,fieldNames,null,new Pager(curPage,pageSize));
            if (CollectionUtils.isEmpty(abpOops)){
                break;
            }
            curPage++;
            List<String> codeNames = new ArrayList<>();
            for (AbpOop abpOop : abpOops) {
                List<SubOop> subOops = abpOop.getSubOops();
                if (CollectionUtils.isEmpty(subOops)){
                    continue;
                }
                for (SubOop subOop : subOops) {
                    TeIdNameCn oop = subOop.getOop();
                    if (oop!=null&&oop.getCodeName()!=null){
                        codeNames.add(oop.getCodeName());
                    }
                }
            }
            //根据codeName查询本版下的辅项目信息
            conds.clear();
            conds.add(new DC_E(DbFieldName.common_isValid,true));
            conds.add(new DC_E(DbFieldName.AbpOop.isDeleted,false));
            conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid),sysVerMgt.getId()));
            conds.add(new DC_I<>(DbFieldName.common_cn,codeNames));
            fieldNames.clear();
            fieldNames.add(DbFieldName.common__id);
            fieldNames.add(DbFieldName.common_cn);
            List<AbpOop> subAbpOops = abpOopDao.findByFieldAndConds(conds,fieldNames);
            Map<String, AbpOop> subMap =new HashMap<>();
            if (!CollectionUtils.isEmpty(subAbpOops)){
                subMap = subAbpOops.stream().collect(Collectors.toMap(AbpOop::getCodeName, s -> s));
            }
            List<List<IDbCondition>> queryConditions=new ArrayList<>();
            List<List<UpdataData>> updateConditions=new ArrayList<>();
            //遍历主项目
            for (AbpOop abpOop : abpOops) {
                List<SubOop> subOops = abpOop.getSubOops();
                if (CollectionUtils.isEmpty(subOops)){
                    continue;
                }
                List<IDbCondition> query = new ArrayList<>();
                query.add(new DC_E(DbFieldName.common__id,abpOop.getId()));
                queryConditions.add(query);
                List<SubOop> newSubOops = new ArrayList<>();
                for (SubOop subOop : subOops) {
                    TeIdNameCn subOopOop = subOop.getOop();
                    if (subOopOop!=null){
                        AbpOop oop = subMap.get(subOopOop.getCodeName());
                        if (oop==null){
                            continue;
                        }
                        subOopOop.setCid(oop.getId());
                        newSubOops.add(subOop);
                    }
                }
                List<UpdataData> update = new ArrayList<>();
                update.add(new UpdataData(DbFieldName.AbpOop.subOops,newSubOops));
                updateConditions.add(update);
            }
            if (!CollectionUtils.isEmpty(queryConditions)){
                abpOopDao.batchUpdate(queryConditions,updateConditions,false);
            }
        }

    }

    private void updateProgressStatus(TeSysUser user, SysVerMgt mgt) {
        //1.查找bu下所有的省份
        List<IDbCondition> conditons = new ArrayList<>();
        conditons.add(new DC_E(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), Constant.ABP_PROV_CODE_NAME));
        conditons.add(new DC_E(DbFieldName.common_isValid, true));
        conditons.add(new DC_E(DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefCodeName), user.getSbuId()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.sysDef__defName);
        fieldNames.add(DbFieldName.common_cn);
        List<TeSysDef> sysDefs = sysDefDao.findByFieldAndConds(conditons, fieldNames);
        ArrayList<AbpOprt> abpOprts = new ArrayList<>();
        //2.把所有的省份的状态更新为“计划编制中”
        for (TeSysDef sysDef : sysDefs) {
            AbpOprt abpOprt = new AbpOprt();
            PlanVersion planVersion = new PlanVersion();
            planVersion.setCid(mgt.getId());
            planVersion.setName(mgt.getVerName());
            planVersion.setVerNo(String.valueOf(mgt.getVerNo()));
            abpOprt.setPlanVer(planVersion);
            TeIdNameCn dept = new TeIdNameCn();
            dept.setCid(sysDef.getId());
            dept.setName(sysDef.getDefName());
            dept.setCodeName(sysDef.getCodeName());
            abpOprt.setDept(dept);
            TeIdNameCn oprt = new TeIdNameCn();
            oprt.setCid(Constant.ABP_OPRT_SUBMIT_ID);
            oprt.setName("初始化");
            //sysMget中没有对应的codeName
            oprt.setCodeName(null);
            abpOprt.setOprt(oprt);
            TeIdNameCn status = new TeIdNameCn();
            status.setCid(Constant.ABP_OPRT_PLANNING_IN_PROGRESS_ID);
            status.setName("计划编制中");
            status.setCodeName(null);
            abpOprt.setStatus(status);
            abpOprt.setDesc("发起版本");
            TeUser teUser = getTeUser(user);
            abpOprt.setOprtUser(teUser);
            abpOprt.setOprtTime(new Date());
            abpOprts.add(abpOprt);
        }
        abpOprtDao.batchSave(abpOprts);
    }

    private void copyPreviousVersionData(ObjectId lastVersionId, SysVerMgt sysVerMgt) {
        int index = 0;
        int pageSize = 1000;
        while (true) {
            Map<ObjectId, AbpOop> abpOopMap = new HashMap<>();
            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DbFieldName.common_isValid, true));
            conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), lastVersionId));
            Pager pager = new Pager();
            pager.setIndex(index);
            pager.setSize(pageSize);
            //把老版本数据abpOop，abpOopBgt复制一份
            List<AbpOop> list = abpOopDao.findByFieldAndConds(conds, null, null, pager);
            if (CollectionUtils.isEmpty(list)) {
                break;
            }
            PlanVersion planVersion = new PlanVersion();
            planVersion.setName(sysVerMgt.getVerName());
            planVersion.setCid(sysVerMgt.getId());
            planVersion.setVerNo(String.valueOf(sysVerMgt.getVerNo()));
            index ++;
            List<ObjectId> abpOopIds = new ArrayList<>();
            for (AbpOop abpOop : list) {
                ObjectId abpOopId = abpOop.getId();
                abpOopIds.add(abpOopId);
                abpOopMap.put(abpOopId, abpOop);
                //新版本id
                abpOop.setId(null);
                abpOop.setPlanVer(planVersion);
                abpOop.setAddTime(new Date());
            }
            abpOopDao.batchSave(list);
            //根据oop.cid查询AbpOopBgt信息
            List<IDbCondition> conditions = new ArrayList<>();
            conditions.add(new DC_E(DbFieldName.common_isValid, true));
            conditions.add(new DC_I<>(DbFieldName.AbpOopBgt.oop.dot(DbFieldName.common_cid), abpOopIds));
            List<AbpOopBgt> abpOopBgts = abpOopBgtDao.findByFieldAndConds(conditions, null);
            for (AbpOopBgt abpOopBgt : abpOopBgts) {
                ObjectId oopCid = abpOopBgt.getOop().getCid();
                AbpOop abpOop = abpOopMap.get(oopCid);
                TeIdNameCn oop = new TeIdNameCn();
                oop.setCid(abpOop.getId());
                oop.setName(abpOop.getName());
                oop.setCodeName(abpOop.getCodeName());
                abpOopBgt.setOop(oop);
                abpOopBgt.setPlanVer(planVersion);
                abpOopBgt.setId(null);
            }
            abpOopBgtDao.batchSave(abpOopBgts);
        }
    }

    private SysVerMgt releaseNextVersion(ObjectId lastVersionId, String verDesc, TeUser addUser, ObjectId ratePlanVerId, String year) {
        // 校验是否存在未锁定经营计划版本
        SysVerMgt sysVerMgt = sysVerMgtDao.findById(lastVersionId);
        Assert.notNull(sysVerMgt, "经营计划版本id对应的经营计划不存在");
        List<SysVerMgt> planVers = listPlanVers(sysVerMgt.getSrcDef().getCodeName(), null, false, null);
        Assert.isTrue(CollectionUtils.isEmpty(planVers), "发起下一版本失败，存在未锁定的经营计划版本");

        //2.查出老版本数据,在此基础上自增版本号
        int verNo = sysVerMgt.getVerNo() + 1;
//        Date date = new Date();
//        int year = DateUtil.getYear(date);
        List<SysVerMgt>sysVerMgts=sysVerMgtDao.queryNextVersion(verNo,Constant.ABP_BUSINESS_PLAN_VERSION_ID,Integer.valueOf(year),sysVerMgt.getSrcDef().getCid());
        if (!CollectionUtils.isEmpty(sysVerMgts)){
            throw new BaseException("版本已存在,请勿重复发布!");
        }
        sysVerMgt.setVerNo(verNo);
        sysVerMgt.setId(null);
        sysVerMgt.setAddUser(addUser);
        sysVerMgt.setAddTime(new Date());
        sysVerMgt.setVerDesc(verDesc);
        sysVerMgt.setIsLocked(false);
        PlanVersion linkedVer = sysVerMgt.getLinkedVer();
        if (!linkedVer.getCid().equals(ratePlanVerId)){
            //年度计划发起下一版本，逻辑基本同发起首版，默认为上一版本下的费率版本
            //查询费率版本
            SysVerMgt ratePlanVer = sysVerMgtDao.findById(ratePlanVerId);
            PlanVersion planVer=new PlanVersion();
            planVer.setCid(ratePlanVer.getId());
            planVer.setName(ratePlanVer.getVerName());
            planVer.setVerNo(String.valueOf(ratePlanVer.getVerNo()));
            sysVerMgt.setLinkedVer(planVer);
        }
        sysVerMgtDao.save(sysVerMgt);
        return sysVerMgt;
    }

    private SysVerMgt releaseAnnualVersion(TeSysUser user, String verDesc, TeUser addUser, ObjectId ratePlanVerId, String year) {
        SysVerMgt sysVerMgt = new SysVerMgt();
        String buCode = user.getSbuId();
        //查询bu信息
        List<TeSysDef> results = queryBuData(buCode);
        TeSysDef sysDef = results.get(0);
        //设置bu信息
        TeIdNameCn srcDef = new TeIdNameCn();
        srcDef.setCid(sysDef.getId());
        srcDef.setName(sysDef.getDefName());
        srcDef.setCodeName(sysDef.getCodeName());
        sysVerMgt.setSrcDef(srcDef);
        //设置版本类型
        TeIdNameCn verMgtType = new TeIdNameCn();
        verMgtType.setCid(Constant.ABP_BUSINESS_PLAN_VERSION_ID);
        verMgtType.setName(Constant.ABP_BUSINESS_PLAN_VERSION_NAME);
        verMgtType.setCodeName(Constant.ABP_BUSINESS_PLAN_VERSION_CODE_NAME);
        sysVerMgt.setVerMgtType(verMgtType);
        //首版,版本号为1
        sysVerMgt.setVerNo(1);
//        sysVerMgt.setYear(String.valueOf(DateUtil.getYear(new Date())));
        sysVerMgt.setYear(year);
        sysVerMgt.setVerName(sysDef.getDefName() + sysVerMgt.getYear());
        sysVerMgt.setAddUser(addUser);
        sysVerMgt.setAddTime(new Date());
        sysVerMgt.setVerDesc(verDesc);
        sysVerMgt.setIsLocked(false);
        //查询关联的费率版本
        SysVerMgt ratePlanVer = sysVerMgtDao.findById(ratePlanVerId);
        if (ratePlanVer==null){
            throw new BaseException("费率版本未查到,请联系管理员!");
        }
        //设置关联的费率版本
        PlanVersion linkedVer = new PlanVersion();
        linkedVer.setCid(ratePlanVerId);
        linkedVer.setName(ratePlanVer.getVerName());
        linkedVer.setVerNo(String.valueOf(ratePlanVer.getVerNo()));
        sysVerMgt.setLinkedVer(linkedVer);
        return sysVerMgtDao.save(sysVerMgt);
    }

    private List<TeSysDef> queryBuData(String buCode) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), SysDefTypeCodeName.AI_BU.getValue()));
        conds.add(new DC_E(DbFieldName.common_cn, buCode));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_cn);
        fieldNames.add(DbFieldName.sysDef__defName);
        return sysDefDao.findByFieldAndConds(conds, fieldNames);
    }

    @Override
    public PageListResult queryAnnualPlan(String buCode, AnnualPlanQueryReq req) {
        //查询abpOop表中符合当前计划年份、isValid=true、是当前登录人所属BU的所有版本信息展示在页面中
        Criteria criteria = Criteria.where(DFN.common_isValid.n()).is(true);
        criteria.and(DbFieldName.AbpOop.isDeleted.n()).is(false);

        List<ObjectId> planVerIds = req.getPlanVerIds();
        if (CollectionUtils.isEmpty(planVerIds)) {
            List<PlanVersion> planVersions = serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
            planVerIds = planVersions.stream().map(PlanVersion::getCid).collect(Collectors.toList());
        }
        criteria.and(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid).n()).in(planVerIds);

        String linkedOrderCode = req.getLinkedOrderCode();
        String codeName = req.getCodeName();

        if (StringUtil.isNotNull(codeName)&&StringUtil.isNotNull(linkedOrderCode)){
            criteria.and(DbFieldName.AbpOop.linkedOrderCode.n()).regex(linkedOrderCode, "i");
            criteria.and(DbFieldName.common_cn.n()).regex(codeName, "i");
        }else if (StringUtil.isNotNull(linkedOrderCode)&&StringUtil.isNull(codeName)){
            Criteria linkedOrderCodeCriteria = Criteria.where(DbFieldName.AbpOop.linkedOrderCode.n()).regex(linkedOrderCode, "i");
            linkedOrderCodeCriteria.and(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid).n()).nin(Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID));

            Criteria codeNameCriteria = Criteria.where(DbFieldName.common_cn.n()).regex(linkedOrderCode, "i");
            codeNameCriteria.and(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid).n()).in(Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID));

            criteria.orOperator(linkedOrderCodeCriteria, codeNameCriteria);
        }else if (StringUtil.isNotNull(codeName)){
            criteria.and(DbFieldName.common_cn.n()).regex(codeName, "i");
            criteria.and(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid).n()).nin(Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID));
        }
        String pmName = req.getPmName();
        String salesUserName = req.getSalesUserName();

        if (StringUtil.isNotNull(pmName)&&StringUtil.isNotNull(salesUserName)){
            Criteria pmCroteria = new Criteria();
            pmCroteria.orOperator(
                    Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName),
                    Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_jobCode).n()).regex(pmName)
            );

            Criteria salesCroteria = new Criteria();
            salesCroteria.orOperator(
                    Criteria.where(DbFieldName.AbpOop.sales.dot(DbFieldName.common_userName).n()).regex(salesUserName),
                    Criteria.where(DbFieldName.AbpOop.sales.dot(DbFieldName.common_jobCode).n()).regex(salesUserName)
            );
            criteria.andOperator(pmCroteria, salesCroteria);
        }else if (StringUtil.isNotNull(pmName)&&StringUtil.isNull(salesUserName)){
            criteria.orOperator(
                    Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName),
                    Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_jobCode).n()).regex(pmName)
            );
        }else if (StringUtil.isNull(pmName)&&StringUtil.isNotNull(salesUserName)){
            criteria.orOperator(
                    Criteria.where(DbFieldName.AbpOop.sales.dot(DbFieldName.common_userName).n()).regex(salesUserName),
                    Criteria.where(DbFieldName.AbpOop.sales.dot(DbFieldName.common_jobCode).n()).regex(salesUserName)
            );
        }

        String name = req.getName();
        if (StringUtil.isNotNull(name)) {
            Pattern compile = Pattern.compile(Pattern.quote(name), Pattern.CASE_INSENSITIVE | Pattern.UNICODE_CASE);
            criteria.and(DbFieldName.common_name.n()).regex(compile.toString(), "i");
        }
        String mainPrjCode = req.getMainPrjCode();
        if (StringUtil.isNotNull(mainPrjCode)) {
            criteria.and(DbFieldName.AbpOop.mainPrjCode.n()).regex(mainPrjCode, "i");
        }

        Criteria oprtInfoCriteria = Criteria.where("oprtInfo").ne("[]");
        if (req.getStartTime() != null && req.getEndTime() != null) {
            oprtInfoCriteria.and("oprtInfo.0.oprtTime").gte(req.getStartTime()).lte(req.getEndTime());
        } else if (req.getStartTime() != null) {
            oprtInfoCriteria.and("oprtInfo.0.oprtTime").gte(req.getStartTime());
        } else if (req.getEndTime() != null) {
            oprtInfoCriteria.and("oprtInfo.0.oprtTime").lte(req.getEndTime());
        }

        ProjectionOperation project = Aggregation.project()
                .andInclude(DbFieldName.common__id.n())
                .andInclude(DbFieldName.AbpOop.prov.n())
                .andInclude(DbFieldName.AbpOop.mainPrjCode.n())
                .andInclude(DbFieldName.AbpOop.type.n())
                .andInclude(DbFieldName.AbpOop.planVer.n())
                .andInclude(DbFieldName.common_name.n())
                .andInclude(DbFieldName.AbpOop.sales.n())
                .andInclude(DbFieldName.AbpOop.pm.n())
                .andInclude(DbFieldName.AbpOop.signDate.n())
                .andInclude(DbFieldName.AbpOop.netSales.n())
                .andInclude(DbFieldName.AbpOop.netSalesL.n())
                .andInclude(DbFieldName.AbpOop.netSalesM.n())
                .andInclude(DbFieldName.AbpOop.isLocked.n())
                .andInclude(DbFieldName.AbpOop.isDeleted.n())
                .andInclude(DbFieldName.common_isValid.n())
                .andInclude(DbFieldName.common_addUser.n())
                .andInclude(DbFieldName.common_addTime.n())
                .andInclude(DbFieldName.common_cn.n())
                .andInclude(DbFieldName.AbpOop.linkedOrderCode.n())
                .andExpression("{'$filter':{'input':'$oprtInfo', 'as':'item', 'cond':{'$and':{{'$eq':{'$$item.oprtType', 'DataSync:KeyInfoChgDetail'}}}}}}")
                .as("oprtInfo");

        ProjectionOperation project2 = Aggregation.project()
                .andInclude(DbFieldName.common__id.n())
                .andInclude(DbFieldName.AbpOop.prov.n())
                .andInclude(DbFieldName.AbpOop.mainPrjCode.n())
                .andInclude(DbFieldName.AbpOop.type.n())
                .andInclude(DbFieldName.AbpOop.planVer.n())
                .andInclude(DbFieldName.common_name.n())
                .andInclude(DbFieldName.AbpOop.sales.n())
                .andInclude(DbFieldName.AbpOop.pm.n())
                .andInclude(DbFieldName.AbpOop.signDate.n())
                .andInclude(DbFieldName.AbpOop.netSales.n())
                .andInclude(DbFieldName.AbpOop.netSalesL.n())
                .andInclude(DbFieldName.AbpOop.netSalesM.n())
                .andInclude(DbFieldName.AbpOop.isLocked.n())
                .andInclude(DbFieldName.AbpOop.isDeleted.n())
                .andInclude(DbFieldName.common_isValid.n())
                .andInclude(DbFieldName.common_addUser.n())
                .andInclude(DbFieldName.common_addTime.n())
                .andInclude(DbFieldName.common_cn.n())
                .andInclude(DbFieldName.AbpOop.linkedOrderCode.n())
                .andExpression("{'$reverseArray' : '$oprtInfo'}")
                .as("oprtInfo");

        Aggregation agg = null;
        if (req.getStartTime() != null  || req.getEndTime() != null){
            agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    project,
                    project2,
                    Aggregation.match(oprtInfoCriteria),
                    Aggregation.skip(req.getCurPage() * req.getPageSize()),
                    Aggregation.limit(req.getPageSize())
            );
        }else {
            agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    project,
                    Aggregation.skip(req.getCurPage() * req.getPageSize()),
                    Aggregation.limit(req.getPageSize())
            );
        }

        List<AbpOop> list = mongoTemplate.aggregate(agg, DBT.ABP_OOP.n(), AbpOop.class).getMappedResults();

        //根据版本id查询版本信息
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataByIds(planVerIds);
        Map<ObjectId, List<SysVerMgt>> planVerMap = sysVerMgts.stream().collect(Collectors.groupingBy(SysVerMgt::getId));
        List<Object> result = new ArrayList<>();
        int index = 1;
        List<ObjectId> pmIds = new ArrayList<>();
        for (AbpOop abpOop : list) {
            if (null != abpOop.getPm() && null != abpOop.getPm().getUserId()) {
                pmIds.add(abpOop.getPm().getUserId());
            }
        }
        List<IDbCondition> pmConds = new ArrayList<>();
        pmConds.add(new DC_I<>(DbFieldName.sysUser_id, pmIds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common_userName);
        fieldNames.add(DbFieldName.common_loginName);
        fieldNames.add(DbFieldName.common_jobCode);
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.sysUser_sbuId);

        List<TeSysUser> subIds = sysUserDao.findByFieldAndConds(pmConds, fieldNames);
        Map<ObjectId, String> buCodeMap = new HashMap<>();
        for (TeSysUser sysUser : subIds) {
            buCodeMap.put(sysUser.getId(), sysUser.getSbuId());
        }

        for (AbpOop abpOop : list) {
            setValues(result, abpOop, planVerMap, index, buCodeMap);
            index++;
        }
        Aggregation countAgg = null;
        CountOperation countOperation = Aggregation.count().as("count");
        SkipOperation skipOperation = Aggregation.skip(req.getCurPage() * req.getPageSize());
        LimitOperation limitOperation = Aggregation.limit(req.getPageSize());
        if (req.getStartTime() != null  || req.getEndTime() != null){
            countAgg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    project,
                    project2,
                    Aggregation.match(oprtInfoCriteria),
                    Aggregation.project(DFN.common__id.n()),
                    Aggregation.facet(countOperation).as("count").and(skipOperation, limitOperation).as("data")
            );
        }else {
            countAgg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.project(DFN.common__id.n()),
                    Aggregation.facet(countOperation).as("count").and(skipOperation, limitOperation).as("data")
            );
        }
        List<Map> resultList = mongoTemplate.aggregate(countAgg, DBT.ABP_OOP.n(), Map.class).getMappedResults();
        List<Map<String, Integer>> mapList = (List<Map<String, Integer>>) resultList.get(0).get("count");
        Integer count = 0;
        if (!CollectionUtils.isEmpty(mapList)) {
            count = mapList.get(0).get("count");
        }

        PageListResult page = new PageListResult();
        page.setCount(count);
        page.setList(result);
        return page;
    }

    @Override
    public PageListResult queryAnnualPlanForIndex(String buCode, AnnualPlanQueryReq req) {
        //查询abpOop表中符合当前计划年份、isValid=true、是当前登录人所属BU的所有版本信息展示在页面中
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DbFieldName.common_isValid, true));
        List<ObjectId> planVerIds = req.getPlanVerIds();
        if (CollectionUtils.isEmpty(planVerIds)) {
            List<PlanVersion> planVersions = serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
            planVerIds = planVersions.stream().map(PlanVersion::getCid).collect(Collectors.toList());
        }
        // 取最新版本
        conditions.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVerIds.get(0)));
        String pmName = req.getPmName();
        String salesUserName = req.getSalesUserName();

        List<ObjectId> typeList = new ArrayList<>();
        typeList.add(Constant.FORECAST_ORDER_ID);
        typeList.add(Constant.FUTURE_FORECAST_ORDER_ID);

        // or查询要放在一起
        String codeName = req.getCodeName();
        String linkedOrderCode = req.getLinkedOrderCode();

        if (StringUtil.isNotNull(pmName) && StringUtil.isNotNull(salesUserName)) {
            List<IDbCondition> pmConds = new ArrayList<>();
            pmConds.add(new DC_L(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName), pmName));
            pmConds.add(new DC_L(DbFieldName.AbpOop.pm.dot(DbFieldName.common_jobCode), pmName));

            List<IDbCondition> salesConds = new ArrayList<>();
            salesConds.add(new DC_L(DbFieldName.AbpOop.sales.dot(DbFieldName.common_userName), salesUserName));
            salesConds.add(new DC_L(DbFieldName.AbpOop.sales.dot(DbFieldName.common_jobCode), salesUserName));

            List<IDbCondition> andConds = new ArrayList<>();
            andConds.add(new DC_OR(pmConds));
            andConds.add(new DC_OR(salesConds));
            // 只有订单流水号
            if (StringUtil.isNotNull(codeName) && StringUtil.isNull(linkedOrderCode)) {
                List<IDbCondition> orCondsForCodeName = new ArrayList<>();
                orCondsForCodeName.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                orCondsForCodeName.add(new DC_L(DbFieldName.common_cn, codeName, true));
                // 这边也得加和的逻辑
                List<IDbCondition> orCondsForSalesTotal = new ArrayList<>();
                orCondsForSalesTotal.add(new DC_OR(orCondsForCodeName));
                orCondsForSalesTotal.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DFN.common_cid), typeList, false));
                andConds.add(new DC_AO(orCondsForSalesTotal));
            } else if (StringUtil.isNotNull(codeName) && StringUtil.isNotNull(linkedOrderCode)) {
                andConds.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                andConds.add(new DC_L(DbFieldName.common_cn, linkedOrderCode, true));
            }
            conditions.add(new DC_AO(andConds));
        } else if (StringUtil.isNotNull(pmName) || StringUtil.isNotNull(salesUserName)) {
            // 不能都没有过滤值
            List<IDbCondition> pmConds = new ArrayList<>();
            List<IDbCondition> salesConds = new ArrayList<>();
            List<IDbCondition> codeNameConds = new ArrayList<>();
            List<IDbCondition> allConds = new ArrayList<>();
            // 单独的也得加
            List<IDbCondition> extraConds = new ArrayList<>();
            if (StringUtil.isNotNull(pmName)) {
                pmConds.add(new DC_L(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName), pmName));
                pmConds.add(new DC_L(DbFieldName.AbpOop.pm.dot(DbFieldName.common_jobCode), pmName));
                allConds.add(new DC_OR(pmConds));
            }
            if (StringUtil.isNotNull(salesUserName)) {
                salesConds.add(new DC_L(DbFieldName.AbpOop.sales.dot(DbFieldName.common_userName), salesUserName));
                salesConds.add(new DC_L(DbFieldName.AbpOop.sales.dot(DbFieldName.common_jobCode), salesUserName));
                allConds.add(new DC_OR(salesConds));
            }
            if (StringUtil.isNotNull(codeName) && StringUtil.isNull(linkedOrderCode)) {
                codeNameConds.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                codeNameConds.add(new DC_L(DbFieldName.common_cn, codeName, true));
                extraConds.add(new DC_OR(codeNameConds));
                extraConds.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DFN.common_cid), typeList, false));
                allConds.add(new DC_AO(extraConds));
            } else if (StringUtil.isNotNull(codeName) && StringUtil.isNotNull(linkedOrderCode)) {
                allConds.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                allConds.add(new DC_L(DbFieldName.common_cn, linkedOrderCode, true));
            }
            conditions.add(new DC_AO(allConds));
        }
        String name = req.getName();
        if (StringUtil.isNotNull(name)) {
            conditions.add(new DC_L(DbFieldName.common_name, name, true));
        }

        // 其他都没有or条件才单独加or
        // 此时还得判断有无项目代码的筛选
        if (StringUtil.isNull(pmName) && StringUtil.isNull(salesUserName) && StringUtil.isNotNull(codeName)) {
            if (StringUtil.isNull(linkedOrderCode)) {
                List<IDbCondition> orCondsForSales = new ArrayList<>();
                List<IDbCondition> orCondsForSalesTotal = new ArrayList<>();
                orCondsForSales.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                orCondsForSales.add(new DC_L(DbFieldName.common_cn, codeName, true));
                orCondsForSalesTotal.add(new DC_OR(orCondsForSales));
                // 同时要是这两种类型 上面是或关系
                orCondsForSalesTotal.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DFN.common_cid), typeList, false));
                conditions.add(new DC_AO(orCondsForSalesTotal));
            } else {
                // 两个都有就应该和
                conditions.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
                conditions.add(new DC_L(DbFieldName.common_cn, linkedOrderCode, true));
            }
        }

        // 项目代码的筛选 只涉及oop的codeName字段 但筛选出来会包含linkedOrderCode
        if (StringUtil.isNotNull(linkedOrderCode) && StringUtil.isNull(codeName)) {
            conditions.add(new DC_L(DbFieldName.common_cn, linkedOrderCode, true));
            conditions.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DFN.common_cid), typeList, true));
        }
        String mainPrjCode = req.getMainPrjCode();
        if (StringUtil.isNotNull(mainPrjCode)) {
            conditions.add(new DC_L(DbFieldName.AbpOop.mainPrjCode, mainPrjCode, true));
        }
        // 首页前台传参
        String queryWord = req.getQueryWord();

        List<IDbCondition> orConds = new ArrayList<>();
        orConds.add(new DC_L(DbFieldName.AbpOop.mainPrjCode, queryWord, true));
        orConds.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, queryWord, true));
        orConds.add(new DC_L(DbFieldName.common_cn, queryWord, true));
        orConds.add(new DC_L(DbFieldName.common_name, queryWord, true));
        // 不用判断queryWord是否为空
        conditions.add(new DC_OR(orConds));

        List<AbpOop> list = abpOopDao.findByFieldAndConds(conditions, null, null, new Pager(req.getCurPage(), req.getPageSize()));
        //根据版本id查询版本信息
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataByIds(planVerIds);
        Map<ObjectId, List<SysVerMgt>> planVerMap = sysVerMgts.stream().collect(Collectors.groupingBy(SysVerMgt::getId));
        List<Object> result = new ArrayList<>();
        int index = 1;
        List<ObjectId> pmIds = new ArrayList<>();
        for (AbpOop abpOop : list) {
            if (null != abpOop.getPm() && null != abpOop.getPm().getUserId()) {
                pmIds.add(abpOop.getPm().getUserId());
            }
        }
        List<IDbCondition> pmConds = new ArrayList<>();
        pmConds.add(new DC_I<>(DbFieldName.sysUser_id, pmIds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common_userName);
        fieldNames.add(DbFieldName.common_loginName);
        fieldNames.add(DbFieldName.common_jobCode);
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.sysUser_sbuId);

        List<TeSysUser> subIds = sysUserDao.findByFieldAndConds(pmConds, fieldNames);
        Map<ObjectId, String> buCodeMap = new HashMap<>();
        for (TeSysUser sysUser : subIds) {
            buCodeMap.put(sysUser.getId(), sysUser.getSbuId());
        }

        for (AbpOop abpOop : list) {
            setValues(result, abpOop, planVerMap, index, buCodeMap);
            index++;
        }
        long count = abpOopDao.countByConds(conditions);
        PageListResult page = new PageListResult();
        page.setCount(count);
        page.setList(result);
        return page;
    }

    private void setValues(List<Object> result, AbpOop abpOop, Map<ObjectId, List<SysVerMgt>> planVerMap, int index, Map<ObjectId, String> buCodeMap) {
        AnnualPlanQueryResp resp = new AnnualPlanQueryResp();
        resp.setId(abpOop.getId());
        resp.setProv(abpOop.getProv());
        resp.setMainPrjCode(abpOop.getMainPrjCode());
        TeIdNameCn oopType = abpOop.getType();
        PlanVersion planVer = abpOop.getPlanVer();
        resp.setPlanVer(planVer);
        List<SysVerMgt> sysVerMgts = planVerMap.get(planVer.getCid());
        SysVerMgt mgt = sysVerMgts.get(0);
        resp.setRatePlanVer(mgt.getLinkedVer());
        resp.setName(abpOop.getName());
        resp.setSales(abpOop.getSales());
        resp.setPm(abpOop.getPm());
        if (null != abpOop.getPm() && null != abpOop.getPm().getUserId() && null != buCodeMap.get(abpOop.getPm().getUserId())) {
            resp.setBuCode(buCodeMap.get(abpOop.getPm().getUserId()));
        }
        resp.setSignDate(abpOop.getSignDate());
        resp.setNetSales(BigDecimalUtils.divideDouble(abpOop.getNetSales(),1000,5));
        resp.setNetSalesL(BigDecimalUtils.divideDouble(abpOop.getNetSalesL(),1000,5));
        resp.setNetSalesM(BigDecimalUtils.divideDouble(abpOop.getNetSalesM(),1000,5));
        if (abpOop.getIsLocked() != null) {
            resp.setLocked(abpOop.getIsLocked());
        }
        if (abpOop.getIsDeleted() != null) {
            resp.setDeleted(abpOop.getIsDeleted());
        }
        if (abpOop.getIsValid() != null) {
            resp.setValid(abpOop.getIsValid());
        }
        if (abpOop.getAddUser() != null) {
            resp.setAddUser(abpOop.getAddUser());
        }
        if (abpOop.getAddTime() != null) {
            resp.setAddTime(abpOop.getAddTime());
        }
        if (Constant.FORECAST_ORDER_ID.equals(oopType.getCid())||Constant.FUTURE_FORECAST_ORDER_ID.equals(oopType.getCid())){
            resp.setLinkedOrderCode(abpOop.getCodeName());

        }else {
            resp.setCodeName(abpOop.getCodeName());
            resp.setLinkedOrderCode(abpOop.getLinkedOrderCode());
        }
        resp.setType(abpOop.getType());
        resp.setIndex(index);

        // 最后更新时间
        Date lastUpdateTime = null;
        List<OperationInfo> oprtInfo = abpOop.getOprtInfo();
        if (!CollectionUtils.isEmpty(oprtInfo)) {
            for (OperationInfo info : oprtInfo) {
                String oprtType = info.getOprtType();
                if (!"DataSync:KeyInfoChgDetail".equals(oprtType))
                    continue;

                Date oprtTime = info.getOprtTime();
                if (null == lastUpdateTime) {
                    lastUpdateTime = oprtTime;
                } else {
                    lastUpdateTime = (1 == lastUpdateTime.compareTo(oprtTime)) ? lastUpdateTime : oprtTime;
                }
            }
            resp.setLastUpdateTime(lastUpdateTime);
        }
        result.add(resp);
    }

    @Override
    public PageBean exportAnnualPlanData(String buCode, AnnualPlanQueryReq req) {
        LinkedHashMap<Integer, String> titleMap = new LinkedHashMap<>();
        List<Object> result = new ArrayList<>();
        int index = 0;
        titleMap.put(index++, "序");
        titleMap.put(index++, "省份");
        titleMap.put(index++, "版本编码");
        titleMap.put(index++, "费率版本");
        titleMap.put(index++, "订单流水号");
        titleMap.put(index++, "项目编码");
        titleMap.put(index++, "主项目编码");
        titleMap.put(index++, "项目名称");
        titleMap.put(index++, "销售人员");
        titleMap.put(index++, "项目经理");
        titleMap.put(index++, "PM所属BU");
        titleMap.put(index++, "签约日期");
        titleMap.put(index++, "年度订单");
        titleMap.put(index++, "项目净销售额M");
        titleMap.put(index++, "项目净销售额L");
        titleMap.put(index++, "计划类型");
        titleMap.put(index++, "补全人");
        titleMap.put(index++, "补全时间");
        titleMap.put(index, "更新时间");
        result.add(titleMap);
        PageListResult pageListResult = this.queryAnnualPlan(buCode, req);
        List<AnnualPlanQueryResp> abpOops = (List<AnnualPlanQueryResp>) pageListResult.getList();
        int num = 1;
        for (AnnualPlanQueryResp abpOop : abpOops) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map.put("num", num++);
            TeIdNameCn prov = abpOop.getProv();
            map.put("省份", "");
            if (prov!=null){
                map.put("省份", prov.getName());
            }
            PlanVersion planVer = abpOop.getPlanVer();
            map.put("版本编码","");
            if (planVer!=null){
                map.put("版本编码", planVer.getVerNo());
            }
            PlanVersion ratePlanVer = abpOop.getRatePlanVer();
            map.put("费率版本", ratePlanVer.getVerNo());
            map.put("订单流水号", abpOop.getCodeName());
            map.put("项目编码", abpOop.getLinkedOrderCode());
            map.put("主项目编码", abpOop.getMainPrjCode());
            map.put("项目名称", abpOop.getName());
            TeUser salesUser = abpOop.getSales();
            map.put("销售人员", "");
            if (salesUser != null) {
                map.put("销售人员", String.join("/",salesUser.getUserName(),salesUser.getJobCode()));
            }
            TeUser pm = abpOop.getPm();
            map.put("项目经理", "");
            if (pm != null) {
                map.put("项目经理", String.join("/",pm.getUserName(),pm.getJobCode()));
            }
            String pmBuCode = abpOop.getBuCode();
            map.put("PM所属BU", "");
            if (pmBuCode != null) {
                map.put("PM所属BU", pmBuCode);
            }
            map.put("签约日期", DateUtil.formatDate2Str(abpOop.getSignDate(),DateUtil.DATE_FORMAT));
            map.put("项目净销售额", abpOop.getNetSales());
            map.put("项目净销售额M", abpOop.getNetSalesM());
            map.put("项目净销售额L", abpOop.getNetSalesL());
            TeIdNameCn type = abpOop.getType();
            map.put("项目类型", "");
            if (type!=null){
                map.put("项目类型", type.getName());
            }
            TeUser addUser = abpOop.getAddUser();
            map.put("补全人", "");
            if (addUser!=null){
                map.put("补全人", String.join("/",addUser.getUserName(),addUser.getJobCode()));
            }else if (prov!=null){
                map.put("补全人", "system");
            }
            map.put("补全时间", "");
            Date addTime = abpOop.getAddTime();
            if (addTime!=null&&prov!=null){
                map.put("补全时间", DateUtil.formatDate2Str(addTime,DateUtil.DATETIME_FORMAT));
            }
            map.put("更新时间", "");
            Date lastUpdateTime = abpOop.getLastUpdateTime();
            if (lastUpdateTime!=null){
                map.put("更新时间", DateUtil.formatDate2Str(lastUpdateTime,DateUtil.DATETIME_FORMAT));
            }
            result.add(map);
        }
        PageBean pageBean = new PageBean();
        pageBean.setObjectList(result);
        pageBean.setCount(abpOops.size());
        return pageBean;
    }

    @Override
    public List<TeSysDef> findProvince(String buCode) {
        if (StringUtils.isEmpty(buCode)){
            return Collections.emptyList();
        }
        List<TeSysDef> provList = sysDefDao.getSysDefByDefTypeAndSrcDef(Constant.ABP_PROV_CODE_NAME, buCode);
        if (CollectionUtils.isEmpty(provList)){
            return Collections.emptyList();
        }
        return response(provList);
    }

    @Override
    public PageListResult queryPlanFormulationData(PlanFormulationReq req) {
        //查询abpOop表中符合当前计划年份、isValid=true、是当前登录人所属BU的所有版本信息展示在页面中
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), req.getPlanVerId()));
        conditions.add(new DC_E(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid), req.getProvId()));
        // 计划编制页面只返回valid为true
        conditions.add(new DC_E(DbFieldName.common_isValid, true));
        if (!req.getIsLock()) {
            conditions.add(new DC_E(DbFieldName.AbpOop.isLocked, false));
        }
        //模糊查询
        String linkedOrderCode = req.getCodeName();
        String codeName = req.getProjectCode();
        if (StringUtil.isNotNull(codeName)&&StringUtil.isNotNull(linkedOrderCode)){
            conditions.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, linkedOrderCode, true));
            conditions.add(new DC_L(DbFieldName.common_cn, codeName, true));
        }else if (StringUtil.isNotNull(linkedOrderCode)&&StringUtil.isNull(codeName)){
            List<IDbCondition> and = new ArrayList<>();
            List<IDbCondition> linkedOrderCodeCond = new ArrayList<>();
            linkedOrderCodeCond.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, linkedOrderCode, true));
            linkedOrderCodeCond.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid),Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID),true));

            List<IDbCondition> codeNameCond = new ArrayList<>();
            codeNameCond.add(new DC_L(DbFieldName.common_cn, linkedOrderCode, true));
            codeNameCond.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid),Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID)));

            and.add(new DC_AO(linkedOrderCodeCond));
            and.add(new DC_AO(codeNameCond));
            conditions.add(new DC_OR(and));
        }else if (StringUtil.isNotNull(codeName)){
            conditions.add(new DC_L(DbFieldName.common_cn, codeName, true));
            conditions.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid),Arrays.asList(Constant.FUTURE_FORECAST_ORDER_ID,Constant.FORECAST_ORDER_ID),true));
        }
        String name = req.getName();
        if (StringUtil.isNotNull(name)) {
            //项目名称
            //去除特殊字符
            Pattern compile = Pattern.compile(Pattern.quote(name), Pattern.CASE_INSENSITIVE | Pattern.UNICODE_CASE);
            conditions.add(new DC_L(DbFieldName.common_name, compile.toString(), true));
        }
        String pmName = req.getPmName();
        if (StringUtil.isNotNull(pmName)) {
            //项目经理
            conditions.add(new DC_L(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName), pmName, true));
        }
        String type = req.getType();
        if (StringUtil.isNotNull(type)) {
            //项目类型
            conditions.add(new DC_L(DbFieldName.AbpOop.type.dot(DbFieldName.common_name), type, true));
        }
        String mainPrjCode = req.getMainPrjCode();
        if (StringUtil.isNotNull(mainPrjCode)) {
            //项目类型
            conditions.add(new DC_L(DbFieldName.AbpOop.mainPrjCode, mainPrjCode, true));
        }
        // 是否做计划标识
        String planFlag = req.getPlanFlag();
        if (StringUtil.isNotNull(planFlag)) {
            if (Constant.TRUE_STR.equals(planFlag)) {
                List<IDbCondition> orConds = new ArrayList<>();
                orConds.add(new DC_E(DFN.AbpOop.subOops.dot(DFN.AbpOop.isPlanPrj), true, true));
                orConds.add(new DC_E(DFN.AbpOop.isMain, true, true));
                List<IDbCondition> and = new ArrayList<>();
                and.add(new DC_OR(orConds));
                and.add(new DC_E(DbFieldName.AbpOop.type.dot(DFN.common_cid), Constant.BACKFILLING_PROJECT_ID, true));
                conditions.add(new DC_AO(and));
            } else if (Constant.FALSE_STR.equals(planFlag)) {
                List<IDbCondition> andConds = new ArrayList<>();
                List<IDbCondition> orConds = new ArrayList<>();
                andConds.add(new DC_E(DFN.AbpOop.subOops.dot(DFN.AbpOop.isPlanPrj), true));
                andConds.add(new DC_E(DFN.AbpOop.isMain, true));
                orConds.add(new DC_E(DbFieldName.AbpOop.type.dot(DFN.common_cid), Constant.BACKFILLING_PROJECT_ID));
                orConds.add(new DC_AO(andConds));
                conditions.add(new DC_OR(orConds));
            }
        }
        List<AbpOop> list = abpOopDao.findByFieldAndConds(conditions, null, null, new Pager(req.getCurPage(), req.getPageSize()));
        if (CollectionUtils.isEmpty(list)){
            PageListResult pageListResult = new PageListResult();
            pageListResult.setList(new ArrayList<>());
            pageListResult.setCount(0);
            return pageListResult;
        }
        //过滤得到提前立项，分拆项目的id
        List<ObjectId> secPrjIds = Arrays.asList(Constant.BACKFILLING_PROJECT_ID, Constant.SPIN_OFF_PROJECT_ID, Constant.HIST_SPLIT_PRJ_ID);
        List<ObjectId> ids = list.stream().filter(s -> s.getType() != null && secPrjIds.contains(s.getType().getCid())).map(AbpOop::getId).collect(Collectors.toList());
        //查询提前立项的主项目
        List<AbpOop> abpOops = this.queryAbpOopByTypeCid(ids);
        //辅项目对应主项目的code关系,键是辅项目的id，值是主项目的codeName
        Map<ObjectId, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(abpOops)) {
            for (AbpOop abpOop : abpOops) {
                List<SubOop> subOops = abpOop.getSubOops();
                for (SubOop subOop : subOops) {
                    TeIdNameCn subOopOop = subOop.getOop();
                    if (subOopOop!=null){
                        map.put(subOopOop.getCid(), abpOop.getCodeName());
                    }
                }
            }
        }
        List<QueryPlanResp> reslut=setResponse(list, map);
        long count = abpOopDao.countByConds(conditions);
        PageListResult pageListResult = new PageListResult();
        pageListResult.setList(reslut);
        pageListResult.setCount(count);
        return pageListResult;
    }

    private List<QueryPlanResp> setResponse(List<AbpOop> list, Map<ObjectId, String> map) {
        int index = 1;
        List<QueryPlanResp> result = new ArrayList<>();
        List<ObjectId> idList = list.stream().filter(item -> Objects.nonNull(item.getPm()) && Objects.nonNull(item.getPm().getUserId()))
                .map(item -> item.getPm().getUserId()).collect(Collectors.toList());
        List<TeSysUser> pmList = querySysUser(null, idList);
        Map<ObjectId, String> ntToSbuName = pmList.stream().filter(item -> Objects.nonNull(item.getId()) && Objects.nonNull(item.getSbuName()))
                .collect(Collectors.toMap(TeSysUser::getId, TeSysUser::getSbuName));
        for (AbpOop abpOop : list) {
            QueryPlanResp planResp = new QueryPlanResp();
            planResp.setId(abpOop.getId());
            planResp.setIsLocked(abpOop.getIsLocked());
            planResp.setIsValid(abpOop.getIsValid());
            planResp.setIsDeleted(abpOop.getIsDeleted());
            planResp.setMainPrjCode(abpOop.getMainPrjCode());
            planResp.setName(abpOop.getName());
            planResp.setCodeName(abpOop.getCodeName());
            planResp.setLinkedOrderCode(abpOop.getLinkedOrderCode());
            planResp.setPmsPlanEndDate(abpOop.getPmsPlanEndDate());
            planResp.setPmsPlanStartDate(abpOop.getPmsPlanStartDate());
            planResp.setPlanVer(abpOop.getPlanVer());
            //这里根据type来判断codeName字段放到前端的那个字段显示：
            //当年签约、历史签约、研发项目、提前立项、其他项目这5中类型的codename是项目代码的意思，预测订单这种类型的codename是预测订单的意思
            TeIdNameCn type = abpOop.getType();
            if(type!=null&&(!type.getCid().equals(Constant.FORECAST_ORDER_ID)&&!type.getCid().equals(Constant.FUTURE_FORECAST_ORDER_ID))){
                planResp.setCodeName(abpOop.getLinkedOrderCode());
                planResp.setLinkedOrderCode(abpOop.getCodeName());
            }
            planResp.setSubOops(abpOop.getSubOops());
            planResp.setIsPrim(true);
            //如果是辅项目
            String cn = map.get(abpOop.getId());
            if (StringUtil.isNotNull(cn)) {
                SubOop subOop = new SubOop();
                TeIdNameCn subOopOop = new TeIdNameCn();
                subOopOop.setCodeName(cn);
                subOop.setOop(subOopOop);
                planResp.setSubOops(Collections.singletonList(subOop));
                planResp.setIsPrim(false);
            }
            planResp.setNetSalesM(BigDecimalUtils.divideDouble(abpOop.getNetSalesM(), 1000, 5));
            planResp.setNetSalesL(BigDecimalUtils.divideDouble(abpOop.getNetSalesL(), 1000, 5));
            planResp.setBacklogAmt(BigDecimalUtils.divideDouble(abpOop.getBacklogAmt(), 1000, 5));
            planResp.setSignDate(abpOop.getSignDate());
            planResp.setType(abpOop.getType());
            planResp.setCustomBudgetType(abpOop.getCustomBudgetType());
            planResp.setContractType(abpOop.getContractType());
            planResp.setPrjType(abpOop.getPrjType());
            planResp.setSales(abpOop.getSales());
            planResp.setPm(abpOop.getPm());
            if (Objects.nonNull(abpOop.getPm()) && Objects.nonNull(abpOop.getPm().getUserId())) {
                planResp.setPmBuName(ntToSbuName.get(abpOop.getPm().getUserId()));
            }
            planResp.setPrjLevel(abpOop.getPrjLevel());
            //产品线和占比要放在一起
            StringBuilder sb = new StringBuilder();
            for (AbpOopPl abpOopPl : abpOop.getPls()) {
                if (!abpOopPl.getIsValid()) {
                    continue;
                }
                TeIdNameCn pl = abpOopPl.getPl();
                if (pl == null) {
                    continue;
                }
                sb.append(pl.getName()).append(Constant.COLON).append(abpOopPl.getPct())
                        .append(Constant.PERCENT_SIGN).append(Constant.COMMA);
            }
            if (StringUtil.isNotNull(sb)){
                sb.deleteCharAt(sb.lastIndexOf(Constant.COMMA));
                planResp.setPl(sb.toString());
            }
            planResp.setOopCode(abpOop.getOopCode());
            planResp.setIndex(index++);
            planResp.setPmIsChangedStr((abpOop.getPmIsChanged() == null || abpOop.getPmIsChanged() == false) ? "未手工修改PM" : "已手工修改PM");
            result.add(planResp);
        }
        return result;
    }

    private List<AbpOop> queryAbpOopByTypeCid(List<ObjectId> ids) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid), Arrays.asList(Constant.FORECAST_ORDER_ID,Constant.FUTURE_FORECAST_ORDER_ID, Constant.SIGNED_CURRENT_YEAR_ID, Constant.HIS_RESIGN_PRJ_ID, Constant.HISTORICAL_SIGNING_ID)));
        conds.add(new DC_I<>(DbFieldName.AbpOop.subOops.dot(DbFieldName.AbpOop.oop.dot(DbFieldName.common_cid)), ids));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_cn);
        fieldNames.add(DbFieldName.AbpOop.subOops);
        return abpOopDao.findByFieldAndConds(conds, fieldNames);
    }

    @Override
    public PageBean exportPlanFormulationData(String buCode, PlanFormulationReq req) {
        LinkedHashMap<Integer, String> titleMap = new LinkedHashMap<>();
        List<Object> result = new ArrayList<>();
        int index = 0;
        titleMap.put(index++, "计划类型");
        titleMap.put(index++, "客户预算类型");
        titleMap.put(index++, "合同类型");
        titleMap.put(index++, "项目类型");
        titleMap.put(index++, "订单流水号");
        titleMap.put(index++, "合同编码");
        titleMap.put(index++, "项目代码（SDP）");
        titleMap.put(index++, "预计签约/签约日期");
        titleMap.put(index++, "项目名称");
        titleMap.put(index++, "项目分级");
        titleMap.put(index++, "项目经理");
        titleMap.put(index++, "项目净销售额");
        titleMap.put(index++, "项目净销售额M");
        titleMap.put(index++, "项目净销售额L");
        titleMap.put(index, "上一年底Backlog（￥K）");
        result.add(titleMap);
        PageListResult pageListResult = this.queryPlanFormulationData(req);
        List<QueryPlanResp> abpOops = (List<QueryPlanResp>) pageListResult.getList();
        for (QueryPlanResp abpOop : abpOops) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map.put("计划类型", null);
            if (abpOop.getType() != null) {
                map.put("计划类型", abpOop.getType().getName());
            }
            map.put("客户预算类型", null);
            if (abpOop.getCustomBudgetType() != null) {
                map.put("客户预算类型", abpOop.getCustomBudgetType().getName());
            }
            map.put("合同类型", null);
            if (abpOop.getContractType() != null) {
                map.put("合同类型", abpOop.getContractType().getName());
            }
            map.put("项目类型", null);
            if (abpOop.getPrjType() != null) {
                map.put("项目类型", abpOop.getPrjType().getName());
            }
            if (abpOop.getLinkedOrderCode() == null) {
                map.put("订单流水号", abpOop.getCodeName());
                map.put("项目代码（SDP）", null);
            } else {
                map.put("订单流水号", abpOop.getLinkedOrderCode());
                map.put("项目代码（SDP）", abpOop.getCodeName());
            }
            map.put("项目代码", abpOop.getLinkedOrderCode());
            map.put("预计签约日期", abpOop.getSignDate());
            map.put("项目名称", abpOop.getName());
            TeIdNameCn prjLevel = abpOop.getPrjLevel();
            map.put("项目分级", "");
            if (prjLevel!=null){
                map.put("项目分级", prjLevel.getName());
            }
            map.put("项目经理", "");
            TeUser pm = abpOop.getPm();
            if (pm != null) {
                map.put("项目经理", pm.getUserName());
            }
            map.put("项目净销售额M", abpOop.getNetSalesM());
            map.put("项目净销售额L", abpOop.getNetSalesL());
            map.put("上一年底Backlog（￥K）", abpOop.getBacklogAmt());
            map.put("产品线名称及占比", abpOop.getPl());
            result.add(map);
        }
        PageBean pageBean = new PageBean();
        pageBean.setObjectList(result);
        pageBean.setCount(abpOops.size());
        return pageBean;
    }


    @Override
    public AbpOop queryAbpOopTypes(ObjectId id) {
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DbFieldName.common__id, id));
        List<AbpOop> abpOops = abpOopDao.findByFieldAndConds(conditions, null);
        return abpOops.get(0);
    }

    @Override
    public void updateLock(ObjectId abpOopId, ObjectId provId, String buCode, Boolean isLockedFlag, Boolean isPms) {
        //1.提前立项,其他项目只能查看不能编辑
        AbpOop abpOop = abpOopDao.findById(abpOopId);
        ObjectId typeId = abpOop.getType() != null ? abpOop.getType().getCid() : null;
//        if (Constant.BACKFILLING_PROJECT_ID.equals(typeId) || Constant.OTHER_PROJECTS_ID.equals(typeId)) {
//            throw BusinessException.initExc("项目编辑失败!提前立项只能查看不能编辑");
//        }
        //2.查询最新版本
        List<PlanVersion> planVersions = serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
        if (CollectionUtils.isEmpty(planVersions)) {
            throw BusinessException.initExc("未查到经营计划版本,请联系管理员!");
        }
        PlanVersion planVersion = planVersions.get(0);
        //3.省份进度在计划编制中的时候，才可以编辑锁
        List<AbpOprt> abpOprts = abpOprtDao.queryDataByPlanVerIdAndDept(planVersion.getCid(), Collections.singletonList(provId));
        if (CollectionUtils.isEmpty(abpOprts)) {
            throw BusinessException.initExc("未查到该省份对应的进度,请联系管理员!");
        }
        AbpOprt abpOprt = abpOprts.get(0);
        TeIdNameCn status = abpOprt.getStatus();
        if (!status.getCid().equals(Constant.ABP_OPRT_PLANNING_IN_PROGRESS_ID)&&!status.getCid().equals(Constant.ABP_OPRT_BEATEN_BACK_ID)) {
            throw BusinessException.initExc("经营计划对应的省份进度在计划编制中或被打回,才可以编辑锁");
        }
//        if (!Boolean.TRUE.equals(isPms)) {
//            if (Constant.FORECAST_ORDER_ID.equals(typeId) || Constant.FUTURE_FORECAST_ORDER_ID.equals(typeId)
//                    || Constant.SIGNED_CURRENT_YEAR_ID.equals(typeId)|| Constant.HIS_RESIGN_PRJ_ID.equals(typeId) || Constant.HISTORICAL_SIGNING_ID.equals(typeId)) {
//
//                boolean fullPeriodDataNullFlag = abpOop.getSumEngineerRg() == null && abpOop.getSumRadRg() == null
//                        && abpOop.getSumOsempRgFee() == null && abpOop.getSumDirectFee() == null
//                        && abpOop.getSumStaffOutsourceFee() == null && abpOop.getSumTechSubcontractFee() == null;
//                if (fullPeriodDataNullFlag) {
//                    throw BusinessException.initExc("项目全周期数据不能为空，请维护后重试！");
//                }
//            }
//        }
        List<UpdataData> update = new ArrayList<>();
        update.add(new UpdataData(DbFieldName.common_isLocked, isLockedFlag));
        List<IDbCondition> query = new ArrayList<>();
        query.add(new DC_E(DbFieldName.common__id, abpOopId));
        abpOopDao.updateByConds(query, update);
        if (Boolean.TRUE.equals(isPms)) {
            return;
        }
        if (isLockedFlag) {
//            SysVerMgt planVer = sysVerMgtDao.findById(planVersion.getCid());
//            feeCalculationService.calcBuHcFee(abpOop, planVersion.getCid(), planVer.getLinkedVer().getCid());
//            feeCalculationService.calcAreaHcFee(abpOop, planVersion.getCid(), planVer.getLinkedVer().getCid());
            feeCalculationService.calculateSharedCosts(planVersion.getCid(), provId);
        }
    }

    @Override
    public CommonResult<Void> updateTypes(PlanFormulationUpdateReq req) {
        //1.提前立项,其他项目只能查看不能编辑
        List<AbpOop> abpOops = serviceManager.queryAbpOopByIdsAndTypeId(Collections.singletonList(req.getId()), null, Arrays.asList(Constant.BACKFILLING_PROJECT_ID,Constant.OTHER_PROJECTS_ID));
        if (!CollectionUtils.isEmpty(abpOops)) {
            return CommonResult.fail("项目编辑失败!提前立项只能查看不能编辑");
        }
        String contractTypeId = req.getContractTypeId();
        String prjTypeId = req.getPrjTypeId();
        String customBudgetTypeId = req.getCustomBudgetTypeId();
        String provId = req.getProvId();
        List<TeSysDef> sysDefs = serviceManager.queryProvAndTypes(contractTypeId, prjTypeId, customBudgetTypeId, provId);
        if (CollectionUtils.isEmpty(sysDefs)) {
            log.info("updateTypes - 查不到各类型数据");
            return CommonResult.fail("查不到各类型数据");
        }
        AbpOop abpOop = serviceManager.setAbpOopTypes(contractTypeId, prjTypeId, customBudgetTypeId, provId, sysDefs);
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.common__id, req.getId()));
        List<UpdataData> updates = new ArrayList<>();
        if (StringUtil.isNotNull(provId)) {
            updates.add(new UpdataData(DbFieldName.AbpOop.prov, abpOop.getProv()));
        }
        if (StringUtil.isNotNull(contractTypeId)) {
            updates.add(new UpdataData(DbFieldName.AbpOop.contractType, abpOop.getContractType()));
        }
        if (StringUtil.isNotNull(prjTypeId)) {
            updates.add(new UpdataData(DbFieldName.AbpOop.prjType, abpOop.getPrjType()));
        }
        if (StringUtil.isNotNull(customBudgetTypeId)) {
            updates.add(new UpdataData(DbFieldName.AbpOop.customBudgetType, abpOop.getCustomBudgetType()));
        }
        abpOopDao.updateByConds(conds, updates);
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<Map<String, Object>>> queryProgressByBpVerIdAndProvId(ObjectId planVerId, ObjectId provId, String buCode) {
        //查询省份的操作记录
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVerId));
        conds.add(new DC_E(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_cid), provId));
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(abpOprts)) {
            throw BusinessException.initExc("版本进度不存在");
        }
        AbpOprt abpOprt = abpOprts.get(0);
        List<StatusHist> statusHist = abpOprt.getStatusHist();
        List<Map<String, Object>> result = new ArrayList<>();
        int index = 1;
        //遍历省份的历史进度，获取进度和提交时间
//        if (!CollectionUtils.isEmpty(statusHist)) {
//            for (StatusHist hist : statusHist) {
//                TeIdNameCn histStatus = hist.getStatus();
//                if (histStatus == null) {
//                    continue;
//                }
//                if ("被打回".equals(histStatus.getName())) {
//                    index = 1;
//                    result.clear();
//                }
//                Map<String, Object> map = new HashMap<>();
//                map.put("step", index++);
//                map.put("submittedDate", DateUtil.formatDate2Str(hist.getOprtTime(), DateUtil.DATE_FORMAT));
//                result.add(map);
//            }
//        }
        //省份当前进度
        TeIdNameCn status = abpOprt.getStatus();
        String name = status.getName();
        //当前状态为被打回，之前的进度重置
        if ("被打回".equals(name)) {
            index = 1;
            result.clear();
        }
        //设置当前进度
        Map<String, Object> map = new HashMap<>();
        map.put("step", index++);
        map.put("submittedDate", DateUtil.formatDate2Str(abpOprt.getOprtTime(), DateUtil.DATE_FORMAT));
        result.add(map);
        if (result.size() == 1 && "已提交".equals(name)) {
            //省份进度已满,需要查询区域,大区,bu在哪个进度
            // 用cid 用名称可能会区域省份大区名称重复
            Map<String, ObjectId> regionMap = new HashMap<>();
            TeSysDef sysDef = sysDefDao.findById(provId);
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            ArrayList<String> nameList = new ArrayList<>();
            for (TeIdNameCn cndtItem : cndtItems) {
                regionMap.put(cndtItem.getCodeName(), cndtItem.getCid());
                nameList.add(cndtItem.getName());
            }
            //区域,大区,bu名称
            nameList.add(buCode);
            List<IDbCondition> conditions = new ArrayList<>();
            conditions.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVerId));
            conditions.add(new DC_I<>(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_name), nameList));
            List<AbpOprt> abpOprtList = abpOprtDao.findByFieldAndConds(conditions, null);
            if (!CollectionUtils.isEmpty(abpOprtList)) {
                Map<ObjectId, AbpOprt> abpOprtMap = new HashMap<>();
                for (AbpOprt oprt : abpOprtList) {
                    TeIdNameCn dept = oprt.getDept();
                    if (dept == null) {
                        continue;
                    }
                    abpOprtMap.put(dept.getCid(), oprt);
                }
                ObjectId abpRegion = regionMap.get("abpRegion");
                ObjectId abpBigRegion = regionMap.get("abpBigRegion");
                AbpOprt regionOprt = abpOprtMap.get(abpRegion);
                // 直接到区域审批 往下移
//                if (regionOprt != null) {
                    Map<String, Object> stepMapFirst = new HashMap<>();
                    stepMapFirst.put("step", index++);
                    stepMapFirst.put("submittedDate", DateUtil.formatDate2Str(regionOprt.getOprtTime(), DateUtil.DATE_FORMAT));
                    result.add(stepMapFirst);
//                }
                AbpOprt bigRegionOprt = abpOprtMap.get(abpBigRegion);
                if (regionOprt != null && regionOprt.getStatus() != null && "已提交".equals(regionOprt.getStatus().getName())) {
                    Map<String, Object> stepMap = new HashMap<>();
                    stepMap.put("step", index++);
                    stepMap.put("submittedDate", DateUtil.formatDate2Str(bigRegionOprt.getOprtTime(), DateUtil.DATE_FORMAT));
                    result.add(stepMap);
                }
                AbpOprt buOprt =
                        abpOprtMap.get(buCode);
                if (bigRegionOprt != null && bigRegionOprt.getStatus() != null && "已提交".equals(bigRegionOprt.getStatus().getName())) {
                    Map<String, Object> stepMap = new HashMap<>();
                    stepMap.put("step", index);
                    stepMap.put("submittedDate", DateUtil.formatDate2Str(buOprt.getOprtTime(), DateUtil.DATE_FORMAT));
                    result.add(stepMap);
                }
            }
        }
        Map<String, Object> startMap = new HashMap<>();
        startMap.put("step", 0);
        startMap.put("submittedDate", null);
        result.add(0, startMap);
        return CommonResult.success(result);
    }

    @Override
    public CommonResult<Void> submitProvBpVer(ObjectId planVerId, ObjectId provId, String submitDesc, TeSysUser user) throws IOException, ClassNotFoundException {
        //1.判断当前省份的当前版本是否处于：计划编制中或者被打回状态，如果是则后续提交动作，反之失败
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVerId));
        conds.add(new DC_E(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_cid), provId));
        List<IDbCondition> orConditions = new ArrayList<>();
        //or查询
        orConditions.add(new DC_E(DbFieldName.AbpOprt.status.dot(DbFieldName.common_cid), Constant.ABP_OPRT_PLANNING_IN_PROGRESS_ID));
        orConditions.add(new DC_E(DbFieldName.AbpOprt.status.dot(DbFieldName.common_cid), Constant.ABP_OPRT_BEATEN_BACK_ID));
        conds.add(new DC_OR(orConditions));
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, null);
        if (CollectionUtils.isEmpty(abpOprts)) {
            return CommonResult.fail("提交的项目计划记录不存在或者当前省份不处于待提交状态");
        }
        AbpOprt abpOprt = abpOprts.get(0);
        //abpOop中数据有效的数据（isValid为true，isDeleted为false）锁定的数据（isLocked为true）
        List<AbpOop> abpOops = abpOopDao.queryDataByPlanVerIdAndProvId(planVerId,provId);
        List<ObjectId> ids = new ArrayList<>();
        StringBuilder error = new StringBuilder();
        //提交省份计划时，批量校验该省份下所有有效且未删除的且未锁定的项目的全周期，校验通过的没锁定的系统自动将该条经营计划锁定，校验不通过的则提示：“***，***，***经营计划全周期不能为空”
        for (AbpOop abpOop : abpOops) {
            //锁定的数据不需要校验全周期
            if (abpOop.getIsLocked()){
                continue;
            }
            // 4.23 -- 省份提交时不再校验全周期
            //校验4中类型的全周期：当前预测，未来预测，当前签约和历史签约,校验完成后自动锁定  新增分拆项目也做全周期
//            ObjectId typeId = abpOop.getType().getCid();
//            if (Constant.FORECAST_ORDER_ID.equals(typeId) || Constant.FUTURE_FORECAST_ORDER_ID.equals(typeId)
//                    ||Constant.SIGNED_CURRENT_YEAR_ID.equals(typeId) || Constant.HISTORICAL_SIGNING_ID.equals(typeId)
//                    || Constant.SPIN_OFF_PROJECT_ID.equals(typeId)){
//                boolean fullPeriodDataNullFlag = abpOop.getSumEngineerRg() == null && abpOop.getSumRadRg() == null
//                        && abpOop.getSumOsempRgFee() == null && abpOop.getSumDirectFee() == null
//                        && abpOop.getSumStaffOutsourceFee() == null && abpOop.getSumTechSubcontractFee() == null;
//                if (fullPeriodDataNullFlag) {
//                    error.append(String.format("经营计划【%s】全周期不能为空",abpOop.getCodeName()));
//                    continue;
//                }
//            }
            //其他类型的数据，自动锁定
            ids.add(abpOop.getId());
        }
        if (!CollectionUtils.isEmpty(ids)){
            //锁定全周期校验通过的数据
            abpOopDao.updateDataLockedByIds(ids);
        }
        if (StringUtil.isNotNull(error)){
            throw new BaseException(error.toString());
        }
        String lockName = "repulsePlanByBpVerIdAndGradeList" + provId;
        RLock lock = redissionClient.getLock(lockName);
        boolean flag;
        try {
            flag = lock.tryLock(0L, 1800L, TimeUnit.SECONDS);
            if (flag) {
                //更新进度
                AbpOprt copy = CommonUtil.deepCopy(abpOprt);
                TeIdNameCn oprt = new TeIdNameCn();
                oprt.setCid(Constant.ABP_OPRT_SUBMIT_ID);
                oprt.setName(Constant.ABP_OPRT_SUBMIT_NAME);
                copy.setOprt(oprt);
                TeIdNameCn status = new TeIdNameCn();
                status.setCid(Constant.ABP_OPRT_SUBMITTED_ID);
                status.setName(Constant.ABP_OPRT_SUBMITTED_NAME);
                copy.setStatus(status);
                copy.setDesc(submitDesc);
                TeUser teUser = getTeUser(user);
                copy.setOprtUser(teUser);
                copy.setOprtTime(new Date());
                StatusHist statusHist = new StatusHist();
                statusHist.setStatus(abpOprt.getStatus());
                statusHist.setDesc(abpOprt.getDesc());
                statusHist.setOprtUser(abpOprt.getOprtUser());
                statusHist.setOprtTime(abpOprt.getOprtTime());
                copy.setStatusHist(Collections.singletonList(statusHist));
                abpOprtDao.save(copy);

//                SysVerMgt planVer = sysVerMgtDao.findById(planVerId);
//                feeCalculationService.calcBuHcFee(provId, planVerId, planVer.getLinkedVer().getCid());
//                feeCalculationService.calcAreaHcFee(provId, planVerId, planVer.getLinkedVer().getCid());
                feeCalculationService.calculateSharedCosts(planVerId, provId);
            } else {
                log.warn("======> submitProvBpVer - 未获取到锁！ ");
                return CommonResult.fail("提交省份失败!该省份正在提交中,请稍后重试!");
            }
        } catch (InterruptedException e) {
            log.warn("======> submitProvBpVer - 获取锁失败！ ", e);
            return CommonResult.fail("提交省份失败!请稍后重试!");
        } finally {
            if (lock!=null&&lock.isLocked()&&lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success();
    }

    @Override
    public void addPrimSecRel(PrimSecRelReq req ,TeSysUser user) {
        ObjectId planVerId = req.getPlanVerId();
        if (planVerId==null){
            throw BusinessException.initExc("版本不能为空");
        }
        //1.查询辅项目信息
        List<AbpOop> abpOops = serviceManager.queryAbpOopByIdsAndTypeId(req.getSecIds(),planVerId, Arrays.asList(Constant.BACKFILLING_PROJECT_ID,Constant.SPIN_OFF_PROJECT_ID,Constant.HIST_SPLIT_PRJ_ID));
        if (CollectionUtils.isEmpty(abpOops) || abpOops.size() != req.getSecIds().size()) {
            throw BusinessException.initExc("辅项目清单类型必须为有效的提前立项或者分拆项目");
        }
        //2.判断主项目类型
        List<AbpOop> abpOopList = serviceManager.queryAbpOopByIdsAndTypeId(Collections.singletonList(req.getPrimId()),planVerId, Arrays.asList(Constant.FORECAST_ORDER_ID,Constant.FUTURE_FORECAST_ORDER_ID, Constant.SIGNED_CURRENT_YEAR_ID,Constant.HIS_RESIGN_PRJ_ID, Constant.HISTORICAL_SIGNING_ID));
        if (CollectionUtils.isEmpty(abpOopList)) {
            throw BusinessException.initExc("主项目清单类型必须为有效的预测订单、当年签约、历史签约,历史重签");
        }
        //查询辅项目是否已经被挂接,被挂接的话,不能再次进行挂接
        List<AbpOop> list = abpOopDao.queryDataBySubOopCid(req.getSecIds());
        if (!CollectionUtils.isEmpty(list)){
            List<String> codeNames = list.stream().map(AbpOop::getCodeName).collect(Collectors.toList());
            throw BusinessException.initExc("辅项目已挂接主项目:"+codeNames);
        }
        AbpOop mainPrj = abpOopList.get(0);
        TeIdNameCn prov = mainPrj.getProv();
        List<SubOop> subOopList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        StringBuilder desc = new StringBuilder();
        for (AbpOop abpOop : abpOops) {
            //主项目和辅项目的省份必须一致
            TeIdNameCn secProv = abpOop.getProv();
            if (secProv==null||!prov.getCid().equals(secProv.getCid())){
                throw BusinessException.initExc("主项目和辅项目的省份必须一致");
            }
            if (map.containsKey(abpOop.getName())){
                throw BusinessException.initExc(String.format("项目【%s】不能重复挂接到同一个主项目上",abpOop.getName()));
            }
            desc.append(String.format("%s,%s被挂接",abpOop.getCodeName(),abpOop.getId()));
            map.put(abpOop.getName(),abpOop.getName());
            SubOop subOop = new SubOop();
            TeIdNameCn subOopOop = new TeIdNameCn();
            subOopOop.setCid(abpOop.getId());
            subOopOop.setName(abpOop.getName());
            subOopOop.setCodeName(abpOop.getCodeName());
            subOop.setOop(subOopOop);
            subOop.setIsPlanPrj(true);
            if(abpOop.getType()!=null&&Constant.BACKFILLING_PROJECT_ID.equals(abpOop.getType().getCid())){
                //如果挂接的项目是提前立项，该属性为false
                subOop.setIsPlanPrj(false);
            }
            subOopList.add(subOop);
        }
        List<OperationInfo> oprtInfo = mainPrj.getOprtInfo();
        if (CollectionUtils.isEmpty(oprtInfo)){
            oprtInfo=new ArrayList<>();
        }
        OperationInfo info = new OperationInfo();
        info.setOprtType("手工挂接");
        info.setOprtDesc(desc.toString());
        info.setOprtTime(new Date());
        TeUser addUser = new TeUser();
        addUser.setUserId(user.getId());
        addUser.setUserName(user.getUserName());
        addUser.setLoginName(user.getLoginName());
        addUser.setJobCode(user.getJobCode());
        info.setOprtUser(addUser);
        oprtInfo.add(info);
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.common__id, mainPrj.getId()));

        List<SubOop> subOops = mainPrj.getSubOops();
        if (CollectionUtils.isEmpty(subOops)){
            subOops=new ArrayList<>();
        }
        subOops.addAll(subOopList);

        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DbFieldName.AbpOop.subOops, subOops));
        updates.add(new UpdataData(DbFieldName.AbpOop.oprtInfo, oprtInfo));
        abpOopDao.updateByConds(conds, updates);
    }

    @Override
    public CommonResult<List<AbpOop>> queryPrimOrSecProject(QueryPrimOrSecReq req) {
        if (req.getIsPrim()){
            //查询主项目
            return CommonResult.success(this.queryAbpOopPrimOrSec(req, Arrays.asList(Constant.FORECAST_ORDER_ID,Constant.FUTURE_FORECAST_ORDER_ID, Constant.SIGNED_CURRENT_YEAR_ID,Constant.HIS_RESIGN_PRJ_ID, Constant.HISTORICAL_SIGNING_ID)));
        }
        //查询辅项目
        List<AbpOop> abpOops = this.queryAbpOopPrimOrSec(req, Arrays.asList(Constant.BACKFILLING_PROJECT_ID,Constant.SPIN_OFF_PROJECT_ID,Constant.HIST_SPLIT_PRJ_ID));
        if (CollectionUtils.isEmpty(abpOops)){
            return CommonResult.success(new ArrayList<>());
        }
        Map<ObjectId, List<AbpOop>> map = abpOops.stream().collect(Collectors.groupingBy(AbpOop::getId));
        List<AbpOop> primAbpOops =this.queryAbpOopPrimOrSec(req, Arrays.asList(Constant.FORECAST_ORDER_ID,Constant.FUTURE_FORECAST_ORDER_ID, Constant.SIGNED_CURRENT_YEAR_ID,Constant.HIS_RESIGN_PRJ_ID,Constant.HISTORICAL_SIGNING_ID)) ;
        for (AbpOop abpOop : primAbpOops) {
            List<SubOop> subOops = abpOop.getSubOops();
            if (CollectionUtils.isEmpty(subOops)){
                continue;
            }
            for (SubOop subOop : subOops) {
                TeIdNameCn subOopOop = subOop.getOop();
                if (subOopOop!=null){
                    List<AbpOop> abpOopList = map.get(subOopOop.getCid());
                    //辅项目不能在主项目下面挂着
                    if (!CollectionUtils.isEmpty(abpOopList)){
                        map.remove(subOopOop.getCid());
                    }
                }

            }
        }
        List<AbpOop> oops = new ArrayList<>();
        for (Map.Entry<ObjectId, List<AbpOop>> en : map.entrySet()) {
            oops.addAll(en.getValue());
        }
        return CommonResult.success(oops);
    }
    public List<AbpOop> queryAbpOopPrimOrSec(QueryPrimOrSecReq req, List<ObjectId> typeIds) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.common_isValid,true));
        conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid),req.getPlanVerId()));
        conds.add(new DC_I<>(DbFieldName.AbpOop.type.dot(DbFieldName.common_cid), typeIds));
        //模糊查询条件
        String queryCond = req.getQueryCond();
        if (StringUtil.isNotNull(queryCond)){
            List<IDbCondition> or = new ArrayList<>();
            or.add(new DC_L(DbFieldName.common_name,queryCond,true));
            or.add(new DC_L(DbFieldName.common_cn,queryCond,true));
            or.add(new DC_L(DbFieldName.AbpOop.type.dot(DbFieldName.common_name),queryCond,true));
            conds.add(new DC_OR(or));
        }
        return abpOopDao.findByFieldAndConds(conds, null);
    }


    @Override
    public void deleteSecProject(PrimSecRelReq req, TeSysUser user) {
        //查询主项目
        AbpOop mainPrj = abpOopDao.findById(req.getPrimId());
        if (mainPrj==null){
            throw BusinessException.initExc("主项目不存在，请重新操作");
        }
        List<SubOop> subOops = mainPrj.getSubOops();
        if (CollectionUtils.isEmpty(subOops)){
            return;
        }
        List<SubOop> list = new ArrayList<>();
        List<ObjectId> secIds = req.getSecIds();
        StringBuilder desc = new StringBuilder();
        for (SubOop subOop : subOops) {
            ObjectId secId = subOop.getOop().getCid();
            if (secIds.contains(secId)){
                desc.append(String.format("%s,%s被解绑",subOop.getOop().getCodeName(),subOop.getOop().getCid()));
                continue;
            }
            list.add(subOop);
        }
        if (StringUtil.isNull(desc)){
            return;
        }
        List<OperationInfo> oprtInfo = mainPrj.getOprtInfo();
        if (CollectionUtils.isEmpty(oprtInfo)){
            oprtInfo=new ArrayList<>();
        }
        OperationInfo info = new OperationInfo();
        info.setOprtDesc(desc.toString());
        info.setOprtType("解绑项目");
        TeUser addUser = new TeUser();
        addUser.setUserId(user.getId());
        addUser.setUserName(user.getUserName());
        addUser.setLoginName(user.getLoginName());
        addUser.setJobCode(user.getJobCode());
        info.setOprtUser(addUser);
        info.setOprtTime(new Date());
        oprtInfo.add(info);
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.common__id,mainPrj.getId()));
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DbFieldName.AbpOop.subOops,list));
        updates.add(new UpdataData(DbFieldName.AbpOop.oprtInfo,oprtInfo));
        abpOopDao.updateByConds(conds,updates);
    }

    @Override
    public CommonResult<List<AbpOop>> queryPlanTransferIn(String queryCond, String provId, String planVerIdStr, TeSysUser user) {
        //1.判断当前登录人是否是区域及以上地区的组织管理员
        List<TeSysDef> sysDefs = serviceManager.queryProvWithAdmin(user);
        if (CollectionUtils.isEmpty(sysDefs)){
            throw BusinessException.initExc("您没有区域以上管理员权限,请先授权在操作!");
        }
        //2.当前省份外的其他省份信息
        List<ObjectId> provIds = sysDefs.stream().map(TeSysDef::getId).filter(id -> !id.equals(new ObjectId(provId))).collect(Collectors.toList());
        //3.查询计划编制中的数据
        ObjectId planVerId = new ObjectId(planVerIdStr);
        List<AbpOprt>abpOprts=abpOprtDao.queryDataByPlanVerId(planVerId);
        List<ObjectId> ids = new ArrayList<>();
        for (AbpOprt abpOprt : abpOprts) {
            TeIdNameCn dept = abpOprt.getDept();
            if (dept==null){
                continue;
            }
            if (provIds.contains(dept.getCid())){
                //当前登录人除当前省份外的,区域及以上组织级管理员下的省份，并且处于计划编制中的省份
                ids.add(dept.getCid());
            }
        }
        //4.查询当前省份外的未提交的省份计划
        List<AbpOop>abpOops=abpOopDao.queryDataByPlanVerIdAndProvIds(planVerId, ids, queryCond);
        return CommonResult.success(abpOops);
    }

    @Override
	public CommonResult<Void> planTransfer(String idStr, String provIdStr, TeSysUser user) {
		// 1.判断当前登录人是否是区域及以上地区的组织管理员
		List<TeSysDef> sysDefs = serviceManager.queryProvWithAdmin(user);
		if (CollectionUtils.isEmpty(sysDefs)) {
			return CommonResult.fail("您没有区域以上管理员权限,请先授权在操作!");
		}
		// 2.查询需要转入的数据的省份信息
		ObjectId id = new ObjectId(idStr);
		ObjectId provId = new ObjectId(provIdStr);
		List<AbpOop> abpOops = abpOopDao.queryDataById(id);
		if (CollectionUtils.isEmpty(abpOops)) {
			return CommonResult.fail("需转入的数据未查到,请联系管理员!");
		}
		AbpOop abpOop = abpOops.get(0);
		// 3.校验当前省份和需转入的数据的省份是否是自己管理员权限下的省份
		List<ObjectId> provIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
		if (!provIds.contains(provId) || !provIds.contains(abpOop.getProv().getCid())) {
			return CommonResult.fail("您没有该省份和需转入省份对应区域的管理员权限,请先授权在操作!");
		}
		// 4.校验省份是否在计划编制中或者是被打回
		List<AbpOprt> abpOprts = abpOprtDao.queryDataByPlanVerIdAndProvId(abpOop.getPlanVer().getCid(),
				Arrays.asList(abpOop.getProv().getCid(), provId));
		if (CollectionUtils.isEmpty(abpOprts) || abpOprts.size() != 2) {
			return CommonResult.fail("当前计划转移数据或目标省份状态不在计划编制中,不可操作!");
		}
		ObjectId outProvId = abpOop.getProv() == null ? null : abpOop.getProv().getCid();

		// 6.查询省份信息
		TeSysDef sysDef = sysDefDao.findById(provId);
		TeIdNameCn prov = new TeIdNameCn();
		prov.setCid(sysDef.getId());
		prov.setName(sysDef.getDefName());
		prov.setCodeName(sysDef.getCodeName());
		OperationInfo info = new OperationInfo();
		info.setOprtType("项目信息更新-计划转入转出");
		info.setOprtDesc(String.format("prov从%s更新为%s",abpOop.getProv().getName(),prov.getName()));
		TeUser oprtUser = new TeUser();
		oprtUser.setUserId(user.getId());
		oprtUser.setUserName(user.getUserName());
		oprtUser.setJobCode(user.getJobCode());
		oprtUser.setLoginName(user.getLoginName());
		info.setOprtUser(oprtUser);
		info.setOprtTime(new Date());
		List<OperationInfo> oprtInfo = abpOop.getOprtInfo();
		if (CollectionUtils.isEmpty(oprtInfo)) {
			oprtInfo = new ArrayList<>();
		}
		oprtInfo.add(info);
        TeIdNameCn type = abpOop.getType();
        List<SubOop> subOops = abpOop.getSubOops();
        List<ObjectId> ids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(subOops)){
            //该项目为主项目，转移转出时需要把对应的辅项目一起转移到此省份下
            for (SubOop subOop : subOops) {
                TeIdNameCn subOopOop = subOop.getOop();
                if (subOopOop!=null&&subOopOop.getCid()!=null){
                    ids.add(subOopOop.getCid());
                }
            }
        }else {
            if (type!=null&&(type.getCid().equals(Constant.BACKFILLING_PROJECT_ID)
                    || type.getCid().equals(Constant.SPIN_OFF_PROJECT_ID)
                    || type.getCid().equals(Constant.HIST_SPLIT_PRJ_ID))) {
                //该项目为辅项目,辅项目转移的时候，需要把对应的主项目和该主项目下的其他辅项目一同转移
                //查询主项目
                List<AbpOop> oops = abpOopDao.queryDataBySubOopCid(Collections.singletonList(abpOop.getId()));
                if (!CollectionUtils.isEmpty(oops)){
                    AbpOop mainOop = oops.get(0);
                    ids.add(mainOop.getId());
                    for (SubOop subOop : mainOop.getSubOops()) {
                        TeIdNameCn subOopOop = subOop.getOop();
                        if (subOopOop!=null&&subOopOop.getCid()!=null&&!subOopOop.getCid().equals(abpOop.getId())){
                            ids.add(subOopOop.getCid());
                        }
                    }
                }
            }
        }
        ids.add(id);
        abpOopDao.updateProvById(ids, prov, oprtInfo);

		// 转入和转出省份都需要重新计算销售成本
		// 当年预测和当年签约，需要重新计算销售成本
		ObjectId planVerId = abpOop.getPlanVer() == null ? null : abpOop.getPlanVer().getCid();
//		if ((Constant.FORECAST_ORDER_ID.equals(type.getCid()) || Constant.SIGNED_CURRENT_YEAR_ID.equals(type.getCid())||Constant.HIS_RESIGN_PRJ_ID.equals(type.getCid()))
//				&& outProvId != null && planVerId != null) {
//			SysVerMgt planVer = sysVerMgtDao.findById(planVerId);
//			if (planVer != null) {
//				ObjectId linkedVerId = planVer.getLinkedVer() == null ? null : planVer.getLinkedVer().getCid();
//				if (linkedVerId != null) {
//					feeCalculationService.calcSalesFee(outProvId, planVerId, linkedVerId);
//					feeCalculationService.calcSalesFee(provId, planVerId, linkedVerId);
//				}
//			}
//		}
        if(planVerId!=null){
            feeCalculationService.calculateSharedCosts(planVerId, outProvId);
            feeCalculationService.calculateSharedCosts(planVerId, provId);
        }
		return CommonResult.success();
	}

    @Override
    public CommonResult<List<QueryPlanTransferOutProvResp>> queryPlanTransferOutProv(String planVerIdStr, TeSysUser user) {
        //1.判断当前登录人是否是区域及以上地区的组织管理员
        List<TeSysDef> sysDefs = serviceManager.queryProvWithAdmin(user);
        if (CollectionUtils.isEmpty(sysDefs)){
            throw BusinessException.initExc("您没有区域以上管理员权限,请先授权在操作!");
        }
        ObjectId planVerId;
        if (StringUtil.isNull(planVerIdStr)){
            List<PlanVersion> planVersions = serviceManager.findPlanVerId(user.getSbuId(), Constant.ABP_BUSINESS_PLAN_VERSION_ID);
            if (CollectionUtils.isEmpty(planVersions)){
                throw BusinessException.initExc("最新版本信息查不到,请联系管理员!");
            }
            planVerId = planVersions.get(0).getCid();
        }else {
            planVerId=new ObjectId(planVerIdStr);
        }
        //2.查询该版本下的计划编制中省份信息
        List<AbpOprt> abpOprts = abpOprtDao.queryDataByPlanVerId(planVerId);
        List<ObjectId> provIds =new ArrayList<>();
        if (!CollectionUtils.isEmpty(abpOprts)){
            provIds = abpOprts.stream().map(s -> s.getDept().getCid()).collect(Collectors.toList());
        }
        //3.查询省份信息
        List<QueryPlanTransferOutProvResp> list = new ArrayList<>();
        for (TeSysDef sysDef : sysDefs) {
            QueryPlanTransferOutProvResp resp = new QueryPlanTransferOutProvResp();
            resp.setId(sysDef.getId());
            resp.setName(sysDef.getDefName());
            resp.setValid(false);
            if (provIds.contains(sysDef.getId())){
                resp.setValid(true);
            }
            list.add(resp);
        }
        return CommonResult.success(list);
    }

    private TeUser getTeUser(TeSysUser user) {
        TeUser teUser = new TeUser();
        teUser.setUserId(user.getId());
        teUser.setUserName(user.getUserName());
        teUser.setLoginName(user.getLoginName());
        teUser.setJobCode(user.getJobCode());
        return teUser;
    }

    // TODO
    private void calcCostSharing(ObjectId planVerId, TeSysUser user, List<AbpOop> abpOops, List<ObjectId> provIds) throws IOException, ClassNotFoundException {
        if (CollectionUtils.isEmpty(abpOops)) {
            return;
        }
        //1.删除版本省份下关联的订项的分摊费用记录
        //1.1.查询对应的abpOop，然后确定对应的abpOopBgt并删除
        List<ObjectId> abpOopIds = abpOops.stream().map(AbpOop::getId).collect(Collectors.toList());
        List<IDbCondition> abpOopBgtCondition = new ArrayList<>();
        abpOopBgtCondition.add(new DC_I<>(DbFieldName.AbpOopBgt.oop.dot(DbFieldName.common_cid), abpOopIds));
        abpOopBgtCondition.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVerId));
        //查出并且删除
        List<AbpOopBgt> abpOopBgts = abpOopBgtDao.findAllAndRemove(abpOopBgtCondition);
        for (AbpOopBgt abpOopBgt : abpOopBgts) {
            //把id置为null，需要重新插入
            abpOopBgt.setId(null);
        }
        //查询出版本待计算的分摊费用数据(AbpMfeeCnfg)
        List<IDbCondition> abpMfeeCnfgConds = new ArrayList<>();
        //取isValid为true下的且buCode为该BU下的且mfeeType.cid为该管理类型下的year为该年度下的且ym为该年月下的cnfgItem.cid为该配置项如省份等下的value
        abpMfeeCnfgConds.add(new DC_E(DbFieldName.common_isValid, true));
        abpMfeeCnfgConds.add(new DC_E(DbFieldName.AbpMfeeCnfg.buCode, user.getSbuId()));
        abpMfeeCnfgConds.add(new DC_I<>(DbFieldName.AbpMfeeCnfg.mfeeType.dot(DbFieldName.common_cid), Arrays.asList(Constant.BU_HC_FEE, Constant.AREA_HC_FEE, Constant.SALES_FEE)));
        abpMfeeCnfgConds.add(new DC_E(DbFieldName.AbpMfeeCnfg.year, String.valueOf(DateUtil.getYear(new Date()))));
        abpMfeeCnfgConds.add(new DC_E(DbFieldName.AbpMfeeCnfg.ym, DateUtil.getCurrentTime(DateUtil.DATE_MONTH_FOTMAT)));
        abpMfeeCnfgConds.add(new DC_I<>(DbFieldName.AbpMfeeCnfg.cnfgItem.dot(DbFieldName.common_cid), provIds));
        List<AbpMfeeCnfg> abpMfeeCnfgs = abpMfeeCnfgDao.findByFieldAndConds(abpMfeeCnfgConds, null);
        if (CollectionUtils.isEmpty(abpMfeeCnfgs)) {
            return;
        }
        Map<ObjectId, Map<ObjectId, List<AbpOopBgt>>> map = abpOopBgts.stream().collect(Collectors.groupingBy(s -> s.getOop().getCid(), Collectors.groupingBy(s -> s.getPl().getCid())));
        List<AbpOopBgt> abpOopBgtList = new ArrayList<>();
        for (AbpMfeeCnfg abpMfeeCnfg : abpMfeeCnfgs) {
            TeIdNameCn mfeeType = abpMfeeCnfg.getMfeeType();
            ObjectId cid = mfeeType.getCid();
            Mfee mfee = new Mfee();
            mfee.setType(mfeeType);
            Double value = abpMfeeCnfg.getValue();
            if (cid.equals(Constant.BU_HC_FEE) || cid.equals(Constant.AREA_HC_FEE)) {
                //1.BU HC管理费率,区域HC管理费率
                mfee.setDesc("");
                mfee.setRate(value);
                //克隆
                List<AbpOopBgt> oopBgtList = ListUtil.deepCopy(abpOopBgts);
                for (AbpOopBgt abpOopBgt : oopBgtList) {
                    mfee.setAmt(abpOopBgt.getRgNum() / 12 * value);
//                    abpOopBgt.setMfee(mfee);
                }
                abpOopBgtList.addAll(oopBgtList);
            } else if (cid.equals(Constant.SALES_FEE)) {
                //2.销售费用
                String buCode = user.getSbuId();
                if (!buCode.equals(Constant.BU_CODE_CMC) && !buCode.equals(Constant.BU_CODE_DNC)) {
                    continue;
                }
                TeSysDef sysDef = sysDefDao.findById(abpMfeeCnfg.getCnfgItem().getCid());
                List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
                String bigRegionName = "";

                for (TeIdNameCn cndtItem : cndtItems) {
                    if (Constant.BIG_REGION.equals(cndtItem.getCodeName())) {
                        bigRegionName = cndtItem.getName();
                        break;
                    }
                }
                boolean flag = DNC_BIG_REGION.contains(bigRegionName);
                double yjProportion = 0.0177;
                // DNC整体三费占比
                double sfProportion = 0.0279;
                if (buCode.equals(Constant.BU_CODE_CMC)) {
                    yjProportion = 0.0166;
                    sfProportion = 0.0165;
                    flag = CMC_BIG_REGION.contains(bigRegionName);
                }
                for (AbpOop abpOop : abpOops) {
                    ObjectId oopId = abpOop.getId();
                    Map<ObjectId, List<AbpOopBgt>> abpOopBgtMap = map.get(oopId);
                    List<AbpOopPl> pls = abpOop.getPls();
                    if (CollectionUtils.isEmpty(pls) || DateUtil.getYear(abpOop.getSignDate()) != DateUtil.getYear(new Date())) {
                        continue;
                    }
                    for (AbpOopPl pl : pls) {
                        TeIdNameCn newPl = pl.getPl();
                        boolean cmcFlag = buCode.equals(Constant.BU_CODE_CMC) && (newPl == null || CMC_PL_LIST.contains(newPl.getName()));
                        boolean dncFlag = buCode.equals(Constant.BU_CODE_DNC) && (newPl == null || DNC_PL_LIST.contains(newPl.getName()));
                        if (cmcFlag || dncFlag) {
                            continue;
                        }
                        ObjectId plId = newPl.getCid();
                        Double pct = pl.getPct();
                        Double netSalesM = abpOop.getNetSalesM();
                        double plShareMoney = pct * netSalesM;
                        double amt = value;
                        if (plShareMoney == 0) {
                            amt = 0d;
                        }
                        String desc = String.format("((%s*%s*%s)/%s)", netSalesM, pct, value, plShareMoney);
                        amt += netSalesM * pct * yjProportion;
                        desc += String.format("+((%s*%s*%s))", netSalesM, pct, yjProportion * 100 + "%");
                        //省份需要满足条件
                        if (flag) {
                            amt += netSalesM * pct * sfProportion;
                            desc += String.format("+((%s*%s*%s))", netSalesM, pct, sfProportion * 100 + "%");
                        }
                        String descStr = String.format("【公式 ：((( 订单额 * 产品线占比 * 分摊金额 ) / 省份订单金额) + ( 订单额 * 产品线占比 *  %s ) + ( 订单额 * 产品线占比 * %s ))】", yjProportion * 100, sfProportion * 100);
                        desc += descStr;
                        mfee.setAmt(amt);
                        mfee.setDesc(desc);
                        List<AbpOopBgt> oopBgts = abpOopBgtMap.get(plId);
                        for (AbpOopBgt oopBgt : oopBgts) {
//                            oopBgt.setMfee(mfee);
                            abpOopBgtList.add(oopBgt);
                        }
                    }
                }

            }
        }
        abpOopBgtDao.batchSave(abpOopBgtList);
    }

    @Override
    public List<TeSysDef> queryGradeTreeByGradeAndPlPower(TeSysUser user, List<ObjectId> resDeptOrPlId,Boolean isPms) {
        //查询当前登陆人角色
        List<ObjectId> roleUserIds = new ArrayList<>();
        roleUserIds.add(user.getId());
        //abp管理员
        List<TeSysDefRoleUser> abpBuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), Constant.ABP_ADMIN_ID, roleUserIds, null);
        //bu预算管理员
        List<TeSysDefRoleUser> BuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);
        //省份运营管理员
        List<TeSysDefRoleUser> provOperateAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.ABP_PROV.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);

        if (BooleanUtils.isTrue(isPms) && CollectionUtils.isEmpty(abpBuAdmins) && CollectionUtils.isEmpty(BuAdmins)){
            if (CollectionUtils.isEmpty(provOperateAdmins)){
                return new ArrayList<>();
            }
            List<ObjectId> provIds = provOperateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(provIds)){
                return new ArrayList<>();
            }
            List<TeSysDef> provs = sysDefService.getTeSysDefsByIds(provIds);
            if (CollectionUtils.isEmpty(provs)){
                return new ArrayList<>();
            }
            return response(provs);
        }
        //resDeptOrPlId为null的时候，代表只查询当前用户的有权限的省份
        if (CollectionUtils.isEmpty(resDeptOrPlId)){
            //查询出所有有权限的省份
            List<TeSysDef> provs = serviceManager.queryProv(user);
            if (CollectionUtils.isEmpty(provs)){
                return new ArrayList<>();
            }
            return response(provs);
        }
        List<TeSysDef> provs = sysDefDao.getSysDefByDefTypeAndSrcDef(Constant.ABP_PROV_CODE_NAME, user.getSbuId());
        return response(provs);
    }

    private List<TeSysDef> response(List<TeSysDef> provs) {
        List<ObjectId> regionIds = new ArrayList<>();
        for (TeSysDef sysDef : provs) {
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            if (CollectionUtils.isEmpty(cndtItems)){
                continue;
            }
            for (TeIdNameCn cndtItem : cndtItems) {
                regionIds.add(cndtItem.getCid());
            }
        }
        if (CollectionUtils.isEmpty(regionIds)){
            return provs;
        }
        //大区和区域数据
        List<TeSysDef> regions = sysDefDao.getSysDefsByIds(regionIds);
        return sort(provs, regions);
    }

    private List<TeSysDef> sort(List<TeSysDef> provs,List<TeSysDef> regions){
        //各层级区域信息按照defNo排序
        if (CollectionUtils.isEmpty(regions)){
            return provs;
        }
        Map<ObjectId, Integer> map = new HashMap<>();
        for (TeSysDef region : regions) {
            TeDefType defType = region.getDefType();
            if (defType==null){
                continue;
            }
            map.put(region.getId(),region.getDefNo());
        }
        provs.sort(Comparator.comparing((Function<TeSysDef, Object>) teSysDef -> {
            List<TeIdNameCn> cndtItems = teSysDef.getCndtItems();
            String bigRegion="";
            String region="";
            String prov= String.valueOf(teSysDef.getDefNo());
            for (TeIdNameCn cndtItem : cndtItems) {
                if (cndtItem.getCodeName().equals(Constant.BIG_REGION)){
                    bigRegion= String.valueOf(map.get(cndtItem.getCid()));
                }
                if (cndtItem.getCodeName().equals(Constant.REGION)){
                    region= String.valueOf(map.get(cndtItem.getCid()));
                }
            }
            return String.join("/",bigRegion,region,prov);
        }, (o1, o2) -> {
            String[] split1 = ((String) o1).split("/");
            String[] split2 = ((String) o2).split("/");
            if (!Objects.equals(split1[0], split2[0])){
                return split1[0].compareTo(split2[0]);
            }else if (!Objects.equals(split1[1], split2[1])){
                return split1[1].compareTo(split2[1]);
            }
            return split1[2].compareTo(split2[2]);
        }));
        return provs;
    }


    @Override
    public CommonResult<List<QueryIncomeForecProvResp>> queryNeedForecastProvListByBpVerId(TeSysUser user, ObjectId planVersion) {
        //1.查询当前用户大区，区域对应的管理员角色
        List<TeSysDef> sysDefs = serviceManager.queryProvWithPermis(user);
        if (CollectionUtils.isEmpty(sysDefs)) {
            throw BusinessException.initExc("该用户没有省份权限");
        }
        //区域map：键区域名称，值区域id
        HashMap<String, ObjectId> areaMap = new HashMap<>();
        Map<ObjectId, List<TeSysDef>> map = new HashMap<>();
        List<ObjectId> provIds = new ArrayList<>();
        //省份数据遍历
        for (TeSysDef sysDef : sysDefs) {
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            if (CollectionUtils.isEmpty(cndtItems)) {
                continue;
            }
            provIds.add(sysDef.getId());
            for (TeIdNameCn cndtItem : cndtItems) {
                if (Constant.REGION.equals(cndtItem.getCodeName())) {
                    areaMap.put(cndtItem.getName(), cndtItem.getCid());
                    map.computeIfAbsent(cndtItem.getCid(), s -> new ArrayList<>()).add(sysDef);
                }
            }

        }
        //3.查询当前版本abpOprt数据
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVersion));
        conditions.add(new DC_I<>(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_cid), provIds));
        List<DbFieldName> fields = new ArrayList<>();
        fields.add(DbFieldName.AbpOprt.status);
        fields.add(DbFieldName.AbpOprt.dept);
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conditions, fields);
        HashMap<ObjectId, AbpOprt> abpOprtMap = new HashMap<>();
        for (AbpOprt abpOprt : abpOprts) {
            TeIdNameCn dept = abpOprt.getDept();
            if (dept == null) {
                continue;
            }
            abpOprtMap.put(dept.getCid(), abpOprt);
        }
        List<QueryIncomeForecProvResp> result = new ArrayList<>();
        for (Map.Entry<String, ObjectId> entry : areaMap.entrySet()) {
            String areaName = entry.getKey();
            ObjectId areaId = entry.getValue();
            QueryIncomeForecProvResp resp = new QueryIncomeForecProvResp();
            resp.setAreaId(areaId);
            resp.setAreaName(areaName);
            List<TeSysDef> defs = map.get(areaId);
            List<IncomeForecProvInfo> provList = new ArrayList<>();
            for (TeSysDef def : defs) {
                ObjectId provId = def.getId();
                IncomeForecProvInfo info = new IncomeForecProvInfo();
                AbpOprt abpOprt = abpOprtMap.get(provId);
                if (abpOprt != null) {
                    TeIdNameCn status = abpOprt.getStatus();
                    if (status == null) {
                        continue;
                    }
                    int bgtStatus = 3;
                    if (status.getCid().equals(Constant.ABP_OPRT_EXPECTED_REVENUE_FORECAST_ID)) {
                        bgtStatus = 1;
                    } else if (status.getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                        bgtStatus = 2;
                    }
                    info.setBgtStatus(bgtStatus);
                    boolean isSelected = bgtStatus == 1;
                    info.setIsSelected(isSelected);
                    info.setOprtStatusId(status.getCid());
                }
                info.setProvId(provId);
                info.setProvName(def.getDefName());
                provList.add(info);
            }
            long count = provList.stream().filter(s -> s.getBgtStatus() != null && s.getBgtStatus() == 1).count();
            resp.setProvList(provList);
            resp.setSelectedCnt(count);
            result.add(resp);
        }

        return CommonResult.success(result);
    }


    @Override
    public CommonResult<PageListResult> queryNeedForecastBpVerOopListByBpVerIdAndProvs(TeSysUser user, QueryIncomeForecMsgReq req) {
        //查询省份对应的区域信息
        List<TeSysDef> sysDefs = queryProvAndRegionAndBigRegionByProvIds(req);
        PageListResult pageListResult = queryIncomeForecastData(user, req, sysDefs);
        return CommonResult.success(pageListResult);
    }

    private PageListResult queryIncomeForecastData(TeSysUser user, QueryIncomeForecMsgReq req, List<TeSysDef> sysDefs) {
        Map<ObjectId, String> map = new HashMap<>();
        for (TeSysDef sysDef : sysDefs) {
            for (TeIdNameCn cndtItem : sysDef.getCndtItems()) {
                if (cndtItem.getCodeName().equals(Constant.REGION)) {
                    map.put(sysDef.getId(), cndtItem.getName());
                }
            }
        }
        //计划版本查询时间
        SysVerMgt sysVerMgt = sysVerMgtDao.findById(req.getPlanVerId());
        String year = sysVerMgt.getYear();
        //查询工作天数
        List<TeSysCal> teSysCals = queryWorkDays(year);
        Map<String, Integer> workDaysMap = new HashMap<>();
        countPerMonthWorkDays(year, teSysCals, workDaysMap);
        List<org.bson.Document> list = queryAbpOopAndAbpOopBgtsCondtions(req);
        Document skip = new Document("$skip", req.getCurPage() * req.getPageSize());
        list.add(skip);
        Document limit = new Document("$limit", req.getPageSize());
        list.add(limit);
        List<org.bson.Document> queryResult = abpOopDao.aggregate(list);
        List<org.bson.Document> countList = queryAbpOopAndAbpOopBgtsCondtions(req);
        countList.add(new Document("$count", "count"));
        List<org.bson.Document> countResult = abpOopDao.aggregate(countList);
        long count = 0;
        if (!CollectionUtils.isEmpty(countResult)) {
            count = (int) countResult.get(0).get("count");
        }
        List<QueryIncomeForecMsgResp> result = new ArrayList<>();
        for (org.bson.Document dbObject : queryResult) {
            QueryIncomeForecMsgResp resp = new QueryIncomeForecMsgResp();
            ObjectId provId = (ObjectId) dbObject.get("provId");
            resp.setAreaName(map.get(provId));
            resp.setProvName((String) dbObject.get("provName"));
            resp.setOopCode((String) dbObject.get("oopCode"));
            resp.setMainPrjCode((String) dbObject.get("mainPrjCode"));
            resp.setOrderId((String) dbObject.get("orderId"));
            resp.setProCode10((String) dbObject.get("proCode10"));
            resp.setProName((String) dbObject.get("proName"));
            resp.setProType((String) dbObject.get("proType"));
            resp.setSales((String) dbObject.get("salesName"));
            resp.setPmName((String) dbObject.get("pmName"));
            Double netSalesL = (Double) dbObject.get("netSalesL");
            if (netSalesL != null) {
                resp.setNetSaleAmt(String.valueOf(netSalesL / 1000));
            }
            resp.setStartPoc(String.valueOf(dbObject.get("startPoc")));
            resp.setSumEngineerRg(String.valueOf(dbObject.get("sumEngineerRg")));
            resp.setSumRadRg(String.valueOf(dbObject.get("sumRadRg")));
            Double sumDirectFee = (Double) dbObject.get("sumDirectFee");
            if (sumDirectFee != null) {
                resp.setSumDirectFee(String.valueOf(sumDirectFee / 1000));
            }
            Double sumStaffOutsourceFee = (Double) dbObject.get("sumStaffOutsourceFee");
            if (sumStaffOutsourceFee != null) {
                resp.setSumStaffOutsourceFee(String.valueOf(sumStaffOutsourceFee / 1000));
            }
            Double sumTechSubcontractFee = (Double) dbObject.get("sumTechSubcontractFee");
            if (sumTechSubcontractFee != null) {
                resp.setSumTechSubcontractFee(String.valueOf(sumTechSubcontractFee / 1000));
            }
            resp.setEndPoc(String.valueOf(dbObject.get("endPoc")));
            List<Document> msts = (List<Document>) dbObject.get("msts");
            if (!CollectionUtils.isEmpty(msts)) {
                for (Document object : msts) {
                    Mst mst = JSON.parseObject(object.toJson(), Mst.class);
                    String planEndDate = mst.getPlanEndDate().substring(0, 7);
                    String name = mst.getName();
                    if (planEndDate.equals(year + "-01")) {
                        String mstName = resp.getMstName01() == null ? mst.getName() : resp.getMstName01();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName01(finalName);
                    } else if (planEndDate.equals(year + "-02")) {
                        String mstName = resp.getMstName02() == null ? mst.getName() : resp.getMstName02();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName02(finalName);
                    } else if (planEndDate.equals(year + "-03")) {
                        String mstName = resp.getMstName03() == null ? mst.getName() : resp.getMstName03();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName03(finalName);
                    } else if (planEndDate.equals(year + "-04")) {
                        String mstName = resp.getMstName04() == null ? mst.getName() : resp.getMstName04();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName04(finalName);
                    } else if (planEndDate.equals(year + "-05")) {
                        String mstName = resp.getMstName05() == null ? mst.getName() : resp.getMstName05();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName05(finalName);
                    } else if (planEndDate.equals(year + "-06")) {
                        String mstName = resp.getMstName06() == null ? mst.getName() : resp.getMstName06();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName06(finalName);
                    } else if (planEndDate.equals(year + "-07")) {
                        String mstName = resp.getMstName07() == null ? mst.getName() : resp.getMstName07();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName07(finalName);
                    } else if (planEndDate.equals(year + "-08")) {
                        String mstName = resp.getMstName08() == null ? mst.getName() : resp.getMstName08();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName08(finalName);
                    } else if (planEndDate.equals(year + "-09")) {
                        String mstName = resp.getMstName09() == null ? mst.getName() : resp.getMstName09();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName09(finalName);
                    } else if (planEndDate.equals(year + "-10")) {
                        String mstName = resp.getMstName10() == null ? mst.getName() : resp.getMstName10();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName10(finalName);
                    } else if (planEndDate.equals(year + "-11")) {
                        String mstName = resp.getMstName11() == null ? mst.getName() : resp.getMstName11();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName11(finalName);
                    } else if (planEndDate.equals(year + "-12")) {
                        String mstName = resp.getMstName12() == null ? mst.getName() : resp.getMstName12();
                        String finalName = mstName.compareTo(name) > 0 ? mstName : name;
                        resp.setMstName12(finalName);
                    }
                }
            }

            List<Document> abpOopBgts = (List<Document>) dbObject.get("abpOopBgt");
            for (Document object : abpOopBgts) {
                AbpOopBgt abpOopBgt = JSON.parseObject(object.toString(), AbpOopBgt.class);
                String ym = abpOopBgt.getYm();
                if (abpOopBgt.getPl() == null) {
                    Income income = abpOopBgt.getIncome();
                    Double amt = 0d;
                    Double poc = 0d;
                    if (income != null) {
                        amt = income.getAmt();
                        poc = income.getPoc();
                    }
                    List<RgInfo> rgInfo = abpOopBgt.getRgInfo();
                    double sumRgFee = 0d;
                    if (!CollectionUtils.isEmpty(rgInfo)) {
                        sumRgFee = rgInfo.stream().filter(info -> info.getEmpType() != null && info.getEmpType().getCid().equals(Constant.OUTSOURCED_STAFF_ID)).mapToDouble(RgInfo::getRgFee).sum();
                    }
                    double bgjHcFee = abpOopBgt.getBgjhqFee() / 1000;
                    double fbfCosts = (sumRgFee + abpOopBgt.getJslfbFee() + abpOopBgt.getDsfdcFee()) / 1000;
                    double directCosts = (abpOopBgt.getTravelFee() + abpOopBgt.getDiningFee() + abpOopBgt.getOtherFee()) / 1000;
                    if (ym.equals(year + "01")) {
                        double planIncome = resp.getPlanIncome01() == null ? 0 : resp.getPlanIncome01();
                        resp.setPlanIncome01(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum01() == null ? 0 : resp.getPocNum01();
                        resp.setPocNum01(pocNum + poc);
                        double costs = resp.getDirectCosts01() == null ? 0 : resp.getDirectCosts01();
                        resp.setDirectCosts01(directCosts + costs);
                        double fbf = resp.getFbfCosts01() == null ? 0 : resp.getFbfCosts01();
                        resp.setFbfCosts01(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts01() == null ? 0 : resp.getHqfCosts01();
                        resp.setFbfCosts01(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "02")) {
                        double planIncome = resp.getPlanIncome02() == null ? 0 : resp.getPlanIncome02();
                        resp.setPlanIncome02(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum02() == null ? 0 : resp.getPocNum02();
                        resp.setPocNum02(pocNum + poc);
                        double costs = resp.getDirectCosts02() == null ? 0 : resp.getDirectCosts02();
                        resp.setDirectCosts02(directCosts + costs);
                        double fbf = resp.getFbfCosts02() == null ? 0 : resp.getFbfCosts02();
                        resp.setFbfCosts02(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts02() == null ? 0 : resp.getHqfCosts02();
                        resp.setFbfCosts02(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "03")) {
                        double planIncome = resp.getPlanIncome03() == null ? 0 : resp.getPlanIncome03();
                        resp.setPlanIncome03(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum03() == null ? 0 : resp.getPocNum03();
                        resp.setPocNum03(pocNum + poc);
                        double costs = resp.getDirectCosts03() == null ? 0 : resp.getDirectCosts03();
                        resp.setDirectCosts03(directCosts + costs);
                        double fbf = resp.getFbfCosts03() == null ? 0 : resp.getFbfCosts03();
                        resp.setFbfCosts03(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts03() == null ? 0 : resp.getHqfCosts03();
                        resp.setFbfCosts03(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "04")) {
                        double planIncome = resp.getPlanIncome04() == null ? 0 : resp.getPlanIncome04();
                        resp.setPlanIncome04(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum04() == null ? 0 : resp.getPocNum04();
                        resp.setPocNum04(pocNum + poc);
                        double costs = resp.getDirectCosts04() == null ? 0 : resp.getDirectCosts04();
                        resp.setDirectCosts04(directCosts + costs);
                        double fbf = resp.getFbfCosts04() == null ? 0 : resp.getFbfCosts04();
                        resp.setFbfCosts04(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts04() == null ? 0 : resp.getHqfCosts04();
                        resp.setFbfCosts04(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "05")) {
                        double planIncome = resp.getPlanIncome05() == null ? 0 : resp.getPlanIncome05();
                        resp.setPlanIncome05(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum05() == null ? 0 : resp.getPocNum05();
                        resp.setPocNum05(pocNum + poc);
                        double costs = resp.getDirectCosts05() == null ? 0 : resp.getDirectCosts05();
                        resp.setDirectCosts05(directCosts + costs);
                        double fbf = resp.getFbfCosts05() == null ? 0 : resp.getFbfCosts05();
                        resp.setFbfCosts05(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts05() == null ? 0 : resp.getHqfCosts05();
                        resp.setFbfCosts05(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "06")) {
                        double planIncome = resp.getPlanIncome06() == null ? 0 : resp.getPlanIncome06();
                        resp.setPlanIncome06(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum06() == null ? 0 : resp.getPocNum06();
                        resp.setPocNum06(pocNum + poc);
                        double costs = resp.getDirectCosts06() == null ? 0 : resp.getDirectCosts06();
                        resp.setDirectCosts06(directCosts + costs);
                        double fbf = resp.getFbfCosts06() == null ? 0 : resp.getFbfCosts06();
                        resp.setFbfCosts06(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts06() == null ? 0 : resp.getHqfCosts06();
                        resp.setFbfCosts06(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "07")) {
                        double planIncome = resp.getPlanIncome07() == null ? 0 : resp.getPlanIncome07();
                        resp.setPlanIncome07(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum07() == null ? 0 : resp.getPocNum07();
                        resp.setPocNum07(pocNum + poc);
                        double costs = resp.getDirectCosts07() == null ? 0 : resp.getDirectCosts07();
                        resp.setDirectCosts07(directCosts + costs);
                        double fbf = resp.getFbfCosts07() == null ? 0 : resp.getFbfCosts07();
                        resp.setFbfCosts07(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts07() == null ? 0 : resp.getHqfCosts07();
                        resp.setFbfCosts07(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "08")) {
                        double planIncome = resp.getPlanIncome08() == null ? 0 : resp.getPlanIncome08();
                        resp.setPlanIncome08(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum08() == null ? 0 : resp.getPocNum08();
                        resp.setPocNum08(pocNum + poc);
                        double costs = resp.getDirectCosts08() == null ? 0 : resp.getDirectCosts08();
                        resp.setDirectCosts08(directCosts + costs);
                        double fbf = resp.getFbfCosts08() == null ? 0 : resp.getFbfCosts08();
                        resp.setFbfCosts08(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts08() == null ? 0 : resp.getHqfCosts08();
                        resp.setFbfCosts08(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "09")) {
                        double planIncome = resp.getPlanIncome09() == null ? 0 : resp.getPlanIncome09();
                        resp.setPlanIncome09(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum09() == null ? 0 : resp.getPocNum09();
                        resp.setPocNum09(pocNum + poc);
                        double costs = resp.getDirectCosts09() == null ? 0 : resp.getDirectCosts09();
                        resp.setDirectCosts09(directCosts + costs);
                        double fbf = resp.getFbfCosts09() == null ? 0 : resp.getFbfCosts09();
                        resp.setFbfCosts09(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts09() == null ? 0 : resp.getHqfCosts09();
                        resp.setFbfCosts09(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "10")) {
                        double planIncome = resp.getPlanIncome10() == null ? 0 : resp.getPlanIncome10();
                        resp.setPlanIncome10(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum10() == null ? 0 : resp.getPocNum10();
                        resp.setPocNum10(pocNum + poc);
                        double costs = resp.getDirectCosts10() == null ? 0 : resp.getDirectCosts10();
                        resp.setDirectCosts10(directCosts + costs);
                        double fbf = resp.getFbfCosts10() == null ? 0 : resp.getFbfCosts10();
                        resp.setFbfCosts10(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts10() == null ? 0 : resp.getHqfCosts10();
                        resp.setFbfCosts10(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "11")) {
                        double planIncome = resp.getPlanIncome11() == null ? 0 : resp.getPlanIncome11();
                        resp.setPlanIncome11(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum11() == null ? 0 : resp.getPocNum11();
                        resp.setPocNum11(pocNum + poc);
                        double costs = resp.getDirectCosts11() == null ? 0 : resp.getDirectCosts11();
                        resp.setDirectCosts11(directCosts + costs);
                        double fbf = resp.getFbfCosts11() == null ? 0 : resp.getFbfCosts11();
                        resp.setFbfCosts11(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts11() == null ? 0 : resp.getHqfCosts11();
                        resp.setFbfCosts11(bgjHcFee + hqfCosts);
                    } else if (ym.equals(year + "12")) {
                        double planIncome = resp.getPlanIncome12() == null ? 0 : resp.getPlanIncome12();
                        resp.setPlanIncome12(planIncome + amt / 1000);
                        double pocNum = resp.getPocNum12() == null ? 0 : resp.getPocNum12();
                        resp.setPocNum12(pocNum + poc);
                        double costs = resp.getDirectCosts12() == null ? 0 : resp.getDirectCosts12();
                        resp.setDirectCosts12(directCosts + costs);
                        double fbf = resp.getFbfCosts12() == null ? 0 : resp.getFbfCosts12();
                        resp.setFbfCosts12(fbfCosts + fbf);
                        double hqfCosts = resp.getHqfCosts12() == null ? 0 : resp.getHqfCosts12();
                        resp.setFbfCosts12(bgjHcFee + hqfCosts);
                    }
                }
                List<RgInfo> rgInfo = abpOopBgt.getRgInfo();
                double sum = rgInfo.stream().filter(info -> info.getEmpType() != null && info.getEmpType().getCid().equals(Constant.REGULAR_EMPLOYEE_ID)).mapToDouble(RgInfo::getRgNum).sum();
                if (ym.equals(year + "01")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "01") == null ? 0 : workDaysMap.get(year + "01");
                    resp.setRgNum01(days * sum + rgNum);
                } else if (ym.equals(year + "02")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "02") == null ? 0 : workDaysMap.get(year + "02");
                    resp.setRgNum02(days * sum + rgNum);
                } else if (ym.equals(year + "03")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "03") == null ? 0 : workDaysMap.get(year + "03");
                    resp.setRgNum03(days * sum + rgNum);
                } else if (ym.equals(year + "04")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "04") == null ? 0 : workDaysMap.get(year + "04");
                    resp.setRgNum04(days * sum + rgNum);
                } else if (ym.equals(year + "05")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "05") == null ? 0 : workDaysMap.get(year + "05");
                    resp.setRgNum05(days * sum + rgNum);
                } else if (ym.equals(year + "06")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "06") == null ? 0 : workDaysMap.get(year + "06");
                    resp.setRgNum06(days * sum + rgNum);
                } else if (ym.equals(year + "07")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "07") == null ? 0 : workDaysMap.get(year + "07");
                    resp.setRgNum07(days * sum + rgNum);
                } else if (ym.equals(year + "08")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "08") == null ? 0 : workDaysMap.get(year + "08");
                    resp.setRgNum08(days * sum + rgNum);
                } else if (ym.equals(year + "09")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "09") == null ? 0 : workDaysMap.get(year + "09");
                    resp.setRgNum09(days * sum + rgNum);
                } else if (ym.equals(year + "10")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "10") == null ? 0 : workDaysMap.get(year + "10");
                    resp.setRgNum10(days * sum + rgNum);
                } else if (ym.equals(year + "11")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "11") == null ? 0 : workDaysMap.get(year + "11");
                    resp.setRgNum11(days * sum + rgNum);
                } else if (ym.equals(year + "12")) {
                    double rgNum = resp.getRgNum01() == null ? 0d : resp.getRgNum01();
                    int days = workDaysMap.get(year + "12") == null ? 0 : workDaysMap.get(year + "12");
                    resp.setRgNum12(days * sum + rgNum);
                }

            }

            result.add(resp);
        }

        List<String> orderIds = result.stream().map(QueryIncomeForecMsgResp::getOrderId).collect(Collectors.toList());
        //从abpOrder中查寻AI软件运维服务开始时间和结束时间
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DbFieldName.AbpOrder.orderCode, orderIds));
        conds.add(new DC_E(DbFieldName.AbpOrder.buCode, user.getSbuId()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.AbpOrder.aiOperatBeginDate);
        fieldNames.add(DbFieldName.AbpOrder.aiOperatEndDate);
        fieldNames.add(DbFieldName.AbpOrder.orderCode);
        List<AbpOrder> abpOrders = abpOrderDao.findByFieldAndConds(conds, fieldNames);
        Map<String, List<AbpOrder>> resultMap = abpOrders.stream().collect(Collectors.groupingBy(AbpOrder::getOrderCode));
        for (QueryIncomeForecMsgResp resp : result) {
            String orderId = resp.getOrderId();
            List<AbpOrder> abpOrderList = resultMap.get(orderId);
            if (CollectionUtils.isEmpty(abpOrderList)) {
                continue;
            }
            AbpOrder abpOrder = abpOrderList.get(0);
            resp.setMtncStartDate(DateUtil.formatDate2Str(abpOrder.getAiOperatBeginDate(), DateUtil.DATETIME_FORMAT));
            resp.setMtncEndDate(DateUtil.formatDate2Str(abpOrder.getAiOperatEndDate(), DateUtil.DATETIME_FORMAT));
        }
        PageListResult pageListResult = new PageListResult();
        pageListResult.setCount(count);
        pageListResult.setList(result);
        return pageListResult;
    }

    private void countPerMonthWorkDays(String year, List<TeSysCal> teSysCals, Map<String, Integer> workDaysMap) {
        for (TeSysCal teSysCal : teSysCals) {
            if (teSysCal.getDate().startsWith(year + "-01")) {
                int count = workDaysMap.get(year + "-01") == null ? 0 : workDaysMap.get(year + "-01");
                workDaysMap.put(year + "-01", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-02")) {
                int count = workDaysMap.get(year + "-02") == null ? 0 : workDaysMap.get(year + "-02");
                workDaysMap.put(year + "-02", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-03")) {
                int count = workDaysMap.get(year + "-03") == null ? 0 : workDaysMap.get(year + "-03");
                workDaysMap.put(year + "-03", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-04")) {
                int count = workDaysMap.get(year + "-04") == null ? 0 : workDaysMap.get(year + "-04");
                workDaysMap.put(year + "-04", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-05")) {
                int count = workDaysMap.get(year + "-05") == null ? 0 : workDaysMap.get(year + "-05");
                workDaysMap.put(year + "-05", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-06")) {
                int count = workDaysMap.get(year + "-06") == null ? 0 : workDaysMap.get(year + "-06");
                workDaysMap.put(year + "-06", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-07")) {
                int count = workDaysMap.get(year + "-07") == null ? 0 : workDaysMap.get(year + "-07");
                workDaysMap.put(year + "-07", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-08")) {
                int count = workDaysMap.get(year + "-08") == null ? 0 : workDaysMap.get(year + "-08");
                workDaysMap.put(year + "-08", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-09")) {
                int count = workDaysMap.get(year + "-09") == null ? 0 : workDaysMap.get(year + "-09");
                workDaysMap.put(year + "-09", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-10")) {
                int count = workDaysMap.get(year + "-10") == null ? 0 : workDaysMap.get(year + "-10");
                workDaysMap.put(year + "-10", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-11")) {
                int count = workDaysMap.get(year + "-11") == null ? 0 : workDaysMap.get(year + "-11");
                workDaysMap.put(year + "-11", count + 1);
            } else if (teSysCal.getDate().startsWith(year + "-12")) {
                int count = workDaysMap.get(year + "-12") == null ? 0 : workDaysMap.get(year + "-12");
                workDaysMap.put(year + "-12", count + 1);
            }

        }
    }

    private List<TeSysCal> queryWorkDays(String year) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.SysCal.prj.dot(DbFieldName.common_cid), null));
        conds.add(new DC_L(DbFieldName.SysCal.date, year));
        conds.add(new DC_E(DbFieldName.SysCal.mh, 0, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.SysCal.date);
        return sysCalDao.findByFieldAndConds(conds, fieldNames);
    }

    private ArrayList<org.bson.Document> queryAbpOopAndAbpOopBgtsCondtions(QueryIncomeForecMsgReq req) {
        //查询abpOop和abpOopBgt数据
        ArrayList<org.bson.Document> list = new ArrayList<>();
        //第一张集合的查询条件
        Document in = new Document("$in", req.getProvList());
        Document abpOopMatchCondtion = new Document();
        abpOopMatchCondtion.append(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid).n(), new ObjectId(req.getPlanVerId()))
                .append(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).n(), in);
        Document abpOopMatch = new Document("$match", abpOopMatchCondtion);
        list.add(abpOopMatch);

        Document ifNullProjectCondtion = new Document();
        Document ifNull = new Document("$ifNull", Arrays.asList(DbFieldName.AbpOop.linkedOrderCode.$n(), 0));
        ifNullProjectCondtion.append(DbFieldName.common__id.n(), 1)
                .append(DbFieldName.AbpOop.oopCode.n(), 1)
                .append(DbFieldName.AbpOop.mainPrjCode.n(), 1)
                .append(DbFieldName.common_name.n(), 1)
                .append(DbFieldName.AbpOop.sales.n(), 1)
                .append(DbFieldName.AbpOop.pm.n(), 1)
                .append(DbFieldName.AbpOop.startPoc.n(), 1)
                .append(DbFieldName.AbpOop.sumEngineerRg.n(), 1)
                .append(DbFieldName.AbpOop.sumRadRg.n(), 1)
                .append(DbFieldName.AbpOop.sumDirectFee.n(), 1)
                .append(DbFieldName.AbpOop.sumStaffOutsourceFee.n(), 1)
                .append(DbFieldName.AbpOop.sumTechSubcontractFee.n(), 1)
                .append(DbFieldName.AbpOop.msts.n(), 1)
                .append(DbFieldName.common_cn.n(), 1)
                .append(DbFieldName.AbpOop.linkedOrderCode.n(), ifNull)
                .append(DbFieldName.AbpOop.prov.n(), 1)
                .append(DbFieldName.AbpOop.prjType.n(), 1);
        Document ifNullProject = new Document("$project", ifNullProjectCondtion);
        list.add(ifNullProject);

        Document abpOopProjectCondtion = new Document();
        Document orderIdEq = new Document("$eq", Arrays.asList(DbFieldName.AbpOop.linkedOrderCode.$n(), 0));
        Document orderIdCondObject = new Document();
        orderIdCondObject.append("if", orderIdEq).append("then", DbFieldName.common_cn.$n()).append("else", DbFieldName.AbpOop.linkedOrderCode.$n());
        Document orderIdCond = new Document("$cond", orderIdCondObject);

        Document proCode10Eq = new Document("$eq", Arrays.asList(DbFieldName.AbpOop.linkedOrderCode.$n(), 0));
        Document proCode10CondObject = new Document();
        proCode10CondObject.append("if", proCode10Eq).append("then", null).append("else", DbFieldName.common_cn.$n());
        Document proCode10Cond = new Document("$cond", proCode10CondObject);

        abpOopProjectCondtion.append(DbFieldName.common__id.n(), 1)
                .append(DbFieldName.AbpOop.oopCode.n(), 1)
                .append(DbFieldName.AbpOop.mainPrjCode.n(), 1)
                .append("proName", DbFieldName.common_name.$n())
                .append("sales", DbFieldName.AbpOop.sales.dot(DbFieldName.common_userName).$n())
                .append("pmName", DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).$n())
                .append(DbFieldName.AbpOop.startPoc.n(), 1)
                .append(DbFieldName.AbpOop.sumEngineerRg.n(), 1)
                .append(DbFieldName.AbpOop.sumRadRg.n(), 1)
                .append(DbFieldName.AbpOop.sumDirectFee.n(), 1)
                .append(DbFieldName.AbpOop.sumStaffOutsourceFee.n(), 1)
                .append(DbFieldName.AbpOop.sumTechSubcontractFee.n(), 1)
                .append(DbFieldName.AbpOop.msts.n(), 1)
                .append("orderId", orderIdCond)
                .append("proCode10", proCode10Cond)
                .append("provId", DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).$n())
                .append("provName", DbFieldName.AbpOop.prov.dot(DbFieldName.common_name).$n())
                .append("proType", DbFieldName.AbpOop.prjType.dot(DbFieldName.common_name).$n());
        Document abpOopProject = new Document("$project", abpOopProjectCondtion);
        list.add(abpOopProject);

        Document lookupCondtion = new Document();
        lookupCondtion.append("from", DbTableName.ABP_OOP_BGT.getName())
                .append("localField", DbFieldName.common__id.getName())
                .append("foreignField", DbFieldName.AbpOopBgt.oop.dot(DbFieldName.common_cid).n())
                .append("as", DbTableName.ABP_OOP_BGT.getName());
        Document lookup = new Document("$lookup", lookupCondtion);
        list.add(lookup);

        Document likeMatchCondtion = new Document();
        String orderId = req.getOrderId();
        if (StringUtil.isNotNull(orderId)) {
            Document orderIdLike = new Document();
            orderIdLike.append("$regex", orderId).append("$options", "i");
            likeMatchCondtion.put("orderId", orderIdLike);
        }
        String proCode10 = req.getProCode10();
        if (StringUtil.isNotNull(proCode10)) {
            Document proCode10Like = new Document();
            proCode10Like.append("$regex", proCode10).append("$options", "i");
            likeMatchCondtion.put("proCode10", proCode10Like);
        }
        String proName = req.getProName();
        if (StringUtil.isNotNull(proName)) {
            Document proNameLike = new Document();
            proNameLike.append("$regex", proName).append("$options", "i");
            likeMatchCondtion.put("proName", proNameLike);
        }
        String pmName = req.getPmName();
        if (StringUtil.isNotNull(pmName)) {
            Document pmNameLike = new Document();
            pmNameLike.append("$regex", pmName).append("$options", "i");
            likeMatchCondtion.put("pmName", pmNameLike);
        }
        String salesName = req.getSalesName();
        if (StringUtil.isNotNull(salesName)) {
            Document salesNameLike = new Document();
            salesNameLike.append("$regex", salesName).append("$options", "i");
            likeMatchCondtion.put("salesName", salesNameLike);
        }
        if (!likeMatchCondtion.isEmpty()) {
            Document likeMatch = new Document("$match", likeMatchCondtion);
            list.add(likeMatch);
        }
        return list;
    }

    private List<TeSysDef> queryProvAndRegionAndBigRegionByProvIds(QueryIncomeForecMsgReq req) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_I<>(DbFieldName.common__id, req.getProvList()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.sysDef_cndtItems);
        return sysDefDao.findByFieldAndConds(conds, fieldNames);
    }

    @Override
    public CommonResult<PageListResult> queryNeedForecastBpVerOopListByBpVerIdAndGradeId(TeSysUser user, QueryIncomeForecMsgReq req) {
        String areaId = req.getAreaId();
        ObjectId id = new ObjectId(areaId);
        //1.查询当前的区域对应的省份信息
        List<TeSysDef> sysDefs = queryProvByArearId(id,user.getSbuId());
        List<ObjectId> provIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
        req.setProvList(provIds);
        PageListResult pageListResult = queryIncomeForecastData(user, req, sysDefs);
        return CommonResult.success(pageListResult);
    }

    private List<TeSysDef> queryProvByArearId(ObjectId id,String buCode) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), Constant.ABP_PROV_CODE_NAME));
        conds.add(new DC_E(DbFieldName.common_isValid, true));
        conds.add(new DC_E(DbFieldName.common_src.dot(DbFieldName.sysDef__srcDefCodeName), buCode));
        List<IDbCondition> or = new ArrayList<>();
        or.add(new DC_E(DbFieldName.common__id, id));
        or.add(new DC_E(DbFieldName.common_src.dot(DbFieldName.sysDef__srcDefId), id));
        or.add(new DC_E(DbFieldName.sysDef_cndtItems.dot(DbFieldName.common_cid), id));
        conds.add(new DC_OR(or));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef_cndtItems);
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_src);
        fieldNames.add(DbFieldName.sysDef__defName);
        return sysDefDao.findByFieldAndConds(conds, fieldNames);
    }

    @Override
    public PageBean exportNeedForecastBpVerOopListByBpVerIdAndProvs(TeSysUser user, QueryIncomeForecMsgReq req) {
        CommonResult<PageListResult> result = queryNeedForecastBpVerOopListByBpVerIdAndProvs(user, req);
        PageListResult data = result.getData();
        List<QueryIncomeForecMsgResp> dataList = (List<QueryIncomeForecMsgResp>) data.getList();
        List<Object> list = new ArrayList<>();
        LinkedHashMap<Integer, String> titleMap0 = new LinkedHashMap<>();
        titleMap0.put(0, "区域");
        titleMap0.put(1, "省份");
        titleMap0.put(2, "经营计划代码");
        titleMap0.put(3, "订单流水号");
        titleMap0.put(4, "10位项目编码");
        titleMap0.put(5, "项目名称");
        titleMap0.put(6, "项目类型");
        titleMap0.put(7, "销售经理");
        titleMap0.put(8, "项目经理");
        titleMap0.put(9, "6位项目编码");
        titleMap0.put(10, "净销售额（￥K）");
        titleMap0.put(11, "NetOrder-CMC（￥K）");
        titleMap0.put(12, "考核比例");
        titleMap0.put(13, "服务期开始日期");
        titleMap0.put(14, "服务期结束日期");
        titleMap0.put(15, "预测GM(%)");
        titleMap0.put(16, "Backlog（￥K）");
        titleMap0.put(17, "期初POC(%)");
        titleMap0.put(18, "工程总人工（人月）");
        titleMap0.put(19, "研发总人工（人月）");
        titleMap0.put(20, "直接总费用（￥K）");
        titleMap0.put(21, "人员外包总费用（￥K）");
        titleMap0.put(22, "技术分包总费用（￥K）");
        titleMap0.put(23, "期末POC(%)");
        titleMap0.put(24, "预测备注");

        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(25 + i, "预测收入（￥K）");
            } else {
                titleMap0.put(25 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(37 + i, "各月累计POC(%)");
            } else {
                titleMap0.put(37 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(49 + i, "进度预测(里程碑计划)");
            } else {
                titleMap0.put(49 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(61 + i, "人工日(非研发人员,天)");
            } else {
                titleMap0.put(61 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(73 + i, "直接成本（￥K）");
            } else {
                titleMap0.put(73 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(85 + i, "分包成本（￥K）");
            } else {
                titleMap0.put(85 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(97 + i, "混签成本（￥K）");
            } else {
                titleMap0.put(97 + i, "");
            }
        }

        LinkedHashMap<Integer, String> titleMap1 = new LinkedHashMap<Integer, String>();
        for (int i = 0; i < 25; i++) {
            titleMap1.put(i, "");
        }
        int pos = 25;
        for (int i = 0; i < 84; i++) {
            titleMap1.put(pos++, ((i % 12) + 1) + "月");
        }
        list.add(titleMap0);
        list.add(titleMap1);
        for (QueryIncomeForecMsgResp resp : dataList) {
            LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<>();
            linkedMap.put("areaName", resp.getAreaName());
            linkedMap.put("provName", resp.getProvName());
            linkedMap.put("oopCode", resp.getOopCode());
            linkedMap.put("orderId", resp.getOrderId());
            linkedMap.put("proCode10", resp.getProCode10());
            linkedMap.put("proName", resp.getProvName());
            linkedMap.put("proType", resp.getProType());
            linkedMap.put("sales", resp.getSales());
            linkedMap.put("pmName", resp.getPmName());
            linkedMap.put("proCode6", resp.getProCode6());
            linkedMap.put("netSaleAmt", resp.getNetSaleAmt());
            linkedMap.put("netOrderCmc", resp.getNetOrderCmc());
            linkedMap.put("assessmentRatio", resp.getAssessmentRatio());
            linkedMap.put("mtncStartDate", resp.getMtncStartDate());
            linkedMap.put("mtncEndDate", resp.getMtncEndDate());
            linkedMap.put("forecastGm", resp.getForecastGm());
            linkedMap.put("backlog", resp.getBacklog());
            linkedMap.put("startPoc", resp.getStartPoc());
            linkedMap.put("sumEngineerRg", resp.getSumEngineerRg());
            linkedMap.put("sumRadRg", resp.getSumRadRg());
            linkedMap.put("sumDirectFee", resp.getSumDirectFee());
            linkedMap.put("sumStaffOutsourceFee", resp.getSumStaffOutsourceFee());
            linkedMap.put("sumTechSubcontractFee", resp.getSumTechSubcontractFee());
            linkedMap.put("endPoc", resp.getEndPoc());
            linkedMap.put("forecastRemark", resp.getForecastRemark());
            linkedMap.put("planIncome1", resp.getPlanIncome01());
            linkedMap.put("planIncome2", resp.getPlanIncome02());
            linkedMap.put("planIncome3", resp.getPlanIncome03());
            linkedMap.put("planIncome4", resp.getPlanIncome04());
            linkedMap.put("planIncome5", resp.getPlanIncome05());
            linkedMap.put("planIncome6", resp.getPlanIncome06());
            linkedMap.put("planIncome7", resp.getPlanIncome07());
            linkedMap.put("planIncome8", resp.getPlanIncome08());
            linkedMap.put("planIncome9", resp.getPlanIncome09());
            linkedMap.put("planIncome10", resp.getPlanIncome10());
            linkedMap.put("planIncome11", resp.getPlanIncome11());
            linkedMap.put("planIncome12", resp.getPlanIncome12());
            linkedMap.put("pocNum01", resp.getPocNum01());
            linkedMap.put("pocNum2", resp.getPocNum02());
            linkedMap.put("pocNum3", resp.getPocNum03());
            linkedMap.put("pocNum4", resp.getPocNum04());
            linkedMap.put("pocNum5", resp.getPocNum05());
            linkedMap.put("pocNum6", resp.getPocNum06());
            linkedMap.put("pocNum7", resp.getPocNum07());
            linkedMap.put("pocNum8", resp.getPocNum08());
            linkedMap.put("pocNum9", resp.getPocNum09());
            linkedMap.put("pocNum10", resp.getPocNum10());
            linkedMap.put("pocNum11", resp.getPocNum11());
            linkedMap.put("pocNum12", resp.getPocNum12());
            linkedMap.put("mstName1", resp.getMstName01());
            linkedMap.put("mstName2", resp.getMstName02());
            linkedMap.put("mstName3", resp.getMstName03());
            linkedMap.put("mstName4", resp.getMstName04());
            linkedMap.put("mstName5", resp.getMstName05());
            linkedMap.put("mstName6", resp.getMstName06());
            linkedMap.put("mstName7", resp.getMstName07());
            linkedMap.put("mstName8", resp.getMstName08());
            linkedMap.put("mstName9", resp.getMstName09());
            linkedMap.put("mstName10", resp.getMstName10());
            linkedMap.put("mstName11", resp.getMstName11());
            linkedMap.put("mstName12", resp.getMstName12());

            linkedMap.put("rgNum1", resp.getRgNum01());
            linkedMap.put("rgNum2", resp.getRgNum02());
            linkedMap.put("rgNum3", resp.getRgNum03());
            linkedMap.put("rgNum4", resp.getRgNum04());
            linkedMap.put("rgNum5", resp.getRgNum05());
            linkedMap.put("rgNum6", resp.getRgNum06());
            linkedMap.put("rgNum7", resp.getRgNum07());
            linkedMap.put("rgNum8", resp.getRgNum08());
            linkedMap.put("rgNum9", resp.getRgNum09());
            linkedMap.put("rgNum10", resp.getRgNum10());
            linkedMap.put("rgNum11", resp.getRgNum11());
            linkedMap.put("rgNum12", resp.getRgNum12());
            linkedMap.put("directCosts1", resp.getDirectCosts01());
            linkedMap.put("directCosts2", resp.getDirectCosts02());
            linkedMap.put("directCosts3", resp.getDirectCosts03());
            linkedMap.put("directCosts4", resp.getDirectCosts04());
            linkedMap.put("directCosts5", resp.getDirectCosts05());
            linkedMap.put("directCosts6", resp.getDirectCosts06());
            linkedMap.put("directCosts7", resp.getDirectCosts07());
            linkedMap.put("directCosts8", resp.getDirectCosts08());
            linkedMap.put("directCosts9", resp.getDirectCosts09());
            linkedMap.put("directCosts10", resp.getDirectCosts10());
            linkedMap.put("directCosts11", resp.getDirectCosts11());
            linkedMap.put("directCosts12", resp.getDirectCosts12());
            linkedMap.put("fbfCosts1", resp.getFbfCosts01());
            linkedMap.put("fbfCosts2", resp.getFbfCosts02());
            linkedMap.put("fbfCosts3", resp.getFbfCosts03());
            linkedMap.put("fbfCosts4", resp.getFbfCosts04());
            linkedMap.put("fbfCosts5", resp.getFbfCosts05());
            linkedMap.put("fbfCosts6", resp.getFbfCosts06());
            linkedMap.put("fbfCosts7", resp.getFbfCosts07());
            linkedMap.put("fbfCosts8", resp.getFbfCosts08());
            linkedMap.put("fbfCosts9", resp.getFbfCosts09());
            linkedMap.put("fbfCosts10", resp.getFbfCosts10());
            linkedMap.put("fbfCosts11", resp.getFbfCosts11());
            linkedMap.put("fbfCosts12", resp.getFbfCosts12());
            linkedMap.put("hqfCosts1", resp.getHqfCosts01());
            linkedMap.put("hqfCosts2", resp.getHqfCosts02());
            linkedMap.put("hqfCosts3", resp.getHqfCosts03());
            linkedMap.put("hqfCosts4", resp.getHqfCosts04());
            linkedMap.put("hqfCosts5", resp.getHqfCosts05());
            linkedMap.put("hqfCosts6", resp.getHqfCosts06());
            linkedMap.put("hqfCosts7", resp.getHqfCosts07());
            linkedMap.put("hqfCosts8", resp.getHqfCosts08());
            linkedMap.put("hqfCosts9", resp.getHqfCosts09());
            linkedMap.put("hqfCosts10", resp.getHqfCosts10());
            linkedMap.put("hqfCosts11", resp.getHqfCosts11());
            linkedMap.put("hqfCosts12", resp.getHqfCosts12());
            list.add(linkedMap);
        }
        PageBean bean = new PageBean();
        bean.setCount((int) data.getCount());
        bean.setObjectList(list);
        return bean;
    }

    @Override
    public PageBean exportNeedForecastBpVerOopListByBpVerIdAndGradeId(TeSysUser user, QueryIncomeForecMsgReq req) {
        CommonResult<PageListResult> commonResult = queryNeedForecastBpVerOopListByBpVerIdAndGradeId(user, req);
        PageListResult pageListResult = commonResult.getData();
        List<QueryIncomeForecMsgResp> dataList = (List<QueryIncomeForecMsgResp>) pageListResult.getList();
        List<Object> list = new ArrayList<>();
        LinkedHashMap<Integer, String> titleMap0 = new LinkedHashMap<>();
        titleMap0.put(0, "区域");
        titleMap0.put(1, "省份");
        titleMap0.put(2, "经营计划代码");
        titleMap0.put(3, "订单流水号");
        titleMap0.put(4, "10位项目编码");
        titleMap0.put(5, "项目名称");
        titleMap0.put(6, "项目类型");
        titleMap0.put(7, "销售经理");
        titleMap0.put(8, "项目经理");
        titleMap0.put(9, "6位项目编码");
        titleMap0.put(10, "净销售额（￥K）");
        titleMap0.put(11, "净销售额（含分包）（￥K）");
        titleMap0.put(12, "NetOrder-CMC（￥K）");
        titleMap0.put(13, "考核比例");
        titleMap0.put(14, "服务期开始日期");
        titleMap0.put(15, "服务期结束日期");
        titleMap0.put(16, "预测GM(%)");
        titleMap0.put(17, "Backlog（￥K）");
        titleMap0.put(18, "期初POC(%)");
        titleMap0.put(19, "工程总人工（人月）");
        titleMap0.put(20, "研发总人工（人月）");
        titleMap0.put(21, "直接总费用（￥K）");
        titleMap0.put(22, "人员外包总费用（￥K）");
        titleMap0.put(23, "技术分包总费用（￥K）");
        titleMap0.put(24, "期末POC(%)");
        titleMap0.put(25, "预测备注");

        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(26 + i, "预测收入（￥K）");
            } else {
                titleMap0.put(26 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(38 + i, "各月累计POC(%)");
            } else {
                titleMap0.put(38 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(50 + i, "进度预测(里程碑计划)");
            } else {
                titleMap0.put(50 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(62 + i, "人工日(非研发人员,天)");
            } else {
                titleMap0.put(62 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(74 + i, "直接成本（￥K）");
            } else {
                titleMap0.put(74 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(86 + i, "分包成本（￥K）");
            } else {
                titleMap0.put(86 + i, "");
            }
        }
        for (int i = 0; i < 12; i++) {
            if (i == 0) {
                titleMap0.put(98 + i, "混签成本（￥K）");
            } else {
                titleMap0.put(98 + i, "");
            }
        }

        LinkedHashMap<Integer, String> titleMap1 = new LinkedHashMap<Integer, String>();
        for (int i = 0; i < 26; i++) {
            titleMap1.put(i, "");
        }
        int pos = 26;
        for (int i = 0; i < 84; i++) {
            titleMap1.put(pos++, ((i % 12) + 1) + "月");
        }

        list.add(titleMap0);
        list.add(titleMap1);

        for (QueryIncomeForecMsgResp resp : dataList) {
            LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<>();
            linkedMap.put("areaName", resp.getAreaName());
            linkedMap.put("provName", resp.getProvName());
            linkedMap.put("oopCode", resp.getOopCode());
            linkedMap.put("orderId", resp.getOrderId());
            linkedMap.put("proCode10", resp.getProCode10());
            linkedMap.put("proName", resp.getProvName());
            linkedMap.put("proType", resp.getProType());
            linkedMap.put("sales", resp.getSales());
            linkedMap.put("pmName", resp.getPmName());
            linkedMap.put("proCode6", resp.getProCode6());
            linkedMap.put("netSaleAmt", resp.getNetSaleAmt());
            linkedMap.put("netSaleAmtHfb", resp.getNetSaleAmtHfb());
            linkedMap.put("netOrderCmc", resp.getNetOrderCmc());
            linkedMap.put("assessmentRatio", resp.getAssessmentRatio());
            linkedMap.put("mtncStartDate", resp.getMtncStartDate());
            linkedMap.put("mtncEndDate", resp.getMtncEndDate());
            linkedMap.put("forecastGm", resp.getForecastGm());
            linkedMap.put("backlog", resp.getBacklog());
            linkedMap.put("startPoc", resp.getStartPoc());
            linkedMap.put("sumEngineerRg", resp.getSumEngineerRg());
            linkedMap.put("sumRadRg", resp.getSumRadRg());
            linkedMap.put("sumDirectFee", resp.getSumDirectFee());
            linkedMap.put("sumStaffOutsourceFee", resp.getSumStaffOutsourceFee());
            linkedMap.put("sumTechSubcontractFee", resp.getSumTechSubcontractFee());
            linkedMap.put("endPoc", resp.getEndPoc());
            linkedMap.put("forecastRemark", resp.getForecastRemark());
            linkedMap.put("planIncome1", resp.getPlanIncome01());
            linkedMap.put("planIncome2", resp.getPlanIncome02());
            linkedMap.put("planIncome3", resp.getPlanIncome03());
            linkedMap.put("planIncome4", resp.getPlanIncome04());
            linkedMap.put("planIncome5", resp.getPlanIncome05());
            linkedMap.put("planIncome6", resp.getPlanIncome06());
            linkedMap.put("planIncome7", resp.getPlanIncome07());
            linkedMap.put("planIncome8", resp.getPlanIncome08());
            linkedMap.put("planIncome9", resp.getPlanIncome09());
            linkedMap.put("planIncome10", resp.getPlanIncome10());
            linkedMap.put("planIncome11", resp.getPlanIncome11());
            linkedMap.put("planIncome12", resp.getPlanIncome12());
            linkedMap.put("pocNum01", resp.getPocNum01());
            linkedMap.put("pocNum2", resp.getPocNum02());
            linkedMap.put("pocNum3", resp.getPocNum03());
            linkedMap.put("pocNum4", resp.getPocNum04());
            linkedMap.put("pocNum5", resp.getPocNum05());
            linkedMap.put("pocNum6", resp.getPocNum06());
            linkedMap.put("pocNum7", resp.getPocNum07());
            linkedMap.put("pocNum8", resp.getPocNum08());
            linkedMap.put("pocNum9", resp.getPocNum09());
            linkedMap.put("pocNum10", resp.getPocNum10());
            linkedMap.put("pocNum11", resp.getPocNum11());
            linkedMap.put("pocNum12", resp.getPocNum12());
            linkedMap.put("mstName1", resp.getMstName01());
            linkedMap.put("mstName2", resp.getMstName02());
            linkedMap.put("mstName3", resp.getMstName03());
            linkedMap.put("mstName4", resp.getMstName04());
            linkedMap.put("mstName5", resp.getMstName05());
            linkedMap.put("mstName6", resp.getMstName06());
            linkedMap.put("mstName7", resp.getMstName07());
            linkedMap.put("mstName8", resp.getMstName08());
            linkedMap.put("mstName9", resp.getMstName09());
            linkedMap.put("mstName10", resp.getMstName10());
            linkedMap.put("mstName11", resp.getMstName11());
            linkedMap.put("mstName12", resp.getMstName12());

            linkedMap.put("rgNum1", resp.getRgNum01());
            linkedMap.put("rgNum2", resp.getRgNum02());
            linkedMap.put("rgNum3", resp.getRgNum03());
            linkedMap.put("rgNum4", resp.getRgNum04());
            linkedMap.put("rgNum5", resp.getRgNum05());
            linkedMap.put("rgNum6", resp.getRgNum06());
            linkedMap.put("rgNum7", resp.getRgNum07());
            linkedMap.put("rgNum8", resp.getRgNum08());
            linkedMap.put("rgNum9", resp.getRgNum09());
            linkedMap.put("rgNum10", resp.getRgNum10());
            linkedMap.put("rgNum11", resp.getRgNum11());
            linkedMap.put("rgNum12", resp.getRgNum12());
            linkedMap.put("directCosts1", resp.getDirectCosts01());
            linkedMap.put("directCosts2", resp.getDirectCosts02());
            linkedMap.put("directCosts3", resp.getDirectCosts03());
            linkedMap.put("directCosts4", resp.getDirectCosts04());
            linkedMap.put("directCosts5", resp.getDirectCosts05());
            linkedMap.put("directCosts6", resp.getDirectCosts06());
            linkedMap.put("directCosts7", resp.getDirectCosts07());
            linkedMap.put("directCosts8", resp.getDirectCosts08());
            linkedMap.put("directCosts9", resp.getDirectCosts09());
            linkedMap.put("directCosts10", resp.getDirectCosts10());
            linkedMap.put("directCosts11", resp.getDirectCosts11());
            linkedMap.put("directCosts12", resp.getDirectCosts12());
            linkedMap.put("fbfCosts1", resp.getFbfCosts01());
            linkedMap.put("fbfCosts2", resp.getFbfCosts02());
            linkedMap.put("fbfCosts3", resp.getFbfCosts03());
            linkedMap.put("fbfCosts4", resp.getFbfCosts04());
            linkedMap.put("fbfCosts5", resp.getFbfCosts05());
            linkedMap.put("fbfCosts6", resp.getFbfCosts06());
            linkedMap.put("fbfCosts7", resp.getFbfCosts07());
            linkedMap.put("fbfCosts8", resp.getFbfCosts08());
            linkedMap.put("fbfCosts9", resp.getFbfCosts09());
            linkedMap.put("fbfCosts10", resp.getFbfCosts10());
            linkedMap.put("fbfCosts11", resp.getFbfCosts11());
            linkedMap.put("fbfCosts12", resp.getFbfCosts12());
            linkedMap.put("hqfCosts1", resp.getHqfCosts01());
            linkedMap.put("hqfCosts2", resp.getHqfCosts02());
            linkedMap.put("hqfCosts3", resp.getHqfCosts03());
            linkedMap.put("hqfCosts4", resp.getHqfCosts04());
            linkedMap.put("hqfCosts5", resp.getHqfCosts05());
            linkedMap.put("hqfCosts6", resp.getHqfCosts06());
            linkedMap.put("hqfCosts7", resp.getHqfCosts07());
            linkedMap.put("hqfCosts8", resp.getHqfCosts08());
            linkedMap.put("hqfCosts9", resp.getHqfCosts09());
            linkedMap.put("hqfCosts10", resp.getHqfCosts10());
            linkedMap.put("hqfCosts11", resp.getHqfCosts11());
            linkedMap.put("hqfCosts12", resp.getHqfCosts12());
            list.add(linkedMap);
        }
        PageBean bean = new PageBean();
        bean.setCount((int) pageListResult.getCount());
        bean.setObjectList(list);
        return bean;
    }

    @Override
    public CommonResult<Void> saveRevenueForecData(Map<Integer, RevenueForecImp> cachedDataMap, ObjectId areaId, ObjectId planVerId, String buCode) {
        //校验数据：不为空
        Map<String, List<String>> oopCodeYmMap = new HashMap<>();
        List<String> oopCodeList = validMsg(cachedDataMap,oopCodeYmMap);
        //初始化数据：防止收入在导入的时候没有abpOopBgt数据
        planningBgtService.syncBgtByPlAndCycleTime(oopCodeYmMap,  planVerId);
        //查询abpOop数据
        List<AbpOop> abpOopList=queryDataByOopCode(planVerId,oopCodeList);
        Map<String, List<AbpOop>> oopCode2AbpOopMap = new HashMap<>();
        Map<ObjectId, AbpOop> abpOopMap = new HashMap<>();
        List<ObjectId> ids = new ArrayList<>();
        for (AbpOop abpOop : abpOopList) {
            ids.add(abpOop.getId());
            oopCode2AbpOopMap.computeIfAbsent(abpOop.getOopCode(), s -> new ArrayList<>()).add(abpOop);
            abpOopMap.put(abpOop.getId(),abpOop);
        }
        //2.查询abpOopBgt的inCome数据，便于算imCome中的poc
        List<AbpOopBgt> abpOopBgts = this.queryAbpOopBgt(ids);
        Map<String, AbpOopBgt> abpOopBgtMap = new HashMap<>();
        for (AbpOopBgt abpOopBgt : abpOopBgts) {
            String plCid = "";
            TeIdNameCn pl = abpOopBgt.getPl();
            if (pl != null) {
                plCid = pl.getCid().toHexString();
            }
            abpOopBgtMap.put(abpOopBgt.getOop().getCid().toHexString() + plCid + abpOopBgt.getYm(),abpOopBgt);
        }
        //查询区域下的省份信息
        Map<ObjectId,TeSysDef> provMap = queryProv(areaId,buCode);
        //3.遍历excel数据,判断数据，并且设置imcome下的amt数据
        for (Map.Entry<Integer, RevenueForecImp> entry : cachedDataMap.entrySet()) {
            Integer rowIndex = entry.getKey();
            RevenueForecImp data = entry.getValue();
            String oopCode = data.getCode();
            List<AbpOop> abpOops = oopCode2AbpOopMap.get(oopCode);
            if (CollectionUtils.isEmpty(abpOops)) {
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：经营计划代码不存在，请修改后重新导入！", rowIndex));
            }
            AbpOop abpOop = abpOops.get(0);
            TeIdNameCn prov = abpOop.getProv();
            if (prov==null){
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：经营计划所在省份不在该区域内,该经营计划省份为空", rowIndex));
            }
            TeSysDef sysDef = provMap.get(prov.getCid());
            if (sysDef==null){
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：经营计划所在省份不在该区域内,经营计划的省份为%s", rowIndex,prov.getName()));
            }
            List<AbpOopPl> pls = abpOop.getPls();
            if (CollectionUtils.isEmpty(pls)) {
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：经营计划代码下没有产品线，请修改后重新导入！", rowIndex));
            }
            String amt = data.getAmt();
            String ym = serviceManager.getYm(data.getYm(),null);
            String sumKey = abpOop.getId().toHexString() + ym;
            AbpOopBgt sumAbpOopBgt = abpOopBgtMap.get(sumKey);
            //校验对应的abpOopBgt的汇总数据是否存在
            if (sumAbpOopBgt==null) {
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：缺少该月份[%s]的预算汇总数据或者该月份已实际化不可修改", rowIndex,ym));
            }
            Income income = sumAbpOopBgt.getIncome();
            if (income == null) {
                income = new Income();
                sumAbpOopBgt.setIncome(income);
            }
            income.setAmt(BigDecimalUtils.addDouble(Double.valueOf(amt),0d,2));
            for (AbpOopPl abpOopPl : pls) {
                TeIdNameCn pl = abpOopPl.getPl();
                if (pl == null||!abpOopPl.getIsValid()) {
                    continue;
                }
                //每条产品线的amt设置：按照产品线的占比设值
                double incomeAmt = BigDecimalUtils.divideDouble(abpOopPl.getPct()*Double.parseDouble(amt),100,2);
                String key = abpOop.getId().toHexString() + pl.getCid().toHexString() + ym;
                AbpOopBgt abpOopBgt = abpOopBgtMap.get(key);
                if (abpOopBgt==null) {
                    throw BusinessException.initExc(String.format("导入失败!行号[%s]：产品线[%s]缺少预算数据或者该产品线已实际化不可修改收入", rowIndex, pl.getName()));
                }
                Income come = abpOopBgt.getIncome();
                if (come == null) {
                    come = new Income();
                    abpOopBgt.setIncome(come);
                }
                come.setAmt(incomeAmt);
            }
        }
        //计算每个abpOopBgt的poc值：必须按照月份计算
        Map<ObjectId, Map<String, List<AbpOopBgt>>> bgtMap = abpOopBgtMap.values().stream().collect(Collectors.groupingBy(s -> s.getOop().getCid(), Collectors.groupingBy(AbpOopBgt::getYm)));
        for (Map.Entry<ObjectId, Map<String, List<AbpOopBgt>>> entry : bgtMap.entrySet()) {
            Map<String, List<AbpOopBgt>> ymMap = entry.getValue();
            AbpOop abpOop = abpOopMap.get(entry.getKey());
            TeIdNameCn type = abpOop.getType();
            Map<ObjectId, Double> sumAmtMap = new HashMap<>();
            //对每个oopCid下的数据计算poc
            for (String ym : serviceManager.getYm()) {
                List<AbpOopBgt> bgts = ymMap.get(ym);
                if (CollectionUtils.isEmpty(bgts)){
                    continue;
                }
                for (AbpOopBgt bgt : bgts) {
                    TeIdNameCn pl = bgt.getPl();
                    ObjectId key=null;
                    if (pl!=null){
                        key=pl.getCid();
                    }
                    Income income = bgt.getIncome();
                    if (income==null){
                        income=new Income();
                        bgt.setIncome(income);
                    }
                    double amt=income.getAmt()==null?0d:income.getAmt();
                    double sumAmt = sumAmtMap.get(key)==null?amt:sumAmtMap.get(key)+amt;
                    sumAmtMap.put(key,sumAmt);

                    Double netSalesM = abpOop.getNetSalesM();
                    double poc=0d;
                    //设置不同类型的项目的poc值，分两大类，不属于这两大类的不做计算
                    if (type.getCid().equals(Constant.FORECAST_ORDER_ID) || type.getCid().equals(Constant.SIGNED_CURRENT_YEAR_ID)
                            || type.getCid().equals(Constant.R2D_PROJECTS_ID)||type.getCid().equals(Constant.FUTURE_FORECAST_ORDER_ID)) {
                        if (netSalesM==null||netSalesM==0){
                            poc=0;
                        }else {
                            poc = BigDecimalUtils.divideDouble(sumAmt ,netSalesM,3);
                        }
                    } else if (type.getCid().equals(Constant.HISTORICAL_SIGNING_ID)) {
                        if (netSalesM==null||netSalesM==0){
                            poc=0;
                        }else {
                            double backlogAmt=abpOop.getBacklogAmt()==null?0d:abpOop.getBacklogAmt();
                            poc = BigDecimalUtils.divideDouble((netSalesM - backlogAmt + sumAmt) , netSalesM,3);
                        }
                    }
                    income.setPoc(poc);
                }
            }

        }
        //批量更新查询条件
        List<List<IDbCondition>> queryConditions = new ArrayList<>();
        //批量更新更新条件
        List<List<UpdataData>> updateConditions = new ArrayList<>();
        int year = DateUtil.getYear(new Date());
        for (AbpOopBgt bgt : abpOopBgtMap.values()) {
            String ymYear = bgt.getYm().substring(0, 4);
            if (Integer.parseInt(ymYear)!=year){
                continue;
            }
            //组装更新条件
            List<IDbCondition> query = new ArrayList<>();
            query.add(new DC_E(DbFieldName.common__id, bgt.getId()));
            queryConditions.add(query);
            List<UpdataData> update = new ArrayList<>();
            update.add(new UpdataData(DbFieldName.AbpOopBgt.income, bgt.getIncome()));
            updateConditions.add(update);
        }
        BulkWriteResult bulkWriteResult = abpOopBgtDao.batchUpdate(queryConditions, updateConditions, false);
        log.info("====>BusinessPlanServiceImpl saveRevenueForecData ==>导入结果:{}", bulkWriteResult.wasAcknowledged());
        return CommonResult.success();
    }


    private List<String> validMsg(Map<Integer, RevenueForecImp> cachedDataMap, Map<String, List<String>> oopCodeYmMap) {
        List<String> oopCodeList = new ArrayList<>();
        for (Map.Entry<Integer, RevenueForecImp> entry : cachedDataMap.entrySet()) {
            RevenueForecImp value = entry.getValue();
            Integer rowIndex = entry.getKey();
            String amt = value.getAmt();
            if (StringUtil.isNull(amt)){
                throw  BusinessException.initExc(String.format("导入失败!行号[%s]：金额不能为空", rowIndex));
            }
            String code = value.getCode();
            if (StringUtil.isNull(code)){
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：经营计划代码不能为空！", rowIndex));
            }
            String ym = value.getYm();
            if (StringUtil.isNull(ym)){
                throw BusinessException.initExc(String.format("导入失败!行号[%s]：月份不能为空", rowIndex));
            }
            oopCodeList.add(code);
            String month = serviceManager.getYm(ym,null);
            oopCodeYmMap.computeIfAbsent(code,s->new ArrayList<>()).add(month);
        }
        return oopCodeList;
    }

    private Map<ObjectId, TeSysDef> queryProv(ObjectId areaId, String buCode) {
        List<TeSysDef> sysDefs = queryProvByArearId(areaId, buCode);
        if (CollectionUtils.isEmpty(sysDefs)){
            throw BusinessException.initExc("该区域下的省份为空,不能导入");
        }
        List<TeIdNameCn> cndtItems = sysDefs.get(0).getCndtItems();
        if (CollectionUtils.isEmpty(cndtItems)){
            throw BusinessException.initExc("该层级不是区域，不能导入");
        }
        boolean flag=true;
        for (TeIdNameCn cndtItem : cndtItems) {
            if (cndtItem.getCid().equals(areaId)&&cndtItem.getCodeName().equals(Constant.REGION)){
                flag=false;
                break;
            }
        }
        if (flag){
            throw BusinessException.initExc("该层级不是区域，不能导入");
        }
        return sysDefs.stream().collect(Collectors.toMap(TeSysDef::getId, s -> s));
    }

    private List<AbpOop> queryDataByOopCode(ObjectId planVerId, List<String> oopCodeList) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVerId));
        conds.add(new DC_I<>(DbFieldName.AbpOop.oopCode, oopCodeList));
        conds.add(new DC_E(DbFieldName.common_isValid, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.AbpOop.oopCode);
        fieldNames.add(DbFieldName.AbpOop.pls);
        fieldNames.add(DbFieldName.AbpOop.netSalesM);
        fieldNames.add(DbFieldName.AbpOop.type);
        fieldNames.add(DbFieldName.AbpOop.prov);
        fieldNames.add(DbFieldName.AbpOop.backlogAmt);
        return abpOopDao.findByFieldAndConds(conds, fieldNames);
    }


    @Override
    public List<TeSysDef> queryEmpConfigItemsByRoleId(TeSysUser user) {
        List<SysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.queryAreaWithAdmin(user.getId());
        if (CollectionUtils.isEmpty(sysDefRoleUsers)) {
            log.info("queryNeedForecastProvListByBpVerId - 该用户没有角色权限");
            return null;
        }
        List<ObjectId> defIds = sysDefRoleUsers.stream().map(SysDefRoleUser::getDefId).collect(Collectors.toList());
        return iSysDefDao.queryDataByIdsAndBu(defIds,user.getSbuId());
    }

    @Override
    public PageListResult queryNeedApprovePlListByBpVerIdAndGradeId(TeSysUser user, PlanApprovalReq req) {
        PageListResult pageListResult = new PageListResult();
        ObjectId areaId = req.getAreaId();
        //1.查询该区域下的省份
        List<TeSysDef> sysDefs = queryProvByArearId(areaId,user.getSbuId());
        if (CollectionUtils.isEmpty(sysDefs)){
            return null;
        }
        // 仅展示提交的省份
        List<TeIdNameCn> teIdNameCns = queryRepulseableListByBpVerIdAndGradeId(req.getPlanVersion(), req.getAreaId(), user);
//        List<ObjectId> provIds = teIdNameCns.stream().map(info -> info.getCid()).collect(Collectors.toList());
        if (teIdNameCns.size() == 0) {
            return pageListResult;
        }
        // 仅展示已提交的省份/ 区域 / 大区信息
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.AbpOprt.planVer.dot(DFN.common_cid), req.getPlanVersion()));
        conds.add(new DC_E(DFN.AbpOprt.status.dot(DFN.common_cid), Constant.ABP_OPRT_SUBMITTED_ID));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.AbpOprt.planVer);
        fieldNames.add(DFN.AbpOprt.oprt);
        fieldNames.add(DFN.AbpOprt.dept);
        fieldNames.add(DFN.AbpOprt.status);
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, fieldNames);
        List<ObjectId> abpOprtIds = abpOprts.stream().map(info -> info.getDept().getCid()).collect(Collectors.toList());
        List<ObjectId> sysDefsList = sysDefs.stream().map(info -> info.getId()).collect(Collectors.toList());
        List<ObjectId> provIds = new ArrayList<>();
        for (ObjectId id : abpOprtIds) {
            if (sysDefsList.contains(id)) {
                provIds.add(id);
            }
        }
        //2.查询abpOop数据
        List<org.bson.Document> list = queryPlanApprovalPlConditions(req, provIds);
        List<org.bson.Document> queryResult = abpOopDao.aggregate(list);
        List<Map<String, Integer>> total = (List<Map<String, Integer>>) queryResult.get(0).get("total");
        if (CollectionUtils.isEmpty(total)) {
            return null;
        }
        //总数
        Integer count = total.get(0).get("total");
        List<ObjectId>ids=new ArrayList<>();
        List data = (List) queryResult.get(0).get("data");
        for (Object object : data) {
            Document dbObject = (Document) object;
            List abpOop = (List) dbObject.get("abpOop");
            for (Object o : abpOop) {
                Document basicDBObject = (Document) o;
                ObjectId id = (ObjectId) basicDBObject.get("_id");
                ids.add(id);
            }

        }
        //查询对应的abpOopBgt数据
        List<AbpOopBgt> abpOopBgts = abpOopBgtDao.queryAbpOopBgtByOopCids(ids);
        Map<String, List<AbpOopBgt>> abpOopBgtMap = abpOopBgts.stream().collect(Collectors.groupingBy(s -> s.getOop().getCid().toHexString() + s.getPl().getCid().toHexString()));
        //3.遍历查询结果，处理业务逻辑
        List<PlanApprovalPlResp> result = new ArrayList<>();
        int rn = 1;
        for (Object object : data) {
            Document dbObject = (Document) object;
            String provName = (String) dbObject.get("provName");
            String bigRegionName = (String) dbObject.get("bigRegionName");
            String regionName = (String) dbObject.get("regionName");
            PlanApprovalPlResp resp = new PlanApprovalPlResp();
            resp.setAreaName(regionName);
            resp.setBigAreaName(bigRegionName);
            resp.setProvName(provName);
            resp.setRn(rn++);
            List abpOop = (List) dbObject.get("abpOop");
            double endYearBacklog = 0d;
            double newOrderAmt = 0d;
            double income = 0d;
            double totalCost = 0d;
            double directCost = 0d;
            double mfeeCost = 0d;
            double convetHc = 0d;
            for (Object o : abpOop) {
                Document basicDBObject = (Document) o;
                ObjectId id = (ObjectId) basicDBObject.get("_id");
                Double netSalesM = (Double) basicDBObject.get("netSalesM");
                Document typeObject = (Document) basicDBObject.get("type");
                TeIdNameCn type = JSON.parseObject(typeObject.toJson(), TeIdNameCn.class);
                Document plsObject = (Document) basicDBObject.get("pls");
                Document pl = (Document) plsObject.get("pl");
                ObjectId plCid = (ObjectId) pl.get("cid");
                AbpOopPl pls = JSON.parseObject(plsObject.toJson(), AbpOopPl.class);
                resp.setPlName(pls.getPl().getName());
                if ("BackLog".equals(type.getCodeName())) {
                    endYearBacklog += netSalesM* pls.getPct();
                } else {
                    newOrderAmt += pls.getAmt();
                }
                String key = id.toHexString() + plCid;
                List<AbpOopBgt> abpOopBgtList = abpOopBgtMap.get(key);
                if (CollectionUtils.isEmpty(abpOopBgtList)){
                    continue;
                }
                for (AbpOopBgt abpOopBgt : abpOopBgtList) {
                    Income come = abpOopBgt.getIncome();
                    if (come != null) {
                        income += come.getAmt();
                    }
                    // 判空
                    double rgFee = (abpOopBgt.getRgFee() != null) ? abpOopBgt.getRgFee() : 0d;
                    double diningFee = (abpOopBgt.getDiningFee() != null) ? abpOopBgt.getDiningFee() : 0d;
                    double travelFee = (abpOopBgt.getTravelFee() != null) ? abpOopBgt.getTravelFee() : 0d;
                    double otherFee = (abpOopBgt.getOtherFee() != null) ? abpOopBgt.getOtherFee() : 0d;
                    double jslfbFee = (abpOopBgt.getJslfbFee() != null) ? abpOopBgt.getJslfbFee() : 0d;
                    double bgjhqFee = (abpOopBgt.getBgjhqFee() != null) ? abpOopBgt.getBgjhqFee() : 0d;
                    double dsfdcFee = (abpOopBgt.getDsfdcFee() != null) ? abpOopBgt.getDsfdcFee() : 0d;

                    totalCost += rgFee + diningFee + travelFee + otherFee + jslfbFee+ bgjhqFee + dsfdcFee;
                    directCost += diningFee + travelFee + otherFee;
//                    Mfee mfee = abpOopBgt.getMfee();
//                    if (mfee != null) {
//                        mfeeCost += mfee.getAmt();
//                    }
                    List<RgInfo> rgInfos = abpOopBgt.getRgInfo();
                    for (RgInfo rgInfo : rgInfos) {
                        TeIdNameCn empType = rgInfo.getEmpType();
                        if (empType == null) {
                            continue;
                        }
                        ObjectId cid = empType.getCid();
                        Double rgNum = (rgInfo.getRgNum() != null) ? rgInfo.getRgNum() : 0d;
                        if (cid.equals(Constant.REGULAR_EMPLOYEE_ID)) {
                            convetHc += rgNum;
                        } else if (cid.equals(Constant.OUTSOURCED_STAFF_ID)) {
                            convetHc += rgNum * 0.7;
                        } else if (cid.equals(Constant.TRAINEE_ID)) {
                            convetHc += rgNum * 0.2;
                        } else {
                            convetHc += 0;
                        }
                    }
                }
            }
            endYearBacklog = BigDecimalUtils.divideDouble(endYearBacklog,1000,5);
            newOrderAmt = BigDecimalUtils.divideDouble(newOrderAmt,1000,5);
            income = BigDecimalUtils.divideDouble(income,1000,5);
            totalCost = BigDecimalUtils.divideDouble(totalCost,1000,5);
            directCost = BigDecimalUtils.divideDouble(directCost,1000,5);
            mfeeCost = BigDecimalUtils.divideDouble(mfeeCost,1000,5);
            convetHc = BigDecimalUtils.divideDouble(convetHc,12,5);
            resp.setEndYearBacklog(String.valueOf(endYearBacklog));
            resp.setNewOrderAmt(String.valueOf(newOrderAmt));
            resp.setIncome(String.valueOf(income));
            double incomRatio = endYearBacklog + newOrderAmt == 0 ? 0 : BigDecimalUtils.multiplyDouble( income / (endYearBacklog + newOrderAmt),100,5);
            resp.setIncomeRatio(incomRatio + "%");
            resp.setTotalCost(String.valueOf(totalCost));
            resp.setDirectCost(String.valueOf(directCost));
            resp.setProGrossProfit(String.valueOf(income - totalCost));
            double grossProfitRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble( (income - totalCost) / income,100,5);
            resp.setGrossProfitRatio(grossProfitRatio + "%");
            resp.setMfeeCost(String.valueOf(mfeeCost));
            resp.setNetProfit(String.valueOf(income - totalCost - mfeeCost));
            double netProfitRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble(Double.parseDouble(resp.getNetProfit()) / income,100,5);
            resp.setNetProfitRatio(netProfitRatio + "%");
            resp.setConvetHc(String.valueOf(convetHc));
            double dailyCapitaDirectCost = income == 0 ? 0 : BigDecimalUtils.divideDouble(directCost / income / 12 ,21.5,5);
            resp.setDailyCapitaDirectCost(String.valueOf(dailyCapitaDirectCost));
            double directCostIncomeRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble(directCost / income,100,5);
            resp.setDirectCostIncomeRatio(directCostIncomeRatio + "%");
            double capitaNewOrderAmt = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(newOrderAmt,convetHc,5);
            resp.setCapitaNewOrderAmt(String.valueOf(capitaNewOrderAmt));
            double capitaCost = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(totalCost + mfeeCost,convetHc,5);
            resp.setCapitaCost(String.valueOf(capitaCost));
            double capitaProfit = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(income - totalCost - mfeeCost,convetHc,5);
            resp.setCapitaProfit(String.valueOf(capitaProfit));
            resp.setTypeName("计划");
            resp.setCnt(Math.toIntExact(count));
            result.add(resp);
        }
        pageListResult.setCount(count);
        pageListResult.setList(result);
        return pageListResult;
    }




    private List<org.bson.Document> queryPlanApprovalPlConditions(PlanApprovalReq req, List<ObjectId> provIds) {
        List<org.bson.Document> list = new ArrayList<>();
        Document in = new Document("$in", provIds);
        //match匹配条件
        Document matchConditions = new Document();
        matchConditions.append(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid).n(), req.getPlanVersion())
                .append(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).n(), in)
                .append(DbFieldName.common_isValid.n(), true)
                .append(DbFieldName.AbpOop.isDeleted.n(), false);
        Document match = new Document("$match", matchConditions);
        list.add(match);

        //过滤abpOop字段
        Document firstProjcetCond = new Document();
        firstProjcetCond.append(DbFieldName.common__id.n(),1)
                .append("provId",DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).$n())
                .append(DbFieldName.AbpOop.pls.n(),1)
                .append(DbFieldName.AbpOop.type.n(), 1)
                .append(DbFieldName.AbpOop.netSalesM.n(), 1);
        Document firstProjcet = new Document("$project", firstProjcetCond);
        list.add(firstProjcet);
        //把pls数组拆分开
        Document unwindPls = new Document("$unwind", DbFieldName.AbpOop.pls.$n());
        list.add(unwindPls);

        //匹配有效的产品线
        Document plMatchCond = new Document(DbFieldName.AbpOop.pls.dot(DbFieldName.common_isValid).n(),true);
        plMatchCond.put(DbFieldName.AbpOop.pls.dot(DbFieldName.AbpOop.pl).n(),new Document("$ne",null));
        Document plMatch = new Document("$match", plMatchCond);
        list.add(plMatch);

        //分组：根据产品线和省份分组
        Document id = new Document("plId",DbFieldName.AbpOop.pls.dot(DbFieldName.AbpOop.pl.dot(DbFieldName.common_cid)).$n());
        id.put("provId","$provId");
        Document abpOop = new Document("$push","$$ROOT");
        Document groupCond = new Document(DbFieldName.common__id.n(),id);
        groupCond.put(DbTableName.ABP_OOP.getName(),abpOop);
        Document group = new Document("$group", groupCond);
        list.add(group);

        //这里要求返回给前端的数据，是按照大区,区域,省份的defNo进行排序
        //关联sysDef表,查询省份信息
        Document provLookupCond = new Document();
        provLookupCond.append("from",DbTableName.SYS_DEF.getName())
                .append("localField","_id.provId")
                .append("foreignField",DbFieldName.common__id.n())
                .append("as",DbFieldName.AbpOop.prov.n());
        Document provLookup = new Document("$lookup", provLookupCond);
        list.add(provLookup);

        //拆开prov数组
        Document unwindProv = new Document("$unwind", DbFieldName.AbpOop.prov.$n());
        list.add(unwindProv);

        //映射字段，得到大区和区域
        Document secProjcetCond = new Document();
        secProjcetCond.append(DbFieldName.common__id.n(),1)
                .append("provName","$prov.defName")
                .append(DbTableName.ABP_OOP.getName(), 1)
                .append("provNo","$prov.defNo")
                .append("bigRegion", new Document("$arrayElemAt",Arrays.asList("$prov.cndtItems",0)))
                .append("region", new Document("$arrayElemAt",Arrays.asList("$prov.cndtItems",1)));
        Document secProjcet = new Document("$project", secProjcetCond);
        list.add(secProjcet);

        //关联查询大区，获取大区名称和defNo
        Document bigRegionLookupCond = new Document();
        bigRegionLookupCond.append("from",DbTableName.SYS_DEF.getName())
                .append("localField","bigRegion.cid")
                .append("foreignField",DbFieldName.common__id.n())
                .append("as","bigRegionArea");
        Document bigRegionLookup = new Document("$lookup", bigRegionLookupCond);
        list.add(bigRegionLookup);

        //拆开bigRegionArea数组
        Document unwindBigRegion = new Document("$unwind", "$bigRegionArea");
        list.add(unwindBigRegion);

        //关联查询区域，获取区域名称和defNo
        Document regionLookupCond = new Document();
        regionLookupCond.append("from",DbTableName.SYS_DEF.getName())
                .append("localField","region.cid")
                .append("foreignField",DbFieldName.common__id.n())
                .append("as","regionArea");
        Document regionLookup = new Document("$lookup", regionLookupCond);
        list.add(regionLookup);

        //拆开regionArea数组
        Document unwindRegion = new Document("$unwind", "$regionArea");
        list.add(unwindRegion);

        //映射字段
        Document thirProjcetCond = new Document();
        thirProjcetCond.append(DbFieldName.common__id.n(),1)
                .append("provName",1)
                .append(DbTableName.ABP_OOP.getName(), 1)
                .append("provNo",1)
                .append("bigRegionName", "$bigRegionArea.defName")
                .append("bigRegionDefNo", "$bigRegionArea.defNo")
                .append("regionName","$regionArea.defName")
                .append("regionDefNo","$regionArea.defNo");
        Document thirProjcet = new Document("$project", thirProjcetCond);
        list.add(thirProjcet);

        //排序,按照产品线id,大区,区域,省份的defNo进行排序
        Document sortCond = new Document();
        sortCond.append("_id.plId",1).append("bigRegionDefNo",1)
                .append("regionDefNo",1).append("provNo",1);
        Document sort = new Document("$sort", sortCond);
        list.add(sort);

        //分页查询数据和数量
        Document facetCond = new Document("total", Collections.singletonList(new Document("$count", "total")));
        facetCond.put("data",Arrays.asList(new Document("$skip",req.getCurPage()*req.getPageSize() ),new Document("$limit",req.getPageSize() )));
        Document facet = new Document("$facet", facetCond);
        list.add(facet);
        return list;
    }

    @Override
    public PageListResult queryNeedApproveListByBpVerIdAndGradeId(TeSysUser user, PlanApprovalReq req) {
        PageListResult pageListResult = new PageListResult();
        ObjectId areaId = req.getAreaId();
        //1.查询该区域下的省份
        List<TeSysDef> sysDefs = queryProvByArearId(areaId,user.getSbuId());

        // 仅展示已提交的省份/ 区域 / 大区信息
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.AbpOprt.planVer.dot(DFN.common_cid), req.getPlanVersion()));
        conds.add(new DC_E(DFN.AbpOprt.status.dot(DFN.common_cid), Constant.ABP_OPRT_SUBMITTED_ID));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.AbpOprt.planVer);
        fieldNames.add(DFN.AbpOprt.oprt);
        fieldNames.add(DFN.AbpOprt.dept);
        fieldNames.add(DFN.AbpOprt.status);
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, fieldNames);
        List<ObjectId> list = abpOprts.stream().map(info -> info.getDept().getCid()).collect(Collectors.toList());
        List<TeIdNameCn> teIdNameCns = queryRepulseableListByBpVerIdAndGradeId(req.getPlanVersion(), req.getAreaId(), user);
        if (teIdNameCns.size() == 0) {
            return pageListResult;
        }
//        List<ObjectId> list = teIdNameCns.stream().map(info -> info.getCid()).collect(Collectors.toList());
        List<ObjectId> provIds = new ArrayList<>();
        Map<ObjectId, Map<String, String>> map = new HashMap<>();
        for (TeSysDef sysDef : sysDefs) {
            ObjectId id = sysDef.getId();
            if (list.contains(id)) {
                provIds.add(id);
                List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
                Map<String, String> nameMap = new HashMap<>();
                for (TeIdNameCn cndtItem : cndtItems) {
                    nameMap.put(cndtItem.getCodeName(), cndtItem.getName());
                }
                map.put(id, nameMap);
            }
        }
        //2.查询区域对应的操作状态
        AbpOprt abpOprt = queryAbpOprt(req, areaId);
        //3.分页查询
        List<org.bson.Document> queryConditions = queryPlanApprovalConditions(req, provIds);
        queryConditions.add(new Document("$skip", req.getCurPage() * req.getPageSize()));
        queryConditions.add(new Document("$limit", req.getPageSize()));
        Document sort = new Document(DbFieldName.common__id.n(), 1);
        queryConditions.add(new Document("$sort", sort));
        List<org.bson.Document> queryResult = abpOopDao.aggregate(queryConditions);

        List<org.bson.Document> countQueryConditions = queryPlanApprovalConditions(req, provIds);
        countQueryConditions.add(new Document("$count", "count"));
        List<org.bson.Document> countResult = abpOopDao.aggregate(countQueryConditions);
        long count = 0;
        if (!CollectionUtils.isEmpty(countResult)) {
            count = (int) countResult.get(0).get("count");
        }
        List<PlanApprovalProvResp> result = new ArrayList<>();
        int rn = 1;
        for (org.bson.Document dbObject : queryResult) {
            PlanApprovalProvResp provResp = new PlanApprovalProvResp();
            ObjectId provId = (ObjectId) dbObject.get("_id");
            Map<String, String> areaMap = map.get(provId);
            provResp.setAreaName(areaMap.get(Constant.REGION));
            provResp.setBigAreaName(areaMap.get(Constant.BIG_REGION));
            provResp.setRn(rn++);
            provResp.setCnt(Math.toIntExact(count));
            TeIdNameCn status = abpOprt.getStatus();
            if (status != null) {
                provResp.setOprtStatus(status.getName());
            }
            provResp.setOprtDesc(abpOprt.getDesc());
            provResp.setTypeName("计划");
            List abpOopObject = (List) dbObject.get("abpOop");
            List<AbpOopAndBgt> abpOopAndBgts = JSON.parseArray(JSON.toJSONString(abpOopObject), AbpOopAndBgt.class);
            double endYearBacklog = 0d;
            double newOrderAmt = 0d;
            double income = 0d;
            double totalCost = 0d;
            double directCost = 0d;
            double mfeeCost = 0d;
            double convetHc = 0d;

            for (AbpOopAndBgt abpOopAndBgt : abpOopAndBgts) {
                provResp.setProvName(abpOopAndBgt.getProv().getName());
                Double netSalesM = abpOopAndBgt.getNetSalesM();
                TeIdNameCn type = abpOopAndBgt.getType();
                if ("BackLog".equals(type.getCodeName())) {
                    endYearBacklog += netSalesM;
                } else {
                    newOrderAmt += netSalesM;
                }
                List<AbpOopBgt> abpOopBgts = abpOopAndBgt.getAbpOopBgt();
                for (AbpOopBgt abpOopBgt : abpOopBgts) {
                    Income come = abpOopBgt.getIncome();
                    if (come == null || come.getAmt() == null) {
                        income = income;
                    } else {
                        income += come.getAmt();
                    }
                    // 判空
                    double rgFee = (abpOopBgt.getRgFee() != null) ? abpOopBgt.getRgFee() : 0d;
                    double diningFee = (abpOopBgt.getDiningFee() != null) ? abpOopBgt.getDiningFee() : 0d;
                    double travelFee = (abpOopBgt.getTravelFee() != null) ? abpOopBgt.getTravelFee() : 0d;
                    double otherFee = (abpOopBgt.getOtherFee() != null) ? abpOopBgt.getOtherFee() : 0d;
                    double jslfbFee = (abpOopBgt.getJslfbFee() != null) ? abpOopBgt.getJslfbFee() : 0d;
                    double bgjhqFee = (abpOopBgt.getBgjhqFee() != null) ? abpOopBgt.getBgjhqFee() : 0d;
                    double dsfdcFee = (abpOopBgt.getDsfdcFee() != null) ? abpOopBgt.getDsfdcFee() : 0d;
//                    totalCost += abpOopBgt.getRgFee() + abpOopBgt.getDiningFee() + abpOopBgt.getTravelFee() + abpOopBgt.getOtherFee() + abpOopBgt.getJslfbFee() + abpOopBgt.getBgjhqFee() + abpOopBgt.getDsfdcFee();
//                    directCost += abpOopBgt.getDiningFee() + abpOopBgt.getTravelFee() + abpOopBgt.getOtherFee();

                    totalCost += rgFee + diningFee +travelFee + otherFee + jslfbFee + bgjhqFee + dsfdcFee;
                    directCost += diningFee + travelFee + otherFee;
//                    Mfee mfee = abpOopBgt.getMfee();
//                    if (mfee != null) {
//                        mfeeCost += mfee.getAmt();
//                    }
                    List<RgInfo> rgInfos = abpOopBgt.getRgInfo();
                    for (RgInfo rgInfo : rgInfos) {
                        TeIdNameCn empType = rgInfo.getEmpType();
                        if (empType == null) {
                            continue;
                        }
                        Double rgNum = (rgInfo.getRgNum() != null) ? rgInfo.getRgNum() : 0d;
                        ObjectId cid = empType.getCid();
                        if (cid.equals(Constant.REGULAR_EMPLOYEE_ID)) {
                            convetHc += rgNum;
                        } else if (cid.equals(Constant.OUTSOURCED_STAFF_ID)) {
                            convetHc += rgNum * 0.7;
                        } else if (cid.equals(Constant.TRAINEE_ID)) {
                            convetHc += rgNum * 0.2;
                        } else {
                            convetHc += 0;
                        }
                    }
                }
            }
            endYearBacklog = BigDecimalUtils.divideDouble(endYearBacklog,1000,5);
            newOrderAmt = BigDecimalUtils.divideDouble(newOrderAmt,1000,5);
            income = BigDecimalUtils.divideDouble(income,1000,5);
            totalCost = BigDecimalUtils.divideDouble(totalCost,1000,5);
            directCost = BigDecimalUtils.divideDouble(directCost,1000,5);
            mfeeCost = BigDecimalUtils.divideDouble(mfeeCost,1000,5);
            convetHc = BigDecimalUtils.divideDouble(convetHc,12,5);
            provResp.setEndYearBacklog(String.valueOf(endYearBacklog));
            provResp.setNewOrderAmt(String.valueOf(newOrderAmt));
            provResp.setIncome(String.valueOf(income));
            double incomeRatio = (endYearBacklog + newOrderAmt) == 0 ? 0 : BigDecimalUtils.multiplyDouble(income / (endYearBacklog + newOrderAmt),100,5);
            provResp.setIncomeRatio(incomeRatio + "%");
            provResp.setTotalCost(String.valueOf(totalCost));
            provResp.setDirectCost(String.valueOf(directCost));
            double proGrossProfit = BigDecimalUtils.subtractDouble(income,totalCost,5);
            provResp.setProGrossProfit(String.valueOf(proGrossProfit));
            double grossProfitRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble((income - totalCost) / income,100,5);
            provResp.setGrossProfitRatio(grossProfitRatio + "%");
            provResp.setMfeeCost(String.valueOf(mfeeCost));
            double netProfit = BigDecimalUtils.subtractDouble(income - totalCost,mfeeCost,5);
            provResp.setNetProfit(String.valueOf(netProfit));
            double netProfitRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble(netProfit / income,100,5);
            provResp.setNetProfitRatio(netProfitRatio + "%");
            provResp.setConvetHc(String.valueOf(convetHc));
            double dailyCapitaDirectCost = income == 0 ? 0 : BigDecimalUtils.divideDouble(directCost / income / 12,21.5,5);
            provResp.setDailyCapitaDirectCost(String.valueOf(dailyCapitaDirectCost));

            double directCostIncomeRatio = income == 0 ? 0 : BigDecimalUtils.multiplyDouble(directCost / income,100,5);
            provResp.setDirectCostIncomeRatio(directCostIncomeRatio + "%");

            double capitaNewOrderAmt = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(newOrderAmt,convetHc,5);
            provResp.setCapitaNewOrderAmt(String.valueOf(capitaNewOrderAmt));

            double capitaCost = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(totalCost + mfeeCost,convetHc,5);
            provResp.setCapitaCost(String.valueOf(capitaCost));

            double capitaProfit = convetHc == 0 ? 0 : BigDecimalUtils.divideDouble(income - totalCost - mfeeCost,convetHc,5);
            provResp.setCapitaProfit(String.valueOf(capitaProfit));
            result.add(provResp);
        }
        pageListResult.setList(result);
        pageListResult.setCount(count);
        return pageListResult;
    }


    private AbpOprt queryAbpOprt(PlanApprovalReq req, ObjectId areaId) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_cid), areaId));
        conds.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), req.getPlanVersion()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.AbpOprt.dept);
        fieldNames.add(DbFieldName.AbpOprt.status);
        fieldNames.add(DbFieldName.AbpOprt.desc);
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(abpOprts)) {
            return new AbpOprt();
        }
        return abpOprts.get(0);
    }


    private List<org.bson.Document> queryPlanApprovalConditions(PlanApprovalReq req, List<ObjectId> provIds) {
        List<org.bson.Document> list = new ArrayList<>();
        Document in = new Document("$in", provIds);
        Document matchConditions = new Document();
        matchConditions.append(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid).n(), req.getPlanVersion())
                .append(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).n(), in)
                .append(DbFieldName.common_isValid.n(), true);
        Document match = new Document("$match", matchConditions);
        list.add(match);

        Document let = new Document(DbFieldName.common_id.n(), DbFieldName.common__id.$n());
        Document idEq = new Document("$eq", Arrays.asList("$$id", DbFieldName.AbpOopBgt.oop.dot(DbFieldName.common_cid).$n()));
        Document lookUpMatchConds = new Document();
        lookUpMatchConds.append("$expr", idEq).append("pl", null);
        Document lookUpMatch = new Document("$match", lookUpMatchConds);
        Document lookUpCondition = new Document();
        lookUpCondition.append("from", DbTableName.ABP_OOP_BGT.getName())
                .append("let", let)
                .append("pipeline", Collections.singletonList(lookUpMatch))
                .append("as", DbTableName.ABP_OOP_BGT.getName());
        Document lookUp = new Document("$lookup", lookUpCondition);
        list.add(lookUp);

        Document projectCondition = new Document();
        projectCondition.append(DbFieldName.common__id.n(), 1)
                .append(DbFieldName.AbpOop.prov.n(), 1)
                .append(DbFieldName.AbpOop.type.n(), 1)
                .append(DbFieldName.AbpOop.netSalesM.n(), 1)
                .append(DbTableName.ABP_OOP_BGT.getName(), 1);
        Document project = new Document("$project", projectCondition);
        list.add(project);

        Document abpOop = new Document("$push", "$$ROOT");
        Document groupCondition = new Document();
        groupCondition.append("_id", "$prov.cid").append("abpOop", abpOop);
        Document group = new Document("$group", groupCondition);
        list.add(group);
        return list;
    }

    @Override
    public PageBean exportNeedApproveListByBpVerIdAndGradeId(TeSysUser user, PlanApprovalReq req) {
        PageListResult pageListResult = this.queryNeedApproveListByBpVerIdAndGradeId(user, req);
        List<PlanApprovalProvResp> queryResult = (List<PlanApprovalProvResp>) pageListResult.getList();
        List<Object> list = new ArrayList<>();
        LinkedHashMap<Integer, String> titleMap = new LinkedHashMap<>();
        titleMap.put(0, "大区");
        titleMap.put(1, "区域");
        titleMap.put(2, "省份");
        titleMap.put(3, "状态");
        titleMap.put(4, "审批说明");
        titleMap.put(5, "类型");
        titleMap.put(6, "上一年底Backlog（￥K）");
        titleMap.put(7, "项目净销售额（￥K）");
        titleMap.put(8, "收入（￥K）");
        titleMap.put(9, "收入结转率");
        titleMap.put(10, "总成本（￥K）");
        titleMap.put(11, "直接成本（￥K）");
        titleMap.put(12, "项目毛利（￥K）");
        titleMap.put(13, "毛利率");
        titleMap.put(14, "分摊成本（￥K）");
        titleMap.put(15, "净利润（￥K）");
        titleMap.put(16, "净利润率）");
        titleMap.put(17, "折算HC（年化）");
        titleMap.put(18, "人日均直接成本（￥K）");
        titleMap.put(19, "直接成本占收入比");
        titleMap.put(20, "人均订单（￥K）");
        titleMap.put(21, "人均成本（￥K）");
        titleMap.put(22, "人均净利润（￥K）");
        list.add(titleMap);
        for (PlanApprovalProvResp resp : queryResult) {
            LinkedHashMap<String, String> linkedMap = new LinkedHashMap<>();
            linkedMap.put("bigAreaName", resp.getBigAreaName());
            linkedMap.put("areaName", resp.getAreaName());
            linkedMap.put("provName", resp.getProvName());
            linkedMap.put("oprtStatus", resp.getOprtStatus());
            linkedMap.put("oprtDesc", resp.getOprtDesc());
            linkedMap.put("typeName", resp.getTypeName());
            linkedMap.put("endYearBacklog", resp.getEndYearBacklog());
            linkedMap.put("newOrderAmt", resp.getNewOrderAmt());
            linkedMap.put("income", resp.getIncome());
            linkedMap.put("incomeRatio", resp.getIncomeRatio());
            linkedMap.put("totalCost", resp.getTotalCost());
            linkedMap.put("directCost", resp.getDirectCost());
            linkedMap.put("proGrossProfit", resp.getProGrossProfit());
            linkedMap.put("grossProfitRatio", resp.getGrossProfitRatio());
            linkedMap.put("mfeeCost", resp.getMfeeCost());
            linkedMap.put("netProfit", resp.getNetProfit());
            linkedMap.put("netProfitRatio", resp.getNetProfitRatio());
            linkedMap.put("convetHc", resp.getConvetHc());
            linkedMap.put("dailyCapitaDirectCost", resp.getDailyCapitaDirectCost());
            linkedMap.put("directCostIncomeRatio", resp.getDirectCostIncomeRatio());
            linkedMap.put("capitaNewOrderAmt", resp.getCapitaNewOrderAmt());
            linkedMap.put("capitaCost", resp.getCapitaCost());
            linkedMap.put("capitaProfit", resp.getCapitaProfit());
            list.add(linkedMap);
        }
        PageBean bean = new PageBean();
        bean.setCount(Math.toIntExact(pageListResult.getCount()));
        bean.setObjectList(list);
        return bean;
    }

    @Override
    public PageBean exportNeedApprovePlListByBpVerIdAndGradeId(TeSysUser user, PlanApprovalReq req) {
        PageListResult pageListResult = this.queryNeedApprovePlListByBpVerIdAndGradeId(user, req);
        List<PlanApprovalPlResp> queryResult = (List<PlanApprovalPlResp>) pageListResult.getList();
        List<Object> list = new ArrayList<>();
        LinkedHashMap<Integer, String> titleMap = new LinkedHashMap<>();
        titleMap.put(0, "产品线");
        titleMap.put(1, "大区");
        titleMap.put(2, "区域");
        titleMap.put(3, "省份");
        titleMap.put(4, "类型");
        titleMap.put(5, "上一年底Backlog（￥K）");
        titleMap.put(6, "项目净销售额（￥K）");
        titleMap.put(7, "收入（￥K）");
        titleMap.put(8, "收入结转率");
        titleMap.put(9, "总成本（￥K）");
        titleMap.put(10, "直接成本（￥K）");
        titleMap.put(11, "项目毛利（￥K）");
        titleMap.put(12, "毛利率");
        titleMap.put(13, "分摊成本（￥K）");
        titleMap.put(14, "净利润（￥K）");
        titleMap.put(15, "净利润率）");
        titleMap.put(16, "折算HC（年化）");
        titleMap.put(17, "人日均直接成本（￥K）");
        titleMap.put(18, "直接成本占收入比");
        titleMap.put(19, "人均订单（￥K）");
        titleMap.put(20, "人均成本（￥K）");
        titleMap.put(21, "人均净利润（￥K）");
        list.add(titleMap);
        for (PlanApprovalPlResp resp : queryResult) {
            LinkedHashMap<String, Object> linkedMap = new LinkedHashMap<>();
            linkedMap.put("plName", resp.getPlName());
            linkedMap.put("bigAreaName", resp.getBigAreaName());
            linkedMap.put("areaName", resp.getAreaName());
            linkedMap.put("provName", resp.getProvName());
            linkedMap.put("typeName", resp.getTypeName());
            linkedMap.put("endYearBacklog", resp.getEndYearBacklog());
            linkedMap.put("newOrderAmt", resp.getNewOrderAmt());
            linkedMap.put("income", resp.getIncome());
            linkedMap.put("incomeRatio", resp.getIncomeRatio());
            linkedMap.put("totalCost", resp.getTotalCost());
            linkedMap.put("directCost", resp.getDirectCost());
            linkedMap.put("proGrossProfit", resp.getProGrossProfit());
            linkedMap.put("grossProfitRatio", resp.getGrossProfitRatio());
            linkedMap.put("mfeeCost", resp.getMfeeCost());
            linkedMap.put("netProfit", resp.getNetProfit());
            linkedMap.put("netProfitRatio", resp.getNetProfitRatio());
            linkedMap.put("convetHc", resp.getConvetHc());
            linkedMap.put("dailyCapitaDirectCost", resp.getDailyCapitaDirectCost());
            linkedMap.put("directCostIncomeRatio", resp.getDirectCostIncomeRatio());
            linkedMap.put("capitaNewOrderAmt", resp.getCapitaNewOrderAmt());
            linkedMap.put("capitaCost", resp.getCapitaCost());
            linkedMap.put("capitaProfit", resp.getCapitaProfit());
            list.add(linkedMap);
        }
        PageBean bean = new PageBean();
        bean.setCount(Math.toIntExact(pageListResult.getCount()));
        bean.setObjectList(list);
        return bean;
    }

    @Override
    public List<TeIdNameCn> queryRepulseableListByBpVerIdAndGradeId(ObjectId planVersion, ObjectId areaId, TeSysUser user) {
        //1.查询该区域下所有省份信息
        List<TeSysDef> sysDefs = this.queryProvByArearId(areaId,user.getSbuId());
        if (CollectionUtils.isEmpty(sysDefs)) {
            log.warn("未查到省份信息");
            return new ArrayList<>();
        }
        //2.查询当前区域的上一层id信息
        List<ObjectId> areaIds = queryUpperLevelOfCurrentArea(areaId, sysDefs);
        if (CollectionUtils.isEmpty(areaIds)) {
            log.warn("queryRepulseableListByBpVerIdAndGradeId - 未查到该区域上一层级的信息！");
            return new ArrayList<>();
        }
        //3.从abpOprt表中查询上一层级并且已提交的数据信息
        List<AbpOprt> abpOprts = this.queryAbpOprtByAreaIds(planVersion, areaIds, true);
        if (CollectionUtils.isEmpty(abpOprts)) {
            log.warn("未查到对应的区域对应的已提交信息");
            return new ArrayList<>();
        }
        return abpOprts.stream().map(AbpOprt::getDept).collect(Collectors.toList());
    }


    private List<ObjectId> queryUpperLevelOfCurrentArea(ObjectId areaId, List<TeSysDef> sysDefs) {
        if (CollectionUtils.isEmpty(sysDefs)) {
            return new ArrayList<>();
        }
        TeSysDef sysDef = sysDefs.get(0);
        //2.判断当前区域的上一级是啥
        List<ObjectId> areaIds = new ArrayList<>();
        TeSrcDef srcDef = sysDef.getSrcDef();
        if (srcDef == null) {
            return new ArrayList<>();
        }
        if (srcDef.getSrcDefId().equals(areaId)) {
            //当前区域为bu，则上一级为大区
            for (TeSysDef def : sysDefs) {
                List<TeIdNameCn> cndtItems = def.getCndtItems();
                for (TeIdNameCn cndtItem : cndtItems) {
                    if (cndtItem.getCodeName().equals(Constant.BIG_REGION)) {
                        areaIds.add(cndtItem.getCid());
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(areaIds)) {
            return areaIds;
        }
        List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
        for (TeIdNameCn cndtItem : cndtItems) {
            if (cndtItem.getCodeName().equals(Constant.BIG_REGION) && cndtItem.getCid().equals(areaId)) {
                //当前区域为大区，则上一级为区域
                for (TeSysDef def : sysDefs) {
                    List<TeIdNameCn> cndtItemList = def.getCndtItems();
                    for (TeIdNameCn teIdNameCn : cndtItemList) {
                        if (teIdNameCn.getCodeName().equals(Constant.REGION)) {
                            areaIds.add(teIdNameCn.getCid());
                        }
                    }
                }
            }
            if (cndtItem.getCodeName().equals(Constant.REGION) && cndtItem.getCid().equals(areaId)) {
                //当前区域为区域，则上一级为省份
                areaIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
            }
        }
        return areaIds;
    }

    @Override
    public CommonResult<Void> repulsePlanByBpVerIdAndGradeList(TeSysUser user, PlanApprovalRepulseReq req) {
        //1.查询父层级是否已提交
        List<AbpOprt> abpOprts = this.queryAbpOprtByAreaIds(req.getPlanVersion(), Collections.singletonList(req.getParentAreaId()), true);
        if (!CollectionUtils.isEmpty(abpOprts)) {
            return CommonResult.fail("父层级已提交，打回失败");
        }
        //2.查询待打回的区域操作信息
        String lockName = "repulsePlanByBpVerIdAndGradeList" + req.getPlanVersion();
        RLock lock = redissionClient.getLock(lockName);
        boolean flag;
        try {
            flag = lock.tryLock(0L, 1800L, TimeUnit.SECONDS);
            if (flag) {
                List<AbpOprt> abpOprtList = this.queryAbpOprtByAreaIds(req.getPlanVersion(), req.getAreaIds(), true);
                if (abpOprtList.size() < req.getAreaIds().size()) {
                    return CommonResult.fail("打回的层级操作记录未全部找到，打回失败");
                }
                TeUser teUser = getTeUser(user);
                //查询条件
                List<List<IDbCondition>> queryConditions = new ArrayList<>();
                //更新条件
                List<List<UpdataData>> updateConditions = new ArrayList<>();
                for (AbpOprt abpOprt : abpOprtList) {
                    List<IDbCondition> query = new ArrayList<>();
                    query.add(new DC_E(DbFieldName.common__id, abpOprt.getId()));
                    queryConditions.add(query);
                    TeIdNameCn newStatus = new TeIdNameCn();
                    newStatus.setCid(Constant.ABP_OPRT_BEATEN_BACK_ID);
                    newStatus.setName("被打回");
                    newStatus.setCodeName("");
                    TeIdNameCn oprt = new TeIdNameCn();
                    oprt.setCid(Constant.ABP_OPRT_REPULSE_ID);
                    oprt.setName("打回");
                    oprt.setCodeName("");
                    List<UpdataData> update = updateAbpOprtConds(req.getRepulseDesc(), teUser, abpOprt, newStatus, oprt);
                    updateConditions.add(update);
                }
                BulkWriteResult updateResult = abpOprtDao.batchUpdate(queryConditions, updateConditions, false);
                log.info("========>收入预测打回结果：{}", updateResult.wasAcknowledged());
                //3. 将打回省份下的经营计划都置为未提交状态
                List<IDbCondition> conditionList = new ArrayList<>();
                conditionList.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), req.getPlanVersion()));
                conditionList.add(new DC_I<>(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid), req.getAreaIds()));
                List<UpdataData> update = new ArrayList<>();
                update.add(new UpdataData(DbFieldName.AbpOop.isLocked, false));
                abpOopDao.updateByConds(conditionList, update);
            } else {
                log.warn("======> repulsePlanByBpVerIdAndGradeList - 未获取到锁！ ");
                return CommonResult.fail("打回失败!该版本下有其他省份正在打回中,请稍后重试!");
            }
        } catch (InterruptedException e) {
            log.warn("======> repulsePlanByBpVerIdAndGradeList - 获取锁失败！ ", e);
            return CommonResult.fail("打回失败!请稍后重试!");
        } finally {
            if (lock!=null&&lock.isLocked()&&lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return CommonResult.success();
    }

    private List<UpdataData> updateAbpOprtConds(String desc, TeUser teUser, AbpOprt abpOprt, TeIdNameCn newStatus, TeIdNameCn oprt) {
        List<UpdataData> update = new ArrayList<>();

        update.add(new UpdataData(DbFieldName.AbpOprt.status, newStatus));
        StatusHist statusHist = new StatusHist();
        statusHist.setStatus(abpOprt.getStatus());
        statusHist.setDesc(abpOprt.getDesc());
        statusHist.setOprtUser(abpOprt.getOprtUser());
        statusHist.setOprtTime(abpOprt.getOprtTime());
        update.add(new UpdataData(DbFieldName.AbpOprt.oprt, oprt));
        update.add(new UpdataData(DbFieldName.AbpOprt.desc, desc));
        update.add(new UpdataData(DbFieldName.AbpOprt.oprtUser, teUser));
        update.add(new UpdataData(DbFieldName.AbpOprt.oprtTime, new Date()));
        List<StatusHist> statusHists = abpOprt.getStatusHist();
        if (CollectionUtils.isEmpty(statusHists)) {
            statusHists = new ArrayList<>();
        }
        statusHists.add(statusHist);
        update.add(new UpdataData(DbFieldName.AbpOprt.statusHist, statusHists));
        return update;
    }


    @Override
    public Map<String, Boolean> queryGradeStatusByBpVerIdAndGradeId(ObjectId planVersion, ObjectId areaId, TeSysUser user) {
        Map<String, Boolean> result = new HashMap<>();
        //1.查询该区域下所有省份信息
        List<TeSysDef> sysDefs = this.queryProvByArearId(areaId,user.getSbuId());
        //2.查询该版本，该区域上一层级信息
        List<ObjectId> areaIds = this.queryUpperLevelOfCurrentArea(areaId, sysDefs);
        if (CollectionUtils.isEmpty(areaIds)) {
            log.warn("queryGradeStatusByBpVerIdAndGradeId - 未查到该区域上一层级的信息！");
            result.put("submitAbleFlag", false);
            result.put("submittedFlag", false);
            result.put("repulseableFlag", false);
            return result;
        }
        //3.查询该区域上一层的已提交的进度信息
        List<AbpOprt> abpOprts = queryAbpOprtByAreaIds(planVersion, areaIds, true);

        boolean submitAbleFlag = false;
        boolean submittedFlag = false;
        boolean repulseableFlag = false;
        boolean subGradesAllSubmittedFlag = false;

        if (!CollectionUtils.isEmpty(abpOprts)) {
            repulseableFlag = true;
        }
        //子层级不为空且都提交了置为true
        if (areaIds.size() == abpOprts.size()) {
            subGradesAllSubmittedFlag = true;
        }
        //4.查询当前区域的进度信息
        List<AbpOprt> abpOprtList = this.queryAbpOprtByAreaIds(planVersion, Collections.singletonList(areaId), false);
        if (!CollectionUtils.isEmpty(abpOprtList) && abpOprtList.get(0).getStatus().getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
            submittedFlag = true;
        }
        //只有在层级之前没提交过或者不为已提交的状态下才可以提交
        if (CollectionUtils.isEmpty(abpOprtList) || (!CollectionUtils.isEmpty(abpOprtList) && !abpOprtList.get(0).getStatus().getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID))) {
            //只有子层级都提交了并且当前层级未提交才可以提交当前层级
            submitAbleFlag = subGradesAllSubmittedFlag;
        }

        result.put("submitAbleFlag", submitAbleFlag);
        result.put("submittedFlag", submittedFlag);
        result.put("repulseableFlag", repulseableFlag);
        return result;
    }

    private List<AbpOprt> queryAbpOprtByAreaIds(ObjectId planVersion, List<ObjectId> areaIds, boolean flag) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVersion));
        conds.add(new DC_I<>(DbFieldName.AbpOprt.dept.dot(DbFieldName.common_cid), areaIds));
        if (flag) {
            conds.add(new DC_E(DbFieldName.AbpOprt.status.dot(DbFieldName.common_cid), Constant.ABP_OPRT_SUBMITTED_ID));
        }
        return abpOprtDao.findByFieldAndConds(conds, null);
    }

    @Override
    public Map<String, Boolean> queryBuStatusByBpVerIdAndGradeId(ObjectId planVersion, ObjectId areaId) {
        Map<String, Boolean> result = new HashMap<>();
        boolean buSubmittedFlag = false;
        boolean buRepulsedFlag = false;
        //1.查询该区域的进度信息
        List<AbpOprt> abpOprts = this.queryAbpOprtByAreaIds(planVersion, Collections.singletonList(areaId), false);
        if (!CollectionUtils.isEmpty(abpOprts) && abpOprts.get(0).getStatus() != null) {
            TeIdNameCn status = abpOprts.get(0).getStatus();
            if (status.getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                buSubmittedFlag = true;
            }
            if (status.getCid().equals(Constant.ABP_OPRT_BEATEN_BACK_ID)) {
                buRepulsedFlag = true;
            }
        }
        result.put("buSubmittedFlag", buSubmittedFlag);
        result.put("buRepulsedFlag", buRepulsedFlag);
        return result;
    }

    @Override
    public CommonResult<Void> submitPlanByBpVerIdAndGradeId(ObjectId planVerId, ObjectId areaId, String submitDesc, TeSysUser user) {
        //只有当前区域的上一层全为已提交并且当前区域不为已提交才能提交
        //1.查询该区域下所有省份信息
        List<TeSysDef> sysDefs = this.queryProvByArearId(areaId,user.getSbuId());
        //2.查询该版本，该区域上一层级信息
        List<ObjectId> areaIds = this.queryUpperLevelOfCurrentArea(areaId, sysDefs);
        if (CollectionUtils.isEmpty(areaIds)) {
            log.warn("submitPlanByBpVerIdAndGradeId - 未查到该区域上一层级的信息！");
            return CommonResult.fail("提交失败,未查到上一层信息");
        }
        //3.查询该区域上一层的已提交的进度信息
        List<AbpOprt> abpOprts = queryAbpOprtByAreaIds(planVerId, areaIds, true);
        if (CollectionUtils.isEmpty(abpOprts)) {
            return CommonResult.fail("提交失败,未查到上一层的已提交信息");
        }
        if (areaIds.size() != abpOprts.size()) {
            return CommonResult.fail("提交计划中存在尚未提交的清单或已提交，不允许提交");
        }
        //4.查询当前区域的提交信息
        RLock lock = redissionClient.getLock("submitPlanByBpVerIdAndGradeId" + areaId);

        // 查询 areaId 对应信息
        TeSysDef byId = sysDefDao.findById(areaId);
        TeIdNameCn dept = new TeIdNameCn();
        dept.setCid(areaId);
        dept.setName(byId.getDefName());
        dept.setCodeName(byId.getDefType().getDefTypeCodeName());
        TeIdNameCn oprtInfo = new TeIdNameCn();
        oprtInfo.setCid(Constant.ABP_OPRT_SUBMIT_ID);
        oprtInfo.setName(Constant.ABP_OPRT_SUBMIT_NAME);

        boolean flag;
        try {
            flag = lock.tryLock(0L, 1800L, TimeUnit.SECONDS);
            if (flag) {
                List<AbpOprt> abpOprtList = this.queryAbpOprtByAreaIds(planVerId, Collections.singletonList(areaId), false);
                if (!CollectionUtils.isEmpty(abpOprtList) && abpOprtList.get(0).getStatus() != null
                        && abpOprtList.get(0).getStatus().getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                    return CommonResult.fail("提交失败,计划已提交，不允许再次提交");
                }
                TeUser teUser = getTeUser(user);
                TeIdNameCn newStatus = new TeIdNameCn();
                newStatus.setCid(Constant.ABP_OPRT_SUBMITTED_ID);
                newStatus.setName("已提交");
                newStatus.setCodeName("");
                //5.开始提交，更新abpOprt信息
                if (CollectionUtils.isEmpty(abpOprtList)) {
                    AbpOprt oprt = abpOprts.get(0);
                    //插入信息
                    AbpOprt abpOprt = new AbpOprt();
                    abpOprt.setPlanVer(oprt.getPlanVer());
                    abpOprt.setDept(dept);
                    abpOprt.setOprt(oprtInfo);
                    abpOprt.setStatus(newStatus);
                    abpOprt.setDesc(submitDesc);
                    abpOprt.setOprtUser(teUser);
                    abpOprt.setOprtTime(new Date());
                    abpOprtDao.save(abpOprt);
                } else {
                    //更新信息
                    AbpOprt abpOprt = abpOprtList.get(0);
                    List<IDbCondition> conds = new ArrayList<>();
                    conds.add(new DC_E(DbFieldName.common__id, abpOprt.getId()));
                    List<UpdataData> updates = updateAbpOprtConds(submitDesc, teUser, abpOprt, newStatus, null);
                    abpOprtDao.updateByConds(conds, updates);
                }
            } else {
                log.warn("======> submitPlanByBpVerIdAndGradeId - 为获取到锁！ ");
                return CommonResult.fail("提交失败!该省份正在提交中,请稍后重试!");
            }
        } catch (InterruptedException e) {
            log.warn("======> submitPlanByBpVerIdAndGradeId - 获取锁失败！ ", e);
            return CommonResult.fail("提交失败!请稍后重试!");
        } finally {
            if (lock!=null&&lock.isLocked()&&lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return CommonResult.success();
    }

    @Override
    public List<Map<String, Object>> queryBpVerListByBuCode(String buCode, String year) {
        //根据bu和年去查询版本
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysVerMgt__srcDef.dot(DbFieldName.common_cn), buCode));
        conds.add(new DC_E(DbFieldName.sysVerMgt__year, year));
        conds.add(new DC_E(DbFieldName.sysVerMgt__verMgtType.dot(DbFieldName.common_cid), Constant.ABP_BUSINESS_PLAN_VERSION_ID));
        List<DbFieldName> fieldNames = new ArrayList<>();
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.findByFieldAndConds(conds, fieldNames);
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, List<SysVerMgt>> yearMap = sysVerMgts.stream().collect(Collectors.groupingBy(SysVerMgt::getYear));
        for (Map.Entry<String, List<SysVerMgt>> entry : yearMap.entrySet()) {
            String bpYear = entry.getKey();
            Map<String, Object> map = new HashMap<>();
            map.put("bpYear", bpYear);
            List<SysVerMgt> value = entry.getValue();
            List<Map<String, Object>> oneBpVerYearList = new ArrayList<>();
            for (SysVerMgt mgt : value) {
                Map<String, Object> tmp = new HashMap<>();
                tmp.put("bpVerId", mgt.getId());
                tmp.put("bpYear", mgt.getYear());
                tmp.put("bpVerNo", mgt.getVerNo());
                if (mgt.getIsLocked()) {
                    tmp.put("isClosed", "已关闭");
                }
                oneBpVerYearList.add(tmp);
            }
            map.put("bpVerList", oneBpVerYearList);
            result.add(map);
        }
        return result;
    }

    @Override
    public List<ProgressOverviewResp> queryPlanProgressOverviewByBpVerId(String buCode, ObjectId planVerId) throws IOException, ClassNotFoundException {
        //查询当前版本和bu下的所有省份，区域，大区，bu的进度
        //1.从sysDef中查询bu下的所有省份，区域，大区的信息
        List<TeSysDef> sysDefs = queryProvByBuCode(buCode);
        if (CollectionUtils.isEmpty(sysDefs)) {
            log.warn("该bu下未查到省份！");
            return new ArrayList<>();
        }
        Collection<ObjectId> areaIds = new HashSet<>();
        Map<ObjectId,Integer> defId2DefNo=new HashMap<>();
        Map<ObjectId, TeIdNameCn> areaId2NameMap = new HashMap<>();
        Map<ObjectId, HashSet<ObjectId>> bigRegionMap = new HashMap<>();
        Map<ObjectId, HashSet<ObjectId>> regionMap = new HashMap<>();
        ObjectId buId = null;
        for (TeSysDef sysDef : sysDefs) {
            areaIds.add(sysDef.getId());
            TeIdNameCn prov = new TeIdNameCn();
            prov.setCid(sysDef.getId());
            prov.setName(sysDef.getDefName());
            prov.setCodeName(sysDef.getDefType().getDefTypeCodeName());
            areaId2NameMap.put(sysDef.getId(), prov);
            TeSrcDef srcDef = sysDef.getSrcDef();
            if (srcDef != null) {
                areaIds.add(srcDef.getSrcDefId());
                TeIdNameCn bu = new TeIdNameCn();
                buId = srcDef.getSrcDefId();
                bu.setCid(buId);
                bu.setName(srcDef.getSrcDefName());
                bu.setCodeName(srcDef.getSrcDefCodeName());
                areaId2NameMap.put(srcDef.getSrcDefId(), bu);
            }
            Map<String, ObjectId> map = new HashMap<>();
            for (TeIdNameCn cndtItem : sysDef.getCndtItems()) {
                areaIds.add(cndtItem.getCid());
                TeIdNameCn area = new TeIdNameCn();
                area.setCid(cndtItem.getCid());
                area.setName(cndtItem.getName());
                area.setCodeName(cndtItem.getCodeName());
                areaId2NameMap.put(cndtItem.getCid(), area);
                map.put(cndtItem.getCodeName(), cndtItem.getCid());
            }
            regionMap.computeIfAbsent(map.get(Constant.REGION), s -> new HashSet<>()).add(sysDef.getId());
            bigRegionMap.computeIfAbsent(map.get(Constant.BIG_REGION), s -> new HashSet<>()).add(map.get(Constant.REGION));
        }
        //查出所有层级的name对应的defNo
        List<TeSysDef> sysDefsByIds = sysDefDao.getSysDefsByIds(new ArrayList<>(areaIds));
        sysDefsByIds.stream().forEach(sysDef->defId2DefNo.put(sysDef.getId(),sysDef.getDefNo()));
        //2.根据查出来的信息，到abpOprt中查询进度信息
        List<AbpOprt> abpOprts = this.queryAbpOprtByAreaIds(planVerId, new ArrayList<>(areaIds), false);
        if (CollectionUtils.isEmpty(abpOprts)) {
            log.warn("未查到该bu下的计划进度！");
            return new ArrayList<>();
        }
        Map<ObjectId, List<AbpOprt>> abpOprtMap = abpOprts.stream().collect(Collectors.groupingBy(s -> s.getDept().getCid()));
        //3.遍历所有进度信息，确认各节点的进度信息
        List<ProgressOverviewResp> bigRegionSteps = new ArrayList<>();
        for (Map.Entry<ObjectId, HashSet<ObjectId>> entry : bigRegionMap.entrySet()) {
            HashSet<ObjectId> regionIds = entry.getValue();
            ObjectId bigRegionId = entry.getKey();
            List<ProgressOverviewResp> regionSteps = new ArrayList<>();
            ProgressOverviewResp bigRegionStep = new ProgressOverviewResp();
            for (ObjectId regionId : regionIds) {
                HashSet<ObjectId> provIds = regionMap.get(regionId);
                List<ProgressOverviewResp> provSteps = new ArrayList<>();
                ProgressOverviewResp regionStep = new ProgressOverviewResp();
                for (ObjectId provId : provIds) {
                    List<AbpOprt> abpOprtList = abpOprtMap.get(provId);
                    if (CollectionUtils.isEmpty(abpOprtList)) {
                        continue;
                    }
                    AbpOprt abpOprt = abpOprtList.get(0);
                    ProgressOverviewResp info = new ProgressOverviewResp();
                    info.setId(provId);
                    TeIdNameCn dept = abpOprt.getDept();
                    if (dept == null) {
                        continue;
                    }
                    info.setName(dept.getName());
                    List<ProgressOverviewResp> list = new ArrayList<>();
                    list.add(info);

                    List<StatusHist> statusHist = sortAndDistStatus(abpOprt);
                    int size = statusHist.size();
                    int count = 1;
                    int isSubmitted = 0;
                    List<ProgressOverviewResp> stepList = new ArrayList<>();
                    for (String statusName : STATUS_LIST) {
                        ProgressOverviewResp step = new ProgressOverviewResp();
                        step.setId(provId);
                        Integer defNo = defId2DefNo.get(provId);
                        step.setDefNo(defNo);
                        step.setName(statusName);
                        String oprtInfo = "";
                        if (size >= count) {
                            StatusHist hist = statusHist.get(count - 1);
                            String time = DateUtil.formatDate2Str(hist.getOprtTime(), DateUtil.DATE_FORMAT);
                            oprtInfo = hist.getOprtUser() == null ? time : hist.getOprtUser().getUserName() + " " + time;
                            isSubmitted = size == count ? 1 : 0;
                        }
                        if (count == 1) {
                            step.setChildren(list);
                        } else {
                            step.setChildren(CommonUtil.deepCopy(stepList));
                            stepList.clear();
                        }
                        step.setOprtInfo(oprtInfo);
                        step.setIsSubmitted(isSubmitted);
                        isSubmitted = 0;
                        stepList.add(step);
                        count++;
                    }
                    provSteps.addAll(stepList);
                }
                provSteps = provSteps.stream()
                        .sorted(Comparator.comparing(ProgressOverviewResp::getDefNo, Comparator.nullsFirst(Integer::compareTo)))
                        .collect(Collectors.toList());
                regionStep.setChildren(provSteps);
                regionStep.setIsSubmitted(0);
                List<AbpOprt> regionAbpOprts = abpOprtMap.get(regionId);
                if (!CollectionUtils.isEmpty(regionAbpOprts)) {
                    AbpOprt regionAbpOprt = regionAbpOprts.get(0);
                    TeIdNameCn status = regionAbpOprt.getStatus();
                    if (status != null && status.getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                        regionStep.setIsSubmitted(1);
                    }
                    String regionTime = DateUtil.formatDate2Str(regionAbpOprt.getOprtTime(), DateUtil.DATE_FORMAT);
                    String oprtInfo = regionAbpOprt.getOprtUser() == null ? regionTime : regionAbpOprt.getOprtUser().getUserName() + " " + regionTime;
                    regionStep.setOprtInfo(oprtInfo);
                }
                TeIdNameCn common = areaId2NameMap.get(regionId);
                regionStep.setId(regionId);
                Integer regionDefNo = defId2DefNo.get(regionId);
                regionStep.setDefNo(regionDefNo);
                regionStep.setName(common.getName());
                regionSteps.add(regionStep);
            }
            regionSteps = regionSteps.stream()
                    .sorted(Comparator.comparing(ProgressOverviewResp::getDefNo, Comparator.nullsFirst(Integer::compareTo)))
                    .collect(Collectors.toList());
            bigRegionStep.setChildren(regionSteps);
            bigRegionStep.setIsSubmitted(0);
            List<AbpOprt> bigRegionAbpOprts = abpOprtMap.get(bigRegionId);
            if (!CollectionUtils.isEmpty(bigRegionAbpOprts)) {
                AbpOprt bigRegionAbpOprt = bigRegionAbpOprts.get(0);
                TeIdNameCn status = bigRegionAbpOprt.getStatus();
                if (status != null && status.getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                    bigRegionStep.setIsSubmitted(1);
                }
                String bigRegionTime = DateUtil.formatDate2Str(bigRegionAbpOprt.getOprtTime(), DateUtil.DATE_FORMAT);
                String oprtInfo = bigRegionAbpOprt.getOprtUser() == null ? bigRegionTime : bigRegionAbpOprt.getOprtUser().getUserName() + " " + bigRegionTime;
                bigRegionStep.setOprtInfo(oprtInfo);
            }
            TeIdNameCn common = areaId2NameMap.get(bigRegionId);
            bigRegionStep.setId(bigRegionId);
            Integer bigRegionDefNo = defId2DefNo.get(bigRegionId);
            bigRegionStep.setDefNo(bigRegionDefNo);
            bigRegionStep.setName(common.getName());
            bigRegionSteps.add(bigRegionStep);
        }
        ProgressOverviewResp buStep = new ProgressOverviewResp();
        bigRegionSteps = bigRegionSteps.stream()
                .sorted(Comparator.comparing(ProgressOverviewResp::getDefNo, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());
        buStep.setChildren(bigRegionSteps);
        buStep.setIsSubmitted(0);
        List<AbpOprt> buAbpOprts = abpOprtMap.get(buId);
        if (!CollectionUtils.isEmpty(buAbpOprts)) {
            AbpOprt buAbpOprt = buAbpOprts.get(0);
            TeIdNameCn status = buAbpOprt.getStatus();
            if (status != null && status.getCid().equals(Constant.ABP_OPRT_SUBMITTED_ID)) {
                buStep.setIsSubmitted(1);
            }
            String buTime = DateUtil.formatDate2Str(buAbpOprt.getOprtTime(), DateUtil.DATE_FORMAT);
            String oprtInfo = buAbpOprt.getOprtUser() == null ? buTime : buAbpOprt.getOprtUser().getUserName() + " " + buTime;
            buStep.setOprtInfo(oprtInfo);
        }
        TeIdNameCn common = areaId2NameMap.get(buId);
        buStep.setId(buId);
        buStep.setName(common.getName());
        List<ProgressOverviewResp> buSteps = new ArrayList<>();
        buSteps.add(buStep);
        //4.返回
        return buSteps;
    }

    @Override
    public CommonResult<List<QueryPmProvRelResp>> queryPmProvRelationData(TeSysUser user, ObjectId areaId) {
        //1.查询当前登录人有权限的省份信息
        List<TeSysDef> provs = serviceManager.queryProvWithPermis(user);
        if (CollectionUtils.isEmpty(provs)){
            throw BusinessException.initExc("查询失败,没有对应区域权限或者权限下的省份信息为空!");
        }
        //2.遍历数据，确认该区域在权限区域内
        List<TeSysDef> sysDefs =new ArrayList<>();
        List<ObjectId> provIds =new ArrayList<>();
        for (TeSysDef sysDef : provs) {
            //该区域为bu
            if (sysDef.getSrcDef().getSrcDefId().equals(areaId)){
                sysDefs=provs;
                provIds=sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
                break;
            }
            //该区域为大区或者区域
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            for (TeIdNameCn cndtItem : cndtItems) {
                if (cndtItem.getCid().equals(areaId)){
                    sysDefs.add(sysDef);
                    provIds.add(sysDef.getId());
                    break;
                }
            }
            if (sysDef.getId().equals(areaId)){
                sysDefs.add(sysDef);
                provIds.add(sysDef.getId());
                break;
            }
        }
        if (CollectionUtils.isEmpty(sysDefs)){
            throw BusinessException.initExc("查询失败,没有对应区域权限,请先授权!");
        }
        //3.查询对应省份的对应项目经理信息
        List<SysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.queryPmByProv(provIds);
        Map<ObjectId, List<SysDefRoleUser>> prov2UserMap=new HashMap<>();
        if (!CollectionUtils.isEmpty(sysDefRoleUsers)){
            prov2UserMap = sysDefRoleUsers.stream().collect(Collectors.groupingBy(SysDefRoleUser::getDefId));
        }
        //4.遍历省份信息，组装返回信息
        List<QueryPmProvRelResp> result = new ArrayList<>();
        for (TeSysDef sysDef : sysDefs) {
            QueryPmProvRelResp data = new QueryPmProvRelResp();
            data.setProvId(sysDef.getId());
            List<TeIdNameCn> cndtItems = sysDef.getCndtItems();
            if (!CollectionUtils.isEmpty(cndtItems)){
                for (TeIdNameCn cndtItem : cndtItems) {
                    if (cndtItem.getCodeName().equals(Constant.BIG_REGION)){
                        data.setBigRegion(cndtItem.getName());
                    }
                    if (cndtItem.getCodeName().equals(Constant.REGION)){
                        data.setRegion(cndtItem.getName());
                    }
                }
            }
            data.setProv(sysDef.getDefName());
            List<TeUser> pmList = new ArrayList<>();
            List<SysDefRoleUser> users = prov2UserMap.get(sysDef.getId());
            if(CollectionUtils.isEmpty(users)){
                data.setPmList(pmList);
                result.add(data);
                continue;
            }
            for (SysDefRoleUser sysDefRoleUser : users) {
                pmList.add(sysDefRoleUser.getRoleUser());
            }
            //经理信息按照jobCode字母排序
            pmList.sort(Comparator.comparing(TeUser::getLoginName));
            data.setPmList(pmList);
            result.add(data);
        }
        return CommonResult.success(result);
    }

    @Override
    public CommonResult<Void> addPmProvRelationData(ObjectId provId, ObjectId userId, TeSysUser user) {
        //1.查询当前登录人有权限的省份信息
        List<TeSysDef> provs = serviceManager.queryProvWithPermis(user);
        if (CollectionUtils.isEmpty(provs)){
            return CommonResult.fail("添加失败,没有省份的权限,请先授权在添加!");
        }
        List<ObjectId> provIds = provs.stream().map(TeSysDef::getId).collect(Collectors.toList());
        if (!provIds.contains(provId)){
            return CommonResult.fail("添加失败,没有该省份的权限,请先授权在添加!");
        }
        //2.查询当前项目经理的省份角色
        List<SysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.queryUserProvRole(Collections.singletonList(userId), user.getSbuId());
        if (!CollectionUtils.isEmpty(sysDefRoleUsers)){
            //遍历该用户的角色
            for (SysDefRoleUser sysDefRoleUser : sysDefRoleUsers) {
                List<ObjectId> roleIds = sysDefRoleUser.getRole().stream().map(Role::getRoleId).collect(Collectors.toList());
                //判断：一个用户只能对应一个省份
                if (roleIds.contains(Constant.PM_ID)){
                    return CommonResult.fail("添加失败!该项目经理已有对应省份,不允许存在多个!");
                }
            }
            Map<ObjectId, List<SysDefRoleUser>> prov2SysDefRoleUserMap = sysDefRoleUsers.stream().collect(Collectors.groupingBy(SysDefRoleUser::getDefId));
            //当前项目经理的当省份角色
            List<SysDefRoleUser> userList = prov2SysDefRoleUserMap.get(provId);
            if (!CollectionUtils.isEmpty(userList)){
                SysDefRoleUser sysDefRoleUser = userList.get(0);
                List<Role> roles = sysDefRoleUser.getRole();
                Role role = new Role();
                role.setRoleId(Constant.PM_ID);
                role.setRoleName(Constant.PM_NAME);
                role.setRoleCodeName(Constant.PM_CODE_NAME);
                roles.add(role);
                List<UpdataData> updates=new ArrayList<>();
                updates.add(new UpdataData(DbFieldName.sysDefRoleUser__role,roles));
                sysDefRoleUserDao.updateById(sysDefRoleUser.getId(),updates);
                return CommonResult.success();
            }
        }
        //3.查询用户信息
        List<TeSysUser> sysUsers = this.querySysUser(null, Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(sysUsers)){
            return CommonResult.fail("添加失败!该项目经理不存在!");
        }
        TeSysUser sysUser = sysUsers.get(0);
        //4.查询bu信息
        List<TeSysDef> sysDefs = this.queryBuData(user.getSbuId());
        if (CollectionUtils.isEmpty(sysDefs)){
            return CommonResult.fail("添加失败!当前用户的bu不存在,请核对bu后添加");
        }
        //5.添加经理对应的省份关系
        SysDefRoleUser sysDefRoleUser = new SysDefRoleUser();
        TeSysDef bu = sysDefs.get(0);
        DefType defType = new DefType();
        defType.setDefTypeId(Constant.ABP_PROV_ID);
        defType.setDefTypeName(Constant.ABP_PROV_NAME);
        defType.setDefTypeCodeName(Constant.ABP_PROV_CODE_NAME);
        sysDefRoleUser.setDefType(defType);

        SrcDef srcDef = new SrcDef();
        srcDef.setSrcDefId(bu.getId());
        srcDef.setSrcDefName(bu.getDefName());
        srcDef.setSrcDefCodeName(bu.getCodeName());
        sysDefRoleUser.setSrcDef(srcDef);

        sysDefRoleUser.setDefId(provId);

        Role role = new Role();
        role.setRoleId(Constant.PM_ID);
        role.setRoleName(Constant.PM_NAME);
        role.setRoleCodeName(Constant.PM_CODE_NAME);
        sysDefRoleUser.setRole(Collections.singletonList(role));

        TeUser roleUser = new TeUser();
        roleUser.setUserId(sysUser.getId());
        roleUser.setUserName(sysUser.getUserName());
        roleUser.setLoginName(sysUser.getLoginName());
        roleUser.setJobCode(sysUser.getJobCode());
        sysDefRoleUser.setRoleUser(roleUser);

        sysDefRoleUser.setValid(true);
        sysDefRoleUser.setAddTime(new Date());

        TeUser addUser = new TeUser();
        addUser.setUserId(user.getId());
        addUser.setUserName(user.getUserName());
        addUser.setJobCode(user.getJobCode());
        addUser.setLoginName(user.getLoginName());
        sysDefRoleUser.setAddUser(addUser);
        sysDefRoleUserDao.save(sysDefRoleUser);
        return CommonResult.success();
    }

    @Override
    public CommonResult<Void> deletePmProvRelationData(ObjectId provId, ObjectId userId, TeSysUser user) {
        //1.查询当前登录人有权限的省份信息
        List<TeSysDef> provs = serviceManager.queryProvWithPermis(user);
        if (CollectionUtils.isEmpty(provs)){
            return CommonResult.fail("添加失败,没有省份的权限,请先授权在添加!");
        }
        List<ObjectId> provIds = provs.stream().map(TeSysDef::getId).collect(Collectors.toList());
        if (!provIds.contains(provId)){
            return CommonResult.fail("添加失败,没有该省份的权限,请先授权在添加!");
        }
        //2.先查出项目经理的该省份的角色
        List<SysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.queryPmProvRel(userId, provId);
        if (CollectionUtils.isEmpty(sysDefRoleUsers)){
            return CommonResult.fail("删除失败,项目经理对应的省份的关系不存在");
        }
        SysDefRoleUser sysDefRoleUser = sysDefRoleUsers.get(0);
        List<Role> roles = sysDefRoleUser.getRole();
        if (roles.size()>1){
            //更新
            Document query = new Document(DbFieldName.common__id.n(), sysDefRoleUser.getId());
            Document roleId = new Document(DbFieldName.sysDefRoleUser__roleId.n(), Constant.PM_ID);
            Document role = new Document(DbFieldName.sysDefRoleUser__role.n(), roleId);
            Document update = new Document("$pull", role);
            sysDefRoleUserDao.update(query,update);
        }else {
            //删除
            sysDefRoleUserDao.deleteById(sysDefRoleUser.getId());
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<QueryHitchPrjResp>> queryHitchPrjById(ObjectId id, Boolean isPrim) {
        List<AbpOop> abpOops;
        if (isPrim){
            //主项目查询
            abpOops=abpOopDao.queryDataById(id);
        }else {
            //查询辅项目的主项目
            abpOops=abpOopDao.queryDataBySubOopCid(Collections.singletonList(id));
        }
        if (CollectionUtils.isEmpty(abpOops)){
            return CommonResult.success(new ArrayList<>());
        }
        List<QueryHitchPrjResp> resps = new ArrayList<>();
        AbpOop abpOop = abpOops.get(0);
        int index=1;
        if (isPrim){
            List<ObjectId> ids = new ArrayList<>();
            List<SubOop> subOops = abpOop.getSubOops();
            if (CollectionUtils.isEmpty(subOops)){
                return CommonResult.success(resps);
            }
            for (SubOop subOop : subOops) {
                TeIdNameCn subOopOop = subOop.getOop();
                if (subOopOop!=null){
                    ids.add(subOopOop.getCid());
                }
            }
            if (CollectionUtils.isEmpty(ids)){
                return CommonResult.success(resps);
            }
            //查询主项目的辅项目
            List<AbpOop> abpOopList= abpOopDao.queryDataByIds(ids);
            for (AbpOop oop : abpOopList) {
                QueryHitchPrjResp queryHitchPrjResp=setQueryHitchPrjResp(oop, index++);
                resps.add(queryHitchPrjResp);
            }
        }else {
            //辅项目的的主项目属性设值
            QueryHitchPrjResp queryHitchPrjResp=setQueryHitchPrjResp(abpOop, index);
            resps.add(queryHitchPrjResp);
        }
        return CommonResult.success(resps);
    }

    @Override
    public void adjustRateVersion(ObjectId planVerId, ObjectId ratePlanVerId, TeSysUser user) {
        //校验bu管理员角色
        serviceManager.queryBuAdminByUser(user);
        //1.查询费率版本信息
        SysVerMgt ratePlanVersion = sysVerMgtDao.findById(ratePlanVerId);
        if (ratePlanVersion==null){
            throw BusinessException.initExc("费率版本未查到,请联系管理员!");
        }
        if (!ratePlanVersion.getIsLocked()){
            throw BusinessException.initExc("费率版本未锁定,不能够绑定计划版本!");
        }
        //2.设置计划版本关联的费率版本信息
        PlanVersion linkVer = new PlanVersion();
        linkVer.setCid(ratePlanVerId);
        linkVer.setName(ratePlanVersion.getVerName());
        linkVer.setVerNo(String.valueOf(ratePlanVersion.getVerNo()));
        sysVerMgtDao.updateLinkerById(planVerId,linkVer);
        //3.调用选择的费率重新进行计算人工费、直接费用、事业部分摊、区域分摊、销售费用
        planningBgtService.syncBudgetInfo(planVerId);
//        feeCalculationService.calcBuHcFee(planVerId, ratePlanVerId);
//        feeCalculationService.calcAreaHcFee(planVerId, ratePlanVerId);
//        feeCalculationService.calcSalesFee(planVerId, ratePlanVerId);
        feeCalculationService.calculateSharedCosts(planVerId, null);
    }

    @Override
    public void lockAbpOop(ObjectId prjId, Boolean isLocked, String buCode) {
        AbpOop abpOop = queryAbpOop(prjId);
        Assert.notNull(abpOop, "经营计划不存在");
        this.updateLock(abpOop.getId(), abpOop.getProv().getCid(), buCode, isLocked, true);
    }

    @Override
    public AbpOop queryAbpOop(ObjectId prjId) {
        TePrjInfo prjInfo = prjInfoDao.findPrjInfoByPrjId(prjId);
        Assert.notNull(prjInfo, "项目定义不存在");
        String proCode = prjInfo.getPrjCode();
        // codeName、buCode、addTime、isValid
        List<PlanVersion> planVersions = findVersionByBuCode(prjInfo.getSbuId());
        PlanVersion planVersion = planVersions.get(0);
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_cn, proCode));
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVersion.getCid()));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.common__id);
        fieldNames.add(DbFieldName.AbpOop.isLocked);
        fieldNames.add(DFN.common_addTime);
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
        List<AbpOop> abpOops = abpOopDao.findByFieldAndConds(conds, fieldNames, sort);
        return CollectionUtils.isEmpty(abpOops) ? null : abpOops.get(0);
    }

    @Override
    public PmsAbpOopVO queryPmsAbpOop(ObjectId prjId, ObjectId bmkVerId) {
        TePrjInfo prjInfo = prjInfoDao.findPrjInfoByPrjId(prjId);
        Assert.notNull(prjInfo, "项目定义不存在");
        String proCode = prjInfo.getPrjCode();
        TePrjInfoPrjBmks maxPrjBmkVer = serviceManager.getMaxPrjBmkVer(prjInfo.getPrjBmks(), bmkVerId);
        AbpOop abpOop = null;
        if (maxPrjBmkVer != null && maxPrjBmkVer.getOop() != null
                && maxPrjBmkVer.getOop().getCid() != null){
            ObjectId oopId = maxPrjBmkVer.getOop().getCid();
            abpOop = abpOopDao.findById(oopId);
        }else{
            // codeName、buCode、addTime、isValid
//            List<PlanVersion> planVersions = findVersionByBuCode(prjInfo.getSbuId());
            List<PlanVersion> planVersions = serviceManager.findAllPlanVerId(prjInfo.getSbuId(), Constant.ABP_BUSINESS_PLAN_VERSION_ID);
            PlanVersion planVersion = planVersions.get(0);
            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DFN.common_cn, proCode));
            conds.add(new DC_E(DFN.common_isValid, true));
            conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVersion.getCid()));
            List<DbFieldName> fieldNames = new ArrayList<>();
            fieldNames.add(DFN.common__id);
            fieldNames.add(DbFieldName.AbpOop.isLocked);
            fieldNames.add(DFN.common_addTime);
            fieldNames.add(DFN.common_cn);
            fieldNames.add(DbFieldName.AbpOop.linkedOrderCode);
            Sort sort = Sort.by(Sort.Direction.DESC, DFN.common_addTime.n());
            List<AbpOop> abpOops = abpOopDao.findByFieldAndConds(conds, fieldNames, sort);
            if (!CollectionUtils.isEmpty(abpOops)) {
                abpOop = abpOops.get(0);
            }
        }

        PmsAbpOopVO pmsAbpOopVO = new PmsAbpOopVO();
        if (abpOop == null) {
            return pmsAbpOopVO;
        }
        pmsAbpOopVO.setProName(prjInfo.getPrjName());
        pmsAbpOopVO.setId(abpOop.getId());
        pmsAbpOopVO.setProCode(abpOop.getCodeName());
        pmsAbpOopVO.setOrderId(abpOop.getLinkedOrderCode());
        return pmsAbpOopVO;
    }

    @Override
    public Boolean queryAbpOopIsLocked(ObjectId abpOopId) {
        AbpOop abpOop = abpOopDao.findById(abpOopId);
        return abpOop.getIsLocked();
    }

    @Override
    public Boolean whetherCanSubmit(TeSysUser user, ObjectId areaId, ObjectId planVerId) {
        //查询当前区域信息
        TeSysDef sysDef = sysDefDao.findById(areaId);
        List<TeSysDef>sysDefs=new ArrayList<>();
        if (sysDef.getDefType().getDefTypeCodeName().equals(Constant.BIG_REGION)){
            //当前区域为大区
            //查询上一次层级信息
            sysDefs=iSysDefDao.queryRegionByBigRegionId(areaId);
        }else if (sysDef.getDefType().getDefTypeCodeName().equals(Constant.REGION)){
            //当前区域为区域
            //查询上一次层级信息
            sysDefs=iSysDefDao.queryProvByRegionId(areaId);
        }else {
            //当前区域为bu
            sysDefs=iSysDefDao.queryBigRegionByBuId(areaId);
        }
        if (CollectionUtils.isEmpty(sysDefs)){
            throw new BaseException("未查到该区域上一层级信息,请联系管理员");
        }
        List<ObjectId> areaIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
        //查询当前区域上一层级是否已提交
        List<AbpOprt> abpOprts = abpOprtDao.querySubmitDataByPlanVerIdAndProvId(planVerId, areaIds);
        return areaIds.size() == abpOprts.size();
    }

    @Override
    public PageListResult getPrjPlanFormulationData(QueryPlanResp queryPlanResp,TeSysUser loginUser) {
        PageListResult result = new PageListResult();
//        SysDef sbu = sysDefService.getSysDefByCodeName(loginUser.getSbuId(), SysDefTypeCodeName.AI_BU);
//        if (sbu == null){
//            return result;
//        }
//        SysVerMgt notLockedMaxVer = getNotLockedMaxVer(sbu);
//        if (notLockedMaxVer == null){
//            return result;
//        }
        if (StringUtil.isNotNull(queryPlanResp.getPmBuName())) {
            return this.getPrjPlanFormulationData2(queryPlanResp, loginUser);
        }

        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataMaxVersion(loginUser.getSbuId(), Constant.ABP_BUSINESS_PLAN_VERSION_ID, false);
        if (CollectionUtils.isEmpty(sysVerMgts)) {
            return result;
        }
        SysVerMgt mgt = sysVerMgts.get(0);
        //查询当前登陆人角色
        List<ObjectId> roleUserIds = new ArrayList<>();
        roleUserIds.add(loginUser.getId());
        //abp管理员
        List<TeSysDefRoleUser> abpBuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), Constant.ABP_ADMIN_ID, roleUserIds, null);
        //bu预算管理员
        List<TeSysDefRoleUser> BuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);
        //省份运营管理员
        List<TeSysDefRoleUser> provOperateAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.ABP_PROV.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);

        //查询abpOop表中符合当前计划年份、isValid=true、是当前登录人所属BU的所有版本信息展示在页面中
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DFN.common_isValid,false,true));
        conditions.add(new DC_E(DFN.AbpOop.isDeleted,true,true));
        conditions.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), mgt.getId()));
        if (queryPlanResp.getProvId() != null){
            conditions.add(new DC_E(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid), queryPlanResp.getProvId()));
        }else {
            if (CollectionUtils.isEmpty(abpBuAdmins) && CollectionUtils.isEmpty(BuAdmins) && !CollectionUtils.isEmpty(provOperateAdmins)){
                List<ObjectId> provIds = provOperateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(provIds)){
                    conditions.add(new DC_I<ObjectId>(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid), provIds));
                }
            }
        }
        //模糊查询
        String codeName = queryPlanResp.getCodeName();
        String projectCode = queryPlanResp.getLinkedOrderCode();
        if (StringUtil.isNotNull(codeName) && StringUtil.isNull(projectCode)) {
            //订单流水号:合同在linkedOrderCode中，订单对应codeName
            List<IDbCondition> or = new ArrayList<>();
            or.add(new DC_L(DbFieldName.common_cn, codeName, true));
            or.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
            conditions.add(new DC_OR(or));
        } else if (StringUtil.isNotNull(codeName) && StringUtil.isNotNull(projectCode)) {
            conditions.add(new DC_L(DbFieldName.AbpOop.linkedOrderCode, codeName, true));
            conditions.add(new DC_L(DbFieldName.common_cn, projectCode, true));
        } else if (StringUtil.isNull(codeName) && StringUtil.isNotNull(projectCode)) {
            //项目代码：目前只有合同有projectCode
            conditions.add(new DC_L(DbFieldName.common_cn, projectCode, true));
        }
        Boolean isPmUserEmpty = queryPlanResp.getIsPmUserEmpty();
        if (isPmUserEmpty != null && isPmUserEmpty){
            conditions.add(new DC_E(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName),null));
        }
        String pmName = queryPlanResp.getPmName();
        if (StringUtil.isNotNull(pmName)) {
            //项目经理
            List<DbFieldName> fieldNameList = new ArrayList<>();
            fieldNameList.add(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName));
            fieldNameList.add(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName));
            conditions.add(new DC_F(fieldNameList, pmName));
        }
        String budgetProgress = queryPlanResp.getBudgetProgress();
        if (StringUtil.isNotNull(budgetProgress)) {
            //未开始清单：从“abpOop”中取出isValid为true下的且planVer为最新计划版本下的且pmsPlanStartDate为null下的所有记录。
            if (budgetProgress.equals("未开始")){
                conditions.add(new DC_E(DFN.AbpOop.pmsPlanStartDate,null));
            }
            //预算中清单：从“abpOop”中取出isValid为true下的且planVer为最新计划版本下的且pmsPlanStartDate不为null下的且isLocked为false下的所有记录
            if (budgetProgress.equals("预算中")){
                conditions.add(new DC_E(DFN.AbpOop.pmsPlanStartDate,null,true));
                conditions.add(new DC_E(DFN.AbpOop.isLocked,false));
            }
        }
        String pmIsChangedStr = queryPlanResp.getPmIsChangedStr();
        if (StringUtil.isNotNull(pmIsChangedStr)){
            if (pmIsChangedStr.equals(PM_IS_CHANGED)){
                conditions.add(new DC_E(DFN.AbpOop.pmIsChanged,true));
            }else if (pmIsChangedStr.equals(PM_NOT_CHANGED)){
                conditions.add(new DC_E(DFN.AbpOop.pmIsChanged,true,true));
            }
        }
        long count = abpOopDao.countByConds(conditions);
        if (count <= 0){
            return result;
        }
        Pager pager = null;
        if (queryPlanResp.getPageIndex() != null && queryPlanResp.getPageSize() !=null){
            pager = new Pager(queryPlanResp.getPageIndex(), queryPlanResp.getPageSize());
        }
        List<AbpOop> list = abpOopDao.findByFieldAndConds(conditions, null, null, pager);
        //过滤得到提前立项的id
        List<ObjectId> ids = list.stream().filter(s -> s.getType() != null && Constant.BACKFILLING_PROJECT_ID.equals(s.getType().getCid())).map(AbpOop::getId).collect(Collectors.toList());
        //查询提前立项的主项目
        List<AbpOop> abpOops = null;
        if (!CollectionUtils.isEmpty(ids)){
            abpOops = this.queryAbpOopByTypeCid(ids);
        }
        //辅项目对应主项目的code关系,键是辅项目的id，值是主项目的codeName
        Map<ObjectId, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(abpOops)) {
            for (AbpOop abpOop : abpOops) {
                List<SubOop> subOops = abpOop.getSubOops();
                for (SubOop subOop : subOops) {
                    TeIdNameCn subOopOop = subOop.getOop();
                    if (subOopOop != null) {
                        map.put(subOopOop.getCid(), abpOop.getCodeName());
                    }
                }
            }
        }
        List<QueryPlanResp> reslutList = setResponse(list, map);
        result.setList(reslutList);
        result.setCount(count);
        return result;
    }

    private PageListResult getPrjPlanFormulationData2(QueryPlanResp queryPlanResp, TeSysUser loginUser) {
        PageListResult result = new PageListResult();
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataMaxVersion(loginUser.getSbuId(), Constant.ABP_BUSINESS_PLAN_VERSION_ID, false);
        if (CollectionUtils.isEmpty(sysVerMgts)) {
            return result;
        }
        Boolean isPmUserEmpty = queryPlanResp.getIsPmUserEmpty();
        if (BooleanUtils.isTrue(isPmUserEmpty) && BooleanUtils.isFalse(queryPlanResp.getIsNotContainPbn())) {
            return result;
        }

        SysVerMgt mgt = sysVerMgts.get(0);
        //查询当前登陆人角色
        List<ObjectId> roleUserIds = new ArrayList<>();
        roleUserIds.add(loginUser.getId());
        //abp管理员
        List<TeSysDefRoleUser> abpBuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), Constant.ABP_ADMIN_ID, roleUserIds, null);
        //bu预算管理员
        List<TeSysDefRoleUser> BuAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.AI_BU.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);
        //省份运营管理员
        List<TeSysDefRoleUser> provOperateAdmins = sysDefRoleUserService.querySysDefRoleUsers(null, SysDefTypeCodeName.ABP_PROV.getValue(), StringUtil.toObjectId(SysDefConstants.BU_PRJ_BUDGET_ADMIN_DEF_ID), roleUserIds, null);

        //查询abpOop表中符合当前计划年份、isValid=true、是当前登录人所属BU的所有版本信息展示在页面中
        Criteria criteria = Criteria.where(DFN.common_isValid.n()).ne(false)
                .and(DFN.AbpOop.isDeleted.n()).ne(true)
                .and(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid).n()).is(mgt.getId());
                //.and(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName).n()).ne(null);

        if (queryPlanResp.getProvId() != null) {
            criteria.and(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).n()).is(queryPlanResp.getProvId());
        } else {
            if (CollectionUtils.isEmpty(abpBuAdmins) && CollectionUtils.isEmpty(BuAdmins) && !CollectionUtils.isEmpty(provOperateAdmins)) {
                List<ObjectId> provIds = provOperateAdmins.stream().filter(roleUser -> roleUser.getDefId() != null).map(TeSysDefRoleUser::getDefId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(provIds)) {
                    criteria.and(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid).n()).in(provIds);
                }
            }
        }
        String pmName = queryPlanResp.getPmName();
        //模糊查询
        String codeName = queryPlanResp.getCodeName();
        String projectCode = queryPlanResp.getLinkedOrderCode();
        if (StringUtil.isNotNull(codeName) && StringUtil.isNull(projectCode)) {
            //订单流水号:合同在linkedOrderCode中，订单对应codeName
            if (StringUtil.isNull(pmName)) {
                criteria.orOperator(Criteria.where(DbFieldName.common_cn.n()).regex(codeName, "i"),
                        Criteria.where(DbFieldName.AbpOop.linkedOrderCode.n()).regex(codeName, "i"));
            } else {
                Criteria criteriaCode = new Criteria();
                criteriaCode.orOperator(Criteria.where(DbFieldName.common_cn.n()).regex(codeName, "i"),
                        Criteria.where(DbFieldName.AbpOop.linkedOrderCode.n()).regex(codeName, "i"));

                //项目经理
                Criteria criteriaPm = new Criteria();
                criteriaPm.orOperator(Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName, "i"),
                        Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName).n()).regex(pmName, "i"));

                criteria.andOperator(criteriaCode, criteriaPm);
            }
        } else if (StringUtil.isNotNull(codeName) && StringUtil.isNotNull(projectCode)) {
            criteria.and(DbFieldName.AbpOop.linkedOrderCode.n()).regex(codeName, "i");
            criteria.and(DbFieldName.common_cn.n()).regex(projectCode, "i");
            if (StringUtil.isNotNull(pmName)) {
                //项目经理
                criteria.orOperator(Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName, "i"),
                        Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName).n()).regex(pmName, "i"));
            }
        } else if (StringUtil.isNull(codeName) && StringUtil.isNotNull(projectCode)) {
            //项目代码：目前只有合同有projectCode
            criteria.and(DbFieldName.common_cn.n()).regex(projectCode, "i");
            if (StringUtil.isNotNull(pmName)) {
                //项目经理
                criteria.orOperator(Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName, "i"),
                        Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName).n()).regex(pmName, "i"));
            }
        } else if (StringUtil.isNotNull(pmName)) {
            //项目经理
            criteria.orOperator(Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_userName).n()).regex(pmName, "i"),
                    Criteria.where(DbFieldName.AbpOop.pm.dot(DbFieldName.common_loginName).n()).regex(pmName, "i"));
        }

        String budgetProgress = queryPlanResp.getBudgetProgress();
        if (StringUtil.isNotNull(budgetProgress)) {
            //未开始清单：从“abpOop”中取出isValid为true下的且planVer为最新计划版本下的且pmsPlanStartDate为null下的所有记录。
            if (budgetProgress.equals("未开始")) {
                criteria.and(DFN.AbpOop.pmsPlanStartDate.n()).is(null);
            }
            //预算中清单：从“abpOop”中取出isValid为true下的且planVer为最新计划版本下的且pmsPlanStartDate不为null下的且isLocked为false下的所有记录
            if (budgetProgress.equals("预算中")) {
                criteria.and(DFN.AbpOop.pmsPlanStartDate.n()).ne(null);
                criteria.and(DFN.AbpOop.isLocked.n()).is(false);
            }
        }
        String pmIsChangedStr = queryPlanResp.getPmIsChangedStr();
        if (StringUtil.isNotNull(pmIsChangedStr)) {
            if (pmIsChangedStr.equals(PM_IS_CHANGED)) {
                criteria.and(DFN.AbpOop.pmIsChanged.n()).is(true);
            } else if (pmIsChangedStr.equals(PM_NOT_CHANGED)) {
                criteria.and(DFN.AbpOop.pmIsChanged.n()).ne(true);
            }
        }

        CountOperation countOperation = Aggregation.count().as("count");
        ProjectionOperation projectionOperation = Aggregation.project()
                .andInclude(DbFieldName.common__id.n())
                .andInclude(DbFieldName.AbpOop.type.n())
                .andInclude(DbFieldName.AbpOop.isLocked.n())
                .andInclude(DbFieldName.common_isValid.n())
                .andInclude(DbFieldName.AbpOop.isDeleted.n())
                .andInclude(DbFieldName.AbpOop.mainPrjCode.n())
                .andInclude(DbFieldName.common_name.n())
                .andInclude(DbFieldName.common_cn.n())
                .andInclude(DbFieldName.AbpOop.linkedOrderCode.n())
                .andInclude(DbFieldName.AbpOop.pmsPlanEndDate.n())
                .andInclude(DbFieldName.AbpOop.pmsPlanStartDate.n())
                .andInclude(DbFieldName.AbpOop.planVer.n())
                .andInclude(DbFieldName.AbpOop.subOops.n())
                .andInclude(DbFieldName.AbpOop.netSalesM.n())
                .andInclude(DbFieldName.AbpOop.netSalesL.n())
                .andInclude(DbFieldName.AbpOop.backlogAmt.n())
                .andInclude(DbFieldName.AbpOop.signDate.n())
                .andInclude(DbFieldName.AbpOop.customBudgetType.n())
                .andInclude(DbFieldName.AbpOop.contractType.n())
                .andInclude(DbFieldName.AbpOop.prjType.n())
                .andInclude(DbFieldName.AbpOop.sales.n())
                .andInclude(DbFieldName.AbpOop.pm.n())
                .andInclude(DbFieldName.AbpOop.prjLevel.n())
                .andInclude(DbFieldName.AbpOop.pls.n())
                .andInclude(DbFieldName.AbpOop.oopCode.n())
                .andInclude(DbFieldName.AbpOop.pmIsChanged.n())
                .andInclude(DbFieldName.AbpOop.prov.n())
                .andInclude(DbFieldName.AbpOop.netSales.n())
                .andExpression("{\n" +
                        "                \"$cond\": {\n" +
                        "                    \"if\": {\n" +
                        "                        \"$eq\": {\"$sysUser.isValid\",true}\n" +
                        "                    },\n" +
                        "                    \"then\":\"$sysUser.sbuName\",\n" +
                        "                    \"else\": \"\"\n" +
                        "                }\n" +
                        "            }").as(DbFieldName.AbpOop.buCode.n());

        Criteria pmBuNameCriteria = null;
        if (BooleanUtils.isTrue(queryPlanResp.getIsNotContainPbn())) {
            pmBuNameCriteria = new Criteria();
            pmBuNameCriteria.orOperator(Criteria.where("pm.userId").is(null),
                    Criteria.where("sysUser.isValid").is(true).and("sysUser.sbuName").not().regex(queryPlanResp.getPmBuName()),
                    Criteria.where("sysUser.isValid").is(false));
        } else {
            pmBuNameCriteria = Criteria.where("sysUser.isValid").is(true).and("sysUser.sbuName").regex(queryPlanResp.getPmBuName());
        }

        Aggregation countAgg = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.lookup("sysUser",
                        DbFieldName.AbpOop.pm.dot(DbFieldName.common_userId).n(),
                        DbFieldName.common__id.n(),
                        "sysUser"),
                Aggregation.unwind("sysUser", true),
                Aggregation.match(pmBuNameCriteria),
                Aggregation.group(DbFieldName.common__id.n()),
                Aggregation.project(DFN.common__id.n()),
                Aggregation.facet(countOperation).as("count")
        );

        List<Map> mappedResults = mongoTemplate.aggregate(countAgg, DBT.ABP_OOP.n(), Map.class).getMappedResults();
        List<Map<String, Integer>> mapList = (List<Map<String, Integer>>) mappedResults.get(0).get("count");
        Integer count = 0;
        if (!CollectionUtils.isEmpty(mapList)) {
            count = mapList.get(0).get("count");
        }
        if (count <= 0) {
            return result;
        }
        Aggregation agg = null;
        if (queryPlanResp.getPageSize() != null && queryPlanResp.getPageIndex() != null) {
            agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.lookup("sysUser",
                            DbFieldName.AbpOop.pm.dot(DbFieldName.common_userId).n(),
                            DbFieldName.common__id.n(),
                            "sysUser"),
                    Aggregation.unwind("sysUser", true),
                    Aggregation.match(pmBuNameCriteria),
                    projectionOperation,
                    Aggregation.group(DbFieldName.common__id.n())
                            .first(DbFieldName.AbpOop.type.n()).as(DbFieldName.AbpOop.type.n())
                            .first(DbFieldName.AbpOop.isLocked.n()).as(DbFieldName.AbpOop.isLocked.n())
                            .first(DbFieldName.common_isValid.n()).as(DbFieldName.common_isValid.n())
                            .first(DbFieldName.AbpOop.isDeleted.n()).as(DbFieldName.AbpOop.isDeleted.n())
                            .first(DbFieldName.AbpOop.mainPrjCode.n()).as(DbFieldName.AbpOop.mainPrjCode.n())
                            .first(DbFieldName.common_name.n()).as(DbFieldName.common_name.n())
                            .first(DbFieldName.common_cn.n()).as(DbFieldName.common_cn.n())
                            .first(DbFieldName.AbpOop.linkedOrderCode.n()).as(DbFieldName.AbpOop.linkedOrderCode.n())
                            .first(DbFieldName.AbpOop.pmsPlanEndDate.n()).as(DbFieldName.AbpOop.pmsPlanEndDate.n())
                            .first(DbFieldName.AbpOop.pmsPlanStartDate.n()).as(DbFieldName.AbpOop.pmsPlanStartDate.n())
                            .first(DbFieldName.AbpOop.planVer.n()).as(DbFieldName.AbpOop.planVer.n())
                            .first(DbFieldName.AbpOop.subOops.n()).as(DbFieldName.AbpOop.subOops.n())
                            .first(DbFieldName.AbpOop.netSalesM.n()).as(DbFieldName.AbpOop.netSalesM.n())
                            .first(DbFieldName.AbpOop.netSalesL.n()).as(DbFieldName.AbpOop.netSalesL.n())
                            .first(DbFieldName.AbpOop.backlogAmt.n()).as(DbFieldName.AbpOop.backlogAmt.n())
                            .first(DbFieldName.AbpOop.signDate.n()).as(DbFieldName.AbpOop.signDate.n())
                            .first(DbFieldName.AbpOop.customBudgetType.n()).as(DbFieldName.AbpOop.customBudgetType.n())
                            .first(DbFieldName.AbpOop.contractType.n()).as(DbFieldName.AbpOop.contractType.n())
                            .first(DbFieldName.AbpOop.prjType.n()).as(DbFieldName.AbpOop.prjType.n())
                            .first(DbFieldName.AbpOop.sales.n()).as(DbFieldName.AbpOop.sales.n())
                            .first(DbFieldName.AbpOop.pm.n()).as(DbFieldName.AbpOop.pm.n())
                            .first(DbFieldName.AbpOop.prjLevel.n()).as(DbFieldName.AbpOop.prjLevel.n())
                            .first(DbFieldName.AbpOop.pls.n()).as(DbFieldName.AbpOop.pls.n())
                            .first(DbFieldName.AbpOop.oopCode.n()).as(DbFieldName.AbpOop.oopCode.n())
                            .first(DbFieldName.AbpOop.pmIsChanged.n()).as(DbFieldName.AbpOop.pmIsChanged.n())
                            .first(DbFieldName.AbpOop.prov.n()).as(DbFieldName.AbpOop.prov.n())
                            .first(DbFieldName.AbpOop.netSales.n()).as(DbFieldName.AbpOop.netSales.n())
                            .first(DbFieldName.AbpOop.buCode.n()).as(DbFieldName.AbpOop.buCode.n()),
                    Aggregation.skip(queryPlanResp.getPageIndex() * queryPlanResp.getPageSize()),
                    Aggregation.limit(queryPlanResp.getPageSize())
            );
        } else {
            agg = Aggregation.newAggregation(
                    Aggregation.match(criteria),
                    Aggregation.lookup("sysUser",
                            DbFieldName.AbpOop.pm.dot(DbFieldName.common_userId).n(),
                            DbFieldName.common_userId.n(),
                            "sysUser"),
                    Aggregation.unwind("sysUser", true),
                    Aggregation.match(pmBuNameCriteria),
                    projectionOperation,
                    Aggregation.group(DbFieldName.common__id.n())
                            .first(DbFieldName.AbpOop.type.n()).as(DbFieldName.AbpOop.type.n())
                            .first(DbFieldName.AbpOop.isLocked.n()).as(DbFieldName.AbpOop.isLocked.n())
                            .first(DbFieldName.common_isValid.n()).as(DbFieldName.common_isValid.n())
                            .first(DbFieldName.AbpOop.isDeleted.n()).as(DbFieldName.AbpOop.isDeleted.n())
                            .first(DbFieldName.AbpOop.mainPrjCode.n()).as(DbFieldName.AbpOop.mainPrjCode.n())
                            .first(DbFieldName.common_name.n()).as(DbFieldName.common_name.n())
                            .first(DbFieldName.common_cn.n()).as(DbFieldName.common_cn.n())
                            .first(DbFieldName.AbpOop.linkedOrderCode.n()).as(DbFieldName.AbpOop.linkedOrderCode.n())
                            .first(DbFieldName.AbpOop.pmsPlanEndDate.n()).as(DbFieldName.AbpOop.pmsPlanEndDate.n())
                            .first(DbFieldName.AbpOop.pmsPlanStartDate.n()).as(DbFieldName.AbpOop.pmsPlanStartDate.n())
                            .first(DbFieldName.AbpOop.planVer.n()).as(DbFieldName.AbpOop.planVer.n())
                            .first(DbFieldName.AbpOop.subOops.n()).as(DbFieldName.AbpOop.subOops.n())
                            .first(DbFieldName.AbpOop.netSalesM.n()).as(DbFieldName.AbpOop.netSalesM.n())
                            .first(DbFieldName.AbpOop.netSalesL.n()).as(DbFieldName.AbpOop.netSalesL.n())
                            .first(DbFieldName.AbpOop.backlogAmt.n()).as(DbFieldName.AbpOop.backlogAmt.n())
                            .first(DbFieldName.AbpOop.signDate.n()).as(DbFieldName.AbpOop.signDate.n())
                            .first(DbFieldName.AbpOop.customBudgetType.n()).as(DbFieldName.AbpOop.customBudgetType.n())
                            .first(DbFieldName.AbpOop.contractType.n()).as(DbFieldName.AbpOop.contractType.n())
                            .first(DbFieldName.AbpOop.prjType.n()).as(DbFieldName.AbpOop.prjType.n())
                            .first(DbFieldName.AbpOop.sales.n()).as(DbFieldName.AbpOop.sales.n())
                            .first(DbFieldName.AbpOop.pm.n()).as(DbFieldName.AbpOop.pm.n())
                            .first(DbFieldName.AbpOop.prjLevel.n()).as(DbFieldName.AbpOop.prjLevel.n())
                            .first(DbFieldName.AbpOop.pls.n()).as(DbFieldName.AbpOop.pls.n())
                            .first(DbFieldName.AbpOop.oopCode.n()).as(DbFieldName.AbpOop.oopCode.n())
                            .first(DbFieldName.AbpOop.pmIsChanged.n()).as(DbFieldName.AbpOop.pmIsChanged.n())
                            .first(DbFieldName.AbpOop.prov.n()).as(DbFieldName.AbpOop.prov.n())
                            .first(DbFieldName.AbpOop.netSales.n()).as(DbFieldName.AbpOop.netSales.n())
                            .first(DbFieldName.AbpOop.buCode.n()).as(DbFieldName.AbpOop.buCode.n())
            );
        }


        List<AbpOop> list = mongoTemplate.aggregate(agg, DBT.ABP_OOP.n(), AbpOop.class).getMappedResults();
        //过滤得到提前立项的id
        List<ObjectId> ids = list.stream().filter(s -> s.getType() != null && Constant.BACKFILLING_PROJECT_ID.equals(s.getType().getCid())).map(AbpOop::getId).collect(Collectors.toList());
        //查询提前立项的主项目
        List<AbpOop> abpOops = null;
        if (!CollectionUtils.isEmpty(ids)) {
            abpOops = this.queryAbpOopByTypeCid(ids);
        }
        //辅项目对应主项目的code关系,键是辅项目的id，值是主项目的codeName
        Map<ObjectId, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(abpOops)) {
            for (AbpOop abpOop : abpOops) {
                List<SubOop> subOops = abpOop.getSubOops();
                for (SubOop subOop : subOops) {
                    TeIdNameCn subOopOop = subOop.getOop();
                    if (subOopOop != null) {
                        map.put(subOopOop.getCid(), abpOop.getCodeName());
                    }
                }
            }
        }
        List<QueryPlanResp> reslutList = setResponse(list, map);
        result.setList(reslutList);
        result.setCount(count);
        return result;
    }

    /**
     * 取消手工修改PM
     */
    @Override
    public void cancelPmsChanged(ObjectId abpOopId) {
        List<UpdataData> updataDataList = new ArrayList<>();
        updataDataList.add(new UpdataData(DFN.AbpOop.pmIsChanged,null));
        abpOopDao.updateById(abpOopId,updataDataList,true);
    }

    @Override
    public List<Map<String, Object>> exportPrjPlanFormulationData(QueryPlanResp queryPlanResp, TeSysUser loginUser) {
        //返回值
        List<Map<String, Object>> dataMap = new ArrayList<>();
        PageListResult prjPlanFormulationData = this.getPrjPlanFormulationData(queryPlanResp, loginUser);
        if (CollectionUtils.isEmpty(prjPlanFormulationData.getList())){
            return dataMap;
        }
        List<QueryPlanResp> list = (List<QueryPlanResp>)prjPlanFormulationData.getList();
        int no = 0;
        for (QueryPlanResp vo : list) {
            no++;
            Map<String, Object> map = new HashMap<>();
            map.put("no",no);
            map.put("planVer",vo.getPlanVer() == null ? "" : vo.getPlanVer().getName());
            StringBuilder progressStr = new StringBuilder();
            if (vo.getPmsPlanStartDate() == null){
                progressStr.append("未开始");
            }else if (vo.getIsLocked() == null || !vo.getIsLocked()){
                progressStr.append("预算中");
            }
            map.put("budgetProgress",progressStr.toString());
            map.put("codeName",vo.getCodeName());
            map.put("linkedOrderCode",vo.getLinkedOrderCode());
            map.put("name",vo.getName());
            map.put("prjLevel",vo.getPrjLevel() == null ? "" : vo.getPrjLevel().getName());
            StringBuilder pmBuilder = new StringBuilder();
            if (vo.getPm() != null){
               if (StringUtil.isNotNull(vo.getPm().getUserName()) && StringUtil.isNotNull(vo.getPm().getLoginName())){
                   pmBuilder.append(vo.getPm().getUserName() +"/"+vo.getPm().getLoginName());
               }else if (StringUtil.isNotNull(vo.getPm().getUserName())){
                   pmBuilder.append(vo.getPm().getUserName());
               }else if (StringUtil.isNotNull(vo.getPm().getLoginName())){
                   pmBuilder.append(vo.getPm().getLoginName());
               }
            }
            map.put("pm", pmBuilder.toString());
            map.put("pmBuName", vo.getPmBuName());
            map.put("pl", vo.getPl());
            map.put("pmIsChanged",vo.getPmIsChangedStr());
            dataMap.add(map);
        }
        return dataMap;
    }

    private SysVerMgt getNotLockedMaxVer(SysDef sbu){
        List<IDbCondition> conds = new ArrayList<>();
        int year = DateUtil.getYear(new Date());
        conds.add(new DC_E(DFN.sysVerMgt__year,String.valueOf(year)));
        conds.add(new DC_E(DFN.sysVerMgt__verMgtType.dot(DFN.common_cid),Constant.ABP_BUSINESS_PLAN_VERSION_ID));
        conds.add(new DC_E(DFN.sysVerMgt__isLocked,false));
        conds.add(new DC_E(DFN.sysVerMgt__srcDef.dot(DbFieldName.common_cn), sbu.getCodeName()));

        Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysVerMgt__verNo.n());
        Pager pager = new Pager(0,1);
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.findByFieldAndConds(conds, null, sort, pager);
        return CollectionUtils.isEmpty(sysVerMgts) ? null : sysVerMgts.get(0);
    }

    //查询子项目详细信息
    @Override
    public List<Map<String,Object>> getSubPrjsDetail(ObjectId prjId) {
        //查询项目集信息
        TePrjInfo prjInfo = prjInfoDao.findPrjInfoByPrjId(prjId);
        Assert.notNull(prjInfo,"项目信息未查到");
        String sbuId = prjInfo.getSbuId();
        Assert.notNull(sbuId,"项目集BU为空");
        //获取子项目
        List<TePrjInfoSubPrj> subPrjs = prjInfo.getSubPrjs();
        if (CollectionUtils.isEmpty(subPrjs)){
            return null;
        }
        //子项目id
        List<ObjectId> subPrjIds = subPrjs.stream().filter(tePrjInfoSubPrj -> tePrjInfoSubPrj != null && tePrjInfoSubPrj.getCid() != null).map(TePrjInfoSubPrj::getCid).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(subPrjIds)){
            return null;
        }
        //子项目详细信息
        List<TePrjInfo> subPrjInfos = prjInfoDao.findPrjInfosByPrjIds(subPrjIds);
        if (CollectionUtils.isEmpty(subPrjInfos)){
            return null;
        }
        //其中POM全周期取值：以下字段需按照子项目编码到itf库 itfPrjBgtStat、itfAiPpb查找有效数据
        // 正式(人月)：(itfAiPpb.totalLabBudget +itfAiPpb.totalLabBudgetRd)/工作日天数  （天数取sysDef中5c6d16b6900add501a1418f9对应配置的185的每月工作日天数）
        // 外包(人月)：itfAiPpb.totalLabBudgetWb/工作日天数
        // 直接费用(K)：itfPrjBgtStat.directFeebgtAll/1000
        //  技术分包(K)：itfPrjBgtStat.techOsFeeAll/1000
        Map<String,Map<String,Double>> prjEmpAndOsMap = new HashMap<>();
        Map<String,Map<String,Double>> prjDirectAndJsfbMap = new HashMap<>();
        List<String> subPrjCodes = subPrjInfos.stream().filter(prj -> StringUtil.isNotNull(prj.getPrjCode())).map(TePrjInfo::getPrjCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(subPrjCodes)){
            SysDef avgWorkDay = sysDefService.getSysDefById(Constant.Prj_Budget_Para_Id);
            List<ItfAiPpb> aiPpbList = aiPpbFeignClient.queryPpbsByProjectCodes(subPrjCodes).getData(true);
            if (!CollectionUtils.isEmpty(aiPpbList)){
                //根据项目分组
                for (ItfAiPpb aiPpb : aiPpbList){
                    String projectCode = aiPpb.getProjectCode();
                    Map<String, Double> empAndOsMap = prjEmpAndOsMap.getOrDefault(projectCode,new HashMap<>());
                    double empMonth = (avgWorkDay == null || StringUtil.isNull(avgWorkDay.getValue())) ? 0d : BigDecimalUtils.divideDouble(DoubleUtil.getNotNull(aiPpb.getTotalLabBudget()) + DoubleUtil.getNotNull(aiPpb.getTotalLabBudgetRd()),DoubleUtil.getNotNull(avgWorkDay.getValue()),2);
                    double empOsMonth = (avgWorkDay == null || StringUtil.isNull(avgWorkDay.getValue())) ? 0d : BigDecimalUtils.divideDouble(DoubleUtil.getNotNull(aiPpb.getTotalLabBudgetWb()),DoubleUtil.getNotNull(avgWorkDay.getValue()),2);
                    Double emp = empAndOsMap.getOrDefault("emp", 0d);
                    empAndOsMap.put("emp",BigDecimalUtils.addDouble(emp,empMonth,2));
                    Double empOs = empAndOsMap.getOrDefault("empOs", 0d);
                    empAndOsMap.put("empOs",BigDecimalUtils.addDouble(empOs,empOsMonth,2));
                    prjEmpAndOsMap.put(projectCode,empAndOsMap);
                }
            }
            List<ItfPrjBgtStat> prjBgtStats = prjBgtStatFeignClient.listByPrjCode(subPrjCodes).getData(true);
            if (!CollectionUtils.isEmpty(prjBgtStats)){
                for (ItfPrjBgtStat prjBgtStat : prjBgtStats){
                    String codeName = prjBgtStat.getPrj().getCodeName();
                    Map<String, Double> directAndJsfbMap = prjDirectAndJsfbMap.getOrDefault(codeName,new HashMap<>());
                    Double directFeeBgtAll = prjBgtStat.getDirectFeeBgtAll();
                    Double direct = directAndJsfbMap.getOrDefault("direct", 0d);
                    directAndJsfbMap.put("direct",BigDecimalUtils.doubleDivide(direct+directFeeBgtAll,1000d,2));
                    Double techOsFeeAll = prjBgtStat.getTechOsFeeAll();
                    Double jsfb = directAndJsfbMap.getOrDefault("jsfb", 0d);
                    directAndJsfbMap.put("jsfb",BigDecimalUtils.doubleDivide(jsfb+techOsFeeAll,1000d,2));
                    prjDirectAndJsfbMap.put(codeName,directAndJsfbMap);
                }
            }
        }
        List<TePrjInfoPrjBmks> prjSetPrjBmks = prjInfo.getPrjBmks();
        //获取最新版本
        Map<ObjectId, Double> forecastIncomeMap = null;
        if (!CollectionUtils.isEmpty(prjSetPrjBmks)){
            TePrjInfoPrjBmks maxPrjBmk = prjSetPrjBmks.get(prjSetPrjBmks.size() - 1);
            List<GoalSubPrjMs> goalSubPrjMs = maxPrjBmk.getGoalSubPrjMs();
            if (!CollectionUtils.isEmpty(goalSubPrjMs)){
                forecastIncomeMap = goalSubPrjMs.stream().filter(go -> go.getPrj() != null && go.getPrj().getCid() != null)
                        .collect(Collectors.toMap(go -> go.getPrj().getCid(), GoalSubPrjMs::getForecastIncome, (v1, v2) -> v2));
            }
        }

        //oopid集合
        List<ObjectId> oopIds = new ArrayList<>();
        List<String> prjCodeList = new ArrayList<>();
        for (TePrjInfo tePrjInfo : subPrjInfos){
            //子项目基准版本
            List<TePrjInfoPrjBmks> prjBmks = tePrjInfo.getPrjBmks();
            if (!CollectionUtils.isEmpty(prjBmks)){
                TePrjInfoPrjBmks maxPrjBmk = prjBmks.get(prjBmks.size() - 1);
                if(maxPrjBmk == null || maxPrjBmk.getOop() == null || maxPrjBmk.getOop().getCid() == null){
                    if (StringUtil.isNotNull(tePrjInfo.getPrjCode())){
                        prjCodeList.add(tePrjInfo.getPrjCode());
                    }
                }else {
                    oopIds.add(maxPrjBmk.getOop().getCid());
                }
            }else if (StringUtil.isNotNull(tePrjInfo.getPrjCode())){
                prjCodeList.add(tePrjInfo.getPrjCode());
            }
        }
        //整合oop信息
        Map<String, List<AbpOop>> abpMap = new HashMap<>();
        //查询oop信息
        if (!CollectionUtils.isEmpty(oopIds) || !CollectionUtils.isEmpty(prjCodeList)){
            List<IDbCondition> conds = new ArrayList<>();
            List<IDbCondition> andConds = new ArrayList<>();
            List<IDbCondition> orConds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(oopIds)){
                orConds.add(new DC_I<ObjectId>(DFN.common__id,oopIds));
            }
            //子项目信息中，等级、预测订单编码、净销售额、产品线占比信息，若未绑定oop，从abpOop中取值时，增加BU的条件。即取 项目集所在BU下的，addTime最大的记录的信息
            if (!CollectionUtils.isEmpty(prjCodeList)){
                andConds.add(new DC_E(DFN.common_isValid,true));
                andConds.add(new DC_I<String>(DFN.common_cn,prjCodeList));
                andConds.add(new DC_E(DFN.AbpOop.buCode,sbuId));
                orConds.add(new DC_AO(andConds));
            }
            conds.add(new DC_OR(orConds));
            List<AbpOop> abpOops = abpOopDao.findByFieldAndConds(conds,null);
            if (!CollectionUtils.isEmpty(abpOops)){
                abpMap = abpOops.stream().filter(abpOop -> abpOop != null && StringUtil.isNotNull(abpOop.getCodeName())).collect(Collectors.groupingBy(abpOop -> abpOop.getCodeName()));
            }
        }
        //整合返回数据
        List<Map<String,Object>> results = new ArrayList<>();
        for (TePrjInfo tePrjInfo : subPrjInfos){
            Map<String,Object> result = new HashMap<>();
            result.put("prjId",tePrjInfo.getPrjId());
            result.put("prjCode",tePrjInfo.getPrjCode());
            result.put("prjName",tePrjInfo.getPrjName());
            //oop信息
            List<AbpOop> abpOops = abpMap.get(tePrjInfo.getPrjCode());
            //不为空时
            if (!CollectionUtils.isEmpty(abpOops)){
                List<AbpOop> abpOopList = abpOops.stream().filter(abpOop -> abpOop.getAddTime() != null).collect(Collectors.toList());
                AbpOop abpOop = null;
                if (CollectionUtils.isEmpty(abpOopList)){
                    abpOop = abpOops.get(0);
                }else {
                    abpOop = abpOopList.stream().max((a, b) -> a.getAddTime().compareTo(b.getAddTime())).get();
                }
                if (abpOop != null){
                    //获取预测订单编号
                    result.put("linkedOrderCode",abpOop.getLinkedOrderCode());
                    StringBuilder plBuilder = new StringBuilder();
                    //获取产品线占比
                    List<AbpOopPl> pls = abpOop.getPls();
                    if (!CollectionUtils.isEmpty(pls)){
                        //过滤isValid为false的
                        pls = pls.stream().filter(abpOopPl -> abpOopPl.getIsValid() == null || abpOopPl.getIsValid()).collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(pls)){
                            //进行字符串拼接
                            for (AbpOopPl pl : pls){
                                TeIdNameCn pl1 = pl.getPl();
                                Double pct = pl.getPct();
                                plBuilder.append((pl1 == null ? "" : StringUtil.getNotNullStr(pl1.getName()) + " ") + (pct == null ? "0%" : pct+"%;"));
                            }
                            //基准页面，子项目集清单，若该产品线占比=100%，则增加“基线”的查看功能
                            if (pls.size() == 1 && pls.get(0).getPct() == 100){
                                AbpOopPl pl = pls.get(0);
                                if (null != pl && null != pl.getPl() && null != pl.getPl().getName()){
                                    result.put("plName", pl.getPl().getName());

                                    List<TeSysDefCnfg> cnfgs = sysDefCnfgDao.getSysDefCnfgsBySrc(
                                            PMS_PRJ_LEVEL3_KEY_PRJ_INDICATOR_CNFG, pl.getPl().getCid());
                                    for (TeSysDefCnfg cnfg : cnfgs) {
                                        if (GM_ID.equals(cnfg.getFirstDef().getCid())){
                                            result.put("GM", cnfg.getValue());
                                        } else if (INCOME_PER_HUNDRED_ID.equals(cnfg.getFirstDef().getCid())){
                                            result.put("incomePerHundred", cnfg.getValue());
                                        } else if (DIRECT_FEE_PER_HUNDRED_ID.equals(cnfg.getFirstDef().getCid())){
                                            result.put("directFeePerHundred", cnfg.getValue());
                                        }
                                    }
                                }
                            }
                        }
                    }
                    result.put("pl",plBuilder.toString());
                    result.put("level",abpOop.getPrjLevel());
                }
            }

            result.put("netSaleAmt",tePrjInfo.getNetSaleAmt());
            result.put("pmsPlanStartDate",tePrjInfo.getPmsPlanStartDate());
            result.put("pmsPlanEndDate",tePrjInfo.getPmsPlanEndDate());
            Map<String, Double> empAndOsMap = prjEmpAndOsMap.get(tePrjInfo.getPrjCode());
            if (MapUtils.isNotEmpty(empAndOsMap)){
                result.putAll(empAndOsMap);
            }
            Map<String, Double> directAndJsfbMap = prjDirectAndJsfbMap.get(tePrjInfo.getPrjCode());
            if (MapUtils.isNotEmpty(directAndJsfbMap)){
                result.putAll(directAndJsfbMap);
            }
            if (MapUtils.isNotEmpty(forecastIncomeMap) && forecastIncomeMap.get(tePrjInfo.getPrjId()) != null){
                result.put("forecastIncome",BigDecimalUtils.divideDouble(forecastIncomeMap.get(tePrjInfo.getPrjId()),1000,2));
            }
            result.put("pom",tePrjInfo.getProjectStatusPom());
            results.add(result);
        }
        return results;
    }

    @Override
    public void updatePm(QueryPlanResp queryPlanResp, TeSysUser user) {
        ObjectId id = queryPlanResp.getId();
        AbpOop oop = abpOopDao.findById(id);
        if (oop == null){
            Assert.notNull(oop,"经营计划为空");
        }
        TeUser pm = queryPlanResp.getPm();
        TeUser oopPm = oop.getPm();
        String oopPmName="无";
        if (oopPm!=null){
            oopPmName=String.join("/",oopPm.getUserName(),oopPm.getJobCode());
        }
        String pmName="无";
        if (pm!=null){
            pmName=String.join("/",pm.getUserName(),pm.getJobCode());
        }
        if (!Objects.equals(pmName,oopPmName)){
            //pm改变需要有变更记录
            List<OperationInfo> oprtInfo = oop.getOprtInfo();
            if (CollectionUtils.isEmpty(oprtInfo)){
                oprtInfo=new ArrayList<>();
            }
            OperationInfo info = new OperationInfo();
            info.setOprtType("项目信息更新-更新项目经理");
            info.setOprtDesc(String.format("pm从%s更新为%s",oopPmName,pmName));
            info.setOprtTime(new Date());
            TeUser oprtUser = new TeUser();
            oprtUser.setUserId(user.getId());
            oprtUser.setUserName(user.getUserName());
            oprtUser.setJobCode(user.getJobCode());
            oprtUser.setLoginName(user.getLoginName());
            info.setOprtUser(oprtUser);
            oprtInfo.add(info);

            List<IDbCondition> conds=new ArrayList<>();
            conds.add(new DC_E(DbFieldName.common__id,id));

            List<UpdataData> updates=new ArrayList<>();
            updates.add(new UpdataData(DbFieldName.AbpOop.pm,pm));
            updates.add(new UpdataData(DbFieldName.AbpOop.oprtInfo,oprtInfo));
            updates.add(new UpdataData(DbFieldName.AbpOop.pmIsChanged,true));
            abpOopDao.updateByConds(conds,updates);
        }
    }

    @Override
    public boolean haveOrgAdminPms(ObjectId provId, TeSysUser user) {
        //1.判断当前登录人是否是区域及以上地区的组织管理员
        List<TeSysDef> sysDefs = serviceManager.queryProvWithAdmin(user);
        if (CollectionUtils.isEmpty(sysDefs)) {
            return false;
        }
        List<ObjectId> provIds = sysDefs.stream().map(TeSysDef::getId).collect(Collectors.toList());
        if (!provIds.contains(provId)) {
            return false;
        }
        return true;
    }

    @Override
    public boolean isAdminOfProvRegionBigRegion(TeSysUser user) {
        List<String> defTypeList = Arrays.asList(Constant.ABP_PROV_CODE_NAME, Constant.BIG_REGION, Constant.REGION);
        List<SysDefRoleUser> sysDefRoleUsers = sysDefRoleUserDao.listAdminByBuAndDefType(user.getSbuId(), defTypeList);
        if (CollectionUtils.isEmpty(sysDefRoleUsers)) {
            return false;
        }
        List<SysDefRoleUser> result = sysDefRoleUsers.stream().filter(item -> user.getId().equals(item.getRoleUser().getUserId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(result)) {
            return false;
        }
        return true;
    }

    private QueryHitchPrjResp setQueryHitchPrjResp(AbpOop abpOop, int index) {
        QueryHitchPrjResp queryHitchPrjResp = new QueryHitchPrjResp();
        queryHitchPrjResp.setId(abpOop.getId());
        queryHitchPrjResp.setIndex(index);
        queryHitchPrjResp.setName(abpOop.getName());
        queryHitchPrjResp.setCodeName(abpOop.getCodeName());
        TeIdNameCn type = abpOop.getType();
        if (type != null) {
            queryHitchPrjResp.setTypeName(type.getName());
        }
        return queryHitchPrjResp;
    }

    private List<StatusHist> sortAndDistStatus(AbpOprt abpOprt) {
        List<StatusHist> statusHist = abpOprt.getStatusHist();
        if (CollectionUtils.isEmpty(statusHist)) {
            statusHist = new ArrayList<>();
        }
        StatusHist status = new StatusHist();
        status.setStatus(abpOprt.getStatus());
        status.setOprtUser(abpOprt.getOprtUser());
        status.setOprtTime(abpOprt.getOprtTime());
        statusHist.add(status);
        List<StatusHist> histList = new ArrayList<>();
        //集合按时间从小到大排序
        statusHist.sort(Comparator.comparing(StatusHist::getOprtTime));
        for (StatusHist hist : statusHist) {
            TeIdNameCn histStatus = hist.getStatus();
            //遇到被打回得重置集合
            if (histStatus.getCid().equals(Constant.ABP_OPRT_BEATEN_BACK_ID)) {
                histList.clear();
            }
            histList.add(hist);
        }
        return histList;
    }


    private List<TeSysDef> queryProvByBuCode(String buCode) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysDef__defType.dot(DbFieldName.sysDef__defTypeCodeName), Constant.ABP_PROV_CODE_NAME));
        conds.add(new DC_E(DbFieldName.sysDef__srcDef.dot(DbFieldName.sysDef__srcDefCodeName), buCode));
        conds.add(new DC_E(DbFieldName.common_isValid, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.sysDef_cndtItems);
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_src);
        fieldNames.add(DbFieldName.sysDef__defType);
        return sysDefDao.findByFieldAndConds(conds, fieldNames);
    }

    private List<AbpOop> queryAbpOopData(ObjectId planVerId, ObjectId provId) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.AbpOop.planVer.dot(DbFieldName.common_cid), planVerId));
        conds.add(new DC_E(DbFieldName.AbpOop.prov.dot(DbFieldName.common_cid), provId));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.AbpOop.oopCode);
        fieldNames.add(DbFieldName.AbpOop.pls);
        fieldNames.add(DbFieldName.AbpOop.netSalesM);
        fieldNames.add(DbFieldName.AbpOop.type);
        return abpOopDao.findByFieldAndConds(conds, fieldNames);
    }


    private List<AbpOopBgt> queryAbpOopBgt(List<ObjectId> oopCids) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.common_isValid, true));
        conds.add(new DC_E(DbFieldName.AbpOopBgt.isActualized, false));
        conds.add(new DC_I<>(DbFieldName.AbpOopBgt.oop.dot(DbFieldName.common_cid), oopCids));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.AbpOopBgt.planVer);
        fieldNames.add(DbFieldName.AbpOopBgt.isActualized);
        fieldNames.add(DbFieldName.AbpOopBgt.oop);
        fieldNames.add(DbFieldName.AbpOopBgt.pl);
        fieldNames.add(DbFieldName.AbpOopBgt.rgInfo);
        fieldNames.add(DbFieldName.AbpOopBgt.ym);
        fieldNames.add(DbFieldName.AbpOopBgt.income);
        return abpOopBgtDao.findByFieldAndConds(conds, fieldNames);
    }


    @Override
    public void savePmProvRelationData(List<PmProvRelationImp> cachedDataList, TeSysUser user, StringBuilder errorMsg) {
        //1.查询bu信息
        String buCode = user.getSbuId();
        List<TeSysDef> buList = queryBuData(buCode);
        if (CollectionUtils.isEmpty(buList)) {
            throw new BaseException("导入失败!:当前人员的bu信息未查到！");
        }
        TeSysDef bu = buList.get(0);
        //2.查询用户有权限的省份
        List<TeSysDef> sysDefs = serviceManager.queryProvWithPermis(user);
        if (CollectionUtils.isEmpty(sysDefs)) {
            throw new BaseException("导入失败!:当前人员没有省份权限,请先授权！");
        }
        Map<String, List<TeSysDef>> provMap = sysDefs.stream().collect(Collectors.groupingBy(TeSysDef::getDefName));
        Map<ObjectId, String> idToProvNameMap = new HashMap<>();
        for (TeSysDef teSysDef : sysDefs) {
            idToProvNameMap.put(teSysDef.getId(), teSysDef.getDefName());
        }
        //3.到sysUser中查询项目经理信息
        List<String> ntList = cachedDataList.stream().map(PmProvRelationImp::getNt).collect(Collectors.toList());
        List<TeSysUser> sysUsers = querySysUser(ntList,null);
        if (CollectionUtils.isEmpty(sysUsers)){
            throw new BaseException("导入失败!nt账号对应的用户已不在职,请修改后导入!");
        }
        List<ObjectId> pmIds = sysUsers.stream().map(TeSysUser::getId).collect(Collectors.toList());
        Map<String, List<TeSysUser>> ntUserMap = sysUsers.stream().collect(Collectors.groupingBy(TeSysUser::getLoginName));
        //4.到表中查询项目经理对应的省份关系
        List<SysDefRoleUser> sysDefRoleUserList = sysDefRoleUserDao.queryUserProvRole(pmIds,buCode);
        Map<String, Map<ObjectId, List<SysDefRoleUser>>> ntSysDefRoleUserMap = sysDefRoleUserList.stream().collect(Collectors.groupingBy(s -> s.getRoleUser().getLoginName(), Collectors.groupingBy(SysDefRoleUser::getDefId)));
        //5.遍历excel数据
        List<SysDefRoleUser> insertList = new ArrayList<>();
        Map<String, String> ntNoMap = new HashMap<>();
        List<List<IDbCondition>> queryConditions=new ArrayList<>();
        List<List<UpdataData>> updateConditions=new ArrayList<>();
        for (PmProvRelationImp data : cachedDataList) {
            //（1）权限校验：当前导入人权限中的省份清单（如果有区域权限，则默认有区域下所有省份的权限）与EXCEL中的省份进行比较。仅支持导入用户有权限的省份，没有则报错：您没有**省份的权限，请修改后重新导入
            String provName = data.getProvName();
            List<TeSysDef> provList = provMap.get(provName);
            if (CollectionUtils.isEmpty(provList)) {
                errorMsg.append(String.format("导入失败!序号[%s]：您没有%s省份的权限，请修改后重新导入！", data.getNo(), provName));
                continue;
//                throw new BaseException(String.format("导入失败!序号[%s]：您没有%s省份的权限，请修改后重新导入！", data.getNo(), provName));
            }
            TeSysDef sysDef = provList.get(0);
            //EXCEL中的同一个项目经理也不能出现在多行记录中，否则则提示：**项目经理在EXCEL中
            String nt = data.getNt();
            String num = ntNoMap.get(nt);
            if (StringUtil.isNotNull(num)) {
                errorMsg.append(String.format("导入失败!序号[%s]与序号[%s]的项目经理NT账号重复，请更改后重新导入！", data.getNo(), num));
                continue;
//                throw new BaseException(String.format("导入失败!序号[%s]与序号[%s]的项目经理NT账号重复，请更改后重新导入！", data.getNo(), num));
            }
            ntNoMap.put(nt, data.getNo());
            //（2）项目经理NT账号校验：通过NT账号在sysUser表中是否存在，如果不存在则报错：**行的NT账号在系统中不存在，请更改后重新导入
            List<TeSysUser> users = ntUserMap.get(nt);
            if (CollectionUtils.isEmpty(users)) {
                errorMsg.append(String.format("导入失败!序号[%s]：NT账号在系统中不存在，请更改后重新导入！", data.getNo()));
                continue;
//                throw new BaseException(String.format("导入失败!序号[%s]：NT账号在系统中不存在，请更改后重新导入！", data.getNo()));
            }
            TeSysUser sysUser = users.get(0);
            TeUser teUser = new TeUser();
            teUser.setUserId(sysUser.getId());
            teUser.setUserName(sysUser.getUserName());
            teUser.setJobCode(sysUser.getJobCode());
            teUser.setLoginName(sysUser.getLoginName());

            Role role = new Role();
            role.setRoleId(Constant.PM_ID);
            role.setRoleName(Constant.PM_NAME);
            role.setRoleCodeName(Constant.PM_CODE_NAME);
            //该项目经理存在省份角色集合
            Map<ObjectId, List<SysDefRoleUser>> provId2SysDefRoleUserMap = ntSysDefRoleUserMap.get(nt);
            // 没有数据则直接新增
            if (MapUtils.isNotEmpty(provId2SysDefRoleUserMap)){
                //该项目经理存在当前省份的角色
                // 原先是查询看之前导入省份有无数据 但还是得先看其他省份有无role信息
                // 有 但role里没PMId时 更新数据
                for (ObjectId key : provId2SysDefRoleUserMap.keySet()) {
                    List<SysDefRoleUser> sysDefRoleUsers = provId2SysDefRoleUserMap.get(key);
                    for (SysDefRoleUser sysDefRoleUser : sysDefRoleUsers) {
                        List<ObjectId> collect = sysDefRoleUser.getRole().stream().map(defRole -> defRole.getRoleId()).collect(Collectors.toList());
                        if (collect.contains(Constant.PM_ID)) {
                            errorMsg.append(String.format("导入失败!序号[%s]：项目经理已维护在%s省份，不允许重复导入！", data.getNo(), idToProvNameMap.get(key)));
                            break;
                        }
                    }
                }
                if (StringUtil.isNotNull(errorMsg)) {
                    throw new BaseException(errorMsg.toString());
                }
                // 查询该项目经理是否有导入省份的数据 有则判断 无则更新
                List<SysDefRoleUser> sysDefRoleUsers = provId2SysDefRoleUserMap.get(sysDef.getId());
                if (!CollectionUtils.isEmpty(sysDefRoleUsers)){
                    SysDefRoleUser sysDefRoleUser = sysDefRoleUsers.get(0);
                    List<ObjectId> roleIds = sysDefRoleUser.getRole().stream().map(Role::getRoleId).collect(Collectors.toList());
                    //（3）人员校验：导入的人员与数据库的人员不能重复，即一个项目经理只能维护在一个省份下，否则报错：**项目经理已维护在**省份，不允许重复导入
                    // 这时是添加的省份已有数据 所以报该省份错误
                    if (roleIds.contains(Constant.PM_ID)){
                        errorMsg.append(String.format("导入失败!序号[%s]：项目经理已维护在%s省份，不允许重复导入！", data.getNo(), provName));
                        continue;
                    }
                    // 没有则在该数据里更新role属性
                    List<IDbCondition>query=new ArrayList<>();
                    query.add(new DC_E(DbFieldName.common__id,sysDefRoleUser.getId()));
                    queryConditions.add(query);
                    List<UpdataData>updata=new ArrayList<>();
                    updata.add(new UpdataData(DbFieldName.sysDefRoleUser__role,role,true));
                    updateConditions.add(updata);
                    continue;
                }
            }
            SysDefRoleUser sysDefRoleUser = new SysDefRoleUser();
            sysDefRoleUser.setDefId(sysDef.getId());
            sysDefRoleUser.setRoleUser(teUser);
            TeUser addUser = new TeUser();
            addUser.setUserId(user.getId());
            addUser.setUserName(user.getUserName());
            addUser.setJobCode(user.getJobCode());
            addUser.setLoginName(user.getLoginName());
            sysDefRoleUser.setAddUser(addUser);
            sysDefRoleUser.setAddTime(new Date());
            sysDefRoleUser.setValid(true);
            DefType defType = new DefType();
            defType.setDefTypeId(Constant.ABP_PROV_ID);
            defType.setDefTypeName(Constant.ABP_PROV_NAME);
            defType.setDefTypeCodeName(Constant.ABP_PROV_CODE_NAME);
            sysDefRoleUser.setDefType(defType);

            sysDefRoleUser.setRole(Collections.singletonList(role));
            SrcDef srcDef = new SrcDef();
            srcDef.setSrcDefId(bu.getId());
            srcDef.setSrcDefName(bu.getDefName());
            srcDef.setSrcDefCodeName(bu.getCodeName());
            sysDefRoleUser.setSrcDef(srcDef);
            insertList.add(sysDefRoleUser);
        }
        if (StringUtil.isNotNull(errorMsg)) {
            throw new BaseException(errorMsg.toString());
        }
        sysDefRoleUserDao.batchSave(insertList);
        if (!CollectionUtils.isEmpty(queryConditions)){
            sysDefRoleUserDao.batchUpdate(queryConditions,updateConditions,false);
        }
    }

    private List<TeSysUser> querySysUser(List<String> ntList,List<ObjectId>userIds) {
        if (CollectionUtils.isEmpty(ntList)&&CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        List<IDbCondition> conds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(ntList)){
            conds.add(new DC_I<>(DbFieldName.common_loginName, ntList));
        }else if (!CollectionUtils.isEmpty(userIds)){
            conds.add(new DC_I<>(DbFieldName.common__id, userIds));
        }
        conds.add(new DC_E(DbFieldName.common_isValid, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DbFieldName.common__id);
        fieldNames.add(DbFieldName.common_loginName);
        fieldNames.add(DbFieldName.common_userName);
        fieldNames.add(DbFieldName.common_jobCode);
        fieldNames.add(DbFieldName.sysUser_sbuId);
        fieldNames.add(DbFieldName.sysUser_sbuName);
        return sysUserDao.findByFieldAndConds(conds, fieldNames);
    }

    @Override
    public void updateProvProgress(TeSysUser user, PlanVersion planVersion) {
        // 该登录人下所有省份信息
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), SysDefTypeConstants.ABP_PROV_CODENAME));
        conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefCodeName), user.getSbuId()));
//        查一次 分开处理
//		conds.add(new DC_E(DFN.sysDef__isValid, true));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.sysDef_id);
        fieldNames.add(DFN.sysDef__defName);
        fieldNames.add(DFN.sysDef__isValid);
        List<TeSysDef> sysDefs = sysDefDao.findByFieldAndConds(conds, fieldNames);
        if (ObjectUtils.isEmpty(sysDefs)) {
            return;
        }

        List<TeSysDef> validSysDefs = sysDefs.stream().filter(sysDef -> {
            if (sysDef.getIsValid()) {
                return true;
            } return false;
        }).collect(Collectors.toList());

        if (ObjectUtils.isEmpty(validSysDefs)) {
            return;
        }

        List<ObjectId> delList = sysDefs.stream().filter(sysDef -> {
            if (!sysDef.getIsValid()) {
                return true;
            } return false;
        }).map(sysDef -> sysDef.getId()).collect(Collectors.toList());

        // 当有省份信息置为false时 先批量删除oprt表里对应数据
        if (!ObjectUtils.isEmpty(delList)) {
            List<IDbCondition> delCons = new ArrayList<>();
            delCons.add(new DC_I<>(DFN.AbpOprt.dept.dot(DFN.common_cid), delList));
            delCons.add(new DC_E(DFN.AbpOprt.planVer.dot(DFN.common_cid), planVersion.getCid()));
            abpOprtDao.deleteByConds(delCons);
        }

        // 数据库里省份的进度信息 先删再查
        conds.clear();
        fieldNames.clear();
        conds.add(new DC_E(DbFieldName.AbpOprt.planVer.dot(DbFieldName.common_cid), planVersion.getCid()));
        fieldNames.add(DbFieldName.AbpOprt.dept);
        fieldNames.add(DbFieldName.AbpOprt.planVer);
        List<AbpOprt> abpOprts = abpOprtDao.findByFieldAndConds(conds, fieldNames);

        if (ObjectUtils.isEmpty(abpOprts)) {
            return;
        }

        // 开始补全信息 oprt是少的部分
//        List<ObjectId> abpOprtList = abpOprts.stream().map(abpOprt -> abpOprt.getDept().getCid()).collect(Collectors.toList());
        List<ObjectId> abpOprtList = new ArrayList<>();

        // 新加逻辑 存放省份id和名称
        Map<ObjectId, String> abpOprtNameMap = new HashMap<>();
        for (AbpOprt abpOprt : abpOprts) {
            abpOprtList.add(abpOprt.getDept().getCid());
            abpOprtNameMap.put(abpOprt.getDept().getCid(), abpOprt.getDept().getName());
        }

        Map<String, ObjectId> resMap = new HashMap<>();

        // 新添功能 当省份表和进度表里id一致但名称不一致时进行以省份表为准批量更新
        Map<String, ObjectId> updateMap = new HashMap<>();

        for (TeSysDef sysDef : validSysDefs) {
            if (!abpOprtList.contains(sysDef.getId())) {
                resMap.put(sysDef.getDefName(), sysDef.getId());
            } else {
                if (!abpOprtNameMap.get(sysDef.getId()).equals(sysDef.getDefName())) {
                    updateMap.put(sysDef.getDefName(), sysDef.getId());
                }
            }
        }

        if (!ObjectUtils.isEmpty(resMap)) {
            List<AbpOprt> resList = new ArrayList();
            resMap.forEach((k, v) -> {
                // 插入信息
                AbpOprt abpOprt = new AbpOprt();
                abpOprt.setPlanVer(planVersion);

                TeIdNameCn dept = new TeIdNameCn();
                dept.setName(k);
                dept.setCodeName(Constant.ABP_PROV_CODE_NAME);
                dept.setCid(v);
                abpOprt.setDept(dept);

                TeIdNameCn oprt = new TeIdNameCn();
                oprt.setCid(Constant.ABP_OPRT_SUBMIT_ID);
                oprt.setName("初始化");
                // sysMget中没有对应的codeName
                oprt.setCodeName(null);
                abpOprt.setOprt(oprt);

                TeIdNameCn status = new TeIdNameCn();
                status.setCid(Constant.ABP_OPRT_PLANNING_IN_PROGRESS_ID);
                status.setName("计划编制中");
                abpOprt.setStatus(status);

                abpOprt.setDesc("发起版本");
                TeUser teUser = getTeUser(user);
                abpOprt.setOprtUser(teUser);
                abpOprt.setOprtTime(new Date());
                resList.add(abpOprt);
            });
            abpOprtDao.batchSave(resList);
        }

        // 批量更新 将进度表里的名称改为和省份表里一致
        if (!ObjectUtils.isEmpty(updateMap)) {
            List<BatchCondsUpsert> condAndUpdate = new ArrayList<>();

            updateMap.forEach((defName, id) -> {
                //批量更新查询条件
                List<IDbCondition> conditions = new ArrayList<>();
                //批量更新更新条件
                List<UpdataData> updateDataList = new ArrayList<>();
                conditions.add(new DC_E(DFN.AbpOprt.dept.dot(DFN.common_cid), id));
                conditions.add(new DC_E(DFN.AbpOprt.planVer.dot(DFN.common_cid), planVersion.getCid()));
                updateDataList.add(new UpdataData(DFN.AbpOprt.dept.dot(DFN.common_name), defName));
                condAndUpdate.add((new BatchCondsUpsert(conditions, updateDataList)));
            });
            abpOprtDao.batchUpdate(condAndUpdate);
        }
    }

    @Override
    public List<SysVerMgt> listPlanVers(String buCode, String year, Boolean isLocked, List<ObjectId> planVerIds) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DbFieldName.sysVerMgt__verMgtType.dot(DbFieldName.common_cid), Constant.ABP_BUSINESS_PLAN_VERSION_ID));
        if (StringUtil.isNotNull(buCode)) {
            conds.add(new DC_E(DbFieldName.sysVerMgt__srcDef.dot(DbFieldName.common_cn), buCode));
        }
        if (StringUtil.isNotNull(year)) {
            conds.add(new DC_E(DbFieldName.sysVerMgt__year, year));
        }
        if (isLocked != null) {
            conds.add(new DC_E(DbFieldName.sysVerMgt__isLocked, isLocked));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(planVerIds)) {
            conds.add(new DC_I<>(DbFieldName.common__id, planVerIds));
        }
        Sort sort = Sort.by(Sort.Direction.DESC, DFN.sysVerMgt__addTime.n());
        return sysVerMgtDao.findByFieldAndConds(conds, null, sort);
    }

    @Override
    public SysVerMgt getPlanVer(ObjectId planVerId) {
        List<SysVerMgt> sysVerMgts = listPlanVers(null, null, null, Arrays.asList(planVerId));
        return CollectionUtils.isEmpty(sysVerMgts) ? null : sysVerMgts.get(0);
    }

    @Override
    public boolean lockPlanVer(TeSysUser loginUser, ObjectId planVerId, Boolean isLocked) {
        // 权限校验
        serviceManager.queryBuAdminByUser(loginUser);
        // 更新版本锁定标识
        List<IDbCondition> conditions = new ArrayList<>();
        conditions.add(new DC_E(DbFieldName.common__id, planVerId));
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DbFieldName.common_isLocked, isLocked));
        int count = sysVerMgtDao.updateByConds(conditions, updates);
        return count > 0;
    }

    @Override
    public void calcBuHcFeeAndAreaHcFee(String buCode) {
        SysDef buDef = sysDefService.getSysDefByCodeName(buCode, SysDefTypeCodeName.AI_BU);
        Assert.notNull(buDef, "buCode定义不存在");
        SysVerMgt planVer = oopService.queryLatestPlanVerOfCurrentYear(buDef.getId());
        Assert.notNull(planVer, "未获取当前年份经营计划版本，buCode：" + buCode);
//        feeCalculationService.calcBuHcFeeAndAreaHcFee(planVer.getId(), planVer.getLinkedVer().getCid());
        feeCalculationService.calculateSharedCosts(planVer.getId(), null);
    }

    @Override
	public void calcBuHcFeeAndAreaHcFee(String buCode, String ym, ObjectId planVerId) {
		SysDef buDef = sysDefService.getSysDefByCodeName(buCode, SysDefTypeCodeName.AI_BU);
		Assert.notNull(buDef, "buCode定义不存在");
//		SysVerMgt planVer = oopService.queryLatestPlanVerOfCurrentYear(buDef.getId());
		SysVerMgt planVer = sysVerMgtDao.findById(planVerId);
		Assert.notNull(planVer, "未获取当前年份经营计划版本，buCode：" + buCode);
//		feeCalculationService.calcBuHcFeeAndAreaHcFee(planVer.getId(), planVer.getLinkedVer().getCid(), ym);
		feeCalculationService.calculateSharedCosts(planVer.getId(), null);
	}

    @Override
    public LinkedHashMap<String,String> querySyncDataMsg(ObjectId provId, String buCode) {
//        List<PlanVersion> planVersions = serviceManager.findPlanVerId(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataMaxVersion(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID, false);
        if (CollectionUtils.isEmpty(sysVerMgts)){
            throw BusinessException.initExc(String.format("该bu【%s】对应的版本不存在，请联系管理员",buCode));
        }
        ObjectId planVerId = sysVerMgts.get(0).getId();
        LinkedHashMap<String,String> map = new LinkedHashMap<>();
        if (provId==null){
            //省份信息为空，查询该bu下的所有同步数据的最新时间
            List<TeSysDef> sysDefs = iSysDefDao.queryBu(buCode);
            if (CollectionUtils.isEmpty(sysDefs)){
                throw BusinessException.initExc(String.format("该bu【%s】信息查不到，请联系管理员",buCode));
            }
            TeSysDef sysDef = sysDefs.get(0);
            List<AbpOprt>abpOprts=abpOprtDao.queryDataByProvAndPlanVerAndOprt(sysDef.getId(),planVerId,Constant.ABP_OPRT_INTERFACE_SYNCHRONIZATION_ID);
            if (CollectionUtils.isEmpty(abpOprts)){
                throw BusinessException.initExc(String.format("该bu【%s】同步数据的操作信息未查到，请联系管理员",buCode));
            }
            //该bu的操作记录
            AbpOprt abpOprt = abpOprts.get(0);
            TeIdNameCn dept = abpOprt.getDept();
            map.put(dept.getName(),DateUtil.formatDate2Str(abpOprt.getOprtTime(),DateUtil.DATETIME_FORMAT));
            //查询bu下的省份信息
            List<TeSysDef>sysDefList=iSysDefDao.queryDataByBuCodeAndDefTypeCodeName(buCode,Constant.ABP_PROV_CODE_NAME);
            if (CollectionUtils.isEmpty(sysDefList)){
                log.warn(String.format("该bu【%s】下的省份信息未查到",buCode));
            }else {
                List<ObjectId> provIds = sysDefList.stream().map(TeSysDef::getId).collect(Collectors.toList());
                //查询该bu下的省份最新操作信息
                List<AbpOprt> abpOprtList=abpOprtDao.queryDataByProvIds(provIds,planVerId,Constant.ABP_OPRT_INTERFACE_SYNCHRONIZATION_ID);
                if (CollectionUtils.isEmpty(abpOprtList)){
                    log.warn(String.format("该bu【%s】下的省份同步信息未查到",buCode));
                }else {
                    //省份同步时间按照省份顺序排列
                    Map<ObjectId, AbpOprt> oprtMap = new HashMap<>();
                    for (AbpOprt oprt : abpOprtList) {
                        TeIdNameCn prov = oprt.getDept();
                        if (prov!=null){
                            oprtMap.put(prov.getCid(),oprt);
                        }
                    }
                    for (TeSysDef def : sysDefList) {
                        AbpOprt oprt = oprtMap.get(def.getId());
                        if (oprt!=null){
                            TeIdNameCn prov = oprt.getDept();
                            map.put(prov.getName(),DateUtil.formatDate2Str(oprt.getOprtTime(),DateUtil.DATETIME_FORMAT));
                        }
                    }

                }
            }
        }else {
            List<AbpOprt>abpOprts=abpOprtDao.queryDataByProvAndPlanVerAndOprt(provId,planVerId,Constant.ABP_OPRT_INTERFACE_SYNCHRONIZATION_ID);
            if (CollectionUtils.isEmpty(abpOprts)){
                return map;
            }
            AbpOprt abpOprt = abpOprts.get(0);
            TeIdNameCn dept = abpOprt.getDept();
            if (dept!=null){
                map.put(dept.getName(),DateUtil.formatDate2Str(abpOprt.getOprtTime(),DateUtil.DATETIME_FORMAT));
            }
        }
        return map;
    }

    @Override
    public void winRateSet(TeSysUser user, String winRate) {
        //校验权限：只有bu管理员才能设置
        serviceManager.queryBuAdminByUser(user);
        //查询该bu下是否已存在赢率
        List<TeSysDef> sysDefList = iSysDefDao.queryDataByDefTypeIdAndSrcDefCodeName(Constant.ABP_ORDER_WIN_RATE_ID, user.getSbuId());
        if (CollectionUtils.isEmpty(sysDefList)){
            TeSysDef sysDef = new TeSysDef();
            sysDef.setIsValid(true);
            sysDef.setIsStd(false);
            TeDefType teDefType = new TeDefType();
            teDefType.setDefTypeId(Constant.ABP_ORDER_WIN_RATE_ID);
            teDefType.setDefTypeName(Constant.ABP_ORDER_WIN_RATE_NAME);
            teDefType.setDefTypeCodeName(Constant.ABP_ORDER_WIN_RATE_CODE_NAME);
            sysDef.setDefType(teDefType);
            List<TeSysDef> sysDefs = iSysDefDao.queryBu(user.getSbuId());
            if (CollectionUtils.isEmpty(sysDefs)){
                throw BusinessException.initExc("用户bu信息未查到，请联系管理员！");
            }
            TeSysDef buMsg = sysDefs.get(0);
            TeSrcDef teSrcDef = new TeSrcDef();
            teSrcDef.setSrcDefId(buMsg.getId());
            teSrcDef.setSrcDefName(buMsg.getDefName());
            teSrcDef.setSrcDefCodeName(buMsg.getCodeName());
            sysDef.setSrcDef(teSrcDef);
            sysDef.setDefName("接口赢率");
            sysDef.setValue(winRate);
            TeUser addUser = new TeUser();
            addUser.setUserId(user.getId());
            addUser.setUserName(user.getUserName());
            addUser.setLoginName(user.getLoginName());
            addUser.setJobCode(user.getJobCode());
            sysDef.setAddUser(addUser);
            sysDef.setAddTime(new Date());
            iSysDefDao.insert(sysDef);
        }else {
            //更新赢率
            TeSysDef sysDef = sysDefList.get(0);
            if (Objects.equals(sysDef.getValue(),winRate)){
                return;
            }
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DbFieldName.sysDef__value,winRate));
            iSysDefDao.updateById(sysDef.getId(),updates);
        }

    }

    @Override
    public TeSysDef winRateQuery(String buCode) {
        List<TeSysDef> sysDefList = iSysDefDao.queryDataByDefTypeIdAndSrcDefCodeName(Constant.ABP_ORDER_WIN_RATE_ID, buCode);
        if (CollectionUtils.isEmpty(sysDefList)){
            return null;
        }
        return sysDefList.get(0);
    }

    @Override
    public List<PlanVersion> queryNextYearVer(String buCode) {
        return serviceManager.findPlanVerIdForNextYear(buCode, Constant.ABP_BUSINESS_PLAN_VERSION_ID);
    }

    @Override
    public List<PlanVersion> findVerByYear(String buCode, String year) {
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.findVerByYear(buCode, year);
        if (CollectionUtils.isEmpty(sysVerMgts)) {
            return new ArrayList<>();
        }
        List<PlanVersion> list = new ArrayList<>();
        for (SysVerMgt sysVerMgt : sysVerMgts) {
            PlanVersion version = new PlanVersion();
            version.setCid(sysVerMgt.getId());
            version.setVerNo(String.valueOf(sysVerMgt.getVerNo()));
            version.setName(sysVerMgt.getVerName());
            list.add(version);
        }
        return list;
    }

    @Override
    public List<PlanVersion> findVersion(String buCode) {
        List<SysVerMgt> sysVerMgts = sysVerMgtDao.queryDataMaxVersion(buCode,Constant.ABP_BUSINESS_PLAN_VERSION_ID,null);
        if (CollectionUtils.isEmpty(sysVerMgts)){
            return new ArrayList<>();
        }
        List<PlanVersion> list = new ArrayList<>();
        for (SysVerMgt sysVerMgt : sysVerMgts) {
            PlanVersion version = new PlanVersion();
            version.setCid(sysVerMgt.getId());
            version.setVerNo(String.valueOf(sysVerMgt.getVerNo()));
            version.setName(sysVerMgt.getVerName());
            list.add(version);
        }
        return list;
    }
}
