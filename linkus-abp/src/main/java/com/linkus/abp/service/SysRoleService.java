package com.linkus.abp.service;

import com.linkus.abp.model.vo.OrgTreeVO;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * 角色配置业务层接口
 *
 * <AUTHOR>
 * @date 2022-05-20
 */
public interface SysRoleService {

    /**
     * 是否是BU管理员
     *
     * @param loginUser
     * @return
     */
    boolean isBuAdmin(TeSysUser loginUser);

    /**
     * 查询组织树
     *
     * @param loginUser
     * @return
     */
    List<OrgTreeVO> queryOrgTree(TeSysUser loginUser);

    /**
     * 根据defTypeCodeName和srcDefCodeName查询SysDef
     *
     * @param defTypeCodeName
     * @param loginUser
     * @param authority       权限逻辑
     * @return
     */
    List<TeSysDef> querySysDefs(String defTypeCodeName, TeSysUser loginUser, boolean authority);

    /**
     * 导入角色人员
     *
     * @param data
     * @param loginUser
     */
    void importRoleUser(List<List<Object>> data, TeSysUser loginUser);

    /**
     * 导出角色人员
     *
     * @param loginUser
     * @param response
     */
    void exportRoleUser(TeSysUser loginUser, HttpServletResponse response);

    /**
     * 新增角色人员
     *
     * @param roleType  (admin:组织管理员（大区、区域、省份）、产品线管理员、资源部门管理员；editor:计划编制人（省份）)
     * @param roleId
     * @param defId
     * @param userId
     * @param loginUser
     */
    void addRoleUser(String roleType, ObjectId roleId, ObjectId defId, ObjectId userId, TeSysUser loginUser);

    /**
     * 删除角色人员
     *
     * @param roleType         (admin:组织管理员（大区、区域、省份）、产品线管理员、资源部门管理员；editor:计划编制人（省份）)
     * @param sysDefRoleUserId
     * @param roleId
     * @param defId
     * @param userId
     * @param loginUser
     */
    void deleteRoleUser(String roleType, ObjectId sysDefRoleUserId, ObjectId roleId, ObjectId defId, ObjectId userId, TeSysUser loginUser);

    /**
     * 根据用户名称、NT账号模糊查询组织管理员角色人员
     *
     * @param roleId
     * @param defId
     * @param queryKey
     * @param loginUser
     * @return
     */
    @Deprecated
    List<Map<String, Object>> queryOrgAdminRoleUser(ObjectId roleId, ObjectId defId, String queryKey, TeSysUser loginUser);

    /**
     * 根据角色、定义和用户名/NT账号模糊查询角色人员
     *
     * @param roleId
     * @param defId
     * @param queryKey
     * @return
     */
    List<Map<String, Object>> queryRoleUser(ObjectId roleId, ObjectId defId, String queryKey);

    /**
     * 根据bu编码获取bu定义
     *
     * @param buCode
     * @return
     */
    TeSysDef getSysDefByBuCode(String buCode);

    /**
     * 新增和删除时校验角色权限
     *
     * @param roleType  (admin:组织管理员（大区、区域、省份）、产品线管理员、资源部门管理员；editor:计划编制人（省份）)
     * @param roleId
     * @param defId
     * @param loginUser
     * @return
     */
    boolean checkRole(String roleType, ObjectId roleId, ObjectId defId, TeSysUser loginUser);

    /**
     * 是否是bu规范管理员
     * @param loginUser
     * @return
     */
    boolean isBuStandAdmin(TeSysUser loginUser);
}
