package com.linkus.devmgt.service.impl;

import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.util.FastDFSClient;
import com.linkus.base.util.StringUtil;
import com.linkus.devmgt.service.IBizDetailInfoReformService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Decoder;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Service
public class BizDetailInfoReformServiceImpl implements IBizDetailInfoReformService {

    @Autowired
    private FastDFSClient fastDFSClient;

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 匹配 src="data:image?"
     * 提取 问好 部分
     */
    private final static Pattern imgTag = Pattern.compile("(?<= src\\=\\\"data:image).*?(?=\\\")");

    /**
     * 匹配 src="data:application?"
     * 提取 问好 部分
     */
    private final static Pattern applicationTag = Pattern.compile("(?<= src\\=\\\"data:application).*?(?=\\\")");

    /**
     * 匹配 /?;base64,
     * 提取 问好 部分
     */
    private final static Pattern base64Tag = Pattern.compile("(?<=/).*?(?=;base64,)");

    @Override
    public void bizDetailInfoReform() {
        org.bson.Document query = new org.bson.Document()
                .append(DFN.biz_detailInfo.n(), new org.bson.Document("$regex", ";base64,"));
        org.bson.Document projection = new org.bson.Document()
                .append(DFN.common__id.n(), DFN.common__id.$n())
                .append(DFN.biz_detailInfo.n(), DFN.biz_detailInfo.$n());
        com.mongodb.client.MongoCursor<org.bson.Document> dbCursor = mongoTemplate.getCollection(DBT.BIZ.n()).find(query).projection(projection).noCursorTimeout(true).cursor();
        while (dbCursor.hasNext()) {
            org.bson.Document next = dbCursor.next();
            String id = StringUtil.getNotNullStr(next.get(DFN.common__id.n()));
            String detailInfo = StringUtil.getNotNullStr(next.get(DFN.biz_detailInfo.n()));
            if (StringUtil.isNull(id) || StringUtil.isNull(detailInfo)) {
                continue;
            }

            Map<String, Object> upset = reformDetailInfo(detailInfo);

            if (StringUtil.toBoolean(upset.get("reform"), false)) {
                Query query1 = new Query();
                query1.addCriteria(Criteria.where(DFN.common__id.n()).is(id));
                Update update = new Update();
                update.set(DFN.biz_detailInfo.n(), StringUtil.getNotNullStr(upset.get("detailInfo")));
                mongoTemplate.updateFirst(query1, update, DBT.BIZ.n());
            }
        }
    }

    private Map<String, Object> reformDetailInfo(String detailInfo) {
        Map<String, Object> result = new HashMap<>(2);
        result.put("reform", false);

        boolean isApplication = false;
        // image带图片后缀
        List<String> base64Strs = regexImg(detailInfo);
        if (CollectionUtils.isEmpty(base64Strs)) {
            // application流没有图片后缀，默认成jpg
            base64Strs = regexApplication(detailInfo);
            if (CollectionUtils.isEmpty(base64Strs)) {
                return result;
            }
            isApplication = true;
        }

        for (String base64Str : base64Strs) {
            if (StringUtil.isNull(base64Str)) {
                continue;
            }
            String suffix = regexBase64(base64Str);
            if (suffix == null) {
                continue;
            }
            byte[] imgByte = generateImage(base64Str.replace("/" + suffix + ";base64,", ""));
            if (Objects.isNull(imgByte)) {
                continue;
            }
            String imgUri = fastDFSClient.uploadFile(
                    imgByte,
                    new ObjectId().toHexString() + "." + (isApplication || StringUtil.isNull(suffix) ? "jpg" : suffix),
                    null
            );
            if (StringUtil.isNull(imgUri)) {
                continue;
            }

            detailInfo = detailInfo.replace((isApplication ? "data:application" : "data:image") + base64Str, "/" + imgUri);
            result.put("reform", true);
            result.put("detailInfo", detailInfo);
        }
        return result;
    }

    public static List<String> regexImg(String img) {
        if (StringUtil.isNull(img)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        Matcher imgMatcher = imgTag.matcher(img);
        while (imgMatcher.find()) {
            result.add(imgMatcher.group());
        }
        return result;
    }

    public static List<String> regexApplication(String img) {
        if (StringUtil.isNull(img)) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        Matcher imgMatcher = applicationTag.matcher(img);
        while (imgMatcher.find()) {
            result.add(imgMatcher.group());
        }
        return result;
    }

    public static String regexBase64(String base64) {
        if (StringUtil.isNull(base64)) {
            return null;
        }
        Matcher base64Matcher = base64Tag.matcher(base64);
        if (base64Matcher.find()) {
            return base64Matcher.group();
        }
        return null;
    }

    public static byte[] generateImage(String base64Img) {
        if (StringUtil.isNull(base64Img)) {
            return null;
        }
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] b = decoder.decodeBuffer(base64Img);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;
                }
            }
            return b;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
