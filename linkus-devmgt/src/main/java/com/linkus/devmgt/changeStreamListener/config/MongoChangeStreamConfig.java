package com.linkus.devmgt.changeStreamListener.config;

import com.linkus.base.db.base.table.DBT;
import com.linkus.cmpt.flow.model.TeFlowTask;
import com.linkus.devmgt.changeStreamListener.AbpOopInfoMessageListener;
import com.linkus.devmgt.changeStreamListener.PrjInfoMessageListener;
import com.linkus.devmgt.changeStreamListener.ScrumTaskMessageListener;
import com.linkus.devmgt.changeStreamListener.SysToDoFlowTaskMessageListener;
import com.linkus.devmgt.changeStreamListener.dao.ChangeStreamTokenDao;
import com.linkus.devmgt.changeStreamListener.model.constant.ChangeStreamConstant;
import com.linkus.devmgt.model.TeAbpOop;
import com.linkus.devmgt.pojo.TePrjInfo;
import com.mongodb.client.model.changestream.FullDocument;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.recipes.leader.LeaderLatch;
import org.apache.curator.framework.recipes.leader.LeaderLatchListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.messaging.ChangeStreamRequest;
import org.springframework.data.mongodb.core.messaging.DefaultMessageListenerContainer;
import org.springframework.data.mongodb.core.messaging.MessageListener;
import org.springframework.data.mongodb.core.messaging.MessageListenerContainer;
import org.springframework.data.mongodb.core.messaging.Subscription;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.match;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.newAggregation;
import static org.springframework.data.mongodb.core.aggregation.Aggregation.project;
import static org.springframework.data.mongodb.core.query.Criteria.where;

/**
 * @Author: feizj
 * @Description: 配置changeStream 监听器
 * @Date: 2023/3/27 9:39
 */
@Configuration
@Slf4j
public class MongoChangeStreamConfig {

    @Resource(name = "ChangeStreamTokenDaoImpl")
    private ChangeStreamTokenDao changeStreamTokenDao;

    private volatile Subscription flowTaskSubscription;
    private volatile Subscription scrumTaskSubscription;
    private volatile Subscription abpOopInfoSubscription;
    private volatile Subscription prjInfoInfoSubscription;

    private final static String CHANGE_STREAM_KEY = "/change_stream_leader_to_do";

    @Bean
    MessageListenerContainer flowTaskMessageListenerContainer(@Qualifier("mongoTemplate") MongoTemplate template) {
        Executor executor = new ThreadPoolExecutor(1, 1,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
        MessageListenerContainer messageListenerContainer = new DefaultMessageListenerContainer(template, executor) {
            @Override
            public boolean isAutoStartup() {
                return true;
            }
        };
        return messageListenerContainer;
    }

    @Bean
    MessageListenerContainer scrumTaskMessageListenerContainer(@Qualifier("mongoTemplate") MongoTemplate template) {
        Executor executor = new ThreadPoolExecutor(1, 1,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
        MessageListenerContainer messageListenerContainer = new DefaultMessageListenerContainer(template, executor) {
            @Override
            public boolean isAutoStartup() {
                return true;
            }
        };
        return messageListenerContainer;
    }

    @Bean
    MessageListenerContainer abpOopMessageListenerContainer(@Qualifier("mongoTemplate") MongoTemplate template) {
        Executor executor = new ThreadPoolExecutor(1, 1,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
        MessageListenerContainer messageListenerContainer = new DefaultMessageListenerContainer(template, executor) {
            @Override
            public boolean isAutoStartup() {
                return true;
            }
        };
        return messageListenerContainer;
    }

    @Bean
    MessageListenerContainer prjInfoMessageListenerContainer(@Qualifier("mongoTemplate") MongoTemplate template) {
        Executor executor = new ThreadPoolExecutor(1, 1,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());
        MessageListenerContainer messageListenerContainer = new DefaultMessageListenerContainer(template, executor) {
            @Override
            public boolean isAutoStartup() {
                return true;
            }
        };
        return messageListenerContainer;
    }

    @Bean(initMethod = "start", destroyMethod = "close")
    LeaderLatch changeStreamLeaderSelectorClient(CuratorFramework curatorFramework,
                                                 @Qualifier("flowTaskMessageListenerContainer") MessageListenerContainer flowTaskMessageListenerContainer,
                                                 @Qualifier("scrumTaskMessageListenerContainer") MessageListenerContainer scrumTaskMessageListenerContainer,
                                                 @Qualifier("abpOopMessageListenerContainer") MessageListenerContainer abpOopMessageListenerContainer,
                                                 @Qualifier("prjInfoMessageListenerContainer") MessageListenerContainer prjInfoMessageListenerContainer,
                                                 SysToDoFlowTaskMessageListener flowTaskMessageListener,
                                                 ScrumTaskMessageListener scrumTaskMessageListener,
                                                 AbpOopInfoMessageListener abpOopInfoMessageListener,
                                                 PrjInfoMessageListener prjInfoMessageListener) {
        LeaderLatch leaderLatch = new LeaderLatch(curatorFramework, CHANGE_STREAM_KEY);
        leaderLatch.addListener(new LeaderLatchListener() {
            @Override
            public void isLeader() {
                synchronized (this) {
                    if (leaderLatch.hasLeadership()) {
                        try {
                            Map<String, Integer> tb2ChangeTime = changeStreamTokenDao.getLastChangeTimeByTbs(Arrays.asList(DBT.FLOW_TASK.n(),
                                    ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_SCRUM_TASK,
                                    ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_ABP_OOP_INFO,
                                    ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_PRJ_INFO));
                            if (flowTaskSubscription == null) {
                                // 注册flowTask的监听
                                ChangeStreamRequest.ChangeStreamRequestBuilder flowTaskBuilder = ChangeStreamRequest.builder(flowTaskMessageListener);
                                if (tb2ChangeTime.get(DBT.FLOW_TASK.n()) != null) {
                                    flowTaskBuilder.resumeAt(Instant.ofEpochSecond(tb2ChangeTime.get(DBT.FLOW_TASK.n())));
                                }
                                ChangeStreamRequest flowTaskRequest = flowTaskBuilder
                                        .collection(DBT.FLOW_TASK.n())
                                        .filter(newAggregation(
                                                match(where("operationType").in("insert", "delete", "update", "replace")),
                                                project().andExclude(
                                                        "raw.updateDescription.updatedFields.scripts",
                                                        "raw.updateDescription.updatedFields.codeFiles",
                                                        "raw.fullDocument.scripts",
                                                        "raw.fullDocument.codeFiles"
                                                )
                                        ))
                                        // 不设置时，文档更新时，只会发送变更字段的信息，设置UPDATE_LOOKUP会返回文档的全部信息
                                        .fullDocumentLookup(FullDocument.UPDATE_LOOKUP)
                                        .build();
                                flowTaskSubscription = flowTaskMessageListenerContainer.register(flowTaskRequest, TeFlowTask.class);
                                log.info("启动flowTask的changeStream监听器");
                            }

                            if (scrumTaskSubscription == null) {
                                // 注册scrumTask的监听
                                ChangeStreamRequest.ChangeStreamRequestBuilder flowTaskBuilder = ChangeStreamRequest.builder(scrumTaskMessageListener);
                                if (tb2ChangeTime.get(ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_SCRUM_TASK) != null) {
                                    flowTaskBuilder.resumeAt(Instant.ofEpochSecond(tb2ChangeTime.get(ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_SCRUM_TASK)));
                                }
                                ChangeStreamRequest scrumTaskRequest = flowTaskBuilder
                                        .collection(DBT.FLOW_TASK.n())
                                        .filter(newAggregation(
                                                match(where("operationType").in("insert", "delete", "update", "replace")),
                                                project().andExclude(
                                                        "raw.updateDescription.updatedFields.scripts",
                                                        "raw.updateDescription.updatedFields.codeFiles",
                                                        "raw.fullDocument.scripts",
                                                        "raw.fullDocument.codeFiles"
                                                )
                                        ))
                                        // 不设置时，文档更新时，只会发送变更字段的信息，设置UPDATE_LOOKUP会返回文档的全部信息
                                        .fullDocumentLookup(FullDocument.UPDATE_LOOKUP)
                                        .build();
                                scrumTaskSubscription = scrumTaskMessageListenerContainer.register(scrumTaskRequest, TeFlowTask.class);
                                log.info("启动scrumTask的changeStream监听器");
                            }

                            if (abpOopInfoSubscription == null) {
                                // 注册scrumTask的监听
                                ChangeStreamRequest.ChangeStreamRequestBuilder abpOopBuilder = ChangeStreamRequest.builder(abpOopInfoMessageListener);
                                if (tb2ChangeTime.get(ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_ABP_OOP_INFO) != null) {
                                    abpOopBuilder.resumeAt(Instant.ofEpochSecond(tb2ChangeTime.get(ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_ABP_OOP_INFO)));
                                }
                                ChangeStreamRequest abpOopInfoRequest = abpOopBuilder
                                        .collection(DBT.ABP_OOP.n())
                                        .filter(newAggregation(
                                                match(where("operationType").in("insert", "delete", "update", "replace")),
                                                project().andExclude(
                                                        "raw.updateDescription.updatedFields.oprtInfo",
                                                        "raw.fullDocument.oprtInfo"
                                                )
                                        ))
                                        // 不设置时，文档更新时，只会发送变更字段的信息，设置UPDATE_LOOKUP会返回文档的全部信息
                                        .fullDocumentLookup(FullDocument.UPDATE_LOOKUP)
                                        .build();
                                abpOopInfoSubscription = abpOopMessageListenerContainer.register(abpOopInfoRequest, TeAbpOop.class);
                                log.info("启动abpOopInfo的changeStream监听器");
                            }

                            if (prjInfoInfoSubscription == null) {
                                prjInfoInfoSubscription = addListener(
                                        DBT.PRJINFO.n(),
                                        TePrjInfo.class,
                                        prjInfoMessageListener,
                                        prjInfoMessageListenerContainer,
                                        tb2ChangeTime.get(ChangeStreamConstant.CHANGE_STREAM_DOCUMENT_TYPE_PRJ_INFO),
                                        null,
                                        Arrays.asList("raw.updateDescription.updatedFields.budgetParaValues",
                                                "raw.fullDocument.budgetParaValues")
                                );
                                log.info("启动prjInfo的changeStream监听器");
                            }

                            log.info("成为changeStreamLeader");
                        } catch (Exception e) {
                            log.error("启动changeStream监听失败：{}", e);
                        }
                    }
                }
            }

            @Override
            public void notLeader() {
                synchronized (this) {
                    if (!leaderLatch.hasLeadership()) {
                        log.info("退出changeStreamLeader");
                        if (flowTaskSubscription != null) {
                            flowTaskMessageListenerContainer.remove(flowTaskSubscription);
                            flowTaskSubscription = null;
                            log.info("关闭flowTask监听器");
                        }
                        if (scrumTaskSubscription != null) {
                            scrumTaskMessageListenerContainer.remove(scrumTaskSubscription);
                            scrumTaskSubscription = null;
                            log.info("关闭scrumTask监听器");
                        }
                        if (abpOopInfoSubscription != null) {
                            abpOopMessageListenerContainer.remove(abpOopInfoSubscription);
                            abpOopInfoSubscription = null;
                            log.info("关闭abpOopInfo监听器");
                        }
                        if (prjInfoInfoSubscription != null) {
                            prjInfoMessageListenerContainer.remove(prjInfoInfoSubscription);
                            prjInfoInfoSubscription = null;
                            log.info("关闭prjInfo监听器");
                        }
                    }
                }
            }
        });
        return leaderLatch;
    }

    private Subscription addListener(String collectionName, Class pojo, MessageListener messageListener,
                                     MessageListenerContainer messageListenerContainer, Integer changeTime,
                                     List<String> includeFields, List<String> excludeFields) {
        // 注册监听
        ChangeStreamRequest.ChangeStreamRequestBuilder changeStreamBuilder = ChangeStreamRequest.builder(messageListener);
        if (changeTime != null) {
            changeStreamBuilder.resumeAt(Instant.ofEpochSecond(changeTime));
        }
        List<AggregationOperation> sqlList = new ArrayList<>();
        sqlList.add(match(where("operationType").in("insert", "delete", "update", "replace")));
        if (CollectionUtils.isNotEmpty(includeFields) && CollectionUtils.isNotEmpty(excludeFields)) {
            sqlList.add(project().andInclude(includeFields.toArray(new String[0])).andExclude(excludeFields.toArray(new String[0])));
        } else if (CollectionUtils.isNotEmpty(includeFields)) {
            sqlList.add(project().andInclude(includeFields.toArray(new String[0])));
        } else if (CollectionUtils.isNotEmpty(excludeFields)) {
            sqlList.add(project().andExclude(excludeFields.toArray(new String[0])));
        }
        Aggregation filter = newAggregation(sqlList);
        ChangeStreamRequest changeStreamRequest = changeStreamBuilder
                .collection(collectionName)
                .filter(filter)
                // 不设置时，文档更新时，只会发送变更字段的信息，设置UPDATE_LOOKUP会返回文档的全部信息
                .fullDocumentLookup(FullDocument.UPDATE_LOOKUP)
                .build();
        return messageListenerContainer.register(changeStreamRequest, pojo);
    }

}
