package com.linkus.devmgt.processors;

import com.linkus.devmgt.jobs.DtsTasks;
import com.linkus.devmgt.service.IDataTransmissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

/**
 * 同步bizOprtHist
 * 0 30 1 * * ?
 *
 * <AUTHOR>
 */
@Component
public class SyncBizOprtHistProcessor implements BasicProcessor {

    @Autowired
    IDataTransmissionService dataTransmissionService;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {
        OmsLogger omsLogger = context.getOmsLogger();
        try {
            omsLogger.info("start");
            dataTransmissionService.syncBizOprtHist();
            omsLogger.info("end");
            return new ProcessResult(true, "success");
        } catch (Exception ex) {
            omsLogger.error("exception", ex);
            return new ProcessResult(false, "exception:" + ex.getMessage());
        }
    }
}