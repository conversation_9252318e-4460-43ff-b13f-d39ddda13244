package com.linkus.pdf.uitl;


import org.apache.commons.lang.StringUtils;

public class PersistTracer {

    private PersistTracer() {
    }

    private static ThreadLocal<String> traceID = new ThreadLocal<>();

    // TODO: 2018/12/19 后续的请求使用了之前的已经产生traceId的线程，待清理
    public static void init() {
        if (traceID.get() == null) {
            traceID.set(java.util.UUID.randomUUID().toString().replace("-", StringUtils.EMPTY));
        }
    }

    public static void remove() {
        traceID.remove();
    }

    public static String getId() {
        if (traceID.get() != null) {
            return traceID.get();
        }
        return StringUtils.EMPTY;
    }
}
