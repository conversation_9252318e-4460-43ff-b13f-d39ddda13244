spelling=Grammar checking
hlp_grammar=Check more grammar errors.
grammar=Possible mistakes
hlp_cap=Check missing capitalization of sentences.
cap=Capitalization
hlp_dup=Check repeated words.
dup=Word duplication
hlp_pair=Check missing or extra parentheses and quotation marks.
pair=Parentheses
punctuation=Punctuation
hlp_spaces=Check single spaces between words.
spaces=Word spacing
hlp_mdash=Force unspaced em dash instead of spaced en dash.
mdash=Em dash
hlp_ndash=Force spaced en dash instead of unspaced em dash.
ndash=En dash
hlp_quotation=Check double quotation marks: "x" \u2192 \u201Cx\u201D
quotation=Quotation marks
hlp_times=Check true multiplication sign: 5x5 \u2192 5\u00D75
times=Multiplication sign
hlp_spaces2=Check single spaces between sentences.
spaces2=Sentence spacing
hlp_spaces3=Check more than two extra space characters between words and sentences.
spaces3=More spaces
hlp_minus=Change hyphen characters to real minus signs.
minus=Minus sign
hlp_apostrophe=Change typewriter apostrophe, single quotation marks and correct double primes.
apostrophe=Apostrophe
hlp_ellipsis=Change three dots with ellipsis.
ellipsis=Ellipsis
others=\u063A\u06CC\u0631\u0647
hlp_metric=Measurement conversion from \u00B0F, mph, ft, in, lb, gal and miles.
metric=Convert to metric (\u00B0C, km/h, m, kg, l)
hlp_numsep=Common (1000000 \u2192 1,000,000) or ISO (1000000 \u2192 1 000 000).
numsep=Thousand separation of large numbers
hlp_nonmetric=Measurement conversion from \u00B0C; km/h; cm, m, km; kg; l.
nonmetric=Convert to non-metric (\u00B0F, mph, ft, lb, gal)
