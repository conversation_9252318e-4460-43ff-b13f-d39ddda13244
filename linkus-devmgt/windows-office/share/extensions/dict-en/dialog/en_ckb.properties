spelling=\u0648\u0631\u062F\u0628\u06CC\u0646\u06CC \u0695\u06CE\u0632\u0645\u0627\u0646
hlp_grammar=\u0648\u0631\u062F\u0628\u06CC\u0646\u06CC \u0632\u06CC\u0627\u062A\u0631\u06CC \u0647\u06D5\u06B5\u06D5\u06CC \u0695\u06CE\u0632\u0645\u0627\u0646\u06CC
grammar=Possible mistakes
hlp_cap=\u0686\u06CE\u06A9\u0631\u062F\u0646\u06CC \u067E\u06CC\u062A\u06CC \u06AF\u06D5\u0648\u0631\u06D5 \u0644\u06D5 \u0631\u0633\u062A\u06D5\u06A9\u06D5\u062F\u0627.
cap=\u0646\u0648\u0648\u0633\u06CC\u0646 \u0628\u06D5 \u067E\u06CC\u062A\u06CC \u06AF\u06D5\u0648\u0631\u06D5
hlp_dup=\u0686\u06CE\u06A9\u0631\u062F\u0646\u06CC \u067E\u06CC\u062A\u06D5 \u062F\u0648\u0648\u0628\u0627\u0631\u06D5\u06A9\u0627\u0646.
dup=\u067E\u06CC\u062A\u06D5 \u062F\u0648\u0648\u0628\u0627\u0631\u06D5
hlp_pair=\u062F\u06B5\u0646\u06CC\u0627\u0628\u0648\u0648\u0646 \u0644\u06D5 \u0644\u06D5 \u0646\u06D5\u0628\u0648\u0648\u0646\u06CC \u06A9\u06D5\u0648\u0627\u0646\u06D5\u06CC \u0632\u06CC\u0627\u062F\u06D5 \u0648 \u0647\u06CE\u0645\u0627\u06CC \u0648\u062A\u06D5 \u062F\u0627\u0646\u0627\u0646.
pair=\u06A9\u06D5\u0648\u0627\u0646\u06D5\u06A9\u0627\u0646
punctuation=\u0647\u06CE\u0645\u0627\u06CC \u0698\u0645\u0627\u0631\u06D5\u06A9\u0631\u062F\u0646
hlp_spaces=\u0686\u06CE\u06A9\u0631\u062F\u0646\u06CC \u0628\u06C6\u0634\u0627\u06CC\u06CC \u0646\u06CE\u0648\u0627\u0646 \u067E\u06CC\u062A\u06D5\u06A9\u0627\u0646
spaces=\u062F\u0648\u0648\u0631\u06CC \u067E\u06CC\u062A\u06D5\u06A9\u0627\u0646
hlp_mdash=Force unspaced em dash instead of spaced en dash.
mdash=Em dash
hlp_ndash=Force spaced en dash instead of unspaced em dash.
ndash=En dash
hlp_quotation=Check double quotation marks: "x" \u2192 \u201Cx\u201D
quotation=\u0647\u06CE\u0645\u0627\u06CC \u0648\u062A\u06D5\u06CC \u062F\u0627\u0646\u0631\u0627\u0648
hlp_times=Check true multiplication sign: 5x5 \u2192 5\u00D75
times=\u0647\u06CE\u0645\u0627\u06CC \u0644\u06CE\u06A9\u062F\u0627\u0646
hlp_spaces2=Check single spaces between sentences.
spaces2=\u062F\u0648\u0648\u0631\u06CC \u0695\u0633\u062A\u06D5\u06A9\u0627\u0646
hlp_spaces3=Check more than two extra space characters between words and sentences.
spaces3=\u062F\u0648\u0648\u0631\u06CC \u0632\u06CC\u0627\u062A\u0631
hlp_minus=Change hyphen characters to real minus signs.
minus=\u0647\u06CE\u0645\u0627\u06CC \u0644\u06CE\u062F\u06D5\u0631\u06A9\u0631\u062F\u0646
hlp_apostrophe=Change typewriter apostrophe, single quotation marks and correct double primes.
apostrophe=Apostrophe
hlp_ellipsis=\u06AF\u06C6\u0695\u06CC\u0646\u06CC \u0633\u06CE \u062E\u0627\u06B5 \u0628\u06C6 \u0628\u0627\u0632\u0646\u06D5\u06CC\u06CC.
ellipsis=Ellipsis
others=\u0632\u06CC\u0627\u062A\u0631
hlp_metric=Measurement conversion from \u00B0F, mph, ft, in, lb, gal and miles.
metric=Convert to metric (\u00B0C, km/h, m, kg, l)
hlp_numsep=Common (1000000 \u2192 1,000,000) or ISO (1000000 \u2192 1 000 000).
numsep=Thousand separation of large numbers
hlp_nonmetric=Measurement conversion from \u00B0C; km/h; cm, m, km; kg; l.
nonmetric=Convert to non-metric (\u00B0F, mph, ft, lb, gal)
