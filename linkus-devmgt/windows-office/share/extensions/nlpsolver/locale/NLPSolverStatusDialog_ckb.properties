#Dialog
NLPSolverStatusDialog.Dialog.Caption=\u062F\u06C6\u062E\u06CC \u0686\u0627\u0631\u06D5\u0633\u06D5\u0631\u06A9\u06D5\u0631

#Controls
NLPSolverStatusDialog.Controls.lblSolution=\u0686\u0627\u0631\u06D5\u0633\u06D5\u0631\u06CC \u0626\u06CE\u0633\u062A\u0627:
NLPSolverStatusDialog.Controls.lblIteration=\u062F\u0648\u0648\u0628\u0627\u0631\u06D5\u06A9\u0631\u062F\u0646\u06D5\u0648\u06D5:
NLPSolverStatusDialog.Controls.lblStagnation=Stagnation:
NLPSolverStatusDialog.Controls.lblRuntime=\u06A9\u0627\u062A\u06CC \u067E\u06CE\u06A9\u0631\u062F\u0646:
NLPSolverStatusDialog.Controls.btnStop=\u0648\u06D5\u0633\u062A\u0627\u0646\u062F\u0646
NLPSolverStatusDialog.Controls.btnOK=\u0628\u0627\u0634\u06D5
NLPSolverStatusDialog.Controls.btnContinue=\u0628\u06D5\u0631\u062F\u06D5\u0648\u0627\u0645 \u0628\u06D5

#Messages
NLPSolverStatusDialog.Message.StopIteration=Maximum iterations reached.
NLPSolverStatusDialog.Message.StopStagnation=Process stopped due to stagnation.
NLPSolverStatusDialog.Message.StopUser=Process stopped due to user interruption.
NLPSolverStatusDialog.Message.CurrentIteration=Process stopped at iteration %d of %d.

#Time formatting
NLPSolverStatusDialog.Time.Nanoseconds=\u0646\u0627\u0646\u06C6 \u0686\u0631\u06A9\u06D5
NLPSolverStatusDialog.Time.Microseconds=Microseconds
NLPSolverStatusDialog.Time.Milliseconds=\u0645\u06CC\u0644\u06CC \u0686\u0631\u06A9\u06D5
NLPSolverStatusDialog.Time.Second=\u0686\u0631\u06A9\u06D5
NLPSolverStatusDialog.Time.Seconds=\u0686\u0631\u06A9\u06D5\u06A9\u0627\u0646
NLPSolverStatusDialog.Time.Minute=\u062E\u0648\u0644\u06D5\u06A9
NLPSolverStatusDialog.Time.Minutes=\u062E\u0648\u0644\u06D5\u06A9\u06D5\u06A9\u0627\u0646
NLPSolverStatusDialog.Time.Hour=\u06A9\u0627\u0698\u06CE\u0631
NLPSolverStatusDialog.Time.Hours=\u06A9\u0627\u0698\u06CE\u0631\u06D5\u06A9\u0627\u0646
NLPSolverStatusDialog.Time.Day=\u0695\u06C6\u0698
NLPSolverStatusDialog.Time.Days=\u0695\u06C6\u0698\u06D5\u06A9\u0627\u0646
