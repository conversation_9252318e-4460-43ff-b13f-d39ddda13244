#BaseNLPSolver
NLPSolverCommon.Properties.AssumeNonNegative=Assume Non-Negative Variables

#BaseEvolutionarySolver
NLPSolverCommon.Properties.SwarmSize=Size of Swarm
NLPSolverCommon.Properties.LibrarySize=Size of Library
NLPSolverCommon.Properties.LearningCycles=Learning Cycles
NLPSolverCommon.Properties.GuessVariableRange=Variable Bounds Guessing
NLPSolverCommon.Properties.VariableRangeThreshold=Variable Bounds Threshold (when guessing)
NLPSolverCommon.Properties.UseACRComparator=Use ACR Comparator (instead of BCH)
NLPSolverCommon.Properties.UseRandomStartingPoint=Use Random starting point
NLPSolverCommon.Properties.StagnationLimit=Stagnation Limit
NLPSolverCommon.Properties.Tolerance=Stagnation Tolerance
NLPSolverCommon.Properties.EnhancedSolverStatus=Show enhanced solver status

#DEPS
NLPSolverCommon.Properties.AgentSwitchRate=Agent Switch Rate (DE Probability)
NLPSolverCommon.Properties.DEFactorMin=DE: Min Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DEFactorMax=DE: Max Scaling Factor (0-1.2)
NLPSolverCommon.Properties.DECR=DE: Crossover Probability (0-1)
NLPSolverCommon.Properties.PSC1=PS: Cognitive Constant
NLPSolverCommon.Properties.PSC2=PS: Social Constant
NLPSolverCommon.Properties.PSWeight=PS: Constriction Coefficient
NLPSolverCommon.Properties.PSCL=PS: Mutation Probability (0-0.005)
