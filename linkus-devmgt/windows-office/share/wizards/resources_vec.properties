#
# This file is part of the LibreOffice project.
#
# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.
#
# This file incorporates work covered by the following license notice:
#
#   Licensed to the Apache Software Foundation (ASF) under one or more
#   contributor license agreements. See the NOTICE file distributed
#   with this work for additional information regarding copyright
#   ownership. The ASF licenses this file to you under the Apache
#   License, Version 2.0 (the "License; you may not use this file
#   except in compliance with the License. You may obtain a copy of
#   the License at http://www.apache.org/licenses/LICENSE-2.0 .
#
# x-no-translate

#
#  resources.properties
#
#  resources for com.sun.star.wizards
#
RID_COMMON_0=A no ze m\u00ECa pos\u00ECbi\u0142e crear \u0142a carte\u0142a '%1'.<BR>Inte'l to disco r\u00ECjido, A podar\u00ECa no \u00E8sarghe m\u00ECa spasio che basta.
RID_COMMON_1=A no ze m\u00ECa pos\u00ECbi\u0142e crear el documento de testo.<BR>Contro\u0142a che el m\u00F2du\u0142o 'PRODUCTNAME Writer' el sipie st\u00E0 insta\u0142\u00E0.
RID_COMMON_2=A no ze m\u00ECa pos\u00ECbi\u0142e crear el fojo e\u0142etr\u00F2nego.<BR>Contro\u0142a che el m\u00F2du\u0142o 'PRODUCTNAME Calc' el sipie st\u00E0 insta\u0142\u00E0.
RID_COMMON_3=A no ze m\u00ECa pos\u00ECbi\u0142e crear \u0142a prezentasion.<BR>Contro\u0142a che el m\u00F2du\u0142o 'PRODUCTNAME Impress' el sipie st\u00E0 insta\u0142\u00E0.
RID_COMMON_4=A no ze m\u00ECa pos\u00ECbi\u0142e crear el dezenjo.<BR>Contro\u0142a che el m\u00F2du\u0142o 'PRODUCTNAME Draw' el sipie st\u00E0 insta\u0142\u00E0.
RID_COMMON_5=A no ze m\u00ECa pos\u00ECbi\u0142e crear \u0142a f\u00F2rmu\u0142a.<BR>Contro\u0142a che el m\u00F2du\u0142o 'PRODUCTNAME Math' el sipie st\u00E0 insta\u0142\u00E0.
RID_COMMON_6=A no ze m\u00ECa pos\u00ECbi\u0142e trovar i file che A te gh\u00E8 dimand\u00E0.<BR>Fa partir el programa de insta\u0142asion de %PRODUCTNAME e se\u0142esiona 'Justa'.
RID_COMMON_7=El file '<PATH>' l'eziste za.<BR><BR>Vuto sorascr\u00ECvarlo?
RID_COMMON_8=S\u00EC
RID_COMMON_9=S\u00EC a tuti
RID_COMMON_10=N\u00F2
RID_COMMON_11=Anu\u0142a
RID_COMMON_12=~Fine
RID_COMMON_13=< ~Indr\u00ECo
RID_COMMON_14=~Vanti>
RID_COMMON_15=Guid~a
RID_COMMON_16=Pasaji
RID_COMMON_17=Sara s\u00F9
RID_COMMON_18=OK
RID_COMMON_19=El file l'eziste za. Vuto sorascr\u00ECvarlo?
RID_COMMON_20=Mode\u0142o cre\u00E0 co <wizard_name> el <current_date>.
RID_COMMON_21=\u0141a prosedura guid\u00E0 no \u0142a pol m\u00ECa \u00E8sar ezegu\u00ECa parch\u00E9 racuanti file inportanti no i ze m\u00ECa st\u00E0i cat\u00E0i.\nFaghe click so'l boton 'Predefen\u00ECo' so 'Strumenti - Opsion - %PRODUCTNAME - Parcorsi' par reinpostar i parcorsi co \u0142e inpostasion predefen\u00ECe orizena\u0142i.\nDopo taca da novo \u0142a prosedura guid\u00E0.
RID_REPORT_0=Creasion guid\u00E0 rezoconto
RID_REPORT_3=~Tabe\u0142a
RID_REPORT_4=Co\u0142o~ne
RID_REPORT_7=Rezoconto_
RID_REPORT_8=- m\u00ECa defin\u00ECo -
RID_REPORT_9=~Canpi de'l rezoconto
RID_REPORT_11=Ingrupasion
RID_REPORT_12=Opsion de ordenasion
RID_REPORT_13=Sernisi layout
RID_REPORT_14=Crea rezoconto
RID_REPORT_15=Layout de i dati
RID_REPORT_16=Layout de intestasion e pie de p\u00E0jina
RID_REPORT_19=Canpi
RID_REPORT_20=\u00D3~rdena par
RID_REPORT_21=~Donca par
RID_REPORT_22=Orientasion
RID_REPORT_23=Vertega\u0142e
RID_REPORT_24=Orizonta\u0142e
RID_REPORT_28=Che canpi vuto aver inte'l to rezoconto?
RID_REPORT_29=Vuto zontar altri \u0142ive\u0142i de ingrupasion?
RID_REPORT_30=Drio che canpi ze\u0142o che te vol ordenar i dati?
RID_REPORT_31=Che aparensa ga\u0142o da aver el rezoconto?
RID_REPORT_32=Sernisi come che A te vol ndar vanti
RID_REPORT_33=T\u00ECto\u0142o de'l rezoconto
RID_REPORT_34=Vizua\u0142iza rezoconto
RID_REPORT_35=Crea rezoconto
RID_REPORT_36=Cresente
RID_REPORT_37=Decresente
RID_REPORT_40=~Rezoconto din\u00E0mego
RID_REPORT_41=Crea rezoconto ~deso
RID_REPORT_42=~Mod\u00ECfega el layout de'l rezoconto
RID_REPORT_43=Rezoconto st\u00E0tego
RID_REPORT_44=Salva cof\u00E0
RID_REPORT_50=Ingrupasion
RID_REPORT_51=Donca ~par
RID_REPORT_52=~Donca par
RID_REPORT_53=Cres~ente
RID_REPORT_54=Crese~nte
RID_REPORT_55=C~resente
RID_REPORT_56=Decre~sente
RID_REPORT_57=De~cresente
RID_REPORT_58=Decre~sente
RID_REPORT_60=A no ze m\u00ECa pos\u00ECbi\u0142e vizua\u0142izar i canpi binari inte'l rezoconto.
RID_REPORT_61=\u0141a tabe\u0142a '<TABLENAME>' no \u0142a eziste m\u00ECa.
RID_REPORT_62=Creasion de'l rezoconto...
RID_REPORT_63=N\u00F9maro de rejistrasion dati insar\u00ECo: <COUNT>
RID_REPORT_64=El formu\u0142aro '<REPORTFORM>' no l'eziste m\u00ECa.
RID_REPORT_65=Inpos\u00ECbi\u0142e ezeguir l'intarogasion co l'espresion <BR>'<STATEMENT>' <BR>. <BR> Contro\u0142a \u0142a to sorzente dati.
RID_REPORT_66=A no ze m\u00ECa pos\u00ECbi\u0142e \u0142\u00E8zar el contro\u0142o sconto inte'l formu\u0142aro '<REPORTFORM>': '<CONTROLNAME>'.
RID_REPORT_67=Inportasion dati...
RID_REPORT_68=Canpi marca
RID_REPORT_69=In che maniera vuto marcar i canpi?
RID_REPORT_70=Marca
RID_REPORT_71=Canpo
RID_REPORT_72=A ze venjesto fora un eror inte \u0142a prosedura guid\u00E0.<BR>El mode\u0142o '%PATH' el podar\u00ECa \u00E8sar zbaj\u00E0.<BR>O che \u0142e sesion o \u0142e tabe\u0142e no \u0142e eziste o che \u0142e ga un nome zbaj\u00E0.<BR>Par aver informasion p\u00EC detaj\u00E0e varda \u0142a Guida.<BR>Se\u0142esiona n'altro mode\u0142o.
RID_REPORT_73=Inte \u0142a tabe\u0142a, A ghe ze un canpo utente m\u00ECa v\u00E0\u0142ido.
RID_REPORT_74=El criterio de ordenasion '<FIELDNAME>' el ze st\u00E0 sielto do volte. Un criterio el pol \u00E8sar se\u0142esion\u00E0 na volta so\u0142o.
RID_REPORT_75=Nota: cuando che el rezoconto el venjar\u00E0 cre\u00E0, el testo de ezenpio el sar\u00E0 renpias\u00E0 co i dati de'l database.
RID_REPORT_76=Inte'l database, A eziste za un rezoconto co'l nome '%REPORTNAME'. D\u00F2para n'altro nome.
RID_REPORT_78=In che maniera vuto ndar vanti dopo aver cre\u00E0 l'intarogasion?
RID_REPORT_79=Che tipo de rezoconto vuto crear?
RID_REPORT_80=In tabe\u0142a
RID_REPORT_81=A co\u0142ona s\u00ECngo\u0142a
RID_REPORT_82=A co\u0142ona dopia
RID_REPORT_83=A co\u0142ona tripla
RID_REPORT_84=A blochi, marche par sanca
RID_REPORT_85=A blochi, marche in sima
RID_REPORT_86=T\u00ECto\u0142o:
RID_REPORT_87=Autor:
RID_REPORT_88=Data:
# Please don't translate the words #page# and #count#, these are placeholders.
RID_REPORT_89=P\u00E0jina #page# de #count#
RID_REPORT_90=N\u00F9maro de p\u00E0jina:
RID_REPORT_91=Conto p\u00E0jine:
RID_REPORT_92=A no ze m\u00ECa st\u00E0 cat\u00E0 njanca un mode\u0142o de rezoconto v\u00E0\u0142ido.
RID_REPORT_93=P\u00E0jina:
RID_REPORT_94=\u0141ineasion par sanca - Bordo
RID_REPORT_95=\u0141ineasion par sanca - Conpato
RID_REPORT_96=\u0141ineasion par sanca - E\u0142egante
RID_REPORT_97=\u0141ineasion par sanca - Evidensi\u00E0
RID_REPORT_98=\u0141ineasion par sanca - Moderno
RID_REPORT_99=\u0141ineasion par sanca - Roso e bl\u00E8
RID_REPORT_100=Predefen\u00ECa
RID_REPORT_101=Contorno - Bordi
RID_REPORT_102=Contorno - Conpato
RID_REPORT_103=Contorno - E\u0142egante
RID_REPORT_104=Contorno - Evidensi\u00E0
RID_REPORT_105=Contorno - Moderno
RID_REPORT_106=Contorno - Roso e bl\u00E8
RID_REPORT_107=Contorno, indentr\u00E0 - Bordi
RID_REPORT_108=Contorno, indentr\u00E0 - Conpato
RID_REPORT_109=Contorno, indentr\u00E0 - E\u0142egante
RID_REPORT_110=Contorno, indentr\u00E0 - Evidensi\u00E0
RID_REPORT_111=Contorno, indentr\u00E0 - Moderno
RID_REPORT_112=Contorno, indentr\u00E0 - Roso e bl\u00E8
RID_REPORT_113=Bo\u0142e
RID_REPORT_114=S\u00ECnema
RID_REPORT_115=Contro\u0142o
RID_REPORT_116=Predefen\u00ECa
RID_REPORT_117=Zboso
RID_REPORT_118=Finanse
RID_REPORT_119=\u0141avanja a foji
RID_REPORT_120=Forma\u0142e co \u0142ogo azienda\u0142e
RID_REPORT_121=Zen\u00E8rego
RID_REPORT_122=Planisfero
RID_DB_COMMON_0=C~rea
RID_DB_COMMON_1=~Anu\u0142a
RID_DB_COMMON_2=< ~Indr\u00ECo
RID_DB_COMMON_3=~Vanti >
RID_DB_COMMON_4=~Database
RID_DB_COMMON_5=Nome ~tabe\u0142a
RID_DB_COMMON_6=A ze venj\u00F9o fora un eror durante \u0142a prosedura guid\u00E0. Deso \u0142a venjar\u00E0 termin\u00E0.
RID_DB_COMMON_8=A no ze st\u00E0 insta\u0142\u00E0 nesun database. Par inviar \u0142a prosedura guid\u00E0 par i formu\u0142ari, A serve \u0142a prezensa de almanco un database.
RID_DB_COMMON_9=Rento a'l database A no ghe ze m\u00ECa nesuna tabe\u0142a.
RID_DB_COMMON_10='Sto t\u00ECto\u0142o l'eziste za inte'l database. Senja n'altro nome.
RID_DB_COMMON_11=El t\u00ECto\u0142o no'l pol m\u00ECa aver rento spasi o car\u00E0tari spesia\u0142i.
RID_DB_COMMON_12=A no ze m\u00ECa st\u00E0 pos\u00ECbi\u0142e istansiar el servisio de database (com.sun.data.DatabaseEngine).
RID_DB_COMMON_13=A no ze m\u00ECa st\u00E0 pos\u00ECbi\u0142e v\u00E8rzar \u0142a tabe\u0142a o \u0142a reserca se\u0142esion\u00E0.
RID_DB_COMMON_14=A no ze m\u00ECa pos\u00ECbi\u0142e stabi\u0142ir na conesion co'l database.
RID_DB_COMMON_20=Guid~a
RID_DB_COMMON_21=~Ferma
RID_DB_COMMON_30=A no ze m\u00ECa pos\u00ECbi\u0142e salvar el documento.
RID_DB_COMMON_33=Ferma prosedura guid\u00E0
RID_DB_COMMON_34=Co\u0142egamento a \u0142a sorzente dati...
RID_DB_COMMON_35=A no ze m\u00ECa pos\u00ECbi\u0142e con\u00E9tarse a \u0142a sorzente dati.
RID_DB_COMMON_36=El parcorso par el file insar\u00ECo no'l ze m\u00ECa v\u00E0\u0142ido.
RID_DB_COMMON_37=Se\u0142esiona na sorzente dati
RID_DB_COMMON_38=Se\u0142esiona na tabe\u0142a o na intarogasion
RID_DB_COMMON_39=Zonta canpo
RID_DB_COMMON_40=Cava v\u00ECa canpo
RID_DB_COMMON_41=Zonta tuti i canpi
RID_DB_COMMON_42=Cava v\u00ECa tuti i canpi
RID_DB_COMMON_43=Movi canpo par s\u00F9
RID_DB_COMMON_44=Movi canpo par z\u00F3
RID_DB_COMMON_45=A no ze m\u00ECa pos\u00ECbi\u0142e \u0142\u00E8zar i nomi de i canpi da '%NAME'.
RID_QUERY_0=Prosedura guid\u00E0 intarogasion
RID_QUERY_1=Intarogasion
RID_QUERY_2=Prosedura guid\u00E0 intarogasion
RID_QUERY_3=~Tabe\u0142e
RID_QUERY_4=Canpi ~dispon\u00ECbi\u0142i
RID_QUERY_5=Nome de l'intarogasion
RID_QUERY_6=Mostra inta~rogasion
RID_QUERY_7=~Mod\u00ECfega intarogasion
RID_QUERY_8=~In che maniera vuto ndar vanti dopo de aver cre\u00E0 l'intarogasion?
RID_QUERY_9=Cata ~tuti cue\u0142i drioman
RID_QUERY_10=Cata ~almanco uno de cue\u0142i drioman
RID_QUERY_11=Intarogasion ~detaj\u00E0 (Mostra tute \u0142e rejistrasion dati de l'intarogasion)
RID_QUERY_12=Somario intaroga~sion (Mostra so\u0142o i rezultadi de \u0142e funsion agreg\u00E0e)
RID_QUERY_16=Funsion agreg\u00E0e
RID_QUERY_17=Comandi de canpo
RID_QUERY_18=~Ingrupa par
RID_QUERY_19=Canpo
RID_QUERY_20=Alias
RID_QUERY_21=Tabe\u0142a:
RID_QUERY_22=Intarogasion:
RID_QUERY_24=Condision
RID_QUERY_25=Va\u0142or
RID_QUERY_26=el ze gua\u0142ivo a
RID_QUERY_27=no'l ze m\u00ECa gua\u0142ivo a
RID_QUERY_28=el ze p\u00EC picenin de
RID_QUERY_29=el ze p\u00EC grando de
RID_QUERY_30=el ze gua\u0142ivo o p\u00EC ce\u0142o de
RID_QUERY_31=el ze gua\u0142ivo o p\u00EC grando de
RID_QUERY_32=s\u00ECmi\u0142e
RID_QUERY_33=m\u00ECa s\u00ECmi\u0142e
RID_QUERY_34=el ze vodo
RID_QUERY_35=no'l ze m\u00ECa vodo
RID_QUERY_36=true
RID_QUERY_37=false
RID_QUERY_38=e
RID_QUERY_39=o
RID_QUERY_40=c\u00E0lco\u0142a \u0142a soma de
RID_QUERY_41=c\u00E0lco\u0142a \u0142a media de
RID_QUERY_42=c\u00E0lco\u0142a el m\u00ECnimo de
RID_QUERY_43=c\u00E0lco\u0142a el m\u00E0simo de
RID_QUERY_44=c\u00E0lco\u0142a el contejo de
RID_QUERY_48=(sensa)
RID_QUERY_50=Canpi inte ~l'intarogasion:
RID_QUERY_51=Tipo de ordenasion:
RID_QUERY_52=A no ze m\u00ECa st\u00E0i asenj\u00E0i canpi de ordenasion.
RID_QUERY_53=Condision de reserca:
RID_QUERY_54=Njauna condision \u0142a ze st\u00E0 asenj\u00E0.
RID_QUERY_55=Funsion agreg\u00E0e:
RID_QUERY_56=Njauna funsion agreg\u00E0 \u0142a ze st\u00E0 asenjada.
RID_QUERY_57=Ingrupasion par:
RID_QUERY_58=Njaun grupo e\u0142 ze st\u00E0 asenj\u00E0.
RID_QUERY_59=Condision de ingrupasion:
RID_QUERY_60=Njauna condision de ingrupasion \u0142a ze st\u00E0 asenj\u00E0.
RID_QUERY_70=Se\u0142esiona i canpi (co\u0142one) par l'intarogasion
RID_QUERY_71=Se\u0142esiona el tipo de ordenasion
RID_QUERY_72=Se\u0142esiona \u0142e condision de reserca
RID_QUERY_73=Se\u0142esiona el tipo de intarogasion
RID_QUERY_74=Se\u0142esiona i grupi
RID_QUERY_75=Se\u0142esiona \u0142e condision de ingrupasion
RID_QUERY_76=Se ocor, asenja i alias
RID_QUERY_77=Contro\u0142a \u0142a panor\u00E0mega e desidi come ndar vanti
RID_QUERY_80=Se\u0142esion de i canpi
RID_QUERY_81=Tipo de ordenasion
RID_QUERY_82=Condision de reserca
RID_QUERY_83=Detaji o somario
RID_QUERY_84=Ingrupasion
RID_QUERY_85=Condision de ingrupasion
RID_QUERY_86=Alias
RID_QUERY_87=Panor\u00E0mega
RID_QUERY_88=Un canpo sensa funsion agreg\u00E0e asenj\u00E0e el ga da \u00E8sar dopar\u00E0 inte un grupo.
RID_QUERY_89=\u0141a condision '<FIELDNAME> <LOGICOPERATOR> <VALUE>' \u0142a ze st\u00E0 sielta do volte. Na condision \u0142a pol \u00E8sar se\u0142esion\u00E0 na volta so\u0142a
RID_QUERY_90=\u0141a funsion agreg\u00E0 <FUNCTION> \u0142a ghe ze st\u00E0 asenj\u00E0 do volte a'l canpo '<NUMERICFIELD>'.
RID_QUERY_91=,
RID_QUERY_92=<FIELDTITLE> (<FIELDNAME>)
RID_QUERY_93=<FIELDNAME> (<SORTMODE>)
RID_QUERY_94=<FIELDNAME> <LOGICOPERATOR> <VALUE>
RID_QUERY_95=<CALCULATEDFUNCTION> <FIELDNAME>
RID_QUERY_96=<FIELDNAME> <LOGICOPERATOR> <VALUE>
RID_FORM_0=Formu\u0142aro prosedura guid\u00E0
RID_FORM_1=Canpi inte'l ~formu\u0142aro
RID_FORM_2=I canpi binari i ze senpre inco\u0142on\u00E0i e se\u0142esion\u00E0bi\u0142i da \u0142a \u0142ista de sanca.\nSe pos\u00ECbi\u0142e, i vien intarpret\u00E0i cof\u00E0 im\u00E0jini.
RID_FORM_3=Un subformu\u0142aro el ze un formu\u0142aro che'l ze insar\u00ECo inte n'altro formu\u0142aro.\nD\u00F2para i subformu\u0142ari par mostrar i dati de tabe\u0142e e reserche co na re\u0142asion uno-a-tanti.
RID_FORM_4=~Zonta subformu\u0142aro
RID_FORM_5=~Subformu\u0142aro baz\u00E0 so na re\u0142asion ezistente
RID_FORM_6=Tabe\u0142e o reserche
RID_FORM_7=Subformu\u0142aro baz\u00E0 so na se\u0142esion de canpi ~manua\u0142e
RID_FORM_8=~Che tipo de re\u0142asion vuto zontar?
RID_FORM_9=Canpi inte'l ~subformu\u0142aro
RID_FORM_12=Canpi ~dispon\u00ECbi\u0142i
RID_FORM_13=Canpi inte'l ~subformu\u0142aro
RID_FORM_19=\u0141a re\u0142asion '<FIELDNAME1>' e '<FIELDNAME2>' \u0142a ze st\u00E0 se\u0142esion\u00E0 do volte.\nOnji re\u0142asion \u0142a pol \u00E8sar dopar\u00E0 na volta so\u0142o.
RID_FORM_20=Canpo de'l subformu\u0142aro co\u0142eg\u00E0 par ~primo
RID_FORM_21=Canpo de'l subformu\u0142aro co\u0142eg\u00E0 par ~secondo
RID_FORM_22=Canpo de'l subformu\u0142aro co\u0142eg\u00E0 par ~terso
RID_FORM_23=Canpo de'l subformu\u0142aro co\u0142eg\u00E0 par ~cuarto
RID_FORM_24=Canpo de'l formu\u0142aro prinsipa\u0142e co\u0142eg\u00E0 par p~rimo
RID_FORM_25=Canpo de'l formu\u0142aro prinsipa\u0142e co\u0142eg\u00E0 par se~condo
RID_FORM_26=Canpo de'l formu\u0142aro prinsipa\u0142e co\u0142eg\u00E0 par te~rso
RID_FORM_27=Canpo de'l formu\u0142aro prinsipa\u0142e co\u0142eg\u00E0 par c~uarto
RID_FORM_28=Bordo de'l canpo
RID_FORM_29=Nesun bordo
RID_FORM_30=Aspeto 3D
RID_FORM_31=Piato
RID_FORM_32=Dispozision marca
RID_FORM_33=\u0141ineasion par sanca
RID_FORM_34=\u0141ineasion par drita
RID_FORM_35=Sistemasion de i canpi DB
RID_FORM_36=Inco\u0142onar - marche par sanca
RID_FORM_37=Inco\u0142onar - marche in sima
RID_FORM_38=A blochi - marche par sanca
RID_FORM_39=A blochi - marche in sima
RID_FORM_40=Cof\u00E0 \u0142a scheda t\u00E8cnega
RID_FORM_41=Sistemasion de'l canpo prinsipa\u0142e
RID_FORM_42=Sistemasion de'l canpo secondario
RID_FORM_44=El formu\u0142aro el ga da \u00E8sar do~par\u00E0 sol che par m\u00E9tar rento dati novi.
RID_FORM_45=I dati ezistenti no i venjar\u00E0 m\u00ECa mostr\u00E0i
RID_FORM_46=El for~mu\u0142aro el mostrar\u00E0 tuti i dati
RID_FORM_47=No st\u00E0 m\u00ECa parm\u00E9tar ~mod\u00ECfeghe de i dati ezistenti
RID_FORM_48=No st\u00E0 m\u00ECa parm\u00E9tar \u0142a ~scanse\u0142asion de i dati ezistenti
RID_FORM_49=No st\u00E0 m\u00ECa parm\u00E9tar de ~zontar dati novi
RID_FORM_50=Nome d~e'l formu\u0142aro
RID_FORM_51=~In che maniera vuto ndar vanti dopo aver cre\u00E0 un formu\u0142aro?
RID_FORM_52=~D\u00F2para el formu\u0142aro
RID_FORM_53=~Mod\u00ECfega el formu\u0142aro
RID_FORM_55=~Sti\u0142i de p\u00E0jina
RID_FORM_80=Se\u0142esion de i canpi
RID_FORM_81=Configura un subformu\u0142aro
RID_FORM_82=Zonta canpi inte'l subformu\u0142aro
RID_FORM_83=Se\u0142esiona canpi co\u0142eg\u00E0i
RID_FORM_84=Sistema canpi de contro\u0142o
RID_FORM_85=Inposta insarimento dati
RID_FORM_86=\u00C0plega sti\u0142i
RID_FORM_87=Inposta nome
RID_FORM_88=(Data)
RID_FORM_89=(Ora)
RID_FORM_90=Se\u0142esiona i canpi de'l formu\u0142aro
RID_FORM_91=Desidi se te vo\u0142i inpostar un subformu\u0142aro
RID_FORM_92=Se\u0142esiona i canpi inte'l subformu\u0142aro
RID_FORM_93=Se\u0142esiona i co\u0142egamenti inframezo i formu\u0142ari
RID_FORM_94=Sistema i canpi de contro\u0142o
RID_FORM_95=Se\u0142esiona el modo de insarimento de i dati
RID_FORM_96=\u00C0plega el sti\u0142e de'l formu\u0142aro
RID_FORM_97=Inposta el nome de'l formu\u0142aro
RID_FORM_98=A eziste za un formu\u0142aro co'l nome '%FORMNAME'.\nD\u00F2para n'altro nome.
RID_TABLE_1=Creasion guid\u00E0 tabe\u0142a
RID_TABLE_2=Se\u0142esiona i canpi
RID_TABLE_3=Inposta tipi e formati
RID_TABLE_4=Inposta \u0142a ciave primaria
RID_TABLE_5=Crea tabe\u0142a
RID_TABLE_8=Se\u0142esiona i canpi par \u0142a tabe\u0142a
RID_TABLE_9=Inposta i tipi de canpo e i formati
RID_TABLE_10=Inposta \u0142a ciave primaria
RID_TABLE_11=Crea tabe\u0142a
RID_TABLE_14='Sta prosedura guid\u00E0 \u0142a te juta a crear na tabe\u0142a par el to database. Dopo aver se\u0142esion\u00E0 na categor\u00ECa de tabe\u0142a e un canpion de tabe\u0142a, sernisi i canpi da incl\u00F9dar inte \u0142a to tabe\u0142a. A te po\u0142i ciapar canpi da p\u00EC de na tabe\u0142a de canpion.
RID_TABLE_15=Ca~tegor\u00ECa
RID_TABLE_16=~\u0141aoro
RID_TABLE_17=P~arsona\u0142e
RID_TABLE_18=~Canpion de tabe\u0142e
RID_TABLE_19=Canpi ~dispon\u00ECbi\u0142i
RID_TABLE_20=Informasion de'l canpo
RID_TABLE_21=+
RID_TABLE_22=-
RID_TABLE_23=Nome de canpo
RID_TABLE_24=Tipo de canpo
RID_TABLE_25=Canpi ~se\u0142esion\u00E0i
RID_TABLE_26=Na ciave primaria \u0142a ident\u00ECfega univogamente onji rejistrasion dati inte na tabe\u0142a de database. \u0141e ciave primarie \u0142e senpl\u00ECfega el co\u0142egamento de \u0142e informasion so tabe\u0142e separ\u00E0e, e A ze racomand\u00E0 de aver na ciave primaria rento de onji tabe\u0142a. Sensa na ciave primaria, A no te podar\u00E8 m\u00ECa m\u00E9tar rento dati inte 'sta tabe\u0142a.
RID_TABLE_27=~Crea na ciave primaria
RID_TABLE_28=Zonta ~automategamente na ciave primaria
RID_TABLE_29=~Dopara un canpo ezistente cof\u00E0 ciave primaria
RID_TABLE_30=Definisi \u0142a ciave p~rimaria cof\u00E0 conbinasion de canpi
RID_TABLE_31=Nome de'l ca~npo
RID_TABLE_32=Canpi de \u0142a ciave ~primaria
RID_TABLE_33=~Va\u0142or autom\u00E0tego
RID_TABLE_34=Che nome vuto darghe a \u0142a tabe\u0142a?
RID_TABLE_35=Congratu\u0142asion. A te gh\u00E8 meso rento tute \u0142e informasion che \u0142e serv\u00ECa par \u0142a creasion de \u0142a tabe\u0142a.
RID_TABLE_36=Come vuto ndar vanti?
RID_TABLE_37=Mod\u00ECfega el projeto de \u0142a tabe\u0142a
RID_TABLE_38=Insarisi suito i dati
RID_TABLE_39=C~rea un formu\u0142aro baz\u00E0 so 'sta tabe\u0142a
RID_TABLE_40=A no ze m\u00ECa pos\u00ECbi\u0142e v\u00E8rzar \u0142a tabe\u0142a cre\u00E0.
RID_TABLE_41=El nome de \u0142a tabe\u0142a '%TABLENAME' el contien un car\u00E0tare ('%SPECIALCHAR') che el podar\u00ECa no \u00E8sar m\u00ECa suport\u00E0 da'l database.
RID_TABLE_42=El nome de'l canpo '%FIELDNAME' el contien un car\u00E0tare ('%SPECIALCHAR') che el podar\u00ECa no \u00E8sar m\u00ECa suport\u00E0 da'l database.
RID_TABLE_43=Canpo
RID_TABLE_44=Tabe\u0142aParsona\u0142e
RID_TABLE_45=Zonta un canpo
RID_TABLE_46=Cava v\u00ECa el canpo se\u0142esion\u00E0
RID_TABLE_47=A no ze m\u00ECa pos\u00ECbi\u0142e insarir el canpo parch\u00E9 A se s\u00F9para el \u0142\u00ECmite m\u00E0simo de %COUNT canpi inte \u0142a tabe\u0142a de'l database
RID_TABLE_48=El nome '%TABLENAME' l'eziste za.\nM\u00E9taghene n'altro.
RID_TABLE_49=Cat\u00E0\u0142ogo de \u0142a tabe\u0142a
RID_TABLE_50=Schema de \u0142a tabe\u0142a
RID_TABLE_51=El canpo '%FIELDNAME' l'eziste za.
STEP_ZERO_0=~Anu\u0142a
STEP_ZERO_1=Guid~a
STEP_ZERO_2=< ~Indr\u00ECo
STEP_ZERO_3=~Convartisi
STEP_ZERO_4=Nota: A no ze m\u00ECa pos\u00ECbi\u0142e convartir i va\u0142ori monedari da link esterni e i fatori de convarsion monedari in f\u00F2rmu\u0142e.
STEP_ZERO_5=Prima de tuto, desprotezi tuti i foji.
STEP_ZERO_6=Va\u0142ude:
STEP_ZERO_7=V~anti>>
STEP_ZERO_8=Sa~ra s\u00F9
STEP_CONVERTER_0=Documento ~intiero
STEP_CONVERTER_1=Se\u0142esion
STEP_CONVERTER_2=S~ti\u0142e de caze\u0142a
STEP_CONVERTER_3=Caze\u0142e de va\u0142uda de'l fo~jo
STEP_CONVERTER_4=Caze\u0142e de va\u0142uda de'l ~documento intiero
STEP_CONVERTER_5=Zona ~se\u0142esion\u00E0
STEP_CONVERTER_6=Se\u0142esiona sti\u0142i de caze\u0142a
STEP_CONVERTER_7=Se\u0142esiona caze\u0142e de \u0142a va\u0142uda
STEP_CONVERTER_8=Zone de va\u0142uda:
STEP_CONVERTER_9=Mode\u0142i:
STEP_AUTOPILOT_0=Estension
STEP_AUTOPILOT_1=Documento ~s\u00ECngo\u0142o %PRODUCTNAME Calc
STEP_AUTOPILOT_2=~Carte\u0142a conpleta
STEP_AUTOPILOT_3=Documento de or\u00ECzine:
STEP_AUTOPILOT_4=Carte\u019Aa de or\u00ECzine:
STEP_AUTOPILOT_5=~Ciapa rento anca \u0142e sotocarte\u0142e
STEP_AUTOPILOT_6=Carte\u0142a de destinasion:
STEP_AUTOPILOT_7=Desprotezi el fojo par un fi\u00E0 de tenpo sensa dimanda de conferma
STEP_AUTOPILOT_10=Convartisi anca i canpi e \u0142e tabe\u0142e inte i documenti de testo
STATUSLINE_0=Stato convarsion:
STATUSLINE_1=Stato convarsion de i mode\u0142i de caze\u0142a:
STATUSLINE_2=Reserca de \u0142e zone da considarar: Fojo %1Number%1 de %2TotPageCount%2
STATUSLINE_3=Reserca de \u0142e zone da convartir...
STATUSLINE_4=\u0141a protesion par onji fojo \u0142a venjar\u00E0 repristin\u00E0...
STATUSLINE_5=Convarsion de \u0142e unit\u00E0 de va\u0142uda inte i mode\u0142i de caze\u0142a...
MESSAGES_0=~Fine
MESSAGES_1=Se\u0142esiona carte\u0142a
MESSAGES_2=Se\u0142esiona file
MESSAGES_3=Se\u0142esiona carte\u0142a de destinasion
MESSAGES_4=mancante
MESSAGES_5=Convartidor Euro
MESSAGES_6=Vuto desprot\u00E8zar i foji tenporaneamente?
MESSAGES_7=Meti rento \u0142a password par desprot\u00E8zar \u0142a tabe\u0142a %1TableName%1
MESSAGES_8=Password zbaj\u00E0!
MESSAGES_9=Fojo protezesto
MESSAGES_10=Atension!
MESSAGES_11=\u0141e protesion par i foji no \u0142e venjar\u00E0 m\u00ECa cav\u00E0e v\u00ECa.
MESSAGES_12=A no se pol m\u00ECa desprot\u00E8zar el fojo
MESSAGES_13=\u0141a prosedura guid\u00E0 no \u0142a pol m\u00ECa modifegar 'sto documento parch\u00E9 i formati de \u0142e caze\u0142e no i pol m\u00ECa \u00E8sar modifeg\u00E0i inte i documenti che i contien foji protezesti.
MESSAGES_14=Tien conto che sen\u00F2 el convartidor Euro no'l pol m\u00ECa modifegar el documento!
MESSAGES_15=Se\u0142esiona na va\u0142uda da convartir!
MESSAGES_16=Password:
MESSAGES_17=OK
MESSAGES_18=Anu\u0142a
MESSAGES_19=Se\u0142esiona un documento %PRODUCTNAME Calc da modifegar!
MESSAGES_20='<1>' no \u0142a ze m\u00ECa na carte\u0142a!
MESSAGES_21=El documento el ze de so\u0142a \u0142etura!
MESSAGES_22=El file '<1>' l'eziste za.<CR>Vutu sorascr\u00ECvarlo?
MESSAGES_23=Vutu davero blocar \u0142a convarsion inte 'sto ponto?
MESSAGES_24=Anu\u0142a prosedura guidada
CURRENCIES_0=Scudo portogheze
CURRENCIES_1=Fiorin o\u0142andeze
CURRENCIES_2=Franco franseze
CURRENCIES_3=Pezeta spanjo\u0142a
CURRENCIES_4=\u0141ira ita\u0142iana
CURRENCIES_5=Marco todesco
CURRENCIES_6=Franco belga
CURRENCIES_7=Sterlina irlandeze
CURRENCIES_8=Franco \u0142usenburgheze
CURRENCIES_9=Se\u0142in Austr\u00ECago
CURRENCIES_10=Marco finlandeze
CURRENCIES_11=Dracma grega
CURRENCIES_12=T\u00E0\u0142aro zloven
CURRENCIES_13=\u0141ira sipriota
CURRENCIES_14=\u0141ira malteze
CURRENCIES_15=Corona zlovaca
CURRENCIES_16=Corona \u00C8stone
CURRENCIES_17=Lats \u0142\u00E8tone
CURRENCIES_18=Litas \u0142ituan
STEP_LASTPAGE_0=Progreso
STEP_LASTPAGE_1=Rec\u00F9paro de i documenti inportanti...
STEP_LASTPAGE_2=Convarsion de i documenti...
STEP_LASTPAGE_3=Inpostasion:
STEP_LASTPAGE_4=Fojo senpre desproteto
STYLES_0=Se\u0142esion de'l tema
STYLES_1=Eror salvando el documento inte \u0142e note! L'asion drioman no \u0142a pol m\u00ECa \u00E8sar revart\u00ECa.
STYLES_2=~Anu\u0142a
STYLES_3=O~K
STYLENAME_0=(Standard)
STYLENAME_1=Foje de autun
STYLENAME_2=\u00C8sar
STYLENAME_3=Bianco e moro
STYLENAME_4=Zbaro de more
STYLENAME_5=Jeans bl\u00E8
STYLENAME_6=Restorante Ani '50
STYLENAME_7=Giasaro
STYLENAME_8=Ua verda
STYLENAME_9=Bl\u00E8 marin
STYLENAME_10=Mi\u0142enio
STYLENAME_11=Natura
STYLENAME_12=Neon
STYLENAME_13=Note
STYLENAME_14=PC Nostalz\u00ECa
STYLENAME_15=Paste\u0142o
STYLENAME_16=Festa in pisina
STYLENAME_17=Suca
CorrespondenceDialog_0=Destinadaro
CorrespondenceDialog_1=Un destinadaro
CorrespondenceDialog_2=P\u00EC destinadari (database indarisario)
CorrespondenceDialog_3=D\u00F2paro de 'sto mode\u0142o
CorrespondenceMsgError=A ze capit\u00E0 un eror.
CorrespondenceFields_0=Clica so senjaposto e sorascrivi
CorrespondenceFields_1=Dita
CorrespondenceFields_2=Reparto
CorrespondenceFields_3=Nome
CorrespondenceFields_4=Nome de Fameja
CorrespondenceFields_5=Via
CorrespondenceFields_6=Paeze
CorrespondenceFields_7=CAP
CorrespondenceFields_8=\u0141oca\u0142it\u00E0
CorrespondenceFields_9=T\u00ECto\u0142o
CorrespondenceFields_10=Pozision
CorrespondenceFields_11=M\u00F2du\u0142o de l'indariso
CorrespondenceFields_12=Inisia\u0142i
CorrespondenceFields_13=F\u00F2rmu\u0142a de sa\u0142udo
CorrespondenceFields_14=Te\u0142. priv\u00E0
CorrespondenceFields_15=Te\u0142. \u0142aoro
CorrespondenceFields_16=Fax
CorrespondenceFields_17=Mail
CorrespondenceFields_18=URL
CorrespondenceFields_19=Note
CorrespondenceFields_20=Canpo 1
CorrespondenceFields_21=Canpo 2
CorrespondenceFields_22=Canpo 3
CorrespondenceFields_23=Canpo 4
CorrespondenceFields_24=ID
CorrespondenceFields_25=Provinsa
CorrespondenceFields_26=Te\u0142. ofisio
CorrespondenceFields_27=Sercaparsone
CorrespondenceFields_28=Te\u0142efonin
CorrespondenceFields_29=N'altro te\u0142\u00E8fono
CorrespondenceFields_30=URL Ca\u0142endaro
CorrespondenceFields_31=Invita
CorrespondenceNoTextmark_0=Manca el senja\u0142ibro 'Destinadaro'.
CorrespondenceNoTextmark_1=A no ze m\u00ECa pos\u00ECbi\u0142e incl\u00F9dar i canpi de \u0142a stanpa in serie.
AgendaDlgName=Mode\u0142o de verba\u0142e
AgendaDlgNoCancel=Na opsion \u0142a ga da \u00E8sar conferm\u00E0 par forsa.
AgendaDlgFrame=Tipo de verba\u0142e
AgendaDlgButton1=Verba\u0142e de i rezultadi
AgendaDlgButton2=Verba\u0142e de va\u0142utasion
TextField=El canpo dati utente no'l ze st\u00E0 m\u00ECa defin\u00ECo!
NoDirCreation=A no ze m\u00ECa pos\u00ECbi\u0142e crear \u0142a carte\u0142a '%1':
MsgDirNotThere=\u0141a carte\u0142a '%1' no \u0142a eziste m\u00ECa.
QueryfornewCreation=Vuto crearla deso?
HelpButton=Guid~a
CancelButton=~Anu\u0142a
BackButton=< ~Indr\u00ECo
NextButton=~Vanti >>
BeginButton=~Convartisi
CloseButton=~Sara s\u00F9
WelcometextLabel1='Sta prosedura guid\u00E0 \u0142a convartise documenti de formati presedenti inte'l formato Open Document par \u0142e aplegasion de ofisio.
WelcometextLabel3=Se\u0142esiona el tipo de documento da convartir:
MSTemplateCheckbox_1_=Mode\u0142i de Word
MSTemplateCheckbox_2_=Mode\u0142i de Excel
MSTemplateCheckbox_3_=Mode\u0142i de PowerPoint
MSDocumentCheckbox_1_=Documenti de Word
MSDocumentCheckbox_2_=Documenti de Excel
MSDocumentCheckbox_3_=Documenti de PowerPoint/Publisher
MSContainerName=Microsoft Office
SummaryHeader=Somario:
GroupnameDefault=Mode\u0142i_inport\u00E0i
ProgressMoreDocs=Documenti
ProgressMoreTemplates=Mode\u0142i
FileExists=El file '<1>' l'eziste za.<CR>Vuto sorascr\u00ECvarlo?
MorePathsError3=\u0141e carte\u0142e no \u0142e eziste m\u00ECa
ConvertError1=Vuto davero blocar \u0142a convarsion inte 'sto ponto?
ConvertError2=Anu\u0142a prosedura guidada
RTErrorDesc=A se g\u00E0 varifeg\u00E0 un eror inte \u0142a prosedura guidada.
RTErrorHeader=Eror
OverwriteallFiles=Vuto sorascr\u00ECvar i documenti sensa dimanda de contro\u0142o?
ReeditMacro=\u0141a macro de'l documento \u0142a ga da ndar revista.
CouldNotsaveDocument=A no ze m\u00ECa pos\u00ECbi\u0142e salvar el documento '<1>'.
CouldNotopenDocument=A no ze m\u00ECa pos\u00ECbi\u0142e v\u00E8rzar el documento '<1>'.
PathDialogMessage=Se\u0142esiona na carte\u0142a
DialogTitle=Convartidor de documenti
SearchInSubDir=Conpreze sotocarte\u0142e
ProgressPage1=Progreso
ProgressPage2=Rec\u00F9paro de i documenti inportanti:
ProgressPage3=Convarsion de i documenti
ProgressFound=Cat\u00E0:
ProgressPage5=%1 cat\u00E0
Ready=Fin\u00ECo
SourceDocuments=Documenti sorzente
TargetDocuments=Documenti de destinasion
LogfileSummary=<COUNT> documenti convart\u00ECbi\u0142i
SumInclusiveSubDir=Tute \u0142e sotocarte\u0142e \u0142e sar\u00E0 ciap\u00E0e in considarasion
SumSaveDokumente=L'esportasion \u0142a venjar\u00E0 fazesta inte \u0142a carte\u0142a drioman:
TextImportLabel=Inporta da:
TextExportLabel=Salva so:
CreateLogfile=Crea file de rejistro
LogfileHelpText=A venjar\u00E0 cre\u00E0 un file de rejistro inte \u0142a to carte\u0142a de \u0142aoro
ShowLogfile=Mostra file de rejistro
SumMSTextDocuments=A venjar\u00E0 inport\u00E0i tuti i documenti de Word che i ze rento de \u0142a carte\u0142a drioman:
SumMSTableDocuments=A venjar\u00E0 inport\u00E0i tuti i documenti de Excel che i ze rento de \u0142a carte\u0142a drioman:
SumMSDrawDocuments=A venjar\u00E0 inport\u00E0i tuti i documenti de Excel che i ze rento de \u0142a carte\u0142a drioman:
SumMSTextTemplates=A venjar\u00E0 inport\u00E0i tuti i mode\u0142i de Word che i ze rento de \u0142a carte\u0142a drioman:
SumMSTableTemplates=A venjar\u00E0 inport\u00E0i tuti i mode\u0142i de Excel che i ze rento de \u0142a carte\u0142a drioman:
SumMSDrawTemplates=A venjar\u00E0 inport\u00E0i tuti i mode\u0142i de PowerPoint che i ze rento de \u0142a carte\u0142a drioman:
