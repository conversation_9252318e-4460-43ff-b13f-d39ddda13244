# turtle graphics

FORWARD=forward|fd
BACKWARD=back|bk
TURNLEFT=left|turnleft|lt
TURNRIGHT=right|turnright|rt
PENUP=penup|pu
PENDOWN=pendown|pd
HOME=\u0998\u09F0
POINT=\u09AC\u09BF\u09A8\u09CD\u09A6\u09C1
CIRCLE=\u09AC\u09C3\u09A4\u09CD\u09A4
ELLIPSE=\u0989\u09AA\u09AC\u09C3\u09A4\u09CD\u09A4
SQUARE=\u09AC\u09F0\u09CD\u0997
RECTANGLE=\u0986\u09DF\u09A4
LABEL=\u09B2\u09C7\u09AC\u09C7\u09B2
PENCOLOR=pencolor|pencolour|linecolor|pc
ANY=\u09AF\u09BF\u0995\u09CB\u09A8\u09CB
PENWIDTH=pensize|penwidth|linewidth|ps
PENSTYLE=penstyle|linestyle
PENJOINT=penjoint|linejoint
PENCAP=pencap|linecap
NONE=\u0995\u09CB\u09A8\u09CB \u09A8\u09B9\u09DF
BEVEL=bevel
MITER=miter
ROUNDED=\u0997\u09CB\u09B2\u09BE\u0995\u09BE\u09F0
SOLID=\u0995\u09A0\u09BF\u09A8
DASH=\u09A1\u09C7\u09B6\u09CD\u09AC\u09AF\u09C1\u0995\u09CD\u09A4
DOTTED=\u09A1\u099F\u09C7\u09A1
CLOSE=\u09AC\u09A8\u09CD\u09A7 \u0995\u09F0\u0995
FILL=\u09AA\u09C2\u09F0\u09CD\u09A3 \u0995\u09F0\u0995
FILLCOLOR=fillcolor|fillcolour|fc
FILLTRANSPARENCY=filltransparency
PENTRANSPARENCY=pentransparency|linetransparency
FILLSTYLE=fillstyle
FONTCOLOR=fontcolor|textcolor|textcolour
FONTTRANSPARENCY=fonttransparency|texttransparency
FONTHEIGHT=fontsize|textsize|textheight
FONTWEIGHT=fontweight
FONTSTYLE=fontstyle
BOLD=\u09A1\u09BE\u09A0
ITALIC=\u0987\u09A4\u09BE\u09B2\u09BF\u0995
UPRIGHT=upright|normal
NORMAL=\u09B8\u09BE\u09A7\u09BE\u09F0\u09A8
FONTFAMILY=fontfamily
CLEARSCREEN=clearscreen|cs
TEXT=\u09B2\u09BF\u0996\u09A8\u09C0
HIDETURTLE=hideturtle|ht|hideme
SHOWTURTLE=showturtle|st|showme
POSITION=position|pos|setpos
HEADING=heading|setheading|seth
PAGESIZE=pagesize
GROUP=picture|pic

# control structures

TO=\u09B2\u09C8
END=\u0985\u09A8\u09CD\u09A4
STOP=\u09AC\u09A8\u09CD\u09A7 \u0995\u09F0\u0995
REPEAT=repeat|forever
REPCOUNT=repcount
BREAK=break
CONTINUE=\u0985\u09AC\u09CD\u09AF\u09BE\u09B9\u09A4 \u09F0\u09BE\u0996\u0995
WHILE=while
FOR=\u0995\u09BE\u09F0\u09A3\u09C7
IN=\u0987\u09A8
IF=if
OUTPUT=\u0986\u0989\u099F\u09AA\u09C1\u099F
LEFTSTRING=\u201C|\u2018
RIGHTSTRING=\u201D|\u2019
TRUE=\u09B8\u0981\u099A\u09BE
FALSE=\u09AE\u09BF\u099B\u09BE
NOT=not
AND=and
OR=or
INPUT=input
PRINT=print
SLEEP=\u09A8\u09BF\u09A6\u09CD\u09F0\u09BE
GLOBAL=\u09AC\u09BF\u09B6\u09CD\u09AC\u09AC\u09CD\u09AF\u09BE\u09AA\u09C0

# functions
RANDOM=\u09AF\u09BE\u09A6\u09C3\u099A\u09CD\u099B\u09BF\u0995
INT=int
FLOAT=float
STR=str
SQRT=sqrt
LOG10=log10
SIN=sin
COS=cos
ROUND=\u0997\u09CB\u09B2\u09BE\u0995\u09BE\u09F0
ABS=abs
COUNT=\u0997\u09A3\u09A8\u09BE
SET=set
RANGE=\u09AC\u09BF\u09B8\u09CD\u09A4\u09BE\u09F0
LIST=list
TUPLE=tuple
SORTED=\u09AC\u09F0\u09CD\u0997\u09C0\u0995\u09F0\u09A3 \u0995\u09F0\u09BF \u09A5\u09CB\u09F1\u09BE
RESUB=sub
RESEARCH=\u09B8\u09A8\u09CD\u09A7\u09BE\u09A8 \u0995\u09F0\u0995
REFINDALL=findall
MIN=min
MAX=max

PI=pi|\u03C0

# measurement
DECIMAL=.
DEG=\u00B0
HOUR=h
MM=\u09AE\u09BF.\u09AE\u09BF.
CM=\u099B\u09C7.\u09AE\u09BF.
PT=pt
INCH=in|"

# color codes

INVISIBLE=\u0985\u09A6\u09C3\u09B6\u09CD\u09AF
BLACK=\u0995\u09B2\u09BE
SILVER=\u09F0\u09C2\u09AA
GRAY=gray|grey
WHITE=\u09AC\u0997\u09BE
MAROON=\u0995\u09C3\u09B7\u09CD\u09A3\u09F0\u0995\u09CD\u09A4\u09AC\u09F0\u09CD\u09A3
RED=\u09F0\u0999\u09BE
PURPLE=\u099C\u09BE\u09AE\u09C1\u0995\u09B2\u09C0\u09DF\u09BE
FUCHSIA=fuchsia|magenta
GREEN=\u09B8\u09C7\u0989\u099C\u09C0\u09DF\u09BE
LIME=\u09A8\u09C7\u09AE\u09C1
OLIVE=\u099C\u09B2\u09AB\u09BE\u0987
YELLOW=\u09B9\u09BE\u09B2\u09A7\u09C0\u09DF\u09BE
NAVY=\u0987\u09B7\u09CE\u09A8\u09C0\u09B2\u09BE
BLUE=\u09A8\u09C0\u09B2\u09BE
TEAL=\u099F\u09BF\u09B2
AQUA=aqua|cyan
PINK=\u0997\u09CB\u09B2\u09BE\u09AA\u09C0
TOMATO=\u099F\u09AE\u09C7\u099F\u09CB
ORANGE=\u09B8\u09C1\u09AE\u09A5\u09C0\u09F0\u09BE
GOLD=\u09B8\u09CB\u09A8
VIOLET=\u09AC\u09C7\u0999\u09C1\u09A8\u09C0\u09DF\u09BE
SKYBLUE=\u0986\u0995\u09BE\u09B6\u09A8\u09C0\u09B2\u09BE
CHOCOLATE=\u099A\u0995\u09CB\u09B2\u09C7\u099F
BROWN=\u09AE\u09C2\u0997\u09BE

# messages

LIBRELOGO=LibreLogo
ERROR=\u09A4\u09CD\u09F0\u09C1\u099F\u09BF (\u09B6\u09BE\u09F0\u09C0 %s \u09A4)
ERR_ZERODIVISION=\u09B6\u09C1\u09A3\u09CD\u09AF\u09F0\u09C7 \u09B9\u09F0\u09A3 \u0995\u09F0\u09BE \u0964
ERR_NAME=Unknown name: \u201C%s\u201D.
ERR_ARGUMENTS=%s \u098F %s \u09A4\u09F0\u09CD\u0995\u09B8\u09AE\u09C2\u09B9 \u0997\u09CD\u09F0\u09B9\u09A3 \u0995\u09F0\u09C7 (%s \u09A6\u09BF\u09DF\u09BE \u09B9\u09C8\u099B\u09C7)\u0964
ERR_BLOCK=\u09A4\u09CD\u09F0\u09C1\u099F\u09BF (\u09AC\u09CD\u09F0\u09C7\u0995\u09C7\u099F\u09B8\u09AE\u09C2\u09B9\u09A4 \u0985\u09A4\u09BF\u09F0\u09BF\u0995\u09CD\u09A4 \u0985\u09A5\u09AC\u09BE \u09B8\u09A8\u09CD\u09A7\u09BE\u09A8\u09B9\u09BF\u09A8 \u09B8\u09CD\u09AA\u09C7\u0987\u099A?)
ERR_KEY=\u0985\u099C\u09CD\u099E\u09BE\u09A4 \u0989\u09AA\u09BE\u09A6\u09BE\u09A8: %s
ERR_INDEX=\u09B8\u09C2\u099A\u09C0 \u09AC\u09BF\u09B8\u09CD\u09A4\u09BE\u09F0\u09F0 \u09AC\u09BE\u09B9\u09BF\u09F0\u0964

ERR_STOP=\u09AA\u09CD\u09F0\u0997\u09CD\u09F0\u09BE\u09AE \u0985\u09A8\u09CD\u09A4 \u0995\u09F0\u09BE \u09B9\u09B2:
ERR_MAXRECURSION=\u09B8\u09F0\u09CD\u09AC\u09BE\u09A7\u09BF\u0995 \u09F0\u09BF\u0995\u09BE\u09F0\u09CD\u099A\u09A8 \u0997\u09AD\u09BF\u09F0\u09A4\u09BE (%d) \u0985\u09A4\u09BF\u0995\u09CD\u09F0\u09AE \u09B9\u09C8\u099B\u09C7\u0964
ERR_MEMORY=\u09AA\u09F0\u09CD\u09AF\u09BE\u09AA\u09CD\u09A4 \u09AE\u09C7\u09AE\u09F0\u09BF \u09A8\u09BE\u0987\u0964
ERR_NOTAPROGRAM=\u0986\u09AA\u09C1\u09A8\u09BF \u098F\u0987 \u09B2\u09BF\u0996\u09A8\u09C0 \u09A6\u09B8\u09CD\u09A4\u09BE\u09AC\u09C7\u099C \u099A\u09B2\u09BE\u09AC \u09AC\u09BF\u099A\u09BE\u09F0\u09C7 \u09A8\u09C7?
