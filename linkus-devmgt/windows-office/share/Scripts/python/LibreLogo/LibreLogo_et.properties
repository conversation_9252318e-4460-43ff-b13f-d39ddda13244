# turtle graphics

FORWARD=edasi|e
BACKWARD=tagasi|t
TURNLEFT=vasakule|vasakp\u00F6\u00F6re|v
TURNRIGHT=paremale|paremp\u00F6\u00F6re|p
PENUP=pliiats_\u00FCles|p\u00FC
PENDOWN=pliiats_alla|pa
HOME=koju
POINT=punkt
CIRCLE=ring
ELLIPSE=ellips
SQUARE=ruut
RECTANGLE=ristk\u00FClik
LABEL=silt
PENCOLOR=pliiatsi_v\u00E4rv|joonev\u00E4rv|pv|jv
ANY=mistahes
PENWIDTH=pliiatsi_paksus|pliiatsi_j\u00E4medus|joonepaksus|joonelaius|jl
PENSTYLE=pliiatsi_stiil|joonestiil
PENJOINT=pliiatsi\u00FChendus|joone\u00FChendus
PENCAP=jooneots
NONE=puudub
BEVEL=faasitud
MITER=terav
ROUNDED=\u00FCmar
SOLID=pidev
DASH=kriipsudega
DOTTED=punktiir
CLOSE=sulge
FILL=t\u00E4ida
FILLCOLOR=t\u00E4itev\u00E4rv|tv
FILLTRANSPARENCY=t\u00E4ite_l\u00E4bipaistvus|tlp
PENTRANSPARENCY=pliiatsi_l\u00E4bipaistvus|joone_l\u00E4bipaistvus
FILLSTYLE=t\u00E4itestiil
FONTCOLOR=fondi_v\u00E4rv|teksti_v\u00E4rv
FONTTRANSPARENCY=fondi_l\u00E4bipaistvus|teksti_l\u00E4bipaistvus
FONTHEIGHT=fondi_suurus|teksti_suurus|teksti_k\u00F5rgus
FONTWEIGHT=fondi_paksus
FONTSTYLE=fondi_stiil
BOLD=paks|rasvane
ITALIC=kaldkiri|kursiiv
UPRIGHT=p\u00FCstine|tavaline
NORMAL=keskmine
FONTFAMILY=font|kirjat\u00FC\u00FCp|fondi_perekond
CLEARSCREEN=puhasta_ekraan|pe
TEXT=tekst
HIDETURTLE=peida|peida_kilpkonn|pk
SHOWTURTLE=n\u00E4ita|n\u00E4ita_kilpkonna|nk
POSITION=asukoht|koht|m\u00E4\u00E4ra_koht
HEADING=pealkiri|m\u00E4\u00E4ra_pealkiri
PAGESIZE=lehe_suurus
GROUP=pilt

# control structures

TO=funktsioon|f
END=l\u00F5pp
STOP=peata
REPEAT=korda|igavesti|l\u00F5pmatuseni
REPCOUNT=korduse_number
BREAK=katkesta
CONTINUE=j\u00E4tka
WHILE=kuniks
FOR=igale_elemendile
IN=hulgas
IF=kui
OUTPUT=v\u00E4ljund
LEFTSTRING=\u201E
RIGHTSTRING=\u201D|\u201C
TRUE=t\u00F5ene
FALSE=v\u00E4\u00E4r
NOT=pole|mitte|ei
AND=ja
OR=v\u00F5i
INPUT=sisend
PRINT=kirjuta|prindi
SLEEP=oota
GLOBAL=\u00FCldine

# functions
RANDOM=juhuslik
INT=t\u00E4isarv
FLOAT=ujukomaarv|ujukoma
STR=s\u00F5ne|string
SQRT=ruutjuur|rtjr|\u221A
LOG10=log10
SIN=sin
COS=cos
ROUND=\u00FCmar
ABS=absoluutv\u00E4\u00E4rtus|abs
COUNT=loenda
SET=hulk
RANGE=vahemik
LIST=loend
TUPLE=ennik|kortee\u017E
SORTED=sorditud
RESUB=asenda
RESEARCH=otsi
REFINDALL=leia_k\u00F5ik
MIN=min
MAX=max|maks

PI=pii|\u03C0

# measurement
DECIMAL=,
DEG=\u00B0
HOUR=t
MM=mm
CM=cm
PT=pt
INCH=tolli|"|\u2033

# color codes

INVISIBLE=n\u00E4htamatu
BLACK=must
SILVER=h\u00F5bedane
GRAY=hall
WHITE=valge
MAROON=kastanpruun
RED=punane
PURPLE=lilla
FUCHSIA=magenta|fuksiapunane
GREEN=roheline
LIME=laimiroheline|laimikarva
OLIVE=kollakasroheline|oliivroheline|oliivikarva
YELLOW=kollane
NAVY=meresinine
BLUE=sinine
TEAL=sinakasroheline
AQUA=rohekassinine|ts\u00FCaan
PINK=roosa
TOMATO=tomatipunane|tomatikarva
ORANGE=oran\u017E|apelsinikarva
GOLD=kuldne
VIOLET=violetne
SKYBLUE=taevasinine
CHOCOLATE=\u0161okolaadipruun|\u0161okolaadikarva
BROWN=pruun

# messages

LIBRELOGO=LibreLogo
ERROR=Viga (real %s)
ERR_ZERODIVISION=Nulliga jagamine.
ERR_NAME=Tundmatu nimi: \u201E%s\u201D.
ERR_ARGUMENTS=%s v\u00F5tab %s argumenti (aga anti %s).
ERR_BLOCK=Viga (liiga palju v\u00F5i v\u00E4he t\u00FChikuid sulgude juures?)
ERR_KEY=Tundmatu element: %s
ERR_INDEX=Indeks vahemikust v\u00E4ljas.

ERR_STOP=Programm l\u00F5petatud:
ERR_MAXRECURSION=\u00FCletati suurim rekursioonis\u00FCgavus (%d).
ERR_MEMORY=pole piisavalt m\u00E4lu.
ERR_NOTAPROGRAM=Kas soovid seda tekstidokumenti k\u00E4ivitada?
