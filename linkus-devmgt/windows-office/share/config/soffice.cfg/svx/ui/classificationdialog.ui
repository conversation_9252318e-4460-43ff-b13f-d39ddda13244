<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="svx">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkImage" id="image1">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="icon_name">cmd/lc_bold.png</property>
  </object>
  <object class="GtkTreeStore" id="liststore1">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
    </columns>
  </object>
  <object class="GtkTreeStore" id="liststore2">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
    </columns>
  </object>
  <object class="GtkTreeStore" id="liststore3">
    <columns>
      <!-- column-name text -->
      <column type="gchararray"/>
      <!-- column-name id -->
      <column type="gchararray"/>
    </columns>
  </object>
  <object class="GtkDialog" id="AdvancedDocumentClassificationDialog">
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="title" translatable="yes" context="classificationdialog|dialogname">Classification</property>
    <property name="modal">True</property>
    <property name="default_width">0</property>
    <property name="default_height">0</property>
    <property name="type_hint">dialog</property>
    <child>
      <placeholder/>
    </child>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can_focus">False</property>
            <property name="layout_style">end</property>
            <child>
              <object class="GtkButton" id="ok">
                <property name="label">gtk-ok</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="can_default">True</property>
                <property name="has_default">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="cancel">
                <property name="label">gtk-cancel</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
                <property name="secondary">True</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <!-- n-columns=1 n-rows=1 -->
          <object class="GtkGrid">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="row_spacing">6</property>
            <property name="column_spacing">6</property>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="row_spacing">6</property>
                <property name="column_spacing">6</property>
                <child>
                  <object class="GtkLabel" id="classificationLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-Classification">Classification:</property>
                    <property name="xalign">0</property>
                    <accessibility>
                      <relation type="label-for" target="classificationCB"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="internationalClassificationLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-InternationalClassification">International:</property>
                    <property name="xalign">0</property>
                    <accessibility>
                      <relation type="label-for" target="internationalClassificationCB"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="classificationCB">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="hexpand">True</property>
                    <accessibility>
                      <relation type="labelled-by" target="classificationLabel"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="internationalClassificationCB">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <accessibility>
                      <relation type="labelled-by" target="internationalClassificationLabel"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="markingLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-Marking">Marking:</property>
                    <property name="xalign">0</property>
                    <accessibility>
                      <relation type="label-for" target="markingLB"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">3</property>
                    <property name="width">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkLabel" id="recentlyUsedLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-Classification">Recently Used:</property>
                    <property name="xalign">0</property>
                    <accessibility>
                      <relation type="label-for" target="recentlyUsedCB"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkComboBoxText" id="recentlyUsedCB">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="hexpand">True</property>
                    <accessibility>
                      <relation type="labelled-by" target="recentlyUsedLabel"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">1</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkScrolledWindow">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="shadow_type">in</property>
                    <child>
                      <object class="GtkTreeView" id="markingLB">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                        <property name="model">liststore1</property>
                        <property name="headers_visible">False</property>
                        <property name="headers_clickable">False</property>
                        <property name="show_expanders">False</property>
                        <child internal-child="selection">
                          <object class="GtkTreeSelection"/>
                        </child>
                        <child>
                          <object class="GtkTreeViewColumn" id="treeviewcolumn3">
                            <property name="resizable">True</property>
                            <property name="spacing">6</property>
                            <child>
                              <object class="GtkCellRendererText" id="cellrenderer1"/>
                              <attributes>
                                <attribute name="text">0</attribute>
                              </attributes>
                            </child>
                          </object>
                        </child>
                        <accessibility>
                          <relation type="labelled-by" target="markingLabel"/>
                        </accessibility>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">4</property>
                    <property name="width">2</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="left_attach">1</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <!-- n-columns=1 n-rows=1 -->
              <object class="GtkGrid">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="row_spacing">3</property>
                <child>
                  <object class="GtkLabel" id="classificationContentLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-Content">Content</property>
                    <property name="xalign">0</property>
                    <property name="yalign">0</property>
                    <accessibility>
                      <relation type="label-for" target="classificationEditWindow"/>
                    </accessibility>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <child>
                      <object class="GtkToggleButton" id="toolbox">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="receives_default">False</property>
                        <property name="tooltip_text" translatable="yes" context="classificationdialog|boldButton">Bold</property>
                        <property name="image">image1</property>
                        <property name="use_underline">True</property>
                        <property name="always_show_image">True</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkButton" id="signButton">
                        <property name="label" translatable="yes" context="classificationdialog|signButton">Sign Paragraph</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">True</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkScrolledWindow">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="border_width">0</property>
                    <property name="shadow_type">in</property>
                    <child>
                      <object class="GtkViewport">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <child>
                          <object class="GtkDrawingArea" id="classificationEditWindow">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="events">GDK_BUTTON_MOTION_MASK | GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_FOCUS_CHANGE_MASK | GDK_STRUCTURE_MASK</property>
                            <property name="hexpand">True</property>
                            <accessibility>
                              <relation type="labelled-by" target="classificationContentLabel"/>
                            </accessibility>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="left_attach">0</property>
                    <property name="top_attach">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkExpander" id="intellectualPropertyExpander">
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="hexpand">True</property>
                <property name="resize_toplevel">True</property>
                <child>
                  <!-- n-columns=1 n-rows=1 -->
                  <object class="GtkGrid">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="row_spacing">6</property>
                    <property name="column_spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="intellectualPropertyPartLabel">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="classificationdialog|label-PartNumber">License:</property>
                        <property name="xalign">0</property>
                        <accessibility>
                          <relation type="label-for" target="intellectualPropertyPartLB"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="intellectualPropertyPartNumberLabel">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="classificationdialog|label-PartNumber">Part Number:</property>
                        <property name="xalign">0</property>
                        <accessibility>
                          <relation type="label-for" target="intellectualPropertyPartNumberLB"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">0</property>
                        <property name="width">2</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="intellectualPropertyPartEntryLabel">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="classificationdialog|label-PartNumber">Part text:</property>
                        <property name="xalign">0</property>
                        <accessibility>
                          <relation type="label-for" target="intellectualPropertyPartEntry"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">2</property>
                        <property name="width">2</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkEntry" id="intellectualPropertyPartEntry">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="activates_default">True</property>
                        <property name="truncate-multiline">True</property>
                        <accessibility>
                          <relation type="labelled-by" target="intellectualPropertyPartEntryLabel"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">3</property>
                        <property name="width">2</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkScrolledWindow">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="shadow_type">in</property>
                        <child>
                          <object class="GtkTreeView" id="intellectualPropertyPartLB">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="model">liststore3</property>
                            <property name="headers_visible">False</property>
                            <property name="headers_clickable">False</property>
                            <property name="show_expanders">False</property>
                            <child internal-child="selection">
                              <object class="GtkTreeSelection"/>
                            </child>
                            <child>
                              <object class="GtkTreeViewColumn" id="treeviewcolumn6">
                                <property name="resizable">True</property>
                                <property name="spacing">6</property>
                                <child>
                                  <object class="GtkCellRendererText" id="cellrenderer6"/>
                                  <attributes>
                                    <attribute name="text">0</attribute>
                                  </attributes>
                                </child>
                              </object>
                            </child>
                            <accessibility>
                              <relation type="labelled-by" target="intellectualPropertyPartLabel"/>
                            </accessibility>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkScrolledWindow">
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="shadow_type">in</property>
                        <child>
                          <object class="GtkTreeView" id="intellectualPropertyPartNumberLB">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="model">liststore2</property>
                            <property name="headers_visible">False</property>
                            <property name="headers_clickable">False</property>
                            <property name="show_expanders">False</property>
                            <child internal-child="selection">
                              <object class="GtkTreeSelection"/>
                            </child>
                            <child>
                              <object class="GtkTreeViewColumn" id="treeviewcolumn4">
                                <property name="resizable">True</property>
                                <property name="spacing">6</property>
                                <child>
                                  <object class="GtkCellRendererText" id="cellrenderer5"/>
                                  <attributes>
                                    <attribute name="text">0</attribute>
                                  </attributes>
                                </child>
                              </object>
                            </child>
                            <accessibility>
                              <relation type="labelled-by" target="intellectualPropertyPartNumberLabel"/>
                            </accessibility>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">1</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkButton" id="intellectualPropertyPartAddButton">
                        <property name="label" translatable="yes" context="classificationdialog|intellectualPropertyPartAddButton">Add</property>
                        <property name="visible">True</property>
                        <property name="can_focus">True</property>
                        <property name="receives_default">True</property>
                        <property name="halign">center</property>
                        <property name="valign">center</property>
                      </object>
                      <packing>
                        <property name="left_attach">2</property>
                        <property name="top_attach">3</property>
                      </packing>
                    </child>
                    <child>
                      <placeholder/>
                    </child>
                    <child>
                      <placeholder/>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="classificationdialog|label-IntellectualProperty">Intellectual Property</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">1</property>
                <property name="width">2</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="-5">ok</action-widget>
      <action-widget response="-6">cancel</action-widget>
      <action-widget response="-11">help</action-widget>
    </action-widgets>
  </object>
</interface>
