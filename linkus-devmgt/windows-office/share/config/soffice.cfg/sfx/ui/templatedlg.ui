<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.38.1 -->
<interface domain="sfx">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkMenu" id="contextmenu1">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
  </object>
  <object class="GtkMenu" id="contextmenu2">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
  </object>
  <object class="GtkImage" id="image1">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <property name="icon-name">sfx2/res/actiontemplates020.png</property>
  </object>
  <object class="GtkImage" id="image3">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <property name="icon-name">sfx2/res/actiontemplates017.png</property>
  </object>
  <object class="GtkImage" id="image4">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <property name="icon-name">sfx2/res/actionview010.png</property>
  </object>
  <object class="GtkImage" id="image5">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <property name="icon-name">cmd/lc_additionsdialog.png</property>
  </object>
  <object class="GtkImage" id="image7">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <property name="icon-name">sfx2/res/actionaction013.png</property>
  </object>
  <object class="GtkMenu" id="menu1">
    <property name="visible">True</property>
    <property name="can-focus">False</property>
    <child>
      <object class="GtkMenuItem" id="default">
        <property name="visible">True</property>
        <property name="can-focus">False</property>
        <property name="label" translatable="yes" context="colsmenu|insert">Reset Default Template</property>
        <property name="use-underline">True</property>
        <child type="submenu">
          <object class="GtkMenu" id="submenu">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
          </object>
        </child>
      </object>
    </child>
  </object>
  <object class="GtkDialog" id="TemplateDialog">
    <property name="width-request">740</property>
    <property name="height-request">500</property>
    <property name="can-focus">False</property>
    <property name="border-width">6</property>
    <property name="title" translatable="yes" context="templatedlg|TemplateDialog">Templates</property>
    <property name="modal">True</property>
    <property name="default-width">0</property>
    <property name="default-height">0</property>
    <property name="type-hint">normal</property>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can-focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">2</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can-focus">False</property>
            <property name="layout-style">end</property>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">True</property>
                <property name="use-stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
                <property name="secondary">True</property>
              </packing>
            </child>
            <child>
              <object class="GtkCheckButton" id="hidedialogcb">
                <property name="label" translatable="yes" context="templatedlg|hidedialogcb">Show this dialog at startup</property>
                <property name="can-focus">True</property>
                <property name="receives-default">False</property>
                <property name="use-underline">True</property>
                <property name="xalign">0</property>
                <property name="draw-indicator">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
                <property name="secondary">True</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="ok">
                <property name="label">gtk-ok</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="can-default">True</property>
                <property name="has-default">True</property>
                <property name="receives-default">True</property>
                <property name="use-stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="cancel">
                <property name="label">gtk-cancel</property>
                <property name="visible">True</property>
                <property name="can-focus">True</property>
                <property name="receives-default">True</property>
                <property name="use-stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">3</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack-type">end</property>
            <property name="position">1</property>
          </packing>
        </child>
        <child>
          <object class="GtkBox" id="box1">
            <property name="visible">True</property>
            <property name="can-focus">False</property>
            <property name="orientation">vertical</property>
            <property name="spacing">6</property>
            <child>
              <object class="GtkBox" id="filter_box">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="spacing">12</property>
                <child>
                  <object class="GtkEntry" id="search_filter">
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|search_filter|tooltip_text">Search</property>
                    <property name="activates-default">True</property>
                    <property name="truncate-multiline">True</property>
                    <property name="placeholder-text" translatable="yes" context="templatedlg|search_filter">Search...</property>
                  </object>
                  <packing>
                    <property name="expand">True</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkBox" id="box5">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="spacing">12</property>
                    <child>
                      <object class="GtkBox" id="box6">
                        <property name="visible">True</property>
                        <property name="can-focus">False</property>
                        <property name="spacing">12</property>
                        <child>
                          <object class="GtkComboBoxText" id="filter_application">
                            <property name="visible">True</property>
                            <property name="can-focus">False</property>
                            <property name="tooltip-text" translatable="yes" context="templatedlg|filter_application|tooltip_text">Filter by Application</property>
                            <property name="resize-mode">queue</property>
                            <items>
                              <item id="0" translatable="yes" context="templatedlg|applist">All Applications</item>
                              <item id="0" translatable="yes" context="templatedlg|applist">Documents</item>
                              <item id="0" translatable="yes" context="templatedlg|applist">Spreadsheets</item>
                              <item id="0" translatable="yes" context="templatedlg|applist">Presentations</item>
                              <item id="0" translatable="yes" context="templatedlg|applist">Drawings</item>
                            </items>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">False</property>
                            <property name="position">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkComboBoxText" id="filter_folder">
                            <property name="width-request">250</property>
                            <property name="visible">True</property>
                            <property name="can-focus">False</property>
                            <property name="tooltip-text" translatable="yes" context="templatedlg|filter_folder|tooltip_text">Filter by Category</property>
                            <items>
                              <item id="0" translatable="yes" context="templatedlg|folderlist">All Categories</item>
                            </items>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">1</property>
                          </packing>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="pack-type">end</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkLabel" id="label1">
                        <property name="visible">True</property>
                        <property name="can-focus">False</property>
                        <property name="label" translatable="yes" context="templatedlg|label1">Filter</property>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="pack-type">end</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="pack-type">end</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkFrame" id="thumbnailviewframe">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="label-xalign">0</property>
                <property name="shadow-type">none</property>
                <child>
                  <object class="GtkBox" id="thumbnailview_box">
                    <property name="visible">True</property>
                    <property name="can-focus">False</property>
                    <property name="hexpand">True</property>
                    <property name="vexpand">True</property>
                    <property name="orientation">vertical</property>
                    <property name="spacing">6</property>
                    <child>
                      <object class="GtkScrolledWindow" id="scrolllocal">
                        <property name="can-focus">True</property>
                        <property name="no-show-all">True</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                        <property name="shadow-type">in</property>
                        <child>
                          <object class="GtkViewport">
                            <property name="visible">True</property>
                            <property name="can-focus">False</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <child>
                              <object class="GtkDrawingArea" id="template_view">
                                <property name="can-focus">True</property>
                                <property name="events">GDK_BUTTON_MOTION_MASK | GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                                <property name="no-show-all">True</property>
                                <property name="hexpand">True</property>
                                <property name="vexpand">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkScrolledWindow" id="scrollsearch">
                        <property name="can-focus">True</property>
                        <property name="no-show-all">True</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                        <property name="shadow-type">in</property>
                        <child>
                          <object class="GtkViewport">
                            <property name="visible">True</property>
                            <property name="can-focus">False</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <child>
                              <object class="GtkDrawingArea" id="search_view">
                                <property name="can-focus">True</property>
                                <property name="events">GDK_BUTTON_MOTION_MASK | GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                                <property name="no-show-all">True</property>
                                <property name="hexpand">True</property>
                                <property name="vexpand">True</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                      <packing>
                        <property name="expand">False</property>
                        <property name="fill">True</property>
                        <property name="position">1</property>
                      </packing>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="thumbnailviewlabel">
                    <property name="can-focus">False</property>
                    <property name="label" translatable="yes" context="templatedlg|thumbnailviewlabel">Template List</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkBox" id="box2">
                <property name="visible">True</property>
                <property name="can-focus">False</property>
                <child>
                  <object class="GtkMenuButton" id="action_menu">
                    <property name="can-focus">True</property>
                    <property name="receives-default">False</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|action_menu|tooltip_text">Settings</property>
                    <property name="image">image7</property>
                    <property name="relief">none</property>
                    <property name="always-show-image">True</property>
                    <property name="popup">menu1</property>
                    <property name="use-popover">False</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="btnMoreTemplates">
                    <property name="label" translatable="yes" context="templatedlg|extensions_btn">_Extensions</property>
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="receives-default">True</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|online_link|tooltip_text">Add more templates via extension</property>
                    <property name="image">image5</property>
                    <property name="relief">none</property>
                    <property name="use-underline">True</property>
                    <property name="always-show-image">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="pack-type">end</property>
                    <property name="position">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="import_btn">
                    <property name="label" translatable="yes" context="templatedlg|import_btn">Import</property>
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="receives-default">True</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|import_btn|tooltip_text">Import Templates</property>
                    <property name="image">image4</property>
                    <property name="relief">none</property>
                    <property name="always-show-image">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="pack-type">end</property>
                    <property name="position">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="export_btn">
                    <property name="label" translatable="yes" context="templatedlg|export_btn">Export</property>
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="receives-default">True</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|export_btn|tooltip_text">Export Templates</property>
                    <property name="image">image1</property>
                    <property name="relief">none</property>
                    <property name="always-show-image">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="pack-type">end</property>
                    <property name="position">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="move_btn">
                    <property name="label" translatable="yes" context="templatedlg|move_btn">Move</property>
                    <property name="visible">True</property>
                    <property name="can-focus">True</property>
                    <property name="receives-default">True</property>
                    <property name="tooltip-text" translatable="yes" context="templatedlg|move_btn|tooltip_text">Move Templates</property>
                    <property name="image">image3</property>
                    <property name="relief">none</property>
                    <property name="always-show-image">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="pack-type">end</property>
                    <property name="position">4</property>
                  </packing>
                </child>
                <child>
                  <placeholder/>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="pack-type">end</property>
                <property name="position">3</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="-11">help</action-widget>
      <action-widget response="-5">ok</action-widget>
      <action-widget response="-6">cancel</action-widget>
    </action-widgets>
  </object>
</interface>
