<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.2 -->
<interface domain="cui">
  <requires lib="gtk+" version="3.20"/>
  <!-- n-columns=1 n-rows=1 -->
  <object class="GtkGrid" id="GalleryGeneralPage">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="hexpand">True</property>
    <property name="vexpand">True</property>
    <property name="border_width">6</property>
    <property name="row_spacing">24</property>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid1">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="column_spacing">12</property>
        <child>
          <object class="GtkLabel" id="label1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="gallerygeneralpage|label1">Modified:</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="modified">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">2</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid2">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="row_spacing">6</property>
        <property name="column_spacing">12</property>
        <child>
          <object class="GtkLabel" id="label2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="gallerygeneralpage|label2">Type:</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="type">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="label3">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="gallerygeneralpage|label3">Location:</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">1</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="label4">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="gallerygeneralpage|label4">Contents:</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">2</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="location">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">1</property>
          </packing>
        </child>
        <child>
          <object class="GtkLabel" id="contents">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="xalign">0</property>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">2</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">1</property>
      </packing>
    </child>
    <child>
      <!-- n-columns=1 n-rows=1 -->
      <object class="GtkGrid" id="grid3">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="hexpand">True</property>
        <property name="column_spacing">12</property>
        <child>
          <object class="GtkBox" id="imagecontainer">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="orientation">vertical</property>
            <child>
              <object class="GtkImage" id="image">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="halign">center</property>
                <property name="valign">center</property>
                <accessibility>
                  <relation type="label-for" target="name"/>
                </accessibility>
                <child internal-child="accessible">
                  <object class="AtkObject" id="image-atkobject">
                    <property name="AtkObject::accessible-name" translatable="yes" context="gallerygeneralpage|image-atkobject">Theme Name</property>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">True</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="left_attach">0</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkEntry" id="name">
            <property name="visible">True</property>
            <property name="can_focus">True</property>
            <property name="valign">center</property>
            <property name="hexpand">True</property>
            <property name="activates_default">True</property>
            <property name="truncate-multiline">True</property>
            <accessibility>
              <relation type="labelled-by" target="image"/>
            </accessibility>
          </object>
          <packing>
            <property name="left_attach">1</property>
            <property name="top_attach">0</property>
          </packing>
        </child>
      </object>
      <packing>
        <property name="left_attach">0</property>
        <property name="top_attach">0</property>
      </packing>
    </child>
  </object>
  <object class="GtkSizeGroup" id="sizegroup1">
    <widgets>
      <widget name="label1"/>
      <widget name="label2"/>
      <widget name="label3"/>
      <widget name="label4"/>
      <widget name="imagecontainer"/>
    </widgets>
  </object>
</interface>
