<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="cui">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkAdjustment" id="adjustmentDistance">
    <property name="upper">999</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustmentPercent">
    <property name="upper">100</property>
    <property name="step_increment">5</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustmentPoint">
    <property name="upper">150</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkBox" id="ShadowTabPage">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="spacing">24</property>
    <child>
      <object class="GtkFrame" id="property">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <object class="GtkBox" id="maingrid">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="orientation">vertical</property>
                <property name="spacing">3</property>
                <child>
                  <object class="GtkCheckButton" id="TSB_SHOW_SHADOW">
                    <property name="label" translatable="yes" context="shadowtabpage|TSB_SHOW_SHADOW">_Use shadow</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">False</property>
                    <property name="use_underline">True</property>
                    <property name="xalign">0</property>
                    <property name="inconsistent">True</property>
                    <property name="draw_indicator">True</property>
                    <child internal-child="accessible">
                      <object class="AtkObject" id="TSB_SHOW_SHADOW-atkobject">
                        <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|TSB_SHOW_SHADOW">Adds a shadow to the selected drawing object.</property>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkAlignment">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="left_padding">18</property>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="gridSHADOW">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="row_spacing">4</property>
                        <property name="column_spacing">6</property>
                        <child>
                          <object class="GtkSpinButton" id="MTR_SHADOW_TRANSPARENT">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="halign">start</property>
                            <property name="activates_default">True</property>
                            <property name="adjustment">adjustmentPercent</property>
                            <property name="truncate-multiline">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="MTR_SHADOW_TRANSPARENT-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|MTR_SHADOW_TRANSPARENT">Enter a percentage from 0% (opaque) to 100% (transparent) to specify the transparency of the shadow.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">4</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkSpinButton" id="LB_SHADOW_BLUR">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="halign">start</property>
                            <property name="activates_default">True</property>
                            <property name="truncate-multiline">True</property>
                            <property name="adjustment">adjustmentPoint</property>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">3</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkSpinButton" id="MTR_FLD_DISTANCE">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="halign">start</property>
                            <property name="activates_default">True</property>
                            <property name="adjustment">adjustmentDistance</property>
                            <property name="truncate-multiline">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="MTR_FLD_DISTANCE-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|MTR_FLD_DISTANCE">Enter the distance that you want the shadow to be offset from the selected object.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">2</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkScrolledWindow">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hscrollbar_policy">never</property>
                            <property name="vscrollbar_policy">never</property>
                            <property name="shadow_type">in</property>
                            <child>
                              <object class="GtkViewport">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <child>
                                  <object class="GtkDrawingArea" id="CTL_POSITION">
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="CTL_POSITION-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|CTL_POSITION">Click where you want to cast the shadow.</property>
                                      </object>
                                    </child>
                                  </object>
                                </child>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkMenuButton" id="LB_SHADOW_COLOR">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <property name="label" translatable="no"></property>
                            <child>
                              <placeholder/>
                            </child>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="LB_SHADOW_COLOR-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|LB_SHADOW_COLOR">Select a color for the shadow.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="FT_DISTANCE">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="shadowtabpage|FT_DISTANCE">_Distance:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">MTR_FLD_DISTANCE</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">2</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="FT_SHADOW_COLOR">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="shadowtabpage|FT_SHADOW_COLOR">_Color:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">LB_SHADOW_COLOR</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="FT_SHADOW_BLUR">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="shadowtabpage|FT_SHADOW_BLUR">_Blur:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">LB_SHADOW_BLUR</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">3</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="FT_TRANSPARENT">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="shadowtabpage|FT_TRANSPARENT">_Transparency:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">MTR_SHADOW_TRANSPARENT</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">4</property>
                          </packing>
                        </child>
                        <child>
                          <placeholder/>
                        </child>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="shadowtabpage|label">Properties</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">0</property>
      </packing>
    </child>
    <child>
      <object class="GtkFrame" id="preview">
        <property name="visible">True</property>
        <property name="can_focus">False</property>
        <property name="label_xalign">0</property>
        <property name="shadow_type">none</property>
        <child>
          <object class="GtkAlignment" id="alignment2">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="top_padding">6</property>
            <property name="left_padding">12</property>
            <child>
              <object class="GtkBox" id="box3">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="orientation">vertical</property>
                <property name="homogeneous">True</property>
                <child>
                  <object class="GtkScrolledWindow">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="hscrollbar_policy">never</property>
                    <property name="vscrollbar_policy">never</property>
                    <property name="shadow_type">in</property>
                    <child>
                      <object class="GtkViewport">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <child>
                          <object class="GtkDrawingArea" id="CTL_COLOR_PREVIEW">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="CTL_COLOR_PREVIEW-atkobject">
                                <property name="AtkObject::accessible-name" translatable="yes" context="shadowtabpage|CTL_COLOR_PREVIEW-atkobject">Example</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child type="label">
          <object class="GtkLabel" id="label1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="label" translatable="yes" context="shadowtabpage|label">Preview</property>
            <attributes>
              <attribute name="weight" value="bold"/>
            </attributes>
          </object>
        </child>
      </object>
      <packing>
        <property name="expand">False</property>
        <property name="fill">True</property>
        <property name="position">1</property>
      </packing>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="ShadowTabPage-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="shadowtabpage|extended_tip|ShadowTabPage">Add a shadow to the selected drawing object, and define the properties of the shadow.</property>
      </object>
    </child>
  </object>
  <object class="GtkSizeGroup" id="sizegroup1">
    <widgets>
      <widget name="MTR_SHADOW_TRANSPARENT"/>
      <widget name="MTR_FLD_DISTANCE"/>
      <widget name="LB_SHADOW_COLOR"/>
      <widget name="LB_SHADOW_BLUR"/>
    </widgets>
  </object>
  <object class="GtkSizeGroup" id="sizegroup2">
    <property name="mode">vertical</property>
    <widgets>
      <widget name="alignment1"/>
      <widget name="alignment2"/>
    </widgets>
  </object>
</interface>
