<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="sc">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkDialog" id="DataFormDialog">
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="title" translatable="yes" context="dataform|DataFormDialog">Data Form</property>
    <property name="type_hint">dialog</property>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can_focus">False</property>
            <property name="homogeneous">True</property>
            <property name="layout_style">end</property>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">6</property>
                <property name="secondary">True</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="close">
                <property name="label" translatable="yes" context="dataform|close">_Close</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_underline">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">7</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <!-- n-columns=1 n-rows=1 -->
          <object class="GtkGrid">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="column_spacing">12</property>
            <child>
              <object class="GtkScrolledWindow" id="scrollbar">
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="hscrollbar_policy">never</property>
                <property name="vscrollbar_policy">always</property>
                <property name="shadow_type">in</property>
                <child>
                  <object class="GtkViewport">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <child>
                      <object class="GtkBox" id="box1">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="hexpand">True</property>
                        <property name="vexpand">True</property>
                        <property name="spacing">6</property>
                        <child>
                          <object class="GtkBox" id="box2">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="margin_left">6</property>
                            <property name="margin_right">6</property>
                            <property name="margin_top">6</property>
                            <property name="margin_bottom">6</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="orientation">vertical</property>
                            <child>
                              <!-- n-columns=1 n-rows=1 -->
                              <object class="GtkGrid" id="grid">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <property name="hexpand">True</property>
                                <property name="row_spacing">6</property>
                                <property name="column_spacing">12</property>
                                <property name="row_homogeneous">True</property>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                                <child>
                                  <placeholder/>
                                </child>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">0</property>
                              </packing>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">0</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
              <packing>
                <property name="left_attach">0</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButtonBox">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="orientation">vertical</property>
                <property name="spacing">6</property>
                <property name="homogeneous">True</property>
                <property name="layout_style">start</property>
                <child>
                  <object class="GtkLabel" id="label">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="dataform|label">New Record</property>
                    <property name="xalign">0</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="new">
                    <property name="label" translatable="yes" context="dataform|new">_New</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="can_default">True</property>
                    <property name="has_default">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="delete">
                    <property name="label" translatable="yes" context="dataform|delete">_Delete</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">2</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="restore">
                    <property name="label" translatable="yes" context="dataform|restore">_Restore</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">3</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="prev">
                    <property name="label" translatable="yes" context="dataform|prev">_Previous Record</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">4</property>
                    <property name="secondary">True</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkButton" id="next">
                    <property name="label" translatable="yes" context="dataform|next">Ne_xt Record</property>
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="receives_default">True</property>
                    <property name="use_underline">True</property>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">5</property>
                    <property name="secondary">True</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="left_attach">1</property>
                <property name="top_attach">0</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="-11">help</action-widget>
      <action-widget response="-7">close</action-widget>
    </action-widgets>
    <child type="titlebar">
      <placeholder/>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="DataFormDialog-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="dataform|extended_tip|DataFormDialog">Data Entry Form is a tool to make table data entry easy in spreadsheets.</property>
      </object>
    </child>
  </object>
  <object class="GtkSizeGroup" id="sizegroup">
    <widgets>
      <widget name="label"/>
      <widget name="new"/>
      <widget name="delete"/>
      <widget name="restore"/>
      <widget name="prev"/>
      <widget name="next"/>
    </widgets>
  </object>
</interface>
