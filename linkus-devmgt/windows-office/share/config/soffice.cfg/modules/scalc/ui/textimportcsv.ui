<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.36.0 -->
<interface domain="sc">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="lower">1</property>
    <property name="upper">4294967295</property>
    <property name="value">1</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment2">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkAdjustment" id="adjustment3">
    <property name="upper">100</property>
    <property name="step_increment">1</property>
    <property name="page_increment">10</property>
  </object>
  <object class="GtkDialog" id="TextImportCsvDialog">
    <property name="can_focus">False</property>
    <property name="border_width">6</property>
    <property name="title" translatable="yes" context="textimportcsv|TextImportCsvDialog">Text Import</property>
    <property name="modal">True</property>
    <property name="default_width">0</property>
    <property name="default_height">0</property>
    <property name="type_hint">dialog</property>
    <child internal-child="vbox">
      <object class="GtkBox" id="dialog-vbox1">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">12</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox" id="dialog-action_area1">
            <property name="can_focus">False</property>
            <property name="layout_style">end</property>
            <child>
              <object class="GtkButton" id="ok">
                <property name="label">gtk-ok</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="can_default">True</property>
                <property name="has_default">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="cancel">
                <property name="label">gtk-cancel</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkButton" id="help">
                <property name="label">gtk-help</property>
                <property name="visible">True</property>
                <property name="can_focus">True</property>
                <property name="receives_default">True</property>
                <property name="use_stock">True</property>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">2</property>
                <property name="secondary">True</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="pack_type">end</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkBox" id="box1">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="orientation">vertical</property>
            <property name="spacing">12</property>
            <child>
              <object class="GtkFrame" id="frame1">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment1">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <!-- n-columns=1 n-rows=1 -->
                      <object class="GtkGrid" id="grid1">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="row_spacing">6</property>
                        <property name="column_spacing">12</property>
                        <child>
                          <object class="GtkLabel" id="textcharset">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="textimportcsv|textcharset">Ch_aracter set:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">charset</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="textlanguage">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="textimportcsv|textlanguage">_Language:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">language</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="textfromrow">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="label" translatable="yes" context="textimportcsv|textfromrow">From ro_w:</property>
                            <property name="use_underline">True</property>
                            <property name="mnemonic_widget">fromrow</property>
                            <property name="xalign">0</property>
                          </object>
                          <packing>
                            <property name="left_attach">0</property>
                            <property name="top_attach">2</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkComboBoxText" id="charset">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="charset-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|charset">Specifies the character set to be used in the imported file.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkComboBoxText" id="language">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="language-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|language">Determines how the number strings are imported.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkSpinButton" id="fromrow">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="halign">start</property>
                            <property name="activates_default">True</property>
                            <property name="adjustment">adjustment1</property>
                            <property name="truncate-multiline">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="fromrow-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|fromrow">Specifies the row where you want to start the import.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="left_attach">1</property>
                            <property name="top_attach">2</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label1">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportcsv|label1">Import</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkFrame" id="frame2">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment2">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="hexpand">True</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <object class="GtkBox" id="box2">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="hexpand">True</property>
                        <property name="orientation">vertical</property>
                        <property name="spacing">6</property>
                        <child>
                          <object class="GtkBox" id="box6">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="hexpand">True</property>
                            <property name="homogeneous">True</property>
                            <child>
                              <object class="GtkRadioButton" id="tofixedwidth">
                                <property name="label" translatable="yes" context="textimportcsv|tofixedwidth">_Fixed width</property>
                                <property name="visible">True</property>
                                <property name="can_focus">True</property>
                                <property name="receives_default">False</property>
                                <property name="use_underline">True</property>
                                <property name="xalign">0</property>
                                <property name="draw_indicator">True</property>
                                <property name="group">toseparatedby</property>
                                <child internal-child="accessible">
                                  <object class="AtkObject" id="tofixedwidth-atkobject">
                                    <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|tofixedwidth">Separates fixed-width data (equal number of characters) into columns.</property>
                                  </object>
                                </child>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">0</property>
                              </packing>
                            </child>
                            <child>
                              <object class="GtkRadioButton" id="toseparatedby">
                                <property name="label" translatable="yes" context="textimportcsv|toseparatedby">_Separated by</property>
                                <property name="visible">True</property>
                                <property name="can_focus">True</property>
                                <property name="receives_default">False</property>
                                <property name="use_underline">True</property>
                                <property name="xalign">0</property>
                                <property name="active">True</property>
                                <property name="draw_indicator">True</property>
                                <child internal-child="accessible">
                                  <object class="AtkObject" id="toseparatedby-atkobject">
                                    <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|toseparatedby">Select the separator used in your data.</property>
                                  </object>
                                </child>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">1</property>
                              </packing>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkAlignment" id="alignment5">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="hexpand">True</property>
                            <property name="left_padding">12</property>
                            <child>
                              <!-- n-columns=1 n-rows=1 -->
                              <object class="GtkGrid" id="grid2">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <property name="hexpand">True</property>
                                <property name="row_spacing">6</property>
                                <property name="column_spacing">12</property>
                                <child>
                                  <object class="GtkCheckButton" id="tab">
                                    <property name="label" translatable="yes" context="textimportcsv|tab">_Tab</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="tab-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|tab">Separates data delimited by tabs into columns.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">0</property>
                                    <property name="top_attach">0</property>
                                  </packing>
                                </child>
                                <child>
                                  <object class="GtkCheckButton" id="mergedelimiters">
                                    <property name="label" translatable="yes" context="textimportcsv|mergedelimiters">Merge _delimiters</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="halign">start</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="mergedelimiters-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|mergedelimiters">Combines consecutive delimiters and removes blank data fields.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">0</property>
                                    <property name="top_attach">1</property>
                                    <property name="width">2</property>
                                  </packing>
                                </child>
                                <child>
                                  <object class="GtkCheckButton" id="removespace">
                                    <property name="label" translatable="yes" context="textimportcsv|removespace">Tr_im spaces</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="halign">start</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="removespace-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|removespace">Removes starting and trailing spaces from data fields.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">2</property>
                                    <property name="top_attach">1</property>
                                    <property name="width">2</property>
                                  </packing>
                                </child>
                                <child>
                                  <object class="GtkCheckButton" id="comma">
                                    <property name="label" translatable="yes" context="textimportcsv|comma">_Comma</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="comma-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|comma">Separates data delimited by commas into columns.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">1</property>
                                    <property name="top_attach">0</property>
                                  </packing>
                                </child>
                                <child>
                                  <object class="GtkCheckButton" id="semicolon">
                                    <property name="label" translatable="yes" context="textimportcsv|semicolon">S_emicolon</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="semicolon-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|semicolon">Separates data delimited by semicolons into columns.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">2</property>
                                    <property name="top_attach">0</property>
                                  </packing>
                                </child>
                                <child>
                                  <object class="GtkCheckButton" id="space">
                                    <property name="label" translatable="yes" context="textimportcsv|space">S_pace</property>
                                    <property name="visible">True</property>
                                    <property name="can_focus">True</property>
                                    <property name="receives_default">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="use_underline">True</property>
                                    <property name="xalign">0</property>
                                    <property name="draw_indicator">True</property>
                                    <child internal-child="accessible">
                                      <object class="AtkObject" id="space-atkobject">
                                        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|space">Separates data delimited by spaces into columns.</property>
                                      </object>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">3</property>
                                    <property name="top_attach">0</property>
                                  </packing>
                                </child>
                                <child>
                                  <!-- n-columns=1 n-rows=1 -->
                                  <object class="GtkGrid" id="grid3">
                                    <property name="visible">True</property>
                                    <property name="can_focus">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="column_spacing">12</property>
                                    <child>
                                      <object class="GtkCheckButton" id="other">
                                        <property name="label" translatable="yes" context="textimportcsv|other">Othe_r</property>
                                        <property name="visible">True</property>
                                        <property name="can_focus">True</property>
                                        <property name="receives_default">False</property>
                                        <property name="use_underline">True</property>
                                        <property name="xalign">0</property>
                                        <property name="draw_indicator">True</property>
                                        <accessibility>
                                          <relation type="label-for" target="inputother"/>
                                        </accessibility>
                                        <child internal-child="accessible">
                                          <object class="AtkObject" id="other-atkobject">
                                            <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|other">Separates data into columns using the custom separator that you specify. Note: The custom separator must also be contained in your data.</property>
                                          </object>
                                        </child>
                                      </object>
                                      <packing>
                                        <property name="left_attach">0</property>
                                        <property name="top_attach">0</property>
                                      </packing>
                                    </child>
                                    <child>
                                      <object class="GtkEntry" id="inputother">
                                        <property name="visible">True</property>
                                        <property name="can_focus">True</property>
                                        <property name="hexpand">True</property>
                                        <property name="max_length">10</property>
                                        <property name="activates_default">True</property>
                                        <property name="width_chars">3</property>
                                        <accessibility>
                                          <relation type="labelled-by" target="other"/>
                                        </accessibility>
                                        <property name="truncate-multiline">True</property>
                                        <child internal-child="accessible">
                                          <object class="AtkObject" id="inputother-atkobject">
                                            <property name="AtkObject::accessible-name" translatable="yes" context="textimportcsv|inputother-atkobject">Other</property>
                                            <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|inputother">Separates data into columns using the custom separator that you specify. Note: The custom separator must also be contained in your data.</property>
                                          </object>
                                        </child>
                                      </object>
                                      <packing>
                                        <property name="left_attach">1</property>
                                        <property name="top_attach">0</property>
                                      </packing>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">4</property>
                                    <property name="top_attach">0</property>
                                  </packing>
                                </child>
                                <child>
                                  <!-- n-columns=1 n-rows=1 -->
                                  <object class="GtkGrid" id="grid4">
                                    <property name="visible">True</property>
                                    <property name="can_focus">False</property>
                                    <property name="hexpand">True</property>
                                    <property name="column_spacing">12</property>
                                    <child>
                                      <object class="GtkLabel" id="texttextdelimiter">
                                        <property name="visible">True</property>
                                        <property name="can_focus">False</property>
                                        <property name="label" translatable="yes" context="textimportcsv|texttextdelimiter">Strin_g delimiter:</property>
                                        <property name="use_underline">True</property>
                                        <property name="mnemonic_widget">textdelimiter</property>
                                        <property name="xalign">0</property>
                                      </object>
                                      <packing>
                                        <property name="left_attach">0</property>
                                        <property name="top_attach">0</property>
                                      </packing>
                                    </child>
                                    <child>
                                      <object class="GtkComboBoxText" id="textdelimiter">
                                        <property name="visible">True</property>
                                        <property name="can_focus">False</property>
                                        <property name="hexpand">True</property>
                                        <property name="has_entry">True</property>
                                        <child internal-child="entry">
                                          <object class="GtkEntry" id="comboboxtext-entry">
                                            <property name="can_focus">True</property>
                                            <property name="activates_default">True</property>
                                            <property name="truncate-multiline">True</property>
                                            <property name="width_chars">1</property>
                                          </object>
                                        </child>
                                        <child internal-child="accessible">
                                          <object class="AtkObject" id="textdelimiter-atkobject">
                                            <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|textdelimiter">Select a character to delimit text data. You can also enter a character in the text box.</property>
                                          </object>
                                        </child>
                                      </object>
                                      <packing>
                                        <property name="left_attach">1</property>
                                        <property name="top_attach">0</property>
                                      </packing>
                                    </child>
                                  </object>
                                  <packing>
                                    <property name="left_attach">4</property>
                                    <property name="top_attach">1</property>
                                  </packing>
                                </child>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">2</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="separatoroptions">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportcsv|separatoroptions">Separator Options</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
            <child>
              <object class="GtkFrame" id="frame3">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment3">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <object class="GtkBox" id="box3">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="spacing">6</property>
                        <property name="homogeneous">True</property>
                        <child>
                          <object class="GtkCheckButton" id="quotedfieldastext">
                            <property name="label" translatable="yes" context="textimportcsv|quotedfieldastext">F_ormat quoted field as text</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0.009999999776482582</property>
                            <property name="draw_indicator">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="quotedfieldastext-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|quotedfieldastext">When this option is enabled, fields or cells whose values are quoted in their entirety (the first and last characters of the value equal the text delimiter) are imported as text.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkCheckButton" id="detectspecialnumbers">
                            <property name="label" translatable="yes" context="textimportcsv|detectspecialnumbers">Detect special _numbers</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="detectspecialnumbers-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|detectspecialnumbers">When this option is enabled, Calc will automatically detect all number formats, including special number formats such as dates, time, and scientific notation.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkCheckButton" id="skipemptycells">
                            <property name="label" translatable="yes" context="textimportcsv|skipemptycells">S_kip empty cells</property>
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="receives_default">False</property>
                            <property name="tooltip_text" translatable="yes" context="textimportcsv|skipemptycells">If enabled, blank cells in source will not override the target.</property>
                            <property name="use_underline">True</property>
                            <property name="xalign">0</property>
                            <property name="draw_indicator">True</property>
                            <child internal-child="accessible">
                              <object class="AtkObject" id="skipemptycells-atkobject">
                                <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|skipemptycells">When this option is enabled, Calc preserves previous content of cells when pasting empty ones. Otherwise, Calc deletes content of previous cells.</property>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">2</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label3">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportcsv|label3">Other Options</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">False</property>
                <property name="position">2</property>
              </packing>
            </child>
            <child>
              <object class="GtkFrame" id="frame4">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <property name="label_xalign">0</property>
                <property name="shadow_type">none</property>
                <child>
                  <object class="GtkAlignment" id="alignment4">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="top_padding">6</property>
                    <property name="left_padding">12</property>
                    <child>
                      <object class="GtkBox" id="box4">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="orientation">vertical</property>
                        <property name="spacing">6</property>
                        <child>
                          <object class="GtkBox" id="box5">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="spacing">12</property>
                            <child>
                              <object class="GtkLabel" id="textcolumntype">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <property name="label" translatable="yes" context="textimportcsv|textcolumntype">Column t_ype:</property>
                                <property name="use_underline">True</property>
                                <property name="mnemonic_widget">columntype</property>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">0</property>
                              </packing>
                            </child>
                            <child>
                              <object class="GtkComboBoxText" id="columntype">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <child internal-child="accessible">
                                  <object class="AtkObject" id="columntype-atkobject">
                                    <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|columntype">Choose a column in the preview window and select the data type to be applied the imported data.</property>
                                  </object>
                                </child>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">1</property>
                              </packing>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">0</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkScrolledWindow" id="scrolledwindow">
                            <property name="visible">True</property>
                            <property name="can_focus">True</property>
                            <property name="hexpand">True</property>
                            <property name="vexpand">True</property>
                            <property name="hadjustment">adjustment2</property>
                            <property name="vadjustment">adjustment3</property>
                            <property name="hscrollbar_policy">always</property>
                            <property name="vscrollbar_policy">always</property>
                            <property name="shadow_type">in</property>
                            <child>
                              <object class="GtkViewport" id="viewport">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <child>
                                  <object class="GtkBox" id="box">
                                    <property name="visible">True</property>
                                    <property name="can_focus">False</property>
                                    <property name="orientation">vertical</property>
                                    <child>
                                      <object class="GtkDrawingArea" id="csvruler">
                                        <property name="visible">True</property>
                                        <property name="can_focus">True</property>
                                        <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                                        <property name="hexpand">True</property>
                                      </object>
                                      <packing>
                                        <property name="expand">False</property>
                                        <property name="fill">True</property>
                                        <property name="position">0</property>
                                      </packing>
                                    </child>
                                    <child>
                                      <object class="GtkDrawingArea" id="csvgrid">
                                        <property name="visible">True</property>
                                        <property name="can_focus">True</property>
                                        <property name="events">GDK_BUTTON_PRESS_MASK | GDK_BUTTON_RELEASE_MASK | GDK_KEY_PRESS_MASK | GDK_KEY_RELEASE_MASK | GDK_STRUCTURE_MASK</property>
                                        <property name="hexpand">True</property>
                                        <property name="vexpand">True</property>
                                      </object>
                                      <packing>
                                        <property name="expand">False</property>
                                        <property name="fill">True</property>
                                        <property name="position">1</property>
                                      </packing>
                                    </child>
                                  </object>
                                </child>
                              </object>
                            </child>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">1</property>
                          </packing>
                        </child>
                        <child>
                          <object class="GtkLabel" id="textalttitle">
                            <property name="can_focus">False</property>
                            <property name="no_show_all">True</property>
                            <property name="label" translatable="yes" context="textimportcsv|textalttitle">Text to Columns</property>
                          </object>
                          <packing>
                            <property name="expand">False</property>
                            <property name="fill">True</property>
                            <property name="position">2</property>
                          </packing>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>
                <child type="label">
                  <object class="GtkLabel" id="label4">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="label" translatable="yes" context="textimportcsv|label4">Fields</property>
                    <attributes>
                      <attribute name="weight" value="bold"/>
                    </attributes>
                  </object>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">3</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">True</property>
            <property name="fill">True</property>
            <property name="position">1</property>
          </packing>
        </child>
      </object>
    </child>
    <action-widgets>
      <action-widget response="-5">ok</action-widget>
      <action-widget response="-6">cancel</action-widget>
      <action-widget response="-11">help</action-widget>
    </action-widgets>
    <child type="titlebar">
      <placeholder/>
    </child>
    <child internal-child="accessible">
      <object class="AtkObject" id="TextImportCsvDialog-atkobject">
        <property name="AtkObject::accessible-description" translatable="yes" context="textimportcsv|extended_tip|TextImportCsvDialog">Sets the import options for delimited data.</property>
      </object>
    </child>
  </object>
  <object class="GtkSizeGroup" id="sizegroup1">
    <widgets>
      <widget name="other"/>
      <widget name="texttextdelimiter"/>
    </widgets>
  </object>
  <object class="GtkMenu" id="popup">
    <property name="visible">True</property>
    <property name="can_focus">False</property>
  </object>
</interface>
