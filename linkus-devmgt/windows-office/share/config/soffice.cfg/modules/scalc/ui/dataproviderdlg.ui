<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated with glade 3.22.1 -->
<interface domain="sc">
  <requires lib="gtk+" version="3.20"/>
  <object class="GtkDialog" id="dataproviderdlg">
    <property name="can_focus">False</property>
    <property name="modal">True</property>
    <property name="type_hint">dialog</property>
    <child>
      <placeholder/>
    </child>
    <child internal-child="vbox">
      <object class="GtkBox">
        <property name="can_focus">False</property>
        <property name="orientation">vertical</property>
        <property name="spacing">2</property>
        <child internal-child="action_area">
          <object class="GtkButtonBox">
            <property name="can_focus">False</property>
            <property name="layout_style">end</property>
            <child>
              <placeholder/>
            </child>
            <child>
              <placeholder/>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">False</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkMenuBar" id="menubar">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <child>
              <object class="GtkMenuItem">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label">_Start</property>
                <property name="use_underline">True</property>
                <child type="submenu">
                  <object class="GtkMenu" id="start">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkMenuItem">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="label">_Column</property>
                <property name="use_underline">True</property>
                <child type="submenu">
                  <object class="GtkMenu" id="column">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                  </object>
                </child>
              </object>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">0</property>
          </packing>
        </child>
        <child>
          <object class="GtkBox">
            <property name="visible">True</property>
            <property name="can_focus">False</property>
            <property name="hexpand">True</property>
            <property name="vexpand">True</property>
            <property name="spacing">12</property>
            <child>
              <object class="GtkBox" id="data_table">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="hexpand">True</property>
                <property name="vexpand">True</property>
                <child>
                  <placeholder/>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">0</property>
              </packing>
            </child>
            <child>
              <object class="GtkBox">
                <property name="visible">True</property>
                <property name="can_focus">False</property>
                <property name="orientation">vertical</property>
                <property name="spacing">6</property>
                <child>
                  <!-- n-columns=1 n-rows=1 -->
                  <object class="GtkGrid">
                    <property name="visible">True</property>
                    <property name="can_focus">False</property>
                    <property name="column_spacing">6</property>
                    <child>
                      <object class="GtkLabel" id="label2">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="label" translatable="yes" context="dataproviderdlg|db_name">Database Range: </property>
                        <accessibility>
                          <relation type="label-for" target="select_db_range"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">0</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                    <child>
                      <object class="GtkComboBoxText" id="select_db_range">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="hexpand">True</property>
                        <accessibility>
                          <relation type="labelled-by" target="label2"/>
                        </accessibility>
                      </object>
                      <packing>
                        <property name="left_attach">1</property>
                        <property name="top_attach">0</property>
                      </packing>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">0</property>
                  </packing>
                </child>
                <child>
                  <object class="GtkScrolledWindow" id="scroll">
                    <property name="visible">True</property>
                    <property name="can_focus">True</property>
                    <property name="vexpand">True</property>
                    <property name="hscrollbar_policy">never</property>
                    <property name="shadow_type">in</property>
                    <child>
                      <object class="GtkViewport">
                        <property name="visible">True</property>
                        <property name="can_focus">False</property>
                        <property name="vexpand">True</property>
                        <child>
                          <object class="GtkBox">
                            <property name="visible">True</property>
                            <property name="can_focus">False</property>
                            <property name="vexpand">True</property>
                            <property name="orientation">vertical</property>
                            <child>
                              <object class="GtkBox" id="operation_ctrl">
                                <property name="visible">True</property>
                                <property name="can_focus">False</property>
                                <property name="vexpand">True</property>
                                <property name="orientation">vertical</property>
                                <child>
                                  <placeholder/>
                                </child>
                              </object>
                              <packing>
                                <property name="expand">False</property>
                                <property name="fill">True</property>
                                <property name="position">0</property>
                              </packing>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                  <packing>
                    <property name="expand">False</property>
                    <property name="fill">True</property>
                    <property name="position">1</property>
                  </packing>
                </child>
              </object>
              <packing>
                <property name="expand">False</property>
                <property name="fill">True</property>
                <property name="position">1</property>
              </packing>
            </child>
          </object>
          <packing>
            <property name="expand">False</property>
            <property name="fill">True</property>
            <property name="position">2</property>
          </packing>
        </child>
      </object>
    </child>
  </object>
</interface>
