<?xml version="1.0" encoding="UTF-8"?>
<!--
 * This file is part of the LibreOffice project.
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at http://mozilla.org/MPL/2.0/.
 *
-->
<menu:menupopup xmlns:menu="http://openoffice.org/2001/menu">
  <menu:menuitem menu:id=".uno:Cut"/>
  <menu:menuitem menu:id=".uno:Copy"/>
  <menu:menuitem menu:id=".uno:Paste"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:TransformDialog"/>
  <menu:menuitem menu:id=".uno:FormatLine"/>
  <menu:menuitem menu:id=".uno:FormatArea"/>
  <menu:menuseparator/>
  <menu:menu menu:id=".uno:ObjectAlign">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:ObjectAlignLeft"/>
      <menu:menuitem menu:id=".uno:AlignCenter"/>
      <menu:menuitem menu:id=".uno:ObjectAlignRight"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:AlignUp"/>
      <menu:menuitem menu:id=".uno:AlignMiddle"/>
      <menu:menuitem menu:id=".uno:AlignDown"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:ArrangeMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:BringToFront"/>
      <menu:menuitem menu:id=".uno:Forward"/>
      <menu:menuitem menu:id=".uno:Backward"/>
      <menu:menuitem menu:id=".uno:SendToBack"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:BeforeObject"/>
      <menu:menuitem menu:id=".uno:BehindObject"/>
      <menu:menuseparator/>
      <menu:menuitem menu:id=".uno:ReverseOrder"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menu menu:id=".uno:ConvertMenu">
    <menu:menupopup>
      <menu:menuitem menu:id=".uno:ChangeBezier"/>
      <menu:menuitem menu:id=".uno:ChangePolygon"/>
      <menu:menuitem menu:id=".uno:convert_to_contour"/>
      <menu:menuitem menu:id=".uno:ConvertInto3D"/>
      <menu:menuitem menu:id=".uno:ConvertInto3DLatheFast"/>
      <menu:menuitem menu:id=".uno:ConvertIntoBitmap"/>
      <menu:menuitem menu:id=".uno:ConvertIntoMetaFile"/>
    </menu:menupopup>
  </menu:menu>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:EnterGroup"/>
  <menu:menuitem menu:id=".uno:LeaveGroup"/>
  <menu:menuitem menu:id=".uno:Window3D"/>
  <menu:menuseparator/>
  <menu:menuitem menu:id=".uno:CustomAnimation"/>
  <menu:menuitem menu:id=".uno:AnimationEffects"/>
  <menu:menuitem menu:id=".uno:ExecuteAnimationEffect"/>
</menu:menupopup>
