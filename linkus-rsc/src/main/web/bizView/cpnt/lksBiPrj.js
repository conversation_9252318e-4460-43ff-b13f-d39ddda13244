Vue.component('lks-bi-prj', {
	template:
		'<Select  v-model="value" :label="showValue" clearable remote :remote-method="searchPrj" placeholder="请选择项目"  @on-change="changed()" filterable style="width:400px;"> '+
		' <Option v-for="prj in prjs" :value="prj.prjId" :key="prj.prjId">{{ (prj.prjCode? (prj.prjCode+"/"):"")+prj.prjName }}</Option> ' +
		'</Select>',
	props:['prdId',  'fieldId',  'initValue',  'multiple', 'showValue', 'value', 'notInCludeIsPrjSet'],
	data: function(){
		return {
			initValue: "",
			value: "",
			fieldId: null,
			prjs: null,
			prjMap: {},
			prdId:"",
			showValue:""
		}
	},
	methods:{
		searchPrj: function(value){
			var sf = this;
			if(!value || !value.trim()) {
				return;
			}
			if(!sf.multiple) {
				if(value == sf.value) {
					var keywords = sf.showValue;
					if(keywords.indexOf("/") > -1) {
						var keywordList = keywords.split("/");
						if(!!keywordList[0]) {
							value = keywordList[0];
						}else if(!!keywordList[1]) {
							value = keywordList[1];
						}
					}else {
						value = sf.showValue;
					}
				}
			}
			if(value.indexOf("/") > -1) {
				var keywordList = value.split("/");
				if(!!keywordList[0]) {
					value = keywordList[0];
				}else if(!!keywordList[1]) {
					value = keywordList[1];
				}
			}
			return $.ajax({
				url: prjDomain + '/prjInfoCtrl/fuzzyPrjNameCodeQuery.action',
				data: {
					"prjNameCode": value,
					"prdId"      : sf.prdId,
					"inCludeIsPrjSet" : !!sf.notInCludeIsPrjSet ? false : true,
				},
			}).then(function(data){
				sf.prjs = data || [];
				sf._initPrjMap();
			});
		},
		changed: function(){
			var _this = this;
			var val = _this.value || "";
			var text = null;

			if(!!val) {
				if(_this.multiple){
					text = val.map(function(item){ return (_this.prjMap[item] || {}).prjCode + "/" + (_this.prjMap[item] || {}).prjName}).join(',');
				} else{
					text = (_this.prjMap[_this.value] || {}).prjCode + "/" + (_this.prjMap[_this.value] || {}).prjName;
				}
			}

			Vue.evtHub.$emit("field-value-changed", {
				fieldId: this.fieldId,
				value: val,
				text: text
			});
		},
		_initPrjMap: function(){
			this.prjMap = [];
			for( var i = 0; i < this.prjs.length; i++ ){
				var o = this.prjs[i];
				this.prjMap[o.prjId] = o;
			}
		}
	},
	mounted: function(){
		var sf = this;
	},
	destroyed: function(){
	}
});