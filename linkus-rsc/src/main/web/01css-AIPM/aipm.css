    [v-cloak] {
    	display: none;
    }
    .homePageNum{
    	cursor: pointer;
    }
    .homePageNum:hover{
    	text-decoration: underline;
    }
    .timelineDiv{
        width: 95%;
        margin: 0px auto;
    }
    .timeline{
        color: #767676;
        width: 90%;
        height: 100%;
        white-space: nowrap;
        position: relative;
        font-size: 12px;
        margin: 0px auto;
    }
    .timeline .line{
        position: absolute;
        top: 90px;
        height: 1px;
        width: 100%;
        border-bottom: 2px solid #7E899D;
        z-index: 1;
    }
    .timeline .next{
        position: absolute;
        top: 68px;
        right: -2px;
        display: inline-block;
        width: 20px;
        height: 40px;
        background: url(../../03images/arrow.png) no-repeat -19px 13px;
    }
    .timeline .wrapper{
        padding: 0 10px;
        margin: auto;
        overflow: hidden;
        position: relative;
        width: 100%;
    }
    .timeline .item{
        width: 90px;
        float: left;
        position: relative;
        height: 176px;
        display: inline-block;
    }
    .timeline .item .content{
        bottom: 30px;
        padding-top: 10px;
        padding-bottom: 5px;
        margin-left: -5px;
        padding-left: 53px;
        position: absolute;
    }
    .timeline .item .content ul li {
        padding-left: 23px;
    }
    .timeline .item .flag {
        top: -55px;
        position: absolute;
        left: 45px;
        bottom: -4px;
        display: block;
        width: 20px;
        height: 20px;
    }
    .timeline ul, li {
        list-style: none;
        line-height: 26px;
    }
    .num-circle {           
        color: #fff; 
        z-index: 1000; 
        background-color: red; 
        padding: 1px 6px 0 6px !important; 
        line-height: 16px; 
        border-radius: 13px;
        font-size: 12px;
    }
    .num-circle.menu-fixed{
        position: absolute;  
        right: -8px; 
        top: -75px; 
    }
    .point{
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: green;
        position: absolute;
        left: 45px;
        top: -25px;
        z-index: 100;
    }
    .today{
        width: 0;
        height: 0;
        border: 7px solid;
        border-color: transparent transparent red transparent;
        position: absolute;
        top: 78px;
        display: inline-block;
        z-index: 100;
    }
   
    .AI-PM-modal .ivu-modal{
    	overflow: hidden;
    	height: 92vh;
    }
    .AI-PM-modal .ivu-modal-content{
    	height: 100%;
    }
    .AI-PM-modal .ivu-modal .ivu-modal-body{
    	height: calc(100% - 52px);
    }
    .AI-PM-modal .ivu-modal .ivu-table-wrapper{
    	height: 98%;
    }
    .AI-PM-modal .ivu-modal .ivu-table-body{
    	height: calc(100% - 51px);
    }
    .small-modal .dashed-line{
    	height: 1px;
    	border-bottom: 1px dashed rgba(255,255,255,.5);
    }
    .spin-icon-load {
        animation: ani-demo-spin 1s linear infinite;
    }
    .blur {
		-webkit-filter: blur(5px); /* Chrome, Opera */
		   -moz-filter: blur(5px);
		    -ms-filter: blur(5px);    
		        filter: blur(5px);
		filter: progid:DXImageTransform.Microsoft.Blur(PixelRadius=10, MakeShadow=false); /* IE6~IE9 */
	}
	.ivu-message .ivu-icon {
		font-size: 31px;
	}
	.ivu-message-notice-close i.ivu-icon{
		font-size: 31px;
	}
	.ivu-message-notice-content-text{
		vertical-align: middle;
	}
	.AI-PM-content .grid-8 .ivu-icon-plus-circled{
		font-size: 2vh;
	}
	.prjgroup-content{
		margin-top: 12px;
		padding: 0 12px;
	}
	.prjgroup-content:hover{
		background: rgba(255, 162, 51, 0.7);
	}
	.prjgroup-msg .ivu-poptip-body{
		padding: 8px 0;
	}
	.ivu-upload-list{
		display: none;
	}
	.notice-modal .ivu-modal-footer{
		border-top: none;
	}
	.notice-modal .ivu-modal{
		width: 42vw !important;
	}
	.prjgroup-msg .ivu-poptip-popper{
		margin-left: 24px;
	}
	.AI-PM-modal .ivu-table th:nth-child(7n) .ivu-tooltip-rel{
		display:flex;
		align-items: center;
	}
	.last-cost{
		width: 100%;
		height: 100%;
	}
	.last-cost .ivu-poptip-rel{
		width: 100%;
		height: 100%;
	}
	.last-cost .ivu-poptip-body{
		padding: 8px 16px;
	}
	.last-cost .ivu-poptip-popper{
		margin-left: 0 !important;
	}
	.AI-PM-modal .ivu-table-fixed{
		background: #555 !important;
	}
	.column-modal .ivu-modal{
		width: 70vw !important;
	}
	.column-modal .ivu-modal .ivu-modal-body div:hover{
		background: transparent;
	}
	.column-modal .ivu-modal .ivu-modal-body{
		padding: 0;
		height: 100%;
	}
	.column-modal .ivu-modal-content{
		height: 92vh !important;
	}
	.column-modal .ivu-modal .ivu-modal-body .tabs{
		background: rgba(0,0,0,.2);
		height: 50px;
		display: flex;
		align-items: center;
		padding-left: 1vw;
	}
	.column-modal .ivu-modal .ivu-modal-body .select-tab{
		background: transparent;
	}
	.column-modal .ivu-modal .ivu-modal-body .tabs:hover{
		background: rgba(255,162,51,.7);
	}
	.column-modal .ivu-modal .ivu-modal-body .tasktip:hover{
		background: rgba(255,162,51,.7);
	}
	.column-modal .ivu-modal-header{
		display: none;
	}
	.ivu-table-fixed-right::before, .ivu-table-fixed::before{
		display: none;
	}
/* 	#prjCostDetailInfoTab .ivu-table-header tr:first-child th:nth-child(5),
	#prjCostDetailInfoTab .ivu-table-header tr:first-child th:nth-child(6),
	#prjCostDetailInfoTab .ivu-table-header tr:nth-child(2) th:nth-child(6),
	#prjCostDetailInfoTab .ivu-table-header tr:nth-child(2) th:nth-child(12),
	#prjCostDetailInfoTab .ivu-table-tbody tr td:nth-child(9),
	#prjCostDetailInfoTab .ivu-table-tbody tr td:nth-child(15){
		border-left: 1px solid #dcdee2 !important;
	} */