<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>AI生成测试用例</title>
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <!-- 本地样式 -->
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <!--<link href="../../01css-portal/style.css" rel="stylesheet" type="text/css"/>-->
    <!-- jQuery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <!-- vue -->
    <script src="../../00scripts/00lib/vue/vue.js"></script>
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>
    <!-- iview -->
    <script src="../../00scripts/00lib/iview/4.0/iview.js"></script>
    <script src="../../00scripts/00lib/iview/4.0/iview.min.js"></script>
    <link href="../../00scripts/00lib/iview/4.0/styles/iview.css" rel="stylesheet" type="text/css"/>
    <!-- 本地图标字体 -->
    <!-- 本地不知道啥 -->
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>
    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>

    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>
    <script type="text/javascript" src="../../00scripts/location/verifyLogin.js"></script>

    <style>
        /* 滚动条整体部分 */
        ::-webkit-scrollbar {
            width: 7px; /* 滚动条的宽度 */
            height: 7px;
        }
        /* 滚动条滑块 */
        ::-webkit-scrollbar-thumb {
            background-color: #C8CDDC; /* 滑块颜色 */
            border-radius: 10px; /* 滑块圆角 */
        }
        /* 滚动条轨道 */
        ::-webkit-scrollbar-track {
            background: #F1F5F9; /* 轨道颜色 */
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f6fa;
            color: #333;
        }
        .container{
            width: 100%;
            height: 100%;
            background: #fff;
            overflow: hidden;
        }
        .ellipsis{
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        .container .main_content{
            width: 100%;
            height: calc(100% - 24px);
            min-height: 556px;
            margin: 12px auto;
            background: #fff;
            border-radius: 5px;
        }
        .container .main_content .header_wrap{
            height: 56px;
            width: 100%;
            padding: 12px 21px;
            display: flex;
            align-items: center;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
            border-bottom: 1px solid #dbe3eb;
        }
        .container .main_content .header_wrap .robot_wrap{
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        .container .main_content .header_wrap .robot_wrap >img{
            width: 32px;
            height: 32px;
        }
        .container .main_content .header_wrap .tips_wrap{
            height: 21px;
            font-size: 12px;
            text-align: left;
            color: #262626;
            line-height: 21px;
        }
        .container .main_content .body_wrap{
            width: 100%;
            height: calc(100% - 57px);
            min-height: 500px;
            margin-bottom: 12px;
            position: relative;
            overflow: hidden;
        }
        .container .main_content .dialog_list_wrap{
            width: 789px;
            height: calc(100% - 146px);
            margin: auto;
            overflow-y: auto;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }
        .container .main_content .dialog_list_wrap::-webkit-scrollbar {
            width: 0;
        }
        .container .main_content .dialog_list_wrap .dialog_history{
            margin-top: 32px;
        }
        .container .main_content .dialog_list_wrap .dialog_history .dialog_history_item{

        }
        .container .main_content .dialog_list_wrap .dialog_history .dialog_history_item .request{
            width: fit-content;
            max-width: 500px;
            margin-left: auto;
            padding: 16px 24px;
            background: #f1f5f9;
            border-radius: 16px 0px 16px 16px;
            font-size: 14px;
            text-align: left;
            white-space: pre-line;
            color: #666666;
            line-height: 20px;
            margin-bottom: 32px;
        }
        .container .main_content .dialog_list_wrap .dialog_history .dialog_history_item .response{
            width: 100%;
            font-size: 14px;
            text-align: left;
            white-space: pre-line;
            color: #262626;
            line-height: 20px;
            margin-bottom: 32px;
        }
        .container .main_content .dialog_list_wrap .dialog_new{

        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item{
            margin-top: 32px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request{
            /*margin-bottom: 32px;*/
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_file_wrap{
            width: fit-content;
            max-width: 450px;
            margin-left: auto;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_file_wrap .request_file{
            width: 220px;
            height: 32px;
            font-size: 14px;
            color: #999;
            line-height: 32px;

            display: flex;
            align-items: center;

            padding-left: 8px;
            padding-right: 16px;
            margin-bottom: 8px;
            background: #f1f5f9;
            border-radius: 8px;
            margin-right: 10px;
            position: relative;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_file_wrap .request_file:nth-child(even){
            margin-right: 0;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_file_wrap .request_file .request_file_icon{
            height: 32px;
            color: #c8cddc;
            margin-right: 8px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_file_wrap .request_file .request_file_name{
            width: 172px;
            color: #666;
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .request .request_details{
            width: fit-content;
            max-width: 450px;
            margin-left: auto;
            padding: 16px 24px;
            background: #f1f5f9;
            border-radius: 16px 0px 16px 16px;
            font-size: 14px;
            text-align: left;
            white-space: pre-line;
            color: #666666;
            line-height: 20px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response{
            margin-top: 32px;
            margin-bottom: 12px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .response_table{
            margin-top: 12px;
            margin-bottom: 12px;
            border: 1px solid #dbe3eb !important;
            border-radius: 16px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .response_table .ivu-table{
            font-size: 12px;
            text-align: left;
            color: #262626;
            line-height: 17px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .response_table .ivu-table .ivu-table-header{
            color: #666666;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .response_table .ivu-table .ivu-table-overflowX{
            overflow-x: hidden;
        }
        .response_table .ivu-table-border td, .response_table .ivu-table-border th {
            border-right: 1px solid #dbe3eb !important;
            border-bottom: 1px solid #dbe3eb !important;
        }
        .response_table .ivu-table-border .ivu-table-tbody tr:last-child td{
            border-bottom: none !important;
        }
        .response_table .ivu-table-border td:last-child, .response_table .ivu-table-border th:last-child {
            border-right: none !important;
        }
        .response_table .ivu-table-header thead th, .response_table .ivu-table-fixed-header thead th {
            background: #f1f5f9;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .table_operate{
            color: #8491a5;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .table_operate span i{
            width: 14px;
            height: 14px;
            cursor: pointer;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .table_operate .submit_poptip .ivu-poptip-popper{
            z-index: 3;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response .table_operate .submit_poptip .ivu-poptip-body{
            height: 32px;
            padding: 4px 16px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response_loading{
            margin-top: 24px;
        }
        .container .main_content .dialog_list_wrap .dialog_new .dialog_new_item .response_loading img{
            width: 60px;
            height: 60px;
        }
        .container .main_content .dialog_wrap{
            width: 789px;
            min-height: 120px;
            max-height: 240px;
            background: #fff;
            padding: 7px 7px 46px 7px;
            border: 1px solid #3883e5;
            border-radius: 8px;
            position: relative;
            margin: auto;
            z-index: 10;
        }
        .container .main_content .dialog_wrap .input_and_doc{
            width: 100%;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll{
            width: 100%;
            position: relative;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            margin-bottom: 7px;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn{
            width: 26px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            z-index: 2;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn i {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 17px;
            height: 17px;
            border: 1px solid #e8ecf1;
            border-radius: 5px;
            color: #8491A5;
            cursor: pointer;
            background: #fff;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn:after {
            content: '';
            position: absolute;
            width: 8px;
            height: 28px;
            opacity: 0.6;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn.file_tab_left {
            left: 0;
            background: linear-gradient(to right, rgba(255,255,255,1), rgba(255,255,255,0));
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn.file_tab_left i {
            transform: rotate(-180deg);
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn.file_tab_left:after {
            right: -8px;
            background: linear-gradient(270deg, rgba(255, 255, 255, 0) 100%, rgba(255, 255, 255, 1) 100%);
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn.file_tab_right {
            right: 0;
            background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .file_tab_btn.file_tab_right:after {
            left: -8px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 100%, rgba(255,255,255,1) 100%);
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .files_wrap{
            height: 100%;
            width: auto;
            position: relative;
            display: flex;
            align-items: center;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .files_wrap .file_item {
            width: 120px;
            height: 32px;
            color: #a5acc4;
            font-size: 12px;
            line-height: 32px;

            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;

            padding-left: 8px;
            padding-right: 16px;
            background: rgba(200,205,220,0.3);
            border-radius: 8px;
            margin-right: 8px;
            position: relative;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .files_wrap .file_item .icon_wrap {
            width: 10px;
            height: 10px;
            color: #fff;
            background: #000;
            border-radius: 50%;
            position: absolute;
            top: 1px;
            right: 1px;
            display: none;
            margin-left: 16px;
            cursor: pointer;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .files_wrap .file_item .icon_wrap i{
            font-size: 9px;
        }
        .container .main_content .dialog_wrap .input_and_doc .file_wrap_scroll .files_wrap .file_item:hover .icon_wrap{
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container .main_content .dialog_wrap .input_and_doc .input_wrap .ivu-input{
            border: none !important;
        }
        .container .main_content .dialog_wrap .input_and_doc .input_wrap .ivu-input:hover,.ivu-input:focus {
            border: none !important;
            box-shadow: none !important;
        }
        .input_wrap textarea {
            resize: none;
        }
        .container .main_content .dialog_wrap .operate_wrap{
            width: calc(100% - 14px);
            margin-top: 7px;
            display: flex;
            align-items: center;
            position: absolute;
            bottom: 7px;
        }
        .container .main_content .dialog_wrap .operate_wrap .icon_wrap{
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f8fb;
            border-radius: 8px;
            margin-right: 8px;
            cursor: pointer;
        }
        .container .main_content .dialog_wrap .operate_wrap .icon_wrap .ivu-upload-list{
            display: none;
        }
        .container .main_content .dialog_wrap .operate_wrap .icon_wrap i{
            font-size: 18px;
            color: #555555;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand{
            width: 339px;
            height: 32px;
            display: flex;
            border: 1px solid #e8ecf1;
            border-radius: 8px;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_label{
            width: 60px;
            height: 17px;
            font-size: 12px;
            text-align: left;
            color: #666666;
            line-height: 17px;
            margin: 7px 1px 7px 7px;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap{
            width: calc(100% - 68px);
            border-radius: 8px;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-input{
            font-size: 12px;
            border-radius: 8px;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-input::placeholder{
            font-size: 12px;
            color: #DBE3EB;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-selection{
            border: none;
            height: 31px;
            min-height: 31px;
            background-color: transparent;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-selection:hover{
            border: none !important;
            box-shadow: none !important;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-visible .ivu-select-selection{
            border: none !important;
            box-shadow: none !important;
        }
        .container .main_content .dialog_wrap .operate_wrap .choose_demand .biz_wrap .ivu-select-dropdown{
            width: 606px !important;
            left: -68px !important;
        }
        .container .main_content .dialog_wrap .operate_wrap .model_wrap{
            position: absolute;
            bottom: 0;
            right: 39px;
        }
        .container .main_content .dialog_wrap .operate_wrap .model_wrap .model_ct_wrap{
            height: 32px;
            color: #666;
            border: 1px solid #e8ecf1;
            border-radius: 8px;
            padding: 7px;
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
        .model_list_poptip .ivu-poptip-popper{
            left: 0 !important;
        }
        .model_list_poptip .ivu-poptip-arrow{
            display: none;
        }
        .model_list_poptip .ivu-poptip-title-inner{
            color: #555;
        }
        .model_list_poptip .model_list .model_item{
            width: 240px;
            height: 32px;
            padding: 6px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            position: relative;
        }
        .model_list_poptip .model_list .model_item:hover{
            background: #3883e51a;
        }
        .model_list_poptip .model_item.selected{
            background: #3883e51a;
        }
        .model_list_poptip .model_item .sel_icon{
            width: 32px;
            height: 32px;
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .model_list_poptip .model_item .sel_icon >i{
            font-size: 18px;
            color: #65CE7A;
        }
        .container .main_content .dialog_wrap .operate_wrap .send_wrap{
            margin-right: 0;
            position: absolute;
            bottom: 0;
            right: 0;
        }
        .container .main_content .dialog_wrap .operate_wrap .send_wrap i{
            color: #3883e5;
        }
        .container .main_content .dialog_wrap .operate_wrap .upload_disabled{
            cursor: not-allowed;
        }
        .container .main_content .dialog_wrap .operate_wrap .upload_disabled >i{
            cursor: not-allowed;
            color: #c8cddc;
        }
        .vertical-center-modal .ivu-modal{
            top: 0 !important;
        }
        .cursor{
            cursor: pointer;
        }
        .edit_table .ivu-table-body .ivu-table-cell{
            color: #555;
            font-size: 12px;
            padding: 12px;
        }
        .edit_table .ivu-table-header .ivu-table-cell{
            color: #999;
            font-size: 12px;
        }
        .edit_table .ivu-table-header thead th{
            background: #f5f8fb;
        }
        .edit_table .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td, .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td{
            background: #f5f8fb;
        }
        .edit_table .ivu-input, .edit_table .ivu-select .ivu-select-selected-value{
            font-size: 12px;
        }
        .ivu-select-item{
            font-size: 12px !important;
        }
        .prompt_word .ivu-modal{
            height: 100%;
        }
        .prompt_word .ivu-modal .ivu-modal-content{
            height: 100%;
        }
        .prompt_word .ivu-modal .ivu-modal-content .ivu-modal-body{
            height: calc(100% - 109px);
        }
        .prompt_word .ivu-modal .ivu-modal-content .ivu-modal-body .word_wrap{
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        .prompt_word .ivu-modal .ivu-modal-content .ivu-modal-body .ivu-input-type-textarea .ivu-input{
            height: 100%;
            resize: none;
        }

        .container .main_content .body_wrap .to_bottom_wrap{
            width: 100%;
            position: relative;
        }
        .container .main_content .body_wrap .to_bottom_wrap .to_bottom_ct_wrap{
            width: 798px;
            margin: auto;
            position: relative;
        }
        .container .main_content .body_wrap .to_bottom_wrap .to_bottom_ct{
            width: 100%;
            height: 84px;
            margin: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            bottom: 0;
            left: 0;
            z-index: 20;
        }
        .container .main_content .body_wrap .to_bottom_wrap .bottom_icon_wrap{
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid rgba(0,0,0,.08);
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
        }
        .container .main_content .body_wrap .to_bottom_wrap .bottom_icon_wrap:hover{
            box-shadow: 0 0 4px 0 rgba(0, 0, 0, .02), 0 10px 16px 0 rgba(47, 53, 64, .14);
        }
        .container .main_content .body_wrap .to_bottom_wrap .bottom_icon_wrap i{
            font-size: 24px;
            color: rgba(0,0,0,0.85);
        }
    </style>
</head>
<body>
    <div class="container" id="main">
        <div class="main_content">
            <div class="header_wrap">
                <div class="robot_wrap">
                    <img src="../../03images/AI/ai_robot.png" />
                </div>
                <div class="tips_wrap">
                    <span>欢迎使用AI测试用例生成器！您可以输入需求描述、上传需求文档或选择现有需求，我将帮助您生成测试用例。</span>
                </div>
            </div>
            <div class="body_wrap" ref="bodyWrap">
                <div class="dialog_list_wrap" id="dialogListWrap" ref="dialogListWrap">
                    <div class="dialog_history" v-if="dialogHistoryList.length > 0">
                        <div class="dialog_history_item" v-for="item in dialogHistoryList">
                            <div class="request">
                                <span>{{ item.request }}</span>
                            </div>
                            <div class="response">
                                <span>{{ item.response }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="dialog_new" v-if="dialogNewList.length > 0">
                        <div class="dialog_new_item" v-for="(item,index) in dialogNewList" :key="item.id" :id="'dialog_new_item'+index">
                            <div class="request">
                                <div class="request_file_wrap" v-if="!!item.request.uploadList && item.request.uploadList.length > 0">
                                    <div class="request_file" v-for="file in item.request.uploadList">
                                        <span class="request_file_icon" v-if="file.fileName.indexOf('.doc')>-1 || file.fileName.indexOf('.docx')>-1">
                                            <i class="iconfont icon-word"></i>
                                        </span>
                                        <span class="request_file_icon" v-else-if="file.fileName.indexOf('.txt')>-1">
                                            <i class="iconfont icon-txt"></i>
                                        </span>
                                        <span class="request_file_icon" v-else-if="file.fileName.indexOf('.pdf')>-1">
                                            <i class="iconfont icon-pdf"></i>
                                        </span>
                                        <span class="request_file_name">{{ file.fileName }}</span>
                                    </div>
                                </div>
                                <div class="request_details">
                                    <p v-if="!!item.request.selectedBiz && !!item.request.selectedBizName">{{ item.request.selectedBizName }}</p>
                                    <p v-if="!!item.request.demandDesc">{{ item.request.demandDesc }}</p>
                                </div>
                            </div>
                            <div class="response_loading" v-if="showCasesLoading && index === (dialogNewList.length-1)">
                                <img src="../../03images/AI/AILoading.gif" />
                            </div>
                            <div class="response" v-if="!!item.response && item.response.length > 0">
                                <div class="response_table_wrap">
                                    <i-table class="response_table" :data="item.response" :columns="testCasesColumns" border></i-table>
                                </div>
                                <div class="table_operate">
                                    <span style="margin-right: 17px;" @click="copyTable(item.response)"><i class="iconfont icon-copy1"></i></span>
                                    <span style="color: #65CE7A;" @click="showSubmit(item.response)">
                                        <Poptip class="submit_poptip" trigger="hover" v-model="item.poptip" placement="right">
                                            <span style="color: #65CE7A;"><i class="iconfont icon-gou"></i></span>
                                            <div class="submit_poptip_ct" slot="content">
                                                <span style="color: #666;line-height: 24px;">{{ submitPoptipContent }}</span>
                                            </div>
                                        </Poptip>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="to_bottom_wrap">
                    <div class="to_bottom_ct_wrap">
                        <div class="to_bottom_ct" v-show="showToBottom">
                            <div class="bottom_icon_wrap" @click="scrollToBottom">
                                <span><i class="iconfont icon-downArrow"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dialog_wrap" ref="dialogWrap">
                    <div class="input_and_doc" ref="inputAndDoc">
                        <div class="file_wrap_scroll" ref="fileWrapScroll" v-show="uploadList.length > 0">
                            <div @click="fileFocusScroll('left')" class="file_tab_btn file_tab_left" v-if="focusFileIndex > 0">
                                <i class="iconfont icon-rightArrow"></i>
                            </div>
                            <div class="files_wrap" ref="focusFile" :style="{left:focusFileLeft+'px'}">
                                <span class="file_item" :ref="'fileFocus'+index" v-for="(item,index) in uploadList">
                                    <span>{{ item.fileName }}</span>
                                    <span class="icon_wrap" @click="removeFile(item)">
                                        <i class="iconfont icon-close"></i>
                                    </span>
                                </span>
                            </div>
                            <div @click="fileFocusScroll('right')" class="file_tab_btn file_tab_right" v-if="isFocusRightShow">
                                <i class="iconfont icon-rightArrow"></i>
                            </div>
                        </div>
                        <div class="input_wrap">
                            <i-input ref="descInput" v-model="demandDesc" type="textarea" placeholder="请输入需求描述，可通过回车键发送" @on-change="resizeDivHeight" @on-enter="generateTestCases"></i-input>
                        </div>
                    </div>
                    <div class="operate_wrap">
                        <div class="icon_wrap">
                            <Upload
                                    ref="upload"
                                    :header="header"
                                    :before-upload="beforeUpload"
                                    :on-success="uploadSuccess"
                                    :action="uploadUrl"
                                    :max-size="uploadFileSize"
                                    :on-exceeded-size="handleMaxSize"
                                    :on-format-error="handFormatError"
                                    :on-error="uploadError"
                                    :format="format"
                                    :data="uploadData"
                                    :show-upload-list="true"
                                    multiple>
                                <i class="iconfont icon-attachment"></i>
                            </Upload>
                        </div>
                        <div class="icon_wrap" @click="getAIWord(true)">
                            <i class="iconfont icon-quotationMark"></i>
                        </div>
                        <div class="choose_demand">
                            <div class="biz_label">选择需求：</div>
                            <div class="biz_wrap">
                                <i-select v-model="selectedBiz" :remote-method="getPrdLinkedBizs" :loading="queryLinkedBizLoading" placeholder="请输入业务编号模糊查询" filterable clearable>
                                    <i-option v-for="qc_biz in linkedBizList" :value="qc_biz.id" :key="qc_biz.id" :label="qc_biz.codeAndName">{{qc_biz.codeAndName}}</i-option>
                                </i-select>
                            </div>
                        </div>
                        <div class="model_wrap">
                            <div class="model_ct_wrap">
                                <Poptip class="model_list_poptip" title="选择模型" placement="top" trigger="click">
                                    <span>{{ aiModel }}</span>
                                    <div class="model_list" slot="content">
                                        <div class="model_item" v-for="item in aiModelList" :key="item.id" :value="item.id" :class="{ selected: item.id === aiModel }" @click="aiModel = item.id">
                                            <span>{{ item.defName }}</span>
                                            <span class="sel_icon" v-show="item.id === aiModel"><i class="iconfont icon-tick"></i></span>
                                        </div>
                                    </div>
                                </Poptip>
                            </div>
                        </div>
                        <div class="icon_wrap send_wrap" :class="{upload_disabled: (!selectedBiz && !demandDesc && uploadList.length === 0) || showCasesLoading}" @click="generateTestCases">
                            <i class="iconfont icon-submitA"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <Modal v-model="showAIWordModal" title="提示词" width="800" class-name="vertical-center-modal prompt_word">
            <div class="word_wrap" ref="wordWrap">
                <div class="user_word">
                    <p>用户提示词：</p>
                    <i-input ref="userWordInput" type="textarea" v-model="tempAIWord"></i-input>
                </div>
                <div class="sys_word" style="margin-top: 8px;" ref="sysWordDiv">
                    <div style="height: 21px;"><p>系统提示词：</p></div>
                    <div class="sys_word_ct" style="height: calc(100% - 21px);color: #515a6e;overflow-y: auto;">
                        <p style="white-space: pre-line;">{{ sysAIWord }}</p>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <i-button type="primary" @click="resetAIWord">重置</i-button>
                <i-button type="primary" @click="updateAIWord" :loading="updateAIWordLoading">更新</i-button>
            </div>
        </Modal>

        <Modal v-model="showEditTable" title="编辑测试用例" width="1200" class-name="vertical-center-modal edit_table">
            <i-table :data="editTableData" :columns="editTableColumns" border stripe></i-table>
            <div slot="footer">
                <i-button type="text" @click="cancelEditTable">取消</i-button>
                <i-button type="primary" @click="submitEditTable" :loading="submitLoading">
                    <span v-if="!submitLoading">提交</span>
                    <span v-if="submitLoading">提交中</span>
                </i-button>
            </div>
        </Modal>
    </div>

    <script type="text/javascript">
        verifyLogin();
        var prjBizHttpRequest = linkus.location.prjbiz;
        var prjUserHttpRequest 	= linkus.location.prjuser;
        var passportHttpRequest = linkus.location.passport;
        var queryHttpRequest = linkus.location.query;
        var queryReportHttpRequest = linkus.location.report;
        var bizDomain = linkus.location.prjbiz;
        var queryDomain = linkus.location.query;
        var kmDomain = linkus.location.km;
        var prjDomain = linkus.location.prj;
        var biz = linkus.location.prjbiz;
        var evtObj = new Vue();
        var vue = new Vue({
            el: '#main',
            data: function() {
                var sf = this;
                return {
                    from: '',
                    prdId: '',
                    prdCtlgId: '',
                    testPlanId: '',
                    bizTypeId: '5c403daae0ee7730f883fcac',
                    aiSceneId: '6849406ac935b27066998e49',

                    dialogId: '',// 会话id
                    dialogName: '',
                    dialogList: [],
                    dialogHistoryList: [],
                    dialogNewList: [],

                    selectedBiz: '',
                    selectedBizName: '',
                    queryLinkedBizLoading: false,
                    queryPrdLinkedBizsAjax: null,
                    linkedBizList: [],
                    linkedBizId: '',

                    demandDesc: '',

                    header: {'Content-Type': 'multipart/form-data'},
                    uploadUrl: linkus.location.prjbiz + '/testCaseMgtCtrl/uploadRequirementFile',
                    uploadFileSize: 1048576,
                    // 上传文档
                    format: ['doc', 'docx', 'txt', 'pdf'],
                    uploadData: {},
                    uploadList: [],
                    // 附件
                    attUploadUrl: linkus.location.km + '/kmFileCtrl/pdfConvertUpload.action',
                    fileUploadData: {},

                    promptWord: '',
                    promptWordInit: '',
                    aiModel: 'Qwen2.5-72B-Instruct',
                    aiModelList: [
                        {
                            id: 'qwen-plus',
                            defName: 'qwen-plus'
                        },
                        {
                            id: 'Qwen2.5-72B-Instruct',
                            defName: 'Qwen2.5-72B-Instruct'
                        },
                        {
                            id: 'Qwen2.5-14B-Instruct',
                            defName: 'Qwen2.5-14B-Instruct'
                        },
                        {
                            id: 'Qwen3-32B',
                            defName: 'Qwen3-32B'
                        },
                    ],
                    showCasesLoading: false,
                    submitLoading: false,
                    testCases: [],
                    testCasesColumns: [
                        {
                            type: 'index',
                            title: '序',
                            width: 39
                        },
                        {
                            title: '用例名称',
                            key: 'name',
                            width: 93
                        },
                        {
                            title: '前置条件',
                            key: 'preConditions',
                            width: 157
                        },
                        {
                            title: '测试步骤',
                            key: 'steps',
                            width: 157
                        },
                        {
                            title: '预期结果',
                            key: 'expectedResults',
                            width: 181
                        },
                        {
                            title: '优先级',
                            key: 'priority',
                            width: 'auto'
                        },
                    ],
                    priorityList: [
                        {
                            id: 'top',
                            defName: '高'
                        },
                        {
                            id: 'middle',
                            defName: '中'
                        },
                        {
                            id: 'bottom',
                            defName: '低'
                        },
                    ],

                    showAIWordModal: false,
                    tempAIWord: '',
                    updateAIWordLoading: false,
                    sysAIWord: '',

                    showEditTable: false,
                    editTableData: [],
                    editTableColumns: [
                        {
                            type: 'index',
                            title: '序',
                            width: 39
                        },
                        {
                            title: '用例名称',
                            key: 'name',
                            width: 246,
                            render: function(h, params) {
                                var editItemList = sf.editItemIndex.split('-');
                                if(!params.row.name || (!!editItemList[0] && !!editItemList[1] && editItemList[0] == params.index && editItemList[1] == params.column._index)){
                                    return h('Input',{
                                        props: {
                                            type: 'textarea',
                                            value: params.row.name,
                                            rows: !params.row.name ? 1 : 4,
                                        },
                                        on: {
                                            'on-change': (event)=>{
                                                sf.editItemChange(event.target.value,params.column.key);
                                            },
                                            'on-focus': ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            },
                                            'on-blur': ()=>{
                                                sf.editItemIndex = '';
                                            }
                                        }
                                    },params.row.name);
                                }else{
                                    return h('div',{
                                        on:{
                                            click: ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            }
                                        }
                                    },params.row.name);
                                }
                            }
                        },
                        {
                            title: '前置条件',
                            key: 'preConditions',
                            width: 246,
                            render: function(h, params) {
                                var editItemList = sf.editItemIndex.split('-');
                                if(!params.row.preConditions || (!!editItemList[0] && !!editItemList[1] && editItemList[0] == params.index && editItemList[1] == params.column._index)){
                                    return h('Input',{
                                        props: {
                                            type: 'textarea',
                                            value: params.row.preConditions,
                                            rows: !params.row.preConditions ? 1 : 4,
                                        },
                                        on: {
                                            'on-change': (event)=>{
                                                sf.editItemChange(event.target.value,params.column.key);
                                            },
                                            'on-focus': ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            },
                                            'on-blur': ()=>{
                                                sf.editItemIndex = '';
                                            }
                                        }
                                    },params.row.preConditions);
                                }else{
                                    return h('div',{
                                        on:{
                                            click: ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            }
                                        }
                                    },params.row.preConditions);
                                }
                            }
                        },
                        {
                            title: '测试步骤',
                            key: 'steps',
                            width: 246,
                            render: function(h, params) {
                                var editItemList = sf.editItemIndex.split('-');
                                if(!params.row.steps || (!!editItemList[0] && !!editItemList[1] && editItemList[0] == params.index && editItemList[1] == params.column._index)){
                                    return h('Input',{
                                        props: {
                                            type: 'textarea',
                                            value: params.row.steps,
                                            rows: !params.row.steps ? 1 : 4,
                                        },
                                        on: {
                                            'on-change': (event)=>{
                                                sf.editItemChange(event.target.value,params.column.key);
                                            },
                                            'on-focus': ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            },
                                            'on-blur': ()=>{
                                                sf.editItemIndex = '';
                                            }
                                        }
                                    },params.row.steps);
                                }else{
                                    return h('div',{
                                        on:{
                                            click: ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            }
                                        }
                                    },params.row.steps);
                                }
                            }
                        },
                        {
                            title: '预期结果',
                            key: 'expectedResults',
                            width: 246,
                            render: function(h, params) {
                                var editItemList = sf.editItemIndex.split('-');
                                if(!params.row.expectedResults || (!!editItemList[0] && !!editItemList[1] && editItemList[0] == params.index && editItemList[1] == params.column._index)){
                                    return h('Input',{
                                        props: {
                                            type: 'textarea',
                                            value: params.row.expectedResults,
                                            rows: !params.row.expectedResults ? 1 : 4,
                                        },
                                        on: {
                                            'on-change': (event)=>{
                                                sf.editItemChange(event.target.value,params.column.key);
                                            },
                                            'on-focus': ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            },
                                            'on-blur': ()=>{
                                                sf.editItemIndex = '';
                                            }
                                        }
                                    },params.row.expectedResults)
                                }else{
                                    return h('div',{
                                        on:{
                                            click: ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            }
                                        }
                                    },params.row.expectedResults);
                                }
                            }
                        },
                        {
                            title: '优先级',
                            key: 'priority',
                            width: 80,
                            render: function(h, params) {
                                var editItemList = sf.editItemIndex.split('-');
                                if(!params.row.priority || (!!editItemList[0] && !!editItemList[1] && editItemList[0] == params.index && editItemList[1] == params.column._index)){
                                    var priority = '';
                                    if(params.row.priority === '高'){
                                        priority = 'top';
                                    }else if(params.row.priority === '中'){
                                        priority = 'middle';
                                    }else if(params.row.priority === '低'){
                                        priority = 'bottom';
                                    }
                                    var options = [];
                                    options = sf.priorityList.map(function(item){
                                        return h('Option', {
                                            props: {
                                                value: item.id,
                                                label: item.defName,
                                            }
                                        });
                                    });
                                    return h('Select',{
                                        props: {
                                            transfer: true,
                                            value: priority,
                                        },
                                        on: {
                                            'on-change': (value)=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                                sf.editItemChange(value,params.column.key);
                                            }
                                        }
                                    },options);
                                }else{
                                    return h('div',{
                                        on:{
                                            click: ()=>{
                                                sf.editItemIndex = '' + params.index + '-' + params.column._index;
                                            }
                                        }
                                    },params.row.priority);
                                }
                            }
                        },
                        {
                            title: '操作',
                            key: 'operate',
                            width: 60,
                            render: function(h, params) {
                                return h('span',[
                                    h('i',{
                                        class: 'iconfont icon-plus cursor',
                                        style: {
                                            fontSize: '16px',
                                            color: '#3883e5',
                                        },
                                        on: {
                                            click: ()=>{
                                                sf.addTestCase(params.index);
                                            }
                                        }
                                    }),
                                    h('i',{
                                        class: 'iconfont icon-minusSign cursor',
                                        style: {
                                            fontSize: '16px',
                                            color: '#3883e5',
                                            margin: '0 0 0 5px'
                                        },
                                        on: {
                                            click: ()=>{
                                                sf.deleteTestCase(params.index);
                                            }
                                        }
                                    })
                                ])
                            }
                        }
                    ],
                    editItemIndex: '',

                    focusFileId: '',
                    isFocusRightShow: false,
                    focusFileWidth: 0,
                    focusFileIndex: 0,
                    focusFileLeft: 0,

                    submitPoptipContent: '点击提交，对生成的测试用例进行查看、编辑、入库',
                    showToBottom: false,
                }
            },
            created: function () {
                var sf = this;
                Vue.evtHub = new Vue();

                if(!!sf.getQueryString('from')){
                    sf.from = sf.getQueryString('from');
                }
                if(!!sf.getQueryString('prdId')){
                    sf.prdId = sf.getQueryString('prdId');
                }
                if(!!sf.getQueryString('prdCtlgId')){
                    sf.prdCtlgId = sf.getQueryString('prdCtlgId');
                }
                if(!!sf.getQueryString('testPlanId')){
                    sf.testPlanId = sf.getQueryString('testPlanId');
                }

                sf.createDialog();
                sf.getAIWord(false);
            },
            mounted: function () {
                var sf = this;

                var dialogListWrap = document.getElementById('dialogListWrap');
                dialogListWrap.addEventListener('scroll', sf.handleScroll);
            },
            beforeDestroy: function(){
                var sf = this;

                var dialogListWrap = document.getElementById('dialogListWrap');
                dialogListWrap.removeEventListener('scroll', sf.handleScroll);
            },
            watch: {
                selectedBiz: function(n,o){
                    var sf = this;
                    sf.demandDesc = '';
                    sf.uploadList = [];
                    sf.uploadData = {};
                    if(!!n){
                        sf.getBizView(n);
                        sf.loadAttachment(n);
                    }
                    if(!!sf.selectedBiz){
                        sf.uploadUrl = linkus.location.km + '/kmFileCtrl/pdfConvertUpload.action';
                    }else{
                        sf.uploadUrl = linkus.location.prjbiz + '/testCaseMgtCtrl/uploadRequirementFile';
                    }
                    sf.$nextTick(()=>{
                        sf.initDivHeight();
                    });
                },
                demandDesc: function(n,o){
                    var sf = this;
                    sf.promptWordAdd = n;
                    sf.promptWord = sf.promptWordInit + sf.promptWordAdd;
                },
                uploadList: function(n,o){
                    var sf = this;
                    sf.$nextTick(function () {
                        sf.focusFileIndex = 0;
                        sf.focusFileLeft = 0;
                        sf.focusFileWidth = sf.$refs.focusFile.scrollWidth;
                        if(sf.focusFileWidth > 774){
                            sf.isFocusRightShow = true;
                        }else{
                            sf.isFocusRightShow = false;
                        }
                    });
                }
            },
            methods: {
                handleScroll: function(){
                    var sf = this;

                    var dialogListWrap = document.getElementById('dialogListWrap');
                    var scrollPosition = dialogListWrap.scrollTop;
                    var elementHeight = dialogListWrap.scrollHeight;
                    var viewHeight = dialogListWrap.clientHeight;

                    // 判断是否滚动到元素底部
                    if((scrollPosition + viewHeight + 1) >= elementHeight){
                        sf.showToBottom = false;
                    }else{
                        sf.showToBottom = true;
                    }
                },
                getQueryString: function(name) {
                    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                    var r = window.location.search.substr(1).match(reg);
                    if (r != null) return unescape(r[2]);
                    return null;
                },
                //时间转换
                formatDate: function (value) {
                    var date = new Date(value);
                    var y = date.getFullYear();
                    var MM = date.getMonth() + 1;
                    MM = MM < 10 ? ('0' + MM) : MM;
                    var d = date.getDate();
                    d = d < 10 ? ('0' + d) : d;
                    var h = date.getHours();
                    h = h < 10 ? ('0' + h) : h;
                    var m = date.getMinutes();
                    m = m < 10 ? ('0' + m) : m;
                    return y + '-' + MM + '-' + d + ' ' + h + ':' + m;
                },
                // 创建会话
                createDialog: function(){
                    var sf = this;

                    var today = new Date();
                    var date = sf.formatDate(today);
                    // yyyy-mm-dd hh24:mm+"测试用例生成"
                    var param = {
                        aiSceneId: sf.aiSceneId,
                        dialogName: date + '测试用例生成'
                    }

                    $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/dialog/create',
                        type: 'post',
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(param),
                    }).then(function (res) {
                        if(res.success){
                            sf.dialogId = res.data.id || '';
                            sf.dialogName = res.data.name || '';

                            sf.queryDialogList(true);
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                    });
                },
                // 查询会话列表
                queryDialogList: function(init){
                    var sf = this;

                    var param = {};
                    if(init){
                        param = {
                            aiSceneId: sf.aiSceneId
                        }
                    }else{
                        param = {
                            dialogId: sf.dialogId,
                            dialogName: sf.dialogName,
                            aiSceneId: sf.aiSceneId
                        }
                    }

                    $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/dialog/list',
                        type: 'post',
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(param),
                    }).then(function (res) {
                        if(res.success){
                            if(res.data.length > 3){
                                sf.dialogList = res.data.slice(0,3);
                            }else{
                                sf.dialogList = res.data || [];
                            }

                            sf.dialogHistoryList = [];
                            if(sf.dialogList.length > 0){
                                sf.dialogList.forEach(function(item){
                                    sf.queryDialogHistory(item.id);
                                });
                            }
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                    });
                },
                // 查询会话历史
                queryDialogHistory: function(id){
                    var sf = this;

                    $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/dialog/history?dialogId=' + id,
                        type: 'get',
                    }).then(function (res) {
                        if(res.success){
                            let data = res.data || [];
                            sf.dialogHistoryList = sf.dialogHistoryList.concat(data);
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                    });
                },
                // 获取业务详细信息
                getBizView: function(bizId){
                    var sf = this;
                    $.ajax({
                        url: linkus.location.prjbiz + '/bizView/getBizView.action',
                        type: 'post',
                        async: true,
                        data: {
                            bizId: bizId
                        },
                    }).then(function (data) {
                        sf.demandDesc = sf.demandDesc + data.name + '\n';
                        sf.selectedBizName = data.code + '/' + data.name;
                        if(!!data.desc){
                            sf.demandDesc = sf.demandDesc+ data.desc + '\n';
                        }
                    });
                },
                // 获取业务附件
                loadAttachment: function (bizId) {
                    var sf = this;
                    var bizViewVo = {
                        bizId: bizId,
                        isCheckAllBizAttachments: false
                    }
                    $.ajax({
                        url: linkus.location.prjbiz + '/bizView/getBizFiles.action',
                        type: 'post',
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(bizViewVo),
                    }).then(function (data) {
                        if(!!data && data.length > 0){
                            sf.uploadList = sf.uploadList.concat(data);
                            sf.$nextTick(()=>{
                                sf.resizeDivHeight();
                            });
                        }
                    });
                },
                beforeUpload: function(){
                    var sf = this;
                },
                uploadSuccess: function(res, file, fileList){
                    var sf = this;

                    var fileItem = {
                        fileId: res.data,
                        fileName: file.name
                    }
                    sf.uploadList.push(fileItem);
                    sf.$nextTick(()=>{
                        sf.resizeDivHeight();
                    });
                    if(!!sf.selectedBiz){
                        sf.uploadBizFile(res,sf.selectedBiz);
                    }
                },
                // 上传附件
                uploadBizFile: function(data,bizId){
                    var sf = this;

                    $.ajax({
                        url: linkus.location.prjbiz + '/bizView/fileAddToBiz.action?t=' + new Date().getTime(),
                        data: {
                            bizId: bizId,
                            filedId: data.idValue
                        },
                    }).then(function (data) {});
                },
                //大小限制
                handleMaxSize: function(file, fileList){
                    var sf = this;
                    var size = sf.uploadFileSize / 1024;
                    sf.$Message.warning({
                        content: '文件大小不能超过' + size + "M！",
                        duration: 5
                    });
                },
                handFormatError: function(file, fileList){
                    var sf = this;
                    sf.$Message.warning({content: '不支持上传该格式的文档！', duration: 5});
                },
                //上传失败
                uploadError: function(param){
                    var sf = this;
                    sf.$Message.warning('上传失败，请联系管理员',3);
                },
                // 生成测试用例
                generateTestCases: function() {
                    var sf = this;

                    if(sf.showCasesLoading){
                        return ;
                    }
                    if(!sf.selectedBiz && !sf.demandDesc && sf.uploadList.length === 0){
                        sf.$Message.error({
                            content: '请填写需求信息！',
                            duration: 3
                        });
                        return ;
                    }
                    if(!sf.aiModel){
                        sf.$Message.error({
                            content: '请选择AI模型！',
                            duration: 3
                        });
                        return ;
                    }
                    if(!sf.dialogId){
                        return ;
                    }

                    sf.testCases = [];
                    sf.showCasesLoading = true;
                    sf.linkedBizId = sf.selectedBiz;

                    var param = {};
                    var uploadListId = [];
                    if(!!sf.selectedBiz){
                        param.bizIds = [sf.selectedBiz];
                    }else{
                        if(sf.uploadList.length > 0){
                            uploadListId = sf.uploadList.map(function(item){
                                return item.fileId;
                            });
                        }
                    }
                    if(uploadListId.length > 0){
                        param.fileIds = uploadListId;
                    }
                    if(!!sf.demandDesc){
                        param.requirement = sf.demandDesc;
                    }

                    if(!!sf.promptWord){
                        param.prompt = sf.promptWord;
                    }
                    param.llm = sf.aiModel;
                    if(!!sf.dialogId){
                        param.dialogId = sf.dialogId;
                    }

                    var dialogItem = {
                        id: sf.dialogNewList.length.toString(),
                        dialogId: sf.dialogId,
                        request: {},
                        response: [],
                        poptip: false,
                    }
                    if(sf.dialogNewList.length === 0){
                        dialogItem.poptip = true;
                    }
                    if(!!sf.selectedBiz){
                        dialogItem.request.selectedBiz = sf.selectedBiz;
                        dialogItem.request.selectedBizName = sf.selectedBizName;
                    }
                    if(!!sf.demandDesc){
                        dialogItem.request.demandDesc = sf.demandDesc;
                    }
                    if(sf.uploadList.length > 0){
                        dialogItem.request.uploadList = sf.uploadList;
                    }
                    sf.dialogNewList.push(dialogItem);
                    sf.reset();
                    // 重置
                    sf.$nextTick(()=>{
                        sf.initDivHeight();
                    });

                    $.ajax({
                        url: linkus.location.prjbiz + '/testCaseMgtCtrl/aiGenTestcases',
                        type: 'post',
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(param),
                    }).then(function(res){
                        sf.showCasesLoading = false;
                        if(!!res && res.success){
                            sf.testCases = res.data || [];

                            sf.dialogNewList[sf.dialogNewList.length-1].response = sf.testCases.map(function(item){
                                let item0 = {...item};
                                return item0;
                            });
                            if(sf.dialogNewList.length > 0){
                                sf.showToBottom = true;
                            }else{
                                sf.showToBottom = false;
                            }
                            if(sf.dialogNewList.length === 1 && sf.dialogNewList[0].poptip){
                                setTimeout(()=>{
                                    sf.dialogNewList[0].poptip = false;
                                },3000);
                            }
                        }else{
                            sf.$Message({
                                content: res.message,
                                duration: 3
                            });
                        }
                    });
                },
                // 重置
                reset: function(){
                    var sf = this;

                    sf.selectedBiz = '';
                    sf.selectedBizName = '';
                    sf.demandDesc = '';
                    sf.uploadList = [];
                    sf.uploadData = {};
                    sf.uploadUrl = linkus.location.prjbiz + '/testCaseMgtCtrl/uploadRequirementFile';
                    sf.promptWord = sf.promptWordInit;
                    sf.promptWordAdd = '';
                    sf.aiModel = 'Qwen2.5-72B-Instruct';
                },
                // 新增测试用例
                addTestCase: function(index){
                    var sf = this;

                    var item = {
                        name: '',
                        steps: '',
                        preConditions: '',
                        expectedResults: '',
                        priority: '',
                        caseCode: '',
                    }
                    sf.$nextTick(function(){
                        sf.editTableData.splice(index+1,0,item);
                    })
                },
                // 删除测试用例
                deleteTestCase: function(index) {
                    var sf = this;

                    sf.$Modal.confirm({
                        title: '删除用例',
                        content: '确定要删除这个测试用例吗？',
                        onOk: ()=>{
                            sf.$nextTick(function(){
                                sf.editTableData.splice(index,1);
                            });
                        },
                        onCancel: ()=>{},
                    });
                },
                // 提交测试用例
                submitTestCases: function(){
                    var sf = this;
                    if(sf.testCases.length > 0){
                        sf.submitLoading = true;

                        var param = {
                            prdId: sf.prdId,
                            prdCtlgId: sf.prdCtlgId,
                            testCases: sf.testCases
                        }
                        if(!!sf.linkedBizId){
                            param.reqBizId = sf.linkedBizId;
                        }
                        if(sf.from === 'batchPlanOperate'){
                            param.testPlanId = sf.testPlanId;
                        }

                        $.ajax({
                            url: linkus.location.prjbiz + '/biz/batchCreateTestCases',
                            type: 'post',
                            headers: {
                                'Content-Type': 'application/json;charset=utf-8'
                            },
                            data: JSON.stringify(param),
                        }).then(function(res){
                            sf.submitLoading = false;
                            sf.showEditTable = false;
                            sf.linkedBizId = '';
                            sf.$Message.success({
                                content: '测试用例提交成功，页面即将跳转！',
                                duration: 3
                            });
                            setTimeout(()=>{
                                if(sf.from === 'testCaseManagement'){
                                    window.location.href = window.location.protocol + '//' + window.location.host + '/linkus-biz/forward.action?t=biz/batchOperate/case/testCaseManagement';
                                }else if(sf.from === 'batchPlanOperate'){
                                    window.location.href = window.location.protocol + '//' + window.location.host + '/linkus-biz/forward.action?t=biz/batchOperate/plan/batchPlanOperate';
                                }
                            },2000);
                        });
                    }else{
                        sf.$Message.warning({
                            content: '无测试用例，不可提交！',
                            duration: 3
                        });
                    }
                },
                // 清空用例
                clearTestsCases: function(){
                    var sf = this;
                    sf.$Modal.confirm({
                        title: '清空用例',
                        content: '确定要清空用例吗？',
                        onOk: ()=>{
                            sf.testCases = [];
                        },
                        onCancel: ()=>{},
                    });
                },
                // 移除上传文件
                removeFile: function(file){
                    var sf = this;
                    sf.uploadList = sf.uploadList.filter(function(item){
                        return item.fileId !== file.fileId;
                    });
                    sf.$nextTick(()=>{
                        sf.resizeDivHeight();
                    });
                },
                // 查询业务
                getPrdLinkedBizs: function(keyWord) {
                    var sf = this;
                    if (sf.queryPrdLinkedBizsAjax && sf.queryPrdLinkedBizsAjax.abort) {
                        sf.queryPrdLinkedBizsAjax.abort();
                    }
                    if (!!keyWord) {
                        sf.queryLinkedBizLoading = true;
                        var param = {
                            conds: [{
                                fieldId: "keyWord",
                                oprtId: "",
                                value: keyWord
                            }, {
                                fieldId: "prdId",
                                oprtId: "",
                                value: sf.prdId
                            }],
                            page: {
                                pageNum: 0,
                                pageSize: 20
                            },
                            prdLinkBizOptionalBizType: {
                                prdId: sf.prdId,
                                bizTypeId: sf.bizTypeId,
                            }
                        };
                        sf.linkedBizList = [];
                        sf.queryPrdLinkedBizsAjax = $.ajax({
                            headers: {'Content-Type': 'application/json;charset=utf-8'},
                            type: "post",
                            url: linkus.location.prjbiz + "/biz/fuzzyQueryBizByCode.action",
                            data: JSON.stringify(param),
                        });
                        sf.queryPrdLinkedBizsAjax.then(function (data) {
                            sf.queryLinkedBizLoading = false;
                            var dataList = data.objectList || [];
                            if (!!dataList && dataList.length > 0) {
                                for (var i = 0; i < dataList.length; i++) {
                                    var name = dataList[i].code + "/" + (!!dataList[i].name ? dataList[i].name : "");
                                    var tempObj = {
                                        id: dataList[i].id,
                                        codeAndName: name
                                    };
                                    sf.linkedBizList.push(tempObj)
                                }
                            }
                        }, function (err) {
                            sf.queryLinkedBizLoading = false;
                        });
                    }
                },
                // 获取提示词
                getAIWord: function(showModal){
                    var sf = this;

                    $.when(sf.getUserAIWord(),sf.getSysAIWord()).then(function(userWord,sysWord){
                        if(userWord && userWord[1] == 'success'){
                            sf.promptWordInit = userWord[0].data || '';
                            sf.promptWord = userWord[0].data || '';

                            sf.tempAIWord = userWord[0].data || '';

                        }
                        if(sysWord && sysWord[1] == 'success'){
                            sf.sysAIWord = sysWord[0].data.promptFixed || '';
                        }
                        if(showModal){
                            sf.showAIWordModal = true;

                            sf.$nextTick(()=>{
                                const textarea = sf.$refs.userWordInput.$el.querySelector('textarea');
                                textarea.style.height = 'auto';

                                const wordWrap = sf.$refs.wordWrap;
                                const sysWordDiv = sf.$refs.sysWordDiv;
                                if(sysWordDiv.scrollHeight > wordWrap.clientHeight/2){
                                    sysWordDiv.style.height = wordWrap.clientHeight/2 - 8 + 'px';
                                    textarea.style.height = wordWrap.clientHeight/2 - 29 + 'px';
                                }else{
                                    textarea.style.height = wordWrap.clientHeight - sysWordDiv.clientHeight - 29 + 'px';
                                }
                            })
                        }
                    });
                },
                // 获取用户提示词
                getUserAIWord: function(){
                    var sf = this;

                    var param = {
                        aiSceneId: sf.aiSceneId
                    }
                    return $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/prompt/getAiSceneEmpPrompt?aiSceneId=' + sf.aiSceneId,
                        type: 'get',
                    });
                },
                // 重置提示词
                resetAIWord: function(){
                    var sf = this;
                    sf.tempAIWord = sf.promptWordInit;
                },
                getSysAIWord: function(){
                    var sf = this;

                    return $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/prompt/getAiSceneSystemPrompt?aiSceneId=' + sf.aiSceneId,
                        type: 'get',
                    });
                },
                // 更新提示词
                updateAIWord: function(){
                    var sf = this;

                    var param = {
                        aiSceneId: sf.aiSceneId,
                        prompt: sf.tempAIWord,
                    }
                    sf.updateAIWordLoading = true;

                    $.ajax({
                        url: linkus.location.rsc + '/linkus-ai/ai/lsm/prompt/updateAiSceneEmpPrompt',
                        type: 'post',
                        headers: {
                            'Content-Type': 'application/json;charset=utf-8'
                        },
                        data: JSON.stringify(param),
                    }).then(function (res) {
                        sf.promptWord = sf.tempAIWord;
                        if(res.success){
                            sf.$Message.success({
                                content: '提示词更新成功',
                                duration: 3
                            });
                        }else{
                            sf.$Message.error({
                                content: res.message,
                                duration: 3
                            });
                        }
                        sf.updateAIWordLoading = false;
                        sf.showAIWordModal = false;
                    });
                },
                // 复制表单数据
                copyTable: function(tableData){
                    var sf = this;

                    var textArea = document.createElement('textarea');
                    var contentForShare = [];

                    contentForShare = tableData.map(function(item){
                        let item0 = {
                            '用例编号': item.caseCode,
                            '用例名称': item.name,
                            '前置条件': item.preConditions,
                            '测试步骤': item.steps,
                            '预期结果': item.expectedResults,
                            '优先级': item.priority
                        };
                        return item0;
                    })

                    textArea.textContent = JSON.stringify(contentForShare,null,2);
                    textArea.style.position = 'fixed';
                    document.body.appendChild(textArea);
                    textArea.select();

                    try {
                        document.execCommand('copy'); // 把要复制的内容拷贝到剪贴板
                        sf.$Message.success('复制成功', 3);
                    } catch (ex) {
                        alert('复制失败');
                        return false;
                    } finally {
                        document.body.removeChild(textArea); // 移除插入的文本域节点
                    }
                },
                showSubmit: function(tableData){
                    var sf = this;

                    sf.editTableData = tableData.map(function(item){
                        let item0 = {...item}
                        return item0;
                    });
                    sf.showEditTable = true;
                },
                // 表格编辑
                editItemChange: function(value,key){
                    var sf = this;

                    var editItemList = sf.editItemIndex.split('-');
                    let index = parseInt(editItemList[0]);

                    if(key === 'priority'){
                        let priority = '';
                        if(value === 'top'){
                            priority = '高';
                        }else if(value === 'middle'){
                            priority = '中';
                        }else if(value === 'bottom'){
                            priority = '低';
                        }
                        sf.editTableData[index]['priority'] = priority;
                        sf.editItemIndex = '';
                    }else{
                        sf.editTableData[index][key] = value;
                    }
                },
                // 取消编辑
                cancelEditTable: function(){
                    var sf = this;

                    sf.editTableData = sf.testCases.map(function(item){
                        let item0 = {...item}
                        return item0;
                    });
                    sf.showEditTable = false;
                },
                // 提交编辑
                submitEditTable: function(){
                    var sf = this;

                    sf.testCases = sf.editTableData.map(function(item){
                        let item0 = {...item}
                        return item0;
                    });
                    sf.submitTestCases();
                },
                // 上传文件滚动
                fileFocusScroll: function(type){
                    var sf = this;
                    var index = type === 'right' ? sf.focusFileIndex : sf.focusFileIndex-1;
                    var refKey = 'fileFocus'+index;
                    var scrollWidth =  sf.$refs[refKey][0].clientWidth;
                    if(type === 'right'){
                        sf.focusFileIndex++;
                        sf.focusFileLeft = sf.focusFileLeft - scrollWidth - 6;
                    }else{
                        sf.focusFileIndex--;
                        sf.focusFileLeft = sf.focusFileLeft + scrollWidth + 6;
                    }
                    if(sf.focusFileWidth + sf.focusFileLeft < 774){
                        sf.isFocusRightShow = false;
                    }else{
                        sf.isFocusRightShow = true;
                    }
                },
                resizeDivHeight: function (){
                    var sf = this;

                    if(sf.uploadList.length === 0 && !sf.demandDesc){
                        sf.$nextTick(()=>{
                            sf.initDivHeight();
                        });
                        return ;
                    }
                    const bodyWrap = sf.$refs.bodyWrap;
                    const bodyWrapHeight = bodyWrap.scrollHeight;

                    const textarea = sf.$refs.descInput.$el.querySelector('textarea');
                    textarea.style.height = 'auto';
                    if(sf.uploadList.length > 0){
                        if(textarea.scrollHeight <= 148){
                            textarea.style.height = `${textarea.scrollHeight}px`;
                        }else{
                            textarea.style.height = '148px';
                        }
                    }else{
                        if(textarea.scrollHeight <= 186){
                            textarea.style.height = `${textarea.scrollHeight}px`;
                        }else{
                            textarea.style.height = '186px';
                        }
                    }

                    const dialogWrap = sf.$refs.dialogWrap;
                    const dialogListWrap = sf.$refs.dialogListWrap;
                    dialogListWrap.style.height = bodyWrapHeight - dialogWrap.scrollHeight - 27 + 'px';

                    sf.handleScroll();
                },
                initDivHeight: function(){
                    var sf = this;

                    const textarea = sf.$refs.descInput.$el.querySelector('textarea');
                    textarea.style.height = '50px';

                    const bodyWrap = sf.$refs.bodyWrap;
                    const dialogListWrap = sf.$refs.dialogListWrap;
                    const dialogWrap = sf.$refs.dialogWrap;

                    dialogListWrap.style.height = bodyWrap.scrollHeight - dialogWrap.scrollHeight - 27 + 'px';

                    sf.handleScroll();
                },
                scrollToBottom: function(){
                    var sf = this;

                    sf.$nextTick(()=>{
                        var dialogListWrap = document.getElementById('dialogListWrap');
                        dialogListWrap.scrollTop = dialogListWrap.scrollHeight;
                        sf.showToBottom = false;
                    });
                }
            },
        });
    </script>
</body>
</html>