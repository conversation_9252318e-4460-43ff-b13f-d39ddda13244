<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>待估任务</title>
    <!--必须引用-->
    <!-- 兼容ie -->
    <script src="../../../00scripts/00lib/ie/browser.js"></script>

    <!-- 本地样式 -->
    <link href="../../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css/ESV/esvStandardStyle.css" rel="stylesheet" type="text/css"/>

    <!-- jQuery -->
    <script src="../../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

    <!-- VUE-->
    <script src="../../../00scripts/00lib/vue/vue.js"></script>
    <script src="../../../00scripts/00lib/vue/vue.min.js"></script>

    <!-- iview -->
    <script src="../../../00scripts/00lib/iview/4.3.2/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../00scripts/00lib/iview/4.3.2/styles/iview.css"/>

    <!-- iconfont字体图标 -->
    <link href="../../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../../01css/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../../00scripts/location/location.js" type="text/javascript"></script>
    <!--检测html页面登录失效-->
    <script src="../../../00scripts/location/verifyLogin.js"></script>

    <!--菜单-->
    <script src="../../../00scripts/esv/menu/esvHeader.js" type="text/javascript"></script>
    <link href="../../../00scripts/esv/menu/esvHeader.css" rel="stylesheet" type="text/css"/>
    
    <!--必须引用end-->


    <!--单独引用组件-->
    <style>

        .esv_warp {
            background: linear-gradient(to top right,#e5f3ff,#fff3e2);
        }
        
        .esv_warp .esv_report_warp{
            position: relative;
        }
        .esv-my-earned{
            position: absolute;
            left: -100px;
            top:-6px;
        }
        .esv-my-earned ul{
            display: flex;
            justify-content: center;
            flex-direction: column;
        }
        .esv-my-earned ul li{
            width: 100px;
            height: 56px;
            line-height: 54px;
            color: #ffffff;
            margin: 6px 0 ;
            border-radius: 4px 0 0 30px;
            text-align: center;
        }
        .esv-my-earned ul li:first-child{
            box-shadow: 0 2px 6px rgba(255,153,0,0.5);
            background: #f90;
        }

        .esv-my-earned ul li:nth-child(2){
            background: rgb(19 143 238);
            box-shadow: 0 2px 6px rgba(19,143,238,0.5);
        }
        
        .esv-my-earned ul li:nth-child(3){
            background: rgb(17 233 219);
            box-shadow: 0 2px 6px rgba(17, 233, 219, 0.5);
        }
        .esv-my-earned ul li:nth-child(4){
            background:  rgba(21,43,83,1);
            box-shadow: 0 2px 6px rgba(21,43,83, 0.5);

        }

        .esv-my-earned ul li:nth-child(5){
            background: rgb(52 72 161);
            box-shadow: 0 2px 6px rgba(52, 72, 161, 0.5);
        }
        .esv-my-earned ul li span{
            padding-right: 5px;
            display: inline-block;
        }
        .esv-my-earned ul li strong{
            font-size: 20px;
        }
        [v-cloak] {
            display: none;
        }
        .esv_content{
            padding-right: 0;
        }
        .esv_list>li{
            display: flex;
            padding: 8px;
        }
        .esv_list>li:nth-child(2n+1) {
            background-color: #f9f9f9;
        }
        .esv_list>li:hover {
            background-color: #ebf7ff;
        }
        .esv_list .esv_info_left{
            width: calc(100% - 150px);
        }
        .esv_list .esv_info_left >div{
            line-height: 25px;
        }
        .esv_list .esv_info_left >div:not(:first-child){
            display: inline-block;
            width: 25%;
            color: #999;
        }
        .esv_list .esv_info_left >div:not(:first-child) span{
            color: #555;
        }
        .esv_list .esv_info_left >div:first-child{
            font-size: 16px;
            color: #138FEE;
            line-height: 30px;
            font-weight: bold;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding-right: 12px;
        }
        
        .esv_list .esv_info_right{
            width: 150px;
            line-height: 55px;
        }
        .esv_list .ivu-input-number-input{
            vertical-align: top;
            line-height: 30px;
            height: 30px;
        }
        .esv_list .esv_info_right .ivu-input-number{
            margin-left: 12px;
        }
        .esv_info_right .drop_down .ivu-dropdown-item div{
            display: block;
            padding: 7px 16px;
        }
        .esv_info_right .drop_down .ivu-dropdown-item{
            padding: 0;
        }
        
        .holiday{
            position: fixed;
            display: flex;
            justify-content: center;
            right: 20px;
            bottom:20px;
            width: 120px;
            height: 64px;
            line-height: 64px;
            text-align: center;
            cursor: pointer;
            background-color: rgba(21,43,83,1);
            color: #ffffff;
            border-radius: 6px;
        }
        .holiday:hover{
            background-color: rgba(21,43,83,.8);
        }

        .holiday img{
            width: 48px;
            height: 48px;
            margin:8px  8px 0 0;
        }
        
        .view_tabs_css .ivu-tabs-nav{
            position: relative;
            left: 50%;
            margin-left: -83px;
            margin-bottom: 15px;
        }
        
        .esv_content{
            margin-top: 12px;
        }
    </style>
</head>
<body>
<div class="esv_warp" id="main" v-cloak>
    <esv-header></esv-header>
    <div class="esv_report_warp">
        <div class="esv-my-earned">
            <ul>
                <li class="ivu-card">
                    <span>今天</span>
                    <strong>{{todayNum}}</strong>
                </li>
                <li class="ivu-card">
                    <span>昨天</span>
                    <strong>{{yesterdayNum}}</strong>
                </li>
                <li class="ivu-card">
                    <span>本周</span>
                    <strong>{{weekNum}}</strong>
                </li>
                <li class="ivu-card">
                    <span>本月</span>
                    <strong>{{monthNum}}</strong>
                </li>
                <li class="ivu-card">
                    <span>今年</span>
                    <strong>{{yearNum}}</strong>
                </li>
            </ul>
        </div>
        <div class="esv_content ivu-card esv_card pd-side16 filter_0" style="position: relative">
            <Spin size="large" fix v-if="listLoading"></Spin>
            <ul class="esv_list scroll_all" style="display: inline-block;width: 100%;height:100%;overflow: auto">
                <li v-for="(item,index) in myAssessedList" :key="index">
                    <div class="esv_info_left">
                        <div>{{item.bizCode}},{{item.bizName}}</div>
                        <div><label>任务名称：</label><span>{{item.name}}</span></div>
                        <div><label>提出人：</label><span>{{item.bizAddUser ? item.bizAddUser.userName : '' }}</span></div>
                        <div><label>计划工时：</label><span>{{item.planEffort }}</span></div>
                    </div>
                    <div class="esv_info_right">
                        <span>
                            余<input-number v-model="item.remainEffort" @on-change="estimateWorkTimeChange(index,item.remainEffort,item.effort)" @on-blur="estimateWorkTime(index,item.id,item.remainEffort,item.effort)"  :min="0"></input-number>
                        </span>
                        <Dropdown trigger="click" class="drop_down" style="margin-left: 20px">
                            <Icon type="md-menu"></Icon>
                            <DropdownMenu slot="list">
                                <Dropdown-Item>
                                    <div  @click="carryOut(index,item.id,0,item.effort)">完成</div>
                                </Dropdown-Item>
                                <Dropdown-Item>
                                    <div @click="addEffort(item)">申请追加</div>
                                </Dropdown-Item>
                                <Dropdown-Item>
                                    <div @click="unclaim(item)">取消认领</div> 
                                </Dropdown-Item>
                                <Dropdown-Item>
                                    <div @click="forward(item)">任务转发</div>
                                </Dropdown-Item>
                                <Dropdown-Item> 
                                    <div @click="presentation(item)">赠送工时</div>
                                </Dropdown-Item>
                                <Dropdown-Item>
                                    <div @click="viewDesc(item)">需求描述</div>
                                </Dropdown-Item>
                            </DropdownMenu>
                        </Dropdown>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="ivu-modal-mask" style="z-index: 1000;" v-show="holidayModal || addModal || viewModal || forwardModal || presentationModal"></div>
    <Modal class="view_modal" draggable v-model="addModal" :styles="{top: '80px'}" class-name="vertical-top-modal" width="500px" title="申请追加">
        <div>
            <i-Form class="view_model_form" ref="formAdd" :model="formData" :rules="rules" :label-width="110">
                <Form-item  label="计划工时（h）">
                    {{selectInfo.planEffort}}
                </Form-item>

                <Form-item  prop="effort" label="申请追加（h）">
                    <input-number v-model="formData.effort" :min="0" style="width: 100%"></input-number>
                </Form-item>

                <Form-item  prop="effortDesc" label="申请原因">
                    <i-input type="textarea" v-model="formData.effortDesc"></i-input>
                </Form-item>
            </i-Form>
        </div>
        <div slot="footer">
            <i-button type="text" @click="cancel">取消</i-button>
            <i-button type="primary" @click="addEffortSave">确定</i-button>
        </div>
    </Modal>

    <Modal class="view_modal" draggable v-model="forwardModal" :styles="{top: '80px'}" class-name="vertical-top-modal" width="500px" title="任务转发">
        <div>
            <i-Form class="view_model_form" ref="formForward" :model="formData" :rules="rules" :label-width="60">
                <Form-item  prop="toRespId" label="转发至">
                    <i-select  v-model="formData.toRespId" filterable>
                        <i-option v-for="(item,index) in personList" :value="item.id" :key="item.id">{{item.userNameCode}}</i-option>
                    </i-select>
                </Form-item>
            </i-Form>
        </div>
        <div slot="footer">
            <i-button type="text" @click="cancel">取消</i-button>
            <i-button type="primary" @click="forwardSave">确定</i-button>
        </div>
    </Modal>


    <Modal class="view_modal" draggable v-model="presentationModal" :styles="{top: '80px'}" class-name="vertical-top-modal" width="500px" title="赠送工时">
        <div>
            <i-Form class="view_model_form" ref="formPresentation" :model="formData" :rules="rules" :label-width="110">
                <Form-item  prop="effortPresentation" label="赠送工时（h）">
                    <input-number @on-change="effortPresentationChange" v-model="formData.effortPresentation"  :min="0" style="width: 100%"></input-number>
                </Form-item>
                <Form-item  prop="toRespId" label="赠送给">
                    <i-select  v-model="formData.toRespId" filterable>
                        <i-option v-for="(item,index) in personList" :value="item.id" :key="item.id">{{item.userNameCode}}</i-option>
                    </i-select>
                </Form-item>
                <Form-item  prop="desc" label="赠送说明">
                    <i-input type="textarea" v-model="formData.desc"></i-input>
                </Form-item>
            </i-Form>
        </div>
        <div slot="footer">
            <i-button type="text" @click="cancel">取消</i-button>
            <i-button type="primary" @click="presentationSave">确定</i-button>
        </div>
    </Modal>



    <Modal class="view_modal" draggable v-model="holidayModal" :styles="{top: '80px'}" class-name="vertical-top-modal" width="500px" title="请假">
        <div>
            <Tabs v-model="holidayTab" class="view_tabs_css">
                <Tab-pane label="按天" name="date">
                    <i-Form class="view_model_form" ref="formHolidayDate" :model="formData" :rules="rules" :label-width="100">
                        <Form-item  prop="starDate" label="开始日期">
                            <Date-Picker :transfer="true" v-model="formData.starDate" type="date" placeholder="请选择开始日期" style="width:100%"></Date-Picker>
                        </Form-item>

                        <Form-item  prop="endDate" label="结束日期">
                            <Date-Picker v-model="formData.endDate" :transfer="true" type="date" placeholder="请选择开结束日期" style="width:100%"></Date-Picker>
                        </Form-item>

                        <Form-item  prop="holidayDesc" label="请假事由">
                            <i-input type="textarea" v-model="formData.holidayDesc"></i-input>
                        </Form-item>
                    </i-Form>
                </Tab-pane>
                <Tab-pane label="按小时" name="hours">
                    <i-Form class="view_model_form" ref="formHolidayHours" :model="formData" :rules="rules" :label-width="100">
                        <Form-item  prop="starDate" label="请假日期">
                            <Date-Picker v-model="formData.starDate" :transfer="true" type="date" placeholder="请选择请假日期" style="width:100%"></Date-Picker>
                        </Form-item>

                        <Form-item  prop="hours" label="请假时长(h)">
                            <input-number
                                    v-model="formData.hours"
                                    :min="0"
                                    :max="8"
                                    style="width: 100%"
                            ></input-number>
                        </Form-item>

                        <Form-item  prop="holidayDesc" label="请假事由">
                            <i-input type="textarea" v-model="formData.holidayDesc"></i-input>
                        </Form-item>
                    </i-Form>
                </Tab-pane>
            </Tabs>
            <p style="font-size: 12px">
                备注：如果您本次提交的请假申请与已申请的请假记录存在冲突（以请假日期为判断依据），系统则会自动将已审批的请假记录删除，只已本次请假为准
            </p>
        </div>
        <div slot="footer">
            <i-button type="text" @click="cancel">取消</i-button>
            <i-button type="primary" @click="holidaySave">确定</i-button>
        </div>
    </Modal>

    <Modal class="view_modal" draggable v-model="viewModal" :styles="{top: '80px'}" class-name="vertical-top-modal" width="500px" title="需求描述">
        <div>
            <p>
                {{selectInfo.bizDesc}}   
            </p>
        </div>
        <div slot="footer"></div>
    </Modal>
 	<Modal  v-model="iframe.createBizModal" :mask-closable="false" @on-cancel="cancelCreateBizModal()" 
        			  class-name="vertical-center-modal embeded-iframe createBizPageNoFooter" width="800" >
            <div slot="header" style="height:22px;">
            	<div class="ivu-modal-header-inner" style="width:25%;float:left;">
            		 <span>任务提交</span>	
            	</div>
       		 	<div style="width:25%;float:left;">
       		 		<span></span>
       		 	</div>
       		 </div>
            <div style="height:450px;">
	            <iframe  :src="iframe.createBizFrameSrc" 
	            		style="width:100%;height:100%;" frameborder="0"></iframe>
            </div>
              <div slot="footer">               	       
        	</div> 
    </Modal>
    <div class="holiday" @click="askForLeave">
        <img src="../../../03images/evs/holiday.png" alt="">
        <span>请假</span>
    </div>
</div>

<script>
    //控制登录失效后HTML页面跳转登录页
    verifyLogin();

    Vue.evtHub  = new Vue();
    vue=new Vue({
        el: '#main',
        data: {
            todayNum:0,
            yesterdayNum:0,
            weekNum:0,
            monthNum:0,
            yearNum:0,

            selectInfo:{},

            addModal:false,
            forwardModal:false,
            presentationModal:false,
            viewModal:false,

            personList:[],

            holidayModal:false,
            todayDate:'',
            holidayTab:'date',
            formData:{
                effort:0,
                effortPresentation:0,
                effortDesc:'',
                desc:'',
                toRespId:'',
                starDate:'',
                endDate:'',
                hours:'',
                holidayDesc:'', 
            },
            rules:{
                effort: [
                    { required: true,type:'number' ,message: '请填写追加工时', trigger: 'blur' }
                ],
                effortDesc: [
                    { required: true, message: '请填写申请原因', trigger: 'blur' }
                ],
                desc: [
                    { required: true, message: '请填写赠送说明', trigger: 'blur' }
                ],
                holidayDesc: [
                    { required: true, message: '请填写请假事由', trigger: 'blur' }
                ],
                toRespId: [
                    { required: true, message: '请选择人员', trigger: 'change' }
                ],
                effortPresentation: [
                    { required: true, type:'number',message: '请填写赠送工时', trigger: 'blur' }
                ],
                starDate: [
                    { required: true,type:'date',message: '请填写请假时间', trigger: 'change' }
                ],
                endDate: [
                    { required: true,type:'date',message: '请填写请假时间', trigger: 'change' }
                ],
                hours: [
                    { required: true,type:'number',message: '请填写请假时长', trigger: 'change' }
                ],
                
            },
            listLoading:false,
            myAssessedList:[],
    		iframe:{
    			createBizFrameSrc:"",
    		    createdBizViewCode:"",
    		    createBizModal:"",
    		    createBizModal:false
    		}
        },
        created:function () {
            var sf =this;
            sf.getMyAssessedList();
            sf.getStatistics();
            var myDate = new Date;
            sf.todayDate = new Date(sf.processDate(myDate));
            sf.formData.starDate = new Date(sf.processDate(myDate));
        },
        mounted: function(){
           
        },
        methods: {
            processDate:function(date){
                var year = date.getFullYear(); //获取当前年
                var mon = date.getMonth() + 1 < 10 ? '0'+(date.getMonth() + 1) : date.getMonth() + 1; //获取当前月
                var date = date.getDate()<10 ? '0'+date.getDate() : date.getDate(); //获取当前日
                return year+'-'+mon+'-'+date+' 00:00:00';
            },
            //获取人员清单
            getPersonList:function(id){
                var sf =this;
                $.ajax({
                    url: linkus.location.scrum + "/scrumCtrl/getDeptUsers.action",
                    data:{
                        taskId:id
                    },
                    success: function (data) {
                       sf.personList= data.objectList || [];
                    },
                    error : function(data) {
                        sf.$Message.error('查询数据失败，请联系管理员',3);
                    }
                });
            },
            //获取统计数据
            getStatistics:function(){
                var sf =this;
                $.ajax({
                    url: linkus.location.scrum + "/scrumCtrl/getMyEarnedValues.action",
                    success: function (data) {
                       sf.todayNum = data.today ? data.today.toFixed(1) : 0;
                       sf.yesterdayNum = data.yesterday ? data.yesterday.toFixed(1) : 0;
                       sf.weekNum = data.week ? data.week.toFixed(1) : 0;
                       sf.monthNum = data.month ? data.month.toFixed(1) : 0;
                       sf.yearNum = data.year ? data.year.toFixed(1) : 0;
                    },
                    error : function(data) {
                        sf.$Message.error('查询数据失败，请联系管理员',3);
                    }
                });
            },
            //我的待估任务
            getMyAssessedList:function(){
                var sf =this;
                sf.listLoading = true;
                $.ajax({
                    url: linkus.location.scrum + "/scrumCtrl/getToEstTaskList.action",
                    data:{
                        pageIndex:0,
                        pageSize:999999,
                        
                    },
                    success: function (data) {
                        sf.listLoading = false;
                        sf.myAssessedList = data.objectList || [];
                        sf.myAssessedList.map(function(item,i){
                            item.effort =  item.remainEffort
                        });
                    },
                    error : function(data) {
                        sf.listLoading = false;
                        sf.$Message.error('查询数据失败，请联系管理员',3);
                    }
                });
            },
            estimateWorkTimeChange:function(index,value,effort){
                var sf =this;
                if(value > effort){
                    sf.$Message.error('剩余工时不能大于剩余工时'+effort+'h',3);
                }
            },
            //剩余工时估算
            estimateWorkTime:function(index,id,value,effort){
                var sf =this;
                if(value > effort){
                    sf.myAssessedList[index].remainEffort = effort;
                    return;
                }
                if(value == 0){
                	this.iframe.createBizModal = true;
                	var sf = this;
                    $.ajax({
                        url: linkus.location.biz + "/flowTaskOprt/getSubmitOprt.action",
                        data: {
                            "flowTaskId": id
                        }
                    }).then(function (oprt) {
                        if (!!oprt) {
                	      sf.iframe.createBizFrameSrc = linkus.location.biz + "/pagecust/getPageCode.action?pageId=" + oprt.pageId
                	    		  + "&prdCtlgId=" + oprt.prdCtlgId + "&bizTypeId=" + oprt.bizTypeId + "&deviceType=1&bizId=" 
                	    		  + oprt.bizId + "&flowTaskId=" + id + "&nodeOprtId=5b9a2cdf900add501a141239&prdId=" + oprt.prdId
    				              + "&isNodePage=true";
                        }
                    })
                }
                else{
	                $.ajax({
	                    url: linkus.location.scrum + "/scrumCtrl/estEffort.action",
	                    data:{
	                        taskId:id,
	                        effort:value,
	                    },
	                    success: function (data) {
	                       sf.getMyAssessedList();
	                       sf.getStatistics();
	                    },
	                    error : function(data) {
	                        sf.$Message.error('查询数据失败，请联系管理员',3);
	                    }
	                });
                }
            },
            //取消认领
            unclaim:function(row){
                var sf =this;
                if(row.planEffort != row.remainEffort){
                    sf.$Message.error('已估过挣值的任务单，不允许取消认领！',3);
                    return;
                }
                var id = row.id;
                $.ajax({
                    url: linkus.location.scrum + "/scrumCtrl/cancelTask.action",
                    data:{
                        taskId:id,
                    },
                    success: function (data) {
                        sf.getMyAssessedList();
                    },
                    error : function(data) {
                        sf.$Message.error('取消认领失败，请联系管理员',3);
                    }
                });
            },
            //完成
            carryOut:function(index,id,value,effort){
                var sf =this;
                sf.estimateWorkTime(index,id,value,effort);
            },
            // 申请追加
            addEffort:function(row){
                var sf =this;
                sf.addModal = true;
                sf.selectInfo = row;
            },
            // 申请追加保存
            addEffortSave:function(){
                var sf =this;
                sf.$refs.formAdd.validate(function(valid) {
                    if (valid) {
                        $.ajax({
                            url: linkus.location.scrum + "/scrumCtrl/addEffort.action",
                            data:{
                                taskId:sf.selectInfo.id,
                                effort:sf.formData.effort,
                                effortDesc:sf.formData.effortDesc,
                            },
                            success: function (data) {
                                sf.cancel();
                            },
                            error : function(data) {
                                sf.$Message.error('申请追加失败，请联系管理员',3);
                            }
                        });
                    }
                })
            },
            // 转发
            forward:function(row){
                var sf =this;
                sf.forwardModal = true;
                sf.selectInfo = row;
                sf.getPersonList(row.id)
            },
            //转发保存
            forwardSave:function(){
                var sf =this;
                sf.$refs.formForward.validate(function(valid) {
                    if (valid) {
                        $.ajax({
                            url: linkus.location.scrum + "/scrumCtrl/forwardResp.action",
                            data:{
                                taskId:sf.selectInfo.id,
                                toRespId:sf.formData.toRespId,
                            },
                            success: function (data) {
                                sf.getMyAssessedList();
                                sf.cancel();
                            },
                            error : function(data) {
                                sf.$Message.error('转发失败，请联系管理员',3);
                            }
                        });
                    }
                })
            },
            // 赠送工时
            presentation:function(row){
                var sf =this;
                sf.presentationModal = true;
                sf.selectInfo = row;
                sf.getPersonList(row.id)
            },
            effortPresentationChange:function(value){
                var sf = this;
               if(value > sf.selectInfo.remainEffort){
                   sf.formData.effortPresentation  = sf.selectInfo.remainEffort
                   sf.$Message.error('赠送工时不能大于剩余工时',3);
               } 
            },
            //赠送保存
            presentationSave:function(){
                var sf =this;
                sf.$refs.formPresentation.validate(function(valid) {
                    if(valid) {
                        $.ajax({
                            url: linkus.location.scrum + "/scrumCtrl/giveEffort.action",
                            data:{
                                taskId:sf.selectInfo.id,
                                effort:sf.formData.effortPresentation,
                                toRespId:sf.formData.toRespId,
                                desc:sf.formData.desc
                            },
                            success: function (data) {
                                sf.getMyAssessedList();
                                sf.cancel();
                            },
                            error : function(data) {
                                sf.$Message.error('赠送失败，请联系管理员',3);
                            }
                        });
                    }
                })
            },
            // 需求描述
            viewDesc:function(row){
                var sf =this;
                sf.viewModal = true;
                sf.selectInfo = row
            },
            // 请假
            askForLeave:function(){
                var sf =this;  
                sf.holidayModal = true;
            },
            //请假保存
            holidaySave:function(){
                var sf = this;
                var startTime = sf.processDate(sf.formData.starDate);
                if(sf.holidayTab === 'date'){
                    sf.$refs.formHolidayDate.validate(function(valid) {
                        if(valid) {
                            var endTime =  sf.processDate(sf.formData.endDate);
                            var params ={
                                holidayDesc:sf.formData.holidayDesc,
                                submitHolidayData:[
                                    {
                                        date:startTime,
                                        hours:'',
                                    },
                                    {
                                        date:endTime,
                                        hours:'',
                                    }
                                ],
                            };
                            sf.addHoliday(params);
                        }
                    })
                }else{
                    sf.$refs.formHolidayHours.validate(function(valid) {
                        if(valid) {
                            var params ={
                                holidayDesc:sf.formData.holidayDesc,
                                submitHolidayData:[
                                    {
                                        date:startTime,
                                        hours:sf.formData.hours,
                                    }
                                ],
                            };
                            sf.addHoliday(params);
                        }
                    })  
                }
            },
            addHoliday:function(params){
                var sf = this;
                $.ajax({
                    url: linkus.location.scrum + "/scrumCtrl/addHoliday.action",
                    headers : 	{ 'Content-Type' : 'application/json;charset=utf-8' },
                    type:'post',
                    data:JSON.stringify(params),
                    success: function (data) {
                        sf.cancel();
                    },
                    error : function(data) {
                        sf.$Message.error('申请失败，请联系管理员',3);
                    }
                });  
            },
            getDate:function(startTime,endTime){
                var attr = [];
                while((endTime.getTime()-startTime.getTime())>=0){
                    var year = startTime.getFullYear();
                    var month = startTime.getMonth().toString().length==1?"0"+(startTime.getMonth()+1).toString():startTime.getMonth()+1;
                    var day = startTime.getDate().toString().length==1?"0"+startTime.getDate():startTime.getDate();
                    startTime.setDate(startTime.getDate()+1);
                    attr.push(
                        {
                            date:year+'-'+month+'-'+day+' 00:00:00',
                            hours:8,
                        }
                    );
                };
                return attr;
            },
            // 关闭
            cancel:function(){
                var sf =this;
                sf.addModal = false;
                sf.forwardModal = false;
                sf.presentationModal = false;
                sf.holidayModal = false;
                sf.$refs.formAdd.resetFields();
                sf.$refs.formForward.resetFields();
                sf.$refs.formHolidayDate.resetFields();
                sf.$refs.formHolidayHours.resetFields();

                sf.holidayTab = 'date';

                var myDate = new Date;
                sf.todayDate = new Date(sf.processDate(myDate));
                sf.formData={
                    effort:0,
                    effortPresentation:0,
                    effortDesc:'',
                    desc:'',
                    toRespId:'',

                    starDate:sf.todayDate,
                    endDate:'',
                    hours:'',
                    holidayDesc:'',
                };
                sf.selectInfo ={};
            },
	    	cancelCreateBizModal:function(iframe){
	    		var _this = this;
	    		_this.getMyAssessedList();
	    		iframe.createBizFrameSrc = '';
	    		iframe.createdBizViewCode = '';
	    		iframe.createBizModal = false;
	    		
	    	}            
        }
    });
    window.addEventListener("message", function (e) {
        if (e.data && e.data == 'closeWindow') {
        	vue.iframe.createBizModal = false;
        	vue.getMyAssessedList();
        }
    });
</script>

</body>
</html>
