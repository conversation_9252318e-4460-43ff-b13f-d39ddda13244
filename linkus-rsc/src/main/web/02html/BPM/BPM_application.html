<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>新增发布申请</title>
    <link href="../../01css-BPM/style.css" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="../../01css-BPM/icomoon/style.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css-BPM/icomoon/selection.json"></script>

    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css">
    <script type="text/javascript" src="../../00scripts/00lib/vue/vue.js"></script>
    <script type="text/javascript" src="../../00scripts/00lib/iview/iview.js"></script>

    <link href="../../01css-BPM/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css-BPM/font/iconfont.js"></script>
</head>
<body style="height: 100%;width: 100%;background-color: #f0f0f0!important;">
    	<div id="bpm-main">
    		<div class="bpm-main-nav">
				<i-Menu mode="horizontal" theme="primary" active-name="1" style=" top: 0; left: 0; right: 0;  padding: 0 15px;width:100%;background-color:#ff9900;">
				<div class="layout-logo" style="display: inline-flex;float: left; height: 60px; width: 182px;align-items: center;">
					<img src="../../03images/logo1.png" alt="" height="36" width="36" >
					<span>BPM流程管理</span>
				</div>
				<div class="layout-nav" style="display: inline">
					<Submenu name="1">
						<template slot="title" >
							<Icon type="ios-cloud-upload" size="16"></Icon>
							发布管理
						</template>
						<Menu-item key="1-1">新增发布申请</Menu-item>
						<Menu-item key="1-2">变更发布申请</Menu-item>
						<Menu-item key="1-3">正式发布</Menu-item>
						<Menu-item key="1-4">发布查询</Menu-item>
					</Submenu>
					<Menu-item name="2">
							<Icon type="nuclear" size="16"></Icon>
							制度&流程
					</Menu-item>
					<Menu-item name="3">
							<Icon type="navicon-round" size="16"></Icon>
							统计报表
					</Menu-item>
					<Submenu name="4">
						<template slot="title" >
							<Icon type="ios-gear" size="16"></Icon>
							系统配置
						</template>
						<Menu-item key="1-1"></Menu-item>
						<Menu-item key="1-2"></Menu-item>
					</Submenu>
					<div class="personal-center">
						<Submenu name="1">
							<template slot="title">
								<img src="../../03images/person-default.png">
	                        	<span>
		                            严晓冬
	                            </span>
							</template>
							<MenuGroup>
								<Menu-Item name="1-1">
									修改密码
								</Menu-Item>
								<Menu-Item name="1-2">
									退出登录
								</Menu-Item>
							</MenuGroup>
						</Submenu>
					</div>
					<div class="c"></div>
				</div>
			</i-Menu>
		</div>
		<div class="bpm-apply-new">
			<div class="apply-new-content">
				<Card :bordered="false">
					<p slot="title">新增发布申请</p>
					<Dropdown>
						 <a href="javascript:void(0)">
							请选择发布类型 
							<Icon type="arrow-down-b"></Icon>
						 </a>
						<Dropdown-Menu slot="list">
							<Dropdown-Item>流程</Dropdown-Item>
							<Dropdown-Item>制度</Dropdown-Item>
							<Dropdown-Item>流程&制度</Dropdown-Item>
						</Dropdown-Menu>
					</Dropdown>
					<div class="apply-content-basic">
						<p class="subtitle">基本信息：</p>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>所属业务域：</span>
								<i-Select v-model="model1" style="width:69%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>管理部门：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="OMC">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
							<div class="selectB">
								<span class="mini-title">OWNER：</span>
								<span style="color: #D1D1D1">王猛</span>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>密级：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
							<div class="selectB">
								<span class="mini-title"><span class="star">*</span>计划发布日期：</span>
								<Date-picker type="data" placeholder="" style="width: 28%"></Date-picker>
							</div>
						</div>
						
					</div>
					<div class="apply-content-process">
						<p class="subtitle">流程信息：</p>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>名称：</span>
								<i-input :value.sync="value"  style="width: 69%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>编号：</span>
								<i-input :value.sync="value" style="width: 28%"></i-input>
							</div>
							<div class="selectB">
								<span class="mini-title"><span class="star">*</span>版本号：</span>
								<i-input :value.sync="value" style="width: 28%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title">编制人：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
							<div class="selectB">
								<span class="mini-title"><span class="star">*</span>IT支持情况：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title" style="vertical-align: top;"><span class="star">*</span>描述：</span>
								<i-input type="textarea" :rows="4" style="width: 69%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title" style="height: 100%;vertical-align: top;margin-top: 5px;"><span class="star">*</span>主文档：</span>
								<Upload action="//jsonplaceholder.typicode.com/posts/" style="display: inline-block;">
									<i-Button type="warning" icon="ios-cloud-upload-outline">上传</i-Button>
								</Upload>	
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectC">
								<span class="mini-title" style="height: 100%;vertical-align: top;margin-top: 5px;"><span class="star">*</span>支持文档：</span>
								<Upload
									multiple
									action="//jsonplaceholder.typicode.com/posts/"  style="display: inline;">
									<i-Button type="warning" icon="ios-cloud-upload-outline">上传</i-Button>
								</Upload>	
							</div>
						</div>
					</div>
					<div class="apply-content-process">
						<p class="subtitle">制度信息：</p>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>名称：</span>
								<i-input :value.sync="value" style="width: 69%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title"><span class="star">*</span>编号：</span>
								<i-input :value.sync="value" style="width: 28%"></i-input>
							</div>
							<div class="selectB">
								<span class="mini-title"><span class="star">*</span>版本号：</span>
								<i-input :value.sync="value" style="width: 28%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title">编制人：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
							<div class="selectB">
								<span class="mini-title"><span class="star">*</span>IT支持情况：</span>
								<i-Select v-model="model1" style="width:28%" placeholder="">
									<i-Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
								</i-Select>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title" style="vertical-align: top;"><span class="star">*</span>描述：</span>
								<i-input type="textarea" :rows="4" style="width: 69%"></i-input>
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectA">
								<span class="mini-title" style="height: 100%;vertical-align: top;margin-top: 5px;"><span class="star">*</span>主文档：</span>
								<Upload action="//jsonplaceholder.typicode.com/posts/" style="display: inline-block;">
									<i-Button type="warning" icon="ios-cloud-upload-outline">上传</i-Button>
								</Upload>	
							</div>
						</div>
						<div class="apply-content-select">
							<div class="selectC">
								<span class="mini-title" style="height: 100%;vertical-align: top;margin-top: 5px;"><span class="star">*</span>支持文档：</span>
								<Upload
									multiple
									action="//jsonplaceholder.typicode.com/posts/"  style="display: inline;">
									<i-Button type="warning" icon="ios-cloud-upload-outline">上传</i-Button>
								</Upload>	
							</div>
						</div>
					</div>
					<div class="apply-content-btn">
					    <i-button type="warning">取消</i-button>
						<i-button type="warning">确认</i-button>
					
					</div>
					
				</Card>
			
			</div>
                       
            
        </div>
	
       
</div>


          <script>
             new Vue({
                 el: '#bpm-main',
                 data: {
                 }
               })
         </script>
</body>

</html>
