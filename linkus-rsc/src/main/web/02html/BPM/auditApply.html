<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>申请单审批</title>
    <link href="../../01css-BPM/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css-BPM/icomoon/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css-BPM/font/iconfont.css" rel="stylesheet" type="text/css" />
    <script src="../../01css-BPM/font/iconfont.js"></script>
    <script src="../../01css-BPM/icomoon/selection.json"></script>

    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css">
    <script type="text/javascript" src="../../00scripts/00lib/vue/vue.js"></script>
    <script type="text/javascript" src="../../00scripts/00lib/iview/iview.js"></script>

    <!-- jquery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <!-- 文件预览 -->
    <script src="../../00scripts/00lib/preview-knowledge/preview.js" type="text/javascript"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="./util.js"></script>
    <script src="./header.js"></script>
    <script src="./Template/auditDeptOwner.js?ver=20200515"></script>
    <script src="./Template/auditInnerBuJointlySign.js?ver=20200515"></script>
    <script src="./Template/auditBuHead.js?ver=20200515"></script>
    <script src="./Template/auditCrossBuJointlySign.js?ver=20200515"></script>
    <script src="./Template/auditEmt.js?ver=20200515"></script>
    <link href="../../00scripts/00lib/vue-quill-editor/quill.core.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/vue-quill-editor/quill.snow.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/vue-quill-editor/font.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/vue-quill-editor/fontsize.css" rel="stylesheet" type="text/css" />
	<script src="../../00scripts/00lib/vue-quill-editor/quill.core.js"></script>
	<script src="../../00scripts/00lib/vue-quill-editor/quill.js"></script>
	<script src="../../00scripts/00lib/vue-quill-editor/vue-quill-editor.js"></script>
	<script src="../../00scripts/00lib/vue-quill-editor/image-resize.min.js"></script>

	<!-- iconfont字体图标 -->
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>
    <style>
        [v-cloak] {
            display: none;
        }

        .current .ivu-steps-icon.ivu-icon.ivu-icon-disc {
            color: #f90 !important;
        }

        .wait .ivu-steps-icon.ivu-icon.ivu-icon-disc {
            color: #ccc !important;
        }

        .ivu-steps-icon.ivu-icon.ivu-icon-close-circled {
            color: #ed3f14 !important;
        }

        .ivu-steps-icon.ivu-icon.ivu-icon-checkmark-circled {
            color: #19be6b !important
        }

        .ivu-steps-icon.ivu-icon.ivu-icon-android-remove-circle {
            color: #ccc !important
        }

        .ivu-steps-item.ivu-steps-status-finish .ivu-steps-tail>i:after {
            background: #e9eaec;
        }
        .ql-container.ql-snow {
            border: 1px solid #dddee1;
            border-radius: 0 0 4px 4px;
        }
        .ql-editor.ql-blank::before {
            color: #A9A9A9;
            font-style: normal;
            left: 9px;
            font-size: 12px;
            top: 8px;
        }
        .ql-container {
            font-size: 12px;
        }
        .ql-container.ql-snow {
            height: auto;
            min-height: 96px;
            max-height: 222px;
            overflow-y: auto;
            resize: vertical;
        }
        .ql-editor.ql-blank {
            height: auto;
            min-height: 94px;
            max-height: 220px;
            overflow-y: hidden;
        }
        .ql-container.ql-snow:focus {
            border-color: #f90;
            box-shadow: 0 0 0 2px rgba(255,153,0,.2) !important;
            outline: 0;
            border-top: 1px solid #f90 !important;
        }
        .ql-container.ql-snow:hover {
            border-color: #f90;
            border-top: 1px solid #f90 !important;
        }
        .ql-editor {
            line-height: 1.5;
            font-size: 14px;
            padding: 6px 8px;
            font-family: "微软雅黑";
        }
        .ql-toolbar.ql-snow {
            border: 1px solid #dddee1;
            border-radius: 4px 4px 0 0px;
        }
        /* .ivu-form-item-content {
            line-height: 22px;
        } */

        .no_tip .ivu-select-not-found {
            display: none !important;
        }

        .keywordWarpClass {
			display: flex;
	   	    flex-wrap: wrap;
	   	    width: 77%;
	   	    margin-left: 4px;
		}
		.keywordWarpClass .ivu-input-wrapper {
			margin-right: 6px;
			width: 160px;
			margin-bottom: 8px;
		}
		.addKeywordClass.ivu-btn i {
			color: #a5acc4;
	   	    margin-right: 6px;
		}
		.addKeywordClass.ivu-btn {
			margin-bottom: 8px;
		}
		.addKeywordClass.ivu-btn > span {
		    display: flex;
		    align-items: center;
		}

		/* .requiredKey .ivu-input::-webkit-input-placeholder{
	        color: rgb(255, 51, 0);
	    }
	    .requiredKey .ivu-input::-moz-placeholder {
	        color: rgb(255, 51, 0);
	    }
	    .requiredKey .ivu-input::-moz-placeholder {
	        color: rgb(255, 51, 0);
	    }
	    .requiredKey .ivu-input::-ms-input-placeholder {
	        color: rgb(255, 51, 0);
	    } */
	    .keywordsTag.ivu-tag {
		    margin-right: 8px;
		    background: rgba(56, 131, 229, 0.1);
		    border-radius: 4px;
		    font-size: 12px;
		    border: none;
		}
		.keywordsTag.ivu-tag .ivu-tag-text {
		    color: #3883e5;
		}
		.keywordInputClass .ivu-icon-ios-close {
	    	display: none;
	    }
	    .keywordInputClass:hover .ivu-icon-ios-close{
	    	display: inline-block;
	    }
        .bpm-newframe {
            width: 100%;
        }
        .bpm-newframe .detail-card .label.dldIsAllowedLabel, .bpm-newframe .detail-card .label-normal.dldIsAllowedLabel {
            width: 10em;
        }
    </style>
</head>

<body style="height: 100%;width: 100%;background-color: #f0f0f0!important;">
    <div id="bpm-main" v-cloak>
        <bpm-header></bpm-header>
        <div class="bpm-newframe">
            <div class="content-all" style="clear:both;">
					<span class="pagetitle">
						<span style="position: relative; display: inline-block;"> {{ (apply.statusId && apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_START) ? '修改' : apply.statusName }} </span>
					</span>
					<Tooltip v-show="isShowBackPage" content="返回" placement="bottom" style="float: right; margin-top: 20px;" @click.native="backBpmTaskBill">
						<span class="iconfont icon-back" style="color: #fff; font-size: 20px;"></span>
					</Tooltip>
					<card :bordered="false" class="bpm-application-card" v-show="!inEditMode">
                    <div slot="title" class="application-form-name">
                        {{apply.processTypeName + '：' + apply.name + '-' + apply.applyType.name}}申请单
                        <div class="button-group">
                            <i-Button type="warning" @click="toUpdateApply" v-if="apply && apply.allowUpdate">修改
                            </i-Button>
                            <i-Button type="warning" @click="cancelApply" v-if="apply && apply.allowCancel">取消
                            </i-Button>
                            <i-Button
                                v-if="apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_PUBLISH && apply.activeTaskId && apply.id"
                                type="warning" @click="publishModal = true">发布</i-Button>
                        </div>
                        <!-- 主办部门审批 -->
                        <audit-dept-owner v-if="apply.statusId == DEF_ID_BPM_PROCESS_SLF_NODE_DEPT_OWNER ||
                            apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_INTERNAL_CONTROL || apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_MANAGE"
                            :apply-id="apply.id" :task-id="apply.activeTaskId"
                            :jointly-sign-bu-resps="jointlySignBuResps"
                            :only-support-files="onlySupportFiles" :status-id="apply.statusId" :must-bu-head-approve="mustBuHeadApprove">
                        </audit-dept-owner>
                        <!-- 部门会签审批 -->
                        <audit-inner-bu-jointly-sign
                            v-if="apply.statusId == DEF_ID_BPM_PROCESS_SLF_NODE_IN_BU_JOINTLY_SIGN" :apply-id="apply.id"
                            :task-id="apply.activeTaskId" :is-jointly-sign-parent-task="apply.isJointlySignParentTask"
                            :jointly-sign-bu-resps="jointlySignBuResps">
                        </audit-inner-bu-jointly-sign>
                        <!-- 主办BU审批 -->
                        <audit-bu-head v-if="apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_BU_HEAD" :apply-id="apply.id"
                            :task-id="apply.activeTaskId">
                        </audit-bu-head>
                        <!-- 跨BU会签 -->
                        <audit-cross-bu-jointly-sign
                            v-if="apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_ACROSS_BU_JOINTLY_SIGN"
                            :apply-id="apply.id" :task-id="apply.activeTaskId"
                            :allow-init-sub-tasks="apply.allowInitSubTasks"
                            :is-jointly-sign-parent-task="apply.isJointlySignParentTask">
                        </audit-cross-bu-jointly-sign>
                        <!-- EMT审批 -->
                        <audit-emt v-if="apply.statusId == DEF_ID_BPM_PROCESS_SFL_NODE_EMT" :apply-id="apply.id"
                            :task-id="apply.activeTaskId">
                        </audit-emt>
                    </div>

                    <div class="approval-information">
                        <p class="prj-title">
                            <span class="labelname">审批信息</span>
                        </p>
                        <div class="approval-state" v-for="(auditTask, index) in auditTasks">
                            <!-- bu内部门会签 -->
                            <ul v-if="auditTask.jointlySignInnerBuDeptAuditTask">
                                <li>
                                    <!-- 父任务 -->
                                    <p class="titleA">
                                        <img :src="auditTask.auditPass === undefined ? '../../03images/BPM-newicon/bpm-clock.png' :(auditTask.auditPass === true ? '../../03images/BPM-newicon/bpm-pass.png' : '../../03images/BPM-newicon/bpm-notpass.png')"
                                            height="24" style="margin-right: 4px;">
                                        汇总：{{ auditTask.task.name }}&nbsp;-&nbsp;{{ auditTask.auditDept.name }}&nbsp;-&nbsp;{{ auditTask.auditUser.userName }}<span
                                            class="time">{{ auditTask.auditTime }}</span>
                                    </p>
                                    <div class="state" style="padding-left: 28px;">
                                        <span class="label">说明：</span><span
                                            class="content">{{ auditTask.auditDesc  }}</span>
                                    </div>
                                    <div class="advice" style="margin-top: 12px;"  v-if="auditTask.linkedDefs">
	                                    <span>——</span>
	                                    <em>建议跨BU部门会签：
	                                        <span style="color: #262626;"
	                                            v-for="(bu, index) in auditTask.linkedDefs">
	                                            {{ bu.name }}&emsp;
	                                        </span>
	                                    </em>
	                                </div>
	                                <div class="advice"  v-if="auditTask.linkedDesc">
	                                    <span>——</span><em>建议说明：<span
	                                            class="state-content">{{ auditTask.linkedDesc }}</span></em>
	                                </div>

                                    <ul class="detail-card" style="margin-bottom: 12px;margin-left: 28px;">
                                        <!-- 子任务 -->
                                        <li style="margin-bottom: 12px;"
                                            v-for="(subAuditTask, subIndex) in auditTask.subTasks"
                                            v-show="subAuditTask.currentUserCanView">
                                            <span class="condition "
                                                :class="subAuditTask.auditPass === undefined ? 'wait' :(subAuditTask.auditPass === true ? 'pass' : 'not-pass')">
                                                {{ subAuditTask.auditPass === undefined ? '待会签' : (subAuditTask.auditPass === true ? '通过': '不通过')}}
                                            </span>
                                            <div class="content" :class="(subAuditTask.auditPass === undefined && (!apply.activeTaskIds || apply.activeTaskIds.indexOf(subAuditTask.task.cid) == -1)) ? 'wait' : ''">
                                                <div >
                                                    {{ subAuditTask.auditDept.name }}&ensp;&ensp;{{ subAuditTask.auditUser.userName }}&ensp;&ensp;{{ subAuditTask.auditTime  }}
                                                </div>
                                                <div style="display: flex;">
                                                    说明：<span class="state-content">{{ subAuditTask.auditDesc}}</span>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </li>
                                <li style="font-weight: normal;" v-if="auditTask">
                                    <div class="titleA" style="padding-bottom: 0;">
                                        <img src="../../03images/BPM-newicon/bpm-sponsor.png" alt="" height="14"
                                            style="margin-right: 4px;padding-left: 28px;">
                                        <em
                                            style="font-size:12px; font-weight: normal;">发起：{{auditTask.jointlySignInnerBuDeptAuditTask.startTask.task.name}}&nbsp;-&nbsp;{{  auditTask.jointlySignInnerBuDeptAuditTask.startTask.auditDept.name}}&nbsp;-&nbsp;{{auditTask.jointlySignInnerBuDeptAuditTask.startTask.auditUser.userName}}</em><span
                                            class="time">{{ auditTask.jointlySignInnerBuDeptAuditTask.startTask.auditTime }}</span>
                                    </div>
                                </li>
                            </ul>

                            <!--跨Bu部门会签-->
                            <ul v-if="auditTask.jonJointlySignCrossBuDeptAuditTask">
                                <li>
                                    <!-- 父任务 -->
                                    <p class="titleA">
                                        <img :src="auditTask.auditPass === undefined ? '../../03images/BPM-newicon/bpm-clock.png' : (auditTask.auditPass === true ? '../../03images/BPM-newicon/bpm-pass.png' : '../../03images/BPM-newicon/bpm-notpass.png')"
                                            height="24" style="margin-right: 4px;">
                                        汇总：{{ auditTask.task.name }}&nbsp;-&nbsp;{{ auditTask.auditDept.name }}&nbsp;-&nbsp;{{ auditTask.auditUser.userName }}<span
                                            class="time">{{ auditTask.auditTime }}</span>
                                    </p>
                                    <div class="state" style="padding-left: 28px;">
                                        <span class="label">说明：</span><span
                                            class="content">{{ auditTask.auditDesc  }}</span>
                                    </div>
                                    <ul style="padding-left: 24px; padding-top: 12px;">
                                        <li style="margin-bottom: 12px;" v-for="(subTask, index) in auditTask.subTasks">
                                            <p class="titleB">
                                                <img :src="subTask.auditPass === undefined ? '../../03images/BPM-newicon/greyLight.png' : (subTask.auditPass === true ? '../../03images/BPM-newicon/greenLight.png' : '../../03images/BPM-newicon/redLight.png')"
                                                    alt="" />
                                                <a href="#" v-if="subTask.subTasks.length > 0"
                                                    @click="toggleCrossAuditBuDetail(subTask)"
                                                    class="department select">{{subTask.auditDept.name}}</a>
                                                <span v-else>{{subTask.auditDept.name}}</span>
                                                    &nbsp;-&nbsp;{{ subTask.auditUser.userName }}
                                                <span class="time">{{ subTask.auditTime }}</span>
                                            </p>
                                            <div class="state">
                                                <span class="label">说明：</span><span
                                                    class="content">{{ subTask.auditDesc }}</span>
                                            </div>
                                            <ul class="detail-card" v-if="subTask.subTasks.length > 0"
                                                v-show="subTask.showDetails">
                                                <li style="margin-bottom: 12px;"
                                                    v-for="(subTask2, subTask2Index) in subTask.subTasks"
                                                    v-show="subTask2.currentUserCanView">
                                                    <span class="condition"
                                                        :class="subTask2.auditPass === undefined ? 'wait' : (subTask2.auditPass === true ? 'pass' : 'not-pass')">{{ subTask2.auditPass === undefined ? '待会签' : (subTask2.auditPass === true ? '通过' : '不通过') }}</span>
                                                    <div class="content" :class="(subTask2.auditPass === undefined && (!apply.activeTaskIds || apply.activeTaskIds.indexOf(subTask2.task.cid) == -1)) ? 'wait' : ''">
                                                        <div>
                                                            {{ subTask2.auditDept.name}}&ensp;&ensp;{{ subTask2.auditUser.userName }}&ensp;&ensp;{{ subTask2.auditTime }}
                                                        </div>
                                                        <div style="display: flex;">说明：<span
                                                                class="state-content">{{ subTask2.auditDesc }}</span>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                        </li>
                                    </ul>
                                </li>
                                <li style="font-weight: normal;" v-if="auditTask">
                                    <div class="titleA" style="padding-bottom: 0;">
                                        <img src="../../03images/BPM-newicon/bpm-sponsor.png" alt="" height="14"
                                            style="margin-right: 4px;padding-left: 28px;">
                                        <em
                                            style="font-size: 12px; font-weight: normal;">发起：{{auditTask.jonJointlySignCrossBuDeptAuditTask.startTask.task.name}}&nbsp;-&nbsp;{{  auditTask.jonJointlySignCrossBuDeptAuditTask.startTask.auditDept.name}}&nbsp;-&nbsp;{{auditTask.jonJointlySignCrossBuDeptAuditTask.startTask.auditUser.userName}}</em><span
                                            class="time">{{ auditTask.jonJointlySignCrossBuDeptAuditTask.startTask.auditTime }}</span>
                                    </div>
                                </li>
                            </ul>

                            <div
                                v-if="!auditTask.jointlySignInnerBuDeptAuditTask && !auditTask.jonJointlySignCrossBuDeptAuditTask">
                                <p class="titleA">
                                    <img :src="getAuditImage(auditTask)"
                                        height="24" style="margin-right: 4px;">
                                    <span>{{auditTask.task.name}}&nbsp;-&nbsp;</span><span
                                        v-if="auditTask.auditDept.name">{{ auditTask.auditDept.name }}&nbsp;-&nbsp;</span><span>{{ auditTask.auditUser.userName}}</span>
                                    <span class="time">{{ auditTask.auditTime}}</span>
                                </p>
                                <div class="state" style="padding-left: 28px;" v-if="index != auditTasks.length - 1 && !auditTask.auditCancel">
                                    <span class="label">说明：</span>
                                    <span class="content">{{ auditTask.auditDesc}}</span>
                                </div>
								<div v-if="DEF_ID_BPM_PROCESS_SFL_NODE_START == auditTask.node.cid && index == auditTasks.length - 1 && !!auditTask.auditDesc"
                                     	  class="state" style="padding-left: 28px;">
                                        <span class="label">说明：</span><span
                                            class="content">{{ auditTask.auditDesc  }}</span>
                                </div>
                                <div class="advice" style="margin-top: 12px;"
                                    v-if="auditTask.node && auditTask.node.cid == DEF_ID_BPM_PROCESS_SLF_NODE_DEPT_OWNER && !onlySupportFiles && !!mustBuHeadApprove && auditTask.linkedDefs">
                                    <span>——</span>
                                    <em>建议跨BU部门会签：
                                        <span style="color: #262626;" v-if="auditTask.linkedDefs"
                                            v-for="(bu, index) in auditTask.linkedDefs">
                                            {{ bu.name }}&emsp;
                                        </span>
                                    </em>
                                </div>
                                <div class="advice"
                                    v-if="auditTask.node && auditTask.node.cid == DEF_ID_BPM_PROCESS_SLF_NODE_DEPT_OWNER && !onlySupportFiles && !!mustBuHeadApprove && auditTask.linkedDesc">
                                    <span>——</span><em>建议说明：<span
                                            class="state-content">{{ auditTask.linkedDesc }}</span></em>
                                </div>
                            </div>

                            <div class="dashline" v-if="index != auditTasks.length - 1"></div>
                        </div>
                    </div>
                    <div class="main-line"></div>

                    <div class="approval-information">
                        <p class="prj-title">
                            <span class="labelname">基本信息</span>
                        </p>
                        <div class="detail-card" style="font-size: 14px;">
                            <div class="multiline">
                                <span class="label">
                                    名称：
                                </span>
                                <span class="text">{{apply.name}}</span>
                            </div>
                            <div class="multiline">
                                <span class="label">所属业务域：</span>
                                <span class="text">{{ apply.processArea ? apply.processArea.name : ''}}</span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">管理部门：</span>
                                    <span class="text">{{ apply.dept ? apply.dept.name.split('_')[0]: '' }}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">负责人：</span>
                                    <span class="text">{{ apply.deptOwner ? apply.deptOwner.userName : '' }}</span>
                                </span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">密级：</span>
                                    <span class="text">{{ apply.secertLevel ? apply.secertLevel.name : '' }}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">计划发布日期：</span>
                                    <span class="text">{{ apply.planReleaseDate ? apply.planReleaseDate.split(' ')[0] : ''  }}</span>
                                </span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">申请人：</span>
                                    <span class="text">{{apply.addUser ? apply.addUser.userName : ''}}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">申请日期：</span>
                                    <span class="text">{{ apply.addTime ? apply.addTime.split(' ')[0] : '' }}</span>
                                </span>
                            </div>

                        </div>
                    </div>
                    <div class="approval-information" style="margin-top: 16px;" v-if="apply.hasRegulation">
                        <p class="prj-title">
                            <span class="labelname">制度信息</span>
                        </p>
                        <div class="detail-card" style="font-size: 14px;">
                            <div class="multiline">
                                <span class="label">
                                    名称：
                                </span>
                                <span class="text">{{ apply.regulationName }}</span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编号：
                                    </span>
                                    <span class="text">{{ apply.regulationCode }}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">
                                        版本号：
                                    </span>
                                    <span class="text">{{ apply.regulationVersion }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">文档生效方式：</span>
                                    <span class="text">{{apply.regulationGoEffectType ? apply.regulationGoEffectType.name : ''}}</span>
                                </span>
                                <span class="contentA" v-if="apply.regulationGoEffectType && (apply.regulationGoEffectType.cid == goEffectOnFixedDateFieldId || apply.regulationGoEffectType.cid == goEffectOnYearFirstDateFieldId)">
                                    <span v-if="apply.regulationGoEffectType.cid == goEffectOnFixedDateFieldId" class="label">生效日期：</span>
                                    <span v-if="apply.regulationGoEffectType.cid == goEffectOnYearFirstDateFieldId" class="label">生效年份：</span>
                                    <span class="text" v-if="apply.regulationGoEffectType.cid == goEffectOnFixedDateFieldId">{{ apply.regulationGoEffectDate ? new Date(apply.regulationGoEffectDate).format('yyyy-MM-dd') : '' }}</span>
                                    <span class="text" v-if="apply.regulationGoEffectType.cid == goEffectOnYearFirstDateFieldId">{{ apply.regulationGoEffectDate ? new Date(apply.regulationGoEffectDate).format('yyyy') : '' }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编制人：
                                    </span>
                                    <span
                                        class="text">{{ apply.regulationAuthor ? apply.regulationAuthor.userName : '' }}</span>
                                </span>
                                <span class="contentA" v-if="apply.statusName==='已关闭' && isBeforeCurrent">
                                    <span class="label-normal">
                                        IT支持情况：
                                    </span>
                                    <span
                                        class="text">{{ apply.regulationItSupport ? apply.regulationItSupport.name : '' }}</span>
                                </span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                    文档权限：
                                    </span>
                                    <span class="text">{{ apply.regulationOpenScopeType.name }}</span>
                                </span>

                                <span class="contentA" v-show="apply.regulationOpenScopeType && (apply.regulationOpenScopeType.cid == '605af1b08006b628960c3466' || apply.regulationOpenScopeType.cid == '685116bdc935b27066c616ac')">
                                    <span class="label-normal">
                                        可见BU：
                                    </span>
                                    <span class="text">{{ apply.regulation2Bus}}</span>
                                </span>
                            </div>

                            <div class="two-columns" v-show="apply.regulationOpenScopeType && apply.regulationOpenScopeType.cid == '605af1708006b628960c3465'">
                                <span class="contentA">
                                    <span class="label-normal dldIsAllowedLabel">
                                        文档实际使用对象：
                                    </span>
                                    <span class="text">{{ apply.regulationActualUse }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA" v-show="apply.regulationOpenScopeType && (apply.regulationOpenScopeType.cid == '605af1b08006b628960c3466' || apply.regulationOpenScopeType.cid == '685116eac935b27066c61b81')">
                                    <span class="label-normal">
                                        可见部门：
                                    </span>
                                    <span class="text">{{ apply.regulation2Ccs }}</span>
                                </span>
                                <span class="contentA" v-show="apply.regulationOpenScopeType && (apply.regulationOpenScopeType.cid == '605af1b08006b628960c3466' || apply.regulationOpenScopeType.cid == '6851170ec935b27066c61f3d')">
                                        <span class="label-normal">
                                            可见人员：
                                        </span>
                                        <span class="text" style="word-break:inherit">{{ apply.regulation2Emps}}</span>
                                    </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label dldIsAllowedLabel">
                                    文档是否允许下载：
                                    </span>
                                    <span class="text">{{ apply.regulationDldIsAllowedLabel }}</span>
                                </span>
                            </div>

                            <div class="multiline">
                                <span class="label">
                                    文档概述：
                                </span>
                                <span class="text">{{ apply.regulationDesc }}</span>
                            </div>
							<div class="multiline" v-if="isAbolish">
							    <span class="label">
							        废止原因：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                            <div class="multiline">
                                <span class="label">
                                    文档关键字：
                                </span>
                                <Tag class="keywordsTag" size="large" v-for="(item, index) in apply.regulationKeywords" :key="index">{{ item }}</Tag>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    关联制度/流程：
                                </span>
                                <span class="text">
                                    <Tag v-for="(item, index) in apply.rLinkedProcesses" type="dot">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(item.mainFileId, item.mainFileName, true, true)">
                                            {{item.name}}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ item.mainFileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label" style="padding-top: 6px;">
                                    主文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(apply.regulationMainFile, apply.regulationMainFileName,true,true)">
                                            {{ apply.regulationMainFileName }}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=false&fileId='+ apply.regulationMainFile">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    支持文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot" v-for="fileId in apply.regulationHelpFiles">
                                        <span style="color:#1d5dd7;" @click="previewFile(fileId, apply.regulationHelpFileNames[fileId],true)">
                                            {{ apply.regulationHelpFileNames[fileId] }}
                                        </span>
                                        <a :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ fileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
							<div class="multiline" v-if="isChangeApply">
							    <span class="label">
							        变更说明：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                        </div>
                    </div>

                    <div class="approval-information" style="margin-top: 16px;" v-if="apply.hasProcess">
                        <p class="prj-title">
                            <span class="labelname">流程信息</span>
                        </p>
                        <div class="detail-card" style="font-size: 14px;">
                            <div class="multiline">
                                <span class="label">
                                    名称：
                                </span>
                                <span class="text">{{ apply.processName }}</span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编号：
                                    </span>
                                    <span class="text">{{ apply.processCode }}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">
                                        版本号：
                                    </span>
                                    <span class="text">{{ apply.processVersion }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">文档生效方式：</span>
                                    <span class="text">{{apply.processGoEffectType ? apply.processGoEffectType.name : ''}}</span>
                                </span>
                                <span class="contentA" v-if="apply.processGoEffectType && (apply.processGoEffectType.cid == goEffectOnFixedDateFieldId || apply.processGoEffectType.cid == goEffectOnYearFirstDateFieldId)">
                                    <span v-if="apply.processGoEffectType.cid == goEffectOnFixedDateFieldId" class="label">生效日期：</span>
                                    <span v-if="apply.processGoEffectType.cid == goEffectOnYearFirstDateFieldId" class="label">生效年份：</span>
                                    <span class="text" v-if="apply.processGoEffectType.cid == goEffectOnFixedDateFieldId">{{ apply.processGoEffectDate ? new Date(apply.processGoEffectDate).format('yyyy-MM-dd') : '' }}</span>
                                    <span class="text" v-if="apply.processGoEffectType.cid == goEffectOnYearFirstDateFieldId">{{ apply.processGoEffectDate ? new Date(apply.processGoEffectDate).format('yyyy') : '' }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编制人：
                                    </span>
                                    <span
                                        class="text">{{ apply.processAuthor ? apply.processAuthor.userName : '' }}</span>
                                </span>
                                <span class="contentA" v-if="apply.statusName==='已关闭' && isBeforeCurrent">
                                    <span class="label-normal">
                                        IT支持情况：
                                    </span>
                                    <span
                                        class="text">{{ apply.processItSupport ? apply.processItSupport.name : '' }}</span>
                                </span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                    文档权限：
                                    </span>
                                    <span class="text">{{ apply.processOpenScopeType.name }}</span>
                                </span>

                                <span class="contentA" v-show="apply.processOpenScopeType && (apply.processOpenScopeType.cid == '605af1b08006b628960c3466' || apply.processOpenScopeType.cid == '685116bdc935b27066c616ac')">
                                    <span class="label-normal">
                                        可见BU：
                                    </span>
                                    <span class="text">{{ apply.process2Bus}}</span>
                                </span>
                            </div>

                            <div class="two-columns" v-show="apply.processOpenScopeType && apply.processOpenScopeType.cid == '605af1708006b628960c3465'">
                                <span class="contentA">
                                    <span class="label-normal dldIsAllowedLabel">
                                        文档实际使用对象：
                                    </span>
                                    <span class="text">{{ apply.processActualUse}}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA" v-show="apply.processOpenScopeType && (apply.processOpenScopeType.cid == '605af1b08006b628960c3466' || apply.processOpenScopeType.cid == '685116eac935b27066c61b81')">
                                    <span class="label-normal">
                                        可见部门：
                                    </span>
                                    <span class="text">{{ apply.process2Ccs }}</span>
                                </span>
                                <span class="contentA" v-show="apply.processOpenScopeType && (apply.processOpenScopeType.cid == '605af1b08006b628960c3466' || apply.processOpenScopeType.cid == '6851170ec935b27066c61f3d')">
                                        <span class="label-normal">
                                            可见人员：
                                        </span>
                                        <span class="text" style="word-break:inherit">{{ apply.process2Emps}}</span>
                                    </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label dldIsAllowedLabel">
                                    文档是否允许下载：
                                    </span>
                                    <span class="text">{{ apply.processDldIsAllowedLabel }}</span>
                                </span>
                            </div>

                            <div class="multiline">
                                <span class="label">
                                    文档概述：
                                </span>
                                <span class="text">{{ apply.processDesc  }}</span>
                            </div>
							<div class="multiline" v-if="isAbolish">
							    <span class="label">
							        废止原因：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                            <div class="multiline">
                                <span class="label">
                                    文档关键字：
                                </span>
                                <Tag class="keywordsTag" size="large" v-for="(item, index) in apply.processKeywords" :key="index">{{ item }}</Tag>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    关联制度/流程：
                                </span>
                                <span class="text">
                                    <Tag type="dot" v-for="(item, index) in apply.pLinkedProcesses">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(item.mainFileId, item.mainFileName, true,true)">
                                            {{ item.mainFileName }}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ item.mainFileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label" style="padding-top: 6px;">
                                    主文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(apply.processMainFile, apply.processMainFileName,true,true)">
                                            {{ apply.processMainFileName }}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=false&fileId='+ apply.processMainFile">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    支持文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot" v-for="fileId in apply.processHelpFiles">
                                        <span style="color:#1d5dd7;" @click="previewFile(fileId, apply.processHelpFileNames[fileId],true)">
                                            {{ apply.processHelpFileNames[fileId] }}
                                        </span>
                                        <a :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ fileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
							<div class="multiline" v-if="isChangeApply">
							    <span class="label">
							        变更说明：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                        </div>
                    </div>

                    <div class="approval-information" style="margin-top: 16px;" v-if="apply.hasOther">
                        <p class="prj-title">
                            <span class="labelname">文件信息</span>
                        </p>
                        <div class="detail-card" style="font-size: 14px;">
                            <div class="multiline">
                                <span class="label">
                                    名称：
                                </span>
                                <span class="text">{{ apply.otherName }}</span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编号：
                                    </span>
                                    <span class="text">{{ apply.otherCode }}</span>
                                </span>
                                <span class="contentA">
                                    <span class="label">
                                        版本号：
                                    </span>
                                    <span class="text">{{ apply.otherVersion }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">文档生效方式：</span>
                                    <span class="text">{{apply.otherGoEffectType ? apply.otherGoEffectType.name : ''}}</span>
                                </span>
                                <span class="contentA" v-if="apply.otherGoEffectType && (apply.otherGoEffectType.cid == goEffectOnFixedDateFieldId || apply.otherGoEffectType.cid == goEffectOnYearFirstDateFieldId)">
                                    <span v-if="apply.otherGoEffectType.cid == goEffectOnFixedDateFieldId" class="label">生效日期：</span>
                                    <span v-if="apply.otherGoEffectType.cid == goEffectOnYearFirstDateFieldId" class="label">生效年份：</span>
                                    <span class="text" v-if="apply.otherGoEffectType.cid == goEffectOnFixedDateFieldId">{{ apply.otherGoEffectDate ? new Date(apply.otherGoEffectDate).format('yyyy-MM-dd') : '' }}</span>
                                    <span class="text" v-if="apply.otherGoEffectType.cid == goEffectOnYearFirstDateFieldId">{{ apply.otherGoEffectDate ? new Date(apply.otherGoEffectDate).format('yyyy') : '' }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                        编制人：
                                    </span>
                                    <span
                                        class="text">{{ apply.otherAuthor ? apply.otherAuthor.userName : '' }}</span>
                                </span>
                                <span class="contentA" v-if="apply.statusName==='已关闭' && isBeforeCurrent">
                                    <span class="label-normal">
                                        IT支持情况：
                                    </span>
                                    <span
                                        class="text">{{ apply.otherItSupport ? apply.otherItSupport.name : '' }}</span>
                                </span>
                            </div>
                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label">
                                    文档权限：
                                    </span>
                                    <span class="text">{{ apply.otherOpenScopeType ? apply.otherOpenScopeType.name : '' }}</span>
                                </span>

                                <span class="contentA" v-show="apply.otherOpenScopeType && (apply.otherOpenScopeType.cid == '605af1b08006b628960c3466' || apply.otherOpenScopeType.cid == '685116bdc935b27066c616ac')">
                                    <span class="label-normal">
                                        可见BU：
                                    </span>
                                    <span class="text">{{ apply.other2Bus }}</span>
                                </span>
                            </div>

                            <div class="two-columns" v-show="apply.otherOpenScopeType && apply.otherOpenScopeType.cid == '605af1708006b628960c3465'">
                                <span class="contentA">
                                    <span class="label-normal dldIsAllowedLabel">
                                        文档实际使用对象：
                                    </span>
                                    <span class="text">{{ apply.otherActualUse }}</span>
                                </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA" v-show="apply.otherOpenScopeType && (apply.otherOpenScopeType.cid == '605af1b08006b628960c3466' || apply.otherOpenScopeType.cid == '685116eac935b27066c61b81')">
                                    <span class="label-normal">
                                        可见部门：
                                    </span>
                                    <span class="text">{{ apply.other2Ccs }}</span>
                                </span>
                                <span class="contentA" v-show="apply.otherOpenScopeType && (apply.otherOpenScopeType.cid == '605af1b08006b628960c3466' || apply.otherOpenScopeType.cid == '6851170ec935b27066c61f3d')">
                                        <span class="label-normal">
                                            可见人员：
                                        </span>
                                        <span class="text" style="word-break:inherit">{{ apply.other2Emps }}</span>
                                    </span>
                            </div>

                            <div class="two-columns">
                                <span class="contentA">
                                    <span class="label dldIsAllowedLabel">
                                    文档是否允许下载：
                                    </span>
                                    <span class="text">{{ apply.otherDldIsAllowedLabel }}</span>
                                </span>
                            </div>

                            <div class="multiline">
                                <span class="label">
                                    文档概述：
                                </span>
                                <span class="text">{{ apply.otherDesc }}</span>
                            </div>
							<div class="multiline" v-if="isAbolish">
							    <span class="label">
							        废止原因：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                            <div class="multiline">
                                <span class="label">
                                    文档关键字：
                                </span>
                                <Tag class="keywordsTag" size="large" v-for="(item, index) in apply.otherKeywords" :key="index">{{ item }}</Tag>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    关联制度/流程：
                                </span>
                                <span class="text">
                                    <Tag v-for="(item, index) in apply.oLinkedProcessesOrRegulation" type="dot">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(item.mainFileId, item.mainFileName, true, true)">
                                            {{item.name}}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ item.mainFileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label" style="padding-top: 6px;">
                                    主文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot">
                                        <span style="color:#1d5dd7;"
                                            @click="previewFile(apply.otherMainFile, apply.otherMainFileName,true,true)">
                                            {{ apply.otherMainFileName }}
                                        </span>
                                        <a
                                            :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=false&fileId='+ apply.otherMainFile">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
                            <div class="multiline">
                                <span class="label-normal" style="padding-top: 6px;">
                                    支持文档：
                                </span>
                                <span class="text">
                                    <Tag type="dot" v-for="fileId in apply.otherHelpFiles">
                                        <span style="color:#1d5dd7;" @click="previewFile(fileId, apply.otherHelpFileNames[fileId],true)">
                                            {{ apply.otherHelpFileNames[fileId] }}
                                        </span>
                                        <a :href="linkusLocationKm + '/kmFileCtrl/download.action?isAddLog=true&fileId='+ fileId">
                                            <span class="iconfont icon-xiazaide"></span>
                                        </a>
                                    </Tag>
                                </span>
                            </div>
							<div class="multiline" v-if="isChangeApply">
							    <span class="label">
							        变更说明：
							    </span>
							    <span class="text">{{ apply.verDesc  }}</span>
							</div>
                        </div>
                    </div>

                </card>

                <card :bordered="false" class="bpm-application-card" v-show="inEditMode">
                    <div slot="title" class="application-form-name">
                        <div>{{apply.name}}申请单</div>
                        <div class="button-group">
                            <i-Button type="warning" @click="beforeUpdateApply">提交</i-Button>
                        </div>
                    </div>
                    <div class="approval-information" style="margin-top: 16px;">
                        <p class="prj-title">
                            <span class="labelname">基本信息</span>
                        </p>
                        <div class="detail-card" style="background: transparent;">
                            <i-Form ref="baseInfoForm" :model="baseInfoForm" label-position="right"
                                :rules="baseInfoFormRuleValidate" :label-width="112" class="BPM-validation">
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item prop="isBPGPreAudit">
                                            <Checkbox v-model="baseInfoForm.isBPGPreAudit" :disabled='isAbolish'></Checkbox>
                                            <span class="mini-title" style="text-align:left;"><span class="star" style="color: #ff3300;">*</span>DC已线下预审</span>
                                            <span v-show="!baseInfoForm.isBPGPreAudit && !isAbolish"
                                                  style="display: inline-block;width: 47.5%;color: #ff3300">请确认DC已线下预审！</span>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12" v-if="!!apply && !!apply.applyType && !!apply.applyType.cid
                                                           && (apply.applyType.cid == DEF_ID_BPM_APPLY_TYPE_ONLY_SUPPORT_FILES || apply.applyType.cid == DEF_ID_BPM_APPLY_TYPE)">
                                        <Form-Item prop="isMustBuHeadApprove">
                                            <Checkbox v-model="baseInfoForm.isMustBuHeadApprove" disabled></Checkbox>
                                            <span class="mini-title" style="text-align:left;"><span class="star" style="color: #ff3300;">*</span>需经BU Head审批</span>
                                        </Form-Item>
                                    </i-Col>
                                </Row>
                                <Form-Item label="所属业务域" prop="processAreas">
                                    <Cascader v-model="baseInfoForm.processAreas" :data="processAreas"
                                        placeholder="请选择所属业务域" :disabled='isAbolish'>
                                    </Cascader>
                                </Form-Item>
                                <Row>
                                    <i-Col span="16">
                                        <Form-Item label="管理部门" prop="deptId">
                                            <Cascader :data="deptTrees" v-model="baseInfoForm.deptId"
                                                style="width:100%; display: inline-block;" placeholder="请选择管理部门" :disabled='isAbolish'
                                                change-on-select></Cascader>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="6">
                                        <Form-Item label="负责人" prop="deptOwner">
                                            <span>{{ baseInfoForm.deptOwner ? baseInfoForm.deptOwner.userName: '' }}</span>
                                        </Form-Item>
                                    </i-Col>
                                </Row>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="密级" prop="secertLevelId">
                                            <i-Select v-model="baseInfoForm.secertLevelId" placeholder="请选择密级" :disabled='isAbolish'>
                                                <i-Option v-for="sl in secertLevels" :value="sl.cid" :key="sl.cid">{{ sl.name }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item label="计划发布日期" prop="planReleaseDate">
                                            <Date-picker type="date" v-model="baseInfoForm.planReleaseDate"
                                                placeholder="请选择计划发布日期" style="width: 100%" :disabled='isAbolish'>
                                            </Date-picker>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                            </i-Form>
                        </div>

                        <p class="prj-title" v-if="apply.hasRegulation">
                            <span class="labelname">制度信息</span>
                        </p>
                        <div class="detail-card" style="background: transparent;" v-if="apply.hasRegulation">
                            <i-Form ref="regulationForm" :model="regulationForm" label-position="right"
                                :rules="regulationFormRuleValidate" :label-width="136" class="BPM-validation">
                                <Form-Item label="名称" prop="rName">
                                    <i-Input v-model="regulationForm.rName" placeholder="请输入名称" :disabled='isAbolish'></i-Input>
                                </Form-Item>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编号" prop="rCode">
                                            <i-Input v-model="regulationForm.rCode" placeholder="请输入编号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item label="版本号" prop="rVersion">
                                            <i-Input v-model="regulationForm.rVersion" placeholder="请输入版本号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档生效方式" prop="rGoEffectType">
                                            <i-Select @on-open-change="rOpenChange" v-model="regulationForm.regulationGoEffectId" style="width:100%" placeholder="请选择文档生效方式" :disabled='isAbolish' @on-change="changeRegulationGoEffectType">
                                                <i-Option v-for="item in bpmDocGoEffectTypeList" :value="item.value" :key="item.value">{{ item.label }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item :label="regulationForm.regulationGoEffectId == goEffectOnFixedDateFieldId ? '生效日期' : '生效年份'" prop="rGoEffectDate" v-show="regulationForm.regulationGoEffectId == goEffectOnFixedDateFieldId || regulationForm.regulationGoEffectId == goEffectOnYearFirstDateFieldId">
                                            <Date-picker :type="regulationForm.regulationGoEffectId == goEffectOnFixedDateFieldId ? 'date' : 'year'" v-model="regulationForm.regulationGoEffectDate"
                                                         :placeholder="regulationForm.regulationGoEffectId == goEffectOnFixedDateFieldId ? '请选择生效日期' : '请选择生效年份'" style="width:100%;" :disabled='isAbolish'>
                                            </Date-picker>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编制人" prop="rAuthorUid">
                                            <i-Select v-model="regulationForm.rAuthorUid" placeholder="请选择制度编制人"
                                                filterable remote clearable :remote-method="getAuthors"
                                                :loading="authorLoading" :label="regulationForm.rAuthorUName" :disabled='isAbolish'>
                                                <i-Option v-for="author in authors" :value="author.id" :key="author.id">{{ author.userName + '/' + author.loginName}}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <!-- <Form-Item label="IT支持情况" prop="rItSupportId">
                                            <i-Select v-model="regulationForm.rItSupportId" placeholder="请选择IT支持情况" :disabled='isAbolish'>
                                                <i-Option v-for="is in itSupports" :value="is.cid" :key="is.cid">{{ is.name }}</i-Option>
                                            </i-Select>
                                        </Form-Item> -->
                                    </i-Col>
                                </Row>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档权限" prop="rDocPermId">
                                            <i-Select v-model="regulationForm.rDocPermId" placeholder="请选择文档权限" :disabled='isAbolish' @on-change="rDocPermIdChange">
                                                <i-Option v-for="d in docPerms" :value="d.id" :key="d.id">{{ d.defName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="regulationForm.rDocPermId == '605af1708006b628960c3465'">
                                    <i-Col span="12">
                                        <Form-Item label="文档实际使用对象" prop="rDocActualUse">
                                            <i-input v-model="regulationForm.rDocActualUse" placeholder="请输入文档实际使用对象"></i-input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="regulationForm.rDocPermId == '605af1b08006b628960c3466' || regulationForm.rDocPermId == '685116bdc935b27066c616ac'">
                                    <i-Col span="12">
                                        <Form-Item label="可见BU" prop="regulationBuIdList">
                                            <i-Select v-model="regulationForm.regulationBuIdList" multiple filterable placeholder="请选择BU" :disabled='isAbolish'>
                                                <i-Option v-for="bu in buList" :value="bu.name" :key="bu.name">{{ bu.code }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12" v-show="regulationForm.rDocPermId == '605af1b08006b628960c3466' || regulationForm.rDocPermId == '685116eac935b27066c61b81'">
                                        <Form-Item  label="可见部门"  prop="regulationCcIdList">
                                            <i-Select class="no_tip" v-model="regulationForm.regulationCcIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入部门名称或CC" filterable :remote-method="queryCC" :disabled='isAbolish'>
                                                <i-Option v-for="cc in regulationCcList" :value="cc.ccId" :key="cc.ccId">{{ cc.ccId+"/"+cc.ccNameCn }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12" v-show="regulationForm.rDocPermId == '605af1b08006b628960c3466' || regulationForm.rDocPermId == '6851170ec935b27066c61f3d'">
                                        <Form-Item label="可见人员" prop="regulationUserIdList">
                                            <i-Select class="no_tip" v-model="regulationForm.regulationUserIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入人员" filterable :remote-method="queryUser" :disabled='isAbolish'>
                                                <i-Option v-for="user in regulationUserList" :value="user.loginName" :key="user.loginName">{{ user.userName+"/"+user.loginName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档是否允许下载" prop="regulationDldIsAllowed">
                                            <Radio-Group v-model="regulationForm.regulationDldIsAllowed" :disabled='isAbolish'>
                                                <Radio label="yes" :disabled="dldIsAllowedDisabled || isAbolish"><span>是</span></Radio>
                                                <Radio label="no" :disabled="dldIsAllowedDisabled || isAbolish"><span>否</span></Radio>
                                            </Radio-Group>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Form-Item label="文档概述" prop="rDesc">
                                    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
                                        v-model="regulationForm.rDesc" placeholder="" :disabled='isAbolish'></i-Input>
                                </Form-Item>

								<Form-Item label="废止原因" prop="rverDesc" v-if="isAbolish">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="regulationForm.rverDesc" placeholder=""></i-Input>
								</Form-Item>

                                <Form-Item label="文档关键字" prop="rKeywords">
                                    <div class="keywordWarpClass">
										<i-input class="requiredKey" type="text" clearable v-model="regulationForm.rKeywordOne" placeholder="必填关键字1" :disabled='isAbolish'></i-input>
										<i-input class="requiredKey" type="text" clearable v-model="regulationForm.rKeywordTwo" placeholder="必填关键字2" :disabled='isAbolish'></i-input>
										<i-input class="keywordInputClass" v-for="(item, index) in regulationForm.rKeywords" icon="ios-close" @on-click="clearKeywords(regulationForm.rKeywords, index)" type="text" v-model="item.name" :placeholder="'请填写关键词' + (index + 3)" :disabled='isAbolish'></i-input>
										<i-Button type="dashed" class="addKeywordClass"  @click="addRKeyShow" :disabled='isAbolish'><i class="iconfont icon-plus"></i>添加关键字</i-Button>
									</div>
                                </Form-Item>

                                <Form-Item label="关联制度/流程" prop="rLinkedProcesses">
                                    <Tag v-for="(item, index) in regulationForm.rLinkedProcesses" color="yellow"
                                        type="dot" closable @on-close="removeLinkedProcess(item, 'r')">
                                        <span style="color: blue;">{{item.name}}</span>
                                    </Tag>
                                    <i-Button type="info" icon="link" @click="openLinkedModal('r')" :disabled='isAbolish'>添加</i-Button>
                                </Form-Item>
                                <Form-Item label="主文档" prop="rMainFileId">
                                    <Tag type="dot" v-if="regulationForm.rMainFileId"><span
                                            style="color: blue;">{{regulationForm.rMainFileName}}</span></Tag>
                                    <Upload
                                            ref="regulationMainFile"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline-block;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadRegulationMainFile"
                                            :on-success="regulationMainFileUploadSuccess"
                                            :on-error="regulationMainFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="mainImportLoading" :disabled='isAbolish'>
                                            <span v-if="!mainImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
                                <Form-Item label="支持文档" prop="rHelpFileIds">
                                    <Tag v-for="fileId in regulationForm.rHelpFileIds" type="dot" :closable="!isAbolish"
                                        @on-close="removeRegulationHelpFile(fileId)">
                                        {{ regulationForm['rHelpFileIdNames'][fileId] }}
                                    </Tag>
                                    <Upload
                                            multiple
                                            ref="regulationHelpFiles"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadRegulationHelpFile"
                                            :on-success="regulationHelpFileUploadSuccess"
                                            :on-error="regulationHelpFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="helpImportLoading" :disabled='isAbolish'>
                                            <span v-if="!helpImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
								<Form-Item label="变更说明" prop="rverDesc" v-if="isChangeApply">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="regulationForm.rverDesc" placeholder=""></i-Input>
								</Form-Item>
                            </i-Form>
                        </div>


                        <p class="prj-title" v-if="apply.hasProcess">
                            <span class="labelname">流程信息</span>
                        </p>
                        <div class="detail-card" style="background: transparent;" v-if="apply.hasProcess">
                            <i-Form ref="processForm" :model="processForm" label-position="right"
                                :rules="processFormRuleValidate" :label-width="136" class="BPM-validation">
                                <Form-Item label="名称" prop="pName">
                                    <i-Input v-model="processForm.pName" placeholder="请填写流程名称" :disabled='isAbolish'></i-Input>
                                </Form-Item>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编号" prop="pCode">
                                            <i-Input v-model="processForm.pCode" placeholder="请填写流程编号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item label="版本号" prop="pVersion">
                                            <i-Input v-model="processForm.pVersion" placeholder="请填写流程版本号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档生效方式" prop="pGoEffectType">
                                            <i-Select @on-open-change="pOpenChange" v-model="processForm.processGoEffectId" style="width:100%" placeholder="请选择文档生效方式" @on-change="changeProcessGoEffectType" :disabled='isAbolish'>
                                                <i-Option v-for="item in bpmDocGoEffectTypeList" :value="item.value" :key="item.value">{{ item.label }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item :label="processForm.processGoEffectId == goEffectOnFixedDateFieldId ? '生效日期' : '生效年份'" prop="pGoEffectDate" v-show="processForm.processGoEffectId == goEffectOnFixedDateFieldId || processForm.processGoEffectId == goEffectOnYearFirstDateFieldId">
                                            <Date-picker :type="processForm.processGoEffectId == goEffectOnFixedDateFieldId ? 'date' : 'year'" v-model="processForm.processGoEffectDate"
                                                         :placeholder="processForm.processGoEffectId == goEffectOnFixedDateFieldId ? '请选择生效日期' : '请选择生效年份'" style="width:100%;" :disabled='isAbolish'>
                                            </Date-picker>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编制人" prop="pAuthorUid">
                                            <i-Select v-model="processForm.pAuthorUid" placeholder="请选择流程编制人" filterable
                                                remote clearable :remote-method="getAuthors" :loading="authorLoading"
                                                :label="processForm.pAuthorUName" :disabled='isAbolish'>
                                                <i-Option v-for="author in authors" :value="author.id" :key="author.id">{{ author.userName + '/' + author.loginName}}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <!-- <Form-Item label="IT支持情况" prop="pItSupportId">
                                            <i-Select v-model="processForm.pItSupportId" placeholder="请选择流程IT支持情况" :disabled='isAbolish'>
                                                <i-Option v-for="is in itSupports" :value="is.cid" :key="is.cid">{{ is.name }}</i-Option>
                                            </i-Select>
                                        </Form-Item> -->
                                    </i-Col>
                                </Row>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档权限" prop="pDocPermId">
                                            <i-Select v-model="processForm.pDocPermId" placeholder="请选择文档权限" :disabled='isAbolish' @on-change="pDocPermIdChange">
                                                <i-Option v-for="d in docPerms" :value="d.id" :key="d.id">{{ d.defName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="processForm.pDocPermId == '605af1708006b628960c3465'">
                                    <i-Col span="12">
                                        <Form-Item label="文档实际使用对象" prop="pDocActualUse">
                                            <i-input v-model="processForm.pDocActualUse" placeholder="请输入文档实际使用对象"></i-input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="processForm.pDocPermId == '605af1b08006b628960c3466' || processForm.pDocPermId == '685116bdc935b27066c616ac'">
                                    <i-Col span="12">
                                        <Form-Item label="可见BU" prop="processBuIdList">
                                            <i-Select v-model="processForm.processBuIdList" multiple filterable placeholder="请选择BU" :disabled='isAbolish'>
                                                <i-Option v-for="bu in buList" :value="bu.name" :key="bu.name">{{ bu.code }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12" v-show="processForm.pDocPermId == '605af1b08006b628960c3466' || processForm.pDocPermId == '685116eac935b27066c61b81'">
                                        <Form-Item label="可见部门" prop="processCcIdList">
                                            <i-Select class="no_tip" v-model="processForm.processCcIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入部门名称或CC" filterable :remote-method="queryCC" :disabled='isAbolish'>
                                                <i-Option v-for="cc in processCcList" :value="cc.ccId" :key="cc.ccId">{{ cc.ccId+"/"+cc.ccNameCn }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12" v-show="processForm.pDocPermId == '605af1b08006b628960c3466' || processForm.pDocPermId == '6851170ec935b27066c61f3d'">
                                        <Form-Item label="可见人员" prop="processUserIdList">
                                            <i-Select class="no_tip" v-model="processForm.processUserIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入人员" filterable :remote-method="queryUser" :disabled='isAbolish'>
                                                <i-Option v-for="user in processUserList" :value="user.loginName" :key="user.loginName">{{ user.userName+"/"+user.loginName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档是否允许下载" prop="processDldIsAllowed">
                                            <Radio-Group v-model="processForm.processDldIsAllowed" :disabled='isAbolish'>
                                                <Radio label="yes" :disabled="dldIsAllowedDisabled || isAbolish"><span>是</span></Radio>
                                                <Radio label="no" :disabled="dldIsAllowedDisabled || isAbolish"><span>否</span></Radio>
                                            </Radio-Group>
                                        </Form-Item>
                                    </i-Col>
                                </Row>


                                <Form-Item label="文档概述" prop="pDesc">
                                    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
                                        v-model="processForm.pDesc" placeholder="请填写概述，阐述文档的主要内容" :disabled='isAbolish'></i-Input>
                                </Form-Item>

								<Form-Item label="废止原因" prop="pverDesc" v-if="isAbolish">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="processForm.pverDesc" placeholder=""></i-Input>
								</Form-Item>

                                <Form-Item label="文档关键字" prop="pKeywords">
                                    <div class="keywordWarpClass">
										<i-input class="requiredKey" clearable type="text" v-model="processForm.pKeywordOne" placeholder="必填关键字1" :disabled='isAbolish'></i-input>
										<i-input class="requiredKey" clearable type="text" v-model="processForm.pKeywordTwo" placeholder="必填关键字2" :disabled='isAbolish'></i-input>
										<i-input class="keywordInputClass" v-for="(item, index) in processForm.pKeywords" icon="ios-close" @on-click="clearKeywords(processForm.pKeywords, index)" type="text" v-model="item.name" :placeholder="'请填写关键词' + (index + 3)" :disabled='isAbolish'></i-input>
										<i-Button type="dashed" class="addKeywordClass"  @click="addPKeyShow" :disabled='isAbolish'><i class="iconfont icon-plus"></i>添加关键字</i-Button>
									</div>
                                </Form-Item>

                                <Form-Item label="关联制度/流程" prop="pLinkedProcesses">
                                    <Tag v-for="(item, index) in processForm.pLinkedProcesses" color="yellow" type="dot"
                                        closable @on-close="removeLinkedProcess(item, 'p')">
                                        <span style="color: blue;">{{item.name}}</span>
                                    </Tag>
                                    <i-Button type="info" icon="link" @click="openLinkedModal('p')" :disabled='isAbolish'>添加</i-Button>
                                </Form-Item>
                                <Form-Item label="主文档" prop="pMainFileId">
                                    <Tag type="dot" v-if="processForm.pMainFileId"><span
                                            style="color: blue;">{{processForm.pMainFileName}}</span></Tag>
                                    <Upload
                                            ref="processMainFile"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline-block;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadProcessMainFile"
                                            :on-success="processMainFileUploadSuccess"
                                            :on-error="processMainFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="mainImportLoading" :disabled='isAbolish'>
                                            <span v-if="!mainImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
                                <Form-Item label="支持文档" prop="pHelpFileIds">
                                    <Tag v-for="(fileId,index) in processForm.pHelpFileIds" type="dot" :closable="!isAbolish"
                                        @on-close="removeProcessHelpFile(fileId)">
                                        {{ processForm['pHelpFileIdNames'][fileId] }}
                                    </Tag>
                                    <Upload
                                            multiple
                                            ref="processHelpFiles"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadProcessHelpFile"
                                            :on-success="processHelpFileUploadSuccess"
                                            :on-error="processHelpFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="helpImportLoading" :disabled='isAbolish'>
                                            <span v-if="!helpImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
								<Form-Item label="变更说明" prop="pverDesc" v-if="isChangeApply">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="processForm.pverDesc" placeholder=""></i-Input>
								</Form-Item>
                            </i-Form>
                        </div>

                        <p class="prj-title" v-if="apply.hasOther">
                            <span class="labelname">文件信息</span>
                        </p>
                        <div class="detail-card" style="background: transparent;" v-if="apply.hasOther">
                            <i-Form ref="otherForm" :model="otherForm" label-position="right"
                                :rules="otherFormRuleValidate" :label-width="136" class="BPM-validation">
                                <Form-Item label="名称" prop="oName">
                                    <i-Input v-model="otherForm.oName" placeholder="请输入名称" :disabled='isAbolish'></i-Input>
                                </Form-Item>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编号" prop="oCode">
                                            <i-Input v-model="otherForm.oCode" placeholder="请输入编号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item label="版本号" prop="oVersion">
                                            <i-Input v-model="otherForm.oVersion" placeholder="请输入版本号" :disabled='isAbolish'></i-Input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档生效方式" prop="oGoEffectType">
                                            <i-Select @on-open-change="oOpenChange" v-model="otherForm.otherGoEffectId" style="width:100%" placeholder="请选择文档生效方式" @on-change="changeOtherGoEffectType" :disabled='isAbolish'>
                                                <i-Option v-for="item in bpmDocGoEffectTypeList" :value="item.value" :key="item.value">{{ item.label }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <Form-Item :label="otherForm.otherGoEffectId == goEffectOnFixedDateFieldId ? '生效日期' : '生效年份'" prop="oGoEffectDate" v-show="otherForm.otherGoEffectId == goEffectOnFixedDateFieldId || otherForm.otherGoEffectId == goEffectOnYearFirstDateFieldId">
                                            <Date-picker :type="otherForm.otherGoEffectId == goEffectOnFixedDateFieldId ? 'date' : 'year'" v-model="otherForm.otherGoEffectDate"
                                                         :placeholder="otherForm.otherGoEffectId == goEffectOnFixedDateFieldId ? '请选择生效日期' : '请选择生效年份'" style="width:100%;" :disabled='isAbolish'>
                                            </Date-picker>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="编制人" prop="oAuthorUid">
                                            <i-Select v-model="otherForm.oAuthorUid" placeholder="请选择制度编制人"
                                                filterable remote clearable :remote-method="getAuthors"
                                                :loading="authorLoading" :label="otherForm.oAuthorUName" :disabled='isAbolish'>
                                                <i-Option v-for="author in authors" :value="author.id" :key="author.id">{{ author.userName + '/' + author.loginName}}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12">
                                        <!-- <Form-Item label="IT支持情况" prop="oItSupportId">
                                            <i-Select v-model="otherForm.oItSupportId" placeholder="请选择IT支持情况" :disabled='isAbolish'>
                                                <i-Option v-for="is in itSupports" :value="is.cid" :key="is.cid">{{ is.name }}</i-Option>
                                            </i-Select>
                                        </Form-Item> -->
                                    </i-Col>
                                </Row>
                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档权限" prop="oDocPermId">
                                            <i-Select v-model="otherForm.oDocPermId" placeholder="请选择文档权限" :disabled='isAbolish' @on-change="oDocPermIdChange">
                                                <i-Option v-for="d in docPerms" :value="d.id" :key="d.id">{{ d.defName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="otherForm.oDocPermId == '605af1708006b628960c3465'">
                                    <i-Col span="12">
                                        <Form-Item label="文档实际使用对象" prop="oDocActualUse">
                                            <i-input v-model="otherForm.oDocActualUse" placeholder="请输入文档实际使用对象"></i-input>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row v-show="otherForm.oDocPermId == '605af1b08006b628960c3466' || otherForm.oDocPermId == '685116bdc935b27066c616ac'">
                                    <i-Col span="12">
                                        <Form-Item label="可见BU" prop="otherBuIdList">
                                            <i-Select v-model="otherForm.otherBuIdList" multiple filterable placeholder="请选择BU" :disabled='isAbolish'>
                                                <i-Option v-for="bu in buList" :value="bu.name" :key="bu.name">{{ bu.code }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12"  v-show="otherForm.oDocPermId == '605af1b08006b628960c3466' || otherForm.oDocPermId == '685116eac935b27066c61b81'">
                                        <Form-Item  label="可见部门"  prop="otherCcIdList">
                                            <i-Select class="no_tip" v-model="otherForm.otherCcIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入部门名称或CC" filterable :remote-method="queryCC" :disabled='isAbolish'>
                                                <i-Option v-for="cc in otherCcList" :value="cc.ccId" :key="cc.ccId">{{ cc.ccId+"/"+cc.ccNameCn }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                    <i-Col span="12" v-show="otherForm.oDocPermId == '605af1b08006b628960c3466' || otherForm.oDocPermId == '6851170ec935b27066c61f3d'">
                                        <Form-Item label="可见人员" prop="otherUserIdList">
                                            <i-Select class="no_tip" v-model="otherForm.otherUserIdList" :remote="ccRemote" @click.native="setRemote" multiple  placeholder="请输入人员" filterable :remote-method="queryUser" :disabled='isAbolish'>
                                                <i-Option v-for="user in otherUserList" :value="user.loginName" :key="user.loginName">{{ user.userName+"/"+user.loginName }}</i-Option>
                                            </i-Select>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Row>
                                    <i-Col span="12">
                                        <Form-Item label="文档是否允许下载" prop="otherDldIsAllowed">
                                            <Radio-Group v-model="otherForm.otherDldIsAllowed" :disabled='isAbolish'>
                                                <Radio label="yes" :disabled="dldIsAllowedDisabled || isAbolish"><span>是</span></Radio>
                                                <Radio label="no" :disabled="dldIsAllowedDisabled || isAbolish"><span>否</span></Radio>
                                            </Radio-Group>
                                        </Form-Item>
                                    </i-Col>
                                </Row>

                                <Form-Item label="文档概述" prop="oDesc">
                                    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
                                        v-model="otherForm.oDesc" placeholder="" :disabled='isAbolish'></i-Input>
                                </Form-Item>

								<Form-Item label="废止原因" prop="overDesc" v-if="isAbolish">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="otherForm.overDesc" placeholder=""></i-Input>
								</Form-Item>

                                <Form-Item label="文档关键字" prop="oKeywords">
                                    <div class="keywordWarpClass">
										<i-input class="requiredKey"  type="text" clearable v-model="otherForm.oKeywordOne" placeholder="必填关键字1" :disabled='isAbolish'></i-input>
										<i-input class="requiredKey" type="text" clearable v-model="otherForm.oKeywordTwo" placeholder="必填关键字2" :disabled='isAbolish'></i-input>
										<i-input class="keywordInputClass" v-for="(item, index) in otherForm.oKeywords" icon="ios-close" @on-click="clearKeywords(otherForm.oKeywords, index)" type="text" v-model="item.name" :placeholder="'请填写关键词' + (index + 3)" :disabled='isAbolish'></i-input>
										<i-Button type="dashed" class="addKeywordClass"  @click="addOKeyShow" :disabled='isAbolish'><i class="iconfont icon-plus"></i>添加关键字</i-Button>
									</div>
                                </Form-Item>

                                <Form-Item label="关联制度/流程" prop="oLinkedProcesses">
                                    <Tag v-for="(item, index) in otherForm.oLinkedProcesses" color="yellow"
                                        type="dot" closable @on-close="removeLinkedProcess(item, 'o')">
                                        <span style="color: blue;">{{item.name}}</span>
                                    </Tag>
                                    <i-Button type="info" icon="link" @click="openLinkedModal('o')">添加</i-Button>
                                </Form-Item>
                                <Form-Item label="主文档" prop="oMailFileId">
                                    <Tag type="dot" v-if="otherForm.oMailFileId"><span
                                            style="color: blue;">{{otherForm.oMainFileName}}</span></Tag>
                                    <Upload
                                            ref="otherMainFile"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline-block;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadOtherMainFile"
                                            :on-success="otherMainFileUploadSuccess"
                                            :on-error="otherMainFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="mainImportLoading" :disabled='isAbolish'>
                                            <span v-if="!mainImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
                                <Form-Item label="支持文档" prop="oHelpFileIds">
                                    <Tag v-for="fileId in otherForm.oHelpFileIds" type="dot" :closable="!isAbolish"
                                        @on-close="removeOtherHelpFile(fileId)">
                                        {{ otherForm['oHelpFileIdNames'][fileId] }}
                                    </Tag>
                                    <Upload
                                            multiple
                                            ref="otherHelpFiles"
                                            :action="fileUploadUrl"
                                            :data = "uploadData"
                                            style="display: inline;"
                                            :show-upload-list="false"
                                            :before-upload="beforeUploadOtherHelpFile"
                                            :on-success="otherHelpFileUploadSuccess"
                                            :on-error="otherHelpFileUploadError"
											:disabled='isAbolish'>
                                        <i-Button type="info" icon="ios-cloud-upload-outline"
                                            :loading="helpImportLoading" :disabled='isAbolish'>
                                            <span v-if="!helpImportLoading">上传</span>
                                            <span v-else>上传中 ...</span>
                                        </i-Button>
                                    </Upload>
                                </Form-Item>
								<Form-Item label="变更说明" prop="overDesc" v-if="isChangeApply">
								    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}"
								        v-model="otherForm.overDesc" placeholder=""></i-Input>
								</Form-Item>
                            </i-Form>
                        </div>

                    </div>
                </card>


                <card :bordered="false" class="bpm-application-card" style="margin-top: 16px;">
                    <div slot="title" class="application-form-name" style="justify-content: center;"
                        @click="toggleAuditHist">审批进度<Icon type="chevron-down" :style="toggleAuditHistStyle"></Icon>
                    </div>
                    <!-- 审批进度区域 -->
                    <div style="font-size: 12px;padding-top: 1em; padding-left: 4em;" v-show="showAuditHist">
                        <steps :current="currentStep" direction="vertical" size="small">
                            <step v-for="(h, index) in applyAuditHist" style="width: 100%" :title="h.taskName + (!!h.def ? '-' + h.def.name : '')"
                                :icon="getAuditHistIcon(h.nodeCodeName)"
                                :class="h.nodeCodeName=='current' ? 'current' : ''">
                                <div class="ivu-steps-content">
                                    <div class="approval-content-C" v-if="index == 0">
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">申请人：</span>
                                            <span class="content">{{ h.auditUser.userName }}</span>
                                        </div>
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">申请时间：</span>
                                            <span class="content">{{ h.auditTime }}</span>
                                        </div>
                                    </div>
                                    <div class="approval-content-C" v-if="h.taskName == '修改'">
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">修改人：</span>
                                            <span class="content">{{ h.auditUser.userName }}</span>
                                        </div>
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">修改时间：</span>
                                            <span class="content">{{ h.auditTime }}</span>
                                        </div>
                                    </div>
                                    <div class="approval-content-C" v-if="h.taskName == '取消'">
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">取消人：</span>
                                            <span class="content">{{ h.auditUser.userName }}</span>
                                        </div>
                                        <div class="selectA" style="height:32px;">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">取消时间：</span>
                                            <span class="content">{{ h.auditTime }}</span>
                                        </div>
                                    </div>
                                    <div class="approval-content-C" v-if="index > 0 && h.taskName != '修改' && h.taskName != '取消'">
                                        <div class="selectA">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批人：</span>
                                            <span class="content">{{ 'cancelled' == h.nodeCodeName ? '' : h.auditUser.userName }}<em v-if="h.agentUser" style="color:red;font-style: normal">  ({{h.agentUser.userName}} 代理)</em></span>
                                        </div>
                                        <div class="selectA">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批时间：</span>
                                            <span class="content">{{ 'cancelled' == h.nodeCodeName ? '' : h.auditTime }}</span>
                                        </div>
                                        <div class="selectA">
                                            <span class="mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批状态：</span>
                                            <span class="content">{{ 'cancelled' == h.nodeCodeName ? '' : h.auditStatusName }}</span>
                                        </div>
                                        <div class="selectA">
                                            <span class=" mini-title"
                                                style="width: 6em;display: inline-block;text-align: right; padding-right:12px;vertical-align: top;">审批意见：</span>
                                            <span class="content" style="margin-bottom: 11px;">{{ h.auditDesc }}</span>
                                        </div>
                                    </div>
                                </div>
                            </step>

                            <step v-for="(h, index) in notApprovalTasks" style="width: 100%" :title="h.name + (!!h.def ? '-' + h.def.name : '')"
                                :icon="getAuditHistIcon(h.nodeCodeName)"
                                :class="h.isActiveTask ? 'current' : 'wait'">
                                <div class="ivu-steps-content">
                                    <div class="selectA" style="height:32px;">
                                        <span class="mini-title"
                                            style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批人：</span>
                                        <span class="content">{{ h.resp[0].userName }}<em v-if="h.agentUser" style="color:red;font-style: normal">  ({{h.agentUser.userName}} 代理)</em></span>
                                    </div>
                                    <div class="selectA" style="height:32px;">
                                        <span class="mini-title"
                                            style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批时间：</span>
                                        <span class="content"></span>
                                    </div>
                                    <div class="selectA" style="height:32px;">
                                        <span class="mini-title"
                                            style="width: 6em;display: inline-block;text-align: right; padding-right:12px">审批状态：</span>
                                        <span class="content">待审批</span>
                                    </div>
                                    <div class="selectA" style="height:32px;">
                                        <span class=" mini-title"
                                            style="width: 6em;display: inline-block;text-align: right; padding-right:12px;vertical-align: top;">审批意见：</span>
                                        <span class="content" style="margin-bottom: 11px;"></span>
                                    </div>
                                </div>
                            </step>
                        </steps>
                    </div>
                </card>
            </div>
        </div>

        <Modal title="修改说明" v-model="verDescModal" width="600" :mask-closable="false" class="BPM-newmodal">
            <p slot="header">申请单修改</p>
            <i-Form ref="verDescForm" :model="verDescForm" label-position="right" :rules="verDescFormValidate"
                :label-width="80" class="BPM-validation">
                <Form-Item label="修改说明" prop="verDesc">
                    <i-Input type="textarea" :autosize="{minRows: 4,maxRows: 10}" v-model="verDescForm.verDesc"
                        placeholder="请填写修改说明"></i-Input>
                </Form-Item>
            </i-Form>
            <div slot="footer">
                <i-Button type="warning" @click="cancelUpdateApply">取消</i-Button>
                <i-Button type="warning" @click="updateApply">确定</i-Button>
            </div>
        </Modal>

        <Modal title="关联" v-model="linkedModal" class-name="window-height" width="70%" :mask-closable="false"
            @on-ok="addLinkedProcess">
            <i-select v-model="linkedProcesses" :loading="false" loading-text="数据加载中..." multiple filterable clearable
                placeholder="请选择关联的流程/制度">
                <i-option v-for="(item, index) in processList" :value="item.id">{{ item.name }}</i-option>
            </i-select>
        </Modal>


        <Modal v-model="publishModal" width="800" class="BPM-newmodal">
            <p slot="header">发布归档</p>
            <i-Form ref="publishForm" :model="publishForm" label-position="right" :rules="publishFormValidate"
                :label-width="80" class="BPM-validation">
                <Form-Item label="邮件主题" prop="mailSubject">
                    <i-Input v-model="publishForm.mailSubject" placeholder="请输入邮件主题"></i-Input>
                </Form-Item>
                <Form-Item label="收件人" prop="mailTo">
                    <i-Input v-model="publishForm.mailTo" placeholder="请输入邮箱地址，多个地址用英文分号（;）隔开"></i-Input>
                </Form-Item>
                <Form-Item label="抄送人" prop="mailRecipient">
                    <i-Input v-model="publishForm.mailRecipient" placeholder="请输入邮箱地址，多个地址用英文分号（;）隔开"></i-Input>
                </Form-Item>
                <Form-Item label="邮件正文" prop="mailContent">
                    <quill-editor ref="myQuillEditor" v-model="publishForm.mailContent" :options="editorOption" @keyup.native="OnKeyup($event)"></quill-editor>
                </Form-Item>
                <Form-Item label="邮件内容" prop="mailAttachment" style="margin-bottom: 0;">
                    <Upload ref="mailAttachmentFiles" :action="attachmentUploadUrl" style="display: inline-block;"
                        :show-upload-list="false" :before-upload="beforeUploadMailAttachment"
                        :on-success="uploadMailAttachmentSuccess" :on-error="uploadMailAttachmentError">
                        <Tag v-for="fileId in publishForm.mailAttachFileIds" type="dot" closable
                            @on-close="removeMailAttachFile(fileId, 0)">
                            {{ publishForm.mailAttachFileNames[fileId] }}
                        </Tag>
                        <i-Button type="info" icon="ios-cloud-upload-outline" :loading="mailAttachLoading">
                            <span v-if="!mailAttachLoading">上传</span>
                            <span v-else>上传中 ...</span>
                        </i-Button>
                    </Upload>
                </Form-Item>
            </i-Form>
            <div slot="footer">
                <i-Button type="warning" @click="publishCancel">取消</i-Button>
                <i-Button type="warning" @click="publishReset">重置</i-Button>
                <i-Button type="warning" @click="publishConfirm">确定</i-Button>
            </div>
        </Modal>
    </div>
    <script src="./auditApply.js?v=20200429"></script>
</body>

</html>
