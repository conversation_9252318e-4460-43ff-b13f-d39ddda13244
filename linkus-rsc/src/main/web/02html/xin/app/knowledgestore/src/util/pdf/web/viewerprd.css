.dropdownToolbarButton {
  background: url(imagesPrd/toolbarButton-menuArrows.png) no-repeat;
}
html[dir='ltr'] .toolbarButton.pageDown::before {
  content: url(imagesPrd/toolbarButton-pageDown.png);
}
html[dir='ltr'] .toolbarButton.pageUp::before {
  content: url(imagesPrd/toolbarButton-pageUp.png);
}
#viewFind.toolbarButton::before {
  content: url(imagesPrd/toolbarButton-search.png);
}
html[dir='ltr'] .toolbarButton#sidebarToggle::before {
  content: url(imagesPrd/toolbarButton-sidebarToggle.png);
}
.toolbarButton.zoomIn::before {
  content: url(imagesPrd/toolbarButton-zoomIn.png);
}
.toolbarButton.zoomOut::before {
  content: url(imagesPrd/toolbarButton-zoomOut.png);
}

.toolbarButton.presentationMode::before,
.secondaryToolbarButton.presentationMode::before {
  content: url(imagesPrd/toolbarButton-presentationMode.png);
}

#toolbarContainer{
	background-color:#fff !important;
	background-image:none;
}
#pageNumber{
	color: black;
	border-color: black;
}
#numPages{
	color: black;
}
#scaleSelectContainer{
	border-color:black;
}
#scaleSelect{
	color:black;
}
.verticalToolbarSeparator.hiddenSmallView{
	display:none;
}
#presentationMode{
	display:block !important;
}