<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>干系人管理</title>
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
	<link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
	<link href="../../01css/prjStandardStyle.css" rel="stylesheet" type="text/css"/>

	<script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <script src="../../00scripts/location/location.js"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <script src="../../00scripts/00lib/vue/vue.js"></script>
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/vue/loading/vue-infinite-loading.js"></script>
    <script src="../../00scripts/00lib/vue/loading/vue-sysc-opt.js" type="text/javascript" ></script>

    <script src="../../00scripts/00lib/iview/iview.min.js"></script>
    <link href="../../00scripts/00lib/iview/styles/iview.css" rel="stylesheet" type="text/css"/>

    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

	<!--     miniui			 -->
    <link href="../../00scripts/00lib/miniui/3.5/themes/default/miniui.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/icons.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/default/medium-mode.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/bootstrap/skin.css" rel="stylesheet" >
	<link href="../../00scripts/00lib/miniui/3.5/res/font-awesome/css/font-awesome.min.css" rel="stylesheet" >
    <script src="../../00scripts/00lib/miniui/3.5/miniui.js" type="text/javascript" ></script>
	<script src="../../00scripts/00lib/vue/loading/lks-prj-loading.js"></script>
    <link href="../../00scripts/00lib/miniui/3.5/themes/bestway/skin.css" rel="stylesheet" >

    <script src="../../00scripts/user/lks-user-load-fuzzy.js" type="text/javascript"></script>

    <!-- 本页面使用 -->
    <script src="../../00scripts/prj/menu/prjHeader.js" type="text/javascript"></script>
	<script src="../../00scripts/prj/prjFixHelp.js" type="text/javascript"></script>
    <link href="../../00scripts/prj/menu/prjHeader.css" rel="stylesheet" type="text/css"/>


	<script type="text/javascript" src="../../common/defConst.js"></script>
	<script type="text/javascript" src="../../common/filterTable.js"></script>
	<script type="text/javascript" src="../../common/iviewTableExtend.js"></script>
	<script type="text/javascript" src="../../00scripts/prj/component/stakeholderLibraryImport.js"></script>

</head>
<style>
[v-cloak] {
  	display: none;
}
.tab .ivu-tabs-nav .ivu-tabs-tab{
	font-size: 14px !important;
}
.tab .ivu-tabs-bar{
	border: none !important;
}
#lineTd:before{
	content:"";
	position: absolute;
	width: 1px;
	background: #000;
	height:148px;
	top:0;
	left:0;
	display: block;
	transform: rotate(-53deg);
	transform-origin: top;
}
#slantTd:before{
    content:"";
	position: absolute;
	width: 1px;
	background: #000;
	height:148px;
	top:0;
	left:0;
	display: block;
	transform: rotate(-53deg);
	transform-origin: top;
}
.standpointtable tr td:first-child{
	width: 120px;
	height: 90px;
	background: #ffe9c9;
}
.standpointtable tr td{
	width : 20vw;
	border: 1px dashed #adadad;
	height: 6vw;
	font-size:14px;
}
.standpointtable th{
	background: #f0f8ff;
	border: 1px dashed #adadad;
	font-size:14px;
}
.profittable tr td:first-child{
	width: 120px;
	height: 90px;
	background: #fff;
	border: 1px dashed #adadad;
}
.profittable tr td{
	width : 30vw;
	border: 1px dashed #adadad;
	height: 11vw;
	font-size:14px;
}
.profittable th{
	background: #fff;
	border: 1px dashed #adadad;
	font-size:14px;
}
.stanpointtd:before{
	content: "立场";
    position: absolute;
    top: -66px;
    right: 20px;
    width: 2em;
    font-weight:bold;
}
.profittd:before{
    content: "利益";
    position: absolute;
    top: -66px;
    right: 20px;
    width: 2em;
    font-weight:bold;
}
#standpointAnalysisDiv{
	display: flex;
	justify-content: center;
	height: 100%;
    align-items: center;
}
#profitAnalysisDiv{
    display: flex;
	justify-content: center;
	height: 100%;
    align-items: center;
}
.layout-content .item-tree>div {
    width: 500px;
}
.layout-content .ivu-select.ivu-select-single .ivu-select-selection {
    height: 30px;
}
.view_tabs_css.tabs_border_none .ivu-tabs-tabpane{
	padding: 0;
}

	.headClass .ivu-table-body{
		overflow: hidden;
	}
textarea.ivu-input{
	font-size: 12px;
}


.stakeholder_library_import_modal p{
 	padding:4px 0;
	font-size: 14px;
	color: #555;
}

.stakeholder_library_import_modal .sli_warp{
	display: flex;
}

.stakeholder_library_import_modal .sli_warp .table_right{
	width: calc(100% - 200px);
}
.headClass .ivu-table-body .ivu-table-row >td:first-child .ivu-checkbox-wrapper{
	display: none;
}
.ivu-tree-title-selected, .ivu-tree-title-selected:hover {
    color: #3883e5;
}
</style>
<body style="width:100%;height:100%;">
	<div id="main" class="bg-light-grey" style="height: 100%;min-width:1190px;width:100%;" v-cloak>
	    <prj-header></prj-header>
		<div class="conrainer prjStakeholder" style="width:100%;padding:0 5% 8px;height: calc(100% - 60px);">
			<prj-fix-help ref="prjFixHelp"></prj-fix-help>
			<div class="layout-content layout-container" ref="layoutContent"
						style="height: 100%;">

				<div class="pd8 pd-side0 prjOrange">
					<div class="item-tree">
						<lks-prj-loading :is-prj-set="isPrjSet" :prj-name="prjName" :prj-id="prjId"
							:bu-prj-budget-admin-first-flag="buPrjBudgetAdmin"
							:pm-prj-admin-first-flag="!buPrjBudgetAdmin"></lks-prj-loading>
					</div>

<!--					<i-select  v-model="selectMatterPsnType" placeholder="干系人类型" style="width:100px" class="data-select"-->
<!--										  ref="changeLevelref" clearable="true" @on-change="changeType">-->
<!--					<i-Option v-for="p in matterPsnType" :value="p.defName">{{ p.defName }}</i-Option>-->
<!--					</i-select>-->

<!--					<i-select  v-model="selectMatterPsnImptLevel" placeholder="重要性" style="width:100px" class="data-select"-->
<!--							    clearable="true" @on-change="changeType">-->
<!--						<i-Option v-for="p in matterPsnImptLevel" :value="p.defName">{{ p.defName }}</i-Option>-->
<!--					</i-select>-->



					<!-- <i-Button class="fl-rg" type="info" icon="ios-search" @click="loadStakeHolderInfo">查 询</i-Button> -->
					<div class="perform">
						<i-button type="primary" class="fl-lg display-inbl" @click="openAddPrjModal" v-if="!!isPmUser || !!isPrjAdmin">新增</i-button>
						<i-button type="primary" class="fl-lg display-inbl" @click="openEditPrjModal" v-if="!!isPmUser || !!isPrjAdmin">编辑</i-button>
						<i-button type="primary" class="fl-lg display-inbl" @click="deleteMultiPrjInfoFromStakeHolder" v-if="!!isPmUser || !!isPrjAdmin">删除</i-button>
						<Dropdown placement="bottom-start" class="delete-month" v-if="!!isPmUser || !!isPrjAdmin">
                                <i-Button type="primary" :loading="loading" loading-text="数据加载中...">
                                    		更多
                                    <Icon type="ios-arrow-down"></Icon>
                                </i-Button>
                                <DropdownMenu slot="list">
									<Dropdown-Item @click.native="openImportLibrary">从干系人库导入</Dropdown-Item>
									<Dropdown-Item :loading="loadingIFM" @click.native="openIFM" >从组织架构导入</Dropdown-Item>
                                    <Dropdown-Item @click.native="openImport">批量导入</Dropdown-Item>
                                    <Dropdown-Item @click.native="downloadTemplate">下载导入模板</Dropdown-Item>
									<Dropdown-Item @click.native="exportPrjReviewInfo" >导出</Dropdown-Item>
                                </DropdownMenu>
                        </Dropdown>
						<!--<i-button type="primary" class="fl-lg display-inbl" @click="exportPrjReviewInfo">导出</i-button>-->
					</div>
				</div>

				<div v-show="!mainShowFlag" class="block pd8" style="height: calc(100% - 48px); height: -moz-calc(100% - 48px); height: -webkit-calc(100% - 48px); box-sizing: border-box;">
					<div  class="vertical-center-modal" style="overflow-y: hidden;height:100%;">
						<div class="text-center">
							<img src="../../03images/empty/choose-item.png" class="empty-img">
							<h2 class="font-weight-noraml font-20 light-grey">请选择项目!</h2>
						</div>
					</div>
				</div>
				<Tabs active-key='key0'  @on-click="switchTab" style="margin-bottom: 0px; margin-top:0px;" class="tab view_tabs_css tabs_border_none" :animated="false" type="card">
						<Tab-pane label='项目管理' key='key0' name="stakeholderManageTab"></Tab-pane>
						<Tab-pane label='立场分析'  key='key1'  name="standpointAnalysisTab"></Tab-pane>
						<Tab-pane label='权利利益分析' key='key2' name="profitAnalysisTab"></Tab-pane>
				</Tabs>
				<div v-show="mainShowFlag" class="block pd8" style="height: calc(100% - 103px);">
					<filter-table
							id="stakeholderManageDiv"
							:height="tableHeight"
							class="view_table lineClamp2 userStoryTableClass"
							row-key="id"
							:columns="stakeHolderInfoColumns"
							:data="stakeHolderInfoData"
							:loading="stakeHolderInfoLoading"
							:select-data-map="selectDataMap"
							:row-click-select="true"
							:disable-dblclick="true"
							stripe>
					</filter-table>

					<div id="standpointAnalysisDiv" style="display: none;height:100%;overflow: auto;">
                        <table class="standpointtable" style="border-collapse:collapse;text-align: center;" border="1" cellpadding="0" cellspacing="0">
                          <tbody>
                            <tr>
                              <td id="lineTd" style="position: relative;">
                                 <span style="position: absolute;bottom: 20px;left: 14px;font-weight:bold;">
                                                                                        重要性
                                 </span>
					             <span class="stanpointtd" style="position: relative;right: 0;top: 0px;display: block;border-top: 90px solid #f0f8ff;border-left: 120px solid transparent;">
                                 </span>
                              </td>
                              <th>
                                 <p>
                                   <span>
                                                                                            不支持
                                   </span>
					             </p>
                              </th>
                              <th>
                                 <p>
                                   <span>
                                                                                            中立
                                   </span>
					             </p>
                              </th>
                              <th>
                                 <p>
                                   <span>
                                                                                            支持
                                   </span>
					             </p>
                              </th>
                            </tr>
                            <tr>
                              <td style="font-weight:bold;">
                                 <p>
                                   <span>
                                                                                             高
                                   </span>
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapSevens.length > 0" style="text-align:center;padding: 0 12px;">
					                <span v-for="(seven,index) in ninePalaceMapSevens" v-if="index != ninePalaceMapSevens.length - 1">
					                  {{seven}}、
					                </span>
					                <span v-for="(seven,index) in ninePalaceMapSevens" v-if="index == ninePalaceMapSevens.length - 1">
					                  {{seven}}
					                </span>
					             </p>
					             <p v-else-if="ninePalaceMapSevens.length == 0" style="text-align:center;padding: 0 12px;">
					                                             暂无数据
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapEights.length > 0" style="text-align:center;padding: 0 12px;">
					                <span v-for="(eight,index) in ninePalaceMapEights" v-if="index != ninePalaceMapEights.length - 1">
					                  {{eight}}、
					                </span>
					                <span v-for="(eight,index) in ninePalaceMapEights" v-if="index == ninePalaceMapEights.length - 1">
					                  {{eight}}
					                </span>
					             </p>
					             <p v-else-if="ninePalaceMapEights.length == 0" style="text-align:center;padding: 0 12px;">
					                                              暂无数据
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapNines.length > 0" style="text-align:center;padding: 0 12px;">
					                <span v-for="(nine,index) in ninePalaceMapNines" v-if="index != ninePalaceMapNines.length - 1">
					                  {{nine}}、
					                </span>
					                <span v-for="(nine,index) in ninePalaceMapNines" v-if="index == ninePalaceMapNines.length - 1">
					                  {{nine}}
					                </span>
					            </p>
					            <p v-else-if="ninePalaceMapNines.length == 0" style="text-align:center;padding: 0 12px;">
					                                             暂无数据
					            </p>
                              </td>
                            </tr>
                            <tr>
                              <td style="font-weight:bold;">
                                 <p>
                                   <span>
                                                                                            中
                                   </span>
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapFours.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(four,index) in ninePalaceMapFours" v-if="index != ninePalaceMapFours.length - 1">
					                   {{four}}、
					                 </span>
					                 <span v-for="(four,index) in ninePalaceMapFours" v-if="index == ninePalaceMapFours.length - 1">
					                   {{four}}
					                 </span>
					             </p>
					             <p v-else-if="ninePalaceMapFours.length == 0" style="text-align:center;padding: 0 12px;">
					                                                暂无数据
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapFives.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(five,index) in ninePalaceMapFives" v-if="index != ninePalaceMapFives.length - 1">
					                   {{five}}、
					                 </span>
					                 <span v-for="(five,index) in ninePalaceMapFives" v-if="index == ninePalaceMapFives.length - 1">
					                   {{five}}
					                 </span>
					            </p>
					            <p v-else-if="ninePalaceMapFives.length == 0" style="text-align:center;padding: 0 12px;">
					                                             暂无数据
					            </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapSixs.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(six,index) in ninePalaceMapSixs" v-if="index != ninePalaceMapSixs.length - 1">
					                   {{six}}、
					                 </span>
					                 <span v-for="(six,index) in ninePalaceMapSixs" v-if="index == ninePalaceMapSixs.length - 1">
					                   {{six}}
					                 </span>
					             </p>
					             <p v-else-if="ninePalaceMapSixs.length == 0" style="text-align:center;padding: 0 12px;">
					                                                暂无数据
					             </p>
                              </td>
                            </tr>
                            <tr>
                              <td style="font-weight:bold;">
                                 <p>
                                   <span>
                                                                                            低
                                   </span>
					             </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapOnes.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(one,index) in ninePalaceMapOnes" v-if="index != ninePalaceMapOnes.length - 1">
					                   {{one}}、
					                 </span>
					                 <span v-for="(one,index) in ninePalaceMapOnes" v-if="index == ninePalaceMapOnes.length - 1">
					                   {{one}}
					                 </span>
					            </p>
					            <p v-else-if="ninePalaceMapOnes.length == 0" style="text-align:center;padding: 0 12px;">
					                                                暂无数据
					            </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapTwos.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(two,index) in ninePalaceMapTwos" v-if="index != ninePalaceMapTwos.length - 1">
					                   {{two}}、
					                 </span>
					                 <span v-for="(two,index) in ninePalaceMapTwos" v-if="index == ninePalaceMapTwos.length - 1">
					                   {{two}}
					                 </span>
					            </p>
					            <p v-else-if="ninePalaceMapTwos.length == 0" style="text-align:center;padding: 0 12px;">
					                                               暂无数据
					            </p>
                              </td>
                              <td>
                                 <p v-if="ninePalaceMapThrees.length > 0" style="text-align:center;padding: 0 12px;">
					                 <span v-for="(three,index) in ninePalaceMapThrees" v-if="index != ninePalaceMapThrees.length - 1">
					                   {{three}}、
					                 </span>
					                 <span v-for="(three,index) in ninePalaceMapThrees" v-if="index == ninePalaceMapThrees.length - 1">
					                   {{three}}
					                 </span>
					            </p>
					            <p v-else-if="ninePalaceMapThrees.length == 0" style="text-align:center;padding: 0 12px;">
					                                               暂无数据
					            </p>
                              </td>
                            </tr>
                          </tbody>
                        </table>
					</div>

					<div id="profitAnalysisDiv" style="display: none;height:100%;overflow: auto;">
					     <table class="profittable" style="border-collapse:collapse;text-align: center;" border="1" cellpadding="0" cellspacing="0">
					       <tbody>
					         <tr>
					           <td id="slantTd" style="position: relative;">
                                 <span style="font-weight:bold;position: absolute;bottom: 20px;left: 14px;">
                                                                                        重要性
                                 </span>
					             <span class="profittd" style="position: relative;right: 0;top: 0px;display: block;border-top: 90px solid #fff;border-left: 120px solid transparent;">
                                 </span>
                              </td>
                              <th>
                                 <p>
                                   <span>
                                                                                            低
                                   </span>
					             </p>
                              </th>
                              <th>
                                 <p>
                                   <span>
                                                                                            高
                                   </span>
					             </p>
                              </th>
					         </tr>
					         <tr>
					           <td style="font-weight:bold;">
                                 <p>
                                   <span>
                                                                                            高
                                   </span>
					             </p>
                               </td>
					           <td style="background: #ffe38e;border-right: 1px dashed #fff;border-bottom: 1px dashed #fff;">
					              <p style="margin-bottom: 15px;color:#888;">
					                  <span>
					                                                  令其满意
					                  </span>
					              </p>
					              <p v-if="fourQuadrantGraphTwos.length > 0" style="font-weight:bold;padding: 0 12px;">
					                  <span v-for="(two,index) in fourQuadrantGraphTwos" v-if="index != fourQuadrantGraphTwos.length - 1">
					                    {{two}}、
					                  </span>
					                  <span v-for="(two,index) in fourQuadrantGraphTwos" v-if="index == fourQuadrantGraphTwos.length - 1">
					                    {{two}}
					                  </span>
					             </p>
					             <p v-else-if="fourQuadrantGraphTwos.length == 0" style="font-weight:bold;padding: 0 12px;">
					                                                  暂无数据
					             </p>
					           </td>
					           <td style="background: #ff8270;border-bottom: 1px dashed #fff;">
					              <p style="margin-bottom: 15px;color:#888;">
					                  <span>
					                                                  重点管理
					                  </span>
					              </p>
					              <p v-if="fourQuadrantGraphOnes.length > 0" style="font-weight:bold;padding: 0 12px;">
					                  <span v-for="(one,index) in fourQuadrantGraphOnes" v-if="index != fourQuadrantGraphOnes.length - 1">
					                    {{one}}、
					                  </span>
					                  <span v-for="(one,index) in fourQuadrantGraphOnes" v-if="index == fourQuadrantGraphOnes.length - 1">
					                    {{one}}
					                  </span>
					             </p>
					             <p v-else-if="fourQuadrantGraphOnes.length == 0" style="font-weight:bold;padding: 0 12px;">
					                                                  暂无数据
					             </p>
					           </td>
					         </tr>
					         <tr>
					           <td style="font-weight:bold;">
                                 <p>
                                   <span>
                                                                                            低
                                   </span>
					             </p>
                               </td>
					           <td style="background: #69d7bc;border-right: 1px dashed #fff;">
					              <p style="margin-bottom: 15px;color:#888;">
					                  <span>
					                                                  监督(花最小精力)
					                  </span>
					              </p>
					              <p v-if="fourQuadrantGraphThrees.length > 0" style="font-weight:bold;padding: 0 12px;">
					                  <span v-for="(three,index) in fourQuadrantGraphThrees" v-if="index != fourQuadrantGraphThrees.length - 1">
					                    {{three}}、
					                  </span>
					                  <span v-for="(three,index) in fourQuadrantGraphThrees" v-if="index == fourQuadrantGraphThrees.length - 1">
					                    {{three}}
					                  </span>
					             </p>
					             <p v-else-if="fourQuadrantGraphThrees.length == 0" style="font-weight:bold;padding: 0 12px;">
					                                                  暂无数据
					             </p>
					           </td>
					           <td style="background: #72cbea;">
					              <p style="margin-bottom: 15px;color:#888;">
					                  <span>
					                                                  随时告知
					                  </span>
					              </p>
					              <p v-if="fourQuadrantGraphFours.length > 0" style="font-weight:bold;padding: 0 12px;">
					                  <span v-for="(four,index) in fourQuadrantGraphFours" v-if="index != fourQuadrantGraphFours.length - 1">
					                    {{four}}、
					                  </span>
					                  <span v-for="(four,index) in fourQuadrantGraphFours" v-if="index == fourQuadrantGraphFours.length - 1">
					                    {{four}}
					                  </span>
					             </p>
					             <p v-else-if="fourQuadrantGraphFours.length == 0" style="font-weight:bold;padding: 0 12px;">
					                                                  暂无数据
					             </p>
					           </td>
					         </tr>
					       </tbody>
					     </table>
					</div>

				</div>

			</div>

		</div>

<!--            <Modal  v-model="isImport" :title="'批量导入'" width="500px" :mask-closable="false"-->
<!--                    :styles="{top: '70px'}" class="vertical-center-modal">-->
<!--                <div style="font-size: 15px">导入将覆盖原有数据。若需保留原数据，请导出数据修改后再执行！</div>-->
<!--                <div slot="footer">-->
<!--                    <i-Button type="text" size="large" @click="cancelImport">取消</i-Button>-->
<!--                    -->
<!--                </div>-->
<!--            </Modal>-->

		<Upload ref="file" :action="fileUploadUrl" style="display: inline-flex;"
				:on-success="fileUploadSuccess"
				:on-error="fileUploadError"
				:before-upload="beforeUploadFile"
				:data="uploadData"
				:show-upload-list="false"
		>
			<i-Button type="primary" ref="importData" size="large" style="display: none">确定</i-Button>
		</Upload>

			<Modal v-model="importFromFmModel" :title="'从组织架构导入'" width="60%" :mask-closable="false"
				   :styles="{top: '70px'}" class="vertical-center-modal">
                <div>请勾选需要从组织架构中导入的人员</div>
				<i-table border ref="ifmTable" :columns="fmColumns" :data="fmData"
						 @on-selection-change="selectFm" height="450"
				>
				</i-table>

				<!--<filter-table :dataTable="true" :columns="fmColumns" :data="fmData"  :height="300">-->
				<!--</filter-table>-->

				<div slot="footer">
					<i-Button type="text" size="large" @click="cancelIFM">取消</i-Button>
					<i-Button type="primary" size="large" @click="confirmIFM">确定</i-Button>
				</div>
			</Modal>

			<Modal v-model="prjInfoModal"  id="prj_info_modal" :title="isAdd ? '新增干系人' : '编辑干系人'" width="800" :mask-closable="false"
			:styles="{top: '70px'}" @on-cancel=prjModalCancel class-name="prj-info-modal">
			<i-form ref="prjInfoForm" :model="prjInfo.matterPsns" style="margin-top:0px;" label-position="right"
				:rules="prjInfoValidate" :label-width="100">
				<Form-Item label="干系人类型" prop="type" ref="prjInfoFormType">
					<i-Select :disabled="!isAdd" @on-change="matterPsnTypeChange" v-model="prjInfo.matterPsns.type.name" placeholder="请选择干系人类型"
							  class="province">
						<i-Option v-for="p in matterPsnType" :value="p.defName">{{ p.defName }}</i-Option>
					</i-Select>
				</Form-Item>
				<Form-Item label="姓名" prop="name" style="position: relative;">
					<i-input disabled v-if="!isAdd"  clearable placeholder="请输入姓名" v-model="prjInfo.matterPsns.name" ></i-input>

					<i-input v-if="prjInfo.matterPsns.type.name != '亚信侧'  && isAdd" @on-change="formBlur('name')" @on-blur="nameAndCompanyBlur"  clearable placeholder="请输入姓名" v-model="prjInfo.matterPsns.name" ></i-input>

					<lks-load-user-fuzzy v-on:clear_user_info="clearUser" v-if="prjInfo.matterPsns.type.name === '亚信侧'  && isAdd" :user.sync="nameInfo"></lks-load-user-fuzzy>
				</Form-Item>

				<Form-Item label="归属省份" prop="company" :class="{'ivu-form-item-required':prjInfo.matterPsns.type.name != '亚信侧'}">
					<i-input type="text" v-if="!isAdd && prjInfo.matterPsns.type.name === '客户侧'" :disabled="!isAdd && prjInfo.matterPsns.type.name === '客户侧'" v-model="prjInfo.matterPsns.company" placeholder="请输入归属省份"></i-input>
					<Cascader v-if="isAdd || prjInfo.matterPsns.type.name != '客户侧'" :data="provs" v-model="prjInfo.matterPsns.company" @on-change="companyChange"></Cascader>

					<!--					<i-select-->
					<!--							v-if="prjInfo.matterPsns.type.name != '亚信侧'  && prjInfo.matterPsns.type.name"-->
					<!--							v-model="prjInfo.matterPsns.company"-->
					<!--							filterable-->
					<!--							remote-->
					<!--							:remote-method="remoteCompany"-->
					<!--							:loading="remoteCompanyLoading"-->
					<!--					>-->
					<!--						<i-option v-for="(item,index) in companyList" :value="item.value" :key="index">{{item.label}}</i-option>-->
					<!--					</i-select>				-->
				</Form-Item>

				<Form-Item label="部门" prop="dept" :class="{'ivu-form-item-required':prjInfo.matterPsns.type.name === '亚信侧'}">
					<i-input type="text" v-model="prjInfo.matterPsns.dept" @on-change="formBlur" placeholder="请输入部门"></i-input>
				</Form-Item>

				<Form-Item label="电话" >
					<i-input type="text" v-model="prjInfo.matterPsns.phone" @on-change="formBlur" placeholder="请输入电话"></i-input>
				</Form-Item>
				<Form-Item label="邮箱" >
					<i-input type="text" v-model="prjInfo.matterPsns.mailbox" @on-change="formBlur" placeholder="请输入邮箱"></i-input>
				</Form-Item>

				<Form-Item label="职务" >
					<i-input type="text" v-model="prjInfo.matterPsns.position" @on-change="formBlur" placeholder="请输入职务"></i-input>
				</Form-Item>

				<Form-Item label="重要性" >
					<i-Select v-model="prjInfo.matterPsns.imptLevel.name" @on-change="formBlur" placeholder="请选择重要性"
						class="province">
						<i-Option v-for="p in matterPsnImptLevel" :value="p.defName">{{ p.defName }}</i-Option>
					</i-Select>
				</Form-Item>
				<Form-Item label="参与度" >
					<i-Select v-model="prjInfo.matterPsns.joinLevel.name" @on-change="formBlur" placeholder="请选择参与度"
						class="province">
						<i-Option v-for="p in matterPsnJoinLevel" :value="p.defName">{{ p.defName }}</i-Option>
					</i-Select>
				</Form-Item>
				<Form-Item label="立场" >
					<i-Select v-model="prjInfo.matterPsns.standpoint.name" @on-change="formBlur" placeholder="请选择立场"
						class="province">
						<i-Option v-for="p in matterPsnsStandpoint" :value="p.defName">{{ p.defName }}</i-Option>
					</i-Select>
				</Form-Item>
				<Form-Item label="个人诉求或期望">
					<i-input type="textarea" v-model="prjInfo.matterPsns.expect" @on-change="formBlur" :autosize="{minRows: 3,maxRows: 6}"></i-input>
				</Form-Item>
				<Form-Item label="负责内容" >
					<i-input type="textarea" v-model="prjInfo.matterPsns.duty" @on-change="formBlur" :autosize="{minRows: 3,maxRows: 6}"></i-input>
				</Form-Item>
				<Form-Item label="个人工作特点" >
					<i-input type="textarea" v-model="prjInfo.matterPsns.trait" @on-change="formBlur" :autosize="{minRows: 3,maxRows: 6}"></i-input>
				</Form-Item>
				<Form-Item label="个人爱好"  class="long-item">
					<i-input type="text" v-model="prjInfo.matterPsns.hobby" @on-change="formBlur" placeholder="请输入个人爱好"></i-input>
				</Form-Item>
				<Form-Item label="沟通机制" >
					<i-input type="text" v-model="prjInfo.matterPsns.talkChannel" @on-change="formBlur" placeholder="请输入沟通机制"></i-input>
				</Form-Item>
				<Form-Item label="备注" >
					<!-- <Date-Picker type="date" v-model="prjInfo.actualOnlineRptDate" placeholder="请选择实际上线报告日期" style="width: 100%;"></Date-Picker> -->
					<i-input type="textarea" v-model="prjInfo.matterPsns.note" @on-change="formBlur" :autosize="{minRows: 3,maxRows: 6}"></i-input>
				</Form-Item>

			</i-form>

			<div slot="footer">
				<i-Button type="text" size="large" @click="prjModalCancel">取消</i-Button>
				<i-Button type="primary" size="large" @click="prjModalOk">确定</i-Button>
			</div>
		</Modal>

		<stakeholder-library-import v-if="stakeholderLibraryImportShow" :prj-id="prjId" :modal-show="stakeholderLibraryImportShow" :table-height="tableHeight"></stakeholder-library-import>

		<iframe width="0" height="0" style="display:none;" id="exportExcelFrame"></iframe>
	</div>
</body>
</html>
<script type="text/javascript">

	var prjBizHttpRequest 	= linkus.location.prjbiz;
	var prjHttpRequest 		= linkus.location.prj;
	var prjUserHttpRequest 	= linkus.location.prjuser;
	var prjKmHttpRequest 	= linkus.location.km;

	var checkReportView = new Vue({

		el 		: '#main',
		data 	: function (){
			var sf = this;

			const validateType = function (rule, value, callback) {
				if (!sf.prjInfo.matterPsns.type.name) {
					callback(new Error('请选择干系人类型'));
				} else {
					callback();
				}
			};
			return {
				/**
				 * 	CAS login user vue property
				 */
				imptLevel       : [{cid: "5df758b0c56a424c334704e8", name: "高"},
				                   {cid: "5df758bfc56a424c334704e9", name: "中"},
				                   {cid: "5df758ccc56a424c334704ea", name: "低"}],
				standpoint      : [{cid: "5df7594ac56a424c334704ee", name: "完全支持"},
				                   {cid: "5df7595ac56a424c334704ef", name: "中立"},
				                   {cid: "5df7596bc56a424c334704f0", name: "不支持"}],
				ninePalaceMapOnes       : [],
				ninePalaceMapTwos       : [],
				ninePalaceMapThrees     : [],
				ninePalaceMapFours      : [],
				ninePalaceMapFives      : [],
				ninePalaceMapSixs       : [],
				ninePalaceMapSevens     : [],
				ninePalaceMapEights     : [],
				ninePalaceMapNines      : [],
				fourQuadrantGraphOnes   : [],
				fourQuadrantGraphTwos   : [],
				fourQuadrantGraphThrees : [],
				fourQuadrantGraphFours  : [],
				loginUser	 	: {},
				fileUploadUrl   : linkus.location.prj + "/prjInfoCtrl/importFile.action",
				/**
				 * 	Project combox vue property
				 */
				prjInfoList 	: [],
				firstAndLast 	: {},
				prjName 		: '',
				prjId 			: '',
				dmpPrjId 		: '',
				prjCode			: '',
				idValue 		: '',
				prjIdValue		: '',
				mainShowFlag	: false,
				isPower	: false,
				isPrjSet: true,
				/**
				 * 	Project daterange vue property
				 */
				startRangeEndDate : [],
				loadingAjax:{},
				selectMatterPsnType:'',
				selectMatterPsnImptLevel:'',
				selectStatus:'',
				matterPsnType:[],
				matterPsnImptLevel:[],
				matterPsnJoinLevel:[],
				matterPsnsStandpoint:[],
				statusList:[
					{
						name: '已提交',
						codeName: 'isSubmitted'
					},
					{
						name: '已审批通过',
						codeName: 'isApproved'
					},
					{
						name: '审批不通过',
						codeName: 'isFailed'
					},
				],
				prjInfo: {
					matterPsns:{
						name	:	null,
						phone	:	null,
						mailbox	:	null,
						type	:	{
							name	:	null
						},
						dept	:	null,
						position	:	null,
						company	:	[],
						imptLevel	:	{
							name	:	null
						},
						joinLevel	:	{
							name	:	null
						},
						standpoint	:	{
							name	:	null
						},
						expect	:	null,
						duty	:	null,
						trait	:	null,
						hobby	:	null,
						talkChannel	:	null,
						note	:	null
					}
				},
				prjInfoValidate:{
					name: [{
						required: true,
						type: 'string',
						message: '请输入姓名',
						trigger: 'change'
					}],
					type:[{
						required: true,
						validator: validateType
					}],
					dept: [{
						required: false,
						type: 'string',
						message: '请输入部门'
					}],
					company: [{
						required: false,
						type: 'array',
						message: '请输入归属省份'
					}],
				},
				isAdd	:	true,
				prjInfoModal: false,
				uploadData      : {
					prjId 	: null
				},
				loading     : false,
				buPrjBudgetAdmin: false,

                // 从组织架构导入干系人
                importFromFmModel: false,
                // 加载组织架构人员信息
                loadingIFM: false,
                // 选中的干系人
                selectedFms: [],
                fmColumns: [
                    {
                        type: 'selection',
                        width: 60,
                        key: 'selection',
                        align: 'center'
                    },
                    {
                        title: '序',
                        key: 'rowNo',
                        width: 60
                    },
                    {
                        title: '姓名',
                        key: 'userName',
                        filter: {
                            type: 'Input'
                        }
                    },
                    {
                        title: '归属小组',
                        key: 'groupName',
                        filter: {
                            type: 'Input'
                        },
                    },
                    {
                        title: '角色',
                        key: 'roleName',
                        filter: {
                            type: 'Input'
                        },
                    }
                ],
                fmData: [],
                searchUserList:[],
                showSearchUser:false,
                isImport:false,

				selectDataMap:{
					type:[],
					imptLevel:[],
					joinLevel:[],
					standpoint:[],
				},
				param:{},
				selectDatas:[],
				stakeHolderInfoLoading:false,
				tableHeight:200,
				stakeHolderInfoData:[],
				stakeHolderInfoColumns:[
					{
						type: 'selection',
						width: 40,
						align: 'center',
						key: 'selection',
					},
					{
						title: '序',
						type: 'index',
						width: 60,
					},
					{
						title: '姓名',
						key: 'name',
						width: 60,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '电话',
						key: 'phone',
						width: 120,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '邮箱',
						key: 'mailbox',
						width: 160,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '干系人类型',
						key: 'type',
						width: 100,
						filter: {
							type: 'Select'
						},
						render:function (h,param) {
							var typeName = param.row.type ? param.row.type.name : '';
							return h('div',typeName)
						}
					},
					{
						title: '部门',
						key: 'dept',
						width: 100,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '职务',
						key: 'position',
						width: 100,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '归属省份',
						key: 'company',
						width: 100,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '重要性',
						key: 'imptLevel',
						width: 60,
						filter: {
							type: 'Select'
						},
						render:function (h,param) {
							var imptLevel = param.row.imptLevel ? param.row.imptLevel.name : '';
							return h('div',imptLevel)
						}
					},
					{
						title: '参与度',
						key: 'joinLevel',
						width: 140,
						filter: {
							type: 'Select'
						},
						render:function (h,param) {
							var imptLevel = param.row.joinLevel ? param.row.joinLevel.name : '';
							return h('div',imptLevel)
						}
					},
					{
						title: '立场',
						key: 'standpoint',
						width: 80,
						filter: {
							type: 'Select'
						},
						render:function (h,param) {
							var imptLevel = param.row.standpoint ? param.row.standpoint.name : '';
							return h('div',imptLevel)
						}
					},
					{
						title: '个人诉求或期望',
						key: 'expect',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '负责内容',
						key: 'duty',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '个人工作特点',
						key: 'trait',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '个人爱好',
						key: 'hobby',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '沟通机制',
						key: 'talkChannel',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
					{
						title: '备注',
						key: 'note',
						width: 140,
						filter: {
							type: 'Input'
						},
					},
				],

				nameInfo:{},
				oldEditData:{},

				companyList:[],
				remoteCompanyLoading:false,
				provinceAjax:'',

				stakeholderLibraryImportShow:false,

				// 所有省份信息
				provs: [],
				isPmUser: false,
				isPrjAdmin: false,
			}


		},
		watch:{
			// 选择用户后，更改干系人信息
			nameInfo:function (newV,oldV) {
				var sf = this;
				if(!sf.isAdd){
					return
				}
				if(JSON.stringify(newV) != '{}'){
					var vo = {
						userId:newV.id,
					};
					sf.resetFieldsPart();
					$.ajax({
						headers:{'Content-Type':'application/json;charset=utf-8'},
						url	: linkus.location.prj+'/prjSthCtrl/query/stakeholder/'+sf.prjId,
						type:'get',
						data:vo,
						success:function(res){
							if(res.success){
								sf.oldEditData = {};
								if(!!res.data && !!res.data.data){
									var msg = res.data.source === 'prjStakeHolder' ? '该人员已存在项目干系人中，是否进行修改?'  : '该人员已存在干系人库中，是否从干系人库导入?';
									sf.$Modal.confirm({
										title: '提示',
										content: msg,
										onOk:function (){
											sf.oldEditData = res.data.data;
											sf.setEditData([res.data.data]);
											if(rows[0].name!=undefined && rows[0].name!=""){
												sf.prjInfo.matterPsns.name = rows[0].name;
											}
										},
										onCancel:function (){
											sf.prjInfo.matterPsns.name = '';
											sf.nameInfo = {};
										}
									})
								}else{
									// 姓名、电话、邮箱、部门
									sf.prjInfo.matterPsns.name = newV.userName + "/" + newV.loginName;
									sf.prjInfo.matterPsns.mailbox = newV.mailBox;
									sf.prjInfo.matterPsns.dept = newV.ccName;
									sf.$refs.prjInfoForm.validateField('name')
								}
							}else{
								sf.oldEditData = {};
								sf.$Message.error(res.message);
							}
						}
					});

				}
			}
		},
		created : function() {
			this.queryLoginUserByCASLoginName();
			// this.loadPrjInfoFirstAndList(this.prjName);
			//this.querySelectParam();
			this.getMatterPsnsType();
			this.getMatterPsnsImptLevel();
			this.getMatterPsnsJoinLevel();
			this.getMatterPsnsStandPoint();
			this.sureLoginUserIsBuPrjBudgetAdmin();
			if(!Vue.evtHub) {
				Vue.evtHub = new Vue();
			}
		},
		mounted : function() {

			var _this = this;
			this.$nextTick(function() {

			});
			Vue.evtHub.$on("prj-choosed", function (data) {
				_this.prjId = data.prjId;
				_this.prjName = data.prjName;
				_this.prjCode = data.prjCode;
				_this.prjIdValue = data.prjIdValue;

				_this.isPmUser = false;
				if(!!data.pmUser && JSON.stringify(data.pmUser) != "{}"
				   && !!_this.loginUser && JSON.stringify(_this.loginUser) != "{}"
				   && data.pmUser.userId == _this.loginUser.id) {
					_this.isPmUser = true;
				}
				_this.checkIsPrjAdmin();

				_this.loadStakeHolderInfo();

			});
			Vue.evtHub.$on("prj-choose-clear", function(data){
				_this.prjId = '';
				_this.prjName = '';
				_this.prjCode = '';
				_this.prjIdValue = '';
				_this.isPmUser = false;
				_this.isPrjAdmin = false;
			});
			Vue.evtHub.$on("prj-set-changed", function (data) {
				_this.isPrjSet = data.isPrjSet;
				_this.loadStakeHolderInfo();
			});

            window.addEventListener("click", function(){
                this.showSearchUser = false;
			});

			//表格筛选
			Vue.evtHub.$on('load-data', function (data) {
				if(_this.stakeholderLibraryImportShow){
					return;
				}
				if(!!data){
					_this.param[data.fieldId] = data.value;
					_this.changeType();
				}
			});

			Vue.evtHub.$on('select-data', function (data) {
				if(_this.stakeholderLibraryImportShow){
					return;
				}
				if(!!data && !!data.selection && data.selection.length > 0){
					_this.selectDatas = data.selection;
				}else{
					_this.selectDatas = [];
				}
			});

			Vue.evtHub.$on('stakeholder-library-import-close', function () {
				_this.stakeholderLibraryImportShow =false;
			});

			Vue.evtHub.$on('stakeholder-library-import', function () {
				_this.stakeholderLibraryImportShow =false;
				_this.changeType();
			});




			_this.tableHeight= _this.$refs.layoutContent.offsetHeight - 186;
			_this.getPrjProvs();
		},
		methods : {
			//人员组件清除
			clearUser:function(){
				var sf = this;
				sf.prjInfo.matterPsns.name = '';
				sf.nameInfo = {};
				sf.resetFieldsPart();
			},

			getPrjProvs: function () {
				var sf = this;

				$.ajax({
					url: linkus.location.prj + '/prjInfoCtrl/queryPrjProvTreeByLoginUser.action',
					type: 'post',
					success: function(value) {
						var treeInfo = [];
						if(value){
							sf.parseJson(value,treeInfo,0);
						}
						sf.provs = treeInfo.children || [];
					},
					error: function(text){
						sf.$Message.error({
							content: text.responseText.slice(17,text.responseText.length-2),
							duration: 3
						});
					}
				});
			},
			parseJson:function(jsonObj,target,count){
				var sf =this;
				// 循环所有键
				for(var key in jsonObj){
					var element = jsonObj[key];
					// 1.判断是对象或者数组
					if(!element.label){
						continue;
					}
					var params = {};
					params = {
						label: element.label,
						value:element.label,
					};

					if(element.children){
						target.children = target.children || [];
						target.children.push(params);
						sf.parseJson(element.children,params)
					}else{
						target.children = target.children || [];
						target.children.push(params)
					}
				}
			},


			// //模糊搜索省份
			// remoteCompany:function(value){
			// 	var sf = this;
			// 	sf.remoteCompanyLoading = true;
			// 	if (sf.provinceAjax && sf.provinceAjax.abort) {
			// 		sf.provinceAjax.abort();
			// 	}
			// 	sf.provinceAjax = $.ajax({
			// 		headers:{'Content-Type':'application/json;charset=utf-8'},
			// 		url	: linkus.location.prj+'/prjSthCtrl/query/province',
			// 		type:'get',
			// 		data:{
			// 			provinceFuzzy:value,
			// 		},
			// 		success:function(res){
			// 			if(res.success){
			// 				if(!!res.data && res.data.length > 0){
			// 					sf.companyList = [];
			// 					res.data.forEach(function (item,i) {
			// 						var param = {
			// 							label:item.company,
			// 							value:item.company,
			// 						};
			// 						sf.companyList.push(param);
			// 					})
			// 				}else{
			// 					sf.$Modal.confirm({
			// 						title: '提示',
			// 						content: '请再次确认是否新增该省份?',
			// 						onOk:function (){
			// 							var param = {
			// 								label:value,
			// 								value:value,
			// 							};
			// 							sf.companyList.push(param);
			// 						},
			// 					})
			// 				}
			// 			}else{
			// 				sf.$Message.error(res.message);
			// 			}
			//
			// 			sf.remoteCompanyLoading = false;
			// 		}
			// 	});
			// },

			//打开干系人库导入
			openImportLibrary:function(){
				var sf = this;
				sf.stakeholderLibraryImportShow = true;
			},

			//form先填校验
			formBlur:function(type){
				var sf = this;
				if(!sf.isAdd || !sf.prjInfoModal){
					return
				}
				if(!sf.prjInfo.matterPsns.type.name){
					sf.$Message.error("请先选择干系人类型");
					return
				}
				if(type === 'name' || type === 'company'){
					return;
				}

				if(!type.data){
					return;
				}

				if(sf.prjInfo.matterPsns.type.name != '亚信侧' && sf.prjInfo.matterPsns.type.name){
					if(!sf.prjInfo.matterPsns.company || !sf.prjInfo.matterPsns.name){
						sf.$Message.error("请先填写省份和姓名");
						return;
					}
				}

				if(sf.prjInfo.matterPsns.type.name === '亚信侧'){
					if(!sf.prjInfo.matterPsns.name){
						sf.$Message.error("请先填写姓名");
						return;
					}
				}
			},
			companyChange:function(value){
				var sf =this;
				console.log(value);
				sf.$nextTick(function () {
					sf.nameAndCompanyBlur();
				})

			},
			//非亚信侧-姓名-省份 失焦
			nameAndCompanyBlur:function(){
				var sf = this;
				if(!sf.isAdd){
					return
				}
				if(sf.prjInfo.matterPsns.type.name != '亚信侧' && sf.prjInfo.matterPsns.type.name){
					if(sf.prjInfo.matterPsns.company && sf.prjInfo.matterPsns.company.length>0 && sf.prjInfo.matterPsns.name){
						var company =  sf.prjInfo.matterPsns.company[sf.prjInfo.matterPsns.company.length-1];
						$.ajax({
							headers:{'Content-Type':'application/json;charset=utf-8'},
							url	: linkus.location.prj+'/prjSthCtrl/query/stakeholder/company',
							type:'get',
							data:{
								prjId:sf.prjId,
								company:company,
								userName:sf.prjInfo.matterPsns.name
							},
							success:function(res){
								if(res.success){
									sf.oldEditData = {};
									if(!!res.data && !!res.data && res.data.data.length >0){
										var msg = res.data.source === 'prjStakeHolder' ? '该人员已存在项目干系人中，是否进行修改?'  : '该人员已存在干系人库中，是否从干系人库导入?';
										sf.$Modal.confirm({
											title: '提示',
											content: msg,
											onOk:function (){
												sf.oldEditData = res.data.data[0];
												sf.setEditData(res.data.data);
											},
											onCancel:function (){
												sf.prjInfo.matterPsns.name = '';
											}
										})
									}
									// else{
									// 	sf.$Modal.confirm({
									// 		title: '提示',
									// 		content: '请再次确认是否新增该干系人?',
									// 		onOk:function (){
									//
									// 		},
									// 		onCancel:function (){
									// 			sf.prjInfo.matterPsns.name = '';
									// 		}
									// 	})
									// }
								}else{
									sf.oldEditData = {};
									sf.$Message.error(res.message);
								}
							}
						});
					}
				}
			},

			matterPsnTypeChange:function(value){
				var sf = this;
				if(!sf.isAdd){
					return
				}
				if(!value){
					return;
				}
				sf.prjInfo.matterPsns.name = '';
				sf.nameInfo ={};
				sf.oldEditData = {};
				sf.resetFieldsPart();
				var propKey = '';
				if(sf.prjInfo.matterPsns.type.name === '亚信侧'){
					sf.$set(sf.prjInfoValidate.company[0],'required',false);
					sf.$set(sf.prjInfoValidate.dept[0],'required',true);
					// sf.prjInfoValidate.company[0].required = false;
					// sf.prjInfoValidate.dept[0].required = true;

				}else{
					sf.$set(sf.prjInfoValidate.company[0],'required',true);
					sf.$set(sf.prjInfoValidate.dept[0],'required',false);

					// sf.prjInfoValidate.company[0].required = true;
					// sf.prjInfoValidate.dept[0].required = false;
				}
				sf.$refs.prjInfoForm.validateField('company');
				sf.$refs.prjInfoForm.validateField('dept');
			},

            "cancelImport":function () {
                this.isImport = false;
            },

            "openImport": function(){
				this.$refs.importData.$el.click();
                this.isImport = true;
            },
            // 打开从组织架构导入
            "openIFM": function(){
                var sf = this;
                sf.loadingIFM = true;
                // 打开选择干系人model
                sf.importFromFmModel = true;
                // 获取项目下组织架构人员信息
                sf.queryFmInfo();
            },

            // 确认从组织架构导入干系人
            "confirmIFM": function(){
                var sf = this
                // sf.selectFm($refs.selection);
                // 获取项目下组织架构人员信息
                sf.importFromFramework();
            },

            // 取消从组织架构导入干系人
            "cancelIFM": function(){
                var sf = this;
                sf.importFromFmModel = false;
            },

            // 获取项目下组织架构人员信息
            "queryFmInfo": function(){
                var sf = this;
                $.ajax({
                    url: prjHttpRequest + '/prjInfoCtrl/getOrgStructInfo.action',
                    type: 'post',
                    async: true,
                    data: {
                        prjId: sf.prjId,
                    },
                }).then(function (data) {
                    sf.fmData = [];
                    if(data && data.length>0){
                        for(var i=0; i < data.length; i++){
                            // 默认勾中项目领导层、项目PMO、项目管理层所有人员，以及其它小组组长角色人员
                            if(data[i].groupName == '项目领导层' || data[i].groupName == '项目PMO'
                                || data[i].groupName == '项目管理层' || data[i].roleName == '组长' || data[i].roleName == '项目组长'){
                                data[i]._checked = true;
                            }
                            sf.fmData.push(data[i]);
                        }
                    }
                    sf.loadingIFM = false;
                }).fail(function () {
                    sf.loadingIFM = false;
                });
            },

            // 选择干系人
            "selectFm": function(selection){
                var sf = this;
                sf.selectedFms = [];
                for (var i = 0; i < selection.length; i++) {
                    sf.selectedFms.push(JSON.parse(JSON.stringify(selection[i])));
                }
            },

            // 从组织架构导入干系人
            "importFromFramework": function(){
                var sf = this;
                var selectedFms = sf.$refs.ifmTable.getSelection();
                if(selectedFms.length > 0){
                    var ifmData = {};
                    var matterPsns = [];
                    for(var i = 0; i < selectedFms.length; i++){
                        var matterPsn = {};
                        if(selectedFms[i].userName && selectedFms[i].userName.indexOf("/")>-1){
                            matterPsn.name = selectedFms[i].userName.split("/")[0];
                        }else{
                            matterPsn.name = selectedFms[i].userName;
                        }

                        matterPsn.mailbox = selectedFms[i].email;
                        matterPsn.dept = selectedFms[i].deptName;
                        matterPsn.type = {};
                        matterPsn.type.cid = "5df75873c56a424c334704e6";
                        matterPsn.type.name = "亚信侧";
                        matterPsn.company = "亚信";
                        matterPsn.emp = {
							userName:matterPsn.name,
							userId:selectedFms[i].userId,
						};
                        matterPsns.push(matterPsn);
                    }
                    ifmData.prjId = sf.prjId;
                    ifmData.matterPsns = matterPsns;
                    $.ajax({
                        headers: {'Content-Type' : 'application/json;charset=utf-8'},
                        url: prjHttpRequest + '/prjInfoCtrl/saveByOrgStruct.action',
                        type: 'post',
                        dataType: 'text',
                        async: false,
                        data:  JSON.stringify(ifmData),
                        success	 	: 	function(){
                            sf.$Message.success("添加干系人成功");
                            sf.loadStakeHolderInfo();
                            sf.importFromFmModel = false;
                        },
                        error	 	: 	function(){
                            sf.$Modal.error({
                                title	:	'错误',
                                content	: 	'新增干系人失败，请联系管理员!'
                            });
                        }
                    });
                }

            },

            // 查询用户
            "searchUser":function(){
                var sf = this;
                sf.showSearchUser = true;
                var value = sf.prjInfo.matterPsns.name;
                $.ajax({
                    type	:	"post",
                    url		: 	linkus.location.prjuser+'/sysUserCtrl/findUserByFuzzy.action',
                    async   :   true,
                    dataType:'JSON',
                    data	:	{
                        "value" :value
                    },
                    success	: 	function(text){
                        var resultData = text;
                        if (!!resultData && resultData.length > 0) {
                            sf.searchUserList = resultData;
                        }else{
                            sf.searchUserList = [];
                        }
                    }
                });
                // sf.prjInfo.matterPsns.name = null;
                sf.prjInfo.matterPsns.mailbox = null;
                sf.prjInfo.matterPsns.dept = null;
            },


            "switchTab" : function(name) {
				var sf = this;
      			if(name == 'stakeholderManageTab'){
      				$('#stakeholderManageDiv').show();
      				$('#standpointAnalysisDiv').hide();
      				$('#profitAnalysisDiv').hide();
      			}else if(name == 'standpointAnalysisTab'){
      				$('#stakeholderManageDiv').hide();
      				$('#standpointAnalysisDiv').show();
      				$('#profitAnalysisDiv').hide();
      			}else if(name == 'profitAnalysisTab'){
      				$('#stakeholderManageDiv').hide();
      				$('#standpointAnalysisDiv').hide();
      				$('#profitAnalysisDiv').show();
      			}
			},
			"sureLoginUserIsBuPrjBudgetAdmin" : function() {
      			var sf = this;

      			$.ajax({
					dataType 	:  	"json",
					url 	 	:  	linkus.location.prjuser + "/sysDefRoleUserCtrl/checkBuPrjBudgetAdmin.action",
					success		: 	function(result){
										if(!!result){
											sf.buPrjBudgetAdmin = true;
										}else{
											sf.buPrjBudgetAdmin = false;
										}
									},
					error	 	: 	function(text){
										sf.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询当前用户角色错误，请联系管理员!'
					                  	});
									}
				 });
      		},
			"getMatterPsnsType": function(){
				var sf = this;
				$.ajax({
					url: linkus.location.prjuser + "/sysDefCtrl/getSysDefBySrcAndCode.action",
					data: {
						srcDefId: '5df725bbc56a424c334704df',
						codeName: 'SYS_PARA_VALUE'
					},
					success: function (data) {
						sf.matterPsnType = data ||[];
						sf.selectDataMap.type = [];
						sf.matterPsnType.forEach(function (item,i){
							var param = {
								label:item.defName,
								value:item.defName,
							};
							sf.selectDataMap.type.push(param);
						})
					}
				});
			},
			"getMatterPsnsImptLevel": function(){
				var sf = this;
				$.ajax({
					url: linkus.location.prjuser+'/sysDefCtrl/getSysDefBySrcAndCode.action',
					data: {
						srcDefId: '5df72642c56a424c334704e0',
						codeName: 'SYS_PARA_VALUE'
					},
					success: function (data) {
						sf.matterPsnImptLevel = data ||[];
						sf.selectDataMap.imptLevel = [];
						sf.matterPsnImptLevel.forEach(function (item,i){
							var param = {
								label:item.defName,
								value:item.defName,
							};
							sf.selectDataMap.imptLevel.push(param);
						})
					}
				});
			},
			"getMatterPsnsJoinLevel": function(){
				var sf = this;
				$.ajax({
					url: linkus.location.prjuser+'/sysDefCtrl/getSysDefBySrcAndCode.action',
					data: {
						srcDefId: '5df7266bc56a424c334704e1',
						codeName: 'SYS_PARA_VALUE'
					},
					success: function (data) {
						sf.matterPsnJoinLevel = data ||[];
						sf.selectDataMap.joinLevel = [];
						sf.matterPsnJoinLevel.forEach(function (item,i){
							var param = {
								label:item.defName,
								value:item.defName,
							};
							sf.selectDataMap.joinLevel.push(param);
						})
					}
				});
			},
			"getMatterPsnsStandPoint": function(){
				var sf = this;
				$.ajax({
					url: linkus.location.prjuser+'/sysDefCtrl/getSysDefBySrcAndCode.action',
					data: {
						srcDefId: '5df72694c56a424c334704e2',
						codeName: 'SYS_PARA_VALUE'
					},
					success: function (data) {
						sf.matterPsnsStandpoint = data ||[];
						sf.selectDataMap.standpoint = [];
						sf.matterPsnsStandpoint.forEach(function (item,i){
							var param = {
								label:item.defName,
								value:item.defName,
							};
							sf.selectDataMap.standpoint.push(param);
						})
					}
				});
			},
			/* "querySelectParam": function(){
				var _this = this;
				$.ajax({
    				url:linkus.location.prj+'/prjInfoCtrl/querySelectParam.action',
    				type:'post',
    				dataType:'JSON',
    				success  	: 	function(data) {
						for(var i=0;i<data.length;i++){
							if(data[i].defName == "客户侧" || data[i].defName == "亚信侧" || data[i].defName == "第三方"){
								var matterPsnTypeSingle = new Object();
								matterPsnTypeSingle.codeName = data[i].id;
								matterPsnTypeSingle.name= data[i].defName;
								_this.matterPsnType.push(matterPsnTypeSingle);
							}else if(data[i].defName == "高" || data[i].defName == "中" || data[i].defName == "低"){
								var matterPsnImptLevelSingle = new Object();
								matterPsnImptLevelSingle.codeName = data[i].id;
								matterPsnImptLevelSingle.name= data[i].defName;
								_this.matterPsnImptLevel.push(matterPsnImptLevelSingle);
							}else if(data[i].defName == "月" || data[i].defName == "周" || data[i].defName == "日"){
								var matterPsnJoinLevelSingle = new Object();
								matterPsnJoinLevelSingle.codeName = data[i].id;
								matterPsnJoinLevelSingle.name= data[i].defName;
								_this.matterPsnJoinLevel.push(matterPsnJoinLevelSingle);
							}else if(data[i].defName == "完全支持" || data[i].defName == "中立" || data[i].defName == "不支持"){
								var matterPsnsStandpointSingle = new Object();
								matterPsnsStandpointSingle.codeName = data[i].id;
								matterPsnsStandpointSingle.name= data[i].defName;
								_this.matterPsnsStandpoint.push(matterPsnsStandpointSingle);
							}
						}
    				},
					error    	: 	function(data) {
						self.$Modal.error({
	                   		title	:	'错误',
	                       	content	: 	'查询干系人选择条件失败，请联系管理员!'
	                  	});
    				}
    			})
			} ,*/
			"exportPrjReviewInfo": function(){
				// var sf = this;
				//
				// var prjId = sf.prjId;
				// var isPrjSet = sf.isPrjSet;
				//
				// var matterPsnImptLevel ;
				// var matterPsnType ;
				// for(var i=0;i<sf.matterPsnType.length;i++){
    			// 	if(sf.matterPsnType[i].defName==sf.selectMatterPsnType){
    			// 		matterPsnType = sf.matterPsnType[i].id;
    			// 	}
    			// }
    			// for(var i=0;i<sf.matterPsnImptLevel.length;i++){
    			// 	if(sf.matterPsnImptLevel[i].defName==sf.selectMatterPsnImptLevel){
    			// 		matterPsnImptLevel = sf.matterPsnImptLevel[i].id;
    			// 	}
    			// }
				//
				// sf.$Message.warning({
	            //     content  : '正在导出数据，请勿重复点击导出按钮，请稍等！',
	            //     duration : 1
	            // });
				// var isPrjSetCondition = "";
				// var matterPsnImptLevelCondition = "";
				// var matterPsnTypeCondition = "";
				// if(undefined == isPrjSet){
				// 	isPrjSet="";
				// }
				// isPrjSetCondition = "&isPrjSet="+isPrjSet;
				//
				// if(undefined == matterPsnImptLevel ){
				// 	matterPsnImptLevel = "";
				// }
				// matterPsnImptLevelCondition = "&matterPsnImptLevel="+matterPsnImptLevel;
				// if(undefined == matterPsnType){
				// 	matterPsnType = "";
				// }
				// matterPsnTypeCondition = "&matterPsnType="+matterPsnType;
    			// $("#exportExcelFrame").attr("src",linkus.location.prj + "/prjInfoCtrl/exportMatterPsns.action" +"?prjId=" +
    			// 		prjId+isPrjSetCondition+matterPsnImptLevelCondition+matterPsnTypeCondition);
				//

				var sf = this;
				if (!sf.prjId){
					sf.$Message.error("请选择项目");
					return;
				}
				sf.$Message.warning({
					content  : '正在导出数据，请勿重复点击导出按钮，请稍等！',
					duration : 5
				});
				var url = linkus.location.prj + '/prjInfoCtrl/export/MatterPsns';
				// 创建表单部分
				var params = new Object();
				params.matterPsns  = new Object();
				for(var key in sf.param){
					if(!sf.param[key]){
						continue;
					}
					if(key === 'type' || key === 'imptLevel' || key === 'joinLevel' || key === 'standpoint'){
						params.matterPsns[key] = {
							name:sf.param[key]
						}
					}else{
						params.matterPsns[key] = sf.param[key];
					}
				}

				//params.isPrjSet=sf.isPrjSet;
				params.prjId=sf.prjId;
				var form = document.createElement('form');
				form.style.display = 'none';
				form.action = url;
				form.method = 'post';
				form.enctype="application/json";
				document.body.appendChild(form);
				setObj(params);// 执行添加input框
				function setObj (obj, name) { // obj是你的对象数据，name是你的父级名字
					for (var key in obj) {
						if ((typeof obj[key]) === 'object') { // 如果值是对象，把键和值传到重新递归
							var valueKey = name ? name + '.' + key : key
							setObj(obj[key], valueKey)
						} else { // 如果值不是对象，创建input
							var input = document.createElement('input')
							input.type = 'hidden'
							input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
							input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
							form.appendChild(input)
						}
					}
				}

				console.log(form);
				form.submit();
				form.remove();


			},
			// 文档上传之前
			"beforeUploadFile" : function (data) {
				var sf = this;
				sf.loading = true;
				return true;
			},
			// 上传失败
			"fileUploadError" : function(){

				var sf = this;
				sf.loading = true;
				sf.$Message.error({
					content: "上传失败！",
					duration: 10
				});
				sf.loading = false;
			},
			// 文档上传成功
			"fileUploadSuccess" : function (e) {
				var sf = this;
				sf.loading = true;
				if (e.result == false){
					sf.$Message.error({
						content: e.msg,
						duration: 10
					});
				}
				sf.loadStakeHolderInfo();
				sf.loading = false;
			},
			'downloadTemplate' : function() {
				var iframe = document.createElement("iframe");
				iframe.src = linkus.location.km + '/kmFileCtrl/downloadByName.action?fileName='+encodeURI("项目干系人导入模板.xlsx");
				iframe.style.display = "none";
				document.body.appendChild(iframe);
			},
			//数据清空-部分
			resetFieldsPart:function () {
				var sf =this;
				Vue.set(sf.prjInfo.matterPsns, 'phone', '');
				Vue.set(sf.prjInfo.matterPsns, 'mailbox', '');
				Vue.set(sf.prjInfo.matterPsns, 'dept', '');
				Vue.set(sf.prjInfo.matterPsns, 'position', '');
				Vue.set(sf.prjInfo.matterPsns, 'company', []);
				Vue.set(sf.prjInfo.matterPsns, 'imptLevel', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'joinLevel', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'standpoint', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'expect', '');
				Vue.set(sf.prjInfo.matterPsns, 'duty', '');
				Vue.set(sf.prjInfo.matterPsns, 'trait', '');
				Vue.set(sf.prjInfo.matterPsns, 'hobby', '');
				Vue.set(sf.prjInfo.matterPsns, 'talkChannel', '');
				Vue.set(sf.prjInfo.matterPsns, 'note', '');
			},

			"resetFields": function (isEditClear) {
				var sf = this;
				sf.$refs['prjInfoForm'].resetFields();
				Vue.set(sf.prjInfo.matterPsns, 'name', '');
				Vue.set(sf.prjInfo.matterPsns, 'phone', '');
				Vue.set(sf.prjInfo.matterPsns, 'mailbox', '');
				Vue.set(sf.prjInfo.matterPsns, 'type', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'dept', '');
				Vue.set(sf.prjInfo.matterPsns, 'position', '');
				Vue.set(sf.prjInfo.matterPsns, 'company', []);
				Vue.set(sf.prjInfo.matterPsns, 'imptLevel', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'joinLevel', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'standpoint', {
					cid:null,
					name:null
				});
				Vue.set(sf.prjInfo.matterPsns, 'expect', '');
				Vue.set(sf.prjInfo.matterPsns, 'duty', '');
				Vue.set(sf.prjInfo.matterPsns, 'trait', '');
				Vue.set(sf.prjInfo.matterPsns, 'hobby', '');
				Vue.set(sf.prjInfo.matterPsns, 'talkChannel', '');
				Vue.set(sf.prjInfo.matterPsns, 'note', '');

				sf.pmsPlanDateRange = [];
				sf.subPrjQueryKeywords = '';
				sf.isAdd = true;
				sf.subPrjLoading = false;
				sf.subPrjs = [];
				sf.subPrjsAjaxQuery = null;

				sf.pmUserLoading = false;
				sf.smUserLoading = false;
				sf.psoUserLoading = false;
				sf.users = [];
				sf.bigRegionAndProv = [];

				sf.nameInfo = {};
			},
			//编辑值赋值
			setEditData:function(rows){
				var sf = this;
				sf.oldEditData = rows[0];
				if(rows[0].cid!=undefined && rows[0].cid!=""){
					sf.prjInfo.matterPsns.cid = rows[0].cid;
				}else{
					sf.prjInfo.matterPsns.cid = null;
				}
				if(rows[0].id!=undefined && rows[0].id!=""){
					sf.prjInfo.matterPsns.id = rows[0].id;
				}else{
					sf.prjInfo.matterPsns.id = null;
				}

				if(rows[0].name!=undefined && rows[0].name!=""){
					sf.prjInfo.matterPsns.name = rows[0].name;
					sf.$refs.prjInfoForm.validateField('name')
				}
				if(rows[0].phone!=undefined && rows[0].phone!=""){
					sf.prjInfo.matterPsns.phone = rows[0].phone;
				}
				if(rows[0].mailbox!=undefined && rows[0].mailbox!=""){
					sf.prjInfo.matterPsns.mailbox = rows[0].mailbox;
				}
				if(rows[0].type!=undefined && rows[0].type!=""){
					sf.prjInfo.matterPsns.type = rows[0].type;
				}

				if(sf.prjInfo.matterPsns.type.name === '亚信侧'){
					sf.prjInfoValidate.company[0].required = false;
					sf.prjInfoValidate.dept[0].required = true;
				}else{
					sf.prjInfoValidate.company[0].required = true;
					sf.prjInfoValidate.dept[0].required = false;
				}


				if(rows[0].dept!=undefined && rows[0].dept!=""){
					sf.prjInfo.matterPsns.dept = rows[0].dept;
				}
				if(rows[0].position!=undefined && rows[0].position!=""){
					sf.prjInfo.matterPsns.position = rows[0].position;
				}
				if(rows[0].company!=undefined && rows[0].company!=""){
					if(rows[0].type.name === '客户侧'){
						sf.prjInfo.matterPsns.company =  [rows[0].company]
					}else{
						sf.prjInfo.matterPsns.company =  sf.getCheckedNodeAndParents(sf.provs, [], rows[0].company)
					}
				}
				if(rows[0].imptLevel!=undefined && rows[0].imptLevel!=""){
					sf.prjInfo.matterPsns.imptLevel = JSON.parse(JSON.stringify(rows[0].imptLevel));
				}
				if(rows[0].joinLevel!=undefined && rows[0].joinLevel!=""){
					sf.prjInfo.matterPsns.joinLevel =JSON.parse(JSON.stringify(rows[0].joinLevel));
				}
				if(rows[0].standpoint!=undefined && rows[0].standpoint!=""){
					sf.prjInfo.matterPsns.standpoint =JSON.parse(JSON.stringify(rows[0].standpoint));
				}
				if(rows[0].expect!=undefined && rows[0].expect!=""){
					sf.prjInfo.matterPsns.expect = rows[0].expect;
				}
				if(rows[0].duty!=undefined && rows[0].duty!=""){
					sf.prjInfo.matterPsns.duty = rows[0].duty;
				}
				if(rows[0].trait!=undefined && rows[0].trait!=""){
					sf.prjInfo.matterPsns.trait = rows[0].trait;
				}
				if(rows[0].hobby!=undefined && rows[0].hobby!=""){
					sf.prjInfo.matterPsns.hobby = rows[0].hobby;
				}
				if(rows[0].talkChannel!=undefined && rows[0].talkChannel!=""){
					sf.prjInfo.matterPsns.talkChannel = rows[0].talkChannel;
				}
				if(rows[0].note!=undefined && rows[0].note!=""){
					sf.prjInfo.matterPsns.note = rows[0].note;
				}

			},
			//获取单条数据链
			getCheckedNodeAndParents:function(array, checkedNodeAndParents, curNodeId) {
				var sf = this;
				for(let i = 0; i < array.length; i++) {
					const item = array[i];
					if (item.value === curNodeId) {
						checkedNodeAndParents.push(item.value);
						return checkedNodeAndParents;
					}
					if(item.children && item.children.length > 0) {
						checkedNodeAndParents.push(item.value);
						const rs = sf.getCheckedNodeAndParents(item.children, checkedNodeAndParents, curNodeId);
						if(rs) {
							return rs;
						} else {
							checkedNodeAndParents.pop();
						}
					}
				}
				return false;
			},


			openEditPrjModal:function () {

				var sf = this;
				var rows = sf.selectDatas;

				var founded = false;
				if(rows.length ==1){
					sf.setEditData(rows)
				}else{
					sf.$Message.error("请只勾选一个干系人");
					return;
				}
				sf.isAdd = false;
				sf.prjInfoModal = true;
			},
			"openAddPrjModal": function () {
				var sf = this;
				sf.isAdd = true;
				sf.prjInfoModal = true;
			},
			"prjModalOk": function () {
				var sf = this;
				if (sf.isAdd === true) {
					sf.createPrjInfoFromStakeHolder();
					return;
				}

				if (sf.isAdd === false) {
					sf.updatePrjInfoFromStakeHolder();
				}
				sf.buildSchemeFile = [];
			},
			"prjModalCancel": function () {
				var sf = this;
				sf.prjInfoModal = false;
				sf.resetFields();
				sf.buildSchemeFile = [];
			},
			"loadStakeHolderInfo":function(){

    			var self = this;
				if (!self.prjId){
					self.$Message.error("请选择项目");
					return;
				}
    			var tePrjInfoVo = new Object();
				tePrjInfoVo.matterPsns  = new Object();

				for(var key in self.param){
					if(!self.param[key]){
						continue;
					}
					if(key === 'type' || key === 'imptLevel' || key === 'joinLevel' || key === 'standpoint'){
						tePrjInfoVo.matterPsns[key] = {
							name:self.param[key]
						}
					}else{
						tePrjInfoVo.matterPsns[key] = self.param[key];
					}
				}
    			//tePrjInfoVo.isPrjSet=self.isPrjSet;
    			tePrjInfoVo.prjId=self.prjId;

    			return $.ajax({
    				headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
    				url:linkus.location.prj+'/prjInfoCtrl/queryPrjInfoFromStakeHolder.action',
    				type:'post',
    				dataType:'JSON',
    				data	 	:	JSON.stringify(tePrjInfoVo),
    				success  	: 	function(data) {
    					self.stakeHolderInfoData = data ||[];
    					self.ninePalaceMapOnes = [];
    					self.ninePalaceMapTwos = [];
    					self.ninePalaceMapThrees = [];
    					self.ninePalaceMapFours = [];
    					self.ninePalaceMapFives = [];
    					self.ninePalaceMapSixs = [];
    					self.ninePalaceMapSevens = [];
    					self.ninePalaceMapEights = [];
    					self.ninePalaceMapNines = [];
    					self.fourQuadrantGraphOnes = [];
    					self.fourQuadrantGraphTwos = [];
    					self.fourQuadrantGraphThrees = [];
    					self.fourQuadrantGraphFours = [];
    					for(var i = 0; i < data.length; i++){
    						if(!!data[i].imptLevel && !!data[i].standpoint){

    							if(data[i].imptLevel.cid == self.imptLevel[0].cid
    							  && data[i].standpoint.cid == self.standpoint[0].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapNines.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapNines.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[0].cid
    	    							  && data[i].standpoint.cid == self.standpoint[1].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapEights.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapEights.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[0].cid
    	    							  && data[i].standpoint.cid == self.standpoint[2].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapSevens.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapSevens.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[1].cid
    	    							  && data[i].standpoint.cid == self.standpoint[0].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapSixs.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapSixs.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[1].cid
    	    							  && data[i].standpoint.cid == self.standpoint[1].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapFives.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapFives.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[1].cid
    	    							  && data[i].standpoint.cid == self.standpoint[2].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapFours.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapFours.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[2].cid
    	    							  && data[i].standpoint.cid == self.standpoint[0].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapThrees.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapThrees.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[2].cid
    	    							  && data[i].standpoint.cid == self.standpoint[1].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapTwos.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapTwos.push(data[i].name);
    								}

    							}else if(data[i].imptLevel.cid == self.imptLevel[2].cid
    	    							  && data[i].standpoint.cid == self.standpoint[2].cid){
    								if(!!data[i].dept && data[i].dept != ''){
    									self.ninePalaceMapOnes.push(data[i].dept + "-" + data[i].name);
    								}else{
    									self.ninePalaceMapOnes.push(data[i].name);
    								}
    							}
    						}
    					}

    					if(self.ninePalaceMapSixs.length > 0){
    						self.fourQuadrantGraphOnes = self.fourQuadrantGraphOnes.concat(self.ninePalaceMapSixs);
    					}
    					if(self.ninePalaceMapEights.length > 0){
    						self.fourQuadrantGraphOnes = self.fourQuadrantGraphOnes.concat(self.ninePalaceMapEights);
    					}
    					if(self.ninePalaceMapNines.length > 0){
    						self.fourQuadrantGraphOnes = self.fourQuadrantGraphOnes.concat(self.ninePalaceMapNines);
    					}

    					if(self.ninePalaceMapFours.length > 0){
    						self.fourQuadrantGraphTwos = self.fourQuadrantGraphTwos.concat(self.ninePalaceMapFours);
    					}
    					if(self.ninePalaceMapFives.length > 0){
    						self.fourQuadrantGraphTwos = self.fourQuadrantGraphTwos.concat(self.ninePalaceMapFives);
    					}
    					if(self.ninePalaceMapSevens.length > 0){
    						self.fourQuadrantGraphTwos = self.fourQuadrantGraphTwos.concat(self.ninePalaceMapSevens);
    					}

    					if(self.ninePalaceMapOnes.length > 0){
    						self.fourQuadrantGraphThrees = self.fourQuadrantGraphThrees.concat(self.ninePalaceMapOnes);
    					}

    					if(self.ninePalaceMapTwos.length > 0){
    						self.fourQuadrantGraphFours = self.fourQuadrantGraphFours.concat(self.ninePalaceMapTwos);
    					}
    					if(self.ninePalaceMapThrees.length > 0){
    						self.fourQuadrantGraphFours = self.fourQuadrantGraphFours.concat(self.ninePalaceMapThrees);
    					}
    				},
					error    	: 	function(data) {
						self.$Modal.error({
	                   		title	:	'错误',
	                       	content	: 	'查询干系人失败，请联系管理员!'
	                  	});
    				}
    			})
    		},
			"deleteMultiPrjInfoFromStakeHolder":function(){
				var _this = this;

				var rows = _this.selectDatas;
				var matterPsnsCids ="";

	            if (rows.length > 0) {
	            	for(var i=0;i<rows.length;i++){
	            		matterPsnsCids=matterPsnsCids+rows[i].cid+",";
	            	}
	            	var allData = {
							prjId:_this.prjId,
							"matterPsnsCids": matterPsnsCids
					};
					$.ajax({

						type	 	:	"post",
						dataType 	:  	"text",
						async	 	:	false,
						url 	 	:  	prjHttpRequest+"/prjInfoCtrl/deleteMultiPrjInfoFromStakeHolder.action",
						data	 	:	allData,
						success	 	: 	function(result){
							_this.loadStakeHolderInfo();
						},
						error	 	: 	function(result){
											_this.$Modal.error({
						                   		title	:	'错误',
						                       	content	: 	'删除干系人失败，请联系管理员!'
						                  	});
										}
					});
	            }
			},
			"updatePrjInfoFromStakeHolder":function(){
				var _this = this;
				var allData =  JSON.parse(JSON.stringify(_this.prjInfo));
				for(var i=0;i<_this.matterPsnType.length;i++){
    				if(_this.matterPsnType[i].defName==_this.selectMatterPsnType){
    					allData.queryType = _this.matterPsnType[i].id;
    				}
    			}
    			for(var i=0;i<_this.matterPsnImptLevel.length;i++){
    				if(_this.matterPsnImptLevel[i].defName==_this.selectMatterPsnImptLevel){
    					allData.queryImptLevel = _this.matterPsnImptLevel[i].id;
    				}
    			}


				for(var i=0;i<_this.matterPsnType.length;i++){
					if(allData.matterPsns.type.name == _this.matterPsnType[i].defName){
						allData.matterPsns.type.cid = _this.matterPsnType[i].id;
					}
				}
				for(var i=0;i<_this.matterPsnImptLevel.length;i++){
					if(allData.matterPsns.imptLevel.name == _this.matterPsnImptLevel[i].defName){
						allData.matterPsns.imptLevel.cid = _this.matterPsnImptLevel[i].id;
					}
				}
				for(var i=0;i<_this.matterPsnJoinLevel.length;i++){
					if(allData.matterPsns.joinLevel.name == _this.matterPsnJoinLevel[i].defName){
						allData.matterPsns.joinLevel.cid = _this.matterPsnJoinLevel[i].id;
					}
				}
				for(var i=0;i<_this.matterPsnsStandpoint.length;i++){
					if(allData.matterPsns.standpoint.name == _this.matterPsnsStandpoint[i].defName){
						allData.matterPsns.standpoint.cid = _this.matterPsnsStandpoint[i].id;
					}
				}

				var prjSthVo = _this.oldEditData;
				var fieldInfo = [];
				for(var key in allData.matterPsns){
					if (key != 'position' && key != 'imptLevel' && key != 'joinLevel' && key != 'standpoint' && key != 'expect' && key != 'duty' && key != 'talkChannel' && key != 'note'){
						if(key != 'type' && key != 'name' && key != 'cid' && key != 'id' && key != 'standpoint'  && key != 'imptLevel'  && key != 'joinLevel'  && key != 'company'){
							if(allData.matterPsns[key] != prjSthVo[key]){
								var param = {
									name:key,
									oldValue:prjSthVo[key] || '',
									newValue:allData.matterPsns[key],
								};
								fieldInfo.push(param)
							}
						}else if(key === 'standpoint' || key === 'imptLevel'  || key === 'joinLevel'){
							if(prjSthVo[key] && allData.matterPsns[key].cid != prjSthVo[key].cid){
								var param = {
									name:key,
									oldValue:prjSthVo[key].name || '',
									newValue:allData.matterPsns[key].name,
								};
								fieldInfo.push(param)
							}else if(!prjSthVo[key] && allData.matterPsns[key].cid){
								var param = {
									name:key,
									oldValue:'',
									newValue:allData.matterPsns[key].name,
								};
								fieldInfo.push(param)
							}
						}else if(key === 'company'){
							var company = allData.matterPsns.company[allData.matterPsns.company.length -1]
							if(!!prjSthVo[key] && (company != prjSthVo[key])){
								var param = {
									name:key,
									oldValue:prjSthVo[key]  || '',
									newValue:company,
								};
								fieldInfo.push(param)
							}
						}
					}
				}

				if(_this.prjInfo.matterPsns.type.name === '亚信侧'){
					allData.matterPsns.emp = prjSthVo.emp;
					allData.matterPsns.name =  allData.matterPsns.name.split('/')[0]
				}
				allData.matterPsns.company = allData.matterPsns.company[allData.matterPsns.company.length -1];
				allData.matterPsns.fieldInfo = fieldInfo;
				allData.matterPsns.prjId = _this.prjId;

				var vo = allData.matterPsns;


				_this.$refs['prjInfoForm'].validate(function (valid) {
					if (valid) {
						$.ajax({
							headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
							type	 	:	"post",
							async	 	:	false,
							url 	 	:  	prjHttpRequest+"/prjInfoCtrl/updatePrjInfoFromStakeHolder.action",
							data	 	:	JSON.stringify(vo),
							success	 	: 	function(result){
								if(result.success){
									_this.$Message.success("修改干系人成功");
									_this.prjInfoModal = false;
									_this.resetFields();
									_this.loadStakeHolderInfo();
								}else{
									_this.$Message.error(result.message);
								}
							},
							error	 	: 	function(result){
												_this.$Modal.error({
							                   		title	:	'错误',
							                       	content	: 	'修改干系人失败，请联系管理员!'
							                  	});
											}
						});
					}else{
						document.querySelector('#prj_info_modal').getElementsByClassName('prj-info-modal')[0].scrollTop = self.$refs.prjInfoFormType.$el.offsetTop
					}
				})

			},
			"createPrjInfoFromStakeHolder":function(){
				var self = this;
				var allData = JSON.parse(JSON.stringify(self.prjInfo));
				for(var i=0;i<self.matterPsnType.length;i++){
					if(allData.matterPsns.type.name == self.matterPsnType[i].defName){
						allData.matterPsns.type.cid = self.matterPsnType[i].id;
					}
				}
				for(var i=0;i<self.matterPsnImptLevel.length;i++){
					if(allData.matterPsns.imptLevel.name == self.matterPsnImptLevel[i].defName){
						allData.matterPsns.imptLevel.cid = self.matterPsnImptLevel[i].id;
					}
				}
				for(var i=0;i<self.matterPsnJoinLevel.length;i++){
					if(allData.matterPsns.joinLevel.name == self.matterPsnJoinLevel[i].defName){
						allData.matterPsns.joinLevel.cid = self.matterPsnJoinLevel[i].id;
					}
				}
				for(var i=0;i<self.matterPsnsStandpoint.length;i++){
					if(allData.matterPsns.standpoint.name == self.matterPsnsStandpoint[i].defName){
						allData.matterPsns.standpoint.cid = self.matterPsnsStandpoint[i].id;
					}
				}

				var prjSthVo = self.oldEditData;
				var fieldInfo = [];
				for(var key in allData.matterPsns){
					if (key != 'position' && key != 'imptLevel' && key != 'joinLevel' && key != 'standpoint' && key != 'expect' && key != 'duty' && key != 'talkChannel' && key != 'note'){
						if(key != 'type' && key != 'name' && key != 'cid' && key != 'id' && key != 'standpoint'  && key != 'imptLevel'  && key != 'joinLevel'  && key != 'company'){
							if(allData.matterPsns[key] != prjSthVo[key]){
								var param = {
									name:key,
									oldValue:prjSthVo[key]  || '',
									newValue:allData.matterPsns[key],
								};
								fieldInfo.push(param)
							}
						}else if(key === 'standpoint' || key === 'imptLevel'  || key === 'joinLevel'){
							if(prjSthVo[key] && allData.matterPsns[key].cid != prjSthVo[key].cid){
								var param = {
									name:key,
									oldValue:prjSthVo[key].name || '',
									newValue:allData.matterPsns[key].name,
								};
								fieldInfo.push(param)
							}else if(!prjSthVo[key] && allData.matterPsns[key].cid){
								var param = {
									name:key,
									oldValue:'',
									newValue:allData.matterPsns[key].name,
								};
								fieldInfo.push(param)
							}
						}else if(key === 'company'){
							var company = allData.matterPsns.company[allData.matterPsns.company.length -1]
							if(!!prjSthVo[key] && (company != prjSthVo[key])){
								var param = {
									name:key,
									oldValue:prjSthVo[key]  || '',
									newValue:company,
								};
								fieldInfo.push(param)
							}
						}
					}
				}

				if(allData.matterPsns.type.name === '亚信侧'){
					allData.matterPsns.emp = {userId:self.nameInfo.id};
					allData.matterPsns.name =  allData.matterPsns.name.split('/')[0]
				}

				allData.matterPsns.company = allData.matterPsns.company[allData.matterPsns.company.length -1];

				allData.matterPsns.fieldInfo = fieldInfo;
				allData.matterPsns.prjId = self.prjId;

				var vo = allData.matterPsns;
				self.$refs['prjInfoForm'].validate(function (valid) {
					if (valid) {
						$.ajax({
							headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
							type	 	:	"post",
							url 	 	:  	prjHttpRequest+"/prjInfoCtrl/createPrjInfoFromStakeHolder.action",
							data	 	:	JSON.stringify(vo),
							success	 	: 	function(res){
								if(res.success){
									self.$Message.success("添加干系人成功");
									self.prjInfoModal = false;
									self.resetFields();
									self.loadStakeHolderInfo();
								}else{
									self.$Message.error(res.message);
								}
							},
							error	 	: 	function(result){
											self.$Modal.error({
							                   		title	:	'错误',
							                       	content	: 	'新增干系人失败，请联系管理员!'
							                  	});
											}
						});
					}else{
						document.querySelector('#prj_info_modal').getElementsByClassName('prj-info-modal')[0].scrollTop = self.$refs.prjInfoFormType.$el.offsetTop
					}
				});

			},
			//加载最多点击次数项目
			"loadClickMaxPrjInfo": function() {
				var self = this;
				return $.ajax({
					url: linkus.location.prj + '/prjInfoCtrl/queryByClickTimeMax.action',
					type: 'post',
					dataType: 'JSON'
				}).done(function(tePrjInfoVo) {
					if(!!tePrjInfoVo && JSON.stringify(tePrjInfoVo) != "{}") {
						self.prjId = tePrjInfoVo.prjId;
						self.prjName = tePrjInfoVo.prjName;
						self.dmpPrjId = tePrjInfoVo.dmpPrjId;
						self.prjCode = tePrjInfoVo.prjCode;
						self.idValue = tePrjInfoVo.idValue;
						self.prjIdValue = tePrjInfoVo.prjIdValue;
						self.mainShowFlag = true;
						if (!!tePrjInfoVo.pmUser && JSON.stringify(tePrjInfoVo.pmUser) != "{}" && !!self.loginUser
							&& JSON.stringify(self.loginUser) != "{}" && tePrjInfoVo.pmUser.userId == self.loginUser.id){
							self.isPmUser = true;
						} else {
							self.isPmUser = false;
						}
						self.checkIsPrjAdmin();
						self.uploadData.prjId = tePrjInfoVo.prjId;
						// 查询干系人
		    			self.loadStakeHolderInfo();
					}
				});
			},

			//获取当前登录人是否是项目管理员
			checkIsPrjAdmin: function() {
				var sf = this;
				sf.isPrjAdmin = false;
				$.ajax({
					type: "get",
					dataType: "json",
					url: linkus.location.prjuser + "/sysDefRoleUserCtrl/checkIsPrjAdmin.action",
					data: {prjId: sf.prjId},
					success: function(result){
						sf.isPrjAdmin = result;
					},
					error: function(text){
						sf.$Modal.error({
							title: '错误',
							content: '查询登陆用户失败!'
						});
					}
				});
			},

			/**
    		 *	***************************************** 查询CAS中当前登录人 *****************************************
    		 */
			'queryLoginUserByCASLoginName' : function() {

    			var _this = this;

    			$.ajax({
					type: "post",
					dataType: "json",
					async: false,
					url: prjUserHttpRequest+"/sysUserCtrl/queryByLoginName.action",
					data: {},
					success: function(result){
						_this.loginUser = mini.decode(result);
						_this.loadClickMaxPrjInfo();
					},
					error: function(text){
						_this.loadClickMaxPrjInfo();
						_this.$Modal.error({
							title: '错误',
					        content: '查询CAS中登陆用户失败，请联系管理员!'
						});
					}
				});
    		},
			/**
    		 *	***************************************** 项目选择执行方法 *****************************************
    		 */
    		"onStop":function(){
    			if(this.loadingAjax.abort){
    				this.loadingAjax.abort();
    			}
    		},
			'onChange' : function(prjName) {

				this.prjName = prjName;
//				this.loadingAjax=this.loadPrjInfoPage(100,1,this.prjName);
				//添加 queryPrjInfoFromStakeHolder 方法

				this.loadingAjax=this.loadStakeHolderInfo(this.prjName);
    		},
    		'onChoose' : function(prjId, prjName, prjInfoId, dmpPrjId, prjCode, idValue, prjIdValue) {

    			this.prjId   = prjId;
    			this.prjName = prjName;
    			this.dmpPrjId = dmpPrjId;
    			this.prjCode = prjCode;
    			this.idValue = idValue;
    			this.prjIdValue = prjIdValue;
    			this.mainShowFlag = true;

    			//新增项目点击记录
    			this.addClickLog();

    			this.queryReportOfPrjId();

    			this.loadStakeHolderInfo();

    		},
    		//选择项目组件	失焦事件
    		"onBlur":function(prjName){
    			this.prjName=prjName;
    			this.loadStakeHolderInfo();

    		},
    		'onClickEmpty' : function() {
    			this.mainShowFlag=false;
    			this.stakeHolderInfoData = [];
    		},
    		'infiniteHandlerUp' : function($state, pageNum) {

    			// this.loadPrjInfo(50, pageNum, this.prjName, "up", $state);
    			this.loadPrjInfoPage(100, pageNum, this.prjName, "up", $state);

    		},
    		'infiniteHandlerDown' : function($state, pageNum) {

    			// this.loadPrjInfo(50, pageNum, this.prjName, "down", $state);
    			this.loadPrjInfoPage(100, pageNum, this.prjName, "down", $state);

    		},
    		//新增项目点击记录
    		"addClickLog":function(){
    			var self=this;
    			var teSysDefClickLog={
    				"defId":self.prjId
    			};
    			return $.ajax({
    				url : linkus.location.prjuser+'/sysDefClickLogCtrl/addClickLog.action',
    				headers : {'Content-Type' : 'application/json;charset=utf-8'},
    				type : 'post',
    				data : JSON.stringify(teSysDefClickLog),
    				dataType : 'JSON'
    			}).done(function(teSysDefClickLog){

    				this.prjId=teSysDefClickLog.defId;
    			});
    		},
    		'loadPrjInfo' : function(pageSize, pageNum, prjName, upOrDown, $state){

    			var self = this;

    			$.ajax({
    				type	 	:	'post',
    				dataType 	:  	'json',
    				async	 	:	false,
    				url  	 	: 	prjHttpRequest+'/prjInfoCtrl/queryByPage.action',
    				data 	 	:	{
				    					'pageSize'	: pageSize,
				    					'pageNum' 	: pageNum,
				    					'prjName' 	: prjName,
				    					'pmJobCode' : self.loginUser.jobCode
				    				},
    				success  	: 	function(prjInfoList) {
				    					var tempList = [];
				    					if(upOrDown == "up") {
				    						tempList = prjInfoList.concat(self.prjInfoList);
				    						self.prjInfoList = tempList.slice(0,499);
				    						$state.loaded();
				    					}else if(upOrDown == "down") {
				    						self.prjInfoList = self.prjInfoList.concat(prjInfoList);
											$state.loaded();
				    					}else{
				    						self.prjInfoList = prjInfoList;
				    					}
				    				},
    				error    	: 	function(data) {
				    					self.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询下拉框项目信息错误，请联系管理员!'
					                  	});
				    				}
    			});
    		},
    		'loadPrjInfoFirstAndList' : function(prjName) {

    			var self = this;

    			$.ajax({
    				type	 	: 	'post',
    				dataType 	:  	'json',
    				async	 	:	false,
    				url  	 	: 	prjHttpRequest+'/prjInfoCtrl/queryFirstAndList.action',
    				data 	 	: 	{
    									'prjName' 	: prjName,
    									'pmJobCode' : self.loginUser.jobCode
    								},
    				success  	: 	function(firstAndLast) {
				    					self.firstAndLast=firstAndLast;
				    					self.loadPrjInfo(100, 1, self.prjName);
				    				},
    				error    	: 	function(data) {
				    					self.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询下拉框项目信息错误，请联系管理员!'
					                  	});
				    				}
    			});
    		},
    		"loadPrjInfoPage":function(pageSize,pageNum,prjName,upOrDown,$state){
    			var self=this;
    			return $.ajax({
    				url:linkus.location.prj+'/prjInfoCtrl/queryByPrjInfoPage.action',
    				headers:{'Content-Type' : 'application/json;charset=utf-8'},
    				type:'get',
    				dataType:'JSON',
    				data : {
    					"pageSize":pageSize,
    					"pageNum":pageNum,
    					"prjName":prjName
    				}
    			}).done(function(map){
    				self.firstAndLast=map.firstAndLast;
    				if(upOrDown=="down"){
    					self.prjInfoList=self.prjInfoList.concat(map.prjList);
    					$state.loaded();
    				}else{
    					self.prjInfoList=map.prjList;
    				}

    			});
    		},
    		/**
    		 *	***************************************** 项目报告执行方法  *****************************************
    		 */
    		'changeDateRange' : function(value) {
    			if ("" != value[0]) {
    				this.mainShowFlag = true;
    				this.startRangeEndDate[0] = new Date(new Date(value[0]).getTime()-8*60*60*1000);
     				this.startRangeEndDate[1] = new Date(new Date(value[1]).getTime()+16*60*60*1000-1000);
    			} else {
    				this.startRangeEndDate = [];
    			}

 				this.queryReportOfPrjId();
 				this.loadStakeHolderInfo();
 			},
			'changeType' : function() {
				this.queryReportOfPrjId();
				this.loadStakeHolderInfo();
			},
    		'queryReportOfPrjId' : function() {

    			var _this = this;

				var prjReportVo = {};

				if (null != _this.prjId && "" != _this.prjId) {
					prjReportVo.prjId = _this.prjId;
				}

				if (_this.startRangeEndDate.length > 0) {
					prjReportVo.startDate = _this.startRangeEndDate[0];
					prjReportVo.endDate	  = _this.startRangeEndDate[1];
				}
				if (!!_this.selectType){
					prjReportVo.typeCodeName = _this.selectType;
				}
				if (!!_this.selectDelay){
					prjReportVo.delay = _this.selectDelay;
				}
				if (!!_this.selectStatus){
					prjReportVo.statusCodeName = _this.selectStatus;
				}
    			$.ajax({
    				type	 	: 	'post',
    				dataType 	:  	'json',
    				async	 	:	false,
    				url  	 	: 	prjHttpRequest+'/prjReportCtrl/queryPrjReport.action',
    				headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
    				data	 	:	JSON.stringify(prjReportVo),
    				success  	: 	function(data) {
				    				},
    				error    	: 	function(data) {
    									_this.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询项目报告错误，请联系管理员!'
					                  	});
				    				}
    			});
    		},
    		'onPrjCodeRenderer' : function(e) {

    			var record = e.record;
    			var linkOperation = "";

    			var prjReportId 	= record.id;
   				var startDate 		= mini.formatDate(new Date(record.startDate),   "yyyy-MM-dd");
    			var endDate   		= mini.formatDate(new Date(record.endDate),     "yyyy-MM-dd");
    			var nextEndDate 	= mini.formatDate(new Date(record.nextEndDate), "yyyy-MM-dd");
    			var prjCode     	= ((""==this.prjId) ? record.prjCode : this.prjCode);
    			var prjName     	= ((""==this.prjId) ? record.prjName : this.prjName);
    			var reportUserName 	= record.reportUser.userName;
    			var statusName 		= record.status.statusName;
    			var reportTime 		= record.reportTime;
    			var prjIdValue 		= ((""==this.prjId) ? record.prjIdValue : this.prjIdValue);

    			linkOperation = '<a href="javascript:checkReportView.gotoReportPage(' +'\''+ prjReportId		+ '\','
    																				  +'\''+ startDate			+ '\','
    																				  +'\''+ endDate			+ '\','
    																				  +'\''+ nextEndDate		+ '\','
    																				  +'\''+ prjCode			+ '\','
    																				  +'\''+ prjName			+ '\','
    																				  +'\''+ reportUserName  	+ '\','
    																				  +'\''+ statusName    		+ '\','
    																				  +'\''+ reportTime    		+ '\','
    																				  +'\''+ prjIdValue			+ '\''
    																				  +')" '
    								+ 'style="font-size:10pt; color:rgb(0,0,238); text-decoration:underline;" >'
									+ "第&nbsp;" + record.no + "&nbsp;期" +
								'</a>';
    			return linkOperation;
    		},
    		'gotoReportPage': function(prjReportId, startDate, endDate, nextEndDate, prjCode, prjNameValue,
    										reportUserNameValue, statusNameValue, reportTime, prjIdValue) {

    			var prjName     	= encodeURI(encodeURI(prjNameValue));
    			var reportUserName 	= encodeURI(encodeURI(reportUserNameValue));
    			var statusName 		= encodeURI(encodeURI(statusNameValue));

    			window.open("previewPrjReport.html?prjReportId="+ prjReportId 		+
										 	"&startDate=" 	 	+ startDate   		+
										 	"&endDate=" 		+ endDate     		+
										 	"&nextEndDate=" 	+ nextEndDate     	+
										 	"&prjCode=" 		+ prjCode 			+
											"&prjName=" 		+ prjName 			+
											"&reportUserName=" 	+ reportUserName	+
											"&statusName=" 		+ statusName		+
											"&reportTime="	 	+ reportTime  		+
											"&prjId="	 		+ prjIdValue
											);
			},
    		'onPrjRenderer' : function(e) {

    			var record  = e.record;
    			var field   = e.field;
    			var result 	= '';

    			if ('prjName'==field) {
    				result = (""==this.prjId) ? record.prjName : this.prjName;
    			}
    			if ('prjCode'==field) {
    				result = (""==this.prjId) ? record.prjCode : this.prjCode;
    			}
    			return result;
    		},
    		'onDateRenderer' : function(e) {

    			var record   = e.record;
    			var field    = e.field;
    			var showDate = '';

    			if ('startDate'==field && !!record.startDate) {
    				showDate = transforTime(record.startDate);
    				/*  showDate = record.startDate;
    				showDate = mini.formatDate(new Date(showDate), "yyyy-MM-dd"); */
    			}
    			if ('endDate'==field && !!record.endDate) {
    				showDate = transforTime(record.endDate);
    				/* showDate = record.endDate;
    				showDate = mini.formatDate(new Date(showDate), "yyyy-MM-dd"); */
    			}
    			return showDate;
    		}
    		/**
    		 *	***************************************** methods{} end. *****************************************
    		 */
		}
	});
	mini.parse();
	function transforTime(Timestr)
	{
		var time=Timestr.split(" ");
		return time[0];
	}
</script>
