<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>配置计划模板</title>
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <script src="../../00scripts/location/location.js"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <script src="../../00scripts/00lib/vue/vue.js"></script>
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/vue/loading/vue-infinite-loading.js"></script>
    <script src="../../00scripts/00lib/vue/loading/vue-sysc-opt.js" type="text/javascript" ></script>
    <script src="../../00scripts/00lib/vue/loading/lks-prj-loading.js"></script>

    <script src="../../00scripts/00lib/iview/iview.min.js"></script>
    <link href="../../00scripts/00lib/iview/styles/iview.css" rel="stylesheet" type="text/css"/>

    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

	<!--     miniui			 -->
    <link href="../../00scripts/00lib/miniui/3.5/themes/default/miniui.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/icons.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/default/medium-mode.css" rel="stylesheet" type="text/css" />
	<link href="../../00scripts/00lib/miniui/3.5/themes/bootstrap/skin.css"rel="stylesheet" >
	<link href="../../00scripts/00lib/miniui/3.5/res/font-awesome/css/font-awesome.min.css" rel="stylesheet" >
    <script src="../../00scripts/00lib/miniui/3.5/miniui.js" type="text/javascript" ></script>

    <link href="../../00scripts/00lib/miniui/3.5/themes/bestway/skin.css" rel="stylesheet" >

    <script src="../../00scripts/user/lks-user-load-fuzzy.js" type="text/javascript"></script>

    <!-- 本页面使用 -->
    <script src="../../00scripts/prj/menu/prjHeader.js" type="text/javascript"></script>
    <link href="../../00scripts/prj/menu/prjHeader.css" rel="stylesheet" type="text/css"/>
	<script src="../../00scripts/prj/prjFixHelp.js" type="text/javascript"></script>
</head>
<style scoped>
	[v-cloak] {
		display: none !important;
	}
</style>
<body style="width:100%;height:100%;">
	<div id="main" class="bg-light-grey" style="height: 100%;min-width:1190px;width:100%;" v-cloak>
	    <prj-header></prj-header>
		<prj-fix-help ref="prjFixHelp"></prj-fix-help>
		<div class="conrainer" style="width:100%;padding:0 5% 8px;height: calc(100% - 60px);">

			<div class="layout-content layout-container" style="height:100%;">

				<div class="pd8 text-right pd-side0">
					<i-Button class="fl-rg mr-lf5" type="error"   icon="android-delete" @click="confirmDeleteTemplate" >删 除</i-Button>
 					<i-Button 					   type="primary" icon="android-create" @click="createTemplate">新 增</i-Button>
				</div>

				<div class="block pd8" style="height: calc(100% - 48px); height: -moz-calc(100% - 48px); height: -webkit-calc(100% - 48px);">

					<div id="templateDataGrid" class="mini-datagrid" style="width: 100%; height: 100%;"
							allowAlternating="true" allowResize="false" multiSelect="true" showPager="false" pageSize="50"
							showHGridLines="false"  showVGridLines="false">
						<div property="columns">
							<div type="checkcolumn" 	  width="30" ></div>
							<div type="indexcolumn" 	  width="30"  align="left" headerAlign="left" >No</div>
							<div field="sbuName" 	  	  width="100" align="left" headerAlign="left" >SBU名称</div>
							<div field="sbuId" 	  	 	  width="100" align="left" headerAlign="left" >SBU编码</div>
							<div field="defName"     	  width="100" align="left" headerAlign="left" renderer="templateView.onNameRenderer">模板名称</div>
							<div field="defDesc"  		  width="200" align="left" headerAlign="left" >模板描述</div>
							<div field="pmsPlanStartDate" width="100" align="left" headerAlign="left" dateFormat="yyyy-MM-dd">项目启动日期</div>
							<div field="addTime" 		  width="140" align="left" headerAlign="left" dateFormat="yyyy-MM-dd HH:mm:ss">创建日期</div>
							<div field="addUser.userName" width="100" align="left" headerAlign="left" >创建人</div>
						</div>
					</div>

				</div>

			</div>

		</div>

		<Modal title="新增计划模板" :loading="templateModalLoading" v-model="templateModal" class-name="vertical-center-modal" width="70%"
					@on-ok="submitPrjPlanTemplate">
			<i-Form ref="templateForm" :model="templateBandForm" :label-width="100">

				<Row>
					<i-Col span="11">
						<Form-Item label="模板名称" prop="">
							<i-Input v-model="templateBandForm.defName"  placeholder=""></i-Input>
						</Form-Item>
					</i-Col>
					<i-Col span="2" style="text-align: center">
						<div class="display-inbl"></div>
					</i-Col>
					<i-Col span="11">
						<Form-Item label="项目启动日期" prop="">
							<Date-Picker type="date" v-model="templateBandForm.pmsPlanStartDate" style="width:100%;" placeholder="请选择项目启动日期" ></Date-Picker>
						</Form-Item>
					</i-Col>
				</Row>
				<Form-Item label="问题描述" prop="">
					<i-Input v-model="templateBandForm.defDesc" rows="2" type="textarea" placeholder=""></i-Input>
				</Form-Item>

			</i-Form>
		</Modal>

	</div>
</body>
</html>
<script type="text/javascript">

	var prjBizHttpRequest 	= linkus.location.prjbiz;
	var prjHttpRequest 		= linkus.location.prj;
	var prjUserHttpRequest 	= linkus.location.prjuser;
	var prjKmHttpRequest 	= linkus.location.km;

	var templateView = new Vue({

		el 		: '#main',
		data 	: {
					/**
					 * 	CAS login user vue property
					 */
					loginUser : {},
					/**
					 * 	Prj plan template vue property
					 */
					haveManagerRights : false,
					/**
					 * 	Template form vue property
					 */
					templateModal 	   	 : false,
					templateModalLoading : true,
					templateBandForm 	 : {
						defName		 : '',
						defDesc		 : '',
						addUser 	 : {},
						sbuId		 : '',
						sbuName		 : '',
						pmsPlanStartDate : ''
					}

		},
		created : function() {

			this.queryLoginUserByCASLoginName();
		},
		mounted : function() {

			var _this = this;
			this.$nextTick(function() {
				_this.sureLoginUserIsBuPrjManage();
			});
		},
		methods : {
			/**
    		 *	***************************************** 查询CAS中当前登录人 *****************************************
    		 */
			'queryLoginUserByCASLoginName' : function() {

    			var _this = this;

    			$.ajax({
					type	 	:	"post",
					dataType 	:  	"json",
					async	 	:	false,
					url 	 	:  	prjUserHttpRequest+"/sysUserCtrl/queryByLoginName.action",
					data	 	:	{},
					success	 	: 	function(result){
										_this.loginUser = mini.decode(result);
									},
					error	 	: 	function(text){
										_this.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询CAS中登陆用户失败，请联系管理员!'
					                  	});
									}
				});
    		},
    		/**
    		 *	***************************************** 项目模板配置执行方法  *****************************************
    		 */
    		'sureLoginUserIsBuPrjManage' : function() {

    			var _this = this;

    			var sysDefRoleUser 						= {};
    			sysDefRoleUser.defType 					= {};
    			sysDefRoleUser.defType.defTypeCodeName 	= null;
    			sysDefRoleUser.role 			 		= {};
    			sysDefRoleUser.role.roleCodeName 		= "buPrjAdmin";
    			sysDefRoleUser.roleUser 		 		= {};
    			sysDefRoleUser.roleUser.userName 		= _this.loginUser.userName;

    			$.ajax({
					type	 	:	"post",
					dataType 	:  	"json",
					async	 	:	false,
					headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
					url 	 	:  	prjUserHttpRequest+"/sysDefRoleUserCtrl/querySysUserDistinctColl.action",
					data	 	:	JSON.stringify(sysDefRoleUser),
					success		: 	function(result){

										_this.respUserList = mini.decode(result);
										if (_this.respUserList.length>0) {
											_this.haveManagerRights = true;
										}
										// 查询模板
										_this.queryPrjPlanConfigTemplate();
									},
					error	 	: 	function(text){
										_this.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'查询当前用户角色错误，请联系管理员!'
					                  	});
									}
				 });
    		},
    		'queryPrjPlanConfigTemplate' : function() {

    			var _this = this;

				grid.setUrl(prjHttpRequest+"/prjPlanTmplCtrl/queryPrjPlanConfigTemplate.action");
				grid.load({
        			'sbuId'	: _this.loginUser.sbuId
    			},function(){
    				var data = grid.getData();
		        },function(){
		        	_this.$Modal.error({
                   		title	:	'错误',
                       	content	: 	'查询项目审批报告错误，请联系管理员!'
                  	});
		        });
    		},
    		'onNameRenderer' : function(e) {

    			var record = e.record;
    			var linkOperation = "";

    			var pmsPlanStartDate = mini.formatDate(record.pmsPlanStartDate, "yyyy-MM-dd");

    			linkOperation = '<a href="javascript:templateView.gotoPrjPlanConifgPage('+'\''+ record.prjIdValue		+ '\','
    																					 +'\''+ record.idValue			+ '\','
																		    			 +'\''+ record.defName			+ '\','
																		    			 +'\''+ record.defDesc			+ '\','
																		    			 +'\''+ pmsPlanStartDate		+ '\','
																		    			 +'\''+ this.loginUser.sbuId	+ '\','
																		    			 +'\''+ this.loginUser.sbuName	+ '\','
	    																				 +'\''+ this.haveManagerRights	+ '\''
	    																				 +')" '
    								+ 'style="font-size:10pt; color:rgb(0,0,238); text-decoration:underline;" >'
									+ record.defName +
								'</a>';
    			return linkOperation;
    		},
    		'gotoPrjPlanConifgPage': function(templateId, templateInfoId, templateName, templateDesc, tempPmsPlanStartDate, userSbuId, userSbuName, haveManagerRights) {
   				window.open("prjPlanConfig.html?templateId=" 		  	 + templateId 							+
						    					"&templateInfoId=" 		 + templateInfoId 						+
						    					"&templateName=" 		 + encodeURI(encodeURI(templateName)) 	+
						    					"&templateDesc=" 		 + encodeURI(encodeURI(templateDesc)) 	+
						    					"&tempPmsPlanStartDate=" + tempPmsPlanStartDate 				+
						    					"&userSbuId=" 	 		 + userSbuId 							+
						    					"&userSbuName=" 	 	 + encodeURI(encodeURI(userSbuName)) 	+
											  	"&haveManagerRights=" 	 + haveManagerRights
											);
			},
    		/**
    		 *	***************************************** 新增删除项目模板配置执行方法  *****************************************
    		 */
    		'confirmDeleteTemplate' : function() {

       	     	var _this = this;

    			var records = grid.getSelecteds();
    			if (null==records || records.length==0) {
    				_this.$Message.info('请选择需要操作的记录!');
    				return;
    			}

       	    	_this.$Modal.confirm({
               		title	:	'确认',
                   	content	: 	'是否确认删除项目计划模板?',
                    onOk	: 	function() {
                    				_this.deleteTemplate(records);
                    			}
               	});
        	},
        	'deleteTemplate' : function(records) {

    			var _this = this;

    			var templateIds = "";
    			for(var i=0;i<records.length;i++) {
    				templateIds = (""==templateIds) ? records[i].idValue : (templateIds+","+records[i].idValue);
    			}

    			// 删除模板
    			$.ajax({
    				type	 	: 	'post',
    				dataType 	:  	'text',
    				async	 	:	true,
    				url  	 	: 	prjHttpRequest + "/prjPlanTmplCtrl/deletePrjPlanTemplate.action",
    				data	 	:	{
    									'templateIds' 	: templateIds
				    				},
    				success  	: 	function(data) {
    									_this.queryPrjPlanConfigTemplate();
				    				},
    				error    	: 	function(data) {
    									_this.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'删除项目计划模板错误，请联系管理员!'
					                  	});
				    				}
    			});
    		},
    		'createTemplate' : function() {

				this.templateBandForm.defName = '';
				this.templateBandForm.defDesc = '';
				this.templateBandForm.addUser = {};
				this.templateBandForm.sbuId   = '';
				this.templateBandForm.sbuName = '';
				this.templateBandForm.pmsPlanStartDate = '';
				this.templateModal = true;
    		},
    		'submitPrjPlanTemplate' : function() {

    			var _this = this;

    			_this.templateModal = false;

    			_this.templateBandForm.sbuId 				= _this.loginUser.sbuId;
    			_this.templateBandForm.sbuName 				= _this.loginUser.sbuName;
    			_this.templateBandForm.addUser.userId 		= _this.loginUser.id;
    			_this.templateBandForm.addUser.loginName 	= _this.loginUser.loginName;
    			_this.templateBandForm.addUser.userName 	= _this.loginUser.userName;
    			_this.templateBandForm.addUser.jobCode 		= _this.loginUser.jobCode;

    			$.ajax({
					type	 	:	"post",
					dataType 	:  	"json",
					async	 	:	false,
					headers  	:  	{'Content-Type' : 'application/json;charset=utf-8'},
					url 	 	:  	prjHttpRequest + "/prjPlanTmplCtrl/createPrjPlanTemplate.action",
					data	 	:	JSON.stringify(_this.templateBandForm),
					success	 	: 	function(result){
										_this.queryPrjPlanConfigTemplate();
										_this.$Modal.success({
					                   		title	:	'成功',
					                       	content	: 	'新增计划模板成功!'
				                  		});
									},
					error	 	: 	function (jqXHR, textStatus, errorThrown) {
										_this.$Modal.error({
					                   		title	:	'错误',
					                       	content	: 	'新增计划模板错误，请联系管理员!'
					                  	});
									}
				});

    		}
    		/**
    		 *	***************************************** methods{} end. *****************************************
    		 */
		}
	});
	mini.parse();
	var grid  = mini.get('templateDataGrid');
</script>
