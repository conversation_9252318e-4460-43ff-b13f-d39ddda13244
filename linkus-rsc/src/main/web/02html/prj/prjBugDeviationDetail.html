<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>项目集详情</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <!-- 本地样式 -->
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/prjStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../../01css/reset.css" rel="stylesheet" type="text/css"/>

    <!-- jQuery -->
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>

    <!-- VUE-->
    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <!-- iview -->
    <script src="../../00scripts/00lib/iview/4.6.1/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/4.6.1/styles/iview.css"/>

    <!-- iconfont字体图标 -->
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <!-- 本页面使用 -->
    <script src="../../00scripts/prj/menu/prjHeader.js" type="text/javascript"></script>
    <script src="../../00scripts/prj/prjFixHelp.js" type="text/javascript"></script>

    <script type="text/javascript" src="../../common/filterTable.js"></script>
    <script type="text/javascript" src="../../common/defConst.js"></script>
    <link href="../../00scripts/prj/menu/prjHeader.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css-Prj/style.css" rel="stylesheet" type="text/css"/>

    <style>
        [v-cloak] {
            display: none;
        }

        .ivu-tooltip-inner {
            white-space: normal;
        }

        .ivu-tooltip-rel {
            display: block;
        }

        /*强制表格文本一行超出后打点表示 */
        .lineClamp1 .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        /*强制表格文本两行超出后打点表示 */
        .lineClamp2 .ivu-tooltip-rel {
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
        }
    </style>
</head>
<body>
    <div class="prj_bug_warp header_fix" id="main" v-cloak>
        <prj-header></prj-header>
        <div class="prj_bug_con" ref="scrollview">
            <div class="prj_bug_left">
                <div class="prj_b_l_bot">
                    <div class="b_base_info">
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                项目集信息
                            </div>
                            <div class="prj_b_b_filter col_4">
                                <div class="fol_item">
                                    <label>项目集编码</label>
                                    <div class="span_ellipsis">
                                        <span>{{ prjBasicInfoCode }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>项目集名称</label>
                                    <div class="span_ellipsis">
                                        <span>{{ prjBasicInfoName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>归属省份</label>
                                    <div>
                                        <span>{{ prjBasicInfoProvinceString }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>项目集分类</label>
                                    <div>
                                        <span>{{ prjBasicInfoLevelName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item">
                                    <label>净销售额(K)</label>
                                    <div>
                                        <span>{{ netSaleAmt }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>项目经理</label>
                                    <div>
                                        <span>{{ pmUserUserNameAndLoginName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>项目集状态</label>
                                    <div>
                                        <span>{{ prjBasicInfoStatusName }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="new_table_warp">
                                <i-table :border="true" @on-cell-click="thisSubPrjCellData" class="new_table no_hidden lineClamp1" stripe :columns="subPrjColumns" :data="subPrjDatas" border></i-table>
                            </div>

                        </div>

                    </div>
                </div>
                <div class="prj_dvt_b_l_bot">
                    <div class="b_base_info">
                        <div class="prj_dvt_top">
                            <div class="prj_dvt_top_cond">
                                <label>管理月份</label>
                                <div class="prj_dvt_top_cond_month">
                                    <Date-Picker @on-change="changeMgtMonth" clearable type="month" style="width: 200px" v-model="selectMgtMonth"
                                                 placeholder="请选择管理月份" placement="bottom-end"></Date-Picker>
                                </div>
                            </div>
                            <div class="tab_blank">
                                <span :class="{active:currentName === 'sign'}" @click="tabChange('sign')">
                                    签约进度

                                </span>
                                <span :class="{active:currentName === 'overall'}" @click="tabChange('overall')">
                                    总体进度
                                </span>
                                <span :class="{active:currentName === 'milestone'}" @click="tabChange('milestone')">
                                    里程碑进度
                                </span>
                                <span :class="{active:currentName === 'baselinePlan'}" @click="tabChange('baselinePlan')">
                                    <Badge :count="baselinePlanCount">
                                        <span>基准VS实施计划</span>
                                    </Badge>
                                </span>
                                <span :class="{active:currentName === 'totalFee'}" @click="tabChange('totalFee')">
                                    累计成本
                                </span>
                                <span :class="{active:currentName === 'effect'}" @click="tabChange('effect')">
                                    效能
                                </span>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                签约进度
                                <Icon type="ios-arrow-down" />
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="signColumns" :data="signDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                总体进度
                                <Icon type="ios-arrow-down" />
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="overallColumns" :data="overallDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                里程碑进度
                                <Icon type="ios-arrow-down" />
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="milestoneColumns" :data="milestoneDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                基准VS实施计划
                                <Icon type="ios-arrow-down" />
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="baselinePlanColumns" :data="baselinePlanDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                偏差反馈&分析
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>偏差原因分析</label>
                                    <div class="span_ellipsis" :title="dvtDescAnalysisOne">
                                        <span>{{ dvtDescAnalysisOne }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="dvtDescAnalysisOne" type="textarea" placeholder="请输入偏差原因分析" :rows="8"></i-input>
                                    </div>-->
                                </div>

                                <div class="fol_item pdL20">
                                    <label>整改措施</label>
                                    <div class="span_ellipsis" :title="rectificationDescOne">
                                        <span>{{ rectificationDescOne }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="rectificationDescOne" type="textarea" placeholder="请输入整改措施" :rows="2"></i-input>
                                    </div>-->
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>整改完成日期</label>
                                    <div>
                                        <span>{{ rectificationClosedDateOne }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="rectificationClosedDateOne" type="textarea" placeholder="请输入偏差原因分析" :rows="8"></i-input>
                                    </div>-->
                                </div>

                                <div class="fol_item pdL20">
                                    <label>首版计划日期</label>
                                    <div>
                                        <span>{{ firstVerPlanDateOne }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="firstVerPlanDateOne" type="textarea" placeholder="请输入整改措施" :rows="2"></i-input>
                                    </div>-->
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_4">
                                <div class="fol_item">
                                    <label>原因大类</label>
                                    <div>
                                        <span>{{ causeTypeOneName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>病因</label>
                                    <div>
                                        <span>{{ causeSubTypeOneName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>药方</label>
                                    <div>
                                        <span>{{ causeSub2TypeOneName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>状态</label>
                                    <div>
                                        <span>{{ statusOneName }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>原因分析</label>
                                    <div class="span_ellipsis" :title="causeDescOne">
                                        <span>{{ causeDescOne }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>整改措施-评审</label>
                                    <div class="span_ellipsis" :title="rectificationReviewOne">
                                        <span>{{ rectificationReviewOne }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                累计成本
                                <Icon type="ios-arrow-down"></Icon>
                                <div class="costStatistic" @click="openCostStatisticModal">
                                    <span class="iconfont icon-statistic"></span>
                                    <span>成本统计</span>
                                </div>
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="totalCostColumns" :data="totalCostDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                偏差反馈&分析
                            </div>
                            <div class="prj_b_b_filter col_4">
                                <div class="fol_item">
                                    <label>原因大类</label>
                                    <div>
                                        <span>{{ causeTypeTwoName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>病因</label>
                                    <div>
                                        <span>{{ causeSubTypeTwoName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>药方</label>
                                    <div>
                                        <span>{{ causeSub2TypeTwoName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>状态</label>
                                    <div>
                                        <span>{{ statusTwoName }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>原因分析</label>
                                    <div class="span_ellipsis" :title="causeDescTwo">
                                        <span>{{ causeDescTwo }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>整改措施-评审</label>
                                    <div class="span_ellipsis" :title="rectificationReviewTwo">
                                        <span>{{ rectificationReviewTwo }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>管理动作</label>
                                    <div>
                                        <span>{{ mgtAction }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>是否考核</label>
                                    <div>
                                        <span>{{ needCheck }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                效能
                                <Icon type="ios-arrow-down"></Icon>
                            </div>
                            <div class="new_table_warp">
                                <i-table :border="true" class="new_table no_hidden lineClamp1" stripe :columns="effectColumns" :data="effectCostDatas" border></i-table>
                            </div>
                        </div>
                        <div class="notional_pooling">
                            <div class="prj_b_title">
                                偏差反馈&分析
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>偏差原因分析</label>
                                    <div class="span_ellipsis" :title="dvtDescAnalysisThree">
                                        <span>{{ dvtDescAnalysisThree }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="dvtDescAnalysisThree" type="textarea" placeholder="请输入偏差原因分析" :rows="8"></i-input>
                                    </div>-->
                                </div>

                                <div class="fol_item pdL20">
                                    <label>整改措施</label>
                                    <div class="span_ellipsis" :title="rectificationDescThree">
                                        <span>{{ rectificationDescThree }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="rectificationDescThree" type="textarea" placeholder="请输入整改措施" :rows="2"></i-input>
                                    </div>-->
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>整改完成日期</label>
                                    <div>
                                        <span>{{ rectificationClosedDateThree }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="rectificationClosedDateThree" type="textarea" placeholder="请输入偏差原因分析" :rows="8"></i-input>
                                    </div>-->
                                </div>

                                <div class="fol_item pdL20">
                                    <label>首版计划日期</label>
                                    <div>
                                        <span>{{ firstVerPlanDateThree }}</span>
                                    </div>
                                    <!--<div v-if="!!isBuOmAdmin">
                                        <i-input v-model="firstVerPlanDateThree" type="textarea" placeholder="请输入整改措施" :rows="2"></i-input>
                                    </div>-->
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_4">
                                <div class="fol_item">
                                    <label>原因大类</label>
                                    <div>
                                        <span>{{ causeTypeThreeName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>病因</label>
                                    <div>
                                        <span>{{ causeSubTypeThreeName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>药方</label>
                                    <div>
                                        <span>{{ causeSub2TypeThreeName }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>状态</label>
                                    <div>
                                        <span>{{ statusThreeName }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="prj_b_b_filter col_2">
                                <div class="fol_item">
                                    <label>原因分析</label>
                                    <div class="span_ellipsis" :title="causeDescThree">
                                        <span>{{ causeDescThree }}</span>
                                    </div>
                                </div>

                                <div class="fol_item pdL20">
                                    <label>整改措施-评审</label>
                                    <div class="span_ellipsis" :title="rectificationReviewThree">
                                        <span>{{ rectificationReviewThree }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

<script>
    Date.prototype.format = function (fmt) {
        var o = {
            "M+": this.getMonth() + 1,                 //月份
            "d+": this.getDate(),                    //日
            "h+": this.getHours(),                   //小时
            "m+": this.getMinutes(),                 //分
            "s+": this.getSeconds(),                 //秒
            "q+": Math.floor((this.getMonth() + 3) / 3), //季度
            "S": this.getMilliseconds()             //毫秒
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    };

    //控制登录失效后HTML页面跳转登录页
    verifyLogin();

    var vue = new Vue({
        el: '#main',
        data: function() {
            var sf = this;

            return {

                dvtDescAnalysisThree: "1、热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作，热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作",
                rectificationDescThree: "热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作，热线销售项目对接的客户负责多个业务板块，其中主动流量平台",
                rectificationClosedDateThree: "2025-07-30",
                firstVerPlanDateThree: "2025-06-30",
                causeTypeThreeName: "项目管理问题",
                causeSubTypeThreeName: "范围管理问题",
                causeSub2TypeThreeName: "加强范围管理及人力管控",
                statusThreeName: "未改进",
                causeDescThree: "增加主动流量平台需求及运维工作(原东方国信负责)，增加人力投入",
                rectificationReviewThree: "增加的内容向用户争取订单补偿，正在争取中",

                effectCostDatas: [],
                effectColumns: [
                    {
                        title: '类型',
                        key: 'type',
                        width: 110,
                    },
                    {
                        title: '总成本(K)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '人工费',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '技术分包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '直接费用',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '差旅费',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '餐费',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '其它费',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '人力资源(人月)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '预测收入(K)',
                        key: 'type',
                        width: 110,
                    },
                    {
                        title: 'GM(%)',
                        key: 'type',
                        width: 110,
                    },
                    {
                        title: '效能(元)',
                        key: 'type',
                        width: 110,
                    },
                ],

                causeTypeTwoName: "项目管理问题",
                causeSubTypeTwoName: "范围管理问题",
                causeSub2TypeTwoName: "加强范围管理及人力管控",
                statusTwoName: "未改进",
                causeDescTwo: "增加主动流量平台需求及运维工作(原东方国信负责)，增加人力投入",
                rectificationReviewTwo: "增加的内容向用户争取订单补偿，正在争取中",
                mgtAction: "提醒",
                needCheck: "否",

                totalCostDatas: [],
                totalCostColumns: [
                    {
                        title: '类型',
                        key: 'type',
                        width: 110,
                    },
                    {
                        title: '累计',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '人力资源(人月)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '差旅费(K)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '餐费(K)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '其它费(K)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '人月均费用',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '差旅费(元)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '餐费(元)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '其它费(元)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                ],

                dvtDescAnalysisOne: "1、热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作，热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作",
                rectificationDescOne: "热线销售项目对接的客户负责多个业务板块，其中主动流量平台(亚信热线销售合同建设范围外的支撑工作，热线销售项目对接的客户负责多个业务板块，其中主动流量平台",
                rectificationClosedDateOne: "2025-07-30",
                firstVerPlanDateOne: "2025-06-30",
                causeTypeOneName: "项目管理问题",
                causeSubTypeOneName: "范围管理问题",
                causeSub2TypeOneName: "加强范围管理及人力管控",
                statusOneName: "未改进",
                causeDescOne: "增加主动流量平台需求及运维工作(原东方国信负责)，增加人力投入",
                rectificationReviewOne: "增加的内容向用户争取订单补偿，正在争取中",

                baselinePlanDatas: [],
                baselinePlanColumns: [
                    {
                        title: '类型',
                        key: 'type',
                        width: 110,
                    },
                    {
                        title: '全周期(人月)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '历史年度(人月)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '本年度(人月)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                    {
                        title: '未来年度(人月)',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '合计',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '正式',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                ],

                overallDatas: [],
                overallColumns: [
                    {
                        type: 'index',
                        title: '序',
                        width: 60
                    },
                    {
                        title: '目标里程碑',
                        key: 'code',
                        width: 110,
                    },
                    {
                        title: '计划完成日期',
                        key: 'code',
                        width: 110,
                    },
                    {
                        title: '实际完成日期',
                        key: 'actEndDate',
                        width: 110,
                    },
                    {
                        title: '延期(月)',
                        key: 'delayMonthNum',
                        width: 100,
                    },
                    {
                        title: '严重程度',
                        key: 'colorSign',
                        width: 110,
                    },
                    {
                        title: '延期说明',
                        key: 'code',
                        //width: 110,
                    },
                    {
                        title: '整改措施',
                        key: 'rectifyAction',
                        //width: 110,
                    },
                    {
                        title: '整改完成日期',
                        key: 'rectifyEndDate',
                        width: 110,
                    },
                    {
                        title: '首版计划日期',
                        key: 'firstVerPlanEndDate',
                        width: 110,
                    },
                    {
                        title: '原因大类',
                        key: 'causeType',
                        width: 110,
                    },
                    {
                        title: '病因',
                        key: 'causeSubType',
                        width: 110,
                    },
                    {
                        title: '药方',
                        key: 'causeSub2Type',
                        width: 110,
                    },
                    {
                        title: '是否考核',
                        key: 'needCheck',
                        width: 90,
                    },
                    {
                        title: '原因分析',
                        key: 'notes',
                        //width: 110,
                    },
                    {
                        title: '整改措施-评审',
                        key: 'rectifyActionReviewed',
                        //width: 110,
                    },
                    {
                        title: '状态',
                        key: 'rectifyStatus',
                        width: 90,
                    },
                ],

                milestoneDatas: [],
                milestoneColumns: [
                    {
                        type: 'index',
                        title: '序',
                        width: 60
                    },
                    {
                        title: '子项目编码',
                        key: 'prjCode',
                        width: 110,
                    },
                    {
                        title: '项目里程碑',
                        key: 'prjMst',
                        width: 110,
                    },
                    {
                        title: '基准版计划完成日期',
                        key: 'prjMstPed',
                        width: 140,
                    },
                    {
                        title: '实际完成时间',
                        key: 'actEndDate',
                        width: 110,
                    },
                    {
                        title: '延期(天)',
                        key: 'delayDays',
                        width: 110,
                    },
                    {
                        title: '挂牌阶段',
                        key: 'colorCardType',
                        width: 110,
                    },
                    {
                        title: '挂牌原因',
                        key: 'listReason',
                        //width: 110,
                    },
                    {
                        title: '计划摘牌时间',
                        key: 'planDelistDate',
                        width: 110,
                    },
                    {
                        title: '首版计划日期',
                        key: 'firstVerPlanEndDate',
                        width: 110,
                    },
                    {
                        title: '改进措施',
                        key: 'code',
                        //width: 110,
                    },
                    {
                        title: '原因大类',
                        key: 'causeType',
                        width: 110,
                    },
                    {
                        title: '原因分析',
                        key: 'notes',
                        //width: 110,
                    },
                ],

                signDatas: [],
                signColumns: [
                    {
                        type: 'index',
                        title: '序',
                        width: 60
                    },
                    {
                        title: '子项目编码',
                        key: 'prjCode',
                        render: function(h, params) {
                            return h('div', params.row.prjCode)
                        }
                    },
                    {
                        title: '提前立项日期',
                        key: 'oaPrjStartDate',
                        width: 110,
                    },
                    {
                        title: '签约日期',
                        key: 'signDate',
                        width: 110,
                    },
                    {
                        title: '立项时长(月)',
                        key: 'projectedDays',
                        width: 110,
                    },
                    {
                        title: '严重程度',
                        key: 'colorSign',
                        width: 110,
                    },
                    {
                        title: '未签约原因',
                        key: 'desc',
                        width: 140,
                    },
                    {
                        title: '解決措施',
                        key: 'rectifyAction',
                        width: 140,
                    },
                    {
                        title: '计划解决日期',
                        key: 'rectifyEndDate',
                        width: 120,
                    },
                    {
                        title: '首版计划日期',
                        key: 'firstVerPlanEndDate',
                        width: 120,
                    },
                    {
                        title: '原因大类',
                        key: 'causeType',
                        width: 120,
                    },
                    {
                        title: '原因分析',
                        key: 'notes',
                        width: 120,
                    },
                ],

                currentUser: {},
                isBuOmAdmin: false,
                tabHeight: '400px',
                prjBasicInfoCode: "other-BJQ-BJ-1BJ225033A",
                prjBasicInfoName: "北京移动2022年应急保障平台建设项目",
                prjBasicInfoProvinceString: "SVP1/总部区/总部区工程一部/北京",
                prjBasicInfoLevelName: "B",
                netSaleAmt: 3155.11,
                pmUserUserNameAndLoginName: "张三/zhangsan",
                prjBasicInfoStatusName: "已发布基准",

                subPrjRow: {},
                subPrjColumns: [
                    {
                        type: 'index',
                        title: '序',
                        width: 60
                    },
                    {
                        title: '子项目编码/名称',
                        key: 'code',
                        render: function(h, params) {
                            return h('div', params.row.prjCode + '/' + params.row.prjName)
                        }
                    },
                    {
                        title: '预测订单号',
                        key: 'linkedOrderCode',
                        width: 120
                    },
                    {
                        title: '净销售额(K)',
                        key: 'netSaleAmt',
                        width: 100,
                        render:function (h,params){
                            if(params.row.netSaleAmt){
                                return h('div',(params.row.netSaleAmt/1000).toFixed(2))
                            }else{
                                return h('div',0)
                            }
                        }
                    },
                    {
                        title: '预测收入(K)',
                        key: 'netSaleAmt',
                        width:100,
                        render:function (h,params){
                            if(params.row.netSaleAmt){
                                return h('div',(params.row.netSaleAmt/1000).toFixed(2))
                            }else{
                                return h('div',0)
                            }
                        }
                    },
                    {
                        title: '分类',
                        key: 'level',
                        width: 60,
                        render:function (h,params){
                            if(params.row.level){
                                return h('span',(params.row.level.name))
                            }else{
                                return h('span',"")
                            }
                        }
                    },
                    {
                        title: 'POM状态',
                        key: 'linkedOrderCode',
                        width: 100
                    },
                    {
                        title: 'POM预算-全周期',
                        key: 'linkedOrderCode',
                        align: "center",
                        children: [
                            {
                                title: '正式(人月)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '外包(人月)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '直接费用(K)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                            {
                                title: '技术分包(K)',
                                key: 'linkedOrderCode',
                                width: 100
                            },
                        ],
                    },
                ],
                subPrjDatas: [],

                selectMgtMonth: null,
                selectPrjId: "",
                pmUserId: "",
                isPmUser: "",

                currentName: "sign",

                prjInfo: {},

                prjDvtMgtTypeProgessDelay: "64df14dd64586deda49dc811", //"进度延期"PRJ项目偏差管理类型
                prjDvtMgtTypeCostABCG: "64decf1564586deda49b3565", //"成本偏差ABCG"PRJ项目偏差管理类型
                prjDvtMgtTypeCostEF: "64decf4f64586deda49b4897", //"成本偏差EF"PRJ项目偏差管理类型
                prjDvtMgtTypePlan: "64decf6564586deda49b52d0", //"实施计划偏差"PRJ项目偏差管理类型
                prjDvtMgtTypeEffect: "64decf7964586deda49b59d0", //"效能偏差"PRJ项目偏差管理类型
                prjDvtMgtTypeSuperBaseLine: "64decf8c64586deda49b5dd5", //"超基线管理"PRJ项目偏差管理类型
                prjDvtMgtTypeOrderDeviation: "65fbced36658b395915481a6", //"订单偏差"PRJ项目偏差管理类型
                prjDvtMgtTypeSignDelay: "66f1159c995c83dcd3f889c1", //"签约延期"PRJ项目偏差管理类型
                prjDvtMgtTypeColorCard: "67176123995c83dcd3da4217", //"色牌管理"PRJ项目偏差管理类型
                prjDvtMgtTypeLowGm: "67ac4dd4ec5e633697ab22a7", //"低GM管理"PRJ项目偏差管理类型

                signCount: 0,
                overallCount: 0,
                milestoneCount: 0,
                baselinePlanCount: 0,
                totalCostCount: 0,
                effectCount: 0,

            }
        },
        created: function() {
            var sf = this;

            if (!Vue.evtHub) {
                Vue.evtHub = new Vue();
            }
            sf.checkBuOmAdmin();
            sf.tabHeight = document.documentElement.clientHeight - 190 - 34 + 'px';

            sf.selectPrjId = sf.getQueryString("selectPrjId");
            sf.pmUserId = sf.getQueryString("pmUserId");
            sf.selectMgtMonth = sf.getQueryString("selectMgtMonth");

            sf.countPrjDvtMgtSubscript();

            sf.loadCurrentUser();
            sf.getPrjInfo();
        },
        mounted: function() {
            var sf = this;
            sf.getDelaySignDvtMgtDetailData();
            sf.getProgressDelayDvtMgtDetailData();
            sf.getPrjMstDvtMgtDetailData();
        },
        methods: {

            //查询角标展示未反馈/分析的异常偏差数量
            countPrjDvtMgtSubscript: function() {
                var sf = this;
                if(!sf.selectMgtMonth) {
                    sf.$Message.warning({
                        content: "请选择管理月份！",
                        duration: 3
                    });
                    return;
                }
                sf.signCount = 0;
                sf.overallCount = 0;
                sf.milestoneCount = 0;
                sf.baselinePlanCount = 0;
                sf.totalCostCount = 0;
                sf.effectCount = 0;
                $.ajax({
                    url: linkus.location.prj + '/prjDvtMgtCtrl/countPrjDvtMgtSubscript',
                    type: "get",
                    data: {
                        ym: new Date(sf.selectMgtMonth).format("yyyy-MM"),
                        prjId: sf.selectPrjId,
                    },
                    success: function(data) {
                        if(!!data && !!data.success && !!data.data && JSON.stringify(data.data) != "{}") {
                            for(var key in data.data) {
                                if(key == sf.prjDvtMgtTypeSignDelay) {
                                    sf.signCount = data.data[sf.prjDvtMgtTypeSignDelay] || 0;
                                }else if(key == sf.prjDvtMgtTypeProgessDelay) {
                                    sf.overallCount = data.data[sf.prjDvtMgtTypeProgessDelay] || 0;
                                }else if(key == sf.prjDvtMgtTypeColorCard) {
                                    sf.milestoneCount = data.data[sf.prjDvtMgtTypeColorCard] || 0;
                                }else if(key == sf.prjDvtMgtTypePlan) {
                                    sf.baselinePlanCount = data.data[sf.prjDvtMgtTypePlan] || 0;
                                }else if(key == sf.prjDvtMgtTypeCostABCG) {
                                    sf.totalCostCount = data.data[sf.prjDvtMgtTypeCostABCG] || 0;
                                }else if(key == sf.prjDvtMgtTypeCostEF) {
                                    sf.totalCostCount = data.data[sf.prjDvtMgtTypeCostEF] || 0;
                                }else if(key == sf.prjDvtMgtTypeEffect) {
                                    sf.effectCount = data.data[sf.prjDvtMgtTypeEffect] || 0;
                                }
                            }
                        }
                    },
                    error: function(data) {
                        sf.$Message.error({
                            content: "统计角标失败，请联系管理员！",
                            duration: 5
                        });
                    }
                });
            },

            //查询签约进度数据
            getDelaySignDvtMgtDetailData: function() {
                var sf = this;
                if(!sf.selectMgtMonth) {
                    sf.$Message.warning({
                        content: "请选择管理月份！",
                        duration: 3
                    });
                    return;
                }
                sf.signDatas = [];
                $.ajax({
                    url: linkus.location.prj + '/prjDvtMgtCtrl/getDelaySignDvtMgtDetailData',
                    type: "get",
                    data: {
                        ym: new Date(sf.selectMgtMonth).format("yyyy-MM"),
                        prjId: sf.selectPrjId,
                    },
                    success: function(data) {
                        if(!!data && !!data.success && !!data.data) {
                            sf.signDatas = data.data || [];
                        }
                    },
                    error: function(data) {
                        sf.$Message.error({
                            content: "查询签约进度数据失败，请联系管理员！",
                            duration: 5
                        });
                    }
                });
            },

            //查询总体进度数据
            getProgressDelayDvtMgtDetailData: function() {
                var sf = this;
                if(!sf.selectMgtMonth) {
                    sf.$Message.warning({
                        content: "请选择管理月份！",
                        duration: 3
                    });
                    return;
                }
                sf.overallDatas = [];
                $.ajax({
                    url: linkus.location.prj + '/prjDvtMgtCtrl/getProgressDelayDvtMgtDetailData',
                    type: "get",
                    data: {
                        ym: new Date(sf.selectMgtMonth).format("yyyy-MM"),
                        prjId: sf.selectPrjId,
                    },
                    success: function(data) {
                        if(!!data && !!data.success && !!data.data) {
                            sf.overallDatas = data.data || [];
                        }
                    },
                    error: function(data) {
                        sf.$Message.error({
                            content: "查询总体进度数据失败，请联系管理员！",
                            duration: 5
                        });
                    }
                });
            },

            //查询里程碑进度数据
            getPrjMstDvtMgtDetailData: function() {
                var sf = this;
                if(!sf.selectMgtMonth) {
                    sf.$Message.warning({
                        content: "请选择管理月份！",
                        duration: 3
                    });
                    return;
                }
                sf.milestoneDatas = [];
                $.ajax({
                    url: linkus.location.prj + '/prjDvtMgtCtrl/getPrjMstDvtMgtDetailData',
                    type: "get",
                    data: {
                        ym: new Date(sf.selectMgtMonth).format("yyyy-MM"),
                        prjId: sf.selectPrjId,
                    },
                    success: function(data) {
                        if(!!data && !!data.success && !!data.data) {
                            sf.milestoneDatas = data.data || [];
                        }
                    },
                    error: function(data) {
                        sf.$Message.error({
                            content: "查询里程碑进度数据失败，请联系管理员！",
                            duration: 5
                        });
                    }
                });
            },

            //打开成本统计弹窗
            openCostStatisticModal: function() {
                var sf = this;
            },

            //加载项目信息
            getPrjInfo: function() {
                var sf = this;
                sf.prjInfo = {};
                if(!sf.selectPrjId) {
                    return;
                }
                $.ajax({
                    url: linkus.location.prj + '/prjInfoCtrl/queryPrjInfoByPrjdefId.action',
                    dataType:'json',
                    data:{
                        prjdefIdList : sf.selectPrjId
                    },
                    success: function(data) {
                        sf.prjInfo = data[0] || {};
                        sf.addPrjBasicInfo(data[0]);
                    },
                    error: function(text){

                    }
                });
            },

            //项目信息填充
            addPrjBasicInfo: function(data) {
                var sf = this;

                //项目集编码
                if(data.prjCode) {
                    sf.prjBasicInfoCode = data.prjCode;
                }
                //项目集名称
                if(data.prjName) {
                    sf.prjBasicInfoName = data.prjName;
                }
                //项目集分类
                if(data.level) {
                    sf.prjBasicInfoLevelName = data.level.name;
                }
                //归属省份
                if(data.prov && data.bigRegion){
                    sf.prjBasicInfoProvinceString = data.bigRegion.name + "/" + data.prov.name;
                }
                //净销售额
                if(data.netSaleAmt) {
                    sf.netSaleAmt = (data.netSaleAmt / 1000).toFixed(2);
                }else {
                    sf.netSaleAmt = 0;
                }
                //项目经理
                if(data.pmUser && data.pmUser.userName){
                    sf.pmUserUserNameAndLoginName = data.pmUser.userName + '/' + data.pmUser.loginName;
                }
                //项目集状态
                if(data.status) {
                    sf.prjBasicInfoStatusName = data.status.name;
                }
            },

            //请选择管理月份
            changeMgtMonth: function() {
                var sf = this;
                sf.countPrjDvtMgtSubscript();
                sf.getDelaySignDvtMgtDetailData();
                sf.getProgressDelayDvtMgtDetailData();
                sf.getPrjMstDvtMgtDetailData();
            },

            //检验是否是BU运营管理员
            checkBuOmAdmin: function () {
                var sf = this;
                sf.isBuOmAdmin = false;
                $.ajax({
                    url: linkus.location.prj + "/prjCapabilityAssessCtrl/hasAuth.action",
                    success: function (data) {
                        if (!!data && data.length > 0) {
                            sf.isBuOmAdmin = true;
                        }
                    },
                    error: function (data) {
                        sf.$Message.error('校验BU运营管理员错误，请联系管理员', 3);
                    }
                });
            },

            //查询当前登录人
            loadCurrentUser: function () {
                var sf = this;
                sf.isPmUser = false;
                $.ajax({
                    url: linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
                    type: 'post',
                    dataType: 'JSON',
                }).done(function (teSysUser) {
                    sf.currentUser = teSysUser;
                    if(!!sf.pmUserId && !!sf.currentUser && !!sf.currentUser["id"]) {
                        if(sf.currentUser["id"] == sf.pmUserId) {
                            sf.isPmUser = true;
                        }
                    }
                });
            },

            //点击子项目清单表格单元格
            thisSubPrjCellData: function(row, column, data, event){
                var sf = this;
                sf.subPrjRow = row;
            },

            //tab页切换
            tabChange: function(name) {
                var sf = this;
                sf.currentName = name;
            },

            /*获取参数*/
            getQueryString: function(name) {
                // 获取参数
                var url = window.location.search;
                // 正则筛选地址栏
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                // 匹配目标参数
                var result = url.substr(1).match(reg);
                //返回参数值
                return result ? decodeURIComponent(result[2]) : null;
            },

        }
    });
</script>
</html>