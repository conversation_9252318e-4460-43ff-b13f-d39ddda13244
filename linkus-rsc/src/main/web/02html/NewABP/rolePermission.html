<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="referrer" content="no-referrer" />
	<title>角色权限</title>
	<!-- 兼容ie -->
	<script src="../../00scripts/00lib/ie/browser.js"></script>
	<!-- 本地样式 -->
	<link href="../../01css/style.css" rel="stylesheet" type="text/css" />
	<!-- jQuery -->
	<script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
	<script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
	<script src="../../00scripts/00lib/jquery-qrcode/jquery.qrcode.min.js"></script>
	<!-- vue -->
	<script src="../../00scripts/00lib/vue/vue.min.js"></script>	
	<!-- iview -->
	<script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
	<link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/4.4.0/styles/iview.css" />

	<!-- 本地不知道啥 -->
	<link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css" />
	<script src="../../01css/font/iconfont.js"></script>	

	<!-- 本地路由 -->
	<script src="../../00scripts/location/location.js" type="text/javascript"></script>
	<script src="../../00scripts/location/verifyLogin.js"></script>
	
	<link href="../../00scripts/newABP/menu/rolePermissionHeader.css" rel="stylesheet" type="text/css" />
    <style>
        [v-cloak] {
		  	display: none;
		}
        #main .help_left{
            box-sizing: border-box;
            width: 25%;
            display: inline-block;
            vertical-align: top;
            overflow-y: auto;            
            height: calc(100% - 120px);
        }
        #main .help_right{
            margin-left: 8px;
            width: calc(75% - 12px);
            display: inline-block;
            vertical-align: top;
            height: calc(100% - 120px);
        }       
        .role-index > div{
            padding: 4px 6px 0px;
        }                	
		.ivu-icon-close-circled:before {
		    content: "\f177";
		}
		.ivu-modal {		   
		    top: 20px;
		}				
		.tree_n_c{		   
		    height: calc(100% - 40px);
		    overflow-y: auto;
		    overflow-x: hidden;
		    width: 100%;
		    position: relative;
		    margin-bottom: 20px;
		}

		.tree_n_c.scroll {
			/*overflow: auto;*/
			background: linear-gradient(to bottom, #fff1dc 2%, #fffbf5 6%, #fff 10%);
		}
		
		.tree_n_c{
		    height: calc(100% - 160px);
		}

		.ivu-tree-title {
			vertical-align: middle;
		}

		.role-index {
		    height: 100%;
		}		
		.ivu-breadcrumb>span:last-child {
		    color: #3883e5;
		    font-weight: normal;
		}		
		.help_left .role_name_div .role_name_span {
		    font-weight: 700;
		    font-size: 20px;
		    color: #262626;
		    line-height: 22px;
		    height: 28px;
		}
		.help_left .role_name_div {		    
		    padding: 16px 16px 0 16px;
		    overflow: hidden;
		    white-space: nowrap;
		    text-overflow: ellipsis;
		    width: 300px;
		}
		.help_left .ctlg_tree_class {
		    padding: 8px 12px
		}

		.ivu-checkbox-wrapper {
		    font-size: 12px !important;
		    color: #555555;	    
		}
		.ivu-table {
		    font-size: 12px !important;
		    color: #555555;
		}
		.ivu-table th {
		    font-weight: 700;
            color: #262626;
		}
		.prj_choose_class .ivu-input {
		    font-size: 12px;		    
		}
		
		.ivu-icon-ios-search-strong:before {
		    content: "\f2a7";
		}
		
		.ivu-select-item {		    
		    font-size: 12px !important;		    
		}
				
		.ivu-tooltip-inner {    
		    white-space: normal;
		}
		
		.top {
		    height: 36px;
		    line-height: 36px;
		    display: flex;
		}
		.top > div:first-child {
		    width: calc(100% - 24px);
		}
		.top .ivu-breadcrumb-item-link .tab_text {
		    max-width: 100px;
		    overflow: hidden;
		    text-overflow: ellipsis;
		    white-space: nowrap;
		    display: inline-block;
		    vertical-align: top;
		}
		.top .ivu-breadcrumb-item-link .ivu-tooltip-inner {
		    white-space: normal;
		}	
		
		.role-wrap-class {
			padding-top: 12px;
		    padding-bottom: 12px;
		    padding-left: 16px;
		}
		
		.role-wrap-class .role-div-class {
			display: inline-flex;
			cursor: pointer;
		}
		
		.role-wrap-class .role-text-class {
			margin-right: 16px;
		}
		
		.role-wrap-class .role-div-class .role-item-class {
			margin-right: 32px;
			padding: 0px 6px;			
		}	
		
		.role-wrap-class .role-div-class .role-item-class.selectedRoleClass {
			background-color: #F59A23;
		    border-radius: 12px;
		    color: #fff;		    
		}
		
		.searchWrapClass {
			margin-bottom: 8px;
		}
		
		.searchWrapClass .searchDivClass .searchInputClass {
			width: 150px;
		}
		
		.searchWrapClass .searchDivClass .searchInputClass .ivu-input {
			border-radius: 12px;
   		    border-color: #4DFBFB;
		}
		
		.searchWrapClass .searchDivClass .searchInputClass .ivu-input:hover {
			border-color: #4DFBFB !important;
		}
		
		.searchWrapClass .searchDivClass .searchInputClass .ivu-input:focus {
			border-color: #4DFBFB !important;
		}
		
		.searchWrapClass .searchDivClass .searchInputClass .ivu-input-prefix i {
		    font-size: 24px;  
		    color: #ff9900;
		}
		
		.searchWrapClass .searchDivClass .importBtnClass {
			border-radius: 12px;
		    background-color: #57a3f3;
		    color: #fff;
		}	
		
		.searchWrapClass .searchDivClass {
			text-align: right;		
		}	
		
		.role_choose_class {
			display: block;
		    margin-bottom: 8px;
		    width: 100%;
		    margin-top: 8px;		
		}

		.tree_warp:hover .tree_title{
			background-color: #d5e8fc;
		}
		.tree_warp:hover .btnNone{
			display: inline-block
		}
		.tree_warp .btnNone span:hover {
			color:#275cd4
		}
		.ivu-tree ul li {
			list-style: none;
			font-size: 14px;
			padding: 0;
			margin: 0;
			white-space: nowrap;
			outline: none;
		}
		
		.help_right .top .icon-ashbin {
			color: #E98E34;
    		font-size: 24px;
		}

		.icon-minusSign {
			width: 24px;
			height: 24px;
			background: #e65d4e;
			border-radius: 50%;
			color: #ffffff;
			position: absolute;
			top: -12px;
			right: -32px;
			text-align: center;
			font-weight: bold;
			cursor: pointer;
		}

		.prjGuide-content  .top >  span.active .icon-ashbin {
			/*默认改为灰色*/
			color: #c8cddc;
		}

		.help_right .top .icon-ashbin {
			color: #E98E34;
			font-size: 24px;
		}

		/*添加溢出内容滚动条*/
		.pd8.help_right.ivu-card {
			overflow-y: auto;
		}
		.pd8.help_right.ivu-card::-webkit-scrollbar {
			width: 10px;
			height: 10px;
		}
		.pd8.help_right.ivu-card::-webkit-scrollbar-thumb {
			background: #bfbfbf;
			border-radius: 10px;
		}
		.pd8.help_right.ivu-card::-webkit-scrollbar-track {
			background: #efefef;
			border-radius: 2px;
		}
		.pd8.help_right.ivu-card::-webkit-scrollbar-thumb:hover {
			background: #979797;
		}
		.pd8.help_right.ivu-card::-webkit-scrollbar-corner {
			background: #179a16;
		}

		.title{
			color: #c8cddc;
			margin-top: 10px;
			margin-left: 12px;
			display: flex;
			align-items: center;
		}

	</style>
</head>
<body class="bodyStyle" style="font-size: 12px !important">
<div style="height: 100%;width: 100%" id="main">	
	<div class="main bg-light-grey conrainer" style="height: 100%; min-width: 1190px; width: 100%; overflow: hidden" v-cloak>
		<div class="role-index">
			<div style="height: 100%;">				
				<div class="role_choose_class">
					<div class="ivu-card role-wrap-class">
						<span class="role-text-class">角色选择</span>
						<div class="role-div-class">							
							<div @click="selectRole('organAdmin')" :class="[selectedRole == 'organAdmin' ? 'role-item-class selectedRoleClass' : 'role-item-class']">
								<span>组织管理员</span>
							</div>
							<div @click="selectRole('prdLineAdmin')" :class="[selectedRole == 'prdLineAdmin' ? 'role-item-class selectedRoleClass' : 'role-item-class']">
								<span>产品线管理员</span>
							</div>
							<div @click="selectRole('resourceDepAdmin')" :class="[selectedRole == 'resourceDepAdmin' ? 'role-item-class selectedRoleClass' : 'role-item-class']">
								<span>资源部门管理员</span>
							</div>
							<div @click="selectRole('planAdmin')" :class="[selectedRole == 'planAdmin' ? 'role-item-class selectedRoleClass' : 'role-item-class']">
								<span>计划编制人</span>
							</div>
						</div>
					</div>															
				</div>	
				<div class="searchWrapClass">
					<div class="searchDivClass">
						<i-Button class="importBtnClass" @click="openImportModal" v-if = editPower>权限导入</i-Button>
						<i-Button class="importBtnClass" @click="jurExport" v-if = editPower>权限导出</i-Button>
					</div>
				</div>			
				<div class="block help_left ivu-card tree_n_c scroll">
					<Tooltip :content="roleName" placement="bottom">
						<div class="role_name_div"><span class="role_name_span">{{ roleName }}</span></div>
					</Tooltip>
<!--					<knowledge-tree ref="knowTree" :loading="treeLoading" :prj-id="choosedPrjId" :data-info="treeData" :is-show-edit="isShowEdit"></knowledge-tree>-->
					<Tree :data="treeData" :render="renderContent"></Tree>
				</div>
				<div class="pd8 help_right ivu-card" style="background: #ffffff;padding-left: 16px !important;
                     padding-right: 16px !important;">
					<div class="prjGuide-content">
						<div class="top">
							<div>
								<Breadcrumb separator="<i class='iconfont icon-rightArrow'></i>">
									<Breadcrumb-Item v-for="(item,index) in tabList">
										<Tooltip :content="item.name" placement="bottom-start">
											<div class="tab_text" @click="tabToList(index)">{{item.name}}</div>
										</Tooltip>
									</Breadcrumb-Item>
								</Breadcrumb>
							</div>
							<span @click="deleteAble = !deleteAble" :class="{active:deleteAble}"><i class="iconfont icon-ashbin"  v-show = 'addAdminFlag || addMemberFlag'></i></span>
<!--							<span class="iconfont icon-ashbin" @click="deleteUser"></span>-->
						</div>
						<div class="title" v-show = "adminList.length > 0 && selectedRole != 'planAdmin' ">
							<span>管理员</span>
						</div>
						<div class="flex_warp">
			               <div :class="[adminList.length > 0 ? 'masterSet masterSetLeft1' : 'masterSet masterSetLeft2']" v-show = 'addAdminFlag'>
			                    <Poptip placement="bottom" class="add_info" width="240" @on-popper-hide="clearUserList"  v-model="tooltipShow">
			                        <div class="add_warp" @click="isMemberAdd = true">
			                            <div class="add_icon">
			                                <i class="iconfont icon-jia"></i>
			                            </div>
			                            <span>点击添加管理员</span>
			                        </div>
			                        <div class="user_list" slot="content">
			                            <div v-show="isMemberAdd">
			                                <i-input @on-change="searchMember" v-model="MemberKey" placeholder="请输入NT账号或姓名查询"></i-input>
			                            </div>
			                            <ul>
			                                <li v-for="(item,index) in MemberData" :title="item.ccIdAndccName" v-html="item.valueName" @click="addUser(item)"></li>
			                            </ul>
			                        </div>
			                    </Poptip>
			                </div>

<!--							<div :class="[masterUsers.length > 0 ? 'masterSet masterSetLeft1' : 'masterSet masterSetLeft2']" v-show = 'powerFlag'>-->
<!--								<Poptip placement="bottom" class="add_info" width="240" @on-popper-hide="clearUserList"  v-model="tooltipShowNormal">-->
<!--									<div class="add_warp" @click="isMemberAdd = true">-->
<!--										<div class="add_icon">-->
<!--											<i class="iconfont icon-jia"></i>-->
<!--										</div>-->
<!--										<span>点击添加普通成员</span>-->
<!--									</div>-->
<!--									<div class="user_list" slot="content">-->
<!--										<div v-show="isMemberAdd">-->
<!--											<i-input @on-change="searchMember" v-model="MemberKey" placeholder="请输入NT账号或姓名查询"></i-input>-->
<!--										</div>-->
<!--										<ul>-->
<!--											<li v-for="(item,index) in MemberData" :title="item.ccIdAndccName" v-html="item.valueName" @click="addNormalUser(item)"></li>-->
<!--										</ul>-->
<!--									</div>-->
<!--								</Poptip>-->
<!--							</div>-->
			               
			                <div class="masterBoard" v-for="(masterUser, index) in adminList">
			                    <div class="masterUserImg">
			                        <img :src="masterUser.headPicUrl" height = "100%" width = "100%">
			                    </div>
			                    <div class="masterUser">
			                        <div class="masterUserName">
			                            <span>{{ !!masterUser.loginName ? masterUser.loginName : '' }}</span>
			                        </div>
			                        <div class="masterLoginName">
			                            <span>{{ !!masterUser.userName ?  masterUser.userName : '' }}</span>
			                        </div>
			                    </div>
			                    <div class="masterUserDel" @click="delRoleUser('master', index, 'admin')" v-if = "deleteAble">
<!--			                        <Icon type="ios-close" size="24"/>-->
									<i class="iconfont icon-minusSign" ></i>
			                    </div>
			                </div>
			                
			            </div>

						<div class="title" v-show = "selectedRole != 'planAdmin' && (adminList.length > 0 || memberList.length > 0) ">
							<span>普通成员</span>
						</div>
						<div class="flex_warp" v-show = "selectedRole != 'planAdmin'">
							<div :class="[memberList.length > 0 ? 'masterSet masterSetLeft1' : 'masterSet masterSetLeft2']" v-show = 'addMemberFlag'>
								<Poptip placement="bottom" class="add_info" width="240" @on-popper-hide="clearUserList"  v-model="tooltipShowNormal">
									<div class="add_warp" @click="isMemberAdd = true">
										<div class="add_icon">
											<i class="iconfont icon-jia"></i>
										</div>
										<span>点击添加普通成员</span>
									</div>
									<div class="user_list" slot="content">
										<div v-show="isMemberAdd">
											<i-input @on-change="searchMember" v-model="MemberKey" placeholder="请输入NT账号或姓名查询"></i-input>
										</div>
										<ul>
											<li v-for="(item,index) in MemberData" :title="item.ccIdAndccName" v-html="item.valueName" @click="addNormalUser(item)"></li>
										</ul>
									</div>
								</Poptip>
							</div>
							<div class="masterBoard" v-for="(masterUser, index) in memberList">
								<div class="masterUserImg">
									<img :src="masterUser.headPicUrl" height = "100%" width = "100%">
								</div>
								<div class="masterUser">
									<div class="masterUserName">
										<span>{{ !!masterUser.loginName ? masterUser.loginName : '' }}</span>
									</div>
									<div class="masterLoginName">
										<span>{{ !!masterUser.userName ?  masterUser.userName : '' }}</span>
									</div>
								</div>
								<div class="masterUserDel" @click="delRoleUser('master', index, 'member')" v-if = "deleteAble">
									<!--			                        <Icon type="ios-close" size="24"/>-->
									<i class="iconfont icon-minusSign" ></i>
								</div>
							</div>
						</div>
										    
					</div>					
				</div>
			</div>
		</div>
		<!-- 导入权限 -->
		<Modal v-model="importModal" title="导入权限" width="25%" :styles="{top: '160px'}" @on-cancel="importModal = false">
			<Upload type="drag" :on-success="importSuccess" :on-error="importFail" :before-upload="beforeImport"
					:show-upload-list="false" :action="importGroupUserUrl">
				<div style="padding: 20px 0">
					<Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
					<p>点击或将文件拖拽到这里上传</p>
				</div>
			</Upload>
			<div slot="footer">
				<Button style="border: none;background: none;display: inline-block;float: left;" type="primary">
					<a :href="href" style="color: #3883e5;font-size: 16px;">下载导入模板</a>
				</Button>
				<i-Button type="primary" @click="importModal = false">关闭</i-Button>
			</div>
		</Modal>

		<iframe width="0" height="0" style="display:none;" id="exportExcelFrame"></iframe>
	</div>
</div>
<script>    	
	//控制登录失效后HTML页面跳转登录页
	verifyLogin();
    var vue =  new Vue({
        el:'#main',
        data: function() {       	                    	        	
        	var sf = this;
        	return {
				defId : "",
				listerId: "5b56d472900add501a1411ca", // 编辑人Id
				managerId: "62a69c9c66a6538180672f94", // 管理员Id
				normalId:"5a45f47cba4014a42391fa8e", // 普通成员Id
				tooltipShow: false,
				tooltipShowNormal: false,
				editPower: false,
				deleteAble : false,
				resData: [],
				buPower: false,
				isAdmin : false, // 节点的添加权限
        		selectGroupIdValue: "",
        		masterUsers: [],
				adminList: [],
				memberList: [],
        		isMemberAdd: false,
        		MemberData: [],
        		MemberKey: "",
        		keyWord: "",
        		selectedRole: 'organAdmin',
            	importModal: false,        	
                importGroupUserUrl: "",        	
                roleName: "",
    			choosedPrjId: "61386c2de0ee770622373f21",
    			treeData: [],				
    			treeLoading: false,			
    			isShowEdit: true,
    			tabList: [
		          // {
		        	//   disabled: true,
		        	//   id: "61503198e0ee7772178cf6f8",
		        	//   name: "项目领导层",
		          // },
		          // {
		        	//   disabled: true,
		        	//   id: "61503198e0ee7772178cf6f9",
		        	//   name: "项目PMO",
		          // }
    		    ],
				uploadModal: false,
				href: linkus.location.km + '/kmFileCtrl/downloadByName.action?fileName='+encodeURI("角色权限-导入模板.xlsx"),
				revenueUrl:'',
				roleType:'admin', // 前三页签为'admin' 计划编制人为'editor'
				addAdminFlag:false,  // 是否能添加管理员的权限
				addMemberFlag:false,  // 是否能添加普通成员的权限
			}
        },
        created: function(){
            var sf = this;            
            sf.importGroupUserUrl = linkus.location.abp + "/role/user/import";
            if(!Vue.evtHub){
				Vue.evtHub = new Vue();	
			}
			sf.queryBUAdminPower();
        },
		watch       :	{
			selectedRole : function() {
				var sf = this;
				if (sf.planAdmin == 'planAdmin') {
					sf.roleType = 'editor';
				} else {
					sf.roleType = 'admin';
				}
				// 切换页签时 加号框隐藏 点击节点后判断权限再展示
				sf.addAdminFlag = false;
				sf.addMemberFlag = false;
			},
		},
        mounted: function(){
        	var sf = this;
        	/* sf.queryprjRoleUsers(); */
			sf.getTreeData();
        	//获取树选择返回值
            Vue.evtHub.$on("treeSelectData",function (data) {
            	 sf.tabList = [];
            	 sf.selectGroupIdValue = data.selectData.id;
            	 var checkedNodeAndParents  = data.checkedNodeAndParents;            	               
                 for(var i = 0; i < checkedNodeAndParents.length; i++) {
                     sf.tabVariety('nofirst',checkedNodeAndParents[i].title,checkedNodeAndParents[i].id,checkedNodeAndParents[i].disabled);                 
                 }              
            });
        },
        methods: {
			//获取树数据
			getTreeData: function() {
				var sf = this;
				var selectedRole = sf.selectedRole;
				// 默认
				if (selectedRole=='organAdmin') {
					$.ajax({
						url: linkus.location.abp + '/role/org/tree',
						type: 'get',
						contentType: 'application/json',
					}).done(function(data) {
						sf.treeData = [];
						// 是否有BU权限
						if (sf.buPower) {  // 有bu权限不用过滤 查全部
							sf.parseJson(data.data, sf.treeData, 0);
						} else {
							sf.dataForFilter(data.data);
							sf.parseJson(sf.resData, sf.treeData, 0);
						}
						sf.treeData = sf.treeData.children;
						// sf.handleClickTreeNode(sf.treeData[0]) //点时触发展示人
						sf.handleClickTreeNode() // 默认不展示 必须点击节点才展示
					});
				}
				if (selectedRole=='prdLineAdmin') {
					$.ajax({
						url: linkus.location.abp + '/role/pl',
						type: 'get',
						contentType: 'application/json',
					}).done(function(data) {
						sf.treeData = [];
						sf.parseJson(data.data, sf.treeData, 0);
						sf.treeData = sf.treeData.children;
						sf.handleClickTreeNode() //点时触发展示人
					});
				}
				if (selectedRole=='resourceDepAdmin') {
					$.ajax({
						url: linkus.location.abp + '/role/res/src',
						type: 'get',
						contentType: 'application/json',
					}).done(function(data) {
						sf.treeData = [];
						sf.parseJson(data.data, sf.treeData, 0);
						sf.treeData = sf.treeData.children;
						sf.handleClickTreeNode() //点时触发展示人
					});
				}
				if (selectedRole=='planAdmin') {
					$.ajax({
						url: linkus.location.abp + '/role/prov',
						type: 'get',
						contentType: 'application/json',
					}).done(function(data) {
						sf.treeData = [];
						sf.parseJson(data.data, sf.treeData, 0);
						sf.treeData = sf.treeData.children;
						sf.handleClickTreeNode() //点时触发展示人
					});
				}
			},

			// 将masterUser值分为adminList和memberList
			filterList : function() {
				var sf = this;
				var data = sf.masterUsers;
				sf.adminList = [];
				sf.memberList = [];
				for (var index in data) {
					var obj = data[index];
					if (obj.roleId == sf.normalId) {
						sf.memberList.push(obj);
					} else {
						sf.adminList.push(obj);
					}
				}
			},

			// 过滤筛选展示的数据
			dataForFilter: function(data) {
				var sf = this;
				// 大区过滤
				var newArr = data.filter(function (element, index, array) {
					if (element.isShow) {
						return true;
					} else {
						return false;
					}
				})

				// 区域过滤
				newArr.forEach(function (item,index) {
					if (item.children && item.children.length > 1) {
						var children = item.children;
						item.children = children.filter(function (element, index, array) {
							if (element.isShow) {
								return true;
							} else {
								return false;
							}
						})
					}
					// console.log(item);
				});

				// 省份过滤
				newArr.forEach(function (item,index) {
					if (item.children) {
						var children = item.children;
						children.forEach(function (e, index) {
							if (e.children && e.children.length > 1) {
								var children = e.children;
								e.children = children.filter(function (element, index, array) {
									if (element.isShow) {
										return true;
									} else {
										return false;
									}
								})
							}
							// console.log(e);
						})
					}
				});
				// console.log(newArr);

				sf.resData = newArr;
			},

			//递归处理树
			parseJson: function(jsonObj, target, count) {
				var sf = this;
				// 循环所有键
				for(var key in jsonObj) {
					var element = jsonObj[key];
					// 1.判断是对象或者数组
					var params = {};
					if(count == 0) {
						params = {
							title: element.defName,
							id: element.id,
							isAdmin: element.isAdmin,
							expand: true,
							render: function(h,data) {
								// var root = data.root;
								var node = data.node;
								var data = data.data;
								var selectedRole = sf.selectedRole;
								// render: (h, { root, node, data }) => {
								return h('span', {
									class: "tree_warp",
									style: {
										display: 'inline-block',
										cursor: 'pointer',
										padding: '2px',
										borderRadius: '4px'
									},
									on:{
										//click:(event)=>{
										click: function(event) {
											// 其他页签数据无isAdmin标识
											if (selectedRole=='organAdmin') {
												if (data.isAdmin) {
													$('.tree_title').removeClass('ivu-tree-title-selected');
													if($(event.target).hasClass('tree_title')) {
														$(event.target).addClass('ivu-tree-title-selected');
													}else {
														$(event.target).find('.tree_title').addClass('ivu-tree-title-selected');
													}
													// 点击Tree节点触发
													sf.handleClickTreeNode(data)
												}
											} else {
												$('.tree_title').removeClass('ivu-tree-title-selected');
												if($(event.target).hasClass('tree_title')) {
													$(event.target).addClass('ivu-tree-title-selected');
												}else {
													$(event.target).find('.tree_title').addClass('ivu-tree-title-selected');
												}
												// 点击Tree节点触发
												sf.handleClickTreeNode(data)
											}
										}
									}
								}, [
									h('span', [
										h('span', {
											class: "tree_title",
											style: {
												display: 'inline-block',
												cursor: (!data.isAdmin && selectedRole =='organAdmin') ? 'no-drop':'pointer',
												padding: '3px',
												borderRadius: '4px'
											},
										}, data.title)
									])
								]);
							},
						};
					}else {
						params = {
							title: element.defName,
							id:element.id,
							isAdmin: element.isAdmin,
						}
					}

					if(element.children) {
						target.children = target.children || [];
						target.children.push(params);
						sf.parseJson(element.children,params)
					}else {
						target.children = target.children || [];
						target.children.push(params)
					}
				}
			},
			// 树渲染逻辑
			//  renderContent:function(h, { root, node, data }) {
			renderContent: function(h, data) {
				// var root = data.root;
				var node = data.node;
				var data = data.data;
				var sf = this;
				var selectedRole = sf.selectedRole;
				return h("span", {
					class: "tree_warp",
					style: {
						display: 'inline-block',
						cursor: 'pointer',
						padding: '2px',
						borderRadius: '4px'
					},
					on: {
						//click:(event)=>{
						click: function(event) {
							if (selectedRole=='organAdmin') {
								if (data.isAdmin) {
									$('.tree_title').removeClass('ivu-tree-title-selected');
									if($(event.target).hasClass('tree_title')) {
										$(event.target).addClass('ivu-tree-title-selected');
									}else {
										$(event.target).find('.tree_title').addClass('ivu-tree-title-selected');
									}
									// 点击Tree节点触发
									sf.handleClickTreeNode(data,node)
								}
							} else {
								$('.tree_title').removeClass('ivu-tree-title-selected');
								if($(event.target).hasClass('tree_title')) {
									$(event.target).addClass('ivu-tree-title-selected');
								}else {
									$(event.target).find('.tree_title').addClass('ivu-tree-title-selected');
								}
								// 点击Tree节点触发
								sf.handleClickTreeNode(data,node)
							}
						}
					}
				}, [
					h('span', [
						h('span' ,
								{
									class: "tree_title",
									style: {
										display: 'inline-block',
										cursor: (!data.isAdmin && selectedRole =='organAdmin') ? 'no-drop':'pointer',
										padding: '3px',
										borderRadius: '4px'
									},
								},data.title)
					])
				]);
			},

			// 点击Tree节点触发 查询该节点下有权限的人
			handleClickTreeNode: function(data, node) {
				var sf = this;
				if (data == null) {
					return;
				}
				sf.masterUsers = []; // 切换页签时会触发 先清理页面展示
				sf.memberList = [];
				sf.adminList = [];
				var selectedRole = sf.selectedRole;
				var keyWord = sf.keyWord;
				if (!!data) {
					sf.isAdmin = data.isAdmin; // 节点编辑权限赋值
				}
				var roleId = '';
				if (selectedRole == 'organAdmin' || selectedRole == 'prdLineAdmin' || selectedRole == 'resourceDepAdmin') {
					roleId = sf.managerId;
				}
				if (selectedRole == 'planAdmin') {
					roleId = sf.listerId;
				}
				// 查询是否有添加管理员的权限
				$.ajax({
					type	:	"get",
					url		: 	linkus.location.abp + '/role/check',
					data    :   {
						roleType : sf.roleType,
						defId: data.id,
						roleId: roleId
					},
					headers: {
						'Content-Type': 'application/json;charset=utf-8'
					},
					success	: 	function(text) {
						sf.addAdminFlag = text.data;
					},
					error	: 	function(text) {
						_this.$Message.warning('查询添加管理员权限出错,请联系管理员!');
					}
				});

				// 计划编制人不分普通用户和管理员
				if (selectedRole == 'planAdmin') {
					sf.addMemberFlag = sf.addAdminFlag;
				} else {
					// 查询是否有添加普通成员的权限
					$.ajax({
						type	:	"get",
						url		: 	linkus.location.abp + '/role/check',
						data    :   {
							roleType : sf.roleType,
							defId: data.id,
							roleId: sf.normalId
						},
						headers: {
							'Content-Type': 'application/json;charset=utf-8'
						},
						success	: 	function(text) {
							sf.addMemberFlag = text.data;
						},
						error	: 	function(text) {
							_this.$Message.warning('查询添加管理员权限出错,请联系管理员!');
						}
					});
				}

				// 组织数的查询方法和其他三不一样
				// roleID（页签ID）中 计划编辑人是单独的
				if (selectedRole == 'organAdmin') {
					$.ajax({
						// url: linkus.location.abp + '/role/admin/user',
						url: linkus.location.abp + '/role/user',
						type: 'get',
						contentType: 'application/json',
						data: {
							roleId : sf.managerId,
							defId : data.id,
							queryKey : sf.keyWord
						},
						success: function(res) {
							sf.masterUsers = res.data;
							sf.filterList();
							if (sf.keyWord.length > 0 && res.data.length == 0) {
								sf.$Message.info("当前节点此人员无权限")
							}
							if (!!data) {
								sf.defId = data.id; // 删除时要用
							}
						},
						error: function(res) {
							sf.$Message.error("查询权限失败，请联系管理员!")
						}
					})
				} else {
					if (selectedRole == 'planAdmin') {
						$.ajax({
							url: linkus.location.abp + '/role/user',
							type: 'get',
							contentType: 'application/json',
							data: {
								roleId : sf.listerId, // 单独的roleID
								defId : data.id,
								queryKey : sf.keyWord
							},
							success: function(res) {
								if (!!res.data) {
									sf.masterUsers = res.data;
									sf.filterList();
									if (sf.keyWord.length > 0 && res.data.length == 0) {
										sf.$Message.info("当前节点此人员无权限")
									}
									if (!!data) {
										sf.defId = data.id; // 删除时要用
									}
								}
							},
							error: function(res) {
								sf.$Message.error("查询权限失败，请联系管理员!")
							}
						})
					} else {
						$.ajax({
							url: linkus.location.abp + '/role/user',
							type: 'get',
							contentType: 'application/json',
							data: {
								roleId : sf.managerId,
								defId : data.id,
								queryKey : sf.keyWord
							},
							success: function(res) {
								if (!!res.data) {
									sf.masterUsers = res.data;
									sf.filterList();
									if (sf.keyWord.length > 0 && res.data.length == 0) {
										sf.$Message.info("当前节点此人员无权限")
									}
									if (!!data) {
										sf.defId = data.id; // 删除时要用
									}
								}
							},
							error: function(res) {
								sf.$Message.error("查询权限失败，请联系管理员!")
							}
						})
					}
				}
			},

        	/* //获取项目角色
      		queryprjRoleUsers: function() {
        		var sf = this;
        		sf.masterUsers = [];       		
        		$.ajax({                    
                    url: linkus.location.prjbiz + "/kbMgr/prdCtlgRoleUser/"+ "5caebb64e0ee7707db144a70",
                    type: 'get',
                    contentType: "application/json; charset=utf-8",                 
                    success: function (result) {
                    	if(result.status == 'success'){
                    		var roleUsers = result.data || [];
                        	if(!!roleUsers && roleUsers.length > 0){
                        		for(var i = 0; i < roleUsers.length; i++){
                        			var roleUser = roleUsers[i];
                        			roleUser['user']['headPicUrl'] = sf.headPicUrl + roleUser.user.userId;
                        			roleUser['user']['roleId'] = roleUser.role.roleId;
                        			sf.masterUsers.push(roleUser.user);
                        		}
                        	}
                    	}else{
                    		sf.$Message.error({
    		                    content: result.message,
    		                    duration: 3
    		                });
                    	}                  	
                    },
                    error: function (text) {                   	
                    	sf.$Message.error('查询产品角色错误，请联系管理员!');
                    }
                });
        	}, */
        	
        	// //删除人员
        	// deleteUser: function() {
        	// 	var sf = this;
			// 	sf.deleteAble = !sf.deleteAble;
        	// },
        	
        	//删除人员
            delRoleUser: function(flag, index, type) {
        		var sf = this;
				if (type == 'admin') {
					if (!sf.addAdminFlag) {
						sf.$Message.warning('暂无权限删除!');
						return;
					}
				} else {
					if (!sf.addMemberFlag) {
						sf.$Message.warning('暂无权限删除!');
						return;
					}
				}

				if (selectedRole == 'organAdmin' && !sf.isAdmin) {
					sf.$Message.warning('无权限删除!');
					return;
				}
					var userId = '';

					var roleId = '';
					var sysDefRoleUserId = '';
					var selectedRole = sf.selectedRole;
					if (selectedRole == 'planAdmin') {
						roleId = sf.listerId;
					} else {
						// 再判断删的是管理员还是普通用户
						if (type == 'admin') {
							roleId = sf.managerId;
						} else {
							roleId = sf.normalId;
						}
					}
					// 分权判断
					if (flag == 'master') {
						// var user = sf.masterUsers[index];
						var user = null;
						if (type == 'admin') {
							user = sf.adminList[index];
						} else {
							user = sf.memberList[index];
						}
						userId = user.userId;
						sysDefRoleUserId = user.sysDefRoleUserId;
					} else if(flag == 'po') {
						var user = sf.poUsers[index];
						userId = user.userId;
						roleId = user.roleId;
					} else if(flag == 'member') {
						var user = sf.memberUsers[index];
						userId = user.userId;
						roleId = user.roleId;
					} else if(flag == 'tester') {
						var user = sf.testerUsers[index];
						userId = user.userId;
						roleId = user.roleId;
					}
					$.ajax({
						url: linkus.location.abp + '/role/user?roleId='+ roleId
								+ '&defId=' + sf.defId
								+ '&userId=' + userId
								+ '&roleType=' + sf.roleType
								+ '&sysDefRoleUserId=' + sysDefRoleUserId,
						type: 'delete',
						contentType: "application/json; charset=utf-8",
						success: function (result) {
							if(result.status == 'success'){
								sf.$Message.success({
									content: '删除人员成功!等待后台刷新',
									duration: 2
								});
								sf.handleClickTreeNodeNodata(); // 当前选择节点后本页面内展示
							}else{
								sf.$Message.error({
									content: result.message,
									duration: 3
								});
							}
						},
						error: function (text) {
							sf.$Message.error('删除人员失败，请联系管理员!');
						}
					});
				// }
        	},
        	
        	//添加管理员
        	addUser: function(item) {
        		var sf = this;
				var selectedRole = sf.selectedRole;
				var defId = sf.defId;
				if (defId == null || defId == "") {
					sf.$Message.warning('请选择相应节点再添加角色!');
					return;
				}
				// 添加权限
				if (selectedRole == 'organAdmin' && !sf.isAdmin) {
					sf.$Message.warning('无权限添加!');
					return;
				}

				if (selectedRole == 'planAdmin') {
					$.ajax({

						url: linkus.location.abp + '/role/user?roleId='+ sf.listerId
								+ '&defId=' + sf.defId
								+ '&userId=' + item.id
								+ '&roleType=' + sf.roleType,
						type: 'post',
						contentType: "application/json; charset=utf-8",
						success: function(res) {
							sf.tooltipShow = false;
							sf.tooltipShowNormal = false;
							if (res.success) {
								sf.$Message.success({
									content: '新增编制人权限成功,页面即将刷新',
									duration: 2
								});
								sf.handleClickTreeNodeNodata();
							} else {
								sf.$Message.warning(res.message);
							}

						},
						error: function(data) {
							sf.$Message.error("新增编制人权限失败，请联系管理员!")
						}
					})
				} else {
					$.ajax({
						url: linkus.location.abp + '/role/user?roleId=' + sf.managerId
								+ '&defId=' + sf.defId
								+ '&userId=' + item.id
								+ '&roleType=' + sf.roleType,
						type: 'post',
						contentType: "application/json; charset=utf-8",
						success: function(res) {
							sf.tooltipShow = false;
							sf.tooltipShowNormal = false;
							if (res.success) {
								sf.$Message.success({
									content: '新增管理员权限成功,页面即将刷新',
									duration: 2
								});
								sf.handleClickTreeNodeNodata();
							} else {
								sf.$Message.warning(res.message);
							}
						},
						error: function(data) {
							sf.$Message.error("新增管理员权限失败，请联系管理员!")
						}
					})
				}
        	},

			//添加普通用户
			addNormalUser: function(item) {
				var sf = this;
				var selectedRole = sf.selectedRole;
				var defId = sf.defId;
				if (defId == null || defId == "") {
					sf.$Message.warning('请选择相应节点再添加角色!');
					return;
				}
				// 添加权限
				if (selectedRole == 'organAdmin' && !sf.isAdmin) {
					sf.$Message.warning('无权限添加!');
					return;
				}
					$.ajax({
						url: linkus.location.abp + '/role/user?roleId='+ sf.normalId
								+ '&defId=' + sf.defId
								+ '&userId=' + item.id
								+ '&roleType=' + sf.roleType,
						type: 'post',
						contentType: "application/json; charset=utf-8",
						success: function(res) {
							sf.tooltipShow = false;
							sf.tooltipShowNormal = false;
							if (res.success) {
								sf.$Message.success({
									content: '新增编制人权限成功,页面即将刷新',
									duration: 2
								});
								sf.handleClickTreeNodeNodata();
							} else {
								sf.$Message.warning(res.message);
							}

						},
						error: function(data) {
							sf.$Message.error("新增编制人权限失败，请联系管理员!")
						}
					})
				},

			// 不传参数的 defId默认是全局赋值的 只是展示用
			handleClickTreeNodeNodata: function() {
				var sf = this;
				var selectedRole = sf.selectedRole;
				var keyWord = sf.keyWord;
				var defId = sf.defId ;
				// 组织数的查询方法和其他三不一样
				// roleID（页签ID）中 计划编辑人是单独的
				if (selectedRole == 'organAdmin') {
					$.ajax({
						// url: linkus.location.abp + '/role/admin/user',
						url: linkus.location.abp + '/role/user',
						type: 'get',
						contentType: 'application/json',
						data: {
							roleId : sf.managerId,
							defId : defId,
							queryKey : sf.keyWord
						},
						success: function(res) {
							sf.masterUsers = res.data;
							sf.filterList();
							if (sf.keyWord.length > 0 && res.data.length == 0) {
								sf.$Message.info("当前节点此人员无权限")
							}
						},
						error: function(res) {
							sf.$Message.error("查询权限失败，请联系管理员!")
						}
					})
				} else {
					if (selectedRole == 'planAdmin') {
						$.ajax({
							url: linkus.location.abp + '/role/user',
							type: 'get',
							contentType: 'application/json',
							data: {
								roleId : sf.listerId, // 单独的roleID
								defId : defId,
								queryKey : sf.keyWord
							},
							success: function(res) {
								if (!!res.data) {
									sf.masterUsers = res.data;
									sf.filterList();
									if (sf.keyWord.length > 0 && res.data.length == 0) {
										sf.$Message.info("当前节点此人员无权限")
									}
								}
							},
							error: function(res) {
								sf.$Message.error("查询权限失败，请联系管理员!")
							}
						})
					} else {
						$.ajax({
							url: linkus.location.abp + '/role/user',
							type: 'get',
							contentType: 'application/json',
							data: {
								roleId : sf.managerId,
								defId : defId,
								queryKey : sf.keyWord
							},
							success: function(res) {
								if (!!res.data) {
									sf.masterUsers = res.data;
									sf.filterList();
									if (sf.keyWord.length > 0 && res.data.length == 0) {
										sf.$Message.info("当前节点此人员无权限")
									}
								}
							},
							error: function(res) {
								sf.$Message.error("查询权限失败，请联系管理员!")
							}
						})
					}
				}
			},
        	
        	//关闭添加人员弹窗
        	clearUserList: function() {
        		var sf = this;
        		sf.MemberData = [];
        		sf.MemberKey = '';
        	},
        	
        	//打开导入弹窗
        	openImportModal: function() {
        		var sf = this;
        		sf.importModal = true;
        	},
        	
        	//选择角色 切换查询
        	selectRole: function(roleName) {
        		var sf = this;
        		sf.selectedRole = roleName;
				// 切换页签时清空
				sf.masterUsers = [];
				sf.memberList = [];
				sf.adminList = [];
				sf.getTreeData();
        	},
        	
        	//搜索人员
        	searchMember: function() {
        		var sf = this;
        		var value = sf.MemberKey;
                $.ajax({
                    url : linkus.location.prjuser +'/sysUserCtrl/findUserByFuzzy.action',
                    type : 'post',
                    data : {
                        value:value
                    },
                    success : function(data) {
                        data = data || [];
                        data.map(function (item,i) {
                            item = sf.dataDeal(item,'name')
                        });
                        sf.MemberData = data;
                    },
                    error : function(data) {
                    }
                });
        	},
        	
        	//处理数据
            dataDeal:function(item,type){
                var sf = this;
                if(type === 'name'){
                    var userName = item.userName;
                    var loginName = item.loginName;
                    if(userName && loginName){
                        item.valueName ='<span>'+userName+'</span> / <span>'+loginName+'</span>'
                    }else if(userName){
                        item.valueName = '<span>'+userName+'</span>'
                    }else{
                        item.valueName = '<span>'+loginName+'</span>'
                    }
                }
                return item;
            },
        	
        	//关键词搜索
        	keywordSearch: function() {
        		var sf = this;
				// sf.getTreeData();
				sf.handleClickTreeNodeNodata();
        	},
        	
        	//索引切换
            tabToList: function(index){
                var sf =this;
                var data = sf.tabList[index];
                sf.selectGroupIdValue = data.id;
                var tabList = [].concat(sf.tabList);
                if(index !=(sf.tabList.length-1)){
                    if(index === 0){
                        sf.tabVariety('first',data.name,data.id,sf.isFirstDisabled);                       
                    }                   
                    var selectId = data.id;
                    var thisIndex = 1;
                    var data = sf.$refs.knowTree.$data.treeData;
                    sf.treeSelect(data,selectId,thisIndex,index,tabList);
                    sf.tabList.splice(index+1, sf.tabList.length-index-1);
                    sf.$refs.knowTree.$data.treeData = data;                    
   				    
                }
            }, 
            //树选中
            treeSelect:function(data,id,index,selectIndex,tabList){
                var sf = this;
                data.map(function (item,i) {
                    if(item.id === tabList[index].id){
                        if(index === tabList.length-1){
                            sf.$set(item, 'selected', false);
                        }else{
                            if(item.id === id){
                                sf.$set(item, 'selected', true);
                            }
                            sf.treeSelect(item.children,id,index+1,selectIndex,tabList);
                        }

                    }
                });
            },
            //tab索引  展示上面的目录路径？
            tabVariety: function(type,defName,id,isDisabled){
                var sf = this;
                if(type === 'first'){
                    sf.tabList = [];
                }
                sf.tabList.push(
                    {
                        name:defName,
                        id:id,
                        disabled:isDisabled
                    }
                );
            },
        	/* 导入成功 */
        	importSuccess: function(result){
	    		var sf = this;
	    		sf.$Spin.hide();
	    		sf.importModal = false;
				if (result.success == false) {
					sf.$Message.error({
						content: result.message,
						duration: 5
					});
				} else {
					sf.$Message.success({
						content: "导入成功,即将刷新页面数据!",
						duration: 4
					});
					sf.getTreeData();
				}
	    	},
	    	/* 导入前 */
	    	beforeImport : function() {
	    		var sf = this;
	    		sf.$Spin.show();
			},
			/* 导入失败 */
			importFail : function(result) {
				var sf = this;
				sf.$Spin.hide();
				sf.importModal = false;
				sf.uploadModal = false;
				sf.$Message.error({
					content: result.message,
					duration: 3
				});
			},

			queryBUAdminPower	:	function() {
				var _this = this;
				$.ajax({
					type	:	"get",
					url		: 	linkus.location.abp + '/role/bu/admin',
					headers: {
						'Content-Type': 'application/json;charset=utf-8'
					},
					success	: 	function(text) {
						var data = text.data;
						if (data) {
							_this.editPower = true;
							_this.buPower = true;
						}
					},
					error	: 	function(text) {
						_this.$Message.warning('查询权限出错,请联系管理员!');
					}
				});
			},

			/* 导入成功 */
			revenueImportSuccess: function(result) {
				var sf = this;
				sf.$Spin.hide();
				sf.uploadModal = false;
				if (result.data == null || result.data.checkValid == 'false') {
					sf.$Message.error({
						content: result.message,
						duration: 5
					});
				} else {
					sf.$Message.success({
						content: "导入成功,即将刷新页面数据!",
						duration: 4
					});
					sf.queryTableList();
				}
			},

			downLoadModal : function() {
				var sf = this;
				sf.$Message.warning({
					content  : '正在下载导入模板，请勿重复点击，请稍等！',
					duration : 5
				});

				var form = $("<form>");
				form.attr('style', 'display:none');
				form.attr('target', '');
				form.attr('method', 'post');
				form.attr('action', sf.href);

				var input1 = $('<input>');
				input1.attr('type', 'hidden');
				input1.attr('name', "value");
				// input1.attr('value', JSON.stringify(params)); //向后台传参，参数值 /

				$('body').append(form);
				form.append(input1);
				form.submit();
				form.remove();
			},

			jurExport:function (){
				var sf = this;
				sf.$Message.warning({
					content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
					duration: 3
				});
				$("#exportExcelFrame").attr("src",linkus.location.abp + "/role/user/export");
			},
		}
    });
</script>
</body>
</html>
