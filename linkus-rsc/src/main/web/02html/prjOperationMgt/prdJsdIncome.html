<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
	<title>产品域-02</title>
	<!-- 强制清楚缓存 -->
	<META HTTP-EQUIV="pragma" CONTENT="no-cache"> 
	<META HTTP-EQUIV="Cache-Control" CONTENT="no-cache, must-revalidate"> 
	<META HTTP-EQUIV="expires" CONTENT="0">
    <!-- 兼容ie -->
    <script src="../../00scripts/00lib/ie/browser.js"></script>
    <link href="../../01css/style.css" rel="stylesheet" type="text/css"/>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.plugin.js"></script>
    <script src="../../00scripts/location/location.js"></script>
    <script src="../../00scripts/00lib/utils/ajax.js"></script>

    <script src="../../00scripts/00lib/vue/2.5.16/vue.js"></script>
    <script src="../../00scripts/00lib/vue/2.5.16/vue.min.js"></script>
    
    <script src="../../00scripts/00lib/iview/2.5.16/iview.min.js"></script>
    <link href="../../00scripts/00lib/iview/2.5.16/styles/iview.css" rel="stylesheet" type="text/css"/>

    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css"/>
    <script src="../../01css/font/iconfont.js"></script>
    <link href="tablecss/jsdTable.css" rel="stylesheet" type="text/css"/>
    <!-- 通用头部 -->
    <script src="./header/header.js"></script>
</head>
<style>
	[v-cloak] {
		display: none !important;
	}
	.ivu-table-row-highlight td, .ivu-table-stripe .ivu-table-body tr.ivu-table-row-highlight:nth-child(2n) td, .ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-highlight:nth-child(2n) td, tr.ivu-table-row-highlight.ivu-table-row-hover td {
    background-color: #fcf5e5;
	}
	.ivu-table-height{
		height: calc(100% - 48px) !important;
	}
	.ivu-table-body{
		height: calc(100% - 100px) !important;
	}
	.ivu-table-header  tr  th{
		font-weight:bold;
	}
</style>
<body style="overflow-y: hidden;overflow-x:auto;">
	<div id="main"  class="main bg-light-grey" v-cloak style="height:100%;padding-top:43px;">
		<div class="conrainer" style=";height:100%;width:100%; width: 100vw;overflow: hidden;" scroll="no">
			<!-- 表头  -->
		 	<prj-header></prj-header>
			<!-- 导出按钮 -->
		 	<div class="pd8 pd-side0" style="height: 48px; margin-top:1%;">
				<i-button :disabled="exportDataFlag" type="warning" style="float: right;" @click="exportData">导出</i-button>
			</div>
			<!-- 表格内容 -->
			<i-Table ref="mdTable" 
						 id="incomeTable" 
						 width="100%"
						 size="small"
						 stripe border 
						 highlight-row
						 class="ivu-table-height"  
						 :height="quaterTableHeight" 
						 :loading="quaterTableLoading"
						 :columns="quaterMdForm.columns" 
						 :data="quaterMdForm.list">
			</i-Table>
			<!-- 下载 -->
			<iframe width="0" height="0" style="display:none;" id="exportExcelFrame"></iframe>	
		</div>
	</div>
</body>
<script type="text/javascript">
var vue=new Vue({
	el 		: '#main',
	data 	: {
		quaterTableHeight: 				580,
		quaterTableLoading: 			false,
		quaterMdForm:{
			columns :[
					{
						title: '序号',
						type : 'index',
						width:40,
						fixed: "left",
					},
					{
						title: 'Bu',
					    key: 'buName',  
					    width:90,
					    fixed: "left",
					    render:function (h, params) {
					    	var buNameObj = params.row.buName;
					    	var buNameObjs = buNameObj.split("-");
					    	if(buNameObjs.length<2){
					    		return h('div',{style: {"fontWeight":900}},params.row.buName)
					    	}else{
					    		return h('div',params.row.buName)
					    	}
						}
					},
					{
						 title: '2018',
						 align: 'left',
						 children: [
							{
							    title: 'BSS',
							    key: 'lqbss',
							    align: 'right',
							    width: 100,
							    style:{color : 'red'},
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.lqbss;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
							{
							    title: 'IOT',
							    key: 'lqlot',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.lqlot;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div',{},value)
								}
							},
							{
							    title: 'OSS',
							    key: 'lqoss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.lqoss;
									if(!!tempValue){
										value = tempValue.toFixed(0);
										var absNum = Math.abs(value);
										if(absNum<=10){
											value= 0;
										}
									}
									value = (value+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
									return h('div', value)
								}
							},
							{
							    title: '软件',
							    key: 'lqswPrdSvc',
							    align: 'right',
							    width: 150,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.lqswPrdSvc;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
						 ]
					},
					
					
					
					
					
					{
						 title: '0331预估收入',
						 align: 'left',
						 children: [
							{
							    title: 'BSS',
							    key: 'Q1bss',
							    align: 'right',
							    width: 100,
							    style:{color : 'red'},
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q1bss;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
							{
							    title: 'IOT',
							    key: 'Q1lot',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q1lot;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div',{},value)
								}
							},
							{
							    title: 'OSS',
							    key: 'Q1oss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q1oss;
									if(!!tempValue){
										value = tempValue.toFixed(0);
										var absNum = Math.abs(value);
										if(absNum<=10){
											value= 0;
										}
									}
									value = (value+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
									return h('div', value)
								}
							},
							{
							    title: '软件',
							    key: 'Q1swPrdSvc',
							    align: 'right',
							    width: 150,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q1swPrdSvc;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
						 ]
					},
					{
						 title: '0630预估收入',
						 align: 'left',
						 children: [
							{
							    title: 'BSS',
							    key: 'Q2bss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q2bss;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
							{
							    title: 'IOT',
							    key: 'Q2lot',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q2lot;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							},
							{
							    title: 'OSS',
							    key: 'Q2oss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q2oss;
									if(!!tempValue){
										value = tempValue.toFixed(0);
										var absNum = Math.abs(value);
										if(absNum<=10){
											value= 0;
										}
									}
									value = (value+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
									return h('div', value)
								}
							},
							{
							    title: '软件',
							    key: 'Q2swPrdSvc',
							    align: 'right',
							    width: 150,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q2swPrdSvc;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							},
						 ]
					},
					{
						 title: '0930预估收入',
						 align: 'left',
						 children: [
							{
							    title: 'BSS',
							    key: 'Q3bss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q3bss;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
							{
							    title: 'IOT',
							    key: 'Q3lot',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q3lot;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							},
							{
							    title: 'OSS',
							    key: 'Q3oss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q3oss;
									if(!!tempValue){
										value = tempValue.toFixed(0);
										var absNum = Math.abs(value);
										if(absNum<=10){
											value= 0;
										}
									}
									value = (value+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
									return h('div', value)
								}
							},
							{
							    title: '软件',
							    key: 'Q3swPrdSvc',
							    align: 'right',
							    width: 150,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q3swPrdSvc;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
						 ]
					},
					{
						 title: '1231预估收入',
						 align: 'left',
						 children: [
							{
							    title: 'BSS',
							    key: 'Q4bss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q4bss;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							}, 
							{
							    title: 'IOT',
							    key: 'Q4lot',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q4lot;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							},
							{
							    title: 'OSS',
							    key: 'Q4oss',
							    align: 'right',
							    width: 100,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q4oss;
									if(!!tempValue){
										value = tempValue.toFixed(0);
										var absNum = Math.abs(value);
										if(absNum<=10){
											value= 0;
										}
									}
									value = (value+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')
									return h('div', value)
								}
							},
							{
							    title: '软件',
							    key: 'Q4swPrdSvc',
							    align: 'right',
							    width: 150,
							    render:function (h, params) {
									var value = 0;
							    	var tempValue = params.row.Q4swPrdSvc;
									if(!!tempValue){
										value = (tempValue.toFixed(0)+"").replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
									}
									return h('div', value)
								}
							},
						 ]
					}
			],
			list		:				[],
			pageTotal	: 				0,
			pageNum		:				1,
			pageSize	:				20,
			pageSizeOpts: 				[20, 50, 100],
		},
		exportDataFlag  : false,
	},
	created : function() {
		this.loadJsdIncome();
		$("title", parent.document).html("产品域-02"); 
		var currentDate = new Date();
		var currYear = currentDate.getFullYear();
		var lqYear = currYear-1;
		lqYear = lqYear +"年度预估收入";
		var columns = this.quaterMdForm.columns;
		columns[2].title = lqYear;
	},
	mounted : function() {
		this.quaterTableHeight = window.innerHeight - this.$refs.mdTable.$el.offsetTop - 80
	},
	methods : {
		"loadJsdIncome": function(){
			var sf = this;
			sf.$Spin.show();
			var currentDate = new Date();
			var currYear = currentDate.getFullYear();
			var vo = {
					cuurentYear: currYear,
			}
			$.ajax({
                url:linkus.location.prjbiz+'/pomCtr/loadPrdJsdIncome.action',
                headers : {'Content-Type' : 'application/json;charset=utf-8'},
                data : JSON.stringify(vo),
                type : 'post',
                success : function(data) {
                	if(!!data){
                		sf.dealResultList(data);
                	}
                	sf.$Spin.hide();
                },
                error : function(data){
                	sf.$Spin.hide();
                }
            });
		},
		"dealResultList": function(data){
			var sf = this;
			var dataArray = sf.getInitDataList();
			if(!!data.Q1){
				for(var i = 0; i<data.Q1.length; i++){
					var list = data.Q1[i];
					for(var j=0; j<dataArray.length; j++){
						if(data.Q1[i].buName == dataArray[j].buName){
							dataArray[j]["Q1bss"] = list.bss;
							dataArray[j]["Q1oss"] = list.oss;
							dataArray[j]["Q1lot"] = list.lot;
							dataArray[j]["Q1swPrdSvc"] = list.swPrdSvc;
							//break;
						}
					}
				}
			}
			if(!!data.Q2){
				for(var i = 0; i<data.Q2.length; i++){
					var list = data.Q2[i];
					for(var j=0; j<dataArray.length; j++){
						if(data.Q2[i].buName == dataArray[j].buName){
							dataArray[j]["Q2bss"] = list.bss;
							dataArray[j]["Q2oss"] = list.oss;
							dataArray[j]["Q2lot"] = list.lot;
							dataArray[j]["Q2swPrdSvc"] = list.swPrdSvc;
							//break;
						}
					}
				}
			}
			if(!!data.Q3){
				for(var i = 0; i<data.Q3.length; i++){
					var list = data.Q3[i];
					for(var j=0; j<dataArray.length; j++){
						if(data.Q3.buName == dataArray[j].buName){
							dataArray[j]["Q3bss"] = list.bss;
							dataArray[j]["Q3oss"] = list.oss;
							dataArray[j]["Q3lot"] = list.lot;
							dataArray[j]["Q3swPrdSvc"] = list.swPrdSvc;
							//break;
						}
					}
				}
			}
			if(!!data.Q4){
				for(var i = 0; i<data.Q4.length; i++){
					var list = data.Q4[i];
					for(var j=0; j<dataArray.length; j++){
						if(data.Q4[i].buName == dataArray[j].buName){
							dataArray[j]["Q4bss"] = list.bss;
							dataArray[j]["Q4oss"] = list.oss;
							dataArray[j]["Q4lot"] = list.lot;
							dataArray[j]["Q4swPrdSvc"] = list.swPrdSvc;
							//break;
						}
					}
				}
			}
			if(!!data.lq){
				for(var i = 0; i<data.lq.length; i++){
					var list = data.lq[i];
					for(var j=0; j<dataArray.length; j++){
						if(data.lq[i].buName == dataArray[j].buName){
							dataArray[j]["lqbss"] = list.bss;
							dataArray[j]["lqoss"] = list.oss;
							dataArray[j]["lqlot"] = list.lot;
							dataArray[j]["lqswPrdSvc"] = list.swPrdSvc;
							//break;
						}
					}
				}
			}
			sf.quaterMdForm.list = dataArray;
		},
		"getInitDataList" : function(){
			var dataList = [
							{
								buName:"CMC"
							},
							{
								buName:"CMC-自有"
							},
							{
								buName:"CMC-OBC"
							},
							{
								buName:"CUC"
							},
							{
								buName:"CUC-自有"
							},
							{
								buName:"CUC-OBC"
							},
							{
								buName:"CTC"
							},
							{
								buName:"CTC-自有"
							},
							{
								buName:"CTC-OBC"
							},
							{
								buName:"CSC"
							},
							{
								buName:"KIC"
							},
							{
								buName:"OBC"
							},
							{
								buName:"OBC-自有"
							},
							{
								buName:"CMC-OBC"
							},
							{
								buName:"CUC-OBC"
							},
							{
								buName:"CTC-OBC"
							},
							{
								buName:"CBU"
							},
							{
								buName:"合计"
							},
			];
			return dataList;
		},
		"exportData": function(){
			var sf = this;
			sf.exportDataFlag = true;
			sf.$Message.warning({
				content: "正在导出，请勿重复点击!"
			})
			var currentDate = new Date();
			var currMonth = currentDate.getMonth() + 1;
			var lastYear =parseInt(currentDate.getFullYear())-1;
			var currQuarter = Math.floor((currMonth % 3 == 0 ? ( currMonth / 3 ) : ( currMonth / 3 + 1 ) ) );
			var url = encodeURI(linkus.location.prjbiz + "/pomCtr/exportDataByName.action?"
					+"tableName=prdJsd"
					+"&cuurentYear="+currentDate.getFullYear()
					+"&cuurentMonth="+currMonth
					+"&cuurentQuater="+currQuarter);
			document.getElementById("exportExcelFrame").src = url;
			setTimeout(function(){
				sf.exportDataFlag = false;
			},10*1000)
		},
	},
})
$(window).resize(function () {
	vue.quaterTableHeight = window.innerHeight - vue.$refs.mdTable.$el.offsetTop - 90
})
</script>
</html>