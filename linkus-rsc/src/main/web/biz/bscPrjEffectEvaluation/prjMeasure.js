Date.prototype.format = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1,                 //月份
        "d+": this.getDate(),                    //日
        "h+": this.getHours(),                   //小时
        "m+": this.getMinutes(),                 //分
        "s+": this.getSeconds(),                 //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds()             //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
};
/* 控制登录失效后页面跳转登录页 */
verifyLogin();
var vue = new Vue({
    el: '#main',
    data: function () {
        var sf = this;
        return {

            reqTimelinessRateErrorMessage: "",

            isSummaryModal: false,
            summaryModalTitle: "",

            isExport: false,
            isDevOrTestDetail: false,

            taskCompletionDatas: [],
            taskCompletionLoading: false,
            taskCompletionTableHeight: 250,
            taskCompletionTableSortKey: "totalDown",

            taskClassificationId: "",
            isTaskCompletionDetail: false,

            taskCompletionColumns: [
                {
                    title: '任务分类',
                    key: 'taskClassification',
                },
                {
                    title: '任务总数',
                    key: 'taskNum',
                    width: 70,
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["taskNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTaskCompletionDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = params.row["taskClassification"] + "-任务总数";
                                    sf.isExport = true;
                                    sf.taskClassificationId = params.row["taskClassificationId"];
                                    sf.searchType = "total";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["taskNum"]);
                    }
                },
                {
                    title: '已完成',
                    key: 'completedNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["completedNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTaskCompletionDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = params.row["taskClassification"] + "-已完成";
                                    sf.isExport = true;
                                    sf.taskClassificationId = params.row["taskClassificationId"];
                                    sf.searchType = "finish";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["completedNum"]);
                    }
                },
                {
                    title: '待完成',
                    key: 'uncompleteNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["uncompleteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTaskCompletionDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = params.row["taskClassification"] + "-待完成";
                                    sf.isExport = true;
                                    sf.taskClassificationId = params.row["taskClassificationId"];
                                    sf.searchType = "unFinish";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["uncompleteNum"]);
                    }
                },
                {
                    title: '完成率',
                    key: 'completionRate',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["completionRate"]) {
                            return h('div', 0);
                        }
                        return h('div', params.row["completionRate"] + "%");
                    }
                },
                {
                    title: '周期内计划完成数',
                    key: 'planCompleteNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["planCompleteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTaskCompletionDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = params.row["taskClassification"] + "-周期内计划完成数";
                                    sf.isExport = true;
                                    sf.taskClassificationId = params.row["taskClassificationId"];
                                    sf.searchType = "plan";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["planCompleteNum"]);
                    }
                },
                {
                    title: '周期内按时完成数',
                    key: 'completedOnTimeNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["completedOnTimeNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTaskCompletionDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = params.row["taskClassification"] + "-周期内按时完成数";
                                    sf.isExport = true;
                                    sf.taskClassificationId = params.row["taskClassificationId"];
                                    sf.searchType = "actual";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["completedOnTimeNum"]);
                    }
                },
                {
                    title: '及时完成率',
                    key: 'taskTimelinessRate',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["taskTimelinessRate"]) {
                            return h('div', 0);
                        }
                        return h('div', params.row["taskTimelinessRate"] + "%");
                    }
                },
            ],

            devTaskDatas: [],
            devTaskLoading: false,
            devTaskTableHeight: 250,
            devTaskTableSortKey: "unRerolvedDown",

            devTaskColumns: [
                {
                    title: '人员',
                    key: 'userName',
                },
                {
                    title: '待开发',
                    key: 'unRerolvedNum',
                    width: 70,
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["unRerolvedNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-待开发";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "unRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unRerolvedNum"]);
                    }
                },
                {
                    title: '待开发超期1周',
                    key: 'unRerolvedDelayNum',
                    width: 112,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["unRerolvedDelayNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-待开发超期1周";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "delayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unRerolvedDelayNum"]);
                    }
                },
                {
                    title: '已开发完成',
                    key: 'rerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["rerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-已开发完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "rerolved";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["rerolveNum"]);
                    }
                },
                {
                    title: '日开发完成',
                    key: 'dayRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["dayRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-日开发完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "dayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["dayRerolveNum"]);
                    }
                },
                {
                    title: '周开发完成',
                    key: 'weekRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["weekRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-周开发完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "weekRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["weekRerolveNum"]);
                    }
                },
                {
                    title: '月开发完成',
                    key: 'monthRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["monthRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待开发任务报表-月开发完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.devNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "monthRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["monthRerolveNum"]);
                    }
                },
            ],

            testTaskDatas: [],
            testTaskLoading: false,
            testTaskTableHeight: 250,
            testTaskTableSortKey: "unRerolvedDown",

            testTaskColumns: [
                {
                    title: '人员',
                    key: 'userName',
                },
                {
                    title: '待测试',
                    key: 'unRerolvedNum',
                    width: 70,
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["unRerolvedNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-待测试";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "unRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unRerolvedNum"]);
                    }
                },
                {
                    title: '待测试超期1周',
                    key: 'unRerolvedDelayNum',
                    width: 112,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["unRerolvedDelayNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-待测试超期1周";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "delayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unRerolvedDelayNum"]);
                    }
                },
                {
                    title: '已测试完成',
                    key: 'rerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["rerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-已测试完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "rerolved";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["rerolveNum"]);
                    }
                },
                {
                    title: '日测试完成',
                    key: 'dayRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["dayRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-日测试完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "dayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["dayRerolveNum"]);
                    }
                },
                {
                    title: '周测试完成',
                    key: 'weekRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["weekRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-周测试完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "weekRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["weekRerolveNum"]);
                    }
                },
                {
                    title: '月测试完成',
                    key: 'monthRerolveNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["monthRerolveNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isDevOrTestDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "待测试任务报表-月测试完成";
                                    sf.isExport = true;
                                    sf.nodeId = sf.testNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "monthRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["monthRerolveNum"]);
                    }
                },
            ],

            testCaseDatas: [],
            testCaseLoading: false,
            testCaseTableHeight: 250,
            testCaseTableSortKey: "unExecuteDown",

            isTestCaseDetail: false,

            testCaseColumns: [
                {
                    title: '测试人员',
                    key: 'testUser',
                },
                {
                    title: '已编写',
                    key: 'editNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["editNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-已编写";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "edit";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["editNum"]);
                    }
                },
                {
                    title: '待执行',
                    key: 'unExecuteNum',
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["unExecuteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-待执行";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "unExecute";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unExecuteNum"]);
                    }
                },
                {
                    title: '已执行',
                    key: 'executeNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["executeNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-已执行";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "execute";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["executeNum"]);
                    }
                },
                {
                    title: '已通过',
                    key: 'passNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["passNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-已通过";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "pass";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["passNum"]);
                    }
                },
                {
                    title: '执行通过率',
                    key: 'executePassRate',
                    render: function (h, params) {
                        var value = params.row.executePassRate;
                        if (!!value) {
                            return h('div', value + '%');
                        }
                        return h('div', '0');
                    }
                },
                {
                    title: '日执行',
                    key: 'dayExecuteNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["dayExecuteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-日执行";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "dayExecute";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["dayExecuteNum"]);
                    }
                },
                {
                    title: '周执行',
                    key: 'weekExecuteNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["weekExecuteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-周执行";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "weekExecute";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["weekExecuteNum"]);
                    }
                },
                {
                    title: '月执行',
                    key: 'monthExecuteNum',
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["monthExecuteNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestCaseDetail = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试用例报表-月执行";
                                    sf.isExport = true;
                                    sf.userId = params.row["testUserId"];
                                    sf.searchType = "monthExecute";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["monthExecuteNum"]);
                    }
                },
            ],

            devNodeId: "5ae2cca8900add501a141178", //开发结点id
            testNodeId: "5ae2cccb900add501a141179", //测试结点id

            devUnRepairedDefectDatas: [],
            devUnRepairedDefectLoading: false,
            devUnRepairedDefectTableHeight: 250,
            devUnRepairedDefectTableSortKey: "unRepairDown",

            testUnVerifyDefectDatas: [],
            testUnVerifyDefectLoading: false,
            testUnVerifyDefectTableHeight: 250,
            testUnVerifyDefectTableSortKey: "unVerifyDown",

            detailTitle: "",
            detailModal: false,

            detailTableTotal: 0,
            detailPageIndex: 0,
            detailPageSize: 10,
            detailPageSizeOpts: [10, 20, 50],

            detailTableHeight: 250,
            detailTableLoading: false,
            detailTableDatas: [],
            detailColumns: [],

            verifyNodeId: "5bf01baee0ee7758f342be77", //验证结点id
            repairNodeId: "5bf01c90e0ee7758f342beb2", //修复结点id
            nodeId: "",
            userId: "",
            searchType: "",

            isTestUnVerifyOrDevUnRepairedDetail: false,

            testUnVerifyDefectColumns: [
                {
                    title: '测试人员',
                    key: 'tester',
                },
                {
                    title: '待验证',
                    key: 'unVerifyDefectNum',
                    width: 70,
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["unVerifyDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-待验证";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "unRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unVerifyDefectNum"]);
                    }
                },
                {
                    title: '超期高等级',
                    key: 'highLevelDelayVerifyDefectNum',
                    width: 112,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["highLevelDelayVerifyDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-超期高等级";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "delayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["highLevelDelayVerifyDefectNum"]);
                    }
                },
                {
                    title: '已验证',
                    key: 'verifiedDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["verifiedDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-已验证";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "rerolved";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["verifiedDefectNum"]);
                    }
                },
                {
                    title: '日验证',
                    key: 'dayVerifyDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["dayVerifyDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-日验证";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "dayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["dayVerifyDefectNum"]);
                    }
                },
                {
                    title: '周验证',
                    key: 'weekVerifyDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["weekVerifyDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-周验证";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "weekRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["weekVerifyDefectNum"]);
                    }
                },
                {
                    title: '月验证',
                    key: 'monthVerifyDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["monthVerifyDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "测试待验证缺陷报表-月验证";
                                    sf.nodeId = sf.verifyNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "monthRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["monthVerifyDefectNum"]);
                    }
                },
            ],

            devUnRepairedDefectColumns: [
                {
                    title: '开发人员',
                    key: 'developer',
                },
                {
                    title: '待修复',
                    key: 'unRepairDefectNum',
                    width: 70,
                    sortable: 'custom',
                    sortType: "desc",
                    render: function (h, params) {
                        if (!params.row["unRepairDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-待修复";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "unRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["unRepairDefectNum"]);
                    }
                },
                {
                    title: '超期高等级',
                    key: 'highLevelDelayRepairDefectNum',
                    width: 112,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["highLevelDelayRepairDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-超期高等级";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "delayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["highLevelDelayRepairDefectNum"]);
                    }
                },
                {
                    title: '已修复',
                    key: 'repairedDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["repairedDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-已修复";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "rerolved";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["repairedDefectNum"]);
                    }
                },
                {
                    title: '日修复',
                    key: 'dayRepairDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["dayRepairDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-日修复";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "dayRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["dayRepairDefectNum"]);
                    }
                },
                {
                    title: '周修复',
                    key: 'weekRepairDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["weekRepairDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-周修复";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "weekRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["weekRepairDefectNum"]);
                    }
                },
                {
                    title: '月修复',
                    key: 'monthRepairDefectNum',
                    width: 70,
                    sortable: 'custom',
                    render: function (h, params) {
                        if (!params.row["monthRepairDefectNum"]) {
                            return h('div', 0);
                        }
                        return h('a', {
                            on: {
                                click: function () {
                                    sf.isTestUnVerifyOrDevUnRepairedDetail = true;
                                    sf.isExport = true;
                                    sf.genrateDetailTableColumns();
                                    sf.detailTitle = "开发待修复缺陷报表-月修复";
                                    sf.nodeId = sf.repairNodeId;
                                    sf.userId = params.row["userId"];
                                    sf.searchType = "monthRerolve";
                                    sf.queryDefectDetailTable();
                                    sf.detailModal = true;
                                },
                            }
                        }, params.row["monthRepairDefectNum"]);
                    }
                },
            ],

            dateOptions: {
                disabledDate(date) {
                    if (!!sf.startDate && !!sf.endDate) {
                        if (new Date(date).getTime() < new Date(sf.startDate).getTime()
                            || new Date(date).getTime() > new Date(sf.endDate).getTime()) {
                            return true;
                        }
                    }
                    return false;
                }
            },

            selectPrjId: null,
            selectPrjName: "",
            selectDatePeriod: [],
            searchPrjList: [],

            devReqNum: 0,
            devReqDelayNum: 0,
            testReqNum: 0,
            testReqDelayNum: 0,

            repairNum: 0,
            verifyNum: 0,
            repairDelayNum: 0,
            verifyDelayNum: 0,

            selectedBtn: "detail",

            selectedNode: "devUser",
            devUserRoleId: "6114e83e952372e6ad1bdd89", //"开发人员"角色id
            testUserRoleId: "6114e7e2952372e6ad1b6b68", //"测试人员"角色id
            reqUserRoleId: "6114e861952372e6ad1c1730", //"需求人员"角色id

            prjPerformanceTableHeight: 270,
            prjPerformanceColumns: [],
            prjPerformanceDatas: [],
            prjPerformanceLoading: false,

            selectMeasureStartMonth: null,
            selectMeasureEndMonth: null,

            burnDownChartData: [],

            isShowHead: true,

            startDate: "",
            endDate: "",

            bugTrendChartData: [],
            reqTrendChartData: [],
            defectClassifyBySeverityVo: {},
            demandReviewCoverageRateData: [],

            circleIcon: `path://M881.387 297.813c38.08 65.387 57.28 136.747 57.28 214.187s-19.094 148.8-57.28 214.187c-38.187 65.28-89.92 117.12-155.2 155.2S589.44 938.667 512 938.667s-148.8-19.094-214.187-57.28c-65.28-38.08-117.013-89.814-155.306-155.307C104.427 660.8 85.333 589.44 85.333 512c0-77.333 19.094-148.693 57.28-214.187 38.08-65.28 89.814-117.013 155.307-155.306C363.2 104.533 434.56 85.333 512 85.333c77.333 0 148.693 19.094 214.187 57.28 65.28 38.187 117.013 89.92 155.2 155.2z m-217.707-47.36C617.387 223.467 566.827 209.92 512 209.92s-105.387 13.547-151.68 40.533-82.987 63.68-109.973 109.974c-26.987 46.293-40.534 96.853-40.534 151.68s13.547 105.386 40.534 151.68c26.986 46.293 63.68 82.986 109.973 109.973 46.293 26.987 96.853 40.533 151.68 40.533s105.387-13.546 151.68-40.533c46.293-26.987 82.987-63.68 109.973-109.973 26.987-46.294 40.534-96.854 40.534-151.68s-13.547-105.387-40.534-151.68c-27.093-46.294-63.786-82.987-109.973-109.974z`,

            devTaskDefectDensityFirstTimeData: [],
            storeTaskRankByUserDataVo: {},
            defectRankByUserDataVo: {},
            taskPromptnessData: [],

            provList: [],
            provTreeData: [],
            provFltList: [],
            prjMgtTypeList: [],
            prjStatusList: [],

            selectProv: null,
            selectPrjMgtTypes: [],
            selectPrjStatuses: [],
            selectYear: null,
            sbuId: "",
            bugTimelinessRateData: [],

            queryPrjAjax: null,
            prjProgQualReportVo: {},

            echartsStoreDevTaskHeight: 258,
            echartsStoreTestTaskHeight: 258,
            echartsRepairDefectHeight: 258,
            echartsVerifyDefectHeight: 258,

            bugFallbackRateDatas: [],

            param: {},

            msmtSysName: "BSC_项目型",

            prjPerformanceTablePageNum: 0,
            prjPerformanceTablePageSize: 100,
            prjPerformanceTablePageSizeOpts:[20, 50, 100],
            prjPerformanceTableDataTotal: 0,

            tabName: 'prjNoticeBoard',
            selectedTypeBtn: "month",
            isRankAsc: true,

            assessTotalTableHeight: 80,
            assessTotalColumns: [
                /*{
                    title: '操作',
                    align: 'center',
                    key: 'Radio',
                    width: 50,
                    render: function(h, params) {
                        return h('div', [
                            h('Radio', {
                                props: {
                                    'value': params.row['Radio'],
                                },
                                on: {
                                    'on-change': function(e) {
                                        sf.assessTotalDatas.forEach(function(item){
                                            sf.$set(item, 'Radio', false);
                                        });
                                        sf.assessTotalDatas[params.index]['Radio'] = true;
                                        sf.radioPrj = sf.assessTotalDatas[params.index]['prjId'];
                                        sf.radioYm = sf.assessTotalDatas[params.index]['ym'];
                                        sf.queryPrjDigitalDetail();
                                    }
                                }
                            })
                        ]);
                    },
                },*/
                {
                    title: '评估月份',
                    key: "ym",
                    width: 70,
                },
                {
                    title: '项目集编码',
                    key: "prjCode",
                    render: function (h, params) {
                        var name = params.row.prjCode;
                        if (name) {
                            return h('Tooltip', {
                                props: {placement: 'top', transfer: true}
                            }, [
                                name,
                                h('span', {
                                        slot: 'content',
                                        style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                    },
                                    name)
                            ]);
                        }
                        return null;
                    },
                },
                {
                    title: '项目集名称',
                    key: "prjName",
                    render: function (h, params) {
                        var name = params.row.prjName;
                        if (name) {
                            return h('Tooltip', {
                                props: {placement: 'top', transfer: true}
                            }, [
                                name,
                                h('span', {
                                        slot: 'content',
                                        style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                    },
                                    name)
                            ]);
                        }
                        return null;
                    },
                },
                {
                    title: '项目等级',
                    key: "prjLevel",
                    width: 70,
                },
                {
                    title: '项目集状态',
                    key: "status",
                    width: 90,
                },
                {
                    title: '数字化工具类型',
                    key: "digitalToolType",
                    width: 110,
                },
                {
                    title: '启用月份',
                    key: "digitalEvalStartYm",
                    width: 70,
                },
                {
                    title: '结束月份',
                    key: "digitalEvalEndYm",
                    width: 70,
                },
                {
                    title: '指标合格数量',
                    key: "qualifiedNum",
                    width: 90,
                },
                {
                    title: '指标不合格数量',
                    key: "notQualifiedNum",
                    width: 110,
                },
                {
                    title: '指标NA数量',
                    key: "naNum",
                    width: 90,
                },
                {
                    title: '评估结果',
                    key: "qualifiedStatus",
                    width: 80,
                },
            ],
            assessTotalDatas: [],
            assessTotalLoading: false,

            assessDetailTableHeight: 200,
            assessDetailColumns: [
                /*{
                    title: '操作',
                    align: 'center',
                    key: 'Radio',
                    width: 50,
                    render: function(h, params) {
                        return h('div', [
                            h('Radio', {
                                props: {
                                    'value': params.row['Radio'],
                                },
                                on: {
                                    'on-change': function(e) {
                                        sf.assessDetailDatas.forEach(function(item){
                                            sf.$set(item, 'Radio', false);
                                        });
                                        sf.assessDetailDatas[params.index]['Radio'] = true;
                                        sf.radioOtherPrj = sf.assessDetailDatas[params.index]['prjId'];
                                        sf.radioOtherYm = sf.assessDetailDatas[params.index]['ym'];
                                        sf.assessIndexName = sf.assessDetailDatas[params.index]['indexName'];
                                        sf.generateAssessIndexTableColumns();
                                        sf.assessIndexTablePageNum = 0;
                                        sf.assessIndexTablePageSize = 100;
                                        sf.queryParam = {};
                                        sf.queryPrjDigitalIndexDetail();
                                    }
                                }
                            })
                        ]);
                    },
                },*/
                {
                    title: '评估月份',
                    key: "ym",
                    width: 80,
                },
                {
                    title: '指标名称',
                    key: "indexName",
                    width: 180,
                },
                {
                    title: '指标合格标志',
                    key: "qualifiedStatus",
                    width: 90,
                },
                {
                    title: '指标阈值',
                    key: "indexThreshold",
                    width: 90,
                },
                {
                    title: '项目指标值',
                    key: "indexValue",
                    width: 90,
                },
                {
                    title: '分子',
                    key: "molecule",
                    width: 90,
                },
                {
                    title: '分母',
                    key: "denominator",
                    width: 90,
                },
                {
                    title: '指标计算说明',
                    key: "indexDesc",
                    render: function (h, params) {
                        var name = params.row.indexDesc;
                        if (name) {
                            return h('Tooltip', {
                                props: {placement: 'top', transfer: true}
                            }, [
                                name,
                                h('span', {
                                        slot: 'content',
                                        style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                    },
                                    name)
                            ]);
                        }
                        return null;
                    },
                },
                /*{
                    title: '改进建议',
                    key: "indexSuggestion",
                    render: function (h, params) {
                        var name = params.row.indexSuggestion;
                        if (name) {
                            return h('Tooltip', {
                                props: {placement: 'top', transfer: true}
                            }, [
                                name,
                                h('span', {
                                        slot: 'content',
                                        style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                    },
                                    name)
                            ]);
                        }
                        return null;
                    },
                },*/
                {
                    title: '数据时间',
                    key: "dataInitTime",
                    width: 160,
                },
            ],
            assessDetailDatas: [],
            assessDetailLoading: false,

            assessIndexTableHeight: 2500,
            assessIndexColumns: [],
            assessIndexDatas: [],
            assessIndexLoading: false,

            assessIndexTablePageNum: 0,
            assessIndexTablePageSize: 100,
            assessIndexTablePageSizeOpts:[20, 50, 100],
            assessIndexTableDataTotal: 0,

            selectMeasureMonth: null,
            assessIndexName: "",

            radioPrj: "",
            radioYm: "",

            radioOtherPrj: "",
            radioOtherYm: "",
            msmtTypeId: "67ecd328fb03e0e173959087", //"项目工具使用评判型"PFM度量体系类型
            msmtSysList: [],
            selectMsmtSysId: "",

            isPrjEffectEvalutionAdmin: false,
            buId: "5a70111329974f7cabe9115d",

            queryPrjLoading: false,

            queryParam: {},
        };
    },

    created: function () {
        var sf = this;
        if (!Vue.evtHub) {
            Vue.evtHub = new Vue();
        }
        sf.checkIsPrjEffectEvalutionAdmin();
        sf.listPfmMsmt();
        sf.selectPrjStatuses = ["5dc3c234c56a424c33470467", "5dc3c1f4c56a424c33470466", "5dc3c246c56a424c33470468", "5dc3c256c56a424c33470469",
                                "5dc3c266c56a424c3347046a", "5dc3c290c56a424c3347046b", "5dc3c2b3c56a424c3347046c"];

        sf.selectMeasureStartMonth = new Date(sf.getUpMonth());
        sf.selectMeasureEndMonth = new Date(sf.getUpMonth());
        sf.selectMeasureMonth = new Date(sf.getUpMonth());

        var urlName = sf.getQueryString("from");
        if (urlName == "bscPrjEffectEvaluationNew") {
            sf.isShowHead = false;
        }

        var tabName = sf.getQueryString("tabName");
        if(!!tabName) {
            sf.tabName = tabName;
        }

        var condParam = sf.getQueryString("condParam");
        if (!!condParam && condParam != "{}") {
            var param = JSON.parse(condParam);
            sf.param = param;
            if (!!param.selectProvIds && param.selectProvIds.length > 0) {
                sf.provFltList = param.selectProvIds;
                sf.selectProv = sf.provFltList[sf.provFltList.length - 1];
            }
            if (!!param.selectPrjMgtTypeIds && param.selectPrjMgtTypeIds.length > 0) {
                sf.selectPrjMgtTypes = param.selectPrjMgtTypeIds;
            }
            if (!!param.selectPrjStatusIds && param.selectPrjStatusIds.length > 0) {
                sf.selectPrjStatuses = param.selectPrjStatusIds;
            }else {
                sf.selectPrjStatuses = [];
            }
            if (!!param.selectYear) {
                sf.selectYear = new Date(param.selectYear);
            }
            if (!!param.selectMeasureMonth) {
                sf.selectMeasureMonth = new Date(param.selectMeasureMonth);
            }
            if (!!param.selectPrjId) {
                sf.selectPrjId = param.selectPrjId;
            }
            if (!!param.selectDatePeriod && param.selectDatePeriod.length == 2 && !!param.selectDatePeriod[0]
                && !!param.selectDatePeriod[1]) {
                sf.selectDatePeriod = [];
                sf.selectDatePeriod.push(new Date(param.selectDatePeriod[0]));
                sf.selectDatePeriod.push(new Date(param.selectDatePeriod[1]));
            }
            if(!!param.selectPrjId && !!param.selectDatePeriod && param.selectDatePeriod.length == 2 && !!param.selectDatePeriod[0]
                && !!param.selectDatePeriod[1]) {
                sf.getPrjProgQualReport();
            }
            if(sf.tabName == "digitalProductionEvaluation") {
                sf.queryPrjDigitalInfo();
            }
        }

        /*sf.getPrjPerformanceList();*/
        /*if(!sf.selectYear) {
            sf.selectYear = new Date();
        }*/
        sf.loadCurrentUser();
        sf.listPrjStatus();
        sf.queryPrjInfo();
    },

    mounted: function () {
        var sf = this;
        sf.detailTableHeight = window.innerHeight - 504;
        sf.detailTableHeight = sf.detailTableHeight > 250 ? sf.detailTableHeight : 250;

        sf.prjPerformanceTableHeight = window.innerHeight - 234;
        sf.prjPerformanceTableHeight = sf.prjPerformanceTableHeight > 270 ? sf.prjPerformanceTableHeight : 270;

        window.addEventListener('message', function (e) {
            if (!!e.data && JSON.stringify(e.data) != "{}") {
                if (!!e.data.changeTab) {
                    sf.tabName = e.data.tabName;
                }
            }
        });

        //列表头筛选
        Vue.evtHub.$on("load-data", function(data) {
            if(data.fieldId === 'userName'){
                sf.queryParam[data.fieldId] = data.value;
            }else if(data.fieldId === 'ym'){
                sf.queryParam[data.fieldId] = data.value;
            }else if(!!data["type"]) {
                var filterField = data.fieldId + 'Sign';
                var filterValue = "";
                if(data.value[0] == "greaterOperator") {
                    filterValue = ">";
                }else if(data.value[0] == "equalOperator") {
                    filterValue = "=";
                }else if(data.value[0] == "lessOperator") {
                    filterValue = "<";
                }
                sf.queryParam[filterField] = filterValue;
                if(data["type"] == "num") {
                    sf.queryParam[data.fieldId] = !!data.value[1] ? parseFloat(data.value[1]) : null;
                }
                if(!sf.queryParam[data.fieldId] && sf.queryParam[data.fieldId] != 0) {
                    if(!!sf.queryParam[filterField]) {
                        delete sf.queryParam[filterField];
                    }
                    return;
                }
            }else {
                sf.queryParam[data.fieldId] = data.value;
            }
            sf.assessIndexTablePageNum = 0;
            sf.queryPrjDigitalIndexDetail();
        });
    },

    methods: {

        //单击项目集评估总结果表格行
        rowClickAssessTotalTable: function(row) {
            var sf = this;
            sf.radioPrj = row['prjId'];
            sf.radioYm = row['ym'];
            sf.queryPrjDigitalDetail();
        },

        //单击评估结果明细表格行
        rowClickAssessDetailTable: function(row) {
            var sf = this;
            sf.radioOtherPrj = row['prjId'];
            sf.radioOtherYm = row['ym'];
            sf.assessIndexName = row['indexName'];
            sf.generateAssessIndexTableColumns();
            sf.assessIndexTablePageNum = 0;
            sf.assessIndexTablePageSize = 100;
            sf.queryParam = {};
            sf.queryPrjDigitalIndexDetail();
        },


        //选择度量月份
        changeMeasureMonth: function() {
            var sf = this;
            if (!sf.selectMeasureMonth) {
                return;
            }
            sf.queryPrjDigitalInfo();
        },

        //选择度量体系
        changeMsmtSys: function() {
            var sf = this;
            if (!sf.selectMsmtSysId) {
                return;
            }
            sf.queryPrjDigitalInfo();
        },
        //查询度量体系下拉选项
        listPfmMsmt: function() {
            var sf = this;
            sf.msmtSysList = [];
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/listPfmMsmt',
                method: 'get',
                data: {
                    msmtTypeId: sf.msmtTypeId,
                },
                success: function(res) {
                    if(!!res.success) {
                        sf.msmtSysList = res.data || [];
                    }
                },
                error: function(res) {
                    sf.$Message.error({
                        content: "查询度量体系下拉选项失败，请联系管理员！",
                        duration: 3
                    });
                }
            });
        },

        //导出项目数字化生产-type为“single”时导出单个，type为“all”时导出全部
        exportPrjDigitalInfo: function(type) {
            var sf = this;
            if(!sf.selectMeasureMonth) {
                sf.$Message.warning({
                    content: "度量月份不能为空！",
                    duration: 3
                })
                return;
            }
            var param = {
                ym: new Date(sf.selectMeasureMonth).format("yyyy-MM"),
            };
            if(type == "single") {
                if (!sf.selectPrjId) {
                    sf.$Message.info({
                        content: "请先选择项目集！",
                        duration: 8
                    });
                    return;
                }
                param["prjId"] = sf.selectPrjId.split('-')[0];
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var xhr = new XMLHttpRequest();
            var url = linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/exportPrjDigitalInfo';
            xhr.open('POST', url, true);
            xhr.responseType = 'blob'; // 设置响应类型为 Blob
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(JSON.stringify(param));
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    var blob = xhr.response; // 获取 Blob 数据
                    sf.blobDataExport(blob, '项目数字化生产.xlsx');
                } else {
                    sf.$Message.error({
                        content: "导出失败，请联系管理员！",
                        duration: 8
                    });
                }
            };
            xhr.onerror = function() {
                sf.$Message.error({
                    content: "导出失败，请联系管理员！",
                    duration: 8
                });
            };
        },

        //查询项目集评估总结果列表
        queryPrjDigitalInfo: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            /*if (!sf.selectMsmtSysId) {
                sf.$Message.info({
                    content: "请先选择度量体系！",
                    duration: 8
                });
                return;
            }*/
            if(!sf.selectMeasureMonth) {
                sf.$Message.warning({
                    content: "度量月份不能为空！",
                    duration: 3
                })
                return;
            }
            sf.assessTotalDatas = [];
            sf.assessDetailDatas = [];
            sf.assessIndexDatas = [];
            sf.assessIndexTableDataTotal = 0;
            sf.assessIndexTablePageNum = 0;
            sf.assessTotalLoading = true;
            var param = {
                /*msmtSysId: sf.selectMsmtSysId,*/
                prjIds: !!sf.selectPrjId ? [sf.selectPrjId.split('-')[0]] : [],
                yms: !!sf.selectMeasureMonth ? [new Date(sf.selectMeasureMonth).format("yyyy-MM")] : [],
            };
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/queryPrjDigitalInfo',
                headers: {'Content-Type' : 'application/json;charset=utf-8'},
                method: 'post',
                data: JSON.stringify(param),
                success: function (res) {
                    sf.assessTotalLoading = false;
                    if(res.success) {
                        sf.assessTotalDatas = res.data || [];
                    }else{
                        sf.$Message.error({
                            content: res.message || "查询项目集评估总结果列表失败，请联系管理员！",
                            duration: 3
                        })
                    }
                },
                error: function(res) {
                    sf.assessTotalLoading = false;
                    sf.$Message.error({
                        content: "查询项目集评估总结果列表失败，请联系管理员！",
                        duration: 3
                    });
                }
            });
        },

        //查询评估结果明细列表
        queryPrjDigitalDetail: function() {
            var sf = this;
            /*if (!sf.selectMsmtSysId) {
                sf.$Message.info({
                    content: "请先选择度量体系！",
                    duration: 8
                });
                return;
            }*/
            var param = {
                /*msmtSysId: sf.selectMsmtSysId,*/
                prjId: sf.radioPrj,
                ym: sf.radioYm,
            };
            sf.assessDetailDatas = [];
            sf.assessDetailLoading = true;
            sf.assessDetailTableHeight = 200;
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/queryPrjDigitalDetail',
                headers: {'Content-Type' : 'application/json;charset=utf-8'},
                method: 'post',
                data: JSON.stringify(param),
                success: function (res) {
                    sf.assessDetailLoading = false;
                    if(res.success) {
                        sf.assessDetailDatas = res.data || [];
                    }else{
                        sf.$Message.error({
                            content: res.message || "查询评估结果明细列表失败，请联系管理员！",
                            duration: 3
                        })
                    }
                    if(sf.assessDetailDatas.length > 4) {
                        sf.assessDetailTableHeight = 285;
                    }else {
                        sf.assessDetailTableHeight = 200;
                    }
                },
                error: function(res) {
                    sf.assessDetailLoading = false;
                    sf.$Message.error({
                        content: "查询评估结果明细列表失败，请联系管理员！",
                        duration: 3
                    });
                }
            });
        },

        //指标明细列表翻页
        onAssessIndexTablePageNumChange: function(pageNum) {
            var sf = this;
            sf.assessIndexTablePageNum = pageNum - 1;
            sf.queryPrjDigitalIndexDetail();
        },

        //指标明细列表变更每页展示条数
        onAssessIndexTablePageSizeChange: function(pageSize) {
            var sf = this;
            sf.assessIndexTablePageNum = 0;
            sf.assessIndexTablePageSize = pageSize;
            sf.queryPrjDigitalIndexDetail();
        },

        //查询指标明细列表
        queryPrjDigitalIndexDetail: function() {
            var sf = this;
            var param = JSON.parse(JSON.stringify(sf.queryParam));
            param["prjId"] = sf.radioOtherPrj;
            param["pageNum"] = sf.assessIndexTablePageNum;
            param["pageSize"] = sf.assessIndexTablePageSize;
            if(!!sf.queryParam["ym"]) {
                param["ym"] = sf.queryParam["ym"];
                param["evalStartYm"] = sf.queryParam["ym"];
            }else {
                param["ym"] = sf.radioOtherYm;
                if(sf.assessIndexName == "当月人员工作量合理性" || sf.assessIndexName == "当月人员使用工具合理性") {
                    param["evalStartYm"] = sf.radioOtherYm;
                }
            }
            if(sf.assessIndexName == "当月人员工作量合理性" || sf.assessIndexName == "全周期人员工作量合理性") {
                param["indexType"] = "人员工作量合理性";
            }
            sf.assessIndexDatas = [];
            sf.assessIndexTableDataTotal = 0;
            sf.assessIndexLoading = true;
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/queryPrjDigitalIndexDetail',
                headers: {'Content-Type' : 'application/json;charset=utf-8'},
                method: 'post',
                data: JSON.stringify(param),
                success: function (res) {
                    sf.assessIndexLoading = false;
                    if(res.success) {
                        var pageBean = {};
                        if(sf.assessIndexName == "全周期任务单数量合理性") {
                            pageBean = (!!res.data.taskNumPageBean && JSON.stringify(res.data.taskNumPageBean) != "{}") ? res.data.taskNumPageBean : {};
                        }else if(sf.assessIndexName == "全周期人员使用工具合理性" || sf.assessIndexName == "当月人员使用工具合理性") {
                            pageBean = (!!res.data.useToolPageBean && JSON.stringify(res.data.useToolPageBean) != "{}") ? res.data.useToolPageBean : {};
                        }else if(sf.assessIndexName == "全周期人员工作量合理性" || sf.assessIndexName == "当月人员工作量合理性") {
                            pageBean = (!!res.data.workloadPageBean && JSON.stringify(res.data.workloadPageBean) != "{}") ? res.data.workloadPageBean : {};
                        }else if(sf.assessIndexName == "全周期任务单缺陷密度合理性") {
                            pageBean = (!!res.data.taskBugRatioPageBean && JSON.stringify(res.data.taskBugRatioPageBean) != "{}") ? res.data.taskBugRatioPageBean : {};
                        }
                        sf.assessIndexDatas = (!!pageBean.objectList && pageBean.objectList.length > 0) ? pageBean.objectList : [];
                        sf.assessIndexTableDataTotal = !!pageBean.count ? pageBean.count : 0;
                    }else{
                        sf.$Message.error({
                            content: res.message || "查询指标明细列表失败，请联系管理员！",
                            duration: 3
                        })
                    }
                },
                error: function(res) {
                    sf.assessIndexLoading = false;
                    sf.$Message.error({
                        content: "查询指标明细列表失败，请联系管理员！",
                        duration: 3
                    });
                }
            });
        },

        //设置行样式
        assessIndexRowClassName: function(row, index) {
            var sf = this;
            if(sf.assessIndexName == "全周期人员使用工具合理性" || sf.assessIndexName == "当月人员使用工具合理性") {
                if(!!row["manToolTmRatio"] && parseFloat(row["manToolTmRatio"]) == 0) {
                    return 'demo-table-red-row';
                }
            }else if(sf.assessIndexName == "全周期人员工作量合理性" || sf.assessIndexName == "当月人员工作量合理性") {
                if(!!row["workloadRatio"] && parseFloat(row["workloadRatio"]) < 0.4) {
                    return 'demo-table-red-row';
                }
            }
            return '';
        },

        //组装指标明细表格列
        generateAssessIndexTableColumns: function() {
            var sf = this;
            sf.assessIndexColumns = [];
            if(sf.assessIndexName == "全周期任务单数量合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                        filter: {
                            type: "DatePicker",
                            datePickerType: "month",
                        },
                    },
                    {
                        title: '完成任务单总数',
                        key: "completeTaskNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '工时人月数',
                        key: "mm",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '任务工时比',
                        key: "taskNumTmRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }else if(sf.assessIndexName == "全周期人员使用工具合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                        filter: {
                            type: "DatePicker",
                            datePickerType: "month",
                        },
                    },
                    {
                        title: '完成任务单总数',
                        key: "completeTaskNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '月份工时天数',
                        key: "md",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '任务工时比',
                        key: "manToolTmRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }else if(sf.assessIndexName == "当月人员使用工具合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                    },
                    {
                        title: '完成任务单总数',
                        key: "completeTaskNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '月份工时天数',
                        key: "md",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '任务工时比',
                        key: "manToolTmRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }else if(sf.assessIndexName == "全周期人员工作量合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                        filter: {
                            type: "DatePicker",
                            datePickerType: "month",
                        },
                    },
                    {
                        title: '总工作量（人天）',
                        key: "totalWorkload",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '月份工时天数',
                        key: "md",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '工作饱和度',
                        key: "workloadRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }else if(sf.assessIndexName == "当月人员工作量合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                    },
                    {
                        title: '总工作量（人天）',
                        key: "totalWorkload",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '月份工时天数',
                        key: "md",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '工作饱和度',
                        key: "workloadRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }else if(sf.assessIndexName == "全周期任务单缺陷密度合理性") {
                sf.assessIndexColumns = [
                    {
                        type: 'index',
                        title: '序',
                        width: 60,
                        className: 'index-table-info-column',
                    },
                    {
                        title: '项目集编码',
                        key: "prjCode",
                        render: function (h, params) {
                            var name = params.row.prjCode;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '项目集名称',
                        key: "prjName",
                        render: function (h, params) {
                            var name = params.row.prjName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {
                                            slot: 'content',
                                            style: {whiteSpace: 'normal', wordBreak: 'break-all'}
                                        },
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '姓名/NT',
                        key: "userName",
                        width: 130,
                        filter: {
                            type: "Input",
                        },
                    },
                    {
                        title: '任务月份',
                        key: "ym",
                        width: 110,
                        filter: {
                            type: "DatePicker",
                            datePickerType: "month",
                        },
                    },
                    {
                        title: '新增缺陷数',
                        key: "newBugNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '完成开发任务数量',
                        key: "completeDevTaskNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '完成测试任务数',
                        key: "completeTestTaskNum",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                    {
                        title: '任务单缺陷密度',
                        key: "taskBugRatio",
                        width: 200,
                        filter: {
                            type: "InputWithOperator",
                            defaultOperatorValue: 'equalOperator',
                        },
                    },
                ];
            }
        },

        //获取上个月份
        getUpMonth: function() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();

            var year2 = year;
            // 注意：1月份返回的是0
            var month2 = parseInt(month);
            if (month2 == 0) {
                year2 = parseInt(year2) - 1;
                month2 = 12;
            }

            if (month2 < 10) {
                month2 = '0' + month2;
            }
            var m = year2.toString();
            var n = month2.toString();
            var t2 = m + "-" + n;
            // 最终返回格式2022-12
            return t2;
        },

        /*查询进度和质量总结接口*/
        getPrjProgQualReport: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 3
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.prjProgQualReportVo = {};
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getPrjProgQualReport",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success && !!data.data && JSON.stringify(data.data) != "{}") {
                        sf.prjProgQualReportVo = data.data;
                        sf.prjProgQualReportVo["codeChangeLineNum"] = parseInt(sf.prjProgQualReportVo.codeChangeLineNum || 0).toLocaleString();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询项目进度和质量总结报告错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },


        /*打开查看总结报告弹窗*/
        openSummaryModal: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 3
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.summaryModalTitle = sf.selectPrjName + "-项目进度和质量总结";
            sf.isSummaryModal = true;
        },

        /*关闭查看总结报告弹窗*/
        closeSummaryModal: function() {
            var sf = this;
            sf.isSummaryModal = false;
        },

        /*复制总结*/
        copySummaryReport: function() {
            var sf = this;
            var clipboard = new ClipboardJS('.copySummaryBtn');
            clipboard.on('success', function (e) {
                sf.$Message.success({
                    content: "已复制进剪贴板！",
                    duration: 3
                });
            });
            clipboard.on('error', function (e) {
                sf.$Message.error({
                    content: "复制失败！",
                    duration: 3
                });
            });
        },

        /*组装详情报表列*/
        genrateDetailTableColumns: function () {
            var sf = this;
            sf.detailColumns = [];
            if (!!sf.isDevOrTestDetail || !!sf.isTestUnVerifyOrDevUnRepairedDetail) {
                sf.detailColumns = [
                    {
                        title: '序',
                        type: 'index',
                        width: 60,
                    },
                    {
                        title: '业务编码',
                        key: 'bizCode',
                        width: 200,
                        render: function (h, params) {
                            var bizId = params.row["bizId"];
                            var url = linkus.location.prjbiz + "/forward.action?t=biz/prdBizView&bizId=" + bizId;
                            return h('a', {
                                attrs: {
                                    href: url,
                                    target: '_blank',
                                    rel: 'noopener'
                                }
                            }, params.row["bizCode"]);
                        }
                    },
                    {
                        title: '业务类型',
                        key: 'bizType',
                        width: 100,
                    },
                    {
                        title: '业务名称',
                        key: 'bizName',
                        width: 200,
                        render: function (h, params) {
                            var name = params.row.bizName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}},
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '提出人',
                        key: 'addUser',
                        width: 100,
                    },
                    {
                        title: '提出时间',
                        key: 'addTime',
                        width: 100,
                    },
                    ...((!!sf.isTestUnVerifyOrDevUnRepairedDetail)
                    ?[{
                        title: '严重程度',
                        key: 'severity',
                        width: 80,
                    }]:[]),
                    {
                        title: '业务状态',
                        key: 'status',
                        width: 100,
                    },
                    {
                        title: '当前责任人',
                        key: 'currentResp',
                        width: 100,
                    },
                    {
                        title: '计划完成时间',
                        key: 'planEndDate',
                        width: 100,
                    },
                ];
            } else if (!!sf.isTaskCompletionDetail) {
                sf.detailColumns = [
                    {
                        title: '序',
                        type: 'index',
                        width: 60,
                    },
                    {
                        title: '业务编码',
                        key: 'code',
                        width: 200,
                        render: function (h, params) {
                            var bizId = params.row["bizId"];
                            var url = linkus.location.prjbiz + "/forward.action?t=biz/prdBizView&bizId=" + bizId;
                            return h('a', {
                                attrs: {
                                    href: url,
                                    target: '_blank',
                                    rel: 'noopener'
                                }
                            }, params.row["code"]);
                        }
                    },
                    {
                        title: '业务名称',
                        key: 'name',
                        width: 200,
                        render: function (h, params) {
                            var name = params.row.name;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}},
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '业务状态',
                        key: 'status',
                        width: 100,
                    },
                    {
                        title: '提出人',
                        key: 'addUser',
                        width: 100,
                    },
                    {
                        title: '提出时间',
                        key: 'addTime',
                        width: 100,
                    },
                    {
                        title: '处理人员',
                        key: 'processPerson',
                        width: 100,
                    },
                    {
                        title: '处理计划完成日期',
                        key: 'processPlanCompleteDate',
                        width: 120,
                    },
                    {
                        title: '处理实际完成日期',
                        key: 'processActualCompleteDate',
                        width: 120,
                    },
                    {
                        title: '审批人员',
                        key: 'verifyPerson',
                        width: 100,
                    },
                    {
                        title: '审批计划完成日期',
                        key: 'verifyPlanCompleteDate',
                        width: 120,
                    },
                    {
                        title: '审批实际完成日期',
                        key: 'verifyActualCompleteDate',
                        width: 120,
                    },
                ];
            }else if(!!sf.isTestCaseDetail) {
                sf.detailColumns = [
                    {
                        title: '序',
                        type: 'index',
                        width: 60,
                    },
                    {
                        title: '业务编码',
                        key: 'bizCode',
                        width: 200,
                        render: function (h, params) {
                            var bizId = params.row["bizId"];
                            var url = linkus.location.prjbiz + "/forward.action?t=biz/prdBizView&bizId=" + bizId;
                            return h('a', {
                                attrs: {
                                    href: url,
                                    target: '_blank',
                                    rel: 'noopener'
                                }
                            }, params.row["bizCode"]);
                        }
                    },
                    {
                        title: '业务名称',
                        key: 'bizName',
                        width: 200,
                        render: function (h, params) {
                            var name = params.row.bizName;
                            if (name) {
                                return h('Tooltip', {
                                    props: {placement: 'top', transfer: true}
                                }, [
                                    name,
                                    h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}},
                                        name)
                                ]);
                            }
                            return null;
                        },
                    },
                    {
                        title: '提出人',
                        key: 'addUser',
                        width: 100,
                    },
                    {
                        title: '用例编写人',
                        key: 'editor',
                        width: 100,
                    },
                    {
                        title: '提出时间',
                        key: 'addTime',
                        width: 100,
                    },
                    {
                        title: '用例责任人',
                        key: 'resp',
                        width: 120,
                    },
                    {
                        title: '用例执行人',
                        key: 'runUser',
                        width: 120,
                    },
                    {
                        title: '执行时间',
                        key: 'runTime',
                        width: 100,
                    },
                ];
            }
        },

        /*任务完成情况报表排序*/
        taskCompletionSortChange: function (column) {
            var sf = this;
            sf.taskCompletionTableSortKey = "";
            if (column.key == "taskNum") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "totalUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "totalDown";
                }
            } else if (column.key == "completedNum") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "completedUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "completedDown";
                }
            } else if (column.key == "uncompleteNum") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "unCompletedUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "unCompletedDown";
                }
            } else if (column.key == "completionRate") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "completeRateUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "completeRateDown";
                }
            } else if (column.key == "planCompleteNum") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "planCompleteUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "planCompleteDown";
                }
            } else if (column.key == "completedOnTimeNum") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "onTimeDown";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "onTimeDown";
                }
            } else if (column.key == "taskTimelinessRate") {
                if (column.order == "asc") {
                    sf.taskCompletionTableSortKey = "timelinessRateUp";
                } else if (column.order == "desc") {
                    sf.taskCompletionTableSortKey = "timelinessRateDown";
                }
            }
            sf.getTaskCompletionReport();
        },

        /*导出任务完成情况报表数据*/
        exportTaskCompletionReport: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportTaskCompletionReport";
            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            var params = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                sortKey: sf.taskCompletionTableSortKey,
            }
            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        /*查询任务完成情况报表数据*/
        getTaskCompletionReport: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.taskCompletionLoading = true;
            sf.taskCompletionDatas = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                sortKey: sf.taskCompletionTableSortKey,
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getTaskCompletionReport",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    sf.taskCompletionLoading = false;
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.taskCompletionDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.taskCompletionLoading = false;
                    sf.$Message.info({
                        content: "查询任务完成情况报表数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*导出待开发任务报表/待测试任务报表*/
        exportDevOrTaskList: function (type) {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportDemandReport";
            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            var params = {
                prjId: sf.selectPrjId.split('-')[0],
                sortKey: type == "dev" ? sf.devTaskTableSortKey : sf.testTaskTableSortKey,
                nodeId: type == "dev" ? sf.devNodeId : sf.testNodeId,
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        /*导出测试用例报表*/
        exportTestCaseTable: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportNewTestCaseReport";
            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            var params = {
                prjId: sf.selectPrjId.split('-')[0],
                sortKey: sf.testCaseTableSortKey,
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        /*查询待开发任务报表*/
        getDevTaskList: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.devTaskLoading = true;
            sf.devTaskDatas = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandReport",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                    sortKey: sf.devTaskTableSortKey,
                    nodeId: sf.devNodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                },
                dataType: "json",
                success: function (data) {
                    sf.devTaskLoading = false;
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.devTaskDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.devTaskLoading = false;
                    sf.$Message.info({
                        content: "查询待开发任务报表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*查询测试用例报表*/
        getTestCaseList: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.testCaseLoading = true;
            sf.testCaseDatas = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getNewTestCaseReport",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                    sortKey: sf.testCaseTableSortKey,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                },
                dataType: "json",
                success: function (data) {
                    sf.testCaseLoading = false;
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.testCaseDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.testCaseLoading = false;
                    sf.$Message.info({
                        content: "查询测试用例报表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*查询待测试任务报表*/
        getTestTaskList: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.testTaskLoading = true;
            sf.testTaskDatas = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandReport",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                    sortKey: sf.testTaskTableSortKey,
                    nodeId: sf.testNodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                },
                dataType: "json",
                success: function (data) {
                    sf.testTaskLoading = false;
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.testTaskDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.testTaskLoading = false;
                    sf.$Message.info({
                        content: "查询待测试任务报表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*待开发任务报表排序*/
        devTaskSortChange: function (column) {
            var sf = this;
            sf.devTaskTableSortKey = "";
            if (column.key == "unRerolvedNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "unRerolvedUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "unRerolvedDown";
                }
            } else if (column.key == "unRerolvedDelayNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "delayUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "delayDown";
                }
            } else if (column.key == "rerolveNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "rerolveUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "rerolveDown";
                }
            } else if (column.key == "dayRerolveNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "dayUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "dayDown";
                }
            } else if (column.key == "weekRerolveNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "weekUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "weekDown";
                }
            } else if (column.key == "monthRerolveNum") {
                if (column.order == "asc") {
                    sf.devTaskTableSortKey = "monthUp";
                } else if (column.order == "desc") {
                    sf.devTaskTableSortKey = "monthDown";
                }
            }
            sf.getDevTaskList();
        },

        /*待测试任务报表排序*/
        testTaskSortChange: function (column) {
            var sf = this;
            sf.testTaskTableSortKey = "";
            if (column.key == "unRerolvedNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "unRerolvedUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "unRerolvedDown";
                }
            } else if (column.key == "unRerolvedDelayNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "delayUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "delayDown";
                }
            } else if (column.key == "rerolveNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "rerolveUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "rerolveDown";
                }
            } else if (column.key == "dayRerolveNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "dayUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "dayDown";
                }
            } else if (column.key == "weekRerolveNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "weekUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "weekDown";
                }
            } else if (column.key == "monthRerolveNum") {
                if (column.order == "asc") {
                    sf.testTaskTableSortKey = "monthUp";
                } else if (column.order == "desc") {
                    sf.testTaskTableSortKey = "monthDown";
                }
            }
            sf.getTestTaskList();
        },

        /*测试用例报表排序*/
        testCaseSortChange: function (column) {
            var sf = this;
            sf.testCaseTableSortKey = "";
            if (column.key == "editNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "editUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "editDown";
                }
            } else if (column.key == "unExecuteNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "unExecuteUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "unExecuteDown";
                }
            } else if (column.key == "executeNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "executeUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "executeDown";
                }
            } else if (column.key == "passNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "passUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "passDown";
                }
            } else if (column.key == "dayExecuteNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "dayUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "dayDown";
                }
            } else if (column.key == "weekExecuteNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "weekUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "weekDown";
                }
            } else if (column.key == "monthExecuteNum") {
                if (column.order == "asc") {
                    sf.testCaseTableSortKey = "monthUp";
                } else if (column.order == "desc") {
                    sf.testCaseTableSortKey = "monthDown";
                }
            }
            sf.getTestCaseList();
        },

        /*关闭报表详情弹窗*/
        closeDetailModal: function () {
            var sf = this;
            sf.detailModal = false;
            sf.isExport = false;
            sf.isDevOrTestDetail = false;
            sf.isTestUnVerifyOrDevUnRepairedDetail = false;
            sf.isTaskCompletionDetail = false;
            sf.isTestCaseDetail = false;
            sf.detailPageIndex = 0;
            sf.detailPageSize = 10;
            sf.detailTableDatas = [];
        },

        /*切换报表详情页码*/
        deatilPageChange: function (pageIndex) {
            var sf = this;
            sf.detailPageIndex = pageIndex - 1;
            sf.queryDefectDetailTable();
        },

        /*切换报表详情每页条数*/
        detailPageSizeChange: function (pageSize) {
            var sf = this;
            sf.detailPageIndex = 0;
            sf.detailPageSize = pageSize;
            sf.queryDefectDetailTable();
        },

        /*导出报表详情数据*/
        exportDefectDetailTable: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!!sf.isTaskCompletionDetail || !!sf.isTestUnVerifyOrDevUnRepairedDetail || !!sf.isDevOrTestDetail || !!sf.isTestCaseDetail) {
                if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                    sf.$Message.info({
                        content: "请先选择周期时间！",
                        duration: 8
                    });
                    return;
                }
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = "";
            var params = {};
            if (!!sf.isDevOrTestDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportDemandReportInfo";
                params = {
                    prjId: sf.selectPrjId.split('-')[0],
                    searchType: sf.searchType,
                    userId: sf.userId,
                    nodeId: sf.nodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            } else if (!!sf.isTaskCompletionDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportTaskCompletionReportInfo";
                params = {
                    prjId: sf.selectPrjId.split('-')[0],
                    searchType: sf.searchType,
                    startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                    taskClassificationId: sf.taskClassificationId,
                };
            }else if(!!sf.isTestCaseDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportNewTestCaseReportInfo";
                params = {
                    prjId: sf.selectPrjId.split('-')[0],
                    userId: sf.userId,
                    searchType: sf.searchType,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            }else if(!!sf.isTestUnVerifyOrDevUnRepairedDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportDefectReportInfo";
                params = {
                    prjId: sf.selectPrjId.split('-')[0],
                    searchType: sf.searchType,
                    userId: sf.userId,
                    nodeId: sf.nodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            }

            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        /*查询报表详情数据*/
        queryDefectDetailTable: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!!sf.isTaskCompletionDetail || !!sf.isTestUnVerifyOrDevUnRepairedDetail || !!sf.isDevOrTestDetail || !!sf.isTestCaseDetail) {
                if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                    sf.$Message.info({
                        content: "请先选择周期时间！",
                        duration: 8
                    });
                    return;
                }
            }
            sf.detailTableTotal = 0;
            sf.detailTableLoading = true;
            sf.detailTableDatas = [];
            var vo = {};
            var url = "";
            if (!!sf.isDevOrTestDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandReportInfo";
                vo = {
                    prjId: sf.selectPrjId.split('-')[0],
                    pageNo: sf.detailPageIndex,
                    pageSize: sf.detailPageSize,
                    searchType: sf.searchType,
                    userId: sf.userId,
                    nodeId: sf.nodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            } else if (!!sf.isTestUnVerifyOrDevUnRepairedDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/getDefectReportInfo";
                vo = {
                    prjId: sf.selectPrjId.split('-')[0],
                    pageNo: sf.detailPageIndex,
                    pageSize: sf.detailPageSize,
                    searchType: sf.searchType,
                    userId: sf.userId,
                    nodeId: sf.nodeId,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            } else if (!!sf.isTaskCompletionDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/getTaskCompletionReportInfo";
                vo = {
                    prjId: sf.selectPrjId.split('-')[0],
                    pageNo: sf.detailPageIndex,
                    pageSize: sf.detailPageSize,
                    searchType: sf.searchType,
                    startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                    taskClassificationId: sf.taskClassificationId,
                };
            }else if(!!sf.isTestCaseDetail) {
                url = linkus.location.report + "/bscEffectMeasure/prjMeasure/getNewTestCaseReportInfo";
                vo = {
                    prjId: sf.selectPrjId.split('-')[0],
                    userId: sf.userId,
                    pageNo: sf.detailPageIndex,
                    pageSize: sf.detailPageSize,
                    searchType: sf.searchType,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                };
            }
            $.ajax({
                url: url,
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    sf.detailTableLoading = false;
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.detailTableDatas = data.data.objectList || [];
                        sf.detailTableTotal = data.data.count || 0;
                    }
                },
                error: function (data) {
                    sf.detailTableLoading = false;
                    sf.$Message.info({
                        content: "查询报表详情数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //获取当前登录人
        loadCurrentUser: function () {
            var sf = this;
            $.ajax({
                url: linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
                type: 'post',
                dataType: 'JSON',
            }).done(function (data) {
                sf.sbuId = data.sbuId;
                sf.listPrjProv();
                sf.listPrjMgtType();
            });
        },

        //查询是否是项目人员效能管理员
        checkIsPrjEffectEvalutionAdmin: function() {
            var sf = this;
            sf.isPrjEffectEvalutionAdmin = false;
            $.ajax({
                url: linkus.location.prjuser + '/sysDefRoleUserCtrl/checkIsPrjEffectEvalutionAdmin.action',
                type: 'post',
                data: {
                    buId: sf.buId
                },
                dataType: 'json',
                success: function (data) {
                    if (!!data) {
                        sf.isPrjEffectEvalutionAdmin = true;
                    }
                },
                error: function (data) {
                    sf.$Message.error({
                        content: "查询是否是项目人员效能管理员角色失败，请联系管理员！",
                        duration: 3
                    });
                }
            });
        },

        /*选择省份*/
        provChanged: function (value) {
            var sf = this;
            sf.provFltList = value || [];
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            if (!!value && value.length > 0) {
                sf.selectProv = value[value.length - 1];
            }
            sf.queryPrjInfo();
        },

        /*选择管控类型*/
        prjMgtTypeChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            sf.queryPrjInfo();
        },

        /*选择状态*/
        prjStatusChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            sf.queryPrjInfo();
        },

        /*选择归属年份*/
        yearChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            sf.queryPrjInfo();
        },

        //选择项目集
        onPrjChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            if (!sf.selectPrjId) {
                sf.queryPrjInfo();
                return;
            }

            if(sf.tabName == "prjNoticeBoard") {
                sf.getDemandOverView();
                sf.getBugOverView();
                sf.getCircleTime();
                sf.getDefectClassifyBySeverity();
                sf.getStoreTaskRankByUser();
                sf.getDefectRankByUser();
                sf.getReqTimelinessRate();
                sf.getBugTimelinessRate();
            }else if(sf.tabName == "prjUserPerformance") {
                sf.getPrjPerformanceList();
            }else if(sf.tabName == "digitalProductionEvaluation") {
                sf.queryPrjDigitalInfo();
            }

            if(!!sf.searchPrjList && sf.searchPrjList.length > 0) {
                for(var i = 0; i < sf.searchPrjList.length; i++) {
                    var item = sf.searchPrjList[i];
                    if(item.cid == sf.selectPrjId) {
                        sf.selectPrjName = item.name;
                    }
                }
            }
        },

        //选择周期时间
        onCycleTimeChanged: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({
                "condChanged": true, "condParam": {
                    "selectProvIds": sf.provFltList,
                    "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                    "selectPrjStatusIds": sf.selectPrjStatuses,
                    "selectYear": sf.selectYear,
                    "selectPrjId": prjId,
                    "selectDatePeriod": sf.selectDatePeriod
                }
            }, '*');
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length == 0 || !sf.selectDatePeriod[0]) {
                return;
            }

            if(sf.tabName == "prjNoticeBoard") {
                sf.getPrjProgQualReport();
                sf.getDefectChangeTrend();
                sf.getDemandChangeTrend();
                sf.getDemandReviewCoverageRate();
                sf.getDevTaskDefectDensity();
                sf.getTaskPromptness();
                sf.getTaskCompletionReport();
                sf.getBugTimelinessRateReport();
                sf.getBugFallbackRateChart();
                sf.getDevUnRepairedDefectReport();
                sf.getTestUnVerifyDefectReport();
                sf.getDevTaskList();
                sf.getTestTaskList();
                sf.getTestCaseList();
            }
        },

        /*效能度量-省份选择下拉列表*/
        listPrjProv: function () {
            var sf = this;
            sf.provTreeData = [];
            $.ajax({
                url: linkus.location.abp + '/v1/findProvince',
                type: 'get',
                data: {
                    buCode: sf.sbuId
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}") {
                        if (!!data.success) {
                            sf.provList = data.data || [];
                            sf.transToProvTreeData();
                        } else {
                            sf.$Message.error({
                                content: data.message || '查询省份列表失败，请联系管理员！',
                                duration: 3
                            });
                        }
                    }
                },
                error: function (data) {
                    if (!!data && !!data.responseText && !!JSON.parse(data.responseText)) {
                        sf.$Message.error({
                            content: JSON.parse(data.responseText).errorMessage || '查询省份列表失败，请联系管理员！',
                            duration: 3
                        });
                    } else {
                        sf.$Message.error({
                            content: '查询省份列表失败，请联系管理员！',
                            duration: 3
                        });
                    }
                }
            });
        },

        // 将省份信息转换成树形结构
        transToProvTreeData: function () {
            var sf = this;
            var provList = sf.provList;

            // 删除所有的children,以防止多次调用
            provList.forEach(function (item) {
                delete item.children;
            });
            var map = {}; //构建map
            provList.forEach(prov => {
                map[prov.id] = {
                    'value': prov.id,
                    'label': prov.defName
                };
                map[prov.cndtItems[0].cid] = {
                    'value': prov.cndtItems[0].cid,
                    'label': prov.cndtItems[0].name,
                    'children': []
                };
                map[prov.cndtItems[1].cid] = {
                    'value': prov.cndtItems[1].cid,
                    'label': prov.cndtItems[1].name,
                    'children': []
                }
            });
            var treeData = [];
            provList.forEach(child => {
                var areaId = child.cndtItems[1].cid;
                var bigAreaId = child.cndtItems[0].cid;

                var prov = map[child.id];
                var area = map[areaId];
                var bigArea = map[bigAreaId];
                // 去重
                if (!JSON.stringify(area.children).includes(JSON.stringify(prov))) {
                    area.children.push(prov);
                }
                if (!JSON.stringify(bigArea.children).includes(JSON.stringify(area))) {
                    bigArea.children.push(area);
                }
                if (!JSON.stringify(treeData).includes(JSON.stringify(bigArea))) {
                    treeData.push(bigArea);
                }
            })
            sf.provTreeData = treeData;
        },

        /*效能度量-管控类型下拉列表*/
        listPrjMgtType: function () {
            var sf = this;
            sf.prjMgtTypeList = [];
            $.ajax({
                url: linkus.location.prj + "/prjInfoCtrl/queryCurUserPrjMgtTypes.action",
                dataType: "json",
                data: {
                    sbuId: sf.sbuId,
                },
                success: function (data) {
                    sf.prjMgtTypeList = data || [];
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询管控类型下拉列表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*效能度量-项目集状态下拉列表*/
        listPrjStatus: function () {
            var sf = this;
            sf.prjStatusList = [];
            $.ajax({
                url: linkus.location.prjuser + "/sysDefCtrl/getSysDefBySrcAndCode.action",
                data: {
                    srcDefId: "5dc3c0dac56a424c33470464",
                    codeName: 'OPTION'
                },
                type: 'post',
                dataType: "json",
                success: function (data) {
                    sf.prjStatusList = data || [];
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询项目集状态下拉列表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        /*进入需求概览详情页*/
        openReqSurveyDetail: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({"reqSurveyDetail": true, "selectPrjId": prjId}, '*');
        },

        /*进入缺陷概览详情页*/
        openBugSurveyDetail: function () {
            var sf = this;
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;
            window.parent.postMessage({"bugSurveyDetail": true, "selectPrjId": prjId}, '*');
        },

        /*获取参数*/
        getQueryString: function (name) {
            // 获取参数
            var url = window.location.search;
            // 正则筛选地址栏
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            // 匹配目标参数
            var result = url.substr(1).match(reg);
            //返回参数值
            return result ? decodeURIComponent(result[2]) : null;
        },

        //查询待修复缺陷按成员排名和待验证缺陷按成员排名可视化图形
        getDefectRankByUser: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            sf.defectRankByUserDataVo = {};
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDefectRankByUser",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.defectRankByUserDataVo = data.data || {};
                        sf.drawDefectRankByUserChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询待修复缺陷按成员排名和待验证缺陷按成员排名错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制待修复缺陷按成员排名和待验证缺陷按成员排名可视化图形
        drawDefectRankByUserChart: function () {
            var sf = this;

            var repairDefects = sf.defectRankByUserDataVo.repair || [];
            if(repairDefects.length > 0) {
                sf.echartsRepairDefectHeight = repairDefects.length > 1 ?  repairDefects.length * 40 : 80;
            }else {
                sf.echartsRepairDefectHeight = 258;
            }

            var verifyDefects = sf.defectRankByUserDataVo.verify || [];
            if(verifyDefects.length > 0) {
                sf.echartsVerifyDefectHeight = verifyDefects.length > 1 ?  verifyDefects.length * 40 : 80;
            }else {
                sf.echartsVerifyDefectHeight = 258;
            }

            sf.$nextTick(function() {
                var myChartRepair = null;
                myChartRepair = echarts.init(document.getElementById('echarts_repair_defect'));
                myChartRepair.clear();

                var myChartVerify = null;
                myChartVerify = echarts.init(document.getElementById('echarts_verify_defect'));
                myChartVerify.clear();

                var repairUsers = [];
                var repairBugNumMap = {};
                var repairDeadlyDefectNums = [];
                var isRepairDeadlyDefectNum = false;
                var repairSrsDefectNums = [];
                var isRepairSrsDefectNum = false;
                var repairGeneralDefectNums = [];
                var isRepairGeneralDefectNum = false;
                var repairLowerDefectNums = [];
                var isRepairLowerDefectNum = false;
                var repairProposeDefectNums = [];
                var isRepairProposeDefectNum = false;

                var verifyUsers = [];
                var verifyBugNumMap = {};
                var verifyDeadlyDefectNums = [];
                var isVerifyDeadlyDefectNum = false;
                var verifySrsDefectNums = [];
                var isVerifySrsDefectNum = false;
                var verifyGeneralDefectNums = [];
                var isVerifyGeneralDefectNum = false;
                var verifyLowerDefectNums = [];
                var isVerifyLowerDefectNum = false;
                var verifyProposeDefectNums = [];
                var isVerifyProposeDefectNum = false;

                if (!!repairDefects && repairDefects.length > 0) {
                    for (var i = 0; i < repairDefects.length; i++) {
                        var item = repairDefects[i];
                        repairUsers.push(item.name);
                        repairBugNumMap[item.name] = item.bugNum || 0;
                        repairDeadlyDefectNums.push(item.deadlyDefectNum || 0);
                        if(!!item.deadlyDefectNum) {
                            isRepairDeadlyDefectNum = true;
                        }
                        repairSrsDefectNums.push(item.srsDefectNum || 0);
                        if(!!item.srsDefectNum) {
                            isRepairSrsDefectNum = true;
                        }
                        repairGeneralDefectNums.push(item.generalDefectNum || 0);
                        if(!!item.generalDefectNum) {
                            isRepairGeneralDefectNum = true;
                        }
                        repairLowerDefectNums.push(item.lowerDefectNum || 0);
                        if(!!item.lowerDefectNum) {
                            isRepairLowerDefectNum = true;
                        }
                        repairProposeDefectNums.push(item.proposeDefectNum || 0);
                        if(!!item.proposeDefectNum) {
                            isRepairProposeDefectNum = true;
                        }
                    }
                }
                if (!!verifyDefects && verifyDefects.length > 0) {
                    for (var i = 0; i < verifyDefects.length; i++) {
                        var item = verifyDefects[i];
                        verifyUsers.push(item.name);
                        verifyBugNumMap[item.name] = item.bugNum || 0;
                        verifyDeadlyDefectNums.push(item.deadlyDefectNum || 0);
                        if(!!item.deadlyDefectNum) {
                            isVerifyDeadlyDefectNum = true;
                        }
                        verifySrsDefectNums.push(item.srsDefectNum || 0);
                        if(!!item.srsDefectNum) {
                            isVerifySrsDefectNum = true;
                        }
                        verifyGeneralDefectNums.push(item.generalDefectNum || 0);
                        if(!!item.generalDefectNum) {
                            isVerifyGeneralDefectNum = true;
                        }
                        verifyLowerDefectNums.push(item.lowerDefectNum || 0);
                        if(!!item.lowerDefectNum) {
                            isVerifyLowerDefectNum = true;
                        }
                        verifyProposeDefectNums.push(item.proposeDefectNum || 0);
                        if(!!item.proposeDefectNum) {
                            isVerifyProposeDefectNum = true;
                        }
                    }
                }

                var seriesRepair = [];
                if(!!isRepairDeadlyDefectNum) {
                    seriesRepair.push({
                        name: '致命',
                        type: 'bar',
                        stack: 'total',
                        data: repairDeadlyDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#E65D4E',
                        }
                    });
                }
                if(!!isRepairSrsDefectNum) {
                    seriesRepair.push({
                        name: '严重',
                        type: 'bar',
                        stack: 'total',
                        data: repairSrsDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#FF9900',
                        }
                    });
                }
                if(!!isRepairGeneralDefectNum) {
                    seriesRepair.push({
                        name: '一般',
                        type: 'bar',
                        stack: 'total',
                        data: repairGeneralDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#FFD500',
                        }
                    });
                }
                if(!!isRepairLowerDefectNum) {
                    seriesRepair.push({
                        name: '低级',
                        type: 'bar',
                        stack: 'total',
                        data: repairLowerDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#3883E5',
                        }
                    });
                }
                if(!!isRepairProposeDefectNum) {
                    seriesRepair.push({
                        name: '建议',
                        type: 'bar',
                        stack: 'total',
                        data: repairProposeDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#84B1EC',
                        }
                    });
                }

                if(seriesRepair.length > 0) {
                    var objRepair = seriesRepair[seriesRepair.length - 1];
                    var itemStyle = {
                        normal: {
                            label: {
                                show: true, //开启显示
                                position: 'right', //在右方显示
                                formatter: function (params) {
                                    return repairBugNumMap[params.name];
                                },
                                textStyle: { //数值样式
                                    color: '#555555',
                                    fontSize: 12,
                                    fontFamily: 'PingFang SC, PingFang SC-Regular',
                                }
                            }
                        },
                    };
                    if(objRepair["name"] == "致命") {
                        itemStyle["normal"]["color"] = "#E65D4E";
                    }else if(objRepair["name"] == "严重") {
                        itemStyle["normal"]["color"] = "#FF9900";
                    }else if(objRepair["name"] == "一般") {
                        itemStyle["normal"]["color"] = "#FFD500";
                    }else if(objRepair["name"] == "低级") {
                        itemStyle["normal"]["color"] = "#3883E5";
                    }else if(objRepair["name"] == "建议") {
                        itemStyle["normal"]["color"] = "#84B1EC";
                    }
                    objRepair["itemStyle"] = itemStyle;
                }
                var optionRepair = null;
                if (repairDefects.length > 0) {
                    optionRepair = {
                        legend: {
                            show: true,
                            icon: sf.circleIcon,
                            align: 'left',
                            top: -5,
                            right: 10,
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '0%',
                            top: '10%',
                            containLabel: true
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            appendToBody: true,
                        },

                        xAxis: {
                            show: false,
                        },

                        yAxis: {
                            type: 'category',
                            data: repairUsers,
                            offset: 12,
                            axisTick: {
                                show: false,
                            },
                        },

                        series: seriesRepair,
                    };
                } else {
                    optionRepair = {
                        title: {
                            text: '暂无数据',
                            x: 'center',
                            y: 'center',
                            textStyle: {
                                fontSize: 12,
                                fontWeight: 'normal',
                                color: '#495060',
                            }
                        }
                    };
                }
                myChartRepair.setOption(optionRepair);

                var seriesVerify = [];
                if(!!isVerifyDeadlyDefectNum) {
                    seriesVerify.push({
                        name: '致命',
                        type: 'bar',
                        stack: 'total',
                        data: verifyDeadlyDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#E65D4E',
                        }
                    });
                }
                if(!!isVerifySrsDefectNum) {
                    seriesVerify.push({
                        name: '严重',
                        type: 'bar',
                        stack: 'total',
                        data: verifySrsDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#FF9900',
                        }
                    });
                }
                if(!!isVerifyGeneralDefectNum) {
                    seriesVerify.push({
                        name: '一般',
                        type: 'bar',
                        stack: 'total',
                        data: verifyGeneralDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#FFD500',
                        }
                    });
                }
                if(!!isVerifyLowerDefectNum) {
                    seriesVerify.push({
                        name: '低级',
                        type: 'bar',
                        stack: 'total',
                        data: verifyLowerDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#3883E5',
                        }
                    });
                }
                if(!!isVerifyProposeDefectNum) {
                    seriesVerify.push({
                        name: '建议',
                        type: 'bar',
                        stack: 'total',
                        data: verifyProposeDefectNums,
                        barWidth: 14,
                        itemStyle: {
                            color: '#84B1EC',
                        }
                    });
                }

                if(seriesVerify.length > 0) {
                    var objVerify = seriesVerify[seriesVerify.length - 1];
                    var itemStyle = {
                        normal: {
                            label: {
                                show: true, //开启显示
                                position: 'right', //在右方显示
                                formatter: function (params) {
                                    return verifyBugNumMap[params.name];
                                },
                                textStyle: { //数值样式
                                    color: '#555555',
                                    fontSize: 12,
                                    fontFamily: 'PingFang SC, PingFang SC-Regular',
                                }
                            }
                        },
                    };
                    if(objVerify["name"] == "致命") {
                        itemStyle["normal"]["color"] = "#E65D4E";
                    }else if(objVerify["name"] == "严重") {
                        itemStyle["normal"]["color"] = "#FF9900";
                    }else if(objVerify["name"] == "一般") {
                        itemStyle["normal"]["color"] = "#FFD500";
                    }else if(objVerify["name"] == "低级") {
                        itemStyle["normal"]["color"] = "#3883E5";
                    }else if(objVerify["name"] == "建议") {
                        itemStyle["normal"]["color"] = "#84B1EC";
                    }
                    objVerify["itemStyle"] = itemStyle;
                }
                var optionVerify = null;
                if (verifyDefects.length > 0) {
                    optionVerify = {
                        legend: {
                            show: true,
                            icon: sf.circleIcon,
                            align: 'left',
                            top: -5,
                            right: 10,
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '0%',
                            top: '10%',
                            containLabel: true
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            appendToBody: true,
                        },

                        xAxis: {
                            show: false,
                        },

                        yAxis: {
                            type: 'category',
                            data: verifyUsers,
                            offset: 12,
                            axisTick: {
                                show: false,
                            },
                        },

                        series: seriesVerify,
                    };
                } else {
                    optionVerify = {
                        title: {
                            text: '暂无数据',
                            x: 'center',
                            y: 'center',
                            textStyle: {
                                fontSize: 12,
                                fontWeight: 'normal',
                                color: '#495060',
                            }
                        }
                    };
                }
                myChartVerify.setOption(optionVerify);

                myChartRepair.getDom().style.height = sf.echartsRepairDefectHeight + "px";
                myChartRepair.getDom().childNodes[0].style.height =sf.echartsRepairDefectHeight + "px";
                myChartRepair.getDom().childNodes[0].childNodes[0].setAttribute("height", sf.echartsRepairDefectHeight);
                myChartRepair.getDom().childNodes[0].childNodes[0].style.height = sf.echartsRepairDefectHeight + "px";
                myChartRepair.resize();

                myChartVerify.getDom().style.height = sf.echartsVerifyDefectHeight + "px";
                myChartVerify.getDom().childNodes[0].style.height =sf.echartsVerifyDefectHeight + "px";
                myChartVerify.getDom().childNodes[0].childNodes[0].setAttribute("height", sf.echartsVerifyDefectHeight);
                myChartVerify.getDom().childNodes[0].childNodes[0].style.height = sf.echartsVerifyDefectHeight + "px";
                myChartVerify.resize();

                var prjId = null;
                sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

                // echart全图鼠标点击事件
                myChartRepair.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    window.parent.postMessage({
                        "defectRankByUserDetail": true,
                        "selectPrjId": prjId,
                        "taskType": "repair",
                        "defectRankByUserDataVo": sf.defectRankByUserDataVo
                    }, '*');
                });

                // echart全图鼠标点击事件
                myChartVerify.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    window.parent.postMessage({
                        "defectRankByUserDetail": true,
                        "selectPrjId": prjId,
                        "taskType": "verify",
                        "defectRankByUserDataVo": sf.defectRankByUserDataVo
                    }, '*');
                });
            });
        },

        //查询存量开发任务按成员排名和存量测试任务按成员排名可视化图形
        getStoreTaskRankByUser: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            sf.storeTaskRankByUserDataVo = {};
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getStoreTaskRankByUser",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.storeTaskRankByUserDataVo = data.data || {};
                        sf.drawStoreTaskRankByUserChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询存量任务错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制存量开发任务按成员排名和存量测试任务按成员排名可视化图形
        drawStoreTaskRankByUserChart: function () {
            var sf = this;

            var devTasks = sf.storeTaskRankByUserDataVo.dev || [];
            if(devTasks.length > 0) {
                sf.echartsStoreDevTaskHeight = devTasks.length > 1 ? devTasks.length * 40 : 80;
            }else {
                sf.echartsStoreDevTaskHeight = 258;
            }

            var testTasks = sf.storeTaskRankByUserDataVo.test || [];
            if(testTasks.length > 0) {
                sf.echartsStoreTestTaskHeight = testTasks.length > 1 ? testTasks.length * 40 : 80;
            }else {
                sf.echartsStoreTestTaskHeight = 258;
            }

            sf.$nextTick(function() {
                var myChartDev = null;
                myChartDev = echarts.init(document.getElementById('echarts_store_dev_Task'));
                myChartDev.clear();

                var myChartTest = null;
                myChartTest = echarts.init(document.getElementById('echarts_store_test_Task'));
                myChartTest.clear();

                var devUsers = [];
                var devTaskNums = [];
                var testUsers = [];
                var testTaskNums = [];
                if (!!devTasks && devTasks.length > 0) {
                    for (var i = 0; i < devTasks.length; i++) {
                        var item = devTasks[i];
                        devUsers.push(item.name);
                        devTaskNums.push(item.taskNum || 0);
                    }
                }
                if (!!testTasks && testTasks.length > 0) {
                    for (var i = 0; i < testTasks.length; i++) {
                        var item = testTasks[i];
                        testUsers.push(item.name);
                        testTaskNums.push(item.taskNum || 0);
                    }
                }

                var optionDev = null;
                if (devTasks.length > 0) {
                    optionDev = {
                        legend: {
                            show: false,
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            top: '3%',
                            containLabel: true
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function (params) {
                                return params[0].name + "，开发任务，" + params[0].value;
                            },
                            appendToBody: true,
                        },

                        xAxis: {
                            show: false,
                        },

                        yAxis: {
                            type: 'category',
                            data: devUsers,
                            offset: 12,
                            axisTick: {
                                show: false,
                            },
                        },

                        series: [{
                            type: 'bar',
                            stack: 'one',
                            data: devTaskNums,
                            barWidth: 14,
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 1,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: '#3883E5' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: '#84B1EC' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    },
                                    label: {
                                        show: true, //开启显示
                                        position: 'right', //在上方显示
                                        textStyle: { //数值样式
                                            color: '#555555',
                                            fontSize: 12,
                                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                                        }
                                    }
                                },
                            }
                        }],
                    };
                } else {
                    optionDev = {
                        title: {
                            text: '暂无数据',
                            x: 'center',
                            y: 'center',
                            textStyle: {
                                fontSize: 12,
                                fontWeight: 'normal',
                                color: '#495060',
                            }
                        }
                    };
                }
                myChartDev.setOption(optionDev);

                var optionTest = null;
                if (testTasks.length > 0) {
                    optionTest = {
                        legend: {
                            show: false,
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            top: '3%',
                            containLabel: true
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function (params) {
                                return params[0].name + "，测试任务，" + params[0].value;
                            },
                            appendToBody: true,
                        },

                        xAxis: {
                            show: false,
                        },

                        yAxis: {
                            type: 'category',
                            data: testUsers,
                            offset: 12,
                            axisTick: {
                                show: false,
                            },
                        },

                        series: [{
                            type: 'bar',
                            stack: 'one',
                            data: testTaskNums,
                            barWidth: 14,
                            itemStyle: {
                                normal: {
                                    color: {
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 1,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0, color: '#3883E5' // 0% 处的颜色
                                        }, {
                                            offset: 1, color: '#84B1EC' // 100% 处的颜色
                                        }],
                                        global: false // 缺省为 false
                                    },
                                    label: {
                                        show: true, //开启显示
                                        position: 'right', //在上方显示
                                        textStyle: { //数值样式
                                            color: '#555555',
                                            fontSize: 12,
                                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                                        }
                                    }
                                },
                            }
                        }],
                    };
                } else {
                    optionTest = {
                        title: {
                            text: '暂无数据',
                            x: 'center',
                            y: 'center',
                            textStyle: {
                                fontSize: 12,
                                fontWeight: 'normal',
                                color: '#495060',
                            }
                        }
                    };
                }
                myChartTest.setOption(optionTest);

                myChartDev.getDom().style.height = sf.echartsStoreDevTaskHeight + "px";
                myChartDev.getDom().childNodes[0].style.height =sf.echartsStoreDevTaskHeight + "px";
                myChartDev.getDom().childNodes[0].childNodes[0].setAttribute("height", sf.echartsStoreDevTaskHeight);
                myChartDev.getDom().childNodes[0].childNodes[0].style.height = sf.echartsStoreDevTaskHeight + "px";
                myChartDev.resize();

                myChartTest.getDom().style.height = sf.echartsStoreTestTaskHeight + "px";
                myChartTest.getDom().childNodes[0].style.height =sf.echartsStoreTestTaskHeight + "px";
                myChartTest.getDom().childNodes[0].childNodes[0].setAttribute("height", sf.echartsStoreTestTaskHeight);
                myChartTest.getDom().childNodes[0].childNodes[0].style.height = sf.echartsStoreTestTaskHeight + "px";
                myChartTest.resize();

                var prjId = null;
                sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

                // echart全图鼠标点击事件
                myChartDev.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    window.parent.postMessage({
                        "storeTaskRankByUserDetail": true,
                        "selectPrjId": prjId,
                        "taskType": "dev"
                    }, '*');
                });

                // echart全图鼠标点击事件
                myChartTest.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    window.parent.postMessage({
                        "storeTaskRankByUserDetail": true,
                        "selectPrjId": prjId,
                        "taskType": "test"
                    }, '*');
                });
            });
        },

        //测试待验证缺陷报表-排序
        testUnVerifyDefectSortChange: function (column) {
            var sf = this;
            sf.testUnVerifyDefectTableSortKey = "";
            if (column.key == "unVerifyDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "unVerifyUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "unVerifyDown";
                }
            } else if (column.key == "highLevelDelayVerifyDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "delayUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "delayDown";
                }
            } else if (column.key == "verifiedDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "verifiedUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "verifiedDown";
                }
            } else if (column.key == "dayVerifyDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "dayUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "dayDown";
                }
            } else if (column.key == "weekVerifyDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "weekUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "weekDown";
                }
            } else if (column.key == "monthVerifyDefectNum") {
                if (column.order == "asc") {
                    sf.testUnVerifyDefectTableSortKey = "monthUp";
                } else if (column.order == "desc") {
                    sf.testUnVerifyDefectTableSortKey = "monthDown";
                }
            }
            sf.getTestUnVerifyDefectReport();
        },

        //查询测试待验证缺陷报表
        getTestUnVerifyDefectReport: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.testUnVerifyDefectLoading = true;
            sf.testUnVerifyDefectDatas = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getTestUnVerifyDefectReport",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                    sortKey: sf.testUnVerifyDefectTableSortKey,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.testUnVerifyDefectLoading = false;
                        sf.testUnVerifyDefectDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.testUnVerifyDefectLoading = false;
                    sf.$Message.info({
                        content: "查询测试待验证缺陷报表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //导出测试待验证缺陷报表
        exportTestUnVerifyDefectReport: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportTestUnVerifyDefectReport";
            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            var params = {
                prjId: sf.selectPrjId.split('-')[0],
                sortKey: sf.testUnVerifyDefectTableSortKey,
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        //开发待修复缺陷报表-排序
        devUnRepairedDefectSortChange: function (column) {
            var sf = this;
            sf.devUnRepairedDefectTableSortKey = "";
            if (column.key == "unRepairDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "unRepairUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "unRepairDown";
                }
            } else if (column.key == "highLevelDelayRepairDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "delayUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "delayDown";
                }
            } else if (column.key == "repairedDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "repairedUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "repairedDown";
                }
            } else if (column.key == "dayRepairDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "dayUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "dayDown";
                }
            } else if (column.key == "weekRepairDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "weekUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "weekDown";
                }
            } else if (column.key == "monthRepairDefectNum") {
                if (column.order == "asc") {
                    sf.devUnRepairedDefectTableSortKey = "monthUp";
                } else if (column.order == "desc") {
                    sf.devUnRepairedDefectTableSortKey = "monthDown";
                }
            }
            sf.getDevUnRepairedDefectReport();
        },

        //查询开发待修复缺陷报表
        getDevUnRepairedDefectReport: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.devUnRepairedDefectLoading = true;
            sf.devUnRepairedDefectDatas = [];
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDevUnRepairedDefectReport",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                    sortKey: sf.devUnRepairedDefectTableSortKey,
                    endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.devUnRepairedDefectLoading = false;
                        sf.devUnRepairedDefectDatas = data.data || [];
                    }
                },
                error: function (data) {
                    sf.devUnRepairedDefectLoading = false;
                    sf.$Message.info({
                        content: "查询开发待修复缺陷报表错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //导出开发待修复缺陷报表
        exportDevUnRepairedDefectReport: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var url = linkus.location.report + "/bscEffectMeasure/prjMeasure/exportDevUnRepairedDefectReport";
            // 创建表单部分
            var form = document.createElement('form');
            form.style.display = 'none';
            form.action = url;
            form.method = 'post';
            form.enctype = "application/json;charset=utf-8";
            document.body.appendChild(form);

            var params = {
                prjId: sf.selectPrjId.split('-')[0],
                sortKey: sf.devUnRepairedDefectTableSortKey,
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            setObj(params);// 执行添加input框
            function setObj(obj, name) { // obj是你的对象数据，name是你的父级名字
                for (var key in obj) {
                    var input = document.createElement('input')
                    input.type = 'hidden'
                    input.name = name ? name + '.' + key : key // 有父级就加上父级的名字（例：goods.goodsId）
                    input.value = obj[key] !== undefined ? obj[key] : null // 如果数据为undefined就改为null
                    form.appendChild(input)
                }
            }

            form.submit();
            form.remove();
        },

        //查询缺陷回退率数据
        getBugFallbackRateChart: function() {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.bugFallbackRateDatas = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getBugFallbackRateChart",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.bugFallbackRateDatas = data.data || [];
                        sf.drawBugFallbackRateChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询缺陷回退率数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

         //绘制缺陷回退率图表
        drawBugFallbackRateChart: function() {
            var sf = this;
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_bug_fallback_rate'));
            myChart.clear();

            if (!!sf.bugFallbackRateDatas && sf.bugFallbackRateDatas.length > 0) {

                var xAxisDate = [];
                var bugFallbackRateMap = {};
                var bugFallbackRate = [];
                var xAxisDateLabels = [];

                for (var i = 0; i < sf.bugFallbackRateDatas.length; i++) {
                    var item = sf.bugFallbackRateDatas[i];
                    xAxisDate.push(item.currentDate);
                    bugFallbackRateMap[item.currentDate] = item.fallBackRate || 0;
                }

                for (var i = 0; i < xAxisDate.length; i++) {

                    bugFallbackRate.push({
                        value: bugFallbackRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });

                    xAxisDateLabels.push({
                        value: xAxisDate[i],
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                }

                var option = {
                    legend: {
                        data: ['缺陷回退率'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            return value + "%";
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '缺陷回退率',
                        type: 'line',
                        data: bugFallbackRate,
                        showAllSymbol: true,
                        //smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#E65D4E'
                                },
                                color: '#E65D4E'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#E65D4E"
                        }
                    }],
                };
                myChart.setOption(option);

                var prjId = null;
                sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

                // echart全图鼠标点击事件
                myChart.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    if (myChart.containPixel('grid', pointInPixel)) {
                        window.parent.postMessage({
                            "bugFallbackRateDetail": true,
                            "selectPrjId": prjId,
                            "selectDatePeriod": sf.selectDatePeriod
                        }, '*');
                    }
                });
            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }
        },

        //查询开发任务缺陷密度和任务单一次通过率
        getDevTaskDefectDensity: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.devTaskDefectDensityFirstTimeData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDevTaskDefectDensity",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.devTaskDefectDensityFirstTimeData = data.data || [];
                        sf.drawDevTaskDefectDensityFirstTimeChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询开发任务缺陷密度和任务单一次通过率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制开发任务缺陷密度和任务单一次通过率
        drawDevTaskDefectDensityFirstTimeChart: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_devTask_defectDensity'));
            myChart.clear();

            var myChartReq = null;
            myChartReq = echarts.init(document.getElementById('echarts_firstTime_Pass'));
            myChartReq.clear();

            if (!!sf.devTaskDefectDensityFirstTimeData && sf.devTaskDefectDensityFirstTimeData.length > 0) {

                var xAxisDate = [];
                var devTaskDefectDensityMap = {};
                var firTimePassRateMap = {};
                var devTaskDefectDensity = [];
                var firTimePassRate = [];
                var xAxisDateLabels = [];

                for (var i = 0; i < sf.devTaskDefectDensityFirstTimeData.length; i++) {
                    var item = sf.devTaskDefectDensityFirstTimeData[i];
                    xAxisDate.push(item.currentDate);
                    devTaskDefectDensityMap[item.currentDate] = item.devTaskDefectDensity || 0;
                    firTimePassRateMap[item.currentDate] = item.firTimePassRate || 0;
                }

                for (var i = 0; i < xAxisDate.length; i++) {
                    devTaskDefectDensity.push({
                        value: devTaskDefectDensityMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    firTimePassRate.push({
                        value: firTimePassRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });

                    xAxisDateLabels.push({
                        value: xAxisDate[i],
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                }

                var option = {
                    legend: {
                        data: ['开发任务缺陷密度'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '开发任务缺陷密度',
                        type: 'line',
                        data: devTaskDefectDensity,
                        showAllSymbol: true,
                        //smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#E65D4E'
                                },
                                color: '#E65D4E'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#E65D4E"
                        }
                    }],
                };
                myChart.setOption(option);

                var optionReq = {
                    legend: {
                        data: ['任务单一次通过率'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            return value + "%";
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '任务单一次通过率',
                        type: 'line',
                        data: firTimePassRate,
                        showAllSymbol: true,
                        //smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#E65D4E'
                                },
                                color: '#E65D4E'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#E65D4E"
                        }
                    }],
                };
                myChartReq.setOption(optionReq);

            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);

                var optionReq = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChartReq.setOption(optionReq);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChart.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                if (myChart.containPixel('grid', pointInPixel)) {
                    window.parent.postMessage({
                        "devTaskDefectDensityDetail": true,
                        "selectPrjId": prjId,
                        selectDatePeriod: sf.selectDatePeriod
                    }, '*');
                }
            });

            // echart全图鼠标点击事件
            myChartReq.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                if (myChartReq.containPixel('grid', pointInPixel)) {
                    window.parent.postMessage({
                        "taskFisrtPassRateDetail": true,
                        "selectPrjId": prjId,
                        selectDatePeriod: sf.selectDatePeriod
                    }, '*');
                }
            });
        },

        //查询任务及时率
        getTaskPromptness: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.taskPromptnessData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getTaskTimelinessRate",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.taskPromptnessData = data.data || [];
                        sf.drawTaskPromptness();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询任务及时率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制任务及时率
        drawTaskPromptness: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_task_promptness'));
            myChart.clear();

            if (!!sf.taskPromptnessData && sf.taskPromptnessData.length > 0) {

                var xAxisDate = [];
                var reqAnalysisTimelinessRateMap = {};
                var designTimelinessRateMap = {};
                var devTimelinessRateMap = {};
                var testTimelinessRateMap = {};

                var reqAnalysisTimelinessRate = [];
                var designTimelinessRate = [];
                var devTimelinessRate = [];
                var testTimelinessRate = [];

                var xAxisDateLabels = [];

                for (var i = 0; i < sf.taskPromptnessData.length; i++) {
                    var item = sf.taskPromptnessData[i];
                    xAxisDate.push(item.currentDate);
                    reqAnalysisTimelinessRateMap[item.currentDate] = item.reqAnalysisTimelinessRate || 0;
                    designTimelinessRateMap[item.currentDate] = item.designTimelinessRate || 0;
                    devTimelinessRateMap[item.currentDate] = item.devTimelinessRate || 0;
                    testTimelinessRateMap[item.currentDate] = item.testTimelinessRate || 0;
                }

                for (var i = 0; i < xAxisDate.length; i++) {
                    reqAnalysisTimelinessRate.push({
                        value: reqAnalysisTimelinessRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    designTimelinessRate.push({
                        value: designTimelinessRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    devTimelinessRate.push({
                        value: devTimelinessRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    testTimelinessRate.push({
                        value: testTimelinessRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });

                    xAxisDateLabels.push({
                        value: xAxisDate[i],
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                }

                var option = {
                    legend: {
                        data: ['需求分析', '设计', '开发', '测试'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            return value + "%";
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '需求分析',
                        type: 'line',
                        data: reqAnalysisTimelinessRate,
                        showAllSymbol: true,
                        smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#FFBB00'
                                },
                                color: '#FFBB00'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#FFBB00"
                        }
                    },
                        {
                            name: '设计',
                            type: 'line',
                            data: designTimelinessRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#08A240'
                                    },
                                    color: '#08A240'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#08A240"
                            }
                        },
                        {
                            name: '开发',
                            type: 'line',
                            data: devTimelinessRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#6962d6'
                                    },
                                    color: '#6962d6'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#6962d6"
                            }
                        }, {
                            name: '测试',
                            type: 'line',
                            data: testTimelinessRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#E65D4E'
                                    },
                                    color: '#E65D4E'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#E65D4E"
                            }
                        }],
                };
                myChart.setOption(option);

                var prjId = null;
                sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

                // echart全图鼠标点击事件
                myChart.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    if (myChart.containPixel('grid', pointInPixel)) {
                        window.parent.postMessage({
                            "taskPromptnessDetail": true,
                            "selectPrjId": prjId,
                            selectDatePeriod: sf.selectDatePeriod
                        }, '*');
                    }
                });

            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }
        },

        //查询缺陷修复/验证及时率
        getBugTimelinessRateReport: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.bugTimelinessRateData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getBugTimelinessRateReport",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.bugTimelinessRateData = data.data || [];
                        sf.drawBugTimelinessRateEcharts();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询缺陷修复/验证及时率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制缺陷修复/验证及时率
        drawBugTimelinessRateEcharts: function () {
            var sf = this;
            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_bug_timeliness_rate'));
            myChart.clear();

            if (!!sf.bugTimelinessRateData && sf.bugTimelinessRateData.length > 0) {

                var xAxisDate = [];
                var timelinessRepairRateMap = {};
                var timelinessVerifyRateMap = {};

                var timelinessRepairRate = [];
                var timelinessVerifyRate = [];

                var xAxisDateLabels = [];

                for (var i = 0; i < sf.bugTimelinessRateData.length; i++) {
                    var item = sf.bugTimelinessRateData[i];
                    xAxisDate.push(item.currentDate);
                    timelinessRepairRateMap[item.currentDate] = item.timelinessRepairRate || 0;
                    timelinessVerifyRateMap[item.currentDate] = item.timelinessVerifyRate || 0;
                }

                for (var i = 0; i < xAxisDate.length; i++) {
                    timelinessRepairRate.push({
                        value: timelinessRepairRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    timelinessVerifyRate.push({
                        value: timelinessVerifyRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });

                    xAxisDateLabels.push({
                        value: xAxisDate[i],
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                }

                var option = {
                    legend: {
                        data: ['缺陷修复', '缺陷验证'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            return value + "%";
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '缺陷修复',
                        type: 'line',
                        data: timelinessRepairRate,
                        showAllSymbol: true,
                        smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#FFBB00'
                                },
                                color: '#FFBB00'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#FFBB00"
                        }
                    },
                        {
                            name: '缺陷验证',
                            type: 'line',
                            data: timelinessVerifyRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#3883E5'
                                    },
                                    color: '#3883E5'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#3883E5"
                            }
                        }],
                };
                myChart.setOption(option);

                var prjId = null;
                sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

                // echart全图鼠标点击事件
                myChart.getZr().on('click', function (params) {
                    var pointInPixel = [params.offsetX, params.offsetY]
                    if (myChart.containPixel('grid', pointInPixel)) {
                        window.parent.postMessage({
                            "timelinessRepairOrVerifyDetail": true,
                            "selectPrjId": prjId,
                            selectDatePeriod: sf.selectDatePeriod
                        }, '*');
                    }
                });

            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }
        },

        //查询评审覆盖率
        getDemandReviewCoverageRate: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.demandReviewCoverageRateData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandReviewCoverageRate",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.demandReviewCoverageRateData = data.data || [];
                        sf.drawReviewCoverageRateChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询评审覆盖率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制评审覆盖率
        drawReviewCoverageRateChart: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_req_severity'));
            myChart.clear();

            if (!!sf.demandReviewCoverageRateData && sf.demandReviewCoverageRateData.length > 0) {

                var xAxisDate = [];
                var reqReviewCoverageRateMap = {};
                var desReviewDemandRateMap = {};
                var codeReviewDemandRateMap = {};

                var reqReviewCoverageRate = [];
                var desReviewDemandRate = [];
                var codeReviewDemandRate = [];

                var xAxisDateLabels = [];

                for (var i = 0; i < sf.demandReviewCoverageRateData.length; i++) {
                    var item = sf.demandReviewCoverageRateData[i];
                    xAxisDate.push(item.currentDate);
                    reqReviewCoverageRateMap[item.currentDate] = item.reqReviewCoverageRate || 0;
                    desReviewDemandRateMap[item.currentDate] = item.desReviewDemandRate || 0;
                    codeReviewDemandRateMap[item.currentDate] = item.codeReviewDemandRate || 0;
                }

                for (var i = 0; i < xAxisDate.length; i++) {
                    reqReviewCoverageRate.push({
                        value: reqReviewCoverageRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    desReviewDemandRate.push({
                        value: desReviewDemandRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });
                    codeReviewDemandRate.push({
                        value: codeReviewDemandRateMap[xAxisDate[i]],
                        symbol: 'circle',
                        symbolSize: 1
                    });

                    xAxisDateLabels.push({
                        value: xAxisDate[i],
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                }

                var option = {
                    legend: {
                        data: ['设计评审', '需求评审', '代码评审'],
                        y: 'top',
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            return value + "%";
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        /*type: 'category',
                            boundaryGap: false,*/
                        data: xAxisDateLabels,
                        /*max: 7,*/
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                    },

                    yAxis: {
                        type: 'value',
                        offset: 8,
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        nameTextStyle: {
                            color: '#999999',
                            padding: [0, 0, 0, 0]
                        }
                    },

                    series: [{
                        name: '需求评审',
                        type: 'line',
                        data: reqReviewCoverageRate,
                        showAllSymbol: true,
                        smooth: true,
                        symbol: "none",
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#FFBB00'
                                },
                                color: '#FFBB00'
                            }
                        },
                        lineStyle: {
                            shadowBlur: 5,
                            shadowColor: "#FFBB00"
                        }
                    },
                        {
                            name: '设计评审',
                            type: 'line',
                            data: desReviewDemandRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#08A240'
                                    },
                                    color: '#08A240'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#08A240"
                            }
                        },
                        {
                            name: '代码评审',
                            type: 'line',
                            data: codeReviewDemandRate,
                            showAllSymbol: true,
                            smooth: true,
                            symbol: "none",
                            itemStyle: {
                                normal: {
                                    lineStyle: {
                                        color: '#6962d6'
                                    },
                                    color: '#6962d6'
                                }
                            },
                            lineStyle: {
                                shadowBlur: 5,
                                shadowColor: "#6962d6"
                            }
                        }],
                };
                myChart.setOption(option);

            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }
        },


        //查询缺陷按严重程度分类数据
        getDefectClassifyBySeverity: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            sf.defectClassifyBySeverityVo = {};
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDefectClassifyBySeverity",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0],
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.defectClassifyBySeverityVo = data.data || {};
                        sf.drawDefectClassifyBySeverityChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询缺陷按严重程度分类数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制缺陷按严重程度分类数据
        drawDefectClassifyBySeverityChart: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_bug_severity'));
            myChart.clear();

            if (!!sf.defectClassifyBySeverityVo && JSON.stringify(sf.defectClassifyBySeverityVo) != "{}") {

                var repairDeadlyDefectNum = 0; //待修复致命缺陷数
                var repairSrsDefectNum = 0; //待修复严重缺陷数
                var repairGeneralDefectNum = 0; //待修复一般缺陷数
                var repairLowerDefectNum = 0; //待修复低级缺陷数
                var repairProposeDefectNum = 0; //待修复建议缺陷数

                var verifyDeadlyDefectNum = 0; //待验证致命缺陷数
                var verifySrsDefectNum = 0; //待验证严重缺陷数
                var verifyGeneralDefectNum = 0; //待验证一般缺陷数
                var verifyLowerDefectNum = 0; //待验证低级缺陷数
                var verifyProposeDefectNum = 0; //待验证建议缺陷数

                var deadlyDefectNum = 0; //全部致命缺陷数
                var srsDefectNum = 0; //全部严重缺陷数
                var generalDefectNum = 0; //全部一般缺陷数
                var lowerDefectNum = 0; //全部低级缺陷数
                var proposeDefectNum = 0; //全部建议缺陷数

                var repairDeadlyDefectProportion = 0; //待修复致命缺陷数占比
                var repairSrsDefectProportion = 0; //待修复严重缺陷数占比
                var repairGeneralDefectProportion = 0; //待修复一般缺陷数占比
                var repairLowerDefectProportion = 0; //待修复低级缺陷数占比
                var repairProposeDefectProportion = 0; //待修复建议缺陷数占比

                var verifyDeadlyDefectProportion = 0; //待验证致命缺陷数占比
                var verifySrsDefectProportion = 0; //待验证严重缺陷数占比
                var verifyGeneralDefectProportion = 0; //待验证一般缺陷数占比
                var verifyLowerDefectProportion = 0; //待验证低级缺陷数占比
                var verifyProposeDefectProportion = 0; //待验证建议缺陷数占比

                var deadlyDefectProportion = 0; //全部致命缺陷数占比
                var srsDefectProportion = 0; //全部严重缺陷数占比
                var generalDefectProportion = 0; //全部一般缺陷数占比
                var lowerDefectProportion = 0; //全部低级缺陷数占比
                var proposeDefectProportion = 0; //全部建议缺陷数占比

                repairDeadlyDefectNum = sf.defectClassifyBySeverityVo.repairDeadlyDefectNum || 0;
                repairSrsDefectNum = sf.defectClassifyBySeverityVo.repairSrsDefectNum || 0;
                repairGeneralDefectNum = sf.defectClassifyBySeverityVo.repairGeneralDefectNum || 0;
                repairLowerDefectNum = sf.defectClassifyBySeverityVo.repairLowerDefectNum || 0;
                repairProposeDefectNum = sf.defectClassifyBySeverityVo.repairProposeDefectNum || 0;

                verifyDeadlyDefectNum = sf.defectClassifyBySeverityVo.verifyDeadlyDefectNum || 0;
                verifySrsDefectNum = sf.defectClassifyBySeverityVo.verifySrsDefectNum || 0;
                verifyGeneralDefectNum = sf.defectClassifyBySeverityVo.verifyGeneralDefectNum || 0;
                verifyLowerDefectNum = sf.defectClassifyBySeverityVo.verifyLowerDefectNum || 0;
                verifyProposeDefectNum = sf.defectClassifyBySeverityVo.verifyProposeDefectNum || 0;

                deadlyDefectNum = sf.defectClassifyBySeverityVo.deadlyDefectNum || 0;
                srsDefectNum = sf.defectClassifyBySeverityVo.srsDefectNum || 0;
                generalDefectNum = sf.defectClassifyBySeverityVo.generalDefectNum || 0;
                lowerDefectNum = sf.defectClassifyBySeverityVo.lowerDefectNum || 0;
                proposeDefectNum = sf.defectClassifyBySeverityVo.proposeDefectNum || 0;

                repairDeadlyDefectProportion = sf.defectClassifyBySeverityVo.repairDeadlyDefectProportion || 0;
                repairSrsDefectProportion = sf.defectClassifyBySeverityVo.repairSrsDefectProportion || 0;
                repairGeneralDefectProportion = sf.defectClassifyBySeverityVo.repairGeneralDefectProportion || 0;
                repairLowerDefectProportion = sf.defectClassifyBySeverityVo.repairLowerDefectProportion || 0;
                repairProposeDefectProportion = sf.defectClassifyBySeverityVo.repairProposeDefectProportion || 0;

                verifyDeadlyDefectProportion = sf.defectClassifyBySeverityVo.verifyDeadlyDefectProportion || 0;
                verifySrsDefectProportion = sf.defectClassifyBySeverityVo.verifySrsDefectProportion || 0;
                verifyGeneralDefectProportion = sf.defectClassifyBySeverityVo.verifyGeneralDefectProportion || 0;
                verifyLowerDefectProportion = sf.defectClassifyBySeverityVo.verifyLowerDefectProportion || 0;
                verifyProposeDefectProportion = sf.defectClassifyBySeverityVo.verifyProposeDefectProportion || 0;

                deadlyDefectProportion = sf.defectClassifyBySeverityVo.deadlyDefectProportion || 0;
                srsDefectProportion = sf.defectClassifyBySeverityVo.srsDefectProportion || 0;
                generalDefectProportion = sf.defectClassifyBySeverityVo.generalDefectProportion || 0;
                lowerDefectProportion = sf.defectClassifyBySeverityVo.lowerDefectProportion || 0;
                proposeDefectProportion = sf.defectClassifyBySeverityVo.proposeDefectProportion || 0;

                var repairBugNum = sf.defectClassifyBySeverityVo.repairBugNum || 0;
                var verifyBugNum = sf.defectClassifyBySeverityVo.verifyBugNum || 0;
                var bugNum = sf.defectClassifyBySeverityVo.bugNum || 0;

                var repairDatas = [];
                var verifyDatas = [];
                var datas = [];

                if(!!repairDeadlyDefectNum) {
                    repairDatas.push({
                        name: "致命",
                        value: repairDeadlyDefectNum,
                        percent: repairDeadlyDefectProportion,
                        itemStyle: {
                            color: "#E65D4E",
                        }
                    });
                }
                if(!!repairSrsDefectNum) {
                    repairDatas.push({
                        name: "严重",
                        value: repairSrsDefectNum,
                        percent: repairSrsDefectProportion,
                        itemStyle: {
                            color: "#FF9900"
                        }
                    });
                }
                if(!!repairGeneralDefectNum) {
                    repairDatas.push({
                        name: "一般",
                        value: repairGeneralDefectNum,
                        percent: repairGeneralDefectProportion,
                        itemStyle: {
                            color: "#FFDD30"
                        }
                    });
                }
                if(!!repairLowerDefectNum) {
                    repairDatas.push({
                        name: "低级",
                        value: repairLowerDefectNum,
                        percent: repairLowerDefectProportion,
                        itemStyle: {
                            color: "#3883E5"
                        }
                    });
                }
               if(!!repairProposeDefectNum) {
                   repairDatas.push({
                       name: "建议",
                       value: repairProposeDefectNum,
                       percent: repairProposeDefectProportion,
                       itemStyle: {
                           color: "#84B1EC"
                       }
                   });
               }

               if(!!verifyDeadlyDefectNum) {
                   verifyDatas.push({
                       name: "致命",
                       value: verifyDeadlyDefectNum,
                       percent: verifyDeadlyDefectProportion,
                       itemStyle: {
                           color: "#E65D4E"
                       }
                   });
               }
               if(!!verifySrsDefectNum) {
                   verifyDatas.push({
                       name: "严重",
                       value: verifySrsDefectNum,
                       percent: verifySrsDefectProportion,
                       itemStyle: {
                           color: "#FF9900"
                       }
                   });
               }
               if(!!verifyGeneralDefectNum) {
                   verifyDatas.push({
                       name: "一般",
                       value: verifyGeneralDefectNum,
                       percent: verifyGeneralDefectProportion,
                       itemStyle: {
                           color: "#FFDD30"
                       }
                   });
               }
               if(!!verifyLowerDefectNum) {
                   verifyDatas.push({
                       name: "低级",
                       value: verifyLowerDefectNum,
                       percent: verifyLowerDefectProportion,
                       itemStyle: {
                           color: "#3883E5"
                       }
                   });
               }
               if(!!verifyProposeDefectNum) {
                   verifyDatas.push({
                       name: "建议",
                       value: verifyProposeDefectNum,
                       percent: verifyProposeDefectProportion,
                       itemStyle: {
                           color: "#84B1EC"
                       }
                   });
               }

                if(!!deadlyDefectNum) {
                    datas.push({
                        name: "致命",
                        value: deadlyDefectNum,
                        percent: deadlyDefectProportion,
                        itemStyle: {
                            color: "#E65D4E"
                        }
                    });
                }
                if(!!srsDefectNum) {
                    datas.push({
                        name: "严重",
                        value: srsDefectNum,
                        percent: srsDefectProportion,
                        itemStyle: {
                            color: "#FF9900"
                        }
                    });
                }
                if(!!generalDefectNum) {
                    datas.push({
                        name: "一般",
                        value: generalDefectNum,
                        percent: generalDefectProportion,
                        itemStyle: {
                            color: "#FFDD30"
                        }
                    });
                }
                if(!!lowerDefectNum) {
                    datas.push({
                        name: "低级",
                        value: lowerDefectNum,
                        percent: lowerDefectProportion,
                        itemStyle: {
                            color: "#3883E5"
                        }
                    });
                }
                if(!!proposeDefectNum) {
                    datas.push({
                        name: "建议",
                        value: proposeDefectNum,
                        percent: proposeDefectProportion,
                        itemStyle: {
                            color: "#84B1EC"
                        }
                    });
                }

               var option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function (params) {
                            return params.name + " " + (params.value || 0) + " " + (params.data.percent || 0) + "%";
                        },
                        appendToBody: true,
                    },
                    legend: {
                        top: '2%',
                        left: 'right',
                        icon: sf.circleIcon
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: ['30%', '60%'],
                            center: ['20%', '55%'],
                            avoidLabelOverlap: true,
                            label: {
                                show: true,
                                position: 'center',
                                formatter: function (params) {
                                    return '待修复' + '\n' + repairBugNum;
                                },
                                fontSize: 14,
                                fontFamily: 'PingFang SC, PingFang SC-Regular',
                                lineHeight: 22,
                                color: '#555555',
                                fontWeight: 400,
                            },
                            emphasis: {
                                label: {
                                    show: false,
                                    fontSize: 20,
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: repairDatas
                        },
                        {
                            type: 'pie',
                            radius: ['30%', '60%'],
                            center: ['50%', '55%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: true,
                                position: 'center',
                                formatter: function (params) {
                                    return '待验证' + '\n' + verifyBugNum;
                                },
                                fontSize: 14,
                                fontFamily: 'PingFang SC, PingFang SC-Regular',
                                lineHeight: 22,
                                color: '#555555',
                                fontWeight: 400,
                            },
                            emphasis: {
                                label: {
                                    show: false,
                                    fontSize: 20,
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: verifyDatas
                        },
                        {
                            type: 'pie',
                            radius: ['30%', '60%'],
                            center: ['80%', '55%'],
                            avoidLabelOverlap: false,
                            label: {
                                show: true,
                                position: 'center',
                                formatter: function (params) {
                                    return '全部' + '\n' + bugNum;
                                },
                                fontSize: 14,
                                fontFamily: 'PingFang SC, PingFang SC-Regular',
                                lineHeight: 22,
                                color: '#555555',
                                fontWeight: 400,
                            },
                            emphasis: {
                                label: {
                                    show: false,
                                    fontSize: 20,
                                    fontWeight: 'bold'
                                }
                            },
                            labelLine: {
                                show: false
                            },
                            data: datas
                        },
                    ]
                };

                if (option && typeof option === 'object') {
                    myChart.setOption(option);
                }
            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChart.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                window.parent.postMessage({"defectClassifyBySeverityDetail": true,
                    "selectPrjId": prjId,
                     "defectClassifyBySeverityVo": sf.defectClassifyBySeverityVo}, '*');
            });
        },

        //查询需求存量/新增/完成趋势数据
        getDemandChangeTrend: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.reqTrendChartData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandChangeTrend",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.reqTrendChartData = data.data || [];
                        sf.drawReqTrendChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询需求存量/新增/完成趋势数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制需求存量/新增/完成趋势数据
        drawReqTrendChart: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_req_trend'));
            myChart.clear();

            if (!!sf.reqTrendChartData && sf.reqTrendChartData.length > 0) {

                var xAxisDates = [];

                var demandAddNumMap = {}; //新增
                var demandStockNumMap = {}; //存量
                var demandCompleteNumMap = {}; //完成

                var demandAddNums = []; //新增
                var demandStockNums = []; //存量
                var demandCompleteNums = []; //完成

                for (var i = 0; i < sf.reqTrendChartData.length; i++) {
                    var item = sf.reqTrendChartData[i];
                    xAxisDates.push({
                        value: item.currentDate,
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                    demandAddNumMap[item.currentDate] = item.demandAddNum || 0;
                    demandStockNumMap[item.currentDate] = item.demandStockNum || 0;
                    demandCompleteNumMap[item.currentDate] = -item.demandCompleteNum || 0;
                }

                for (var i = 0; i < xAxisDates.length; i++) {
                    demandAddNums.push({
                        value: demandAddNumMap[xAxisDates[i].value],
                    });
                    demandStockNums.push({
                        value: demandStockNumMap[xAxisDates[i].value],
                    });
                    demandCompleteNums.push({
                        value: demandCompleteNumMap[xAxisDates[i].value],
                    });
                }

                var option = {
                    backgroundColor: '#fff',
                    legend: {
                        data: ['存量', '新增', '完成'],
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            if (value < 0) {
                                return -value
                            }
                            return value;
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        data: xAxisDates, // x轴数据
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        }
                    },
                    yAxis: {
                        offset: 8,
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        axisLabel: {
                            show: true,
                            formatter: function (value, index) {
                                if (value < 0) {
                                    return -value
                                }
                                return value;
                            }
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    series: [
                        {
                            name: '存量',
                            type: 'line',
                            symbol: "none",
                            lineStyle: {
                                color: '#FFBB00',
                            },
                            itemStyle: {
                                color: '#FFBB00',
                            },
                            data: demandStockNums,
                            smooth: true,
                            areaStyle: {
                                opacity: 0.3,
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: '#FFBB00'
                                    },
                                    {
                                        offset: 1,
                                        color: '#FFFFFF'
                                    }
                                ])
                            },
                        },
                        {
                            name: '新增',
                            type: 'bar',
                            stack: 'one',
                            data: demandAddNums,
                            barWidth: 14,
                            itemStyle: {
                                color: "#6962d6"
                            }
                        },
                        {
                            name: '完成',
                            type: 'bar',
                            stack: 'one',
                            data: demandCompleteNums,
                            barWidth: 14,
                            itemStyle: {
                                color: "#65ce7a"
                            }
                        }
                    ]
                };

                if (option && typeof option === 'object') {
                    myChart.setOption(option);
                }
            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }
            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChart.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                if (myChart.containPixel('grid', pointInPixel)) {
                    window.parent.postMessage({
                        "demandChangeTrendDetail": true,
                        "selectPrjId": sf.selectPrjId.split('-')[0],
                        selectDatePeriod: sf.selectDatePeriod
                    }, '*');
                }
            });
        },

        //查询缺陷存量/新增/完成趋势数据
        getDefectChangeTrend: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            if (!sf.selectDatePeriod || sf.selectDatePeriod.length < 2 || !sf.selectDatePeriod[0] || !sf.selectDatePeriod[1]) {
                sf.$Message.info({
                    content: "请先选择周期时间！",
                    duration: 8
                });
                return;
            }
            sf.bugTrendChartData = [];
            var vo = {
                prjId: sf.selectPrjId.split('-')[0],
                startDate: new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd"),
                endDate: new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd"),
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDefectChangeTrend",
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                type: 'post',
                data: JSON.stringify(vo),
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        sf.bugTrendChartData = data.data || [];
                        sf.drawBugTrendChart();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询缺陷存量/新增/完成趋势数据错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制缺陷存量/新增/完成趋势数据
        drawBugTrendChart: function () {
            var sf = this;

            var myChart = null;
            myChart = echarts.init(document.getElementById('echarts_bug_trend'));
            myChart.clear();

            if (!!sf.bugTrendChartData && sf.bugTrendChartData.length > 0) {

                var xAxisDates = [];

                var defectAddNumMap = {}; //新增
                var defectStockNumMap = {}; //存量
                var defectCompleteNumMap = {}; //完成

                var defectAddNums = []; //新增
                var defectStockNums = []; //存量
                var defectCompleteNums = []; //完成

                for (var i = 0; i < sf.bugTrendChartData.length; i++) {
                    var item = sf.bugTrendChartData[i];
                    xAxisDates.push({
                        value: item.currentDate,
                        textStyle: {
                            color: '#262626',
                            fontWeight: 400,
                            fontFamily: 'PingFang SC, PingFang SC-Regular',
                            padding: [12, 0, 12, 0],
                        }
                    });
                    defectAddNumMap[item.currentDate] = item.defectAddNum || 0;
                    defectStockNumMap[item.currentDate] = item.defectStockNum || 0;
                    defectCompleteNumMap[item.currentDate] = -item.defectCompleteNum || 0;
                }

                for (var i = 0; i < xAxisDates.length; i++) {
                    defectAddNums.push({
                        value: defectAddNumMap[xAxisDates[i].value],
                    });
                    defectStockNums.push({
                        value: defectStockNumMap[xAxisDates[i].value],
                    });
                    defectCompleteNums.push({
                        value: defectCompleteNumMap[xAxisDates[i].value],
                    });
                }

                var option = {
                    backgroundColor: '#fff',
                    legend: {
                        data: ['存量', '新增', '完成'],
                        align: 'left',
                        right: 10,
                        icon: sf.circleIcon
                    },
                    tooltip: {
                        trigger: "axis",
                        extraCssText: 'z-index: 1;',
                        valueFormatter: function (value) {
                            if (value < 0) {
                                return -value
                            }
                            return value;
                        },
                        appendToBody: true,
                    },
                    xAxis: {
                        data: xAxisDates, // x轴数据
                        axisTick: {
                            show: false //隐藏刻度线
                        },
                        splitLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        }
                    },
                    yAxis: {
                        offset: 8,
                        splitLine: {
                            lineStyle: {
                                type: 'dashed',
                                width: 0.5
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: "#262626",
                            }
                        },
                        axisLabel: {
                            show: true,
                            formatter: function (value, index) {
                                if (value < 0) {
                                    return -value
                                }
                                return value;
                            }
                        }
                    },
                    grid: {
                        left: '2%',
                        right: '3%',
                        bottom: '18%',
                        top: '12%',
                        borderWidth: 0,
                        containLabel: true
                    },
                    series: [
                        {
                            name: '存量',
                            type: 'line',
                            symbol: "none",
                            lineStyle: {
                                color: '#FFBB00',
                            },
                            itemStyle: {
                                color: '#FFBB00',
                            },
                            data: defectStockNums,
                            smooth: true,
                            areaStyle: {
                                opacity: 0.3,
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    {
                                        offset: 0,
                                        color: '#FFBB00'
                                    },
                                    {
                                        offset: 1,
                                        color: '#FFFFFF'
                                    }
                                ])
                            },
                        },
                        {
                            name: '新增',
                            type: 'bar',
                            stack: 'one',
                            data: defectAddNums,
                            barWidth: 14,
                            itemStyle: {
                                color: "#6962d6"
                            }
                        },
                        {
                            name: '完成',
                            type: 'bar',
                            stack: 'one',
                            data: defectCompleteNums,
                            barWidth: 14,
                            itemStyle: {
                                color: "#65ce7a"
                            }
                        }
                    ]
                };

                if (option && typeof option === 'object') {
                    myChart.setOption(option);
                }
            } else {
                var option = {
                    title: {
                        text: '暂无数据',
                        x: 'center',
                        y: 'center',
                        textStyle: {
                            fontSize: 12,
                            fontWeight: 'normal',
                            color: '#495060',
                        }
                    }
                };
                myChart.setOption(option);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChart.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                if (myChart.containPixel('grid', pointInPixel)) {
                    window.parent.postMessage({
                        "bugChangeTrendDetail": true,
                        "selectPrjId": prjId,
                        selectDatePeriod: sf.selectDatePeriod
                    }, '*');
                }
            });
        },

        //查询项目人员绩效
        getPrjPerformanceList: function() {
            var sf = this;
            if(sf.selectedBtn === 'sum') {
                sf.prjPerformanceDatas = [];
                return;
            }
            if(!!sf.isPrjEffectEvalutionAdmin) {
                if (!sf.selectPrjId) {
                    sf.$Message.info({
                        content: "请先选择项目集！",
                        duration: 8
                    });
                    return;
                }
            }
            if(!sf.selectMeasureStartMonth || !sf.selectMeasureEndMonth) {
                sf.$Message.warning({
                    content: "度量开始月份和度量结束月份不能为空！",
                    duration: 3
                })
                return;
            }
            sf.prjPerformanceDatas = [];
            sf.prjPerformanceTableDataTotal = 0;
            sf.prjPerformanceLoading = true;
            var param = sf.getPrjPerformanceListParams(true);
            $.ajax({
                url: linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/queryPfmIndex',
                headers: {'Content-Type' : 'application/json;charset=utf-8'},
                method: 'post',
                data: JSON.stringify(param),
                success: function (res) {
                    sf.prjPerformanceLoading = false;
                    if(res.success) {
                        sf.prjPerformanceColumns = [];
                        var displayColList = res.data.displayColList || [];
                        sf.prjPerformanceColumns = sf.generatePrjPerformanceColumns(displayColList, false);
                        if(!!sf.prjPerformanceColumns && sf.prjPerformanceColumns.length > 0) {
                            sf.prjPerformanceColumns.unshift({
                                title: '序',
                                type: 'index',
                                width: 60,
                                align: 'center'
                            });
                        }
                        sf.prjPerformanceDatas = (!!res.data.pfmIndexData && JSON.stringify(res.data.pfmIndexData) != "{}") ? (res.data.pfmIndexData.objectList || []) : [];
                        sf.prjPerformanceTableDataTotal = (!!res.data.pfmIndexData && JSON.stringify(res.data.pfmIndexData) != "{}") ? (res.data.pfmIndexData.count || 0) : 0;
                    }else{
                        sf.$Message.error(res.message)
                    }
                },
                error: function (res) {
                    sf.prjPerformanceLoading = false;
                    sf.$Message.error({
                        content: "查询项目人员绩效失败，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        onPrjPerformanceTablePageNumChange: function(pageNum) {
            var sf = this;
            sf.prjPerformanceTablePageNum = pageNum - 1;
            sf.getPrjPerformanceList();
        },
        onPrjPerformanceTablePageSizeChange: function(pageSize) {
            var sf = this;
            sf.prjPerformanceTablePageNum = 0;
            sf.prjPerformanceTablePageSize = pageSize;
            sf.getPrjPerformanceList();
        },

        //组装参数
        getPrjPerformanceListParams: function(isPage) {
            var sf = this;
            var param = {
                msmtSysTypeId:  "67808aea8a13e55b777f7713",
                isSourceEip: true,
                isRankAsc: sf.isRankAsc
            };
            if(isPage) {
                param.pageNum = sf.prjPerformanceTablePageNum;
                param.pageSize = sf.prjPerformanceTablePageSize;
            };
            param.msmtSysId = "67d7dad41ffa7e9c09072f8a";
            if(!!sf.selectPrjId) {
                param.prjIds = [sf.selectPrjId.split('-')[0]];
            }
            if(sf.selectedTypeBtn == "month") {
                param.isTrial = false;
            }else if(sf.selectedTypeBtn == "total") {
                param.isTrial = true;
            }
            if(sf.selectedBtn === 'detail') {
                if(!!sf.selectMeasureStartMonth && !!sf.selectMeasureEndMonth) {
                    if(sf.selectedTypeBtn == "month") {
                        param.yms = [];
                        param.yms.push(new Date(sf.selectMeasureStartMonth).format("yyyy-MM"));
                        param.yms.push(new Date(sf.selectMeasureEndMonth).format("yyyy-MM"));
                    }else if(sf.selectedTypeBtn == "total") {
                        param.trialYms = [];
                        param.trialYms.push(new Date(sf.selectMeasureStartMonth).format("yyyy-MM"));
                        param.trialYms.push(new Date(sf.selectMeasureEndMonth).format("yyyy-MM"));
                    }
                }
            }else if(sf.selectedBtn === 'sum') {
            }
            var deptIds = [];
            param.deptIds = deptIds;
            if(sf.selectedNode == "devUser") {
                param.roleId = sf.devUserRoleId;
            }else if(sf.selectedNode == "testUser") {
                param.roleId = sf.testUserRoleId;
            }else if(sf.selectedNode == "reqUser") {
                param.roleId = sf.reqUserRoleId;
            }
            return param;
        },

        //选择度量开始月份
        changeMeasureStartMonth: function() {
            var sf = this;
            if(!!sf.selectMeasureStartMonth && !!sf.selectMeasureEndMonth) {
                if(new Date(sf.selectMeasureStartMonth).getTime() > new Date(sf.selectMeasureEndMonth).getTime()) {
                    sf.$Message.warning({
                        content: '度量开始月份不能大于度量结束月份！',
                        duration: 3
                    });
                    sf.selectMeasureStartMonth = null;
                    return;
                }
            }
            sf.prjPerformanceTablePageNum = 0;
            /*sf.getPrjPerformanceList();*/
        },

        //选择度量结束月份
        changeMeasureEndMonth: function() {
            var sf = this;
            if(!!sf.selectMeasureStartMonth && !!sf.selectMeasureEndMonth) {
                if(new Date(sf.selectMeasureStartMonth).getTime() > new Date(sf.selectMeasureEndMonth).getTime()) {
                    sf.$Message.warning({
                        content: '度量开始月份不能大于度量结束月份！',
                        duration: 3
                    });
                    sf.selectMeasureEndMonth = null;
                    return;
                }
            }
            sf.prjPerformanceTablePageNum = 0;
           /* sf.getPrjPerformanceList();*/
        },

        //导出项目人员绩效
        exportPrjPerformanceList: function() {
            var sf = this;
            if(sf.selectedBtn === 'sum') {
                return;
            }
            if(!!sf.isPrjEffectEvalutionAdmin) {
                if (!sf.selectPrjId) {
                    sf.$Message.info({
                        content: "请先选择项目集！",
                        duration: 8
                    });
                    return;
                }
            }
            if(!sf.selectMeasureStartMonth || !sf.selectMeasureEndMonth) {
                sf.$Message.warning({
                    content: "度量开始月份和度量结束月份不能为空！",
                    duration: 3
                })
                return;
            }
            sf.$Message.warning({
                content: '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration: 3
            });
            var param = sf.getPrjPerformanceListParams();

            var xhr = new XMLHttpRequest();
            var url = linkus.location.rsc + '/linkus-pfm/pfmIndexCtrl/exportReqPlanManager';
            xhr.open('POST', url, true);
            xhr.responseType = 'blob'; // 设置响应类型为 Blob
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.send(JSON.stringify(param));
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    var blob = xhr.response; // 获取 Blob 数据
                    var roleName = "";
                    if(sf.selectedNode == "devUser") {
                        roleName = "_开发人员度量";
                    }else if(sf.selectedNode == "testUser") {
                        roleName = "_测试人员度量";
                    }else if(sf.selectedNode == "reqUser") {
                        roleName = "_需求人员度量";
                    }
                    sf.blobDataExport(blob, sf.msmtSysName + roleName + '.xlsx');
                } else {
                    sf.$Message.error({
                        content: "导出项目人员绩效失败，请联系管理员！",
                        duration: 8
                    });
                }
            };
            xhr.onerror = function() {
                sf.$Message.error({
                    content: "导出项目人员绩效失败，请联系管理员！",
                    duration: 8
                });
            };
        },

        // blob数据导出
        blobDataExport: function(blob, fileName) {
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        },

        //切换角色
        changeNode: function (type) {
            var sf = this;
            sf.selectedNode = type;
            sf.prjPerformanceTablePageNum = 0;
            sf.getPrjPerformanceList();
        },

        //切换明细/汇总
        changeBtn: function (type) {
            var sf = this;
            sf.selectedBtn = type;
            sf.prjPerformanceTablePageNum = 0;
            sf.getPrjPerformanceList();
        },

        //切换按月/累计
        changeTypeBtn: function (type) {
            var sf = this;
            sf.selectedTypeBtn = type;
            sf.prjPerformanceTablePageNum = 0;
            sf.getPrjPerformanceList();
        },

        //项目人员绩效报表排序
        prjPerformanceTableSortChange: function (column) {
            var sf = this;
            if (column.order == "asc") {
                sf.isRankAsc = true;
            } else if (column.order == "desc") {
                sf.isRankAsc = false;
            }
            sf.getPrjPerformanceList();
        },

        //组装项目人员绩效表格列
        generatePrjPerformanceColumns: function(data, isChildren) {
            var sf = this;
            var getWidth = function(length, title, children) {
                if(title == '角色') {
                    return 70;
                }else if(title == '人员') {
                    return 110;
                }else if(title == '任务/工时情况') {
                    return 100;
                }else if(title == '员工类型') {
                    return 90;
                }else if(title == '人员系数') {
                    return 50;
                }else if(title == '度量状态') {
                    return 50;
                }else if(title == '在职状态') {
                    return 70;
                }else if(title == '工时情况') {
                    return 70;
                }else if(title == '项目型_开发人员度量(全BSC)' && !children){
                    return 120;
                }else {
                    var lengthWidth = 30 * length;
                    if(lengthWidth < 82) {
                        return 82;
                    }else if(lengthWidth > 200) {
                        return 200;
                    }else{
                        return lengthWidth
                    }
                }
            }

            return data.map(function(item) {
                var params = {};
                if(item.title == "排名") {
                    params = {
                        title: item.title,
                        key: item.key,
                        width: 70 ,
                        align: 'center',
                        sortable: 'custom',
                        sortType: !!sf.isRankAsc ? "asc" : "desc",
                    };
                }else {
                    params = {
                        title: item.title,
                        key: item.key,
                        width: getWidth(item.title.length, item.title, item.children) ,
                        align: 'center'
                    }
                }
                if(!!item.children && item.children.length > 0) {
                    params.children = sf.generatePrjPerformanceColumns(item.children, true);
                }
                return params;
            });
        },

        //查询页面数据
        queryData: function () {
            var sf = this;
            if(sf.tabName == "prjNoticeBoard") {
                sf.getPrjProgQualReport();
                sf.getDemandOverView();
                sf.getBugOverView();
                sf.getDefectChangeTrend();
                sf.getDemandChangeTrend();
                sf.getDefectClassifyBySeverity();
                sf.getDemandReviewCoverageRate();
                sf.getDevTaskDefectDensity();
                sf.getDevUnRepairedDefectReport();
                sf.getTestUnVerifyDefectReport();
                sf.getStoreTaskRankByUser();
                sf.getDefectRankByUser();
                sf.getDevTaskList();
                sf.getTestTaskList();
                sf.getTestCaseList();
                sf.getTaskPromptness();
                sf.getTaskCompletionReport();
                sf.getReqTimelinessRate();
                sf.getBugTimelinessRate();
                sf.getBugTimelinessRateReport();
                sf.getBugFallbackRateChart();
            }else if(sf.tabName == "prjUserPerformance") {
                sf.getPrjPerformanceList();
            }else if(sf.tabName == "digitalProductionEvaluation") {
                sf.queryPrjDigitalInfo();
            }
        },

        //查询周期时间
        getCircleTime: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            sf.selectDatePeriod = [];
            var startDate = new Date().format("yyyy-MM") + "-01";
            var endDate = new Date().format("yyyy-MM-dd");
            sf.selectDatePeriod.push(startDate);
            sf.selectDatePeriod.push(endDate);
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getCircleTime",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0]
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        if (!!data.data && data.data.length == 2 && !!data.data[0] && !!data.data[1]) {
                            sf.selectDatePeriod = [];
                            sf.selectDatePeriod = data.data;
                        }
                    }
                    if (!!sf.selectDatePeriod && sf.selectDatePeriod.length == 2) {
                        window.parent.postMessage({
                            "condChanged": true, "condParam": {
                                "selectProvIds": sf.provFltList,
                                "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                                "selectPrjStatusIds": sf.selectPrjStatuses,
                                "selectYear": sf.selectYear,
                                "selectPrjId": sf.selectPrjId.split('-')[0],
                                "selectDatePeriod": sf.selectDatePeriod
                            }
                        }, '*');
                        sf.startDate = (new Date(sf.selectDatePeriod[0]).format("yyyy-MM-dd")) + " 00:00:00";
                        sf.endDate = (new Date(sf.selectDatePeriod[1]).format("yyyy-MM-dd")) + " 23:59:59";
                    }
                    if(sf.tabName == "prjNoticeBoard") {
                        sf.getPrjProgQualReport();
                        sf.getDefectChangeTrend();
                        sf.getDemandChangeTrend();
                        sf.getDemandReviewCoverageRate();
                        sf.getDevTaskDefectDensity();
                        sf.getTaskPromptness();
                        sf.getTaskCompletionReport();
                        sf.getBugTimelinessRateReport();
                        sf.getBugFallbackRateChart();
                        sf.getDevUnRepairedDefectReport();
                        sf.getTestUnVerifyDefectReport();
                        sf.getDevTaskList();
                        sf.getTestTaskList();
                        sf.getTestCaseList();
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询周期时间错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //项目度量-需求概览
        getDemandOverView: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getDemandOverView",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0]
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        var demandData = data.data;

                        sf.devReqNum = demandData.devReqNum || 0; //开发中
                        sf.devReqDelayNum = demandData.devReqDelayNum || 0; //开发超期
                        sf.testReqNum = demandData.testReqNum || 0; //测试中
                        sf.testReqDelayNum = demandData.testReqDelayNum || 0; //测试超期

                        sf.drawDemandOverView(demandData);
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询项目度量-需求概览错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制项目度量-需求概览仪表盘
        drawDemandOverView: function (data) {
            var sf = this;
            var myChartReq = null;
            myChartReq = echarts.init(document.getElementById('echarts_req'));
            myChartReq.clear();

            var reqCompleteRate = data.reqCompleteRate || 0;
            var taskCompleteRate = data.taskCompleteRate || 0;
            var reqCompleteRateColor = "";
            var taskCompleteRateColor = "";

            var totalReqNum = data.totalReqNum || 0;
            var totalTaskNum = data.totalTaskNum || 0;
            var completedReqNum = data.completedReqNum || 0;
            var completedTaksNum = data.completedTaksNum || 0;

            if (parseFloat(reqCompleteRate).toFixed(0) < 50) {
                reqCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(reqCompleteRate).toFixed(0) >= 50 && parseFloat(reqCompleteRate).toFixed(0) <= 80) {
                reqCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(reqCompleteRate).toFixed(0) > 80) {
                reqCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            if (parseFloat(taskCompleteRate).toFixed(0) < 50) {
                taskCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(taskCompleteRate).toFixed(0) >= 50 && parseFloat(taskCompleteRate).toFixed(0) <= 80) {
                taskCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(taskCompleteRate).toFixed(0) > 80) {
                taskCompleteRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            var optionReq = null;
            optionReq = {
                tooltip: {
                    formatter: "总数：" + totalReqNum + "</br>已完成：" + completedReqNum,
                    trigger: 'item',
                    appendToBody: true,
                },
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: reqCompleteRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: '#C8CDDC'
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: '#C8CDDC',
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: reqCompleteRateColor,
                                borderColor: reqCompleteRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: reqCompleteRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '40%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-35%'],
                            formatter: "{value}%",
                            color: reqCompleteRateColor
                        },
                        data: [
                            {
                                value: parseFloat(reqCompleteRate).toFixed(0),
                                name: "需求完成率"
                            }
                        ]
                    }
                ]
            };
            if (optionReq && typeof optionReq === 'object') {
                myChartReq.setOption(optionReq);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChartReq.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

            var myCharTask = null;
            myCharTask = echarts.init(document.getElementById('echarts_task'));
            myCharTask.clear();

            var optionTask = null;
            optionTask = {
                tooltip: {
                    formatter: "总数：" + totalTaskNum + "</br>已完成：" + completedTaksNum,
                    trigger: 'item',
                    appendToBody: true,
                },
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: taskCompleteRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: "#C8CDDC"
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: "#C8CDDC",
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: taskCompleteRateColor,
                                borderColor: taskCompleteRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: taskCompleteRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '40%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-35%'],
                            formatter: "{value}%",
                            color: taskCompleteRateColor
                        },
                        data: [
                            {
                                value: parseFloat(taskCompleteRate).toFixed(0),
                                name: "任务完成率"
                            }
                        ]
                    }
                ]
            };
            if (optionTask && typeof optionTask === 'object') {
                myCharTask.setOption(optionTask);
            }

            // echart全图鼠标点击事件
            myCharTask.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

        },

        //项目度量-缺陷概览
        getBugOverView: function (data) {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getBugOverView",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0]
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        var bugData = data.data;

                        sf.repairNum = bugData.repairNum || 0; //修复中
                        sf.verifyNum = bugData.verifyNum || 0; //验证中
                        sf.repairDelayNum = bugData.repairDelayNum || 0; //超期未修复
                        sf.verifyDelayNum = bugData.verifyDelayNum || 0; //超期未验证

                        sf.drawBugOverView(bugData);
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询项目度量-缺陷概览错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制项目度量-缺陷概览仪表盘
        drawBugOverView: function (data) {
            var sf = this;
            var myChartEscape = null;
            myChartEscape = echarts.init(document.getElementById('echarts_escape'));
            myChartEscape.clear();

            var rwBugRate = data.rwBugRate || 0;
            var bugResRate = data.bugResRate || 0;
            var rwBugRateColor = "";
            var bugResRateColor = "";

            var totalBugNum = data.totalBugNum || 0;
            var rwBugNum = data.rwBugNum || 0;
            var completedBugNum = data.completedBugNum || 0;

            if (parseFloat(rwBugRate).toFixed(0) < 7) {
                rwBugRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(rwBugRate).toFixed(0) >= 7 && parseFloat(rwBugRate).toFixed(0) <= 10) {
                rwBugRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#FF9900' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#FFBB00' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(rwBugRate).toFixed(0) > 10) {
                rwBugRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            if (parseFloat(bugResRate).toFixed(0) < 50) {
                bugResRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(bugResRate).toFixed(0) >= 50 && parseFloat(bugResRate).toFixed(0) <= 80) {
                bugResRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(bugResRate).toFixed(0) > 80) {
                bugResRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            var optionEscape = null;
            optionEscape = {
                tooltip: {
                    formatter: "总数：" + totalBugNum + "</br>逃逸：" + rwBugNum,
                    trigger: 'item',
                    appendToBody: true,
                },
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: rwBugRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: "#C8CDDC"
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: "#C8CDDC",
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: rwBugRateColor,
                                borderColor: rwBugRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: rwBugRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '40%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-35%'],
                            formatter: "{value}%",
                            color: rwBugRateColor,
                        },
                        data: [
                            {
                                value: rwBugRate,
                                name: "缺陷逃逸率"
                            }
                        ]
                    }
                ]
            };
            if (optionEscape && typeof optionEscape === 'object') {
                myChartEscape.setOption(optionEscape);
            }

            var myCharSolve = null;
            myCharSolve = echarts.init(document.getElementById('echarts_solve'));
            myCharSolve.clear();

            var optionSolve = null;
            optionSolve = {
                tooltip: {
                    formatter: "总数：" + totalBugNum + "</br>已解决：" + completedBugNum,
                    trigger: 'item',
                    appendToBody: true,
                },
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: bugResRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: "#C8CDDC"
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: "#C8CDDC",
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: bugResRateColor,
                                borderColor: bugResRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: bugResRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '40%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-35%'],
                            formatter: "{value}%",
                            color: bugResRateColor
                        },
                        data: [
                            {
                                value: parseFloat(bugResRate).toFixed(0),
                                name: "缺陷解决率"
                            }
                        ]
                    }
                ]
            };
            if (optionSolve && typeof optionSolve === 'object') {
                myCharSolve.setOption(optionSolve);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChartEscape.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY]
                window.parent.postMessage({
                    "bugEscapeRateDetail": true,
                    "selectPrjId": prjId,
                }, '*');
            });

            // echart全图鼠标点击事件
            myCharSolve.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

        },

        //查询需求录入及时率和任务及时率
        getReqTimelinessRate: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            sf.reqTimelinessRateErrorMessage = "";
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getReqTimelinessRate",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0]
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}") {
                        if(!!data.success) {
                            var reqTimelinessRateData = data.data;
                            sf.drawReqTimelinessRate(reqTimelinessRateData);
                        }else {
                            sf.reqTimelinessRateErrorMessage = data.message || "";
                        }
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询需求录入及时率和任务及时率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制需求录入及时率和任务及时率仪表盘
        drawReqTimelinessRate: function (data) {
            var sf = this;
            var myChartReq = null;
            myChartReq = echarts.init(document.getElementById('echarts_req_timeliness'));
            myChartReq.clear();

            var reqTimelinessRate = data.reqTimelinessRate || 0;
            var taskTimelinessRate = data.taskTimelinessRate || 0;
            var reqTimelinessRateColor = "";
            var taskTimelinessRateColor = "";

            /*var totalReqNum = data.totalReqNum || 0;
                var totalTaskNum = data.totalTaskNum || 0;
                var completedReqNum = data.completedReqNum || 0;
                var completedTaksNum = data.completedTaksNum || 0;*/

            if (parseFloat(reqTimelinessRate).toFixed(0) < 50) {
                reqTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(reqTimelinessRate).toFixed(0) >= 50 && parseFloat(reqTimelinessRate).toFixed(0) <= 80) {
                reqTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(reqTimelinessRate).toFixed(0) > 80) {
                reqTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            if (parseFloat(taskTimelinessRate).toFixed(0) < 50) {
                taskTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(taskTimelinessRate).toFixed(0) >= 50 && parseFloat(taskTimelinessRate).toFixed(0) <= 80) {
                taskTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(taskTimelinessRate).toFixed(0) > 80) {
                taskTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            var optionReq = null;
            optionReq = {
                /*tooltip: {
                        formatter:  "总数：" + totalReqNum + "</br>已完成：" + completedReqNum,
                        trigger: 'item',
                        appendToBody: true,
                    },*/
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: reqTimelinessRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: '#C8CDDC'
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: '#C8CDDC',
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: reqTimelinessRateColor,
                                borderColor: reqTimelinessRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: reqTimelinessRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '35%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-40%'],
                            formatter: "{value}%",
                            color: reqTimelinessRateColor
                        },
                        data: [
                            {
                                value: parseFloat(reqTimelinessRate).toFixed(0),
                                name: "需求录入及时率"
                            }
                        ]
                    }
                ]
            };
            if (optionReq && typeof optionReq === 'object') {
                myChartReq.setOption(optionReq);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChartReq.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

            var myCharTask = null;
            myCharTask = echarts.init(document.getElementById('echarts_task_timeliness'));
            myCharTask.clear();

            var optionTask = null;
            optionTask = {
                /*tooltip: {
                        formatter:  "总数：" + totalTaskNum + "</br>已完成：" + completedTaksNum,
                        trigger: 'item',
                        appendToBody: true,
                    },*/
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: taskTimelinessRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: "#C8CDDC"
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: "#C8CDDC",
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: taskTimelinessRateColor,
                                borderColor: taskTimelinessRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: taskTimelinessRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '35%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-40%'],
                            formatter: "{value}%",
                            color: taskTimelinessRateColor
                        },
                        data: [
                            {
                                value: parseFloat(taskTimelinessRate).toFixed(0),
                                name: "任务及时率"
                            }
                        ]
                    }
                ]
            };
            if (optionTask && typeof optionTask === 'object') {
                myCharTask.setOption(optionTask);
            }

            // echart全图鼠标点击事件
            myCharTask.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

        },

        //查询缺陷修复及时率和缺陷验证及时率
        getBugTimelinessRate: function () {
            var sf = this;
            if (!sf.selectPrjId) {
                sf.$Message.info({
                    content: "请先选择项目集！",
                    duration: 8
                });
                return;
            }
            $.ajax({
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/getBugTimelinessRate",
                type: 'post',
                data: {
                    prjId: sf.selectPrjId.split('-')[0]
                },
                dataType: "json",
                success: function (data) {
                    if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                        var bugTimelinessRateData = data.data;
                        sf.drawBugTimelinessRate(bugTimelinessRateData);
                    }
                },
                error: function (data) {
                    sf.$Message.info({
                        content: "查询缺陷修复及时率和缺陷验证及时率错误，请联系管理员！",
                        duration: 8
                    });
                }
            });
        },

        //绘制缺陷修复及时率和缺陷验证及时率仪表盘
        drawBugTimelinessRate: function (data) {
            var sf = this;
            var myChartRepair = null;
            myChartRepair = echarts.init(document.getElementById('echarts_bug_repair'));
            myChartRepair.clear();

            var repairTimelinessRate = data.repairTimelinessRate || 0;
            var verifyTimelinessRate = data.verifyTimelinessRate || 0;
            var repairTimelinessRateColor = "";
            var verifyTimelinessRateColor = "";

            /*var totalReqNum = data.totalReqNum || 0;
                var totalTaskNum = data.totalTaskNum || 0;
                var completedReqNum = data.completedReqNum || 0;
                var completedTaksNum = data.completedTaksNum || 0;*/

            if (parseFloat(repairTimelinessRate).toFixed(0) < 50) {
                repairTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(repairTimelinessRate).toFixed(0) >= 50 && parseFloat(repairTimelinessRate).toFixed(0) <= 80) {
                repairTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(repairTimelinessRate).toFixed(0) > 80) {
                repairTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            if (parseFloat(verifyTimelinessRate).toFixed(0) < 50) {
                verifyTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#E65D4E' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#EE9086' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(verifyTimelinessRate).toFixed(0) >= 50 && parseFloat(verifyTimelinessRate).toFixed(0) <= 80) {
                verifyTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#3883E5' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#84B1EC' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            } else if (parseFloat(verifyTimelinessRate).toFixed(0) > 80) {
                verifyTimelinessRateColor = {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 1,
                    colorStops: [{
                        offset: 0, color: '#65CE7A' // 0% 处的颜色
                    }, {
                        offset: 1, color: '#87F19C' // 100% 处的颜色
                    }],
                    global: false // 缺省为 false
                };
            }

            var optionRepair = null;
            optionRepair = {
                /*tooltip: {
                        formatter:  "总数：" + totalReqNum + "</br>已完成：" + completedReqNum,
                        trigger: 'item',
                        appendToBody: true,
                    },*/
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: repairTimelinessRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: '#C8CDDC'
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: '#C8CDDC',
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: repairTimelinessRateColor,
                                borderColor: repairTimelinessRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: repairTimelinessRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '35%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-40%'],
                            formatter: "{value}%",
                            color: repairTimelinessRateColor
                        },
                        data: [
                            {
                                value: parseFloat(repairTimelinessRate).toFixed(0),
                                name: "缺陷修复及时率"
                            }
                        ]
                    }
                ]
            };
            if (optionRepair && typeof optionRepair === 'object') {
                myChartRepair.setOption(optionRepair);
            }

            var prjId = null;
            sf.selectPrjId ? prjId = sf.selectPrjId.split('-')[0] : null;

            // echart全图鼠标点击事件
            myChartRepair.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

            var myCharVerify = null;
            myCharVerify = echarts.init(document.getElementById('echarts_bug_verify'));
            myCharVerify.clear();

            var optionVerify = null;
            optionVerify = {
                /*tooltip: {
                        formatter:  "总数：" + totalTaskNum + "</br>已完成：" + completedTaksNum,
                        trigger: 'item',
                        appendToBody: true,
                    },*/
                series: [
                    {
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 15,
                            itemStyle: {
                                color: verifyTimelinessRateColor
                            }
                        },
                        radius: "100%",
                        center: ["50%", "50%"],
                        axisLine: {
                            lineStyle: {
                                width: 15
                            }
                        },
                        axisTick: {
                            show: true,
                            lineStyle: {
                                color: "#C8CDDC"
                            }
                        },
                        splitLine: {
                            length: 16,
                            lineStyle: {
                                width: 2,
                                color: "#C8CDDC"
                            }
                        },
                        axisLabel: {
                            distance: 27,
                            color: "#C8CDDC",
                            fontSize: 20,
                            show: false
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 6,
                            itemStyle: {
                                borderWidth: 6,
                                color: verifyTimelinessRateColor,
                                borderColor: verifyTimelinessRateColor,
                            }
                        },
                        pointer: {
                            width: 5,
                            itemStyle: {
                                color: verifyTimelinessRateColor
                            }
                        },
                        title: {
                            show: true,
                            offsetCenter: [0, '35%'],
                            color: '#999',
                            fontSize: 14,
                        },
                        endAngle: -3,
                        startAngle: 181,
                        detail: {
                            valueAnimation: true,
                            fontSize: 14,
                            offsetCenter: [0, '-40%'],
                            formatter: "{value}%",
                            color: verifyTimelinessRateColor
                        },
                        data: [
                            {
                                value: parseFloat(verifyTimelinessRate).toFixed(0),
                                name: "缺陷验证及时率"
                            }
                        ]
                    }
                ]
            };
            if (optionVerify && typeof optionVerify === 'object') {
                myCharVerify.setOption(optionVerify);
            }

            // echart全图鼠标点击事件
            myCharVerify.getZr().on('click', function (params) {
                var pointInPixel = [params.offsetX, params.offsetY];
                window.parent.postMessage({
                    "openPrjTargetSummary": true,
                    "condParam": {
                        "selectProvIds": sf.provFltList,
                        "selectPrjMgtTypeIds": sf.selectPrjMgtTypes,
                        "selectPrjStatusIds": sf.selectPrjStatuses,
                        "selectYear": sf.selectYear,
                        "selectPrjId": prjId,
                        "selectDatePeriod": sf.selectDatePeriod
                    }
                }, '*');
            });

        },

        // 查询项目集信息
        queryPrjInfo: function (keyword) {
            var sf = this;
            var vo = {};
            if (!!sf.selectProv) {
                vo["provId"] = sf.selectProv;
            }
            if (!!sf.selectPrjMgtTypes && sf.selectPrjMgtTypes.length > 0) {
                vo["prjMgtTypeIds"] = sf.selectPrjMgtTypes;
            }
            if (!!sf.selectPrjStatuses && sf.selectPrjStatuses.length > 0) {
                vo["statusIds"] = sf.selectPrjStatuses;
            }
            if (!!sf.selectYear) {
                vo["year"] = new Date(sf.selectYear).format("yyyy");
            }
            vo["prjNameOrCode"] = "";
            if(!!keyword && !!keyword.trim()) {
                if(!!sf.selectPrjId && sf.selectPrjId == keyword.trim()) {
                    return;
                }
                vo["prjNameOrCode"] = keyword.trim();
            }

            if (sf.queryPrjAjax && sf.queryPrjAjax.abort) {
                sf.queryPrjAjax.abort();
            }
            sf.searchPrjList = [];
            sf.queryPrjLoading = true;
            //sf.selectPrjId = null;
            sf.queryPrjAjax = $.ajax({
                type: 'post',
                headers: {'Content-Type': 'application/json;charset=utf-8'},
                url: linkus.location.report + "/bscEffectMeasure/prjMeasure/listPrjInfoByIds",
                data: JSON.stringify(vo),
                dataType: "json",
            });
            sf.queryPrjAjax.then(function (data) {
                sf.queryPrjLoading = false;
                if (!!data && JSON.stringify(data) != "{}" && !!data.success) {
                    //sf.searchPrjList = data.data || [];
                    if(data.data.length > 0){
                        let keyWord = keyword || '';
                        sf.searchPrjList = data.data.map(function(item){
                            let item0 = {
                                cid: item.cid + '-' + keyWord,
                                codeName: item.codeName,
                                name: item.name
                            }
                            return item0;
                        })
                    }
                    /*if (!!sf.searchPrjList && sf.searchPrjList.length > 0) {
                        sf.selectPrjId = sf.searchPrjList[0].cid;
                        sf.onPrjChanged();
                    } else {
                        sf.$Message.info({
                            content: "您不在任何项目中，无法看到数据！",
                            duration: 88
                        });
                    }*/
                }
            }, function (data) {
                sf.queryPrjLoading = false;
                if (!data || JSON.stringify(data) == "{}" || !data.statusText || data.statusText != "abort") {
                    sf.$Message.error({
                        content: "查询项目集错误，请联系管理员！",
                        duration: 5
                    });
                }
            });
        },

    },

});
