Vue.component('report-library-header', {
    template:
        '<div class="abs_white_paper_header_wrap">' +
        '   <div class="abs_white_paper_header_btn_wrap">' +
        '       <div class="abs_white_paper_header_btn cursor" @click="toDataSearch">返回数据查询</div>' +
        '   </div>'+
        '   <div class="abs_white_paper_header_menu_wrap">'+
        '       <div class="abs_white_paper_header_menu_content">'+
        '	        <div class="abs_white_paper_header_menu">' +
        '	            <i-Menu ref="menus" mode="horizontal">'+
        '                   <Submenu :name="item.id" v-for="(item, index) in treeData" v-if="!!item.children && item.children.length > 0">' +
        '                       <template :name="item.id" slot="title"><div :class="{disabled: !!item.disabled }" @click="clickMenu(0, item, index)">{{ item.title }}</div></template>'+
        '                       <Menu-Group :class="{disabled: !!obj.disabled }" :name="obj.id" :title="obj.title" v-for="(obj, num) in item.children" v-if="!!obj.children && obj.children.length > 0" @click.native="clickMenu(1, item, index, obj, num)">' +
        '                           <span class="iconfont icon-downArrow" :class="{isExpand: !!obj.isExpand}" @click.stop="expandMenu(1, item, index, obj, num)"></span>'+
        '                           <Menu-Item :class="{disabled: !!three.disabled }" v-if="!!obj.isExpand" :name="three.id" v-for="(three, ind) in obj.children" @click.native="clickMenu(2, item, index, obj, num, three, ind)">{{ three.title }}</Menu-Item>'+
        '                       </Menu-Group>'+
        '                      <Menu-Item v-else :class="{disabled: !!obj.disabled }" :name="obj.id" @click.native="clickMenu(1, item, index, obj, num)">{{ obj.title }}</Menu-Item>' +
        '                   </Submenu>'+
        '                   <Menu-Item v-else :class="{disabled: !!item.disabled }" :name="item.id" @click.native="clickMenu(0, item, index)">{{ item.title }}</Menu-Item>' +
        '			    </i-Menu>'+
        '		    </div>' +
        '           <div class="abs_white_paper_header_right" v-if="!!fileFolderDefId">' +
        '               <div class="header_search" @click="showSearch">' +
        '                   <span><i class="iconfont icon-search circle"></i></span>' +
        '                   <span>搜索</span>'+
        '               </div>' +
        '               <div class="menu_search_warp">' +
        '                   <i-input v-focus ref="searchInput" @on-blur="hideSearch" v-model="keyWord" placeholder="请输入关键字" @on-enter="toSearch" @on-click="toSearch">' +
        '                       <span slot="suffix"><i @click="toSearch" class="iconfont icon-search circle"></i></span>' +
        '                   </i-input>' +
        '               </div>' +
        '           </div>'+
        '       </div>'+
        '   </div>'+
        '</div>',
    props: ['fileFolderDefId'],
    data: function() {
        return {
            activeName: '',
            isSearch: false,
            keyWord: "",

            selectBaseId: "66505e9fcb343e5ff4d45ea8", //运营商经营分析报告库

            treeData: [],

        }
    },
    created: function () {
        var sf = this;
        sf.getFolderTree();
    },
    mounted: function () {
        var sf = this;

    },
    methods: {

        //点击菜单
        clickMenu: function(flag, item, index, obj, num, three, ind) {
            var sf = this;
            sf.fileFolderDefId = "";
            if(flag == 0) {
                if(!!item.disabled) {
                    return;
                }
                sf.fileFolderDefId = item.id;
            }else if(flag == 1) {
                if(!!obj.disabled) {
                    return;
                }
                sf.fileFolderDefId = obj.id;
            }else if(flag == 2) {
                if(!!three.disabled) {
                    return;
                }
                sf.fileFolderDefId = three.id;
            }
            Vue.evtHub.$emit("change-ctlg", {
                fileFolderDefId: sf.fileFolderDefId,
            });
        },

        //展开/收起菜单
        expandMenu: function(flag, item, index, obj, num, three, ind) {
            var sf = this;
            if(flag == 1) {
                if(!!obj["isExpand"]) {
                    obj["isExpand"] = false;
                }else {
                    obj["isExpand"] = true;
                }
                item.children[num] = obj;
                sf.$set(sf.treeData, index, item);
            }
        },

        //查询菜单目录树
        getFolderTree: function() {
            var sf = this;
            $.ajax({
                url: linkus.location.km +'/knowledgeCtrl/getNewFolderTree.action',
                type: 'post',
                data: {
                    kmBaseDefId: sf.selectBaseId,
                    level: 3,
                },
                dataType: 'JSON',
                success: function(text) {
                    var data = {};
                    if(!!text && JSON.stringify(text) != "{}") {
                        data = text;
                        if(!!data["children"] && data["children"].length > 0 && !sf.fileFolderDefId) {
                            sf.traverseTree(data["children"]);
                            Vue.evtHub.$emit("change-ctlg", {
                                fileFolderDefId: sf.fileFolderDefId,
                            });
                        }
                    }
                    sf.treeData = [];
                    sf.treeLoading = false;

                    if(data && data.children) {
                        sf.parseJson(data.children,sf.treeData,0);
                        sf.treeData = sf.treeData.children;
                    }
                },
                error: function(data) {

                }
            });
        },

        //遍历目录树
        traverseTree: function(treeData) {
            var sf = this;
            if (!treeData) {
                return;
            }
            for (var i = 0; i < treeData.length; i++) {
                if(!!treeData[i].authed) {
                    sf.fileFolderDefId = treeData[i].value;
                    return;
                }
                // 递归遍历子节点
                sf.traverseTree(treeData[i].children);
            }

        },

        //递归处理树
        parseJson: function(jsonObj,target,count) {
            var sf =this;
            // 循环所有键
            for(var key in jsonObj){
                var element = jsonObj[key];
                // 1.判断是对象或者数组
                var params = {};
                params = {
                    title: element.label,
                    id:element.value,
                    num:0,
                };
                if(element.expandable){
                    params.children = [];
                    params.loading = false;
                }

                if(!element.authed){
                    params.disabled = true
                }
                if(sf.selectDomainId === element.value && sf.selectDomainId){
                    params.selected = true;
                }

                if(element.children){
                    target.children = target.children || [];
                    target.children.push(params);
                    sf.parseJson(element.children,params)
                }else{
                    target.children = target.children || [];
                    target.children.push(params)
                }
            }
        },

        //显示搜索
        showSearch: function() {
            var sf = this;
            sf.isSearch = true;
            setTimeout(function () {
                sf.$refs.searchInput.focus();
            },200);
            $(".menu_search_warp").addClass("show");
            $(".header_search").fadeOut(10);
            $(".menu_search_warp .ivu-input-with-suffix").animate({width: "166px"}, 500);
        },

        //隐藏搜索
        hideSearch: function() {
            var sf = this;
            setTimeout(function () {
                sf.isSearch = false;
                $(".menu_search_warp").removeClass("show");
                $(".menu_search_warp .ivu-input-with-suffix").animate({width: "0px"}, 500);
                $(".header_search").fadeIn(500);
            },200);
        },

        //进入搜索页
        toSearch: function() {
            var sf = this;
            window.open(linkus.location.rsc + '/02html/reportLibrary/search.html?keyWord=' + sf.keyWord + "&fileFolderDefId=" + sf.fileFolderDefId);
        },

        //跳转数据查询页面
        toDataSearch: function(){
            var sf = this;
            window.location.href = linkus.location.rsc + '/02html/reportLibrary/dataSearch.html';
        },
    }
});