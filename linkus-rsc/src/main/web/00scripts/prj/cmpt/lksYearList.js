Vue.component('lks-year-list', {
	template:
		'<dropdown>'+						
		'	<i-button type="primary"> '+
		'		{{curYear}}年 '+
		'		<Icon type="arrow-down-b"></Icon> '+
		'	</i-button> '+
		'	<DropdownMenu slot="list"> '+
		'		<Dropdown-Item v-for="year in yearList" @click.native="yearClick(year)">{{year}}年</Dropdown-Item> '+
		'	</DropdownMenu> '+
		'</dropdown> ',												
		props:[],	
	data:function(){
		var sf = this;
		return {
			curYear: '',
			yearList: []
		}
	},
	created: function(){
		var sf = this;
		
		var date = new Date();
		sf.curYear = date.getFullYear();
		for(var i=1; i<=4; i++){
			sf.yearList.push(sf.curYear - i);
		}
	},
	mounted : function(){
	},
	methods:{
		yearClick: function(year){
			var sf = this;
			
			sf.yearList.push(sf.curYear);
			var index = sf.yearList.indexOf(year);
			sf.yearList.splice(index, 1);
			sf.yearList.sort().reverse();
			sf.curYear=year;
			
			Vue.evtHub.$emit("year-choosed", {
				year: sf.curYear
			});	
		}
	},
})