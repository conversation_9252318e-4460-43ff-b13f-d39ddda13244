Vue.component('pdf-convert', {
    template:		
    	'<div style="z-index: 8;position: fixed;bottom: 10vh;right: 1vw;display: flex;justify-content: center;flex-direction: column;">	 '+
		'	<div style="cursor:pointer;width: 80px;	 '+
		'		 height: 72px;color: #147bd1;line-height: 12px;text-align:center;background: url(../../03images/watermark-bg.png);	 '+
		'		 background-size: contain;background-repeat: no-repeat;background-position: center;display: flex;align-items: center;	 '+
		'		 justify-content: center;font-size: 12px;font-weight: bold;" @click="uploadPdfFile">	 '+
	    '       	PDF水印工具	 '+
	    '   </div>	 '+
		'   <Modal	'+
		'    	v-model="pdfConvertModal"	'+
		'		title="水印工具"	'+
		'		width="800"	'+
		'		height="300"	'+
		'		class-name="vertical-center-modal prd-modal pdf-convert"	'+
		'		:mask-closable="false"	'+
		'		@on-cancel="cancelConvertPdfFile">	'+
		'	    <!-- 上传遮罩层 -->	'+
		'       <div>	'+
		'          <Spin fix v-if="pdfConvertLoading">	'+
		'              <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>	'+
		'              <div>Loading</div>	'+
		'          </Spin>	'+
		'   	</div>	'+
		'       <div style="margin-bottom: 24px;display: flex;">	'+
		'			<span style="display: inline-block;width: 5em;">基本信息</span> '+
		'			<CheckboxGroup v-model="defaultWaterMarks" @on-change="defaultWaterMarkChange" style="display: inline-block;"> '+
		'		        <Checkbox :label="companyName"></Checkbox> '+
		'		        <Checkbox :label="currentName"></Checkbox> '+
		'		        <Checkbox :label="currentJobCode"></Checkbox>   '+
		'		        <Checkbox :label="currentTime"></Checkbox> '+
		'		    </CheckboxGroup>	'+
		'       </div>	'+
		'       <div style="margin-bottom: 24px;display: flex;">	'+
		'			<span style="display: inline-block;width: 5em;">中/英文</span> '+
		'			<RadioGroup v-model="language" @on-change="languageChange" style="display: inline-block;"> '+
	    '    			<Radio label="中文"></Radio>	'+
	    '    			<Radio label="英文"></Radio>	'+
	    '			</RadioGroup>	'+
		'       </div>	'+
		'   	<div style="display: flex;justify-content: space-between;align-items: center;margin-bottom: 24px;">	'+		
		'   	    	<span>水印文字</span>	'+
		'				<i-Input v-model="pdfWaterMark" style="width: calc(100% - 5em);" placeholder="请输入水印文字"></i-Input>	'+
		'       </div>	'+
		'	    <div style="text-align:center;color: #bbbbbb;">	'+
		'	    	<Upload	'+
		'					ref="pdfUpload"	'+
		'					:header = "header"	'+
		'					:before-upload="beforePdfUpload"	'+
		'					:on-success="pdfUploadSuccess"	'+
		'					:show-upload-list="true"	'+
		'					:action="pdfCovertUrl"	'+
		'					:max-size="51200"	'+
		'					:on-exceeded-size="pdfHandleMaxSize"	'+
		'					:on-error="pdfUploadError"	'+
		'					:on-remove = "removePdfFile"	'+
		'					:format="pdfFormat"	'+
		'					:on-format-error="pdfHandFormatError" >	'+
		'				<i-Button v-if="!pdfFilePath" type="primary" size="large" style="font-size:18px;width:200px;">	'+
		'					<i class="iconfont icon-unie123" style="margin-right: 15px;font-size: 18px;vertical-align: top"></i>选择文档 '+
		'				</i-Button>'+
		'				<i-Button v-if="!!pdfFilePath" type="ghost" size="large" class="reselect" '+
		'						  style="font-size:16px;width: 97px;color: #147bd1;border-color: #147bd1;">	'+
		'					<i class="iconfont icon-unie123" style="margin-right: 15px;font-size: 18px;vertical-align: top"></i>重新选择 	'+
		'				</i-Button>'+
		'				<i-Button v-if="!!pdfFilePath" type="primary" size="large" style="font-size:16px;width: 97px;" '+
		'						  @click.stop="downPdfFile">'+
		'					<i class="iconfont icon-unie123" style="margin-right: 15px;font-size: 18px;vertical-align: top"></i>下载pdf '+
		'				</i-Button>'+
		'			</Upload>	'+
		'				<p style="text-align: left;font-size: 14px;margin-top: 1.5em">温馨提示</p>	'+
		'				<p style="text-align: left;margin-top: 1em">支持的文档格式包括doc、docx、xls、xlsx、ppt、pptx、txt、pdf。</p>	'+
		'				<p style="text-align: left">上传文档需50M以下</p>	'+
		'	    </div>	'+
		'  </Modal>	'+
	    '</div> ',
    props: [],
    data: function () {
        return {
        	pdfCovertUrl			: linkus.location.km + "/prdKnowledge/pdfConvert.action",
        	pdfFormat				: ['doc','docx','xls','xlsx','ppt','pptx','txt','pdf'],
        	header					: {'Content-Type':'multipart/form-data'},
        	currentUser				: {},
        	defaultWaterMarks		: [],
        	currentWaterMarks		: [],
        	companyName				: '',
    		currentName				: '',
    		currentJobCode			: '',
    		currentTime				: null,
    		language				: '中文',
 			pdfConvertModal			: false,
 			pdfConvertLoading		: false,
 			pdfFilePath				: null,
       	 	pdfFileName				: '',
       	 	pdfWaterMark			: '',
       	 	waterMarkMap			: {},
        };
    },
    methods: {
    	// 当前登录用户
    	loadCurrentUser : function() {
			  
			var sf = this;
   			$.ajax({
   			     url : linkus.location.prjuser + '/sysUserCtrl/queryByLoginName.action',
   				 type : 'post',
   				 dataType : 'JSON',
   				 async	   :  true,
   				 success : function(teSysUser) {
   					sf.currentUser = teSysUser;
                 },
                 error : function(data) {
                     console.log(data);
                 }
             }); 
		},
    	uploadPdfFile : function(){
    		
    		this.pdfWaterMark = this.getCookie('pdfWaterMark');
    		this.companyName = '亚信科技';
    		this.currentName = this.currentUser.userName;
    		this.currentJobCode = '工号';
    		this.currentTime = '当前时间';
    		// 初始化水印文字选项
    		this.waterMarkMap['亚信科技'] = '亚信科技';
    		this.waterMarkMap[this.currentUser.userName] = this.currentUser.userName;
    		this.waterMarkMap['工号'] = this.currentUser.jobCode;
    		this.waterMarkMap['当前时间'] = this.getNow();
    		this.waterMarkMap['Asiainfo'] = 'Asiainfo';
    		this.waterMarkMap[this.currentUser.loginName] = this.currentUser.loginName;
			this.waterMarkMap['jobCode'] = this.currentUser.jobCode;
			this.waterMarkMap['currentTime'] = this.getNow();
			this.pdfConvertModal = true;
    	},
    	beforePdfUpload : function(file){
    		
    		this.pdfConvertLoading = true;
    		this.$refs.pdfUpload.clearFiles();
    		this.pdfFileName = file.name;
    	},
		pdfUploadSuccess : function(filePath){
    		
    		this.pdfConvertLoading = false;
    		this.removeByFilePath(this.pdfFilePath);
    		this.pdfFilePath = filePath;
    		this.$Message.success('转换成功，可点击下载水印文档',3);
    	},
    	//大小限制
    	pdfHandleMaxSize : function(){
    		 
    		 this.$Message.warning('文件大小不能超过50M！',3);
    		 this.pdfConvertLoading = false;
    	},
    	//上传失败
    	pdfUploadError: function(param){
    		
    		this.$Message.warning('上传失败，请联系管理员',3);
    		this.pdfConvertLoading = false;
    	},
    	pdfHandFormatError : function(){
    		 
    		this.$Message.warning({content:'不支持上传该格式的文档！',duration:5});
			this.pdfConvertLoading = false;
    	},
    	cancelConvertPdfFile : function(){
    		
    		this.pdfWaterMark	= '';
    		this.language = '中文';
    		this.defaultWaterMarks = [];
    		this.removePdfFile();
    	},
    	// 移除文档
    	removePdfFile : function(file){
    		
    		var sf = this;
    		sf.removeByFilePath(sf.pdfFilePath);
    		sf.resetPdfConvert();
    	},
    	resetPdfConvert	: function(){
    		
    		var sf = this;
    		sf.pdfConvertLoading = false;
       	 	sf.pdfFilePath = null;
       	 	sf.pdfFileName = '';
    		sf.$refs.pdfUpload.clearFiles();
    	},
    	downPdfFile : function(){
    		
    		var sf = this;
    		var iframe = document.createElement("iframe");  
            iframe.src = linkus.location.km +'/prdKnowledge/downPdfFile.action?filePath=' + encodeURIComponent(encodeURIComponent(sf.pdfFilePath))
            					            								+'&fileName=' + encodeURIComponent(encodeURIComponent(sf.pdfFileName))
            					            								+'&waterMark=' + encodeURIComponent(encodeURIComponent(sf.pdfWaterMark.trim()));  
            iframe.style.display = "none";  
            document.body.appendChild(iframe); 
            sf.setCookie("pdfWaterMark", sf.pdfWaterMark);
    	},
    	removeByFilePath : function(filePath){
    		
    		var sf = this;
    		if(!filePath){
    			return;
    		}
    		var filePathList = [];
    		filePathList.push(filePath);
     		$.ajax({
                 url : linkus.location.km +'/prdKnowledge/removeByFilePath.action',
                 type : 'post',
                 data : {
                	 filePathList : filePathList
                 },
                 success : function(data) {
                 },
                 error : function(data) {
                     console.log(data);
                 }
             }); 
     		sf.pdfFilePath = null;
    	},
    	defaultWaterMarkChange : function(values){
    		
    		var isAdd;
    		var waterMark = '';
    		if(values.length > this.currentWaterMarks.length){
    			isAdd = true;
    			for(var i = 0; i < values.length; i++){
    				if(this.currentWaterMarks.indexOf(values[i]) == -1){
    					waterMark = values[i];
    				}
    			}
    		}else{
    			isAdd = false;
    			for(var i = 0; i < this.currentWaterMarks.length; i++){
    				if(values.indexOf(this.currentWaterMarks[i]) == -1){
    					waterMark = this.currentWaterMarks[i];
    				}
    			}
    		}
    		waterMark = this.waterMarkMap[waterMark];
    		if(isAdd){
    			this.pdfWaterMark = this.pdfWaterMark + (!!this.pdfWaterMark ? ' ' : '') + waterMark;
    		}else{
    			var replaceReg = new RegExp(waterMark, 'g');
    			this.pdfWaterMark = this.pdfWaterMark.replace(replaceReg, '');
    		}
    		this.currentWaterMarks = values;
    	},
    	getNow : function (){
    		
		    var myDate = new Date();
		    var year = myDate.getFullYear();	// 获取当前年
		    var month = myDate.getMonth() + 1;	// 获取当前月
		    var date = myDate.getDate();	 	// 获取当前日
		    var h = myDate.getHours(); 			// 获取当前小时数(0-23)
		    var m = myDate.getMinutes(); 		// 获取当前分钟数(0-59)
		    var s = myDate.getSeconds();
		    // 获取当前时间
		    return year + '-' + this.conver(month) + "-" + this.conver(date);
    	},
    	//日期时间处理
    	conver : function (s) {
    		
    		return s < 10 ? '0' + s : s;
    	},
    	languageChange : function(value){
    		
    		this.defaultWaterMarks = [];
    		this.currentWaterMarks = [];
    		if('中文' == value){
        		this.companyName = '亚信科技';
        		this.currentName = this.currentUser.userName;
        		this.currentJobCode = '工号';
        		this.currentTime = '当前时间';
    		}else{
    			this.companyName = 'Asiainfo';
    			this.currentName = this.currentUser.loginName;
    			this.currentJobCode = 'jobCode';
        		this.currentTime = 'currentTime';
    		}
    	},
    	setCookie : function(name, value){
	    	
    		var days = 30; // 保留30天
	    	var exp = new Date();
	    	exp.setTime(exp.getTime() + days*24*60*60*1000);
	    	document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
    	},
    	getCookie : function(name){
	    	
    		var arr,reg = new RegExp("(^| )"+name+"=([^;]*)(;|$)");
	    	if(arr=document.cookie.match(reg)){
	    		return unescape(arr[2]);
	    	}else{
	    		return '';
	    	}
    	}
    },
    created : function () {
		
    	this.loadCurrentUser();
	},
    mounted: function () {
    	
    }
});