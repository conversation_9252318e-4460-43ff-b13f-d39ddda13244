Vue.component('edit-file-info', {
    template:
        '               <Modal v-model="editModal" title="编辑" width="800" top="20x">' +
        '                   <i-form ref="compeleteInfoForm" :model="compeleteInfo" :rules="compeleteInfoValidate"   :label-width="130">' +
        '                       <Form-Item label="名称" prop="fileName">' +
        '                           <i-input v-model="compeleteInfo.fileName"></i-input>' +
        '                       </Form-Item>' +
        '                       <Form-Item label="存储目录" class="required_form">' +
        '                           <select-tree ref="selectTree" :select-base-id="selectBaseId" :select-tree-id="compeleteInfo.fileCategoryId" :suffix-name="compeleteInfo.fileCategoryName"></select-tree>' +
        '                       </Form-Item>' +
        '                       <Form-Item label="描述" prop="fileDesc">' +
        '                           <i-input type="textarea" v-model="compeleteInfo.fileDesc"></i-input>' +
        '                       </Form-Item>' +
        '                       <Form-Item  label="封面" class="image_upload_warp" prop="image">' +
        '                           <div v-show="!!imagePath" class="demo-upload-list" style="display: flex">' +
        '                               <img :src="imageUrl" style="object-fit: cover;">' +
        '                               <div class="demo-upload-list-cover" style="padding-top: 105px;height:20px">' +
        '                                   <bw-com-imgupload :url="uploadUrlImg"' +
        '                                          file-param-name="file"' +
        '                                          :customize-params="customizeParams"' +
        '                                          :is-new-icon="true"' +
        '                                          :on-success="uploadSuccessImg" :on-error="uploadErrorImg" btn-text="添加封面"' +
        '                                          modal-text="添加封面">' +
        '                                   </bw-com-imgupload>' +
        '                                   <i class="iconfont icon-ashbin1"  @click="removeImage"></i>' +
        '                               </div>' +
        '                           </div>' +
        '                           <div style="text-align: center">' +
        '                               <km-com-imgupload v-show="!imagePath" :url="uploadUrlImg"' +
        '                                      file-param-name="file"' +
        '                                      :is-new-icon="true"' +
        '                                      :on-success="uploadSuccessImg" :on-error="uploadErrorImg" :customize-params="customizeParams" btn-text="添加封面"' +
        '                                      modal-text="添加封面">' +
        '                               </km-com-imgupload>' +
        '                           </div>' +
        '                       </Form-Item>' +
        '                       <Form-Item label="标签"  class="qa_tag_group">' +
        '                           <Tag size="large" closable v-for="(item,index) in compeleteInfo.tags" :key="index" @on-close="removeTag(item)">{{item}}</Tag>' +
        '                           <i-Button v-if="!isAddTag" @click.native="addTagShow" ><i class="iconfont icon-plus"></i>添加标签</i-Button>' +
        '                           <div class="search_tag_warp" ref="searchTagWarp" v-if="isAddTag">' +
        '                               <i-input v-focus ref="addTagInput" @on-enter="addTag" style="width: 150px" v-model="compeleteInfo.tagSearch" @on-change="queryTagsByKeyWord" clearable="true" :loading="tagLoading">' +
        '                                   <i class="iconfont icon-search" slot="suffix" />' +
        '                               </i-input>' +
        '                               <ul class="tag_list" v-show="compeleteInfo.tagSearch"><spin fix v-if="tagLoading"></spin><li v-for="item in optionalTags" @click="selectTag(item)" class="tag">{{item}}</li><li v-if="optionalTags.length === 0" style="color: rgba(200, 205, 220)">无相关标签，按回车生成</li></ul>' +
        '                           </div>' +
        '                       </Form-Item>' +
        '                       <Form-Item label="知识作者" prop="author">'+
		'                       	<lks-load-user-fuzzy :user.sync="compeleteInfo.author"></lks-load-user-fuzzy>'+
	    '                       </Form-Item>'+

        '                       <Form-Item label="审批人" prop="userIds" v-if="cnfgList.length > 0 && !allowMultiUserAppr">'+
        '                       	  <i-select v-model="compeleteInfo.userIds" filterable multiple>' +
        '                                  <i-option v-for="item in cnfgList" :value="item.id" :key="item.id">{{ item.userName }}</i-option>' +
        '                             </i-select>'+
        '                       </Form-Item>'+

        '                       <Form-Item label="浏览及下载权限" prop="secrect">' +
        '                           <i-select v-model="compeleteInfo.secrect">' +
        '                               <i-option v-for="(item,index) in secrectList" :value="item.id" :key="item.id">{{ item.defName }}</i-option>' +
        '                           </i-select>' +
        '                       </Form-Item>' +
        '                       <Form-Item label="编码" prop="no">' +
        '                           <div slot="label" class="cust_label">' +
        '                               <span>编码</span>' +
        '                               <Tooltip content="本文可以根据编码排序"  placement="right"><i class="iconfont icon-qbzuocesvg02" style="color: #FFBB00;font-size: 16px"></i></Tooltip>' +
        '                           </div>' +
        '                           <Input-Number v-model="compeleteInfo.no" :min="1" style="width: 100%" ></Input-Number>' +
        '                       </Form-Item>' +
        '                   </i-form>'  +
        '                   <div slot="footer">' +
        '                       <i-Button  type="text" size="large" @click="editModal = false">取消</i-Button>' +
        '                       <i-Button  type="primary" size="large"  @click="saveEditInfo">确定</i-Button>' +
        '                   </div>' +
        '               </Modal>' ,
    props:['editModal','fileInfo','selectBaseId','loginUser'],
    data: function () {
        var sf = this;
        //知识作者校验
	    var validateAuthor = function (rule, value, callback) {
		  if (!sf.compeleteInfo.author || !sf.compeleteInfo.author.userId) {
			   callback(new Error('请选择知识作者'));
		  } else {
			  callback();
		   }
	    };
        return {
            secrectList:[],
            //校验
            compeleteInfoValidate: {
                fileName:[
                    {required: false,type:'string',message: '文档名不能为空',trigger: 'blur'}
                ],
                secrect: [
                    { required: true,type:'string', message: '请选择浏览及下载权限', trigger: 'change' }
                ],
                fileCategory: [
                    { required: true,type:'array', message: '请选择目录', trigger: 'change'}
                ],
                tags: [{
                    required: true,
                    type: 'array',
                    message: '请选择标签',
                    trigger: 'blur'
                }],
                author: [{
					required: true,
					validator: validateAuthor
				}],
                userIds: [
                    { required: true,type:'array', message: '请选择审批人', trigger: 'change' }
                ],
            },
            compeleteInfo:{
                fileName:'',//标题
                suffixName: '',//后缀
                fileDesc:'',//描述
                fileCategory:'',//目录
                fileCategoryName:'',//目录 - 中文名
                fileCategoryId:'',//目录 - id
                secrect:'',//浏览及下载权限

                tagSearch:'',
                userIds:[],
                tags:null,
                author: {
                	userId: null,
					userName: null,
					loginName: null,
					jobCode: null
                },
                no:'',
                relFileSign:[],
                imagePath:null,
            },
            tagLoading:false,
            optionalTags:[],
            isAddTag:false,
            tagSearchAjax:'',


            baseImageUrl:linkus.location.km +'/prdView/previewImage.action?filePath=',
            imagePath:'',
            imageUrl:'',
            uploadUrlImg: linkus.location.km + "/prdView/uploadHeadImg.action",
            fileId:'',
            //自定义截图图片的宽度
            customizeParams:{
                aspectRatio:1.7, //宽高比（截取框  width/heigth）
                canvasWidth:218,//截取显示宽  aspectRatio*128 取整
                canvasHeightSave:436,//保存图片高
                canvasWidthSave:930,//保存图片宽
            },

            cnfgList:[],
            allowMultiUserAppr:true,
            editCnfgId:'',
        }
    },
    watch:{
        fileInfo:function (newV,oldV) {
            console.log(newV);
            var sf = this;
            var attr =newV.fileName.split(".");
            var lastIndexOf = newV.fileName.lastIndexOf(".");
            var suffixName = newV.fileName.slice(lastIndexOf);
            if (attr.length > 1) {
                attr.pop();
            }
            var fileName =attr.join('.');
            sf.fileId = newV.id;
            sf.compeleteInfo = {
                suffixName :suffixName,
                fileName :fileName,
                fileCategoryName :newV.suffixName,
                no :newV.no >=0 ? newV.no : '',
                fileCategoryId :newV.fileFolderDefId,
                fileCategory :[{id:newV.fileFolderDefId}],
                fileDesc :newV.uploadDesc,
                imagePath :newV.imagesPath ? newV.imagesPath[0] :null,
                tags:newV.tagList || [],
                secrect:newV.fileSecretLevel ? newV.fileSecretLevel.cid : null,
                userIds:[],
            };
            sf.imagePath = newV.imagesPath ? newV.imagesPath[0] :null;
            sf.imageUrl = sf.baseImageUrl + sf.imagePath;
            var authors = newV.authors;
            if(!!authors && authors.length > 0) {
            	sf.compeleteInfo.author = authors[0];
            }else {
            	sf.compeleteInfo.author = {
                	userId: sf.loginUser.id,
					userName: sf.loginUser.userName,
					loginName: sf.loginUser.loginName,
					jobCode: sf.loginUser.jobCode,
                }
            }
            sf.queryApprover();
        },
        editModal:function (newV,oldV) {
            if(!newV){
                Vue.evtHub.$emit("editMoadlClose",{});
            }
        }
    },
    directives: {
        focus:function (el) {
            el.focus();
        }
    },
    created : function () {
        var sf = this;
        if(!!Vue.evtHub){
            Vue.evtHub = new Vue();
        }
        sf.loadAllSecrect();
    },
    mounted:function (){
        var sf = this;
        //获取树选择返回值
        Vue.evtHub.$on("treeSelectDataSel",function (data) {
            if(JSON.stringify(data.checkedNodeAndParents) != '{}'){
                sf.compeleteInfo.fileCategory = data.checkedNodeAndParents;
            }
            sf.queryCnfgId();
            sf.queryApprover();
        });
    },
    destroyed:function() {
        var sf = this;
    },
    methods: {
        //文档密级
        loadAllSecrect: function(){
            var sf=this;
            var vo={
                srcId: "5c37f83d900add501a1414f8",
            };
            $.ajax({
                url:linkus.location.km+'/prdKnowledge/queryFileAttrBySrcId.action',
                headers : {'Content-Type' : 'application/json;charset=utf-8'},
                data : JSON.stringify(vo),
                type : 'post',
                success : function(data) {
                    sf.secrectList = data;
                },
                error : function(data){
                }
            });
        },

        //查询审批人
        queryApprover: function () {
            var sf = this;
            sf.cnfgList = [];
            if(!sf.compeleteInfo.fileCategory){
                return;
            }
            sf.compeleteInfo.userIds = [];
            $.ajax({
                url: linkus.location.km + '/knowledgeCtrl/queryApprover.action',
                data:{
                    kmBaseId:sf.selectBaseId,
                    fileFolderDefId:sf.compeleteInfo.fileCategory[0].id,
                },
                type: 'post',
            }).done(function (res) {
                if(res.success){
                    sf.cnfgList = res.data.approver || [];
                    if(sf.cnfgList.length === 1){
                        sf.compeleteInfo.userIds = [sf.cnfgList[0].id];
                    }
                    res.data.allowMultiUserAppr =  res.data.allowMultiUserAppr === undefined ? true : res.data.allowMultiUserAppr;

                    sf.allowMultiUserAppr = res.data.allowMultiUserAppr;
                }
            });
        },

        //移除标签
        removeTag:function (tag) {
            this.compeleteInfo.tags = this.compeleteInfo.tags || [];
            var index = this.compeleteInfo.tags.indexOf(tag);
            this.compeleteInfo.tags.splice(index, 1);
        },

        //显示-搜索添加标签
        addTagShow:function(){
            var sf = this;
            sf.isAddTag = true;
            setTimeout(function () {
                sf.$refs.addTagInput.focus();
            },200);
        },
        //查询标签
        queryTagsByKeyWord:function(){
            var sf = this;
            var value = sf.compeleteInfo.tagSearch;
            if(!value || !value.trim()){
                sf.optionalTags = [];
                return;
            }
            sf.tagLoading = true;

            if (sf.tagSearchAjax && sf.tagSearchAjax.abort) {
                sf.tagSearchAjax.abort();
            }

            sf.tagSearchAjax = $.ajax({
                url:linkus.location.km + '/prdView/queryTagsByKeyWord.action',
                data:{
                    keyWord:value,
                    fileBaseDefId:sf.selectBaseId,
                },
                type:'post',
                success:function(data) {
                    sf.optionalTags = data || [];
                    sf.tagLoading = false;
                },
                error:function(data){
                    sf.tagLoading = false;
                }
            });
        },
        // 选择标签
        selectTag : function(value){
            var sf = this;
            sf.compeleteInfo.tags = sf.compeleteInfo.tags || [];
            if(sf.compeleteInfo.tags.indexOf(value.trim())){
                sf.compeleteInfo.tags.push(value.trim());
            }
            sf.compeleteInfo.tagSearch = '';
            sf.isAddTag =false;
        },
        // 添加标签
        addTag:function(){
            var sf =this;
            var valueStr = sf.compeleteInfo.tagSearch.trim();
            if(!valueStr || !valueStr.trim()){
                sf.isAddTag =false;
                return;
            }
            sf.compeleteInfo.tags = sf.compeleteInfo.tags || [];
            if(sf.compeleteInfo.tags.indexOf(valueStr)){
                sf.compeleteInfo.tags.push(valueStr);
            }
            sf.compeleteInfo.tagSearch = '';
            setTimeout(function () {
                sf.$refs.addTagInput.focus();
            },200);
        },

        // 上传成功
        uploadSuccessImg : function(filePath){
            var sf = this;
            sf.doRemoveImage(sf.imagePath);
            sf.imagePath = filePath;
            sf.compeleteInfo.imagePath = filePath;
            sf.imageUrl = sf.baseImageUrl + sf.imagePath;
            sf.$Message.success('上传成功',3);
        },
        // 上传失败
        uploadErrorImg : function(param){
            var sf = this;
            sf.imagePath = null;
            sf.compeleteInfo.imagePath = null;
            sf.$Message.warning('上传失败，请联系管理员',3);
        },

        removeImage : function(){
            var sf = this;
            sf.doRemoveImage(sf.imagePath);
            sf.compeleteInfo.imagePath = null;
            sf.imagePath = null;
        },
        // 删除图片
        doRemoveImage : function(path){
            var sf = this;
            if(!path || (!!sf.originImagePath && sf.originImagePath == path)){
                return;
            }
            $.ajax({
                url  : linkus.location.km +'/prdView/removeFileByPath.action',
                type : 'post',
                data : {
                    path : path
                },
                dataType : 'text',
                success : function(data) {

                },
                error : function(data) {
                    console.log(data);
                }
            });
        },

        saveEditInfo : function(){
            var sf = this;
            sf.$refs.compeleteInfoForm.validate(function(valid){
                if(valid){
                    var secrect ={};
                    for(var i=0;i<sf.secrectList.length;i++){
                        if(sf.compeleteInfo.secrect === sf.secrectList[i].id){
                            secrect = {
                                cid:sf.secrectList[i].id,
                                name:sf.secrectList[i].defName,
                                codeName: sf.secrectList[i].codeName,
                            }
                        }
                    }
                    var fileFolderDefId = sf.compeleteInfo.fileCategory[sf.compeleteInfo.fileCategory.length-1].id;
                    var author = sf.compeleteInfo.author;
                    var authorIds = [author.userId];
                    var param = {
                        id:sf.fileId,
                        fileName:sf.compeleteInfo.fileName+sf.compeleteInfo.suffixName,
                        images:sf.compeleteInfo.imagePath,
                        tags:sf.compeleteInfo.tags.length > 0 ?','+sf.compeleteInfo.tags.join(',')+',':'',
                        secretLevel:secrect,
                        uploadDesc:sf.compeleteInfo.fileDesc,
                        fileFolderDefId:fileFolderDefId,
                        no:sf.compeleteInfo.no,
                        authorIds: authorIds
                    };
                    if(sf.compeleteInfo.userIds && !sf.allowMultiUserAppr && sf.cnfgList.length > 0){
                        param.isAppr = true;
                    }

                    $.ajax({
                        url  : linkus.location.km +'/prdKnowledge/editFile.action',
                        headers : {'Content-Type' : 'application/json;charset=utf-8'},
                        type : 'post',
                        data : JSON.stringify(param),
                        success : function(data) {
                            if(sf.compeleteInfo.userIds  && !sf.allowMultiUserAppr && sf.cnfgList.length > 0){
                                sf.setPendingStatus(sf.compeleteInfo.userIds,data.data)
                            }else{
                                sf.$Message.warning('编辑成功',3);
                                sf.editModal = false;
                            }

                        },
                        error : function(data) {
                            console.log(data);
                        }
                    });
                }
            });
        },

        // 设置审批状态
        setPendingStatus : function(userIds,fileIds){
            var sf = this;

            $.ajax({
                url  : linkus.location.km +'/knowledgeCtrl/setPendingStatus.action',
                type : 'post',
                data : {
                    cnfgId:sf.editCnfgId,
                    fileIds:[fileIds],
                    userIds:userIds,
                },
                dataType : 'text',
                success : function(data) {
                    sf.$Message.warning('提交审批成功！',3);
                    sf.editModal = false;
                },
                error : function(data) {
                    console.log(data);
                }
            });
        },

        queryCnfgId : function(){
            var sf = this;

            $.ajax({
                url  : linkus.location.km +'/knowledgeCtrl/queryCnfgId.action',
                type : 'post',
                data : {
                    kmBaseId:sf.selectBaseId,
                    cnfgTypeId:'62ec7ec68fe17d21b08d9af2',
                },
                success : function(data) {
                    sf.editCnfgId = data.data;
                },
                error : function(data) {
                    console.log(data);
                }
            });
        },

    }

});

