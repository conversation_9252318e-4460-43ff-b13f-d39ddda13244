.navigation_warp {
  height: calc(100% - 80px);
  padding: 16px 24px;
  background-color: #F5F8FB;
  border: 1px solid #3883e5;
  position: fixed;
  left: 60px;
  overflow: auto;
  top: 68px;
  z-index: 999;
  font-size: 14px;
  border-radius: 13px;
  box-shadow: 0 2px 12px 0 rgba(56, 131, 229, 0.2), 0 2px 12px 0 rgba(56, 131, 229, 0.2);
}
.navigation_warp::-webkit-scrollbar {
  width: 0;
}
.navigation_warp .tip_arrow {
  display: block;
  width: 0;
  height: 0;
  position: fixed;
  border-color: transparent;
  border-style: solid;
  top: calc(30vh - 42px);
  border-width: 0 8px 8px;
  border-bottom-color: #3883e5;
  left: 56px;
  margin-left: -8px;
  z-index: 999;
  transform: rotate(-90deg);
}
.navigation_warp .tip_arrow:after {
  content: " ";
  top: 1px;
  border-width: 0 8px 8px;
  margin-left: -8px;
  border-color: transparent;
  border-bottom-color: #F5F8FB;
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  border-style: solid;
}
.navigation_warp > ul > li {
  display: flex;
}
.navigation_warp > ul > li > span {
  font-weight: bold;
  color: #262626;
}
.navigation_warp > ul > li .line_group {
  margin: 7px ;
}
.navigation_warp > ul > li .line_group > li {
  display: flex;
  height: 28px;
}
.navigation_warp > ul > li .line_group > li > span {
  width: 24px;
}
.navigation_warp > ul > li .line_group > li > span:nth-child(2) {
  margin-left: -2px;
}
.navigation_warp > ul > li .line_group > li > em {
  font-style: normal;
  margin-left: 8px;
  margin-top: 18px;
  height: 16px;
  cursor: pointer;
}
.navigation_warp > ul > li .line_group > li > em:hover {
  color: #3883e5;
}
.navigation_warp > ul > li .line_group > li > em.no_url {
  color: #999999;
  cursor: default;
}
.navigation_warp > ul > li .line_group > li > em.no_url:hover {
  color: #999999;
}
.navigation_warp > ul > li .line_group > li.active > em {
  color: #3883e5;
}
.navigation_warp > ul > li .line_group > .first > span:first-child {
  border-top: 2px solid #3883e5;
  border-right: 2px solid #3883e5;
  border-radius: 0 12px 0 0;
}
.navigation_warp > ul > li .line_group > .first > span:nth-child(2) {
  border-bottom: 2px solid #3883e5;
  position: relative;
}
.navigation_warp > ul > li .line_group > .first > span:nth-child(2):after {
  content: '';
  position: absolute;
  left: -2px;
  bottom: -4px;
  width: 6px;
  height: 6px;
  background: #ffffff;
  border: 1px solid #3883e5;
  border-radius: 50%;
  z-index: 2;
}
.navigation_warp > ul > li .line_group > .first.active > span:nth-child(2):after {
  background: #ffff7a;
}
.navigation_warp > ul > li .line_group > .first.two_d {
  height: 14px;
}
.navigation_warp > ul > li .line_group > .first.two_d > span:nth-child(2) {
  border-bottom: none;
}
.navigation_warp > ul > li .line_group > .first.two_d > span:nth-child(2):after {
  content: '';
  position: absolute;
  left: -2px;
  bottom: -4px;
  width: 6px;
  height: 6px;
  background: transparent;
  border: none;
  border-radius: 50%;
}
.navigation_warp > ul > li .line_group > .last > span:nth-child(2) {
  border-left: 2px solid #3883e5;
  border-bottom: 2px solid #3883e5;
  border-radius: 0 0 0 12px;
}
.navigation_warp > ul > li .line_group > .last.two_d {
  height: 14px;
}
.navigation_warp > ul > li .line_group > .last.two_d > em {
  margin-top: 6px;
}
.navigation_warp > ul > li .line_group > .data-line > span:nth-child(2) {
  border-bottom: 2px solid #3883e5;
  border-left: 2px solid #3883e5;
  position: relative;
}
.navigation_warp > ul > li .line_group > .data-line > span:nth-child(2):after {
  content: '';
  position: absolute;
  left: -4px;
  bottom: -4px;
  width: 6px;
  height: 6px;
  background: #ffffff;
  border: 1px solid #3883e5;
  border-radius: 50%;
  z-index: 4;
}
.navigation_warp > ul > li .line_group > .data-line.active > span:nth-child(2):after {
  background: #ffff7a;
}
.navigation_warp > ul > li .line_group .line_group {
  margin-top: 26px;
}
.full_in_tag_warp .full_in_tag_cen {
  padding: 9px 16px;
  font-size: 14px;
}
.full_in_tag_warp .full_in_tag_cen > p {
  color: #555555;
  margin-bottom: 24px;
}
.full_in_tag_warp .full_in_tag_cen .full_method {
  display: flex;
  margin-bottom: 32px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > label {
  width: 60px;
  position: relative;
  color: #262626;
  font-weight: 700;
}
.full_in_tag_warp .full_in_tag_cen .full_method > label:after {
  content: '';
  position: absolute;
  top: 18px;
  left: 0;
  width: 47px;
  height: 3px;
  opacity: 0.8;
  background: #3883e5;
}
.full_in_tag_warp .full_in_tag_cen .full_method > label.full_table_label {
  padding-top: 6px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > label.full_table_label:after {
  top: 24px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div {
  color: #555555;
  width: calc(100% - 60px);
  font-size: 12px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div {
  color: #555555;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div:first-child {
  font-size: 14px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div:not(:last-child) {
  margin-bottom: 12px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.bg_ye {
  background: rgba(255, 153, 0, 0.1);
  border-radius: 4px;
  padding: 8px 12px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.bg_ye span {
  color: #262626;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.full_img {
  display: flex;
  padding-left: 12px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.full_img img {
  width: 100px;
  height: 100px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.full_table_fliter {
  display: flex;
  align-items: center;
  margin-right: 14px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.full_table_fliter > div {
  margin-left: auto;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div > div.full_table_fliter > span {
  color: #999999;
  padding: 0 4px;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div .group_select ul {
  display: flex;
  flex-wrap: wrap;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div .group_select ul li {
  margin-bottom: 10px;
  margin-right: 10px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  font-size: 12px;
  text-align: center;
  width: 102px;
  cursor: pointer;
  text-overflow: ellipsis;
  white-space: normal;
  min-height: 42px;
  border-width: 1px;
  border-style: solid;
  border-color: #dedddd;
  border-image: initial;
  border-radius: 3px;
  padding: 5px 10px;
  outline: 0;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div .group_select ul li i {
  position: absolute;
  right: -8px;
  top: -8px;
  width: 16px;
  height: 16px;
  font-size: 18px;
  border-radius: 50%;
  background: #fff;
  display: none;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div .group_select ul li.active {
  color: #3883e5;
  border: 1px solid #3883e5;
}
.full_in_tag_warp .full_in_tag_cen .full_method > div .group_select ul li.active i {
  display: inline-block;
}
.full_in_tag_warp .tag_btn {
  text-align: right;
}
.full_in_tag_warp .ivu-modal-footer {
  border-top: none;
}
.turn_down_info {
  background-color: #FFF9E6;
  border: 1px solid #FFD77A;
  color: #555;
  line-height: 32px;
  padding: 0 16px;
  font-size: 12px;
  border-radius: 4px;
}
.turn_down_info span {
  color: #f90;
  padding: 0 12px 0 4px;
}
/*项目基准*/
.prj_ben_warp.header_fix .prjHeader.header_menu {
  position: fixed !important;
  top: 0;
}
.prj_ben_warp.header_fix .prj_ben_con {
  padding-top: 76px;
  height: 100%;
  overflow: auto;
}
.prj_ben_warp .prj_ben_con {
  min-height: calc(100% - 60px);
  background: #f8f8f9;
  padding: 16px;
  display: flex;
  position: relative;
}
.prj_ben_warp .prj_ben_con .prj_b_title {
  height: 22px;
  line-height: 22px;
  padding-left: 16px;
  border-left: 3px solid #3883e5;
  position: relative;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_b_title .prj_b_title_right {
  margin-left: auto;
}
.prj_ben_warp .prj_ben_con .prj_b_title .prj_b_title_right .ivu-btn-text {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_b_title .scope_view_btn {
  color: #A5ACC4;
  font-size: 12px;
  margin-left: 8px;
  cursor: pointer;
}
.prj_ben_warp .prj_ben_con .prj_b_title:after {
  content: '';
  position: absolute;
  left: -7px;
  top: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.prj_ben_warp .prj_ben_con .prj_b_title:before {
  content: '';
  position: absolute;
  left: -7px;
  bottom: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.prj_ben_warp .prj_ben_con .prj_ben_left {
  width: calc((19/24)*100% - 16px);
  margin-right: 16px;
  height: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top {
  background: #ffffff;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
  margin-bottom: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter {
  padding: 12px 16px;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter * {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div {
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_1 {
  width: 400px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_1 > div {
  display: flex !important;
  align-items: center;
  width: calc(100% - 2em - 12px);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_1 > div > div.flow-window {
  width: calc(100% - 88px) !important;
  display: flex !important;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_1 > div > div.flow-window .ivu-input {
  border: 1px solid #dcdee2;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_3 {
  cursor: pointer;
  color: #555555;
  margin-left: auto;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div.t_col_3 i {
  color: #a5acc4;
  margin-right: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_filter > div > label {
  color: #666666;
  padding-right: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_tab {
  padding: 0 16px;
  border-top: 1px solid #f1f5f9;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_tab ul {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_tab ul li {
  padding: 19px 16px;
  cursor: pointer;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_top .ben_top_tab ul li.active {
  border-bottom: 2px solid #3883e5;
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot {
  min-height: calc(100% - 130px);
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
  padding: 12px 0;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot.flex .l_tip {
  text-align: center;
  font-size: 20px;
  color: #bbbec4;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .user-fuzzy .ivu-input {
  border: 1px solid #DBE3EB;
  color: #262626;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .member-list-box.drop-select {
  width: 420px !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info > div {
  margin-bottom: 24px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .cost_basis .prj_b_title {
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .cost_basis .prj_b_title .cost_basis_top {
  margin-left: auto;
  height: auto;
  margin-bottom: 0;
  border-bottom: none;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-header th {
  background-color: #f5f8fb;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-header thead tr:first-child th {
  text-align: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-header thead tr:first-child th:not(:last-child) {
  border-right: 1px solid #dbe3eb !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-header thead tr:nth-child(2) th:nth-child(3n) {
  border-right: 1px solid #dbe3eb !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-header thead tr:nth-child(2) th:last-child {
  border-right: none !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-body .ivu-table-tbody td:nth-child(3n) {
  border-right: 1px solid #dbe3eb !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .benchmark_info .ivu-table-body .ivu-table-tbody td:last-child {
  border-right: none !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter.super_base_line_filter {
  padding-top: 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter.super_base_line_filter .fol_item {
  height: auto;
  align-items: flex-start;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter {
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter.col_3 .fol_item {
  width: calc(100% / 3);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter.col_2 .fol_item {
  width: 50%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter.col_1 .fol_item {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item {
  display: flex;
  height: 32px;
  align-items: center;
  margin-bottom: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .ivu-cascader .ivu-select-dropdown {
  overflow-x: hidden !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: calc(100% - 10px);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .height100 {
  height: 100%;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .edit_frame {
  height: 100%;
  align-items: center;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #DBE3EB;
  padding: 0 7px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .edit_frame:hover {
  border: 1px solid #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .edit_frame > span {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item * {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item.pdL20 {
  padding-left: 20px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item label {
  width: 6em;
  color: #666666;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .txt_break {
  white-space: pre-wrap;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div {
  padding-left: 24px;
  color: #262626;
  width: calc(100% - 6em);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div > div {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div > em {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .new_table_warp {
  padding: 0 16px;
  border-top: 1px solid #f1f5f9;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .new_table_warp.no_border {
  border: none;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .new_table_warp .scope_view_table_warp {
  height: auto;
  overflow: hidden;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .new_table_warp .scope_view_table_warp::-webkit-scrollbar {
  width: 0;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .new_table_warp .scope_view_table_warp.scopeClose {
  height: 440px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .cost_basic_emp_month_avg {
  padding: 8px 16px 0 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .b_base_info .cost_basic_emp_month_avg .cost_basic_emp_month_avg_title {
  margin-top: 4px;
  margin-bottom: 12px;
  display: inline-block;
  padding-left: 8px;
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info {
  padding: 0 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_cond_wrap {
  padding-left: 7px;
  color: #999999;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info th {
  border-bottom: 1px solid #dbe3eb !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter {
  display: flex;
  align-items: center;
  padding: 0 8px;
  flex-wrap: wrap;
  padding-top: 8px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter.col_3 .fol_item {
  width: calc(100% / 3);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter.col_2 .fol_item {
  width: 50%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter.col_1 .fol_item {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item {
  display: flex;
  height: 32px;
  align-items: center;
  margin-bottom: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .ivu-cascader .ivu-select-dropdown {
  overflow-x: hidden !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .span_ellipsis {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .span_ellipsis > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: calc(100% - 10px);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .height100 {
  height: 100%;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .edit_frame {
  height: 100%;
  align-items: center;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #DBE3EB;
  padding: 0 7px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .edit_frame:hover {
  border: 1px solid #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .edit_frame > span {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item * {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item.pdL20 {
  padding-left: 20px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item label {
  width: 6em;
  color: #666666;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item .txt_break {
  white-space: pre-wrap;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item > div {
  padding-left: 24px;
  color: #262626;
  width: calc(100% - 6em);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item > div > div {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .target_datum_info .prj_b_b_filter .fol_item > div > em {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .range_datum_info .prj_b_btn,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .speed_of_progress_datum_info .prj_b_btn,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_btn {
  display: flex;
  justify-content: flex-end;
  padding: 0 16px;
  line-height: 38px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .range_datum_info .prj_b_btn .prj-button,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .speed_of_progress_datum_info .prj_b_btn .prj-button,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_btn .prj-button {
  margin-left: 12px;
  cursor: pointer;
  color: #999999;
  font-size: 12px;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .range_datum_info .prj_b_btn .prj-button i,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .speed_of_progress_datum_info .prj_b_btn .prj-button i,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_btn .prj-button i {
  margin-right: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .range_datum_info .prj_b_btn .prj-button:hover,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .speed_of_progress_datum_info .prj_b_btn .prj-button:hover,
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_btn .prj-button:hover {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis {
  padding: 0 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top {
  padding: 0 12px;
  height: 38px;
  border-bottom: 1px solid #dbe3eb;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  width: calc(100% - 160px);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div {
  display: flex;
  width: 100%;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > span {
  width: 46px;
  cursor: pointer;
  color: #999999;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > span.active {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > i {
  cursor: pointer;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > i.no_active {
  color: #c5c8ce;
  cursor: no-drop;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > div {
  display: flex;
  width: calc(100% - 78px);
  align-items: center;
  position: relative;
  overflow: hidden;
  height: 24px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > div > ul {
  display: flex;
  align-items: center;
  position: absolute;
  left: 0px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > div > ul li {
  color: #999999;
  font-size: 14px;
  margin-right: 24px;
  line-height: 22px;
  cursor: pointer;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top > div > div > ul li.active {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .cost_basis_top .prj_b_btn {
  margin-left: auto;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter {
  display: flex;
  align-items: center;
  padding: 0 8px;
  flex-wrap: wrap;
  padding-top: 8px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter.col_3 .fol_item {
  width: calc(100% / 3);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter.col_2 .fol_item {
  width: 50%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter.col_1 .fol_item {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item {
  display: flex;
  height: 32px;
  align-items: center;
  margin-bottom: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .ivu-cascader .ivu-select-dropdown {
  overflow-x: hidden !important;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .span_ellipsis {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .span_ellipsis > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: calc(100% - 10px);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .height100 {
  height: 100%;
  display: flex;
  align-items: center;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .edit_frame {
  height: 100%;
  align-items: center;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #DBE3EB;
  padding: 0 7px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .edit_frame:hover {
  border: 1px solid #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .edit_frame > span {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item * {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item.pdL20 {
  padding-left: 20px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item label {
  width: 10em;
  color: #666666;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item .txt_break {
  white-space: pre-wrap;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item > div {
  padding-left: 24px;
  color: #262626;
  width: calc(100% - 10em);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item > div > div {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .prj_b_l_bot .cost_basis .prj_b_b_filter .fol_item > div > em {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list {
  padding: 0 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item {
  margin-bottom: 24px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item.col3 {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item.col3 .result_info_item_col {
  width: calc(100% / 3);
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item.col3 .result_info_item_col:not(:last-child) {
  margin-right: 8px;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item .result_info_item_col .result_info_item_col_title {
  height: 26px;
  line-height: 22px;
  padding-left: 16px;
  position: relative;
  color: #262626;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e8eaec;
}
.prj_ben_warp .prj_ben_con .prj_ben_left .result_info .result_info_list .result_info_item .result_info_item_col .result_info_item_col_title:before {
  content: '';
  position: absolute;
  width: 6px;
  height: 6px;
  background: #3883e5;
  border-radius: 50%;
  left: 0;
  top: calc(50% - 3px);
}
.prj_ben_warp .prj_ben_con .prj_ben_right {
  width: calc((5/24)*100%);
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp {
  background: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
  padding: 12px 16px;
  transition: 0.5s;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp.no_show {
  height: 0;
  overflow: hidden;
  padding: 0 16px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn {
  margin-bottom: 24px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn > div {
  width: 100%;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn .one_btn .ivu-btn {
  width: 100%;
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn .two_btn {
  display: flex;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn .two_btn .ivu-btn {
  width: calc(50% - 6px);
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp .approve_btn .two_btn .ivu-btn:first-child {
  margin-right: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul {
  border-left: 1px solid #dbe3eb;
  margin-left: 4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li {
  display: flex;
  height: 20px;
  align-items: center;
  padding-left: 24px;
  position: relative;
  color: #555555;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li:not(:last-child) {
  margin-bottom: 32px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li i {
  position: absolute;
  left: -4px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li.pass {
  color: #65CE7A;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li.pass i {
  width: 8px;
  height: 8px;
  background: #ffffff;
  border: 2px solid #65ce7a;
  border-radius: 50%;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li.current {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_warp > ul li.current i {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #3883e5;
  box-shadow: 0 0 4px rgba(56, 131, 229, 0.8);
  left: -5px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .his_btn {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  background: #ffffff;
  border: 1px solid #dbe3eb;
  border-radius: 4px;
  color: #555555;
  font-size: 12px;
  cursor: pointer;
  margin: 16px 0;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_desc {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #ffffff;
  border: 1px dashed #ff9900;
  border-radius: 4px;
  color: #555555;
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_desc > span {
  color: #3883e5;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp {
  overflow: hidden;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his {
  padding: 12px 0;
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his::-webkit-scrollbar {
  width: 0;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his .prj_b_title {
  display: flex;
  align-items: center;
  padding-right: 18px;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his .prj_b_title i {
  color: #a5acc4;
  margin-left: auto;
  cursor: pointer;
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his .approve_his_step {
  padding: 16px;
  overflow-y: auto;
  height: calc(100% - 52px);
}
.prj_ben_warp .prj_ben_con .prj_ben_right .approve_his_warp .approve_his .approve_his_step::-webkit-scrollbar {
  width: 0;
}
.prj_ben_warp .prj_ben_con .prj_b_cond_wrap {
  display: flex;
  padding-left: 24px;
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .prj_b_cond_wrap .prj_b_cond {
  width: 33.33%;
  display: inline-block;
}
.prj_ben_warp .prj_ben_con .prj_b_cond_wrap .prj_b_cond .prj_b_cond_label {
  display: inline-block;
  width: 6em;
  line-height: 32px;
}
.prj_ben_warp .prj_ben_con .prj_b_cond_wrap .prj_b_cond .prj_b_cond_component {
  margin-left: -4px;
  width: calc(100% - 6em - 12px);
  display: inline-block;
  vertical-align: middle;
}
.prj_ben_warp .prj_ben_con .prj_b_cond_wrap .prj_b_cond .prj_b_cond_component .ivu-input {
  font-size: 12px;
}
.prj_ben_warp .prj_ben_con .b_base_info .prj_b_cond_tips {
  display: flex;
  font-size: 12px;
  justify-content: start;
  padding-left: 16px;
  padding-bottom: 8px;
  color: #999999;
}
.prj_ben_warp .prj_ben_con .target_datum_info .prj_b_cond_tips {
  display: flex;
  font-size: 12px;
  justify-content: start;
  padding-left: 8px;
  padding-bottom: 8px;
  color: #999999;
}
.new_table .ivu-table-stripe .ivu-table-body tr:nth-child(2n - 1) td,
.new_table .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n - 1) td {
  background-color: #f5f8fb;
}
.new_table .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.new_table .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background-color: #ffffff;
}
.new_table .ivu-table-row:hover td {
  background-color: #d5e8fc !important;
}
.new_table .ivu-table-header thead th,
.new_table .ivu-table-fixed-header thead th {
  background: #ffffff;
}
.new_table .ivu-table th {
  color: #999999;
}
.new_table .ivu-table td,
.new_table .ivu-table th {
  height: 40px;
  padding: 0;
  border-right-color: transparent;
  border-bottom-color: transparent;
}
.new_table .ivu-table td:last-child,
.new_table .ivu-table th:last-child {
  border-right: none;
}
.new_table .ivu-table td *,
.new_table .ivu-table th * {
  font-size: 12px;
}
.new_table .ivu-table td .ivu-table-cell,
.new_table .ivu-table th .ivu-table-cell {
  padding: 0 8px;
  width: 100%;
}
.new_table .ivu-table td .ivu-table-cell .ivu-select-selected-value,
.new_table .ivu-table th .ivu-table-cell .ivu-select-selected-value {
  font-size: 12px;
}
.new_table .ivu-table td .ivu-table-cell .ivu-input,
.new_table .ivu-table th .ivu-table-cell .ivu-input,
.new_table .ivu-table td .ivu-table-cell .ivu-input-number,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number,
.new_table .ivu-table td .ivu-table-cell .ivu-input-number-input,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number-input {
  border-color: transparent;
  background-color: transparent;
}
.new_table .ivu-table td .ivu-table-cell .ivu-input-number-focused,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number-focused,
.new_table .ivu-table td .ivu-table-cell .ivu-input:focus,
.new_table .ivu-table th .ivu-table-cell .ivu-input:focus,
.new_table .ivu-table td .ivu-table-cell .ivu-input-number:focus,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number:focus,
.new_table .ivu-table td .ivu-table-cell .ivu-input:hover,
.new_table .ivu-table th .ivu-table-cell .ivu-input:hover,
.new_table .ivu-table td .ivu-table-cell .ivu-input-number:hover,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number:hover {
  box-shadow: none;
  border-color: #dbe3eb;
  background-color: #ffffff;
}
.new_table .ivu-table td .ivu-table-cell .ivu-input-number-handler-wrap,
.new_table .ivu-table th .ivu-table-cell .ivu-input-number-handler-wrap {
  display: none;
}
.new_table .ivu-table:after,
.new_table .ivu-table-fixed:after,
.new_table .ivu-table-fixed-right:after,
.new_table .ivu-table:before,
.new_table .ivu-table-fixed:before,
.new_table .ivu-table-fixed-right:before {
  width: 0;
  height: 0;
}
.new_table .ivu-table table,
.new_table .ivu-table-fixed table,
.new_table .ivu-table-fixed-right table {
  border: none;
}
.new_form.ivu-form {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.new_form.ivu-form.ivu-form-inline .ivu-form-item {
  width: calc(50% - 10px);
}
.new_form.ivu-form *:not(i) {
  font-size: 12px;
  color: #262626;
}
.new_form.ivu-form .ivu-form-item-label {
  color: #666666;
}
.his_tab .ivu-steps.ivu-steps-small .ivu-steps-head-inner > .ivu-steps-icon.ivu-icon {
  font-size: 18px;
}
.his_tab .ivu-steps-icon.ivu-icon.ivu-icon-ios-checkmark {
  color: #ffffff !important;
}
.his_tab .ivu-steps-status-process .ivu-steps-head-inner span {
  color: #74d286 !important;
}
.his_tab .ivu-steps-status-finish .ivu-steps-head-inner .ivu-icon-ios-close-circle {
  color: red !important;
}
.his_tab .ivu-steps-status-wait .ivu-steps-head-inner span {
  color: #ccc !important;
}
.tab_warp {
  box-shadow: 0 4px 18px 0 rgba(198, 213, 229, 0.4);
  background: #ffffff;
}
.tab_warp .tab_t_info {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f8fb;
  position: relative;
}
.tab_warp .tab_t_info > .col_item {
  display: flex;
  align-items: center;
  width: 33%;
}
.tab_warp .tab_t_info > .col_item * {
  font-size: 12px;
}
.tab_warp .tab_t_info > .col_item.pdL20 {
  padding-left: 20px;
}
.tab_warp .tab_t_info > .col_item .ivu-input {
  font-size: 12px;
  border: 1px solid #dcdee2;
}
.tab_warp .tab_t_info > .col_item > .col_con {
  width: calc(100% - 6em);
  display: flex;
}
.tab_warp .tab_t_info > .col_item > .col_con.fuzz_suer > div {
  width: 100%;
  position: relative;
}
.tab_warp .tab_t_info > .col_item > label {
  width: 6em;
  font-size: 12px;
}
.tab_warp .tab_t_info > .col_item .ivu-checkbox-wrapper {
  font-size: 12px;
}
.tab_warp .tab_t_info > .col_item.pd_l24 {
  padding-left: 24px;
}
.tab_warp .tab_t_tab {
  padding: 12px 16px 0;
  display: flex;
}
.tab_warp .tab_t_tab .tab_t_btn {
  margin-left: auto;
}
.tab_warp .tab_t_tab .tab_t_btn .ivu-btn {
  font-size: 12px;
}
.tab_warp .tab_t_tab ul {
  display: flex;
  align-items: center;
}
.tab_warp .tab_t_tab ul li {
  padding: 0 16px;
  line-height: 34px;
  color: #555555;
  height: 46px;
  cursor: pointer;
}
.tab_warp .tab_t_tab ul li.active {
  color: #3883E5;
  border-bottom: 2px solid #3883E5;
}
.tab_warp .tab_t_tab .tab_btn {
  margin-left: auto;
}
.prj_grad_warp {
  height: calc(100% - 60px);
}
.prj_grad_warp .prj_grad_bot {
  height: calc(100% - 58px);
  background: #f5f8fb;
  padding: 16px;
}
.prj_grad_warp .prj_grad_bot .prj_grad_bot_con {
  box-shadow: 0 4px 18px 0 rgba(198, 213, 229, 0.4);
  background: #ffffff;
  height: 100%;
  overflow-y: auto;
}
.prj_grad_warp .prj_grad_bot .prj_grad_bot_con .prj_grad_bot_page {
  padding: 0 16px;
}
.prj_role_warp {
  height: calc(100% - 60px);
}
.prj_role_warp .prj_role_bot {
  height: calc(100% - 115px);
  background: #f5f8fb;
  padding: 16px;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con {
  box-shadow: 0 4px 18px 0 rgba(198, 213, 229, 0.4);
  background: #ffffff;
  height: 100%;
  overflow-y: auto;
  padding: 10px 16px;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con.prov {
  padding: 0;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .ivu-table-cell * {
  font-size: 12px;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .list_box .title {
  color: #c8cddc;
  margin: 12px 0;
  display: flex;
  align-items: center;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .list_box .title > span {
  color: #3883e5;
  padding-left: 8px;
  border-left: 2px solid #3883e5;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .list_box .title .title_right {
  margin-left: auto;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .list_box .title .title_right span {
  width: 36px;
  height: 36px;
  line-height: 36px;
  background-color: rgba(200, 205, 220, 0.2);
  color: #c8cddc;
  text-align: center;
  border-radius: 50%;
  font-size: 16px;
  vertical-align: top;
  margin-left: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
.prj_role_warp .prj_role_bot .prj_role_bot_con .list_box .title .title_right span.active {
  background-color: #3883e5;
  color: #ffffff;
}
.person_list {
  display: flex;
  flex-wrap: wrap;
  padding: 4px 0 32px;
}
.person_list:not(:last-child) {
  border-bottom: 1px dashed #dddee1;
}
.person_list > li,
.person_list .ivu-poptip {
  padding: 14px 12px;
  box-shadow: 0 1px 12px 0 rgba(56, 131, 229, 0.16);
  width: 185px;
  margin: 0 12px 16px 0;
  position: relative;
}
.person_list > li > i.icon-minusSign {
  width: 24px;
  height: 24px;
  background: #e65d4e;
  border-radius: 50%;
  color: #ffffff;
  position: absolute;
  top: -12px;
  right: -12px;
  text-align: center;
  font-weight: bold;
  cursor: pointer;
}
.person_list .add_info {
  text-align: center;
  background: #ffffff;
  border-radius: 5px;
  box-shadow: 0 1px 12px 0 rgba(0, 0, 0, 0);
  border: 1px dashed rgba(56, 131, 229, 0.8);
  color: #3883e5;
  cursor: pointer;
  padding: 0;
}
.person_list .add_info:hover {
  background-color: rgba(56, 131, 229, 0.05);
}
.person_list .add_info .add_warp {
  padding: 22px;
  height: 100%;
}
.person_list .add_info .add_warp span {
  line-height: 24px;
}
.person_list .add_info .add_warp i.icon-jia {
  font-size: 24px;
}
.person_list .ivu-poptip-rel {
  width: 100%;
  height: 100%;
}
.person_list img {
  width: 48px;
  height: 48px;
  display: inline-block;
}
.person_list > li > div:last-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #999999;
  margin-top: 4px;
}
.person_list > li > div:last-child span {
  color: #555555;
}
.person_list > li > div .user {
  display: inline-block;
  width: calc(100% - 52px);
  vertical-align: top;
  padding-left: 13px;
  position: relative;
}
.person_list > li > div .user i {
  position: absolute;
  right: 0;
  top: 0;
  width: 18px;
  height: 18px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 10px;
}
.person_list > li > div .user i.icon-member {
  background: rgba(56, 131, 229, 0.1);
  color: #3883e5;
}
.person_list > li > div .login_name {
  line-height: 16px;
  font-size: 14px;
  color: #999999;
  margin-bottom: 10px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: calc(100% - 18px);
}
.person_list > li > div .user_name {
  font-size: 18px;
  font-weight: 700;
  color: #262626;
}
.add_info_pop {
  z-index: 100;
}
.add_info_pop .ivu-poptip-body {
  padding: 0;
}
.add_info_pop .ivu-poptip-body .user_list > div:first-child {
  padding: 12px 16px;
}
.add_info_pop .ivu-poptip-body .user_list ul {
  max-height: 150px;
  overflow-y: auto;
  line-height: 30px;
}
.add_info_pop .ivu-poptip-body .user_list ul li {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 4px 16px;
  color: #999999;
}
.add_info_pop .ivu-poptip-body .user_list ul li:hover,
.add_info_pop .ivu-poptip-body .user_list ul li.active {
  background: #f8f8f8;
  cursor: pointer;
}
.add_info_pop .ivu-poptip-body .user_list ul li span {
  color: #555555;
}
.prj_basic_res_group {
  margin-top: 12px;
}
.prj_basic_res_group li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.prj_basic_res_group li * {
  font-size: 12px;
}
.prj_basic_res_group li .res_title {
  width: 10em;
  margin-right: 12px;
  display: flex;
  align-items: center;
}
.prj_basic_res_group li .res_title > span {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  background: #f5f8fb;
  border: 1px solid #e7eef5;
  border-radius: 3px;
  white-space: nowrap;
  max-width: 10em;
  text-overflow: ellipsis;
  overflow: hidden;
  color: #555555;
  display: inline-block;
}
.prj_basic_res_group li .res_user_l {
  margin-right: 34px;
}
.prj_basic_res_group li .res_user {
  width: calc((100% - 10em - 12px)/2 - 17px);
  display: flex;
  align-items: center;
}
.prj_basic_res_group li .res_user > span {
  width: 32px;
  color: #666666;
}
.prj_basic_res_group li .res_user > div {
  width: calc(100% - 32px);
}
.ivu-cascader-rel:hover .ivu-input {
  border-color: rgba(56, 131, 229, 0.8) !important;
}
.buoy_operate_warp {
  position: fixed;
  right: 280px;
  bottom: 40px;
}
.buoy_operate_warp i.iconfont {
  width: 48px;
  height: 48px;
  background: rgba(165, 172, 196, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a5acc4;
  font-size: 22px;
  cursor: pointer;
}
.notional_pooling .prj_value_title {
  display: flex;
  height: 22px;
  line-height: 22px;
  padding-left: 16px;
  border-left: 3px solid #3883e5;
  position: relative;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
}
.notional_pooling .prj_value_title .actionWarp {
  margin-left: auto;
  margin-right: 16px;
  color: #999999;
}
.notional_pooling .prj_value_title .actionWarp:hover {
  color: #3883e5;
}
.notional_pooling .prj_value_title .actionWarp span {
  color: #999999;
  cursor: pointer;
  font-size: 12px;
  display: inline-flex;
}
.notional_pooling .prj_value_title .actionWarp span:hover {
  color: #3883e5;
}
.notional_pooling .prj_value_title .actionWarp.disable:hover {
  color: #999999;
}
.notional_pooling .prj_value_title .actionWarp.disable span {
  cursor: default;
}
.notional_pooling .prj_value_title .actionWarp.disable span:hover {
  color: #999999;
}
.notional_pooling .prj_value_title:after {
  content: '';
  position: absolute;
  left: -7px;
  top: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.notional_pooling .prj_value_title:before {
  content: '';
  position: absolute;
  left: -7px;
  bottom: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.notional_pooling .prj_b_title {
  height: 22px;
  line-height: 22px;
  padding-left: 16px;
  border-left: 3px solid #3883e5;
  position: relative;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.notional_pooling .prj_b_title .scope_view_btn {
  color: #A5ACC4;
  font-size: 12px;
  margin-left: 8px;
  cursor: pointer;
}
.notional_pooling .prj_b_title:after {
  content: '';
  position: absolute;
  left: -7px;
  top: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.notional_pooling .prj_b_title:before {
  content: '';
  position: absolute;
  left: -7px;
  bottom: -4px;
  width: 0;
  height: 0;
  border: 4px solid transparent;
  border-right-color: #ffffff;
}
.notional_pooling .responsibility_group {
  padding-top: 16px;
}
.base_line_info {
  position: fixed;
  z-index: 999;
  padding: 8px 12px;
  background-color: #fff;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  font-size: 12px;
  line-height: 22px;
}
.base_line_info .base_line_info_m {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}
.base_line_info li {
  display: flex;
}
.base_line_info li span {
  color: #999999;
  width: 140px;
}
.ellipsis_tooltip_two .ivu-tooltip {
  width: 100%;
}
.ellipsis_tooltip_two .ivu-tooltip .ivu-tooltip-rel {
  width: 100%;
}
.ellipsis_tooltip_two .ivu-tooltip .ivu-tooltip-rel .ellipsis_two {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
  width: 100%;
}
.cost_basis_top_edit {
  display: flex;
  align-items: center;
}
.cost_basis_top_edit .cost {
  padding: 0 12px;
  height: 38px;
  display: flex;
  align-items: center;
  width: calc(100% - 168px);
}
.cost_basis_top_edit .cost > div {
  display: flex;
  width: 100%;
  align-items: center;
}
.cost_basis_top_edit .cost > div > span {
  width: 46px;
  cursor: pointer;
  color: #999999;
}
.cost_basis_top_edit .cost > div > span.active {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}
.cost_basis_top_edit .cost > div > i {
  cursor: pointer;
}
.cost_basis_top_edit .cost > div > i.no_active {
  color: #c5c8ce;
  cursor: no-drop;
}
.cost_basis_top_edit .cost > div > div {
  display: flex;
  width: calc(100% - 78px);
  align-items: center;
  position: relative;
  overflow: hidden;
  height: 24px;
}
.cost_basis_top_edit .cost > div > div > ul {
  display: flex;
  align-items: center;
  position: absolute;
  left: 0px;
}
.cost_basis_top_edit .cost > div > div > ul li {
  color: #999999;
  font-size: 14px;
  margin-right: 22px;
  line-height: 22px;
  cursor: pointer;
}
.cost_basis_top_edit .cost > div > div > ul li.active {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}
.cost_basis_top_edit .prj_b_btn {
  margin-left: auto;
}
.costBasisItem_view {
  display: flex;
  align-items: center;
  margin-left: auto;
  overflow: hidden;
}
.costBasisItem_view li {
  color: #999999;
  font-size: 14px;
  margin-right: 24px;
  line-height: 22px;
  cursor: pointer;
}
.costBasisItem_view li.active {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}
.cost_basic_emp_month_avg {
  /*padding: 8px 16px 0 16px;*/
}
.cost_basic_emp_month_avg .cost_basic_emp_month_avg_title {
  margin-top: 12px;
  margin-bottom: 12px;
  display: inline-block;
  padding-left: 8px;
  font-size: 12px;
}
.cost_basic_emp_month_avg_table .ivu-table-header thead tr:first-child th:not(:last-child) {
  border-right: 1px solid #dbe3eb !important;
}
.cost_basic_emp_month_avg_table .ivu-table-header thead tr:first-child th {
  text-align: center;
}
.cost_basic_emp_month_avg_table .ivu-table-header thead tr:nth-child(2) th:nth-child(2n) {
  border-right: 1px solid #dbe3eb !important;
}
.cost_basic_emp_month_avg_table .ivu-table-header thead tr:nth-child(2) th:last-child {
  border-right: none !important;
}
.cost_basic_emp_month_avg_table .ivu-table-header th {
  background-color: #f5f8fb !important;
}
.cost_basic_emp_month_avg_table .ivu-table-body .ivu-table-tbody td:nth-child(2n) {
  border-right: 1px solid #dbe3eb !important;
}
.cost_basic_emp_month_avg_table .ivu-table-body .ivu-table-tbody td:last-child {
  border-right: none !important;
}
/*异常偏差管理-项目集详情页*/
.prj_bug_warp.header_fix .prjHeader.header_menu {
  position: fixed !important;
  top: 0;
}
.prj_bug_warp.header_fix .prj_bug_con {
  padding-top: 76px;
  height: 100%;
  overflow: auto;
}
.prj_bug_warp .prj_bug_con {
  min-height: calc(100% - 60px);
  background: #f8f8f9;
  padding: 16px;
  display: flex;
  position: relative;
}
.prj_bug_warp .prj_bug_con .prj_bug_left {
  width: 100%;
  height: 100%;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
  padding-top: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info > div {
  margin-bottom: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter {
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter.col_4 .fol_item {
  width: calc(100% / 4);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item {
  display: flex;
  height: 32px;
  align-items: center;
  margin-bottom: 4px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item.pdL20 {
  padding-left: 20px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div {
  padding-left: 24px;
  color: #262626;
  width: calc(100% - 6em);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis {
  display: flex;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: calc(100% - 10px);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item * {
  font-size: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .prj_b_b_filter .fol_item label {
  width: 6em;
  color: #666666;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_b_l_bot .b_base_info .new_table_warp {
  padding: 0 16px;
  border-top: 1px solid #f1f5f9;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(144, 144, 144, 0.2);
  padding: 12px 0;
  min-height: calc(100% - 242px);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info > div {
  margin-bottom: 24px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top {
  display: flex;
  padding: 0 16px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .prj_dvt_top_cond label {
  width: 6em;
  color: #666666;
  font-size: 12px;
  display: inline-block;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .prj_dvt_top_cond .prj_dvt_top_cond_month {
  display: inline-block;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .prj_dvt_top_cond .prj_dvt_top_cond_month .ivu-input {
  font-size: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .prj_dvt_top_cond .prj_dvt_top_cond_month .ivu-select-dropdown {
  font-size: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank {
  margin-left: auto;
  display: flex;
  font-size: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span {
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  padding: 0 12px;
  border: 1px solid #dddee1;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span .ivu-badge {
  border: none;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span .ivu-badge > span {
  border: none;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span:first-child {
  border-radius: 5px 0 0 5px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span:last-child {
  border-radius: 0 5px 5px 0;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span:not(:first-child) {
  margin-left: -1px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_dvt_top .tab_blank span.active {
  color: #3883e5;
  border-color: #3883e5;
  z-index: 2;
  position: relative;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .notional_pooling .prj_b_title .ivu-icon {
  margin-left: 8px;
  cursor: pointer;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .notional_pooling .prj_b_title .costStatistic {
  font-size: 12px;
  margin-left: auto;
  padding-right: 34px;
  cursor: pointer;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .notional_pooling .prj_b_title .costStatistic .icon-statistic {
  font-size: 14px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .notional_pooling .new_table_warp {
  padding: 0 16px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter {
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-wrap: wrap;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter.col_2 .fol_item {
  width: calc(100% / 2);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter.col_3 .fol_item {
  width: calc(100% / 3);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter.col_4 .fol_item {
  width: calc(100% / 4);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item {
  display: flex;
  height: 32px;
  align-items: center;
  margin-bottom: 4px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item.pdL20 {
  padding-left: 20px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item > div {
  padding-left: 24px;
  color: #262626;
  width: calc(100% - 7em);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis {
  display: flex;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item .span_ellipsis > span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: calc(100% - 10px);
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item * {
  font-size: 12px;
}
.prj_bug_warp .prj_bug_con .prj_bug_left .prj_dvt_b_l_bot .b_base_info .prj_b_b_filter .fol_item label {
  width: 7em;
  color: #666666;
}
.prj_bug_warp .prj_bug_con .prj_b_title {
  height: 22px;
  line-height: 22px;
  padding-left: 16px;
  border-left: 3px solid #3883e5;
  position: relative;
  color: #262626;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
