@charset "utf-8";
#headMenu .ivu-menu-submenu .ivu-btn.ivu-btn-primary {
  background-color: #3abcff !important;
}
/* CSS Document */
@font-face {
  font-family: 'iconfont';
  /* project id 308560 */
  src: url('//at.alicdn.com/t/font_n4xbv3thkkj1nhfr.eot');
  src: url('//at.alicdn.com/t/font_n4xbv3thkkj1nhfr.eot?#iefix') format('embedded-opentype'), url('//at.alicdn.com/t/font_n4xbv3thkkj1nhfr.woff') format('woff'), url('//at.alicdn.com/t/font_n4xbv3thkkj1nhfr.ttf') format('truetype'), url('//at.alicdn.com/t/font_n4xbv3thkkj1nhfr.svg#iconfont') format('svg');
}
.stakeholder_library_import_modal .sli_warp .tree_left {
  width: 200px;
  overflow: auto;
  border: 1px dashed #3883e5;
  background: #F5F8FB;
  padding: 12px 0 12px 12px;
}
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.iconfont {
  font-family: "iconfont" !important;
  /*font-size:16px;*/
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0px;
  -moz-osx-font-smoothing: grayscale;
  vertical-align: middle;
}
html,
body {
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "寰蒋闆呴粦", "Arial", sans-serif !important;
  height: 100%;
  background-color: white !important;
  font-size: 14px;
  color: #333 !important;
  margin: 0;
  padding: 0;
}
ul,
ul li {
  list-style: outside none none;
}
html,
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
dl,
dt,
dd,
ul,
li,
p,
form,
table,
th,
td,
dl,
dd {
  margin: 0;
  padding: 0;
  text-decoration: none;
}
.main {
  background-color: white!important;
  /*height:100%;*/
  min-width: 1244px !important;
  margin: 0 auto;
  padding: 0;
}
input:-moz-placeholder,
textarea:-moz-placeholder {
  color: #bbbec4;
}
input:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #bbbec4;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #bbbec4;
}
.fl-lf {
  float: left !important;
}
.fl-rg {
  float: right !important;
}
.fl-none {
  float: none !important;
}
.mr-rg10 {
  margin-right: 10px !important;
}
.mr-lf0 {
  margin-left: 0 !important;
}
.mr-lf20 {
  margin-left: 20px !important;
}
.mr-lf28 {
  margin-left: 28px !important;
}
.mr-top0 {
  margin-top: 0 !important;
}
.mr-top8 {
  margin-top: 8px !important;
}
.mr-top48 {
  margin-top: 48px !important;
}
.mr-rg8 {
  margin-right: 8px !important;
}
.mr-rg16 {
  margin-right: 16px !important;
}
.mr-top10 {
  margin-top: 10px !important;
}
.mr-top15 {
  margin-top: 15px !important;
}
.mr-top16 {
  margin-top: 16px !important;
}
.mr-top20 {
  margin-top: 20px !important;
}
.mr-btm0 {
  margin-bottom: 0 !important;
}
.mr-btm48 {
  margin-bottom: 48px !important;
}
.white {
  color: #fff !important;
}
.orange {
  color: #3883e5 !important;
}
.red {
  color: #ff5c33 !important;
}
.green {
  color: #0c6 !important;
}
.bg-orange {
  background-color: #3883e5 !important;
}
.blue {
  color: #2d8cf0 !important;
}
.bg-white {
  background-color: #fff !important;
}
.bg-unset {
  background-color: unset !important;
}
.box-shadow {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  border-color: #eee;
}
.border {
  border: 1px solid #d7dde4;
}
.border-none {
  border: none !important;
}
.border-dashed {
  border: 1px dashed #e3e8ee;
}
.grey {
  color: #80848f;
}
.grey-text {
  color: #666 !important;
}
.light-grey {
  color: #bbbec4;
}
.bg-light-grey {
  background-color: #f8f8f8 !important;
}
.bg-dark-grey {
  background-color: #80848f !important;
}
.lh16 {
  line-height: 16px !important;
}
.lh20 {
  line-height: 20px !important;
}
.hg-20 {
  height: 20px;
  line-height: 20px;
}
.lh26 {
  line-height: 26px !important;
}
.lh32 {
  line-height: 32px !important;
}
.hg32 {
  height: 32px !important;
}
.hg-24 {
  height: 24px;
  line-height: 24px;
}
.pd0 {
  padding: 0 !important;
}
.pd8 {
  padding: 8px !important;
}
.pd5 {
  padding: 5px !important;
}
.pd6 {
  padding: 6px !important;
}
.pd10 {
  padding: 10px !important;
}
.mr-lf5 {
  margin-left: 5px !important;
}
.mr-rg5 {
  margin-right: 5px !important;
}
.mr-btm5 {
  margin-bottom: 5px !important;
}
.mr-btm10 {
  margin-bottom: 10px !important;
}
.mr-btm16 {
  margin-bottom: 16px !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.vertical-top {
  vertical-align: top !important;
}
.vertical-mid {
  vertical-align: middle;
}
.vertical-bottom {
  vertical-align: bottom !important;
}
.font-bold {
  font-weight: bold;
}
.font-weight-noraml {
  font-weight: normal !important;
}
.inline-block {
  display: inline-block !important;
}
.font-style-normal {
  font-style: normal !important;
}
.conrainer {
  width: 90%;
  display: block;
  margin: 0 auto;
  position: relative;
  height: 100%;
  min-width: 1190px;
}
.border-top {
  border-top: 1px solid rgba(222, 221, 221, 0.5) !important;
}
.border-btm {
  border-bottom: 1px solid rgba(222, 221, 221, 0.5) !important;
}
.c {
  clear: both;
}
.font-16 {
  font-size: 16px !important;
  color: #333;
}
.font-14 {
  font-size: 14px !important;
}
.font-12 {
  font-size: 12px !important;
}
.font-20 {
  font-size: 20px !important;
}
.pd-side0 {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.pd-side5 {
  padding-left: 5px !important;
  padding-right: 5px !important;
}
.pd-side16 {
  padding-left: 16px !important;
  padding-right: 16px !important;
}
.pd-side16 .ivu-table {
  color: #333 !important;
}
.pd-top10 {
  padding-top: 10px !important;
}
.pd-btm0 {
  padding-bottom: 0 !important;
}
.pd-top8 {
  padding-top: 8px !important;
}
.pd-btm8 {
  padding-bottom: 8px !important;
}
.pd-top0 {
  padding-top: 0 !important;
}
.display-inbl {
  display: inline-block;
}
.cursor-pointer {
  cursor: pointer;
}
/*--------------------------瀵筰view鏍峰紡鐨勪慨鏀�-----------------------------*/
.conrainer .layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
.conrainer .layout-content {
  min-height: 500px;
  margin: 15px;
  overflow: hidden;
  background: #fff;
  border-radius: 4px;
}
.conrainer .layout-content-main {
  padding: 10px;
}
.conrainer .layout-copy {
  text-align: center;
  padding: 10px 0 20px;
  color: #9ea7b4;
}
.conrainer .layout-menu-left {
  background: #ebf0f5;
}
.conrainer .layout-nav {
  margin-left: 100px;
}
.conrainer .layout-header {
  height: 60px;
  background: #fff;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}
.conrainer .layout-logo-left {
  width: 90%;
  height: 30px;
  border-radius: 3px;
  margin: 15px auto;
}
.conrainer .layout-ceiling-main a {
  color: #9ba7b5;
}
.conrainer .layout-hide-text .layout-text {
  display: none;
}
.conrainer .ivu-col {
  transition: width 0.2s ease-in-out;
}
/*鑿滃崟-menu*/
.ivu-menu-item > i {
  vertical-align: middle;
}
.ivu-menu-submenu-title > i {
  vertical-align: middle;
}
.conrainer .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active,
.conrainer .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu .ivu-menu-item-active:hover {
  border-right: none;
  color: #fff;
  background: #3883e5 !important;
}
.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-horizontal .ivu-menu-submenu {
  padding-left: 10px !important;
  padding-right: 10px !important;
}
/*杈撳叆妗�*/
.ivu-input {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
/*瀵艰埅椤�*/
.layout-breadcrumb .ivu-breadcrumb span {
  font-size: 12px !important;
  font-weight: normal !important;
}
/*琛ㄦ牸*/
.ivu-table th {
  font-weight: normal;
}
/*缈婚〉*/
.ivu-page-item:hover,
.ivu-page-next:hover,
.ivu-page-prev:hover {
  border-color: #3883e5 !important;
}
.ivu-page-item:hover a,
.ivu-page-next:hover a,
.ivu-page-prev:hover a {
  color: #3883e5 !important;
}
.ivu-page-item-active {
  background-color: #3883e5 !important;
  border-color: #3883e5 !important;
}
.ivu-page-item-active:hover a {
  color: #fff !important;
}
/*寮圭獥*/
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
}
.vertical-center-modal .ivu-modal {
  max-height: 100vh !important;
}
.vertical-top-modal .ivu-modal {
  top: 20px;
}
.ivu-modal-header-inner,
.ivu-modal-header p {
  font-weight: normal !important;
}
/*tips*/
.ivu-tooltip-inner {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
/*鍗＄墖card*/
.ivu-card-head-inner,
.ivu-card-head p {
  font-weight: normal !important;
}
/*鎸夐挳*/
.ivu-btn-primary {
  background-color: #3883e5 !important;
  border-color: #3883e5 !important;
}
.ivu-btn-primary.active,
.ivu-btn-primary:active {
  color: rgba(56, 131, 229, 0.8) !important;
  border-color: rgba(56, 131, 229, 0.8) !important;
}
.ivu-btn-primary:hover {
  background-color: rgba(56, 131, 229, 0.8) !important;
  border-color: rgba(56, 131, 229, 0.8) !important;
  color: #fff !important;
}
.ivu-btn-text:hover {
  color: rgba(56, 131, 229, 0.8) !important;
}
.ivu-btn-text.active,
.ivu-btn-text:active {
  color: rgba(56, 131, 229, 0.8) !important;
}
.ivu-btn,
.ivu-btn:active,
.ivu-btn:focus {
  outline: none !important;
  box-shadow: none !important;
}
/*杈撳叆妗�*/
.ivu-input:hover {
  border-color: rgba(56, 131, 229, 0.8) !important;
}
.ivu-input:focus {
  border-color: rgba(56, 131, 229, 0.8) !important;
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
/*閿欒鎻愮ず*/
.ivu-form-item {
  margin-bottom: 12px !important;
}
.ivu-form-item-error {
  margin-bottom: 24px !important;
}
/*涓嬫媺妗�*/
.ivu-select-visible .ivu-select-selection {
  border: 1px solid rgba(56, 131, 229, 0.8) !important;
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
.ivu-select-selection:hover,
.ivu-select:focus .ivu-select-selection {
  border: 1px solid rgba(56, 131, 229, 0.8) !important;
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-selected,
.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-selected:hover {
  color: #657180 !important;
  background: #f5f5f5 !important;
}
.ivu-select-item-selected,
.ivu-select-item-selected:hover {
  background: #f5f5f5 !important;
  color: #495060 !important;
}
.ivu-select {
  position: relative;
}
.ivu-modal .ivu-modal-content .ivu-select-dropdown {
  /* position: fixed !important; */
  top: 30px;
}
/*.ivu-select-selection{*/
/*border: 1px solid #ebeff3 !important;*/
/*}*/
.ivu-col {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
/*涓嬫媺妗嗘棤鑳屾櫙鑹�*/
.data-select {
  display: inline-block;
  margin-left: 20px;
}
.data-select,
.data-select.ivu-select:focus,
.data-select .ivu-select-selection:hover,
.data-select.ivu-select:focus .ivu-select-selection,
.data-select.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.data-select.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  outline: none !important;
  border-color: unset !important;
  box-shadow: none;
}
.data-select .ivu-select-selection,
.data-select .ivu-input:focus,
.date-select .ivu-input:focus {
  background-color: unset !important;
  border: none !important;
  box-shadow: none !important;
}
.data-select .ivu-select-visible .ivu-select-selection,
.data-select .ivu-select-selection:hover {
  border-color: unset !important;
  box-shadow: none !important;
}
/*tag鏍囩*/
.ivu-tag .ivu-icon-ios-close-empty:hover {
  color: #ed3f14;
}
.tag-flex-width {
  width: 100%;
}
.tag-flex-width .ivu-tag-text {
  width: calc(100% - 20px);
  display: inline-block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
}
/*绾ц仈閫夋嫨*/
.ivu-cascader .ivu-select-dropdown {
  max-width: 100%;
  overflow: auto !important;
}
.ivu-cascader-label {
  padding: 0 16px 0 7px !important;
}
/*鏁板瓧*/
.data-select.ivu-input-number {
  background-color: unset;
  border-color: unset;
  border: none;
  box-shadow: none;
}
.data-select .ivu-input-number-input {
  background-color: unset;
}
.data-select.ivu-input-number-focused {
  background-color: #f1f1f1;
}
.data-select .ivu-input-number-handler-wrap {
  background-color: #f1f1f1;
  box-shadow: none;
}
/*缁勫悎鎺т欢,鏍�*/
.data-select .ivu-input {
  background-color: unset;
  border: none;
}
.data-select .ivu-input:hover,
.data-select .ivu-input:focus {
  border-color: unset !important;
  background-color: #f1f1f1;
  box-shadow: none;
  border: none;
}
.choose_window {
  min-width: 200px;
  height: auto;
  z-index: 100000;
  position: absolute;
  top: 32px;
  z-index: 100;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  background: white;
}
.border_radius5 {
  border-radius: 5px !important;
  -web-border-radius: 5px !important;
  -moz-border-radius: 5px !important;
}
/*琛ㄦ牸*/
.ivu-table-wrapper {
  border: none !important;
}
.ivu-table-cell {
  padding-left: 8px !important;
  padding-right: 8px !important;
}
.ivu-table:after,
.ivu-table:before {
  display: none !important;
}
.ivu-table-border td,
.ivu-table-border th {
  border: none !important;
}
/*琛ㄦ牸-琛ㄥご棰滆壊缁熶竴*/
.ivu-table-header thead th,
.ivu-table-fixed-header thead th {
  background: #f1f1f1;
}
/*tab閫夐」*/
.ivu-tabs-bar {
  border-bottom: none !important;
}
/*--------------------------------------椤甸潰鍐呭------------------------------------*/
/*鐧诲綍椤甸潰*/
.login-main {
  margin: 0 auto;
  display: block;
  height: auto;
  position: relative;
  padding: 10% 5% 5% 5%;
}
/*杞挱鍥�*/
.slideshow {
  width: 66vh;
  height: 313px;
  position: relative;
  overflow: hidden;
  display: inline-block;
  margin-left: 10%;
}
.slideshow img {
  width: 100%;
  height: 100%;
  display: block;
}
/*鐧诲綍妗�*/
.log-box {
  width: 30%;
  display: inline-block;
  float: right;
  margin-right: 10%;
}
.log-box h1 {
  font-size: 28px;
  line-height: 36px;
  color: #2d8cf0;
}
.login-language {
  margin: 12px 0 30px;
}
.login-language a {
  font-size: 12px;
  text-align: center;
  color: #2d8cf0;
  line-height: 24px;
  text-decoration: underline;
}
.login-language a:hover {
  text-decoration: underline;
}
.login-text {
  width: 100%;
  display: block;
  height: 40px;
  line-height: 40px\9;
  margin-top: 10px;
  background: #fff;
  font-size: 12px;
  color: #495060;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  position: relative;
}
.login-text input {
  width: 100%;
  display: block;
  height: 38px;
  line-height: 38px\9;
  padding-left: 40px;
  font-size: 12px;
  color: #495060;
  border: 1px solid #dddee1;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  background-repeat: no-repeat;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
}
.login-text i {
  font-size: 18px;
  color: #80848f;
  display: inline-block;
  font-style: normal;
  position: absolute;
  width: 40px;
  height: 40px;
  line-height: 40px;
  left: 0;
  text-align: center;
  top: 0;
}
.login-text input:hover {
  border: 1px solid #5cadff;
}
.login-text input:focus {
  outline: none;
  border-color: #57a3f3;
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  -webkit-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
  -moz-box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2);
}
.login-text input:focus + i {
  color: #5cadff;
}
.find-password {
  float: right;
  color: #2d8cf0;
  text-decoration: underline;
}
.find-password:hover {
  text-decoration: underline;
}
.copy-right {
  width: 100%;
  background: #ebebeb;
  position: fixed;
  bottom: 0px;
  padding: 20px 0;
  text-align: center;
  color: #495060;
}
.app-code {
  display: block;
  position: fixed;
  top: 46px;
  right: 20px;
  text-align: center;
}
.app-code span {
  display: block;
  color: #80848f;
}
.app-code img {
  display: block;
  width: 86px;
  height: 86px;
  margin-top: 5px;
}
.app-code a {
  text-align: center;
  color: #2d8cf0;
  text-decoration: underline;
  margin-top: 5px;
  display: block;
}
.app-code a:hover {
  text-decoration: underline;
}
/*home*/
.conrainer .layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  overflow: auto;
}
.conrainer .layout-content {
  min-height: 200px;
  /*height:calc(100% - 68px);*/
  /*height:-webkit-calc(100% - 68px);*/
  /*height:-moz-calc(100% - 68px);*/
  margin: 0;
  overflow: hidden;
  background: transparent;
  border-radius: 4px;
  width: 100%;
}
.conrainer .layout-logo {
  /*width: 100px;*/
  height: 36px;
  /*background: #5b6270;*/
  border-radius: 3px;
  float: left;
  position: relative;
  top: 12px;
  left: 0;
}
.conrainer .ivu-menu-dark.ivu-menu-horizontal .ivu-menu-item,
.ivu-menu-dark.ivu-menu-horizontal .ivu-menu-submenu {
  color: hsla(0, 0%, 100%, 0.7);
}
.conrainer .ivu-menu-primary {
  background-color: #3883e5;
}
.conrainer .ivu-menu-primary.ivu-menu-horizontal .ivu-menu-item-active,
.ivu-menu-primary.ivu-menu-horizontal .ivu-menu-item:hover,
.ivu-menu-primary.ivu-menu-horizontal .ivu-menu-submenu-active,
.ivu-menu-primary.ivu-menu-horizontal .ivu-menu-submenu:hover {
  background-color: #3883e5;
}
.conrainer .layout-assistant {
  /*width: 300px;*/
  margin: 0 auto;
  height: inherit;
}
.conrainer .layout-content-main {
  padding: 10px;
}
.layout-logo-left {
  height: 36px;
  border-radius: 3px;
  float: left;
  position: relative;
  top: 12px;
  left: 20px;
}
.layout-logo img {
  height: 100%;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  float: left;
}
.layout-logo span {
  float: left;
  display: inline-block;
  /*height:36px;*/
  color: #fff;
  line-height: 16px;
  padding: 10px;
  font-size: 20px;
  font-weight: bold;
}
.layout-container .ivu-row .ivu-col {
  padding: 8px 0;
}
.personal-center {
  float: right;
  margin-right: 0px;
  color: #fff;
  font-size: 16px;
}
.personal-center .ivu-menu-submenu .ivu-menu-submenu-title span {
  display: inline-block;
  color: #fff;
  line-height: 40px;
  height: 40px;
  /*width: 40px;*/
  text-align: center;
  font-size: 14px;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.personal-center .ivu-menu-submenu .ivu-menu-submenu-title img {
  width: 36px;
  height: 36px;
  border-radius: 5px;
  vertical-align: middle;
}
.personal-center .ivu-menu-submenu {
  padding-right: 0 !important;
}
.conrainer .ivu-menu-primary.ivu-menu-horizontal .personal-center .ivu-menu-submenu:hover,
.conrainer .ivu-menu-primary.ivu-menu-horizontal .personal-center .ivu-menu-item-active {
  background-color: unset !important;
}
/*椤圭洰璁″垝*/
.choose-prj {
  display: inline-block;
  width: auto !important;
  max-width: 500px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.choose-prj a,
.choose-prj a:hover {
  color: #80848f;
}
/*涓嬫媺閫夋嫨*/
.choose-prj .ivu-select-dropdown {
  width: 100% !important;
  padding: 10px 0;
  margin: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  max-height: 50% !important;
  /*overflow-y: auto !important;*/
  font-size: 0;
  /* 鎵�鏈夋祻瑙堝櫒 */
  *word-spacing: -1px;
  /* IE6銆�7 */
  left: 0 !important;
  box-shadow: none;
  background: none;
  background-color: unset;
}
.choose-prj .ivu-select-selection {
  background: none;
}
.choose-prj .ivu-select-dropdown .ivu-select-dropdown-list {
  box-shadow: 0 1px 6px rgba(255, 153, 0, 0.5);
  padding: 10px;
  white-space: normal;
  max-height: 100px;
  overflow-y: auto;
  background: white;
}
.choose-prj .ivu-select-dropdown .ivu-select-dropdown-list:before {
  content: '';
  position: fixed;
  width: 0;
  height: 0;
  left: 30px;
  top: -10px;
  border: 10px solid;
  border-color: transparent transparent rgba(255, 153, 0, 0.2) transparent;
}
.choose-prj .ivu-select-dropdown .ivu-select-dropdown-list:after {
  content: '';
  position: fixed;
  max-height: 100px;
  overflow-y: auto;
  width: 0;
  height: 0;
  left: 30px;
  top: -9px;
  border: 10px solid;
  border-color: transparent transparent #ffffff transparent;
}
.choose-prj .ivu-select-dropdown .ivu-select-item {
  padding: 8px;
  width: calc((100% - 50px)/5);
  width: -webkit-calc(50%/5);
  width: -moz-calc(50%/5);
  max-width: 20%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline-block;
  font-size: 14px;
}
.choose-prj .ivu-select-selection,
.choose-prj .ivu-select-visible .ivu-select-selection {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
.perform {
  display: inline-block;
  float: right;
  /*margin-top:8px;*/
}
.perform button {
  /*margin-left:5px;*/
}
/* .search-icon{
    font-size: 16px;
    vertical-align: middle;
    color:#ff9900;
}
.prj-search{
    display: inline-block;
}
.prj-search .ivu-select-dropdown{
    display: none;
}
.prj-search:hover .ivu-select-dropdown{
    display: block;
    width: 68px;
    position: absolute;
    left:0;
    top: 108px;
    transform-origin: center top 0px;
}
.prj-search .ivu-select-dropdown .ivu-select-dropdown-list:before{
    left:60px;
}
.prj-search .ivu-select-dropdown .ivu-select-dropdown-list:after{
    left:60px;
}
.search-result{
    padding:10px 0 0 !important;
    margin-top: 10px;
    box-shadow: none !important;
}
.prj-search .ivu-select-dropdown .ivu-select-dropdown-list .ivu-input-type .ivu-input{
    border:none;
    margin:0 20px;
    width: calc(100% - 40px);
     width: -webkit-calc(100% - 40px);
    width: -moz-calc(100% - 40px);
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}
.prj-search .ivu-select-dropdown .ivu-select-dropdown-list .ivu-input-type .ivu-input:focus{
    box-shadow: none;
    outline: none;
    border:none;
}
.prj-search .ivu-select-dropdown .ivu-select-dropdown-list .ivu-select-not-found{
    border-top:1px solid rgba(222, 221, 221, 0.5);
    width:100%;
} */
.choose-prj .ivu-select-dropdown .ivu-select-not-found {
  box-shadow: 0 1px 6px rgba(255, 153, 0, 0.5);
  padding: 10px;
  background: white;
}
.choose-prj .ivu-select-dropdown .ivu-select-not-found:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 30px;
  top: -10px;
  border: 10px solid;
  border-color: transparent transparent rgba(255, 153, 0, 0.2) transparent;
}
.choose-prj .ivu-select-dropdown .ivu-select-not-found:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 30px;
  top: -9px;
  border: 10px solid;
  border-color: transparent transparent #ffffff transparent;
}
.choose-prj .ivu-select-dropdown .ivu-select-not-found li {
  display: block;
  text-align: left;
  width: 100%;
  line-height: 30px;
  font-size: 12px !important;
  padding: 0 8px;
  color: #657180;
  box-sizing: border-box;
}
.block {
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  height: auto;
  background-color: #fff;
  min-height: 200px;
}
.block img {
  width: 100%;
}
.layout-container .ivu-row .ivu-col {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
/*浜哄憳鍒楄〃*/
.list {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  border: 1px solid #eee;
  background-color: #fff;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  box-sizing: border-box;
  webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.list .ivu-table-wrapper {
  border: none;
  height: 100%;
}
.list .ivu-table-wrapper .ivu-table:before,
.list .ivu-table-wrapper .ivu-table:after {
  background-color: unset ;
}
.list .ivu-table td,
.list .ivu-table th {
  background-color: unset;
  border: none;
}
.list .ivu-table .ivu-table-body {
  max-height: calc(100% - 40px);
  max-height: -webkit-calc(60%);
  max-height: -moz-calc(60%);
}
.swich-large {
  width: 70px !important;
}
.swich-large.ivu-switch-checked:after {
  left: 48px !important;
}
.swich-large.ivu-switch-checked {
  border-color: #3883e5;
  background-color: #3883e5;
}
.swich-large .ivu-switch-inner {
  left: 36px;
}
.btn-ghost-orange {
  border-color: #3883e5 !important;
  color: #3883e5 !important;
}
.btn-ghost-orange:hover {
  border-color: #3883e5 !important;
  color: #fff !important;
  background-color: #3883e5 !important;
}
/*鐭ヨ瘑搴�*/
.btn-default.ivu-btn-ghost {
  background-color: #fff;
}
.btn-default.ivu-btn-ghost:hover {
  border-color: #3883e5 !important;
  color: #3883e5;
  background-color: #fff;
}
/*棰滆壊閫夋嫨*/
.color1 {
  color: rgba(56, 131, 229, 0.8);
}
.bg-color1 {
  background-color: rgba(56, 131, 229, 0.8) !important;
  border-color: rgba(56, 131, 229, 0.8) !important;
}
.color2 {
  color: rgba(56, 131, 229, 0.2);
}
.bg-color2 {
  background-color: rgba(56, 131, 229, 0.6) !important;
  border-color: rgba(56, 131, 229, 0.6) !important;
}
.color3 {
  color: #bed742;
}
.bg-color3 {
  background-color: #bed742 !important;
  border-color: #bed742 !important;
}
.color4 {
  color: rgba(56, 131, 229, 0.6);
}
.bg-color4 {
  background-color: rgba(56, 131, 229, 0.6) !important;
  border-color: rgba(56, 131, 229, 0.6) !important;
}
.color5 {
  color: #f8aba6;
}
.bg-color5 {
  background-color: #f8aba6 !important;
  border-color: #f8aba6 !important;
}
.color6 {
  color: #78cdd1;
}
.bg-color6 {
  background-color: #78cdd1 !important;
  border-color: #78cdd1 !important;
}
.color7 {
  color: #7bbfea;
}
.bg-color7 {
  background-color: #7bbfea !important;
  border-color: #7bbfea !important;
}
.color8 {
  color: #6a6da9;
}
.bg-color8 {
  background-color: #6a6da9 !important;
  border-color: #6a6da9 !important;
}
.bg-finish {
  background-color: #cff399;
}
.bg-back {
  background-color: #ffbdbd;
}
.bg-current {
  background-color: #abc9ff;
}
.bg-undo {
  background-color: #d9d9d9;
}
.bg-abnormal {
  background-color: rgba(56, 131, 229, 0.6);
}
.bg-no-right {
  background-color: #cebcff;
}
.bg-file {
  background-color: #33b2ff;
}
.bg-script {
  background-color: rgba(56, 131, 229, 0.6);
}
/**/
.item-tree {
  display: inline-block;
  /*max-width: 30%;*/
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.item-tree .wrapper .nav-item .ivu-breadcrumb {
  margin-left: 10px;
}
.item-tree .wrapper .nav-item .ivu-breadcrumb .ivu-breadcrumb-item-link {
  font-weight: normal !important;
  cursor: pointer;
  font-size: 12px;
  color: #3883e5;
}
.item-tree a {
  display: inline-block;
  /*max-width: 100px;*/
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  line-height: 20px;
  height: 20px;
}
.card-tit {
  overflow: hidden;
}
.card-tit .ivu-card-head p {
  height: auto;
}
.card-tit .ivu-card-body {
  max-height: calc(100% - 70px);
  overflow-y: auto;
  height: calc(100% - 70px);
}
.btn-txt-outline:hover,
.btn-txt-outline.ivu-btn-text:hover,
.btn-txt-outline.ivu-btn-text.active,
.btn-txt-outline.ivu-btn-text:active {
  border-color: #3883e5;
  color: #3883e5;
  background-color: #fff;
}
.btn-txt-outline.ivu-btn-text.active,
.btn-txt-outline.ivu-btn-text:active {
  border-color: #3883e5;
  color: #fff;
  background-color: #3883e5;
}
.card-tit .ivu-upload-list {
  margin-top: 0;
}
.view {
  float: right;
  display: inline-block;
  background-color: #fff;
  border: 1px solid #dddee1;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  height: 33px;
  line-height: 33px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
}
.view .ivu-icon,
.view .iconfont {
  vertical-align: top;
  text-align: center;
  width: 31px;
  height: 31px;
  line-height: 31px;
  /*border-radius: 5px;*/
  /*-webkit-border-radius: 5px;*/
  /*-moz-border-radius: 5px;*/
}
.view .ivu-tooltip {
  float: left;
}
.file-list {
  display: block;
}
.file-list li {
  line-height: 40px;
  vertical-align: middle;
  display: block;
}
.file-list li:nth-child(2n) {
  background-color: #f9f9f9;
}
.file-list li:hover {
  background-color: #ebf7ff;
}
.file-list li img {
  padding: 8px;
  height: 40px;
  vertical-align: middle;
  display: inline-block;
  width: 40px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.file-name {
  display: inline-block;
  width: 47%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  max-width: 47%;
}
.file-node {
  display: inline-block;
  width: 22%;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  color: #acacac;
}
.km-file-name {
  display: inline-block;
  width: 43%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  max-width: 43%;
}
.file-creater {
  display: inline-block;
  width: 10%;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  color: #acacac;
}
.file-list li .file-creater img {
  margin-right: 5px;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: inline-block;
  padding: 0;
  vertical-align: middle;
}
.file-time {
  display: inline-block;
  width: 13%;
  font-size: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  vertical-align: middle;
  color: #acacac;
  min-width: 114px;
}
.file-perform {
  display: inline-block;
  float: right;
  vertical-align: middle;
}
.file-perform a {
  display: inline-block;
  vertical-align: middle;
  font-size: 12px;
}
.file-perform .iconfont,
.file-perform .ivu-icon {
  cursor: pointer;
}
.file-card {
  display: block;
}
.file-card img {
  width: 90%;
  margin: 0 5%;
  display: block;
}
.file-card p {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  text-align: center;
}
.collect-btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  line-height: 1.5;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  padding: 6px 15px;
  font-size: 12px;
  border-radius: 4px;
  transform: translate3d(0, 0, 0);
  transition: color 0.2s linear, background-color 0.2s linear, border 0.2s linear;
  color: #657180;
  background-color: #fff;
  border-color: #d7dde4;
  outline: none;
}
.item-choose {
  display: block;
  position: absolute;
  z-index: 100;
  /*max-height: 500px;*/
  /*overflow-y: auto;*/
  /*overflow-x: hidden;*/
  top: 30px;
  left: 0px;
  right: 0px;
}
.item-choose ul:before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 30px;
  top: -10px;
  border: 9px solid;
  border-color: transparent transparent rgba(56, 131, 229, 0.2) transparent;
}
.item-choose ul:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  left: 30px;
  top: -9px;
  border: 9px solid;
  border-color: transparent transparent #ffffff transparent;
}
.item-choose ul {
  padding: 8px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: block;
  background: #fff;
  box-shadow: 0 1px 6px rgba(56, 131, 229, 0.5);
  /* border-radius:8px;*/
  float: left;
  margin: 8px 0;
  width: 100%;
  max-height: 400px;
  overflow-x: hidden;
  overflow-y: auto;
}
.item-choose ul li {
  padding: 8px 5px;
  width: 20%;
  max-width: 20%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline-block;
  font-size: 14px;
  float: left;
  position: relative;
}
.item-choose ul li:hover {
  background: #f8f8f8;
}
.plus {
  width: 14px;
  height: 10px;
  cursor: pointer;
  position: absolute;
  top: 5px;
  left: 10px;
  display: inline-block;
}
.item-name {
  float: left;
  position: relative;
  left: 12px;
  width: calc(100% - 24px);
  width: -webkit-calc(76%);
  width: -moz-calc(76%);
  max-width: calc(100% - 24px);
  max-width: -webkit-calc(76%);
  max-width: -moz-calc(76%);
  text-align: left;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: default;
  font-size: 12px;
  color: #495060;
}
/*.item-choose ul li .item-name:hover{
    background: #f3f3f3;
}*/
.item-name:hover {
  color: #495060 !important;
}
.plus:hover {
  color: #3883e5 !important;
}
.flow-window .ivu-input,
.flow-window .ivu-input:hover,
.flow-window .ivu-input:focus {
  border: none;
  outline: none !important;
  box-shadow: none !important;
  background: none;
  background-color: unset;
}
.window-height {
  /*max-height: 80%;*/
}
.window-height.vertical-center-modal .ivu-modal {
  /*height:80%;*/
}
.window-height .ivu-modal-content {
  /*height:100%;
    min-height: 188px;*/
}
.window-height .ivu-modal-body {
  /*max-height:calc(100% - 146px);*/
  /* max-height: 350px;*/
  /*overflow-y: auto;*/
}
/*鏍�*/
.tree-box {
  display: inline-block;
  border-right: 1px solid #dedddd;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: auto;
  z-index: 1;
}
.menu-lv3 {
  text-align: left;
  cursor: pointer;
}
.menu-item-lv3 {
  display: block;
  float: left;
}
.menu-item-lv3-name {
  display: inline-block;
  width: 80%;
  float: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
  font-size: 12px;
  vertical-align: middle;
}
.modify-visible {
  float: right;
  display: inline-block;
  position: absolute;
  right: 4px;
  background-color: #f9efe3;
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  vertical-align: middle;
  top: 4px;
  padding: 3px 5px 3px 0;
  cursor: default;
}
.modify-visible .iconfont {
  width: 20px;
  color: #80848f;
  line-height: 20px;
  font-size: 14px;
  text-align: center;
  margin-left: 5px;
  cursor: pointer;
}
.modify-visible .iconfont:hover {
  color: #495060;
}
.noborder .ivu-select-selection,
.noborder .ivu-input {
  border: 1px solid transparent;
}
.noborder .ivu-input:hover {
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
.noborder .ivu-select-visible .ivu-select-selection,
.noborder .ivu-input:focus {
  background-color: #f8f8f8;
  outline: 0;
  border: none;
  box-shadow: none !important;
}
.noborder .ivu-select-disabled .ivu-select-selection:hover,
.noborder .ivu-select-disabled.ivu-select:focus .ivu-select-selection {
  border: 1px solid transparent !important;
  box-shadow: none !important;
}
.noborder .ivu-input.ivu-input-disabled:hover {
  border: 1px solid transparent !important;
  box-shadow: none !important;
}
.width-auto {
  display: inline-block;
  width: auto !important;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.table-height,
.table-height .ivu-table td,
.table-height .ivu-table th {
  border: none !important;
}
.table-height .ivu-table:before,
.table-height .ivu-table:after {
  background-color: unset !important;
}
.table-height th .ivu-table-cell,
.table-height .ivu-table-cell {
  line-height: 20px;
}
.table-height {
  height: 100%;
}
.table-height .ivu-table-body {
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100% - 40px);
}
.circle {
  display: block;
  border-radius: 20px;
  width: 20px;
  height: 20px;
  verticle-align: middle;
  cursor: pointer;
}
.circle:hover,
.circle:active,
.circle-point {
  box-shadow: 0 1px 5px 2px rgba(0, 0, 0, 0.1);
}
.mini-listbox {
  background: #fff !important;
}
/*缁勭粐鏋舵瀯*/
/*.org-tree-out{*/
/*position: absolute;*/
/*left: 10px;*/
/*top: 10px;*/
/*right: 10px;*/
/*bottom: 20px;*/
/*}*/
/*.org-tree-innner{*/
/*display: -webkit-box;*/
/*display: -ms-flexbox;*/
/*display: flex;*/
/*-webkit-box-align: center;*/
/*-ms-flex-align: center;*/
/*align-items: center;*/
/*-webkit-box-pack: center;*/
/*-ms-flex-pack: center;*/
/*justify-content: center;*/
/*height: 100%;*/
/*}*/
.org-tree {
  display: block;
  /*overflow: auto;*/
  /*padding:10px;*/
  margin: 0 auto;
}
.org-tree li {
  display: block;
  line-height: 40px;
  height: 40px;
  text-align: center;
  white-space: nowrap;
  font-size: 0;
}
.topic-center {
  text-align: center;
  max-width: 140px;
  min-width: 56px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
  border-radius: 5px;
  background-color: #fff;
  border: 2px solid #3883e5;
  padding: 4px 5px;
  display: inline-block;
  line-height: 28px;
  z-index: 1;
  cursor: pointer;
  outline: none;
}
.topic-center:hover {
  box-shadow: 0 1px 5px 2px rgba(0, 0, 0, 0.1);
}
.topic {
  border: 1px solid #2d8cf0;
  font-size: 14px;
}
.topic-children {
  border: 1px solid #dddee1;
  font-size: 12px;
}
.topic-space {
  border: none;
  cursor: default;
}
.topic-space:hover {
  box-shadow: none;
}
.line-hr {
  min-width: 56px;
  max-width: 140px;
  height: 2px;
  background: #dddee1;
  display: inline-block;
  margin: 19px 0;
}
.line-lr {
  width: 2px;
  min-height: 21px;
  background: #dddee1;
  display: inline-block;
  vertical-align: initial;
  position: relative;
}
.line-lr .iconfont {
  position: absolute;
  left: -9px;
  width: 18px;
  height: 18px;
  border: 1px solid #dddee1;
  color: #80848f;
  border-radius: 15px;
  top: 9px;
  background: #fff;
  vertical-align: middle;
  line-height: 18px;
  font-size: 12px;
  cursor: pointer;
}
.line-lr .iconfont:hover {
  color: #3883e5;
}
.org-tree-list {
  display: block;
}
.org-tree-list li {
  display: block;
  line-height: 20px;
  height: 20px;
  padding: 5px;
  text-align: left;
  font-size: 12px;
  cursor: default;
}
.org-tree-list li i {
  text-align: right;
  font-style: normal;
  float: right;
}
.org-tree-list li:hover {
  background: #f8f8f8;
  color: #3883e5;
}
.org-tree .ivu-poptip-body {
  padding: 5px;
  background: #f8f8f8;
}
.org-tree .ivu-poptip-popper[x-placement^=right] .ivu-poptip-arrow:after {
  border-right-color: #f8f8f8;
}
/*璇勮*/
.comment-box {
  display: block;
  /*position: fixed;*/
  /*right: 0;*/
  width: 100%;
  box-shadow: 0 1px 5px 2px rgba(0, 0, 0, 0.1);
  /*top: 105px;*/
  background: #fff;
  z-index: 10;
  /*padding:10px;*/
  min-height: 200px;
  /*bottom:8px;*/
  height: 100%;
  border-top-right-radius: 4px;
  -moz-border-top-right-radius: 4px;
  -webkit-border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  -moz-border-bottom-right-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
}
.comment-tit {
  display: block;
  text-align: left;
}
.comment-tit h3 {
  display: inline-block;
  float: left;
  font-weight: normal;
  font-size: 12px;
}
.comment-tit h3 span,
.comment-tit h3 span i {
  font-size: 12px;
  color: #ccc;
  font-style: normal;
}
.comment-tit .ivu-icon-ios-close-empty {
  font-size: 31px;
  color: #999;
  transition: color 0.2s ease;
  float: right;
  line-height: 21px;
  cursor: pointer;
}
.comment-tit .ivu-icon-ios-close-empty:hover {
  color: #495060;
}
.comment-file {
  display: block;
  text-align: left;
}
.comment-file img {
  height: 24px;
  vertical-align: middle;
  display: inline-block;
  width: 24px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.comment-file span {
  line-height: 24px;
  display: inline-block;
  max-width: calc(100% - 50px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
  margin-left: 5px;
}
.comment-textarea,
.comment-textarea .ivu-input {
  border: none;
  background-color: #f9f9f9;
}
.comment-textarea .ivu-input:focus {
  background-color: #fff;
  outline: 0;
  border: none;
  box-shadow: none !important;
}
.cut-line {
  width: 100%;
  height: 1px;
  background: #e9eaec;
}
.comment-area {
  display: block;
  max-height: calc(100% - 223px);
  max-height: -webkit-calc(-123%);
  max-height: -moz-calc(-123%);
  overflow-y: auto;
  width: 100%;
  padding: 8px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.comment-area ul {
  display: block;
}
.comment-area ul li {
  display: block;
  margin: 10px 0 20px 0;
  position: relative;
}
.commenter {
  position: absolute;
  width: 36px;
  height: 36px;
  border-radius: 30px;
  display: inline-block;
  text-align: center;
  line-height: 36px;
  color: #fff;
}
.commenter-content {
  /*margin-left: 41px;*/
  /*width:calc(100% - 41px);*/
  /*width:-webkit-calc(100% - 41px);*/
  /*width:-moz-calc(100% - 41px);*/
  width: 100%;
  font-size: 12px;
  text-align: left;
}
.commenter-detail {
  color: #ccc;
  line-height: 20px;
}
.commenter-content p {
  line-height: 18px;
}
.comment-member-list {
  position: relative;
}
.member-at {
  position: absolute;
  z-index: 1;
  left: 10px;
  top: 6px;
}
.comment-member-list .ivu-select-single .ivu-select-input {
  width: calc(100% - 15px);
  width: -webkit-calc(85%);
  width: -moz-calc(85%);
  left: 5px;
}
.display-file {
  display: block;
}
.display-file li {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  padding: 8px;
}
.display-file li:nth-child(2n+1) {
  background-color: #f8f8f9;
}
.display-file li:hover {
  background-color: #ebf7ff;
}
.display-file-name {
  display: inline-block;
  width: 47%;
  max-width: 47%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
  float: left;
}
.display-file-creater {
  display: inline-block;
  width: 15%;
  max-width: 15%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
  float: left;
  color: #acacac;
}
.display-file-time {
  display: inline-block;
  width: 25%;
  max-width: 25%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 10px;
  float: left;
  color: #acacac;
}
.display-file-download {
  display: inline-block;
  width: 5%;
  text-align: right;
  float: right;
}
.refresh {
  padding: 0 !important;
}
.refresh:hover {
  color: #5cadff !important;
}
.select-bg {
  background-color: #f1f1f1 !important;
}
.file-collection-list .ivu-select-dropdown {
  max-width: 16%;
  width: 16%;
}
.file-collection-list .ivu-select-dropdown .ivu-dropdown-item {
  width: 100%;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.file-collection-list .ivu-select-dropdown .ivu-dropdown-item span {
  width: 90%;
  max-width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 40px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline-block;
  line-height: 16px;
  vertical-align: middle;
}
.file-collection-list .ivu-select-dropdown .ivu-dropdown-item a {
  position: absolute;
  right: 16px;
  color: #ccc;
  font-size: 10px;
  text-align: right;
}
.file-collection-list .ivu-select-dropdown .ivu-dropdown-item .ivu-badge .ivu-badge-count {
  height: 16px;
  line-height: 15px;
  text-align: center;
  padding: 0 3px;
  font-size: 10px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  min-width: 16px;
}
.date-select {
  display: inline-block;
  margin-left: 20px;
}
.date-select .ivu-input,
.date-select .ivu-input:focus {
  background-color: unset;
  border: none;
  outline: none;
}
.file-list .more-list .ivu-dropdown-item {
  line-height: 1;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
/*妯℃澘閫夋嫨*/
.blank-card {
  padding: 8px;
  background: rgba(248, 248, 248, 0.5) !important;
  margin-bottom: 10px;
}
.blank-card .ivu-card-head {
  padding: 0 5px 5px 5px;
  width: 100%;
  max-width: 100%;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.blank-card .ivu-card-body {
  padding: 5px;
}
.blank-card .ivu-card-body p {
  font-size: 12px;
  line-height: 18px;
}
.blank-card .ivu-form-item {
  margin-bottom: 0 !important;
}
.border-left {
  border-bottom-left-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -moz-border-bottom-left-radius: 5px;
  border-top-left-radius: 5px;
  -webkit-border-top-left-radius: 5px;
  -moz-border-top-left-radius: 5px;
}
.border-right {
  border-top-right-radius: 0;
  -webkit-border-top-right-radius: 0;
  -moz-border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  -webkit-border-bottom-right-radius: 0;
  -moz-border-bottom-right-radius: 0;
  border-right: 1px solid #e8eaec;
}
.border-right-solid {
  border-right: 1px solid #e9eaec !important;
}
.no-radius {
  border-radius: 0 !important;
}
/*鐭ヨ瘑搴�*/
.search-input {
  display: inline-block;
  float: left;
  margin-left: 30px;
}
.search-input .ivu-input-icon {
  top: 14px;
  right: 5px;
  cursor: pointer;
  color: #3883e5;
}
.search-input .ivu-input {
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  background-color: #fff;
  border: none;
}
.knowledge-name {
  width: 100%;
  font-size: 16px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
}
.card-head .ivu-card-head {
  padding-top: 8px;
  padding-bottom: 8px;
}
.book-list-box {
  display: block;
  padding: 8px;
  background-color: #fff;
  position: absolute;
  z-index: 1;
  top: 65px;
  border-radius: 4px;
  right: 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  transform-origin: center top 0px;
}
.book-list-box ul {
  display: block;
  font-size: 0;
  line-height: 0;
}
.book-list-box ul li {
  line-height: 16px;
  padding: 5px 15px;
  display: inline-block;
  font-size: 12px;
  text-align: left;
  max-width: 100px;
  min-width: 50px;
  text-overflow: ellipsis;
  white-space: normal;
  overflow: hidden;
  cursor: pointer;
}
.book-list-box ul li:hover {
  background-color: #f8f8f8;
}
.mini-grid-cell-inner-pdright .mini-grid-cell-inner {
  padding-right: 40px;
  position: relative;
  box-sizing: border-box;
}
.mini-grid-cell-inner-pdright .ivu-btn-success {
  position: absolute;
  right: 0px;
  top: 1px;
}
.bg-light-orange {
  background-color: rgba(56, 131, 229, 0.2) !important;
}
/*姘旀场鎻愮ず绐�*/
.ivu-poptip-body-content-li {
  display: block;
  font-size: 0;
  white-space: normal;
  width: auto;
}
.ivu-poptip-body-content-li li {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  display: inline-block;
  font-size: 12px;
  padding: 7px;
  max-width: 74px;
  width: 74px;
}
.ivu-poptip-body-content-li li:hover {
  background-color: #f8f8f8;
}
.empty-img {
  width: 30% !important;
  margin: 0 auto;
  display: block;
}
.card-head-noborder .ivu-card-head {
  border: none;
}
.li-style li {
  padding: 8px;
}
.li-style li:hover {
  background-color: #ebf7ff;
}
/*娴佺▼鍥�*/
.process-perform {
  position: absolute;
  width: 222px;
  height: 70px;
  background-color: #f8f8f800;
  z-index: 1;
}
.process-perform-box {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  display: none;
}
.process-perform-box .iconfont {
  display: inline-block;
  width: 24px;
  height: 24px;
  background-color: #f5f5f5;
  border-radius: 50%;
  margin-top: 23px;
  cursor: pointer;
}
.process-perform:hover,
.process-perform:focus {
  background-color: #fcfcfc;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}
.process-perform:hover .process-perform-box {
  display: block;
}
.process-perform-box .icon-jia:hover,
.process-perform-box .icon-shuru:hover,
.process-perform-box .icon-open:hover,
.process-perform-box .icon-submit:hover {
  color: #3883e5;
}
.process-perform-box .icon-wechaticon13:hover {
  color: #ed3f14;
}
.unstart {
  background-color: #1d953f;
  display: inline-block;
  width: 222px;
  height: 70px;
  padding: 5px;
  color: #fff;
  position: absolute;
}
.unstart h1,
.finish h1,
.delay h1 {
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 18px;
  font-weight: normal;
  color: #fff;
  max-height: 35px;
  overflow: hidden;
  line-height: 24px;
  height: 24px;
}
.unstart p,
.finish p,
.delay p {
  text-overflow: ellipsis;
  font-size: 12px;
  color: #fff;
  overflow: hidden;
  white-space: nowrap;
  line-height: 18px;
  height: 18px;
  display: block;
}
.finish {
  background-color: #999;
  display: inline-block;
  width: 222px;
  height: 70px;
  padding: 5px;
  color: #fff;
  position: absolute;
}
.unstart span,
.finish span {
  font-size: 12px;
  color: #fff;
  display: inline-block;
}
.delay {
  background-color: #3883e5;
  display: inline-block;
  width: 222px;
  height: 70px;
  padding: 5px;
  color: #fff;
  position: absolute;
}
.layout-logo i {
  display: inline-block;
  font-size: 30px;
  color: #fff;
  vertical-align: middle;
  float: left;
  line-height: 36px;
}
.layout-logo i:before {
  color: #fff;
}
/*宸﹀彸甯冨眬*/
.search-input {
  display: inline-block;
  float: left;
  margin-left: 30px;
}
.search-input {
  display: inline-block;
  float: left;
  margin: 14px 0 14px 30px;
}
.search-input .ivu-input-icon {
  top: 0;
  right: 5px;
  cursor: pointer;
  color: #3883e5;
}
.search-input .ivu-input {
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  background-color: unset;
  border-color: rgba(56, 131, 229, 0.6);
  color: #3883e5;
  background-color: #fff;
}
.layout-container .ivu-menu {
  z-index: 0;
}
.blank-name {
  display: block;
  height: 50px;
  border-bottom: 1px solid #e3e8ee;
  padding: 0 16px;
}
.blank-name h1 {
  display: block;
  line-height: 50px;
  font-size: 18px;
  font-weight: normal;
}
.blank-name h1 i {
  cursor: pointer;
  font-weight: 600;
}
.blank-name h1 .icon-renew:hover,
.blank-name h1 .icon-save0:hover,
.blank-name h1 .icon-save-as0:hover {
  color: #3883e5;
}
.blank-name h1 .icon-share0:hover {
  color: #0c6;
}
.blank-name h1 .icon-delete0:hover {
  color: #ff3333;
}
.blank-name h1 .biz-name {
  display: inline-block;
  max-width: calc(100% - 250px);
  height: 50px;
}
.blank-name h1 .biz-name .biz-name-read {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-right: 0;
  max-width: calc(100% - 29px);
}
.blank-name h1 .biz-name-edit {
  margin-right: 0;
}
.blank-name h1 .biz-name:hover .icon-bianji,
.blank-name h1 .biz-name .ivu-icon,
.blank-name h1 .biz-name:hover .icon-bianji,
.blank-name h1 .biz-name .ivu-icon {
  margin-top: 16px;
}
.blank-name-tab {
  display: flex;
  align-items: center;
}
.blank-name-tab .tab_blank {
  margin-left: auto;
  display: flex;
}
.blank-name-tab .tab_blank span:first-child {
  border-radius: 5px 0 0 5px;
}
.blank-name-tab .tab_blank span:last-child {
  border-radius: 0 5px  5px 0;
}
.blank-name-tab .tab_blank span:not(:first-child) {
  margin-left: -1px;
}
.blank-name-tab .tab_blank span {
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  padding: 0 12px;
  border: 1px solid #dddee1;
}
.blank-name-tab .tab_blank span.active {
  color: #3883e5;
  border-color: #3883e5;
  /*color: #fff;*/
  z-index: 2;
  position: relative;
}
.menu-left {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline;
  width: 250px;
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}
.menu-left .ivu-menu-vertical .ivu-menu-item,
.menu-left .ivu-menu-vertical .ivu-menu-submenu-title {
  padding: 14px 16px;
}
.menu-left .ivu-menu .ivu-menu-item:hover {
  background: #fffcf0;
}
.menu-left .ivu-menu {
  min-height: 100%;
  max-height: 100%;
  overflow: auto;
}
.right-content {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline;
  position: absolute;
  left: 251px;
  top: 0;
  height: 100%;
  right: 0;
}
.right-content .layout-content-main {
  box-sizing: border-box;
  height: 100%;
  min-height: 100%;
  overflow: auto;
}
.right-content .layout-content-main .ivu-table td,
.right-content .layout-content-main .ivu-table th,
.ivu-modal .ivu-table td,
.ivu-modal .ivu-table th,
.table-noborder .ivu-table td,
.table-noborder .ivu-table th {
  border: none;
}
/*鍙彲瑙�*/
.visible {
  display: block;
  font-size: 14px;
  margin-left: 16px;
}
.visible-person {
  display: block;
  text-align: center;
  margin-left: 16px;
  width: 48px;
}
.visible-person img {
  border-radius: 5px;
  width: 48px;
  height: 48px;
  display: block;
}
.visible-person p {
  display: block;
  line-height: 22px;
  font-size: 12px;
  margin-top: 5px;
}
.attention {
  text-align: left;
  line-height: 22px;
  font-size: 14px;
}
.attention p {
  display: inline-block;
  width: 472px;
  float: right;
}
.tips {
  text-align: left;
  line-height: 22px;
  font-size: 12px;
  color: #999;
}
/*鏇存崲澶村儚*/
.cut-img-box {
  width: 640px;
  height: 360px;
  overflow: hidden;
  text-align: center;
  vertical-align: middle;
  position: relative;
  margin: 0 auto;
  display: inline-block;
  background-color: #f8f8f8;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.cut-img-box img {
  display: block;
}
.preview-box {
  display: inline-block;
  width: 72px;
  text-align: center;
  margin-left: 33px;
}
.preview {
  width: 72px;
  height: 72px;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 10px;
}
.preview img {
  width: 72px;
  height: 72px;
  display: block;
  margin: 0 auto;
}
canvas {
  display: inline-block;
}
/*鑺傜偣瀹氬埗*/
.custome-box {
  display: block;
  float: left;
  width: 100%;
  box-sizing: border-box;
}
.custome-box ul {
  display: block;
}
.custome-box ul li {
  text-align: center;
  display: inline-block;
  position: relative;
  float: left;
}
.custome-box ul li .custome-content:hover,
.ivu-modal-body .custome-box ul li .custome-content:hover {
  cursor: pointer;
}
.custome-box h2 {
  display: block;
  font-size: 14px;
  font-weight: normal;
  padding: 8px 10px;
  position: relative;
}
.custome-box h2:before {
  display: inline-block;
  content: '';
  width: 4px;
  background: #3883e5;
  position: absolute;
  height: 20px;
  left: 0;
}
.ivu-modal-body .custome-box ul li:hover .custome-content div {
  background: #fffcf0;
  border: 1px solid #3883e5;
}
.ivu-modal-body .custome-box ul li:hover .custome-content div .custome-icon {
  border: none;
}
.mask {
  position: absolute;
  left: 5px;
  top: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  cursor: default;
  background-color: #f5f5f5;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  border: 1px solid #dedddd;
  box-shadow: 0 1px 7px 3px #dddee1;
  display: none;
}
.mask-perform {
  position: absolute;
  top: 10px;
  right: 0;
  width: 100%;
  display: block;
}
.mask .icon-shanchu1,
.mask .icon-bianji,
.mask .icon-shanchu1 {
  width: 26px;
  height: 26px;
  background-color: #fff;
  color: #80848f;
  border-radius: 15px;
  -webkit-border-radius: 15px;
  -moz-border-radius: 15px;
  line-height: 26px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
  display: none;
}
.mask .icon-bianji:hover {
  color: #2d8cf0;
}
.mask .icon-shanchu1:hover {
  color: #ff5c33;
}
.custome-box ul li:hover .mask {
  display: block;
}
/*.custome-box ul li:hover .mask .mask-perform .iconfont{*/
/*display: inline-block;*/
/*}*/
.custome-box ul li:hover .mask.has-right .iconfont {
  display: inline-block;
}
.custome-box ul li:hover .mask.has-right .mask-perform p {
  display: none;
}
.custome-box ul li:hover .mask.no-right .perform {
  display: block;
}
.custome-content {
  display: block;
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -ms-border-radius: 5px;
  cursor: default;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -o-box-sizing: border-box;
  margin: 5px;
}
.custome-content div {
  height: 48px;
  width: 100%;
  display: inline-block;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  vertical-align: middle;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.custome-content div input {
  max-height: 38px;
  height: 38px;
  line-height: 19px;
  width: 66px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "寰蒋闆呴粦", Arial, sans-serif;
  text-overflow: ellipsis;
  white-space: normal;
  display: inline-block;
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  margin: 5px 5px 5px 0;
  background: none;
  overflow: hidden;
  padding: 0;
  text-align: center;
  cursor: inherit;
}
.custome-content .add-node {
  max-height: 48px;
  height: 48px;
  line-height: 19px;
  width: 66px;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "寰蒋闆呴粦", Arial, sans-serif;
  text-overflow: ellipsis;
  white-space: normal;
  display: inline-block;
  margin: 0;
  background: none;
  overflow: hidden;
  padding: 0;
  text-align: center;
  cursor: inherit;
  border: 1px dashed #d7dde4;
  border-radius: 5px;
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  outline: 0;
  font-size: 20px;
}
.custome-content .add-node:hover {
  border: 1px solid #3883e5;
  color: #3883e5;
}
.custome-content .add-node:focus {
  border: 1px solid #3883e5;
  font-size: 14px;
}
.mask-perform p {
  font-size: 12px;
  text-align: center;
  margin-top: 5px;
}
.custome-content div .custome-icon {
  width: 46px;
  display: inline-block;
  height: 46px;
  line-height: 46px;
  float: left;
  vertical-align: middle;
  text-align: center;
  padding: 6px;
  margin-right: 5px;
  box-sizing: border-box;
}
.custome-content div .custome-icon i {
  font-size: 30px;
  vertical-align: middle;
  line-height: 36px;
  display: block;
  height: 36px;
}
.custome-content div .custome-text {
  border: none;
  max-height: 38px;
  height: 38px;
  line-height: 19px;
  margin: 5px 5px 5px 0;
  width: 66px;
  background: none;
  color: #495060;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "寰蒋闆呴粦", Arial, sans-serif;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  display: inline-block;
  padding: 0;
  float: left;
  text-align: left;
  outline: 0;
}
.custome-content div .custome-text:focus {
  outline: 0;
}
.disc-text {
  font-size: 12px;
  position: relative;
  color: #657180;
}
.flow-card {
  cursor: pointer;
}
.flow-card .ivu-card-head {
  padding: 8px;
}
.flow-card .ivu-card-head p,
.flow-card .ivu-card-head-inner {
  line-height: 32px;
  height: 32px;
}
/*宸�-涓�-鍙冲竷灞�*/
.center-box {
  float: left;
  width: 250px;
  display: inline-block;
  height: 100%;
}
.right-box {
  float: right;
  display: inline-block;
  width: calc(100% - 250px);
  padding: 0 16px;
}
.member-list {
  display: block;
}
.member-list li {
  display: inline-block;
  float: left;
  width: 48px;
  text-align: center;
  margin: 0 16px 16px 0;
  position: relative;
}
.member-list li img {
  display: block;
  width: 48px;
  margin: 0 auto;
  border-radius: 4px;
}
.member-list li .ivu-icon {
  position: absolute;
  right: -7px;
  top: -7px;
  cursor: pointer;
  background: #fff;
  border-radius: 50%;
  text-align: center;
  width: 14px;
  height: 14px;
  display: none;
}
.member-list li:hover .ivu-icon-ios-close,
.member-list .member-list-check .ivu-icon-ios-checkmark,
.member-list li:hover .ivu-icon-ios-checkmark {
  display: block;
}
.member-list li p {
  display: block;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 100%;
  font-size: 12px;
}
.member-list li .ivu-poptip .ivu-poptip-popper {
  width: 200px !important;
  top: 53px;
}
.member-list li .ivu-poptip .ivu-poptip-popper .ivu-poptip-body {
  padding: 8px;
}
.member-list-box {
  display: block;
  max-height: 184px;
  height: 184px;
  overflow: auto;
  margin: 8px 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}
.member-list-box li {
  display: block;
  width: 100%;
  position: relative;
  padding: 0 5px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  line-height: 40px;
  height: 40px;
  margin: 0 0 8px 0;
}
.member-list-box li:hover {
  background: #f8f8f8;
  cursor: pointer;
}
.member-list-box li div {
  display: block;
  padding-right: 20px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
.member-list-box li img {
  width: 32px;
  height: 32px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 5px;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
}
.member-list-box li .ivu-icon {
  display: none;
  position: absolute;
  right: 8px;
  top: 15px;
}
.member-list-box .member-list-box-check .ivu-icon {
  display: block;
  background: none;
}
.modal-height {
  max-height: 400px;
  overflow: auto;
}
.role-list {
  display: block;
}
.role-list li {
  padding: 0 10px;
  border-radius: 4px;
  margin: 0 0 10px 0;
}
.role-list li:nth-child(2n+1) {
  background: #f8f8f8;
}
.role-list li:hover {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
}
/*--涓氬姟瑙嗗浘--*/
.biz-tit {
  display: block;
}
.biz-tit span {
  display: inline-block;
  line-height: 24px;
  height: 24px;
  vertical-align: middle;
  margin-right: 5px;
}
.biz-name {
  display: block;
}
.biz-name-read {
  max-width: 100%;
  display: inline-block;
}
.biz-name-read.ivu-input-wrapper {
  max-width: calc(100% - 55px);
}
.biz-name-edit {
  max-width: calc(100% - 28px);
  margin: 0;
  display: inline-block;
  padding: 5px 0;
  font-size: 14px;
  line-height: 1.5;
}
.biz-name .icon-bianji {
  display: none;
}
.biz-name:hover .icon-bianji,
.biz-name .ivu-icon {
  display: inline-block;
  width: 24px;
  /*height:24px;*/
  cursor: pointer;
  vertical-align: top;
  margin-top: 7px;
  text-align: right;
}
.module {
  display: block;
  margin-top: 10px;
}
.module-name {
  display: block;
  line-height: 32px;
  height: 32px;
  color: #333;
}
.module-name .ivu-form-item-label {
  color: #333 !important;
}
.module-name .ivu-form-item-content {
  color: #666 !important;
}
.module-name div {
  display: inline-block;
  background: #fff;
  width: 100px;
}
.module-name div .ivu-icon {
  width: 16px;
  display: inline-block;
  text-align: center;
}
.module-name hr {
  vertical-align: middle;
  margin-top: -16px;
  border-color: #e3e8ee;
  color: #e3e8ee;
  border-width: 0;
  height: 1px;
  background: #e3e8ee;
}
.perform-node-box {
  display: block;
}
.perform-node-box li {
  display: inline-block;
  font-size: 14px;
  margin: 4px 8px 4px 0;
  position: relative;
  cursor: pointer;
}
.perform-node:hover {
  color: #3883e5;
}
.perform-node-select .perform-node {
  color: #3883e5;
}
.perform-node-select .pop-detail {
  display: block;
}
.pop-detail {
  position: absolute;
  transform-origin: center top 0px;
  top: 18px;
  padding: 8px 0 5px;
  display: none;
}
.pop-detail-content {
  box-sizing: border-box;
}
.pop-detail-content .pop-arrow {
  left: 16px;
  top: 3px;
  border-width: 0 5px 5px;
  /*border-bottom-color: rgba(255, 153, 0, 0.1);*/
}
.pop-arrow,
.pop-arrow:after {
  display: block;
  width: 0;
  height: 0;
  position: absolute;
  border-color: transparent;
  border-style: solid;
}
.pop-detail-content .pop-arrow:after {
  content: " ";
  top: 2px;
  margin-left: -3px;
  border-top-width: 0;
  border-bottom-color: rgba(56, 131, 229, 0.1);
}
.pop-detail-inner {
  width: 100%;
  background: rgba(56, 131, 229, 0.1);
  background-clip: padding-box;
  white-space: nowrap;
  border-radius: 4px;
  padding: 8px;
}
.pop-detail-inner li {
  display: inline-block;
  padding: 0 10px 0 7px;
  font-size: 12px;
  border-right: 1px solid #3883e5;
  margin: 0;
  line-height: 12px;
  cursor: pointer;
}
.pop-detail-inner li:last-child {
  border: none;
}
.pop-detail-inner li:hover {
  color: #3883e5;
}
.module .ivu-form-item {
  margin-bottom: 0  !important;
}
.module .ivu-form-item-content p {
  max-width: calc(100% - 28px);
  padding: 4px 7px;
  line-height: 22px;
  word-break: break-all;
  /* display: inline-block; */
  width: auto;
}
.module .ivu-form-item-content .ql-container p {
  padding: 0px;
  line-height: 18px;
}
.module .ivu-form-item-content .ql-editor p {
  padding: 0px;
  line-height: 18px;
}
.module .ivu-form-item-content .ivu-input-wrapper {
  max-width: calc(100% - 49px);
  display: inline-block;
  margin-right: 5px;
  width: auto;
}
.module .ivu-form-item-content .iconfont {
  width: 16px;
  height: 24px;
  display: none;
  cursor: pointer;
  margin-top: 8px;
  vertical-align: top;
}
.module .ivu-form-item-content:hover .iconfont {
  display: inline-block;
}
.module .ivu-form-item-content .ivu-icon-checkmark-round,
.module .ivu-form-item-content .ivu-icon-close-round {
  width: 24px;
  height: 24px;
  display: inline-block;
  cursor: pointer;
  margin-top: 8px;
  vertical-align: top;
  text-align: right;
}
.module .ivu-form-item-content .ivu-date-picker {
  margin-right: 5px;
}
.module .ivu-form-item-content .ivu-date-picker .ivu-date-picker-rel .ivu-input-wrapper {
  width: 100%;
  max-width: 100%;
}
.tab {
  display: block;
}
.tab .ivu-tabs-bar {
  border-bottom: 1px solid #dddee1 !important;
}
.tab .ivu-tabs-nav .ivu-tabs-tab {
  color: #999;
  font-size: 16px;
}
.tab .ivu-tabs-nav .ivu-tabs-tab-active,
.tab .ivu-tabs-nav .ivu-tabs-tab:hover {
  color: #3883e5;
}
.tab .ivu-tabs-ink-bar {
  background-color: #3883e5;
}
.legend {
  display: block;
}
.legend li {
  margin-right: 10px;
  display: inline-block;
  font-size: 10px;
  color: #9ea7b4;
}
.legend li span {
  width: 10px;
  height: 10px;
  margin-right: 5px;
  border-radius: 50%;
  display: inline-block;
}
.history-box {
  display: block;
  color: #555;
  margin-bottom: 8px;
}
.history-box.flex {
  display: flex;
}
.history-box .history-box-list-index {
  position: relative;
  display: flex;
}
.history-box .history-box-list-index span {
  text-align: center;
  display: block;
  width: 18px;
  height: 18px;
  line-height: 18px;
  background: #3883e5;
  border-radius: 50%;
  color: #fff;
  font-size: 12px;
}
.history-box .history-box-list-index:after {
  content: '';
  position: absolute;
  width: 1px;
  height: calc(100% - 20px);
  background: #e8eaec;
  left: 8px;
  top: 24px;
}
.history-box:last-child .history-box-list-index:after {
  width: 0;
}
.history-box .history-box-list-index img {
  width: 36px;
  height: 36px;
  display: inline-block;
  vertical-align: middle;
  border-radius: 5px;
  margin-left: 12px;
}
.history-box-list-warp {
  margin-left: 12px;
}
.history {
  display: block;
}
.history-manager {
  display: inline-block;
  vertical-align: middle;
  text-align: left;
}
.history-manager > div {
  display: flex;
  align-items: center;
}
.history-manager div span {
  display: inline-block;
  vertical-align: middle;
}
.history-manager > div > span:first-child {
  width: 10em;
}
.history-state {
  min-width: 120px;
}
.history-state span {
  width: auto;
  border-radius: 4px;
  padding: 0 5px;
  display: inline-block;
  background: #2baee9;
  color: #fff;
  font-size: 12px;
}
.history-box ul {
  display: flex;
  margin-top: 8px;
}
.history-box ul li {
  display: block;
  margin-bottom: 4px;
}
.history-box ul li span {
  display: inline-block;
  vertical-align: middle;
  line-height: 22px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.custom-info {
  display: block;
}
.custom-info li {
  display: inline-block;
  float: left;
  width: 80px;
  position: relative;
  margin-bottom: 10px;
  margin-right: 10px;
}
.custom-info li input {
  background-color: white;
  font-size: 12px;
  text-align: center;
  width: 80px;
  line-height: 16px;
  cursor: pointer;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "寰蒋闆呴粦", Arial, sans-serif;
  float: left;
  text-overflow: ellipsis;
  white-space: normal;
  height: 42px;
  border-width: 1px;
  border-style: solid;
  border-color: #dedddd;
  border-image: initial;
  border-radius: 3px;
  padding: 5px 10px;
  overflow: hidden;
  outline: 0;
}
.custom-info li .ivu-icon {
  display: none;
}
.custom-info input:hover {
  /*     border:1px solid #ff9900;
    color:#ff9900; */
}
.custom-info .btn-line-press input {
  color: #3883e5;
  border: 1px solid #3883e5;
}
.custom-info .btn-line-press.custFieldClass input {
  color: #c5c8ce;
  border-color: #dcdee2;
}
.btn-line-press input:focus {
  outline: 0;
}
.custom-info .btn-line-press .ivu-icon,
.custom-info .ivu-icon {
  display: inline-block;
  position: absolute;
  right: -8px;
  top: -8px;
  width: 16px;
  height: 16px;
  font-size: 18px;
  border-radius: 50%;
  background: #fff;
}
.custom-info .btn-line-press.custFieldClass .ivu-icon.custFieldIconClass {
  color: #dcdee2;
}
/*--鏌ヨ--*/
.query-condition {
  display: block;
}
.query-condition .ivu-radio-group {
  display: block;
}
.query-condition li {
  display: inline-block;
  padding: 0 10px;
  text-align: center;
  font-size: 12px;
  line-height: 22px;
  cursor: pointer;
  position: relative;
  margin-right: 8px;
}
.query-condition li:hover {
  color: #3883e5;
}
.query-condition-press {
  background: #3883e5;
  color: #fff !important;
  border-radius: 12px;
  position: relative;
}
.query-condition-clear {
  background: #fff;
  color: #555;
  border-radius: 12px;
  border: 1px solid #555;
  box-sizing: border-box;
  display: inline-flex !important;
  align-items: center;
  height: 22px;
  top: 1px;
}
.query-condition-clear:hover {
  border: 1px solid #3883e5;
}
/*.query-condition li .ivu-icon-android-close{*/
/*display: none;*/
/*}*/
/*.query-condition .query-condition-press  .ivu-icon-android-close{*/
/*position: absolute;*/
/*right: 6px;*/
/*color: #fff;*/
/*top: 6px;*/
/*font-size: 12px;*/
/*display: inline-block;*/
/*}*/
/*.query-condition-disable{*/
/*color:#c3cbd6 !important;*/
/*}*/
.check-group-list .ivu-checkbox-group .ivu-checkbox-group-item {
  display: block;
  line-height: 32px;
  margin-right: 0;
}
.check-group-list .ivu-checkbox-group .ivu-checkbox-group-item .ivu-checkbox + span,
.check-group-list .ivu-checkbox-group .ivu-checkbox-group-item.ivu-checkbox-wrapper + span {
  max-width: calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 0;
  display: inline-block;
  vertical-align: middle;
}
.prd-tree {
  display: none;
}
.query-condition-show .prd-tree {
  display: block;
  border-radius: 4px;
  background: #fff;
  padding: 5px;
  position: absolute;
  left: 0;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  z-index: 4;
  top: 27px;
}
.prd-tree .ivu-tree ul li {
  text-align: left !important;
  margin: 0;
  line-height: 32px;
}
.prd-tree .ivu-tree ul li:hover {
  color: #495060 !important;
}
.module .ivu-form-item-content .ivu-input-wrapper .ivu-input-icon {
  z-index: 1;
  cursor: pointer;
}
.prd-tree .ivu-tree ul li .ivu-tree-arrow {
  color: #495060;
}
.module-mr-btm .ivu-form-item {
  margin-bottom: 5px !important;
}
/*--鍗忚--*/
.protocpl-box .ivu-tabs-bar {
  margin-bottom: 8px;
}
.perform-up {
  width: 100%;
  left: 0;
  background-color: #f8f8f8;
  text-align: center;
  position: absolute;
  bottom: 0;
  padding: 0 8px;
}
.protocpl-box .ivu-card {
  position: relative;
}
.protocpl-box .ivu-button {
  color: #9ea7b4;
}
.protocpl-box .edit:hover {
  color: #2d8cf0 !important;
}
.protocpl-box .delete:hover {
  color: #ff5c33 !important;
}
.protocpl-box .submit:hover {
  color: #0c6 !important;
}
.ivu-col-span-24-5 {
  display: inline-block !important;
  width: 20%;
  float: left;
}
.protocol-upload {
  border-radius: 4px;
  border: 1px dashed #e3e8ee;
  width: 100%;
  height: 15vw;
  overflow: hidden;
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  cursor: pointer;
}
.protocol-img {
  width: 100%;
  height: 15vw;
  overflow: hidden;
  text-align: center;
  cursor: pointer;
}
.protocol-img img {
  width: 100%;
}
.protocol-tit.ivu-form .ivu-form-item-label {
  color: #9ea7b4;
  padding: 7px 5px 7px 0;
}
.protocol-tit .ivu-form-item-content {
  line-height: 26px;
}
.protocol-tit .ivu-form-item {
  margin-bottom: 0 !important;
}
.protocol-tit .ivu-form-item-content {
  line-height: 22px;
}
.no-footer .ivu-modal-footer {
  border: none;
  padding: 0;
}
.link-blue-text {
  color: #0000ee;
}
.link-blue-text:hover {
  color: #3883e5;
}
.linkedClass .ivu-select-dropdown {
  max-width: calc(100vw - 680px) !important;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞�>椤圭洰鍩哄噯椤甸潰琛ㄦ牸鏍峰紡淇敼鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
#treegrid .mini-grid-headerCell-inner {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
#treegrid .mini-grid-headerCell {
  height: 32px;
}
#treegrid .mini-grid-cell.mini-tree-treecell {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
#treegrid .mini-grid-cell-inner.mini-grid-cell-nowrap {
  height: 32px;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩繍钀ラ〉闈㈠脊绐楁牱寮�*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prjOperationMain-win .ivu-upload {
  text-align: center;
  margin-top: 1em;
}
.prjOperationMain-win .ivu-upload.ivu-upload-select {
  width: 100%;
}
.prjOperationMain-win .iconfont.icon-unie123 {
  margin-right: 1em;
  vertical-align: top;
}
.prjOperationMain-win .ivu-modal-body {
  padding-top: 2em;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斾笟鍔′换鍔＄粺璁￠潰鏉块〉闈㈡牱寮忊�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.layout-content-main .select {
  font-size: 14px;
}
/* .layout-content-main .ivu-select-selection{
    border: 1px solid transparent !important;
} */
.layout-content-main .ivu-select-selection:hover {
  border: 1px solid #3883e5 !important;
}
/* .layout-content-main .line-chart{
    margin-top: 10px;
} */
.line-chart.ivu-card {
  margin: 16px 5% 0 5% !important;
}
/* .ring-chart.ivu-card{
    margin: 16px 5% 0 5% !important;
} */
.layout-content-main .line-chart .changebutton {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.layout-content-main .line-chart .changebutton .buttonA {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 16px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background: #3883e5;
  /*  box-shadow: 0 1px 10px #bcbcbc;*/
  color: #fff;
}
.layout-content-main .line-chart .changebutton .buttonB {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 16px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  background: #eee;
  color: #979797;
  /*  box-shadow: inset 0 1px 5px #bcbcbc;*/
}
.layout-content-main .line-chart .pic {
  /*  border: 1px solid;*/
  margin: 16px 5% 10px 5%;
}
.layout-content-main .ring-chart .ring {
  margin: 10px 5%;
}
.layout-content-main .ring-chart .ring .pic {
  /*  border: 1px solid;*/
  width: 24%;
  display: inline-flex;
  height: 100%;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩潰鏉块〉闈㈡牱寮忊�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.content .ivu-select-selection {
  border: 1px solid transparent !important;
}
.prjpanel .ivu-card {
  padding: 0;
}
.prjpanel .ivu-card-body {
  padding: 0 16px 0 16px;
  height: 100%;
}
.prjpanel .title {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.prjpanel .ivu-icon.ivu-icon-cube {
  color: #5cadff;
  margin-right: 6px;
}
.prjpanel-carousel {
  display: flex;
  justify-content: center;
  align-items: flex-start;
}
.prjpanel .ivu-carousel {
  height: calc(100% - 32px);
}
.prjpanel .ivu-carousel .ivu-carousel-list {
  height: 100%;
}
.prjpanel .ivu-carousel .ivu-carousel-list .ivu-carousel-track.higher {
  height: 100% !important;
}
.prjpanel .ivu-carousel .ivu-carousel-list .ivu-carousel-track.higher .ivu-carousel-item {
  height: 100% !important;
}
.itemset-detail .regionCol span {
  display: inline-block;
  width: 3em;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: middle;
}
.prjpanel-carousel .ivu-table-wrapper {
  height: 90%;
  margin-left: 1px;
}
.prjpanel-carousel .ivu-table-wrapper .ivu-table {
  height: 100%;
}
.prjpanel-carousel .ivu-table-wrapper .ivu-table .ivu-table-body {
  height: calc(100% - 64px);
}
.prjpanel .title .text {
  background: #e8f0fe;
  color: #333;
  height: 32px;
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
  box-shadow: 0 2px 12px rgba(232, 240, 254, 0.7);
}
.prjpanel .title .year {
  display: inline-flex;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  color: #fff;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  background: #3883e5;
  box-shadow: 0 2px 8px rgba(56, 131, 229, 0.7);
  margin-right: 6px;
}
.prjpanel .title .month {
  display: inline-flex;
  width: 18px;
  height: 18px;
  font-size: 12px;
  align-items: center;
  justify-content: center;
  color: #999;
  margin-right: 6px;
}
.prjpanel .change-icon {
  display: inline-flex;
  align-items: center;
  height: 24px;
}
.prjpanel .change-icon .ivu-icon.ivu-icon-social-yen {
  margin-top: 2px;
}
.prjpanel .title .year1 {
  color: #3883e5;
  font-size: 12px;
}
.prjpanel .title .month1 {
  color: #B9B9B9;
  font-size: 12px;
}
.prjpanel .user-information {
  height: calc(100% - 32px);
  overflow: hidden;
  font-size: 12px;
  color: #888;
}
.prjpanel .user-information .line {
  width: 100%;
  height: 1px;
  background: #e9eaec;
}
.prjpanel .user-information .people {
  height: 44%;
  padding: 10px 6px;
  line-height: 24px;
  display: flex;
  align-items: center;
}
.prjpanel .user-information .people ul {
  width: 48%;
  display: inline-block;
  line-height: 24px;
}
.prjpanel .user-information .people ul .position {
  display: inline-block;
  width: 84px;
}
.prjpanel .user-information .people ul .name {
  font-size: 14px;
  color: #333;
  line-height: 24px;
}
.prjpanel .user-information .prj {
  height: 34%;
  margin-top: 2%;
  display: flex;
  align-items: center;
}
.prjpanel .user-information .prj .name {
  display: inline-block;
  line-height: 24px;
  width: 24%;
}
.prjpanel .user-information .prj .num {
  display: inline-block;
  line-height: 24px;
  font-size: 15px;
  color: #333;
  width: 23.6%;
}
.prjpanel .user-information .more {
  text-align: right;
  padding-top: 12px;
}
.prjpanel .user-information .more .ivu-icon.ivu-icon-ios-arrow-forward {
  margin-left: 4px;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旇繍缁寸鐞唌ini琛ㄦ牸瀛椾綋淇敼鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.mini-grid-font-style .mini-grid-cell-inner,
.mini-grid-font-style .mini-grid-headerCell-inner {
  font-family: "Microsoft YaHei", "寰蒋闆呴粦", Arial, "PingFang SC", "Helvetica Neue", Helvetica, "Hiragino Sans GB", sans-serif;
  font-size: 12px;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斿洖娆捐窡韪〉闈㈡牱寮忊�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.payment-collection-content {
  height: 100% !important;
}
.payment-collection-content .left-content {
  height: 100%;
  background: #fff;
  padding: 48px 16px 0 !important;
}
.payment-collection-content .right-content {
  height: 100%;
  position: relative;
  left: 0;
  padding: 48px 0 0 16px !important;
}
.payment-collection-card {
  height: 200px !important;
}
.payment-collection-card .ivu-col.ivu-col-span-6 {
  padding: 0 8px;
  height: 100% !important;
}
.payment-collection-card .ivu-card:hover {
  box-shadow: 0 2px 12px #99999960;
}
.payment-collection-card .ivu-card {
  box-shadow: 0 2px 12px #99999920;
  display: flex;
  justify-content: center;
  align-items: center;
}
.payment-collection-card .prj-num {
  font-size: 32px;
  color: #3883e5;
  font-weight: bold;
  display: flex;
  justify-content: center;
  line-height: 32px;
}
.payment-collection-card .prj-name {
  font-size: 16px;
  display: flex;
  justify-content: center;
  margin-top: 1%;
}
.payment-collection-card .prj-icon {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
.payment-collection-card .prj-icon .icon-non-payment {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-non-payment-v1.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-num-A {
  font-size: 32px;
  color: #3883e5;
  font-weight: bold;
  display: flex;
  justify-content: center;
  line-height: 32px;
}
.payment-collection-card .prj-icon .icon-payment-out {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-payment-out.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-icon .icon-receivables {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-receivables.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-icon .icon-bad-debt {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-bad-debt.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-icon .icon-line-chart {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-line-chart.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-icon .icon-actual-receipts {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-actual-receipts.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-icon .icon-accumulated-receipts {
  width: 40px;
  height: 40px;
  background: url("../03images/icon-payment/icon-accumulated-receipts.png");
  background-size: contain;
  filter: drop-shadow(0px 4px 6px rgba(56, 131, 229, 0.5));
}
.payment-collection-card .prj-add {
  border: 1px dashed #3883e5;
}
.payment-collection-card .prj-add:hover {
  border: 1px dashed rgba(56, 131, 229, 0.5);
}
.payment-collection-card .prj-add .ivu-icon-plus-round {
  color: #3883e5;
  font-size: 32px;
}
.choosed-prj a {
  color: #3883e5;
}
.choosed-prj a:hover {
  color: #3883e5 !important;
}
.prjOrange .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.prjOrange .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.prjOrange .ivu-checkbox-focus {
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2);
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩潰鏉块〉闈�(鏀圭増)鏍峰紡鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prjpanel .ivu-card {
  box-shadow: 0 2px 12px rgba(144, 144, 144, 0.2);
  padding: 1.5vh 2vh;
}
.prjpanel .ivu-card-body {
  padding: 0;
}
.prjpanel .top-content {
  height: 35vh;
}
.prjpanel .middle-content {
  height: 40vh;
}
.prjpanel .bottom-content {
  height: 45vh;
  margin-top: 12px;
}
.prjpanel .title {
  height: 5vh;
}
.prjpanel .title .text {
  background: #fff;
  font-size: 2.5vh;
  box-shadow: 0 0 0 0;
  border-bottom: 1vh solid;
  border-radius: 0;
  border-image: -webkit-linear-gradient(to right, #3883e5, rgba(56, 131, 229, 0.6)) 5 5;
  border-image: -moz-linear-gradient(to right, #3883e5, rgba(56, 131, 229, 0.6)) 5 5;
  border-image: linear-gradient(to right, #3883e5, rgba(56, 131, 229, 0.6)) 5 5;
  padding: 0;
  height: 2vh;
  line-height: 2vh;
}
.prjpanel .title .more {
  font-size: 2vh;
  cursor: pointer;
}
.prjpanel .title .more .ivu-icon-ios-arrow-right {
  margin-left: 6px;
}
.prjpanel .top-content .basic-info {
  height: calc(100% - 6vh);
}
.prjpanel .top-content .basic-info .ivu-col {
  height: 100%;
}
.prjpanel .top-content .basic-info .ivu-col .ivu-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(144, 144, 144, 0.2);
}
.prjpanel .top-content .basic-info .mini-title {
  font-size: 2.5vh;
  /*font-weight: bold;*/
  color: #555;
}
.prjpanel .top-content .basic-info .chart {
  height: calc(100% - 4vh);
}
.prjpanel .top-content .basic-info .info-all,
.prjpanel .middle-content .baseline-info .info-all {
  display: flex;
  height: 100%;
}
.prjpanel .top-content .basic-info .info-all ul,
.prjpanel .middle-content .baseline-info .info-all ul {
  height: 100%;
  padding-top: 1vh;
}
.prjpanel .top-content .basic-info .info-all ul li {
  height: 33.3%;
}
.prjpanel .middle-content .baseline-info .info-all ul li {
  height: 25%;
}
.prjpanel .top-content .basic-info .info-all .mini-title,
.prjpanel .middle-content .baseline-info .info-all .mini-title {
  font-size: 2vh;
  color: #999;
  font-weight: normal;
}
.prjpanel .top-content .basic-info .info-all .info-text,
.prjpanel .middle-content .baseline-info .info-all .info-text {
  font-size: 2.5vh;
  color: #333;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 1vh;
  font-family: Georgia;
}
.prjpanel .top-content .basic-info .info-all .ivu-poptip {
  width: 100%;
}
.prjpanel .top-content .basic-info .info-all .ivu-poptip-rel {
  width: 100%;
}
.prjpanel .middle-content .baseline-info .info-all .red {
  color: #ed3f14;
}
.prjpanel .middle-content {
  margin-top: 12px;
}
.prjpanel .bottom-content .ivu-switch {
  background: #3883e5;
  border-color: #3883e5;
  box-shadow: 0 0 0 0;
}
.prjpanel .bottom-content .ivu-switch-checked {
  background: #1bbdcc;
  border-color: #1bbdcc;
}
.linkedBiz-modal {
  z-index: 2000 !important;
}
.luuc-commontable {
  position: relative;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗕笉鍚屽垎杈ㄧ巼鏍峰紡锛�2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
/*鍘� max-height 768*/
@media screen and (min-height: 10px) and (max-height: 1050px) {
  .systemHealthVerticalLine_ywzb {
    margin-top: 4px;
  }
  .systemHealthVerticalLine_ywjkd {
    top: 20px;
  }
  .prjFixedHelp {
    font-size: 13px;
  }
  .prjFixedHelp .prjFixedHelp_1 {
    height: 160px;
  }
  .prjFixedHelp_2 {
    top: calc(30vh + 170px);
  }
  .prjFixedHelp_3 {
    top: calc(30vh - 60px);
  }
  .prj-index {
    overflow-y: auto;
    overflow-x: hidden;
  }
  .prj-index .ivu-card .subTitle {
    font-size: 14px;
  }
  .prj-index .topContent {
    height: 36vh;
  }
  .prj-index .bottomContent {
    height: 70vh;
    margin-top: 12px;
  }
  .prj-index .ivu-card .subTitle .fontSize-12 {
    font-size: 12px;
  }
  .prj-index .prjInfo .labelName {
    font-size: 12px;
  }
  .prj-index .prjInfo .info {
    font-size: 14px;
  }
  .prj-index .bullet-green {
    border-top: 8px solid transparent;
    border-left: 8px solid #01b8aa;
    border-bottom: 8px solid transparent;
  }
  .prj-index .bullet-orange {
    border-top: 8px solid transparent;
    border-left: 8px solid #ff9900;
    border-bottom: 8px solid transparent;
  }
  .prj-index .bullet-blue {
    border-top: 8px solid transparent;
    border-left: 8px solid #138fee;
    border-bottom: 8px solid transparent;
  }
  .prj-index .subTitle .icon-wenhao-wuquan {
    height: 14px;
    width: 14px;
    line-height: 14px;
    font-size: 12px;
  }
  .prj-index .healthy img {
    height: calc(100% - 46px);
    padding-top: 2vh;
  }
  .prj-index .healthy .state {
    font-size: 24px;
    line-height: 28px;
  }
  .prj-index .healthy .date {
    font-size: 12px;
  }
  .prj-index .prjTip .title {
    font-size: 14px;
  }
  .prj-index .prjTip .bullet-size {
    border-width: 1vh;
    top: 1.2vh;
  }
  .prj-index .chart-num {
    font-size: 14px;
  }
  .prj-index .ivu-icon-android-sync {
    transform: rotate(90deg);
    color: #999;
    font-size: 18px;
  }
  .prj-index .healthRefresh {
    width: calc((100% - 3em) - 22px);
  }
}
@media screen and (min-height: 1051px) and (max-height: 2160px) {
  .systemHealthVerticalLine_ywzb {
    margin-top: 2px;
  }
  .systemHealthVerticalLine_ywjkd {
    top: 17px;
  }
  .prjFixedHelp {
    font-size: 14px;
  }
  .prjFixedHelp .prjFixedHelp_1 {
    height: 170px;
  }
  .prjFixedHelp_2 {
    top: calc(30vh + 190px);
  }
  .prjFixedHelp_3 {
    top: calc(30vh - 70px);
  }
  .prj-index {
    overflow: hidden;
  }
  .prj-index .ivu-card .subTitle {
    font-size: 2vh;
  }
  .prj-index .topContent {
    height: 30%;
  }
  .prj-index .bottomContent {
    height: calc(70% - 24px);
    margin-top: 12px;
  }
  .prj-index .ivu-card .subTitle .fontSize-12 {
    font-size: 1.5vh;
  }
  .prj-index .prjInfo .labelName {
    font-size: 1.5vh;
  }
  .prj-index .prjInfo .info {
    font-size: 1.8vh;
    height: 2.5vh;
    line-height: 2.5vh;
  }
  .prj-index .bullet-green {
    border-top: 1vh solid transparent;
    border-left: 1vh solid #01b8aa;
    border-bottom: 1vh solid transparent;
  }
  .prj-index .bullet-orange {
    border-top: 1vh solid transparent;
    border-left: 1vh solid #ff9900;
    border-bottom: 1vh solid transparent;
  }
  .prj-index .bullet-blue {
    border-top: 1vh solid transparent;
    border-left: 1vh solid #138fee;
    border-bottom: 1vh solid transparent;
  }
  .prj-index .buttonGroup .ivu-btn {
    font-size: 1.5vh;
  }
  .prj-index .subTitle .icon-wenhao-wuquan {
    height: 2vh;
    width: 2vh;
    line-height: 2vh;
    font-size: 1.5vh;
  }
  .prj-index .healthy img {
    height: 12vh;
  }
  .prj-index .healthy .state {
    font-size: 3vh;
  }
  .prj-index .healthy .date {
    font-size: 1.5vh;
  }
  .prj-index .prjTip .title {
    font-size: 16px;
  }
  .prj-index .prjTip .bullet-size {
    border-width: 0.8vh;
    top: 0.8vh;
  }
  .prj-index .chart-num {
    font-size: 2vh;
  }
  .prj-index .ivu-icon-android-sync {
    transform: rotate(90deg);
    color: #999;
    font-size: 2.5vh;
  }
  .prj-index .healthRefresh {
    width: calc(100% - 4em - 30px);
  }
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛椤甸〉闈㈡牱寮忥紙2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prj-index .prjContent {
  height: 100%;
}
.prj-index .ivu-col {
  height: 100%;
}
.prj-index .ivu-card {
  height: 100%;
}
.prj-index .ivu-card-body {
  height: 100%;
}
.prj-index .prjContent .leftContent .bottom {
  height: 54%;
  margin-top: 12px;
  padding: 8px;
}
.prj-index .prjContent .leftContent .bottom .ivu-card-body {
  padding: 0;
}
.prj-index .prjContent .leftContent .top {
  height: calc(46% - 12px);
}
.prj-index .ivu-card {
  box-shadow: 0 2px 12px rgba(144, 144, 144, 0.2);
}
.prj-index .ivu-card:hover {
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  border-color: #eee;
}
.prj-index .ivu-card .subTitle {
  position: relative;
  display: flex;
  align-items: center;
  height: 4vh;
  line-height: 4vh;
}
.prj-index .ivu-card .subTitle .bullet {
  height: 2vh;
  width: 2px;
  background: #3883e5;
  display: inline-block;
  position: absolute;
  left: -16px;
}
.prj-index .chart {
  width: 100%;
  height: calc(100% - 4vh);
}
/* .prj-index .itemset-detail{
	height: 100%;
} */
.prj-index .prjRegionStatCard .ivu-carousel {
  height: 100%;
}
.prj-index .prjRegionStatCard .ivu-carousel .ivu-carousel-list {
  height: 100%;
}
.prj-index .prjRegionStatCard .ivu-carousel .ivu-carousel-list .ivu-carousel-track {
  height: calc(100% - 40px);
}
.prj-index .prjRegionStatCard .ivu-carousel .ivu-carousel-list .ivu-carousel-track .itemset-detail {
  height: 100%;
}
.prj-index .prjRegionStatCard .ivu-carousel .ivu-carousel-list .ivu-carousel-track .itemset-detail .ivu-table-body {
  height: 100%;
}
.prj-index .itemset-detail .ivu-table-body::-webkit-scrollbar {
  display: none;
}
.prj-index .itemset-detail .ivu-table-body {
  /* height: calc(100% - 44px); */
  overflow-x: hidden;
  color: #262626;
}
.prj-index .itemset-detail .ivu-icon-arrow-right-b,
.prj-index .itemset-detail .ivu-icon-arrow-down-b {
  padding-right: 8px;
  color: #138fee;
}
.prj-index .itemset-detail .ivu-icon-ios-arrow-right {
  color: #138fee;
  font-weight: bold;
}
.prj-index .itemset-detail .ivu-table-cell {
  padding: 0 4px !important;
}
.prj-index .itemset-detail td,
.prj-index .itemset-detail th {
  height: 5.3vh;
  border-bottom: none;
}
.prj-index .itemset-detail th {
  font-weight: bold;
  color: #262626;
}
.prj-index .itemset-detail th:first-child {
  background: #f1f1f1;
}
.prj-index .itemset-detail th:nth-child(2),
.prj-index .itemset-detail th:nth-child(5) {
  background: #bfe7ff;
}
.prj-index .itemset-detail th:nth-child(3) {
  background: rgba(56, 131, 229, 0.4);
}
.prj-index .itemset-detail th:nth-child(4) {
  background: #7dd7bc;
}
.prj-index .itemset-detail .ivu-table-stripe tr:nth-child(2n) td {
  background: #fff;
}
.prj-index .itemset-detail .itemset-detail-dropdown td {
  background: #f1f1f1;
}
.prj-index .itemset-detail .itemset-detail-dropdown:nth-child(2n) td {
  background: #f1f1f1 !important;
}
.prj-index .itemset-detail .ivu-table-stripe .ivu-table-body tr.ivu-table-row-hover td,
.prj-index .itemset-detail .ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td {
  background: rgba(80, 184, 255, 0.2) !important;
  cursor: pointer;
}
.itemset-detail-header-table .ivu-table-tip {
  display: none;
}
.prj-index .middleContent .select-year {
  font-size: 2.5vh;
  height: 4vh;
  line-height: 4vh;
  display: flex;
  font-weight: bold;
  margin-bottom: 2vh;
  justify-content: flex-end;
}
.prj-index .ivu-dropdown .ivu-btn {
  background: #fff !important;
  border-color: #fff !important;
  font-size: 2vh;
  box-shadow: 0 2px 12px rgba(144, 144, 144, 0.2) !important;
  margin-right: 8px;
  color: #262626;
  padding: 4px 8px;
}
.prj-index .ivu-dropdown .ivu-btn:hover {
  color: #3883e5 !important;
}
.prj-index .middleContent .prj-map {
  width: 100%;
  height: calc(100% - 6vh);
}
.prj-index .ivu-dropdown .ivu-icon-arrow-down-b {
  color: #3883e5;
}
.prj-index .middleContent .other-area {
  position: absolute;
  bottom: 6vh;
  width: 10vw;
}
.prj-index .middleContent .other-area .ivu-btn-ghost {
  width: 100%;
  height: 8vh;
  font-size: 2.5vh;
  background: rgba(56, 131, 229, 0.1);
  border-color: #3883e5;
  color: #3883e5;
}
.prj-index .rightContent .health {
  height: 30vh;
  display: flex;
}
.prj-index .rightContent .health > div {
  flex: 1;
}
.prj-index .rightContent .line {
  height: 1px;
  width: calc(100% + 32px);
  background: #f0f0f0;
  margin: 2vh -16px;
}
.prj-index .rightContent .itemset-state {
  height: calc(100% - 42vh);
}
.prj-index .chart-num {
  font-family: Georgia;
  margin-left: 4px;
  color: #262626;
  font-weight: bold;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛潰鏉块〉闈㈡牱寮忥紙2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prj-index {
  padding: 8px 5% 12px 5%;
  background: #f8f8f9;
  height: 100%;
  position: relative;
}
.prj-index .ivu-card .subTitle .ivu-icon-ios-arrow-right {
  margin-left: 4px;
}
.prj-index .ivu-card .subTitle .fontSize-12 {
  color: #999;
}
.prj-index .ivu-card .lineHeight-3vh {
  height: 3vh;
  line-height: 3vh;
  margin-bottom: 1vh;
}
.prj-index .prjInfo {
  height: calc((100% - 10vh - 1px) / 4);
  display: flex;
  align-items: center;
}
.prj-index .evaluation {
  height: calc((100% - 4vh) / 3);
}
.prj-index .prjInfo .leftInfo {
  width: 56%;
}
.prj-index .prjInfo .rightInfo {
  width: 44%;
}
.prj-index .prjInfo .leftInfo,
.prj-index .prjInfo .rightInfo {
  height: auto;
  display: inline-block;
}
.prj-index .prjInfo .labelName {
  color: #999;
  display: inline-block;
}
.prj-index .prjInfo .fixedLabelName {
  width: calc(5em + 5px);
}
.prj-index .prjInfo .info {
  color: #262626;
  font-family: Georgia;
  display: inline-block;
}
.prj-index .chart-per45 {
  width: 45%;
  display: inline-block;
}
.prj-index .chart-per55 {
  width: 55%;
  display: inline-block;
}
.prj-index .buttonGroup {
  display: inline-flex;
  width: 30%;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
}
.prj-index .buttonGroup .ivu-btn {
  height: 5vh;
}
.prj-index .buttonGroup .ivu-btn:hover {
  opacity: 0.8;
}
.prj-index .buttonGroup .buttonGhost-green {
  background-color: #eefffe;
  border-color: rgba(1, 184, 170, 0.3);
  color: #01b8aa;
}
.prj-index .buttonGroup .buttonGhost-blue {
  background-color: #f2f9fe;
  border-color: rgba(19, 143, 238, 0.3);
  color: #138fee;
}
.prj-index .buttonGroup .buttonGhost-orange {
  background-color: rgba(56, 131, 229, 0.1);
  border-color: rgba(56, 131, 229, 0.3);
  color: #3883e5;
}
.prj-index .chart-long {
  width: 100%;
  padding: 0 2vh;
  height: 45%;
  display: flex;
}
.prj-index .chart-long .flex-isometry {
  flex: 1;
}
.prj-index .dashedLine {
  height: 1px;
  border-bottom: 1px dashed #f0f0f0;
  margin: 2% 0;
}
.prj-index .subTitle .icon-wenhao-wuquan {
  border: 1px solid #dedede;
  border-radius: 50%;
  color: #999;
  text-align: center;
  margin-left: 4px;
}
.prj-index .subTitle .icon-wenhao-wuquan:hover {
  cursor: pointer;
}
.prj-index .healthy img {
  display: block;
  margin: 0 auto;
}
.prj-index .healthy .prj-health {
  filter: drop-shadow(0 0 0px rgba(94, 193, 171, 0.2)) drop-shadow(0 0 6px rgba(97, 198, 171, 0.2)) drop-shadow(0 0 6px rgba(110, 225, 173, 0.2));
  -webkit-filter: drop-shadow(0 0 0px rgba(94, 193, 171, 0.2)) drop-shadow(0 0 6px rgba(97, 198, 171, 0.2)) drop-shadow(0 0 6px rgba(110, 225, 173, 0.2));
}
.prj-index .healthy .prj-subHealth {
  filter: drop-shadow(0 0 0px rgba(56, 131, 229, 0.2)) drop-shadow(0 0 6px rgba(56, 131, 229, 0.2)) drop-shadow(0 0 6px rgba(56, 131, 229, 0.2));
  -webkit-filter: drop-shadow(0 0 0px rgba(56, 131, 229, 0.2)) drop-shadow(0 0 6px rgba(56, 131, 229, 0.2)) drop-shadow(0 0 6px rgba(56, 131, 229, 0.2));
}
.prj-index .healthy .prj-notHealth {
  filter: drop-shadow(0 0 0px rgba(255, 95, 51, 0.2)) drop-shadow(0 0 6px rgba(255, 95, 51, 0.2)) drop-shadow(0 0 6px rgba(255, 95, 51, 0.2));
  -webkit-filter: drop-shadow(0 0 0px rgba(255, 95, 51, 0.2)) drop-shadow(0 0 6px rgba(255, 95, 51, 0.2)) drop-shadow(0 0 6px rgba(255, 95, 51, 0.2));
}
.prj-index .healthy .state {
  text-align: center;
  font-weight: bold;
}
.prj-index .healthy .health {
  color: #01b8aa;
}
.prj-index .healthy .subHealth {
  color: #3883e5;
}
.prj-index .healthy .notHealth {
  color: #ff4521;
}
.prj-index .healthy .date {
  text-align: center;
  font-family: "Microsoft YaHei";
  font-weight: normal;
  color: #555;
}
.prj-index .bullet-green,
.prj-index .bullet-orange,
.prj-index .bullet-blue {
  width: 0;
  height: 0;
  display: inline-block;
  position: absolute;
  left: -16px;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛潰鏉垮仴搴峰害鎻愮ず闈㈡澘鏍峰紡锛�2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prj-index .ivu-poptip-body {
  padding: 16px;
  width: 500px;
  max-width: 600px;
}
.prj-index .miniTip .ivu-poptip-body {
  width: 400px;
}
.prj-index .prjTip {
  width: 100%;
  height: auto;
  font-size: 14px;
}
.prj-index .prjTip .title {
  font-weight: bold;
  color: #262626;
  margin-bottom: 8px;
}
.prj-index .prjTip .content-info {
  line-height: 2;
  display: flex;
  align-items: flex-start;
  position: relative;
}
.prj-index .prjTip .content-info .label {
  color: #555;
  padding-left: 16px;
  /* border-left: 2px solid; */
}
.prj-index .prjTip .content-info .text {
  color: #999;
  width: calc(100% - 50px);
  word-break: break-all;
  display: inline-block;
  white-space: normal;
}
.prj-index .prjTip .content-rule {
  display: flex;
  justify-content: space-between;
}
.prj-index .prjTip .content-rule > div {
  flex: 0.3;
}
.prj-index .prjTip .content-rule .mini-title {
  line-height: 2;
  padding: 0 8px;
}
.prj-index .prjTip .content-rule .detail {
  display: flex;
  justify-content: space-between;
  padding: 0 8px;
  line-height: 2;
}
.prj-index .prjTip .bullet-size {
  left: 0;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛潰鏉胯摑鑹茶〃鏍兼牱寮忥紙2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prj-blueTable .ivu-table {
  color: #262626;
}
.prj-blueTable .ivu-table th {
  background: #bfe7ff;
  border-right: 1px solid #f2f9fe !important;
  border-bottom: 1px solid #f2f9fe !important;
}
.prj-blueTable .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.prj-blueTable .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background: #f2f9fe;
}
.prj-blueTable .ivu-table-stripe .ivu-table-body tr.ivu-table-row-hover td,
.prj-blueTable .ivu-table-stripe .ivu-table-fixed-body tr.ivu-table-row-hover td {
  background: rgba(80, 184, 255, 0.2) !important;
}
.prj-bluePager .ivu-page-item-active {
  background-color: #138fee !important;
  border-color: #138fee !important;
}
.prj-bluePager .ivu-page-item:hover,
.prj-bluePager .ivu-page-next:hover,
.prj-bluePager .ivu-page-prev:hover {
  border-color: #138fee !important;
}
.prj-bluePager .ivu-page-item:hover a,
.prj-bluePager .ivu-page-next:hover a,
.prj-bluePager .ivu-page-prev:hover a {
  color: #138fee !important;
}
.prj-bluePager .ivu-page-item-active a,
.prj-bluePager .ivu-page-item-active:hover a {
  color: #fff !important;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗙壒娈婂脊绐楋紙2020-04-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prjModal-noHeader .ivu-modal-header {
  display: none;
}
.prjModal-noHeader .ivu-modal-close {
  z-index: 999;
}
.prjModal-noHeader .ivu-modal-body {
  min-height: 64vh;
  padding-top: 8px;
}
.prjModal-noHeader .ivu-modal-body .ivu-tabs-nav-wrap {
  padding-top: 0;
}
.prjModal-noHeader .ivu-modal-body .ivu-tabs-bar {
  border-bottom: 1px solid #dddee1 !important;
  margin-bottom: 16px !important;
}
.prjModal-noHeader .tab .ivu-tabs-nav .ivu-tabs-tab {
  font-size: 14px;
}
.prjNocardModal .ivu-card {
  box-shadow: 0 0 0 0;
  padding: 0;
}
.prjNocardModal .ivu-card:hover {
  box-shadow: 0 0 0 0;
}
.prjNocardModal .ivu-card-body .title {
  display: flex;
  justify-content: flex-end;
}
.prjNocardModal .ivu-card-body .title .text {
  display: none;
}
.prjNocardModal .ivu-carousel {
  height: 55vh;
}
/* .prjNocardModal .ivu-carousel-list{
	height: 100%;
}
.prjNocardModal .ivu-carousel-track.higher{
	height: 100%;
}
.prjNocardModal .ivu-carousel-item{
	height: 100% !important;
}   */
.prjInfoModal .prjBenchmarkInfoTable td {
  height: 32px;
}
.prjInfoModal .prjBasicInfo .ivu-form-item {
  margin-bottom: 12px !important;
}
.prjInfoModal .prjBasicInfo .ivu-form-item {
  margin-bottom: 12px !important;
}
.prjInfoModal .prjBasicInfo .ivu-form .ivu-form-item-label {
  font-size: 14px;
}
.prjInfoModal .prjBasicInfo .ivu-form .ivu-form-item-content {
  font-size: 14px;
}
.prjLevel2Plan .ivu-btn-primary.disabled,
.prjLevel2Plan .ivu-btn-primary.disabled.active,
.prjLevel2Plan .ivu-btn-primary.disabled:active,
.prjLevel2Plan .ivu-btn-primary.disabled:focus,
.prjLevel2Plan .ivu-btn-primary.disabled:hover,
.prjLevel2Plan .ivu-btn-primary[disabled],
.prjLevel2Plan .ivu-btn-primary[disabled].active,
.prjLevel2Plan .ivu-btn-primary[disabled]:active,
.prjLevel2Plan .ivu-btn-primary[disabled]:focus,
.prjLevel2Plan .ivu-btn-primary[disabled]:hover,
.prjLevel2Plan fieldset[disabled] .ivu-btn-primary,
.prjLevel2Plan fieldset[disabled] .ivu-btn-primary.active,
.prjLevel2Plan fieldset[disabled] .ivu-btn-primary:active,
.prjLevel2Plan fieldset[disabled] .ivu-btn-primary:focus,
.prjLevel2Plan fieldset[disabled] .ivu-btn-primary:hover {
  color: #bbbec4;
  background-color: #f7f7f7 !important;
  border-color: #dddee1 !important;
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛潰鏉跨炕椤佃〃鏍兼牱寮忥紙2020-06-12锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.maxHeightTable .ivu-table-body {
  max-height: calc(100vh - 172px);
}
/*鈥斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�旈」鐩鐞嗛」鐩�夋嫨缁勪欢鏍峰紡锛�2020-06-23锛夆�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺�斺��*/
.prj-item-choose {
  display: block;
  position: absolute;
  z-index: 100;
  top: 48px;
  left: 5%;
  right: 0px;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  background: #fff;
  box-shadow: 0 1px 8px rgba(167, 167, 167, 0.5);
  float: left;
  width: 90%;
  min-height: 260px;
  max-height: 400px;
  overflow-x: hidden;
  overflow-y: hidden;
}
.prj-item-choose .prjList {
  display: inline-block;
  max-height: 352px;
  overflow: auto;
  background: #f8f8f8;
  padding-top: 8px;
  width: 100%;
}
.prj-item-choose .prjList li {
  padding: 8px 5px;
  width: 20%;
  max-width: 20%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  display: inline-block;
  font-size: 14px;
  float: left;
  position: relative;
}
.prjList::-webkit-scrollbar {
  display: none;
}
.prj-item-choose .bigRegions {
  font-size: 12px;
  color: #999;
  margin-left: 17px;
  padding-bottom: 4px;
}
.prj-item-choose .bigRegions .choosed {
  color: #3883e5;
  font-weight: bold;
  border-bottom: 2px solid #3883e5;
}
.prj-item-choose .bigRegions span {
  padding: 4px 0;
  margin-right: 16px;
  cursor: pointer;
}
.prj-item-choose .bigRegions span:last-child {
  margin-right: 0;
}
.prj-index .middleContent .prj-mgttypes .unchoosedBtn {
  margin-left: 10px;
  font-size: 2vh;
  background: #fff;
  border: 1px solid transparent;
  box-shadow: 0 2px 12px rgba(144, 144, 144, 0.2) !important;
}
.prj-index .middleContent .prj-mgttypes .choosedBtn {
  margin-left: 10px;
  font-size: 2vh;
  background: #3883e5;
  border: 1px solid transparent;
  box-shadow: 0 2px 12px rgba(56, 131, 229, 0.2) !important;
  color: #fff;
}
/*--------------------------------------------------------------------*/
.luuc-nodeplan-table .mini-grid-newRow {
  background: #fff;
}
.luuc-nodeplan-table .mini-grid-row-selected {
  background: #dedede;
}
/*------------------------绯荤粺鍏憡寮规鏍峰紡-----------------------------*/
.notice-detail-modal .ivu-modal-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
}
.notice-detail-modal .ivu-modal {
  top: 0;
  max-height: 100vh;
}
/*------------------------鏂扮増DMP杞彂璐ｄ换浜虹粍浠舵牱寮�-----------------------------*/
.taskForward {
  padding-left: 4px;
  height: 24px;
  line-height: 24px;
}
.taskForward .general {
  display: inline-block;
  padding: 0 6px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  cursor: pointer;
}
.taskForward .select {
  background: #3883e5;
  color: #fff;
}
.taskForward-mrt .ivu-form-item-label {
  margin-top: 24px;
}
.custLi .view_table_width .ivu-table-wrapper {
  border: 1px solid #dddee1 !important;
  border-right: 0 !important;
  border-bottom: 0 !important;
}
.custLi .view_table_width .ivu-table:before,
.custLi .view_table_width .ivu-table:after {
  display: block !important;
  background-color: #dddee1;
}
.custLi .view_table_width .ivu-table:before {
  height: 1px !important;
}
.custLi .view_table_width .ivu-table th {
  font-weight: bold;
  height: 36px;
  border-bottom: 1px solid #dddee1;
}
.custLi .view_table_width .ivu-table th:not(:last-child) {
  border-right: 1px solid #dddee1;
}
.custLi .view_table_width .ivu-table td {
  border: none !important;
  height: 36px;
}
.custLi .view_table_width .ivu-table td .ivu-table-cell {
  padding: 0 8px !important;
}
/*鑺傜偣椤甸潰/鏂板椤甸潰宓屽琛ㄦ牸鏍峰紡缁熶竴*/
.custLi .ivu-table th {
  font-weight: bold;
  height: 36px;
  border: 1px solid #dddee1 !important;
}
.custLi .ivu-table th:not(:last-child) {
  border-right: transparent !important;
}
.custLi .ivu-table td {
  height: 36px;
}
.custLi .ivu-table td:first-child {
  border-left: 1px solid #dddee1 !important;
}
.custLi .ivu-table td:last-child {
  border-right: 1px solid #dddee1 !important;
}
.reqDeliveryFormClass.custLi .reqDeliveryTable .ivu-table .ivu-table-tip td:last-child {
  width: 100% !important;
}
.custLi .ivu-table td .ivu-table-cell,
.custLi .ivu-table th .ivu-table-cell {
  padding: 0 8px !important;
}
.custLi .ivu-table .ivu-table-tbody tr:last-child td {
  border-bottom: 1px solid #dddee1 !important;
}
.custLi .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.custLi .view_modal_table .ivu-table .ivu-table-tip tr td {
  border: none !important;
}
.custLi .ivu-checkbox-checked:hover .ivu-checkbox-inner {
  border-color: #3883e5 !important;
}
.custLi .ivu-checkbox .ivu-checkbox-inner:focus,
.custLi .ivu-checkbox:focus,
.ivu-checkbox-wrapper:focus {
  box-shadow: 0 0 0 1px rgba(56, 131, 229, 0.2) !important;
  outline: none;
}
.custLi .mini-grid-headerCell {
  font-weight: bold;
}
.custLi .mini-grid-headerCell,
.custLi .mini-grid-topRightCell,
.custLi .mini-grid-columnproxy {
  background: #f1f1f1 !important;
}
.custLi .mini-grid-headerCell:not(:last-child),
.custLi .mini-grid-bottomCell:not(:last-child) {
  border-right: 1px solid #dddee1 !important;
}
.custLi .luuc-nodeplan-table .mini-grid-row-selected {
  background: transparent !important;
}
.custLi .luuc-nodeplan-table .mini-grid-row:hover {
  background: #ebf7ff !important;
}
.custLi .mini-grid .mini-grid-cell-selected {
  background: transparent !important;
}
.custLi .mini-grid-table tr {
  height: 36px;
}
.custLi .mini-grid-headerCell-outer {
  padding-left: 0;
}
.custLi .mini-grid-cell,
.custLi .mini-grid-headerCell,
.custLi .mini-grid-filterCell,
.custLi .mini-grid-summaryCell {
  padding-left: 8px;
}
.personalLabelCss .ivu-radio-wrapper,
.ivu-checkbox-wrapper {
  line-height: 32px;
}
.personalLabelCss .illustrationCss {
  font-size: 12px;
  padding: 8px;
  line-height: 32px;
  border: 1px solid #3883e5;
  text-align: left;
}
.personalLabelCss li {
  list-style: disc;
  color: #999;
}
.personalLabelCss ul {
  padding-left: 16px;
}
.personalLabelCss .illustrationLabelCss {
  color: #999;
  font-weight: bold;
  margin-right: 8px;
}
.personalLabelCss .illustrationContentCss {
  color: #333;
}
/*flex甯冨眬鏍峰紡*/
.flexStart {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
/*椤圭洰鎺т欢-鍖哄煙澶у皬*/
.bigRegions_tab {
  width: 65%;
}
.bigRegions_tab .bigRegions {
  display: inline-block;
  vertical-align: bottom;
  margin-left: 0;
}
.bigRegions_warp {
  position: relative;
  display: inline-block;
  width: calc(100% - 100px);
  height: 24px;
  overflow: hidden;
  vertical-align: middle;
  line-height: 24px;
}
.bigRegions_tab .bigRegions {
  position: absolute;
  white-space: nowrap;
  left: 0;
}
.bigRegions_tab .bigRegions_tab_all {
  /* padding: 0 0 4px 0; */
  margin-right: 16px;
  cursor: pointer;
  color: #999;
  vertical-align: top;
  line-height: 24px;
}
.bigRegions_tab .bigRegions_tab_all.choosed {
  color: #3883e5;
  font-weight: bold;
  /* border-bottom: 2px solid @mainColor; */
}
.bigRegions_tab i {
  display: inline-block;
  vertical-align: middle;
  font-size: 16px;
  cursor: pointer;
  /* margin-top: -6px; */
  color: #808695;
}
.bigRegions_tab i.no_active {
  color: #c5c8ce;
  cursor: no-drop;
}
.bigRegions_tab i.no_show {
  visibility: hidden;
}
.prjpanel .ivu-btn-ghost:hover {
  color: #3883e5;
  border-color: #3883e5;
}
.costDisplayCol ul li {
  display: flex;
  align-items: center;
}
.colorBlack {
  color: black;
}
.dep_header_lo {
  line-height: 32px;
  padding: 0 12px;
  cursor: pointer;
  font-size: 14px !important;
}
.prj-item-choose .roleCondWrapClass {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 6px 0px;
  background: #ffffff;
  padding-left: 18px;
}
.prj-item-choose .roleCondWrapClass .roleCondItemClass {
  display: inline-block;
  padding: 0 0 4px 0;
  margin-right: 16px;
  cursor: pointer;
  color: #999;
  vertical-align: top;
}
.prj-item-choose .roleCondWrapClass .roleCondItemClass.choosedRoleCond {
  color: #3883e5;
  font-weight: bold;
  border-bottom: 2px solid #3883e5;
}
.prj-item-choose .roleCondWrapClass .roleCondItemClass .ivu-tooltip {
  display: inline-block;
}
.prj-item-choose .roleCondWrapClass .roleCondItemClass .iconfont {
  color: #A5ACC4;
}
.prj-item-choose .roleCondWrapClass .roleCondItemClass.choosedRoleCond .iconfont {
  color: #3883e5;
}
.allow_select .ivu-select-arrow {
  margin-top: 0;
  display: block !important;
}
.prjNocardModal .prjHumanResCardHeightNew .ivu-carousel {
  height: auto;
}
.prjHrCompareCarTwo {
  height: 61vh;
  width: 100%;
  display: flex;
}
.prjHrCompareCarTwo .prjHrCompareCarTwoLeft {
  width: 40%;
  height: 100%;
  display: flex;
}
.prjHrCompareCarTwo .prjHrCompareCarTwoLeft > div {
  width: 50%;
  height: 100%;
}
.prjHrCompareCarTwo .prjHrCompareCarTwoLeft > div > div {
  width: 100%;
  height: 100%;
}
.prjHrCompareCarTwo .prjHrCompareCarTwoRight {
  width: 60%;
  height: 100%;
}
.prjSearchWWarp {
  position: relative;
}
.prjSearchWWarp .prjSearchIcon {
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 32px;
  right: 0;
  z-index: 99;
  top: 0;
  cursor: pointer;
}
.prjSearchWWarp .prjSearchTable {
  position: absolute;
  width: 100%;
  max-height: 100px;
  overflow: auto;
  margin: 5px 0;
  padding: 5px 8px;
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  z-index: 99;
}
.ivu-btn-primary.disabled,
.ivu-btn-primary.disabled.active,
.ivu-btn-primary.disabled:active,
.ivu-btn-primary.disabled:focus,
.ivu-btn-primary.disabled:hover,
.ivu-btn-primary[disabled],
.ivu-btn-primary[disabled].active,
.ivu-btn-primary[disabled]:active,
.ivu-btn-primary[disabled]:focus,
.ivu-btn-primary[disabled]:hover,
fieldset[disabled] .ivu-btn-primary,
fieldset[disabled] .ivu-btn-primary.active,
fieldset[disabled] .ivu-btn-primary:active,
fieldset[disabled] .ivu-btn-primary:focus,
fieldset[disabled] .ivu-btn-primary:hover {
  color: #c5c8ce !important;
  background-color: #f7f7f7 !important;
  border-color: #dcdee2 !important;
}
/*鑹茬郴鏀圭増*/
/*aipms*/
.data-select,
.data-select.ivu-select:focus,
.data-select .ivu-select-selection:hover,
.data-select.ivu-select:focus .ivu-select-selection,
.data-select.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.data-select.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  border-color: #3883e5 !important;
}
.data-select .ivu-select-selection:hover,
.data-select .ivu-select-selection:focus {
  border: 1px solid #3883e5 !important;
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
.ivu-select-visible .ivu-select-selection {
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
.date-select .ivu-input:hover,
.date-select .ivu-input:focus,
.flow-window .ivu-input:hover,
.flow-window .ivu-input:focus {
  border: 1px solid #3883e5 !important;
  box-shadow: 0 0 0 2px rgba(56, 131, 229, 0.2) !important;
}
/*鏋舵瀯鍙婁汉鍛�*/
.page_tab span.active {
  border-bottom: 2px solid #3883e5;
  font-weight: 800;
  color: #3883e5;
}
.trend_warp > div .trend_title {
  position: absolute;
  left: 12px;
  border-left: 3px solid #3883e5;
  line-height: 16px;
  padding-left: 4px;
}
/*鏋舵瀯鍙婁汉鍛� end*/
/*鏂囨。搴�*/
.uploadFolder:hover {
  border: 1px solid #3883e5;
  color: #3883e5;
}
/*鏂囨。搴� end*/
/*绯荤粺浣跨敤鍒嗘瀽*/
.el-input.is-active .el-input__inner,
.el-input__inner:focus,
.el-input__inner:hover,
.el-input-number__decrease:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled),
.el-input-number__increase:hover:not(.is-disabled) ~ .el-input .el-input__inner:not(.is-disabled) {
  border-color: #3883e5 !important;
}
/*绯荤粺浣跨敤鍒嗘瀽 end*/
/*缂洪櫡瓒嬪娍鎶ヨ〃*/
.ivu-card-body .changebutton .buttonB {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 16px;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  background: #eee;
  color: #979797;
  /* box-shadow: inset 0 1px 5px #bcbcbc; */
}
.ivu-card-body .changebutton_title i {
  height: 12px;
  border-right: 1px solid #dddee1;
  margin: 4px 8px;
}
.ivu-card-body .changebutton_title span:not(.active) {
  font-size: 12px;
  color: #999999;
}
.ivu-card-body .changebutton > div > span.buttonA,
.ivu-card-body .changebutton > div > span.buttonB {
  background: #eee;
  color: #979797;
  cursor: pointer;
}
.ivu-card-body .changebutton .buttonA {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 16px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  background: #3883e5;
  /* box-shadow: 0 1px 10px #bcbcbc; */
  color: #fff;
}
.ivu-card-body .changebutton > div > span.active {
  background: #3883e5;
  color: #fff;
}
/*缂洪櫡瓒嬪娍鎶ヨ〃 end*/
/*aipms end*/
/*閮ㄩ棬绠＄悊*/
.myApprove .approve-type .selectedClass {
  color: #3883e5;
  border-bottom: 1px solid #3883e5;
}
.testApprove .approve-type .selectedClass {
  color: #3883e5;
}
.testApprove .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.testApprove .ivu-checkbox-checked:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.testApprove .ivu-checkbox-focus {
  box-shadow: 0 0 0 2px #3883e5;
}
.individualEnroll .ivu-radio-checked .ivu-radio-inner {
  border-color: #3883e5;
}
.individualEnroll .ivu-radio-inner:after {
  background-color: #3883e5;
}
.individualEnroll .ivu-radio-checked:hover .ivu-radio-inner {
  border-color: #3883e5;
}
.individualEnroll .ivu-radio-focus {
  box-shadow: 0 0 0 2px #3883e5;
}
/*閮ㄩ棬绠＄悊 end*/
/*杩愮淮绠＄悊*/
.prjGantt .scale-button {
  display: flex;
  align-items: center;
  color: #3883e5;
  font-size: 14px;
}
.prjGantt .scale-button:hover {
  color: #3abcff;
}
/*杩愮淮绠＄悊 end*/
/*鏂扮増DMP*/
/*鎸傛帴瀵煎叆妯℃澘*/
.importTempalte .hookImport .select {
  color: #3883e5;
  font-weight: bold;
}
.importTempalte .custome-box ul li.selected .custome-content .border {
  background: #fffcf0;
  border: 1px solid #3883e5;
}
/*涓氬姟瑙嗗浘*/
.bizView {
  /*涓氬姟瑙嗗浘-榛樿灞曞紑-涓嬫媺*/
  /*娴佺▼鍥句笅椤电*/
  /*闇�姹�&BUG鎬讳綋瑙嗗浘*/
  /*浠诲姟鍒嗘瀽缁熻*/
  /*浜哄憳浠诲姟鍒嗘瀽*/
  /*缂洪櫡瓒嬪娍鎶ヨ〃*/
}
.bizView .state-btn:hover {
  color: #3883e5;
}
.bizView .directionIcon:hover .ivu-icon-arrow-down-b {
  color: #3883e5 !important;
}
.bizView .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab-active {
  color: #3883e5;
  border-bottom: 2px solid #3883e5 !important;
}
.bizView .ivu-tabs.ivu-tabs-card > .ivu-tabs-bar .ivu-tabs-tab:hover {
  color: #3883e5;
}
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked:first-child,
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked,
.bizView .demandBugOverview .ivu-radio-checked .ivu-radio-inner {
  border-color: #3883e5;
}
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked {
  color: #3883e5;
  box-shadow: -1px 0 0 0 #3883e5;
}
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked:hover,
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked:active,
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked:focus {
  border-color: #3883e5;
  color: #3883e5;
}
.bizView .demandBugOverview .ivu-radio-group-button .ivu-radio-wrapper-checked:before {
  background: #3883e5;
}
.bizView .demandBugOverview .includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.bizView .demandBugOverview .includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.bizView .taskAnalyReport .ivu-input-number:hover {
  border-color: #3883e5;
  box-shadow: 0 0 0 0;
}
.bizView .taskAnalyReport .ivu-tabs-nav .ivu-tabs-tab-active {
  color: #3883e5;
}
.bizView .taskAnalyReport .ivu-tabs-ink-bar {
  background-color: #3883e5;
}
.bizView .taskAnalyReport .ivu-tabs-nav .ivu-tabs-tab:hover {
  color: #3883e5;
}
.bizView .prjTeamAnaly .parentBizClass .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.bizView .prjTeamAnaly .parentBizClass .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.bizView .prjTeamAnaly .includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.bizView .prjTeamAnaly .includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked:first-child,
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked,
.bizView .bizDefectTrend .ivu-radio-checked .ivu-radio-inner {
  border-color: #3883e5;
}
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked {
  color: #3883e5;
  box-shadow: -1px 0 0 0 #f90;
}
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked:hover,
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked:active,
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked:focus {
  border-color: #3883e5;
  color: #3883e5;
}
.bizView .bizDefectTrend .ivu-radio-group-button .ivu-radio-wrapper-checked:before {
  background: #3883e5;
}
.bizView .bizDefectTrend .includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.bizView .bizDefectTrend .includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5;
}
.bizView .bizDefectTrend .layout-content-main .line-chart .changebutton > div > span.active {
  background: #3883e5;
  color: #fff;
}
/*鏂扮増DMP end*/
/* PMS */
.prdBudgetDashboard {
  /*椤圭洰鍩哄噯*/
  /*椤圭洰棰勭畻*/
  /*鏋舵瀯鍙婁汉鍛�*/
  /*浜哄憳鏍囩绠＄悊*/
  /*浜ょ淮鎶ュ憡*/
  /*瀹℃壒椤圭洰鎶ュ憡*/
  /*鎴愭湰鍒嗘瀽*/
}
.prdBudgetDashboard .prj-mgttypes .mgtTypeChoose.active {
  color: #3883e5;
}
.prdBudgetDashboard .project_context_title .bullet {
  content: '';
  height: 2vh;
  width: 2px;
  background: #3883e5;
  display: inline-block;
  vertical-align: sub;
  margin-right: 10px;
}
.prdBudgetDashboard .prjDeliveryImplement .flow-window .ivu-input:hover,
.prdBudgetDashboard .prjDeliveryImplement .flow-window .ivu-input:focus {
  border: 1px solid #4c67a7 !important;
  box-shadow: 0 0 0 2px rgba(255, 173, 51, 0.2) !important;
}
.prdBudgetDashboard .prjDeliveryImplement .ivu-input:hover {
  border-color: #4c67a7 !important;
}
.prdBudgetDashboard .prjBenchBudgetRpt .ckbx-style-5 input[type=checkbox]:checked + label:before {
  background: #3abcff;
}
.prdBudgetDashboard .prjBenchBudgetRpt .ckbx-style-5 input[type=checkbox]:checked + label:after {
  left: 1.2em;
  background: #3883e5;
  box-shadow: 0 2px 5px 0 rgba(56, 131, 229, 0.5);
  animation: switch-on 0.4s ease-out forwards;
}
.prdBudgetDashboard .structureAndUser .prjOrange .ivu-dropdown .ivu-btn {
  color: #fff;
  background-color: #3883e5 !important;
  border-color: #3883e5 !important;
}
.prdBudgetDashboard .structureAndUser .lineClamp2 .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5;
  background-color: #3883e5;
}
.prdBudgetDashboard .prjUserTag .stafflevel .ivu-select-selection:hover {
  border: 1px solid transparent !important;
  box-shadow: 0 0 0 0 !important;
  color: #3883e5;
}
.prdBudgetDashboard .prjApproveReport .data-select,
.prdBudgetDashboard .prjApproveReport .data-select.ivu-select:focus,
.prdBudgetDashboard .prjApproveReport .data-select .ivu-select-selection:hover,
.prdBudgetDashboard .prjApproveReport .data-select.ivu-select:focus .ivu-select-selection,
.prdBudgetDashboard .prjApproveReport .data-select.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
.prdBudgetDashboard .prjApproveReport .data-select.ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  border-color: #3883e5 !important;
}
.prdBudgetDashboard .prjApproveReport .data-select .ivu-select-selection:hover,
.prdBudgetDashboard .prjApproveReport .data-select:focus .ivu-select-selection {
  border: 1px solid #3883e5 !important;
  box-shadow: 0 0 0 2px rgba(255, 173, 51, 0.2) !important;
}
.prdBudgetDashboard .prjApproveReport .date-select .ivu-input:hover,
.prdBudgetDashboard .prjApproveReport .date-select .ivu-input:focus,
.prdBudgetDashboard .prjApproveReport .flow-window .ivu-input:hover,
.prdBudgetDashboard .prjApproveReport .flow-window .ivu-input:focus {
  border: 1px solid #3883e5;
  box-shadow: 0 0 0 2px rgba(255, 173, 51, 0.2) !important;
}
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state:focus,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state .ivu-select-selection:hover,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state:focus .ivu-select-selection,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state .ivu-select-selection .ivu-select-placeholder,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state .ivu-select-selection .ivu-select-selected-value {
  border-color: #3883e5 !important;
}
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state .ivu-select-selection:hover,
.prdBudgetDashboard .prjCostAnalysis #prjgroup-state:focus .ivu-select-selection {
  box-shadow: 0 0 0 2px rgba(255, 173, 51, 0.2) !important;
  border: 1px solid #3883e5 !important;
}
/*PMS  end*/
/*鑹茬郴鏀圭増 end*/
/* 头部开始 */
.header_menu {
  height: 60px;
  z-index: 999;
  background: -webkit-linear-gradient(left, #28B9F8, #363DFD);
  background: -o-linear-gradient(left, #28B9F8, #363DFD);
  background: -moz-linear-gradient(left, #28B9F8, #363DFD);
  background: linear-gradient(left, #28B9F8, #363DFD);
  position: relative;
}
.header_menu .img_header {
  display: flex;
  justify-content: flex-start;
  position: absolute;
  top: 0;
  right: 0;
  height: 60px;
  width: 100%;
  background: url(/03images/knowledge/newHeaderBg.png) no-repeat;
  background-size: cover;
  -webkit-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}
.header_menu .img_header .add_biz_btn {
  background-color: #22CEB7 !important;
}
.header_menu .img_header .add_biz_btn:hover {
  opacity: 0.8;
}
.header_menu .ivu-menu-horizontal .ivu-menu-item,
.header_menu .ivu-menu-horizontal .ivu-menu-submenu {
  padding: 0 12px;
}
.header_menu .ivu-menu-horizontal .ivu-dropdown-menu .ivu-menu-item {
  width: 100%;
  padding: 4px 10px;
  height: auto;
  line-height: normal;
  border: none !important;
}
.header_menu .ivu-menu-horizontal .ivu-dropdown-menu .ivu-menu-item i {
  font-size: 18px;
  margin-right: 5px;
}
.header_menu .ivu-menu-light {
  background-color: transparent;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item,
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal:after {
  height: 0;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .layout-nav > .ivu-menu-item-active,
.header_menu .ivu-menu-light.ivu-menu-horizontal .layout-nav > .ivu-menu-item:hover,
.header_menu .ivu-menu-light.ivu-menu-horizontal .layout-nav > .ivu-menu-submenu-active,
.header_menu .ivu-menu-light.ivu-menu-horizontal .layout-nav > .ivu-menu-submenu:hover {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0), rgba(200, 243, 237, 0) 13%, rgba(145, 231, 219, 0.1) 41%, rgba(34, 206, 183, 0.5));
  border-bottom: 2px solid #46ffe6;
  color: rgba(255, 255, 255, 0.7);
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item:hover {
  background: #f3f3f3;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item {
  color: #515a6e;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-menu-item:hover {
  border-bottom: none;
  color: #515a6e;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-menu-item-active {
  font-weight: normal;
  background: none;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item-active {
  color: #ffffff;
  font-weight: bold;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu.ivu-menu-item-active .ivu-menu-item {
  font-weight: normal;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-select-dropdown .ivu-menu-item-selected {
  color: #515a6e;
  background: #f5f5f5;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-item a {
  height: 100%;
  width: 100%;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
}
.header_menu .ivu-menu-light.ivu-menu-horizontal .ivu-menu-submenu .ivu-menu-item a {
  height: 100%;
  width: 100%;
  color: #515a6e;
  font-size: 14px;
}
/* 头部结束 */
.onlineExamModalClass .onlineExamModalHeader {
  display: flex;
  align-items: center;
}
.onlineExamModalClass .ivu-modal-header .onlineExamModalTitle {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  font-size: 16px;
  color: #17233d;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.onlineExamModalClass .ivu-modal-header .onlineExamModalTip {
  display: inline-block;
  margin-left: 10px;
  color: #f16643;
  font-size: 12px;
}
.onlineExamModalClass .onlineExamModalContent .ivu-checkbox-wrapper .ivu-checkbox {
  padding-right: 8px;
}
.onlineExamModalClass .onlineExamModalContent .onlineExamModalQuestion:not(:last-child) {
  padding-bottom: 16px;
}
.onlineExamModalClass .onlineExamModalContent .onlineExamModalQuestion .ivu-checkbox-group > div {
  width: 100%;
  padding-left: 16px;
  padding-top: 16px;
}
.onlineExamModalClass .onlineExamModalContent .onlineExamModalQuestion .ivu-radio-group > div {
  width: 100%;
  padding-left: 16px;
  padding-top: 16px;
}
.onlineExamModalClass .onlineExamModalContent .onlineExamModalQuestion .ivu-checkbox-group .ivu-checkbox-wrapper {
  line-height: 1.5;
}
.includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5 !important;
  background-color: #3883e5 !important;
}
.includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5 !important;
}
.includeChildCtlgs .ivu-checkbox-focus {
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2) !important;
}
.parentBizClass .ivu-checkbox-checked .ivu-checkbox-inner {
  border-color: #3883e5 !important;
  background-color: #3883e5 !important;
}
.parentBizClass .ivu-checkbox:hover .ivu-checkbox-inner {
  border-color: #3883e5 !important;
}
.parentBizClass .ivu-checkbox-focus {
  box-shadow: 0 0 0 2px rgba(45, 140, 240, 0.2) !important;
}
.data_area .no_scroll .ivu-table-overflowX {
  overflow: hidden;
}
.abp_new_table.no_hidden {
  overflow: inherit;
}
.abp_new_table.no_hidden .headClass .ivu-table-overflowX {
  overflow: inherit;
}
.abp_new_table.no_hidden .ivu-table {
  overflow: inherit;
}
.abp_new_table .ivu-table-row:hover td {
  background-color: #d5e8fc !important;
}
.abp_new_table .headClass .ivu-table-header tr th {
  border-bottom: 1px solid #dbe3eb !important;
}
.abp_new_table .headClass .ivu-table-body tr td {
  background-color: #ffffff !important;
}
.abp_new_table .headClass .ivu-table-body tr td .ivu-table-cell .ivu-input,
.abp_new_table .headClass .ivu-table-body tr td .ivu-table-cell .ivu-input-number,
.abp_new_table .headClass .ivu-table-body tr td .ivu-table-cell .ivu-select-selection,
.abp_new_table .headClass .ivu-table-body tr td .ivu-table-cell .ivu-input-number-input {
  border-color: #dcdee2;
  background-color: transparent;
}
.abp_new_table .ivu-table td,
.abp_new_table .ivu-table th {
  height: 40px;
  padding: 0;
  border-right-color: transparent;
  border-bottom-color: transparent;
}
.abp_new_table .ivu-table td:last-child,
.abp_new_table .ivu-table th:last-child {
  border-right: none;
}
.abp_new_table .ivu-table td *,
.abp_new_table .ivu-table th * {
  font-size: 12px;
}
.abp_new_table .ivu-table td .ivu-table-cell,
.abp_new_table .ivu-table th .ivu-table-cell {
  padding: 0 8px;
  width: 100%;
}
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-select-selection,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-select-selection,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number-input,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number-input {
  border-color: transparent;
  background-color: transparent;
}
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number-focused,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number-focused,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input:focus,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input:focus,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number:focus,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number:focus,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input:hover,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input:hover,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-select-selection:hover,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-select-selection:hover,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-select-selection-focused,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-select-selection-focused,
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number:hover,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number:hover {
  box-shadow: none;
  border-color: #dbe3eb;
  background-color: #ffffff;
}
.abp_new_table .ivu-table td .ivu-table-cell .ivu-input-number-handler-wrap,
.abp_new_table .ivu-table th .ivu-table-cell .ivu-input-number-handler-wrap {
  display: none;
}
.abp_new_table .ivu-table:after,
.abp_new_table .ivu-table:before {
  width: 0;
  height: 0;
}
.abp_new_table .ivu-table table {
  border: none;
}
.new_table.no_hidden {
  overflow: inherit;
}
.create_prj_set .new_table .ivu-table-body {
  overflow: inherit;
}
.dep-header-warp .ivu-select-dropdown {
  overflow: inherit;
}
.third_menu {
  position: relative;
}
.third_menu .youjiantou {
  position: absolute;
  right: 5px;
}
.third_menu .third_menu_li {
  height: 30px;
  width: 100%;
  color: #495060;
  text-align: left;
  padding: 5px 10px 0;
  opacity: 1;
  background-color: #ffffff;
}
.third_menu .third_menu_li:hover {
  background: #EFEFEF;
  color: #333333;
}
.third_menu .third_menu_ul {
  position: absolute;
  left: 105px;
  top: -5px;
  display: none;
  padding: 7px 0px 8px;
  background-color: #ffffff;
}
.third_menu:hover .third_menu_ul {
  display: inherit;
}
.dmp_quote_config_set_class .page_config_modal_head {
  font-weight: normal;
}
.dmp_quote_config_set_class .pick_prd_div {
  width: 100%;
  height: 100px;
  border: solid #bbbec4 1px;
  overflow: auto;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank {
  display: flex;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank span:first-child {
  border-radius: 5px 0 0 5px;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank span:last-child {
  border-radius: 0 5px  5px 0;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank span:not(:first-child) {
  margin-left: -1px;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank span {
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  padding: 0 12px;
  border: 1px solid #dddee1;
}
.dmp_quote_config_set_class .dmp_quote_config_set_tab .tab_blank span.active {
  color: #3883e5;
  border-color: #3883e5;
  /*color: #fff;*/
  z-index: 2;
  position: relative;
}
.dmp_quote_config_set_class .ivu-form-item {
  margin-bottom: 12px;
}
.dmp_quote_config_set_class .targetPrdSelectClass .ivu-select-dropdown {
  top: 32px !important;
}
.blank-name.empConfig {
  display: flex;
  align-items: baseline;
}
.blank-name.empConfig .tab_blank {
  margin-left: auto;
  display: flex;
}
.blank-name.empConfig .tab_blank span {
  text-align: center;
  line-height: 30px;
  cursor: pointer;
  padding: 0 12px;
  border: 1px solid #dddee1;
}
.blank-name.empConfig .tab_blank span:not(:first-child) {
  margin-left: -1px;
}
.blank-name.empConfig .tab_blank span:last-child {
  border-radius: 0 5px 5px 0;
}
.blank-name.empConfig .tab_blank span.active {
  color: #3883e5;
  border-color: #3883e5;
  /* color: #fff; */
  z-index: 2;
  position: relative;
}
.outer_account_mgt_wrap {
  margin: 16px;
  box-shadow: 0 2px 12px rgba(144, 144, 144, 0.2);
  height: calc(100% - 72px);
}
.outer_account_mgt_wrap .filter_cond_wrap {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 16px;
}
.outer_account_mgt_wrap .filter_cond_wrap .filter_col {
  display: flex;
  align-items: center;
}
.outer_account_mgt_wrap .filter_cond_wrap .view_button {
  display: inline-block;
  text-align: right;
}
.outer_account_mgt_wrap .filter_cond_wrap .view_button .ivu-btn:not(:first-child) {
  margin-left: 10px;
}
.outer_account_mgt_wrap .filter_cond_wrap > div {
  width: 20%;
}
.outer_account_mgt_wrap .filter_cond_wrap > div > label {
  width: 4em;
  display: inline-block;
}
.outer_account_mgt_wrap .filter_cond_wrap > div > div {
  display: inline-block;
  width: calc(100% - 4em - 12px);
}
.outer_account_mgt_wrap .data_area {
  padding: 0 16px;
}
.outer_account_mgt_wrap .data_area .lineClamp1 .ivu-tooltip-rel {
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.bizFileUploadTips .ivu-tooltip-inner {
  white-space: normal;
}
.bizAttachFileWrapClass .bizAttachFileListWrapClass .bizAttachFileListClass:first-child {
  margin-top: 8px;
  line-height: 12px;
}
.bizFileUploadModalClass .ivu-modal-body {
  padding: 16px 40px;
}
.bizFileUploadModalClass .bizFileUploadModalLabel {
  display: flex;
  align-items: center;
  position: relative;
}
.bizFileUploadModalClass .bizFileUploadModalLabel .bizFileUploadModalRequired {
  color: #ed3f14;
  font-size: 16px;
  display: flex;
  position: absolute;
  top: 4px;
  left: -10px;
}
.bizFileUploadModalClass .bizFileUploadModalLabel .bizFileUploadModalTitle {
  font-size: 14px;
  color: #1c2438;
  padding-right: 8px;
}
.bizFileUploadModalClass .bizFileUploadModalLabel .ivu-tooltip i {
  color: #3883e5;
}
.bizFileUploadModalClass .ivu-form-item-label:before {
  display: none !important;
}
.bizFileUploadModalClass .ivu-form-item-content {
  position: initial;
}
.bizFileUploadModalClass .ivu-form-item-content .ivu-upload .ivu-btn {
  background-color: #fff;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass {
  margin-left: 120px;
  display: flex;
  margin-top: 12px;
  align-items: center;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass .fileListClass {
  padding: 6px 12px;
  position: relative;
  background-color: #F2F2F2;
  /*border: 1px solid #dddee1;*/
  border-radius: 4px;
  min-width: 150px;
  width: auto;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass .fileListClass .ivu-icon-ios-close {
  position: absolute;
  right: 4px;
  font-size: 20px;
  top: 0px;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass .fileListClass .fileNameClass {
  color: #495060;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass .ivu-icon-checkmark-circled {
  font-size: 16px;
  margin-left: 8px;
  color: #19be6b;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass .fileErrorMsg {
  font-size: 14px;
  margin-left: 8px;
  color: #ed3f14;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass.error .fileListClass {
  border: 2px dashed #ed3f14;
  background-color: #fff;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass.error .fileListClass .ivu-icon-ios-close {
  color: #ed3f14;
}
.bizFileUploadModalClass .ivu-form-item-content .fileListWrapClass.error .fileListClass .fileNameClass {
  color: #ed3f14;
}
.standardFlow {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid #65CE7A;
  text-align: center;
}
.standardFlow .standardFlowText {
  display: inline-block;
  width: 30px;
  color: #65CE7A;
  font-size: 12px;
  transform: rotate(50deg);
}
.standardPage {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  border: 1px solid #65CE7A;
  text-align: center;
}
.standardPage .standardPageText {
  display: inline-block;
  width: 24px;
  color: #65CE7A;
  font-size: 12px;
  transform: rotate(50deg);
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap {
  margin-top: 10px;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .filterCondWrap {
  display: flex;
  width: 100%;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .filterCondWrap .filterCond {
  width: 50%;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .filterCondWrap .filterCond label {
  width: 6em;
  display: inline-block;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .filterCondWrap .filterCond > div {
  width: calc(100% - 6em);
  display: inline-block;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .filterCondWrap .filterCond:first-child {
  margin-right: 8px;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .selectedPrdCtlgWrap {
  display: flex;
  width: 100%;
  margin-top: 10px;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .selectedPrdCtlgWrap label {
  width: 6em;
  display: inline-block;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .selectedPrdCtlgWrap .selectedPrdCtlgCalss {
  display: inline-block;
  width: calc(100% - 6em);
  min-height: 250px;
  border-radius: 4px;
  border: 1px solid #dddee1;
  padding: 12px 0 0px 12px;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .selectedPrdCtlgWrap .selectedPrdCtlgCalss .ctlgWrap .ctlgOpt {
  margin-bottom: 8px;
  display: flex;
}
.issuedModalWrap .issuedUnusedPrdAndCtlgWrap .selectedPrdCtlgWrap .selectedPrdCtlgCalss .ctlgWrap .ctlgOpt > i {
  font-size: 18px;
  margin-left: 6px;
}
/*菜单业务关键字搜索 */
.search_tasks {
  width: 200px;
  float: right;
  margin-top: 14px;
  margin-right: 24px;
  color: #fff;
  font-size: 16px;
}
.search_tasks .lks_menu_tasks_search .ivu-input-wrapper {
  display: flex;
  position: relative;
}
.search_tasks .lks_menu_tasks_search .ivu-input-wrapper input {
  color: #fff;
  font-size: 12px;
  line-height: 22px;
  padding-left: 12px;
  border: 1px solid #254cb7;
  opacity: 0.8;
  background: #254cb7;
}
.search_tasks .lks_menu_tasks_search .ivu-input-wrapper .ivu-input-prefix {
  position: absolute;
  top: 7px;
  right: 12px;
}
.search_tasks .lks_menu_tasks_search .tasks_search_input .ivu-input-prefix i {
  color: #fff;
  font-size: 14px;
  opacity: 0.8;
  background: #254cb7;
}
.tasks_search_modal .ivu-modal {
  width: 100% !important;
  height: calc(100% - 62px);
  top: 61px;
}
.tasks_search_modal .ivu-modal .ivu-modal-content {
  width: 100%;
  height: 100%;
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body {
  width: 100%;
  height: calc(100% - 50px);
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body {
  width: 100%;
  height: calc(100% - 34px);
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table {
  width: 100%;
  height: 100%;
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table .ivu-table .ivu-table-body {
  width: 100% !important;
  height: calc(100% - 50px);
  overflow: auto !important;
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table .ivu-table .ivu-table-body table .ivu-table-body {
  width: 100%;
  height: calc(100% - 0px);
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table .ivu-table .ivu-table-header {
  width: 100% !important;
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table .ivu-table td {
  height: 48px !important;
}
.tasks_search_modal .ivu-modal .ivu-modal-content .ivu-modal-body .table_body .menu_prdkeywords_table > .ivu-spin-fix {
  height: calc(100% - 20px) !important;
  width: calc(100% - 0px) !important;
}
.custcols-ico-warp {
  display: flex;
}
.custcols-ico-warp i {
  color: #A5ACC4;
  margin-right: 8px;
  cursor: pointer;
}
.custcols-ico-warp .icon-sonwflake.colSelect {
  color: #3883e5;
}
.report_body_height.isFullScreen .data_area {
  position: fixed;
  z-index: 999;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: 0;
  height: 100% !important;
}
.prefixInputNoBorderClass input {
  border: none;
}
.prefixInputNoBorderClass input:focus {
  outline: none;
  box-shadow: none !important;
}
.prefixInputNoBorderClass .ivu-input:focus {
  outline: none;
  box-shadow: none !important;
}
.selectOneLine .ivu-select-selection {
  max-height: 32px;
  overflow: auto;
}
.popupPrjTipContents .ivu-tooltip-inner {
  white-space: normal;
}
.popupPrjTips .ivu-tooltip-rel {
  word-break: break-all;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
