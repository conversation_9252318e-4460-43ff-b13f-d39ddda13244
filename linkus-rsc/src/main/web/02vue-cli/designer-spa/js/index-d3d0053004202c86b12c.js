/*! For license information please see index-d3d0053004202c86b12c.js.LICENSE.txt */
(()=>{var __webpack_modules__={216:function(module,__unused_webpack_exports,__webpack_require__){var e;e=(__WEBPACK_EXTERNAL_MODULE__8156__,__WEBPACK_EXTERNAL_MODULE__7111__)=>(()=>{"use strict";var __webpack_modules__={7545:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(8156),o=n.n(r),i=n(7929),a=n(1962);function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const c=function(e){var t,n=e.children,c=e.className,u=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(!1))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return l(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),s=u[0],p=u[1];return o().createElement("div",{className:"".concat(i.Z.collapse," ").concat(null!=c?c:"")},o().createElement("div",{className:i.Z.icon},o().createElement(a.Z,{name:"up",style:{transform:s?"rotate(180deg)":"rotate(0)"},onClick:function(){return p((function(e){return!e}))}})),o().createElement("div",{className:s?i.Z.collapsed:""},n))}},733:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(3724);const a=function(e){var t=e.onClick,n=e.children;return o().createElement("div",{className:i.Z.mask,onClick:function(e){e.target===e.currentTarget&&(null==t||t()),e.stopPropagation()}},n)}},5966:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(9480);const a=function(e){var t=e.children;return o().createElement("div",{className:i.Z.wrap},t)}},6034:(e,t,n)=>{n.d(t,{Z:()=>d});var r=n(8156),o=n.n(r),i=n(733),a=n(5966),l=n(1962),c=n(9740),u=n(7545),s=n(7033),p=function(e){var t=e.open,n=e.title,p=void 0===n?"Dialog":n,d=e.width,f=void 0===d?1024:d,h=e.children,m=e.footer,g=e.draggable,v=e.inside,y=e.maskClosable,_=e.contentClassName,b=e.onClose,x=(0,r.useMemo)((function(){var e=o().createElement("div",{className:s.Z.dialog,style:{width:f}},o().createElement("div",{"data-handler":!0,className:s.Z["dialog-header"]},p),o().createElement("div",{className:"".concat(s.Z["dialog-content"]," ").concat(null!=_?_:"")},h),o().createElement(l.Z,{name:"close",className:s.Z.close,onClick:b}),m&&o().createElement(u.Z,null,m));return g?o().createElement(c.Z,{inside:v},e):e}),[g,v]);return t?o().createElement(i.Z,{onClick:y?b:function(){}},o().createElement(a.Z,null,x)):null};p.Icon=l.Z;const d=p},9740:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(8156);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach((function(t){var r,i,a,l;r=e,i=t,a=n[t],l=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(i),(i="symbol"==o(l)?l:String(l))in r?Object.defineProperty(r,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[i]=a})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const c=function(e){var t,n=e.inside,o=e.children,i=o.props.children,c=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)())||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return l(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),u=c[0],s=c[1],p=(0,r.useRef)(null),d=(0,r.useRef)([0,0]),f=(0,r.useRef)([0,0]),h=(0,r.useRef)(),m=(0,r.useCallback)((function(e){var t,n;d.current=[e.clientX,e.clientY];var r=window.getComputedStyle(p.current),o=r.transform,i=r.top,a=r.bottom,l=r.left,c=r.right,u=null===(t=p.current)||void 0===t||null===(t=t.parentElement)||void 0===t?void 0:t.getBoundingClientRect(),s=null===(n=p.current)||void 0===n?void 0:n.getBoundingClientRect();if(u&&s&&(h.current=[-parseFloat(i),((null==u?void 0:u.width)-(null==s?void 0:s.width))/2-parseFloat(c),(null==u?void 0:u.height)-(null==s?void 0:s.height)+parseFloat(a),-(((null==u?void 0:u.width)-(null==s?void 0:s.width))/2-parseFloat(l))]),o&&"none"!==o){var m=null==o?void 0:o.split(",");f.current=[parseFloat(m[4]),parseFloat(m[5])]}document.addEventListener("mousemove",g),document.addEventListener("mouseup",v),e.preventDefault()}),[]),g=(0,r.useCallback)((function(e){var t=e.clientX-d.current[0],r=e.clientY-d.current[1];t=f.current[0]+t,r=f.current[1]+r,n&&h.current&&(t<h.current[3]&&(t=h.current[3]),t>h.current[1]&&(t=h.current[1]),r<h.current[0]&&(r=h.current[0]),r>h.current[2]&&(r=h.current[2])),requestAnimationFrame((function(){return s("translate(".concat(t,"px, ").concat(r,"px)"))}))}),[n]),v=(0,r.useCallback)((function(){document.removeEventListener("mousemove",g),document.removeEventListener("mouseup",v)}),[]),y=(0,r.useCallback)((function(e){for(var t=0;t<e.length;t++){var n=e[t];if((0,r.isValidElement)(n)){if("data-handler"in n.props)return void(e[t]=(0,r.cloneElement)(n,a(a({},n.props),{},{onMouseDown:m,style:a(a({},n.props.style),{},{cursor:"move"})})));n.props.children&&y(n.props.children)}}}),[]),_=(0,r.useMemo)((function(){var e=r.Children.toArray(i);return y(e),e}),[i]);return(0,r.cloneElement)(o,a(a({},o.props),{},{style:a(a({},o.props.style),{},{transform:u}),children:_,ref:p}))}},1962:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(5235),a={close:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"3185",width:"64",height:"64"},o().createElement("path",{d:"M512 128C300.8 128 128 300.8 128 512s172.8 384 384 384 384-172.8 384-384S723.2 128 512 128zM672 627.2c12.8 12.8 12.8 32 0 44.8s-32 12.8-44.8 0L512 556.8l-115.2 115.2c-12.8 12.8-32 12.8-44.8 0s-12.8-32 0-44.8L467.2 512 352 396.8C339.2 384 339.2 364.8 352 352s32-12.8 44.8 0L512 467.2l115.2-115.2c12.8-12.8 32-12.8 44.8 0s12.8 32 0 44.8L556.8 512 672 627.2z","p-id":"3186",fill:"#8a8a8a"})),plus:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5072",width:"32",height:"32"},o().createElement("path",{d:"M288 928h-96c-52.8 0-96-43.2-96-96v-96c0-17.6 14.4-32 32-32s32 14.4 32 32v96c0 17.6 14.4 32 32 32h96c17.6 0 32 14.4 32 32s-14.4 32-32 32z m544 0h-96c-17.6 0-32-14.4-32-32s14.4-32 32-32h96c17.6 0 32-14.4 32-32v-96c0-17.6 14.4-32 32-32s32 14.4 32 32v96c0 52.8-43.2 96-96 96z m64-608c-17.6 0-32-14.4-32-32v-96c0-17.6-14.4-32-32-32h-96c-17.6 0-32-14.4-32-32s14.4-32 32-32h96c52.8 0 96 43.2 96 96v96c0 17.6-14.4 32-32 32z m-768 0c-17.6 0-32-14.4-32-32v-96c0-52.8 43.2-96 96-96h96c17.6 0 32 14.4 32 32s-14.4 32-32 32h-96c-17.6 0-32 14.4-32 32v96c0 17.6-14.4 32-32 32z m544 448H352c-52.8 0-96-43.2-96-96V352c0-52.8 43.2-96 96-96h320c52.8 0 96 43.2 96 96v320c0 52.8-43.2 96-96 96zM352 320c-17.6 0-32 14.4-32 32v320c0 17.6 14.4 32 32 32h320c17.6 0 32-14.4 32-32V352c0-17.6-14.4-32-32-32H352z",fill:"#8a8a8a","p-id":"5073"})),format:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"5685",width:"32",height:"32"},o().createElement("path",{d:"M801.450667 103.082667H222.549333c-65.877333 0-119.466667 53.589333-119.466666 119.466666v578.901334c0 65.877333 53.589333 119.466667 119.466666 119.466666h578.901334c65.877333 0 119.466667-53.589333 119.466666-119.466666V222.549333c0-65.877333-53.589333-119.466667-119.466666-119.466666z m51.2 698.368c0 28.16-23.04 51.2-51.2 51.2H222.549333c-28.16 0-51.2-23.04-51.2-51.2V222.549333c0-28.16 23.04-51.2 51.2-51.2h578.901334c28.16 0 51.2 23.04 51.2 51.2v578.901334z","p-id":"5686",fill:"#8a8a8a"}),o().createElement("path",{d:"M552.96 330.24c-18.261333-4.949333-36.864 5.973333-41.813333 24.234667L431.445333 651.946667c-4.949333 18.261333 5.973333 36.864 24.234667 41.813333 2.901333 0.853333 5.973333 1.194667 8.874667 1.194667 15.018667 0 28.842667-10.069333 32.938666-25.258667l79.701334-297.642667a34.304 34.304 0 0 0-24.234667-41.813333zM399.872 388.608c-13.312-13.312-34.986667-13.312-48.298667 0l-99.328 99.328a33.8432 33.8432 0 0 0 0 48.128l99.328 99.328c6.656 6.656 15.36 10.069333 24.064 10.069333s17.408-3.413333 24.064-10.069333c13.312-13.312 13.312-34.986667 0-48.298667L324.778667 512l75.264-75.264a34.133333 34.133333 0 0 0-0.170667-48.128zM672.426667 388.608c-13.312-13.312-34.986667-13.312-48.298667 0-13.312 13.312-13.312 34.986667 0 48.298667l75.093333 75.093333-75.264 75.264a34.2016 34.2016 0 0 0 24.064 58.368c8.704 0 17.408-3.413333 24.064-10.069333l99.328-99.328a33.8432 33.8432 0 0 0 0-48.128l-98.986666-99.498667z","p-id":"5687",fill:"#8a8a8a"})),preview:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"12967",width:"32",height:"32"},o().createElement("path",{d:"M668.7 150.3c-14.3 0-25.9-11.6-25.9-25.9s11.6-25.9 25.9-25.9h151.4c59.6 0 107.9 48.2 107.9 107.8v151.4c0 14.3-11.6 25.9-25.9 25.9s-25.9-11.6-25.9-25.9V206.3c0-30.9-25-56-56-56H668.7z m207.4 506.3c-0.6-14.3 10.4-26.4 24.7-27.1 14.3-0.6 26.4 10.4 27.1 24.7V820.3c0 59.6-48.2 107.8-107.8 107.8H717.8c-14.3 0-25.9-11.6-25.9-25.9s11.6-25.9 25.9-25.9h102.4c30.9 0 56-25 56-56V656.6zM369.8 876.4c14.3 0 25.9 11.6 25.9 25.9s-11.6 25.9-25.9 25.9H206c-59.5 0-107.8-48.3-107.8-107.8V668.9c0-14.3 11.6-25.9 25.9-25.9s25.9 11.6 25.9 25.9v151.4c0 30.9 25 56 56 56h163.8v0.1zM150.1 357.8c0 14.3-11.6 25.9-25.9 25.9s-25.9-11.6-25.9-25.9V206.3c0-59.7 48.2-107.9 107.8-107.9h151.4c14.3 0 25.9 11.6 25.9 25.9s-11.6 25.9-25.9 25.9H206.1c-30.9 0-56 25-56 56v151.6z m363 285.2c88.8 0 171.5-46.1 249.8-142.6-78.3-96.5-161-142.6-249.8-142.6-88.8 0-171.5 46.2-249.9 142.6C341.6 596.8 424.3 643 513.1 643z m0 51.8c-105.8 0-202.6-53.9-290.1-161.8-15.4-19-15.4-46.3 0-65.3 87.5-107.9 184.3-161.8 290.1-161.8 105.8 0 202.5 53.9 290.1 161.8 15.4 19 15.4 46.3 0 65.3-87.6 107.9-184.3 161.8-290.1 161.8z","p-id":"12968",fill:"#8a8a8a"}),o().createElement("path",{d:"M513.1 565.2c35.8 0 64.8-29 64.8-64.8s-29-64.8-64.8-64.8-64.8 29-64.8 64.8 29 64.8 64.8 64.8z m0 51.9c-64.4 0-116.7-52.2-116.7-116.7s52.2-116.7 116.7-116.7 116.7 52.2 116.7 116.7-52.2 116.7-116.7 116.7z","p-id":"12969",fill:"#8a8a8a"})),up:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2321",width:"32",height:"32"},o().createElement("path",{d:"M512.146286 454.753524l-266.849524 266.727619L193.584762 669.744762l318.585905-318.415238 318.268952 318.415238-51.736381 51.687619z","p-id":"2322",fill:"#8a8a8a"})),down:o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"3383",width:"32",height:"32"},o().createElement("path",{d:"M512.146286 619.52L245.296762 352.792381 193.584762 404.48l318.585905 318.415238 318.268952-318.415238-51.736381-51.687619z","p-id":"3384",fill:"#8a8a8a"}))};const l=function(e){var t=e.name,n=e.className,r=e.style,l=e.onClick;return o().createElement("span",{style:r,className:"".concat(i.Z.icon," ").concat(n),onClick:l},a[t])}},5971:(e,t,n)=>{n.d(t,{Z:()=>r});const r=n(6034).Z},4479:(e,t,n)=>{function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function a(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function l(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e)){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw i}}return n}}(e,t)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,{Kd:()=>a,ZQ:()=>l,Zj:()=>i})},2515:(e,t,n)=>{n.d(t,{Z:()=>r});const r={paths:{vs:"https://cdn.jsdelivr.net/npm/monaco-editor@0.43.0/min/vs"}}},7521:(e,t,n)=>{n.d(t,{Z:()=>r.Z});var r=n(5709)},5709:(e,t,n)=>{n.d(t,{Z:()=>_});var r=n(4479),o=n(6237),i=n(2515),a=n(6769),l=n(212),c=n(8756),u=n(9315),s=o.Z.create({config:i.Z,isInitialized:!1,resolve:null,reject:null,monaco:null}),p=(0,r.ZQ)(s,2),d=p[0],f=p[1];function h(e){return document.body.appendChild(e)}function m(e){var t,n,r=d((function(e){return{config:e.config,reject:e.reject}})),o=(t="".concat(r.config.paths.vs,"/loader.js"),n=document.createElement("script"),t&&(n.src=t),n);return o.onload=function(){return e()},o.onerror=r.reject,o}function g(){var e=d((function(e){return{config:e.config,resolve:e.resolve,reject:e.reject}})),t=window.require;t.config(e.config),t(["vs/editor/editor.main"],(function(t){v(t),e.resolve(t)}),(function(t){e.reject(t)}))}function v(e){d().monaco||f({monaco:e})}var y=new Promise((function(e,t){return f({resolve:e,reject:t})}));const _={config:function(e){var t=a.ZP.config(e),n=t.monaco,o=(0,r.Kd)(t,["monaco"]);f((function(e){return{config:(0,c.Z)(e.config,o),monaco:n}}))},init:function(){var e=d((function(e){return{monaco:e.monaco,isInitialized:e.isInitialized,resolve:e.resolve}}));if(!e.isInitialized){if(f({isInitialized:!0}),e.monaco)return e.resolve(e.monaco),(0,u.Z)(y);if(window.monaco&&window.monaco.editor)return v(window.monaco),e.resolve(window.monaco),(0,u.Z)(y);(0,l.Z)(h,m)(g)}return(0,u.Z)(y)},__getMonacoInstance:function(){return d((function(e){return e.monaco}))}}},212:(e,t,n)=>{n.d(t,{Z:()=>r});const r=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}},3431:(e,t,n)=>{n.d(t,{Z:()=>r});const r=function(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(o,r))}}}},8756:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(4479);const o=function e(t,n){return Object.keys(n).forEach((function(r){n[r]instanceof Object&&t[r]&&Object.assign(n[r],e(t[r],n[r]))})),(0,r.Zj)((0,r.Zj)({},t),n)}},1742:(e,t,n)=>{n.d(t,{Z:()=>r});const r=function(e){return{}.toString.call(e).includes("Object")}},9315:(e,t,n)=>{n.d(t,{Z:()=>o});var r={type:"cancelation",msg:"operation is manually canceled"};const o=function(e){var t=!1,n=new Promise((function(n,o){e.then((function(e){return t?o(r):n(e)})),e.catch(o)}));return n.cancel=function(){return t=!0},n}},6769:(e,t,n)=>{n.d(t,{ZP:()=>l});var r=n(3431),o=n(1742),i={configIsRequired:"the configuration object is required",configType:"the configuration object should be an object",default:"an unknown error accured in `@monaco-editor/loader` package",deprecation:"Deprecation warning!\n    You are using deprecated way of configuration.\n\n    Instead of using\n      monaco.config({ urls: { monacoBase: '...' } })\n    use\n      monaco.config({ paths: { vs: '...' } })\n\n    For more please check the link https://github.com/suren-atoyan/monaco-loader#config\n  "},a=(0,r.Z)((function(e,t){throw new Error(e[t]||e.default)}))(i);const l={config:function(e){return e||a("configIsRequired"),(0,o.Z)(e)||a("configType"),e.urls?(console.warn(i.deprecation),{paths:{vs:e.urls.monacoBase}}):e}}},1736:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t,n){var o;return o=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==r(o)?o:String(o))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{SQ:()=>i,o:()=>u,uU:()=>l});var i=function(e){return e.Javascript="javascript",e.Typescript="typescript",e}({}),a=o(o({},i.Javascript,(function(e){e.languages.typescript.javascriptDefaults.setDiagnosticsOptions({noSemanticValidation:!1,noSyntaxValidation:!1}),e.languages.typescript.javascriptDefaults.setCompilerOptions({target:e.languages.typescript.ScriptTarget.ES6,allowNonTsExtensions:!0,noImplicitAny:!0,strict:!0})})),i.Typescript,(function(e,t){e.languages.typescript.typescriptDefaults.setDiagnosticsOptions({noSemanticValidation:!!t||!1,noSyntaxValidation:!1}),e.languages.typescript.typescriptDefaults.setCompilerOptions({target:e.languages.typescript.ScriptTarget.ES6,allowNonTsExtensions:!0,noImplicitAny:!1,strict:!1,jsx:e.languages.typescript.JsxEmit.ReactJsx,lib:["es2020","dom","DOM.Iterable"],module:"ESNext",skipLibCheck:!0,esModuleInterop:!0,noEmit:!0,jsxFactory:"React.createElement",reactNamespace:"React"})})),l=function(e){var t;return null!==(t=a[e])&&void 0!==t?t:function(){}},c=o(o({},i.Javascript,(function(){return{presets:["env"],comments:!1}})),i.Typescript,(function(e){return e?{presets:["env","react"],parserOpts:{strictMode:!1},plugins:[["proposal-decorators",{legacy:!0}],"proposal-class-properties",["transform-typescript",{isTSX:!0}]]}:{presets:["env","typescript"],parserOpts:{strictMode:!1},comments:!1,filename:"types.d.ts"}})),u=function(e){var t;return null!==(t=c[e])&&void 0!==t?t:function(){}}},5457:(e,t,n)=>{n.d(t,{b:()=>o});var r=n(1036),o=function(e,t){var n=new r.t((0,r.S)(),t).highlighterBuilder({editor:e}),o=n.highlighter,i=n.dispose;return o(),e.onDidChangeModelContent((function(){o()})),i}},7085:(e,t,n)=>{n.d(t,{X:()=>b});var r=n(8156),o=n.n(r),i=n(73),a=n(1736),l=n(5457),c=n(7015),u=n(1157),s=n(22),p=n(692);function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function f(){return f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}function h(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e,t,n){var r;return r=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==d(r)?r:String(r))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function g(){g=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,a=Object.create(i.prototype),l=new N(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",h="suspendedYield",m="executing",v="completed",y={};function _(){}function b(){}function x(){}var w={};u(w,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&r.call(O,a)&&(w=O);var S=x.prototype=_.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(o,i,a,l){var c=p(e[o],e,i);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==d(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=f;return function(i,a){if(o===m)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=m;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?v:h,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(d(t)+" is not iterable")}return b.prototype=x,o(S,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:b,configurable:!0}),b.displayName=u(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,u(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),u(j.prototype,l,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),u(S,c,"Generator"),u(S,a,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function v(e,t,n,r,o,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function y(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return _(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n(6515);var b=(0,r.forwardRef)((function(e,t){var n,d=(0,u.TS)(c.n,e),_=d.extraLib,b=d.language,x=d.defaultLanguage,w=d.isTsx,E=d.loaderConfig,O=d.eslint,S=d.theme,k=d.babel,j=null!=b?b:x,C=y((0,r.useState)(!1),2),A=C[0],Z=C[1],T=(0,r.useRef)(),N=(0,r.useRef)([]),P=(0,i.Ik)(),R=(0,r.useRef)(),L=(0,r.useRef)(null);(0,r.useImperativeHandle)(t,(function(){return{monaco:P,editor:T.current,format:function(){T.current&&T.current._actions.get("editor.action.formatDocument")._run()},compile:function(e,t){return(n=g().mark((function n(){var r,o,i,l,c,s,p;return g().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(e&&[a.SQ.Javascript,a.SQ.Typescript].includes(j)){n.next=2;break}return n.abrupt("return",e);case 2:return n.prev=2,n.next=5,(0,u.i0)(null==k?void 0:k.standalone);case 5:return i=n.sent,l=(0,a.o)(j),c=l(w),s=i.transform(e,null!==(r=null!==(o=null==k?void 0:k.options)&&void 0!==o?o:t)&&void 0!==r?r:c),p=s.code,n.abrupt("return",p);case 12:throw n.prev=12,n.t0=n.catch(2),n.t0;case 15:case"end":return n.stop()}}),n,null,[[2,12]])})),function(){var e=this,t=arguments;return new Promise((function(r,o){var i=n.apply(e,t);function a(e){v(i,r,o,a,l,"next",e)}function l(e){v(i,r,o,a,l,"throw",e)}a(void 0)}))})();var n}}}),[P,j,w,k,A]),(0,r.useLayoutEffect)((function(){E&&i._m.config(E)}),[E]),(0,r.useEffect)((function(){[a.SQ.Javascript,a.SQ.Typescript].includes(j)&&P&&((0,a.uU)(j)(P,w),_&&P.languages.typescript.typescriptDefaults.addExtraLib(_,"ts:filename/facts.d.ts"))}),[P,_,j,w]),(0,r.useEffect)((function(){if(P&&A&&w){var e=(0,l.b)(T.current,P);return function(){"function"==typeof e&&e()}}}),[P,w,A]),(0,r.useEffect)((function(){var e=L.current.nextElementSibling.querySelector(".jsx-editor"),t=p.J[S];if(e&&t)for(var n=0,r=Object.entries(t);n<r.length;n++){var o=y(r[n],2),i=o[0],a=o[1];e.style.setProperty(i,a)}}),[S]);var M=(0,r.useCallback)((function(e){if(P&&T.current&&R.current&&e&&j===a.SQ.Javascript){var t=T.current.getModel(),n=R.current.verify(e,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?h(Object(n),!0).forEach((function(t){m(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):h(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({env:{browser:!0,es6:!0},parserOptions:{ecmaVersion:2018,ecmaFeatures:{jsx:!0},sourceType:"module"}},null==O?void 0:O.config)).map((function(e){var t=e.line,n=e.column,r=e.message;return{startLineNumber:t,endLineNumber:t,startColumn:n,endColumn:n,message:"".concat(r),severity:3}}));P.editor.setModelMarkers(t,"ESlint",n)}}),[A,O,P,j]),I=(0,r.useCallback)((function(e){"function"==typeof d.onFocus&&d.onFocus(e)}),[d.onFocus]),F=(0,r.useCallback)((function(e){"function"==typeof d.onBlur&&d.onBlur(e)}),[d.onBlur]),D=(0,r.useCallback)((function(e,t){M(e),"function"==typeof d.onChange&&d.onChange(e,t)}),[d.onChange]);return o().createElement(o().Fragment,null,o().createElement("div",{"data-element-type":"themeTag",ref:L}),o().createElement(i.ML,f({},d,{onMount:function(e,t){Z(!0),e.addCommand(t.KeyMod.CtrlCmd|t.KeyCode.KeyS,(function(){var t=e.getValue();D(t,null)})),N.current=(0,s.A)(e,[{name:"onDidFocusEditorText",callback:function(){I(e)}},{name:"onDidBlurEditorText",callback:function(){F(e)}}],N.current),"function"==typeof d.onMount&&d.onMount(e,t),T.current=e,queueMicrotask((function(){(0,u.eh)(null==O?void 0:O.src).then((function(t){R.current=t,M(e.getValue())}))}))},onChange:D,className:"".concat(null!==(n=d.className)&&void 0!==n?n:""," ").concat(w?"jsx-editor":"")})))}))},692:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t,n){var o;return o=function(e,t){if("object"!=r(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==r(o)?o:String(o))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{J:()=>a});var i=function(e){return e.Dark="vs-dark",e.Light="light",e}({}),a=o(o({},i.Dark,{"--string-color":"#e37a11","--language-keyword-color":"#619ac3","--global-variable-color":"#fae56e","--local-variable-color":"#fae56e","--unused-opacity":.5,"--grammar-color":"#369b99","--jsx-tag-color":"#4595ce","--jsx-attribute-color":"#afd5f1","--jsx-text-color":"#efeeee","--jsx-tag-angle-bracket":"#888"}),i.Light,{"--string-color":"#c96705","--language-keyword-color":"#619ac3","--global-variable-color":"#934d08","--local-variable-color":"#934d08","--unused-opacity":.5,"--grammar-color":"#369b99","--jsx-tag-color":"#f27b0c","--jsx-attribute-color":"#f94f20","--jsx-text-color":"#333","--jsx-tag-angle-bracket":"#888"})},7015:(e,t,n)=>{n.d(t,{n:()=>r});var r={theme:"vs-dark",width:"100%",height:"100%",path:"file:///index.tsx",options:{automaticLayout:!0,detectIndentation:!1,formatOnType:!1,lineNumbers:"on",scrollbar:{horizontal:"hidden",vertical:"hidden",verticalScrollbarSize:0},lineNumbersMinChars:3,lineDecorationsWidth:0,minimap:{enabled:!1},fontSize:14,tabSize:2},loaderConfig:{paths:{vs:"https://f2.eckwai.com/udata/pkg/eshop/fangzhou/pub/pkg/monaco-editor/0.45.0/min/vs"}},eslint:{src:"https://f2.eckwai.com/udata/pkg/eshop/fangzhou/pub/pkg/eslint/8.15.0/eslint.js",config:{env:{browser:!0,es6:!0},parserOptions:{ecmaVersion:2018,sourceType:"module"}}}}},22:(e,t,n)=>{n.d(t,{A:()=>r});var r=function(e,t,n){return n.length&&n.forEach((function(e){return e.dispose()})),t.forEach((function(t){var r=t.name,o=t.callback;n.push(e[r](o))})),n}},748:(e,t,n)=>{n.d(t,{Z:()=>v});var r=n(8156),o=n.n(r),i=n(5971),a=n(7503),l=n(7085),c=n(1157),u=n(2911),s=n(8025),p=["value","modal","comment","toolbar","children"];function d(){return d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},d.apply(this,arguments)}function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||h(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){if(e){if("string"==typeof e)return m(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?m(e,t):void 0}}function m(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var g=i.Z.Icon;const v=(0,r.forwardRef)((function(e,t){var n=e.value,v=e.modal,y=e.comment,_=e.toolbar,b=e.children,x=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,p),w=f((0,r.useState)(!(null==v||!v.open)),2),E=w[0],O=w[1],S=f((0,r.useState)((function(){return n})),2),k=S[0],j=S[1];(0,u.P)((function(){O(!(null==v||!v.open))}),[null==v?void 0:v.open]),(0,u.P)((function(){j(n)}),[n]);var C=(0,r.useMemo)((function(){return o().createElement(l.X,d({},x,{value:k,ref:t}))}),[x,k,t]),A=(0,r.useMemo)((function(){var e,t,n,r=Math.floor(10*Math.random());return null!=y&&y.value?o().createElement(l.X,d({},x,{value:y.value,options:{readOnly:!0,lineNumbers:"off",fontSize:null===(e=x.options)||void 0===e?void 0:e.fontSize},height:null!==(t=null!==(n=y.height)&&void 0!==n?n:x.height)&&void 0!==t?t:300,path:"".concat(r,"_comment.ts")})):null}),[x,y,x.theme]),Z=(0,r.useCallback)((function(){if(t.current.editor){var e=t.current.editor.getValue();j(e)}}),[]),T=(0,r.useCallback)((function(){(0,c.BS)([Z,function(){return O(!0)}]),"function"==typeof(null==v?void 0:v.onOpen)&&v.onOpen()}),[null==v?void 0:v.onOpen]),N=(0,r.useCallback)((function(){(0,c.BS)([Z,function(){return O(!1)}]),"function"==typeof(null==v?void 0:v.onClose)&&v.onClose()}),[null==v?void 0:v.onClose]),P=(0,r.useMemo)((function(){if(!_)return null;var e,t=function(e){if(Array.isArray(e))return m(e)}(e=r.Children.toArray(_))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||h(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();return v&&!E&&t.push(o().createElement(g,{name:"plus",onClick:T})),o().createElement(a.Z,null,t)}),[v,E,_]),R=(0,r.useMemo)((function(){var e;return(0,r.isValidElement)(b)?"fit-content":null!==(e=x.height)&&void 0!==e?e:500}),[b,x.height]);return o().createElement("div",{className:s.Z.wrap,style:{height:R}},E&&o().createElement(i.Z,d({draggable:!0,contentClassName:s.Z["dialog-content"]},v,{open:E,footer:A,onClose:N}),C,P),b,!E&&!b&&o().createElement(o().Fragment,null,C,P))}))},2911:(e,t,n)=>{n.d(t,{P:()=>o});var r=n(8156),o=function(e,t){(0,r.useEffect)(e,t)}},7503:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(4824);const a=function(e){var t=e.children;return o().createElement("div",{className:i.Z.toolbar},t)}},7233:(e,t,n)=>{n.d(t,{J:()=>o,Z:()=>i});var r=n(748),o=n(5971).Z.Icon;const i=r.Z},1157:(e,t,n)=>{function r(){r=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},l=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var o=t&&t.prototype instanceof _?t:_,a=Object.create(o.prototype),l=new N(r||[]);return i(a,"_invoke",{value:C(e,n,l)}),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function _(){}function b(){}function x(){}var w={};p(w,l,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&o.call(O,l)&&(w=O);var S=x.prototype=_.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(r,i,a,l){var u=f(e[r],e,i);if("throw"!==u.type){var s=u.arg,p=s.value;return p&&"object"==c(p)&&o.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(p).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,l)}))}l(u.arg)}var r;i(this,"_invoke",{value:function(e,o){function i(){return new t((function(t,r){n(e,o,t,r)}))}return r=r?r.then(i,i):i()}})}function C(t,n,r){var o=h;return function(i,a){if(o===g)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var u=f(t,n,r);if("normal"===u.type){if(o=r.done?v:m,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[l];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function n(){for(;++r<t.length;)if(o.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(c(t)+" is not iterable")}return b.prototype=x,i(S,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:b,configurable:!0}),b.displayName=p(x,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,p(e,s,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),p(j.prototype,u,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(d(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),p(S,s,"Generator"),p(S,l,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return r("end");if(a.tryLoc<=this.prev){var c=o.call(a,"catchLoc"),u=o.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return r(a.catchLoc,!0);if(this.prev<a.finallyLoc)return r(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return r(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return r(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function o(e,t,n,r,o,i,a){try{var l=e[i](a),c=l.value}catch(e){return void n(e)}l.done?t(c):Promise.resolve(c).then(r,o)}function i(e){return function(){var t=this,n=arguments;return new Promise((function(r,i){var a=e.apply(t,n);function l(e){o(a,r,i,l,c,"next",e)}function c(e){o(a,r,i,l,c,"throw",e)}l(void 0)}))}}function a(e){return function(e){if(Array.isArray(e))return l(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e){return"[object Object]"===Object.prototype.toString.call(e)}n.d(t,{BS:()=>m,TS:()=>p,eh:()=>h,i0:()=>f});var s=function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new WeakMap;if(!u(t))throw TypeError("arguments must be Object");if(n.has(t))return n.get(t);for(var r={},o=0,i=Object.keys(t);o<i.length;o++){var l=i[o],s=t[l];"object"!==c(s)||null===s?r[l]=s:Array.isArray(s)?r[l]=a(s):s instanceof Set?r[l]=new Set(a(s)):s instanceof Map?r[l]=new Map(a(s)):(n.set(s,s),r[l]=e(s,n))}return r};function p(e,t){if(!u(e))return t;if(!u(t))return e;var n=s(e),r=s(t);return Object.keys(r).forEach((function(e){var t=n[e],o=r[e];Array.isArray(t)&&Array.isArray(o)?n[e]=t.concat(o):u(t)&&u(o)?n[e]=p(t,o):n[e]=null!=o?o:n[e]})),n}function d(e){return"function"==typeof window.define&&window.define.amd&&(console.log("%c".concat("@mybricks/coder"),"background: #FA6400;color: #fff;padding: 2px 6px;border-radius: 4px;","@0.0.34"),Reflect.deleteProperty(window.define,"amd")),new Promise((function(t,n){var r=document.createElement("script");r.src=e,r.crossOrigin="anonymous",r.onload=function(e){t(null)},r.onerror=n,document.body.appendChild(r)}))}var f=function(){var e=i(r().mark((function e(){var t,n=arguments;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=n.length>0&&void 0!==n[0]?n[0]:"https://unpkg.com/@babel/standalone/babel.min.js",window.Babel){e.next=5;break}return console.info("%c[Babel was not found in global,loading babel...]","color: orange"),e.next=5,d(t);case 5:return e.abrupt("return",window.Babel);case 6:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),h=function(){var e=i(r().mark((function e(t){var n;return r().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=2;break}return e.abrupt("return");case 2:if(window.eslint){e.next=5;break}return e.next=5,d(t);case 5:return n=window.eslint.Linter,e.abrupt("return",new n);case 7:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),m=function(e){e.reduce((function(e,t){return e.then(t)}),Promise.resolve())}},5619:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(3976);const a=function(e){var t=e.children,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["children"]);return o().createElement("button",Object.assign({className:i.Z.btn},n),o().createElement("span",null,t))}},8250:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(8156),o=n.n(r),i=n(9493),a=n(8502);function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const c=function(e){var t,n=e.children,c=e.defaultFold,u=void 0===c||c,s=e.header,p=e.headerStyle,d=e.contentStyle,f=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}(e,["children","defaultFold","header","headerStyle","contentStyle"]),h=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(u))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return l(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?l(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=h[0],g=h[1],v=(0,r.useCallback)((function(){return g((function(e){return!e}))}),[]);return o().createElement("div",Object.assign({className:a.Z.collapse},f),o().createElement("div",{className:"".concat(a.Z.header),style:p,onClick:v},o().createElement("div",{className:"".concat(a.Z.icon," ").concat(m?a.Z.fold:"")},i.Dp),s),m?null:o().createElement("div",{className:"".concat(a.Z.content),style:d},n))}},6233:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(1673);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const l=function(e){var t,n=e.dropDownStyle,l=e.children,c=e.overlay,u=e.onBlur,s=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(!1))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return a(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),p=s[0],d=s[1],f=(0,r.useCallback)((function(e){e.stopPropagation(),d((function(e){return!e}))}),[]);return(0,r.useEffect)((function(){null==u||u((function(){return d(!1)}))}),[]),o().createElement("div",{className:i.Z.dropdown},o().createElement("div",{onClick:f},l),o().createElement("div",{style:n,className:i.Z.content},p?c:null))}},6017:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(7762);const a=function(e){var t=e.label,n=e.require,r=e.contentStyle,a=e.className,l=void 0===a?"":a,c=e.children,u=e.labelTop,s=void 0!==u&&u;return o().createElement("div",{className:"".concat(i.Z.item," ").concat(s?i.Z.labelTop:""," ").concat(l)},o().createElement("label",null,n?o().createElement("i",null,"*"):null,t),o().createElement("div",{className:i.Z.content,style:r},c))}},5132:(e,t,n)=>{n.d(t,{K:()=>c,Z:()=>u});var r=n(8156),o=n.n(r),i=n(2808);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var l=function(e){var t,n=e.defaultValue,l=e.onChange,c=e.onBlur,u=e.validateError,s=void 0===u?"":u,p=e.placeholder,d=e.style,f=void 0===d?{}:d,h=e.type,m=void 0===h?"input":h,g=(0,r.useRef)(null),v=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(n))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return a(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),y=v[0],_=v[1];(0,r.useEffect)((function(){var e;s&&!y&&(null===(e=g.current)||void 0===e||e.classList.add(i.Z.error))}),[s]);var b=function(e){var t;s&&(null===(t=g.current)||void 0===t||t.classList.remove(i.Z.error)),_(e.target.value),null==l||l(e)};return o().createElement("div",{className:i.Z.input},o().createElement("div",{ref:g,className:"".concat(i.Z.editor," ").concat(i.Z.textEdt),"data-err":s},"input"===m?o().createElement("input",{defaultValue:n,value:y,placeholder:p,onBlur:c,style:f,onChange:b}):o().createElement("textarea",{defaultValue:n,placeholder:p,onChange:b,onBlur:c,style:f})))},c=function(e){return l(Object.assign(Object.assign({},e),{type:"textarea"}))};const u=l},5196:(e,t,n)=>{n.d(t,{I:()=>s});var r=n(8156),o=n.n(r),i=n(7111),a=n(9493),l=n(393),c=null,u=null,s=function(){var e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{type:"error",timeout:2e3,targetContainer:void 0};((null===(t=null===(e=document.querySelector("div[data-id=plugin-panel]"))||void 0===e?void 0:e.parentNode)||void 0===t?void 0:t.parentNode)||(null==r?void 0:r.targetContainer))&&(c&&(c=document.querySelector("div[data-id=http-plugin-panel-message]"))||(c=document.createElement("div")).setAttribute("data-id","http-plugin-panel-message"),document.body.appendChild(c),clearTimeout(u),(0,i.render)(o().createElement(p,{type:r.type,message:n}),c),u=setTimeout((function(){return(0,i.unmountComponentAtNode)(c)}),r.timeout||2e3))},p=function(e){var t=e.type,n=void 0===t?"error":t,r=e.message;return r?o().createElement("div",{className:l.Z.message},"error"===n?a.vU:null,"warning"===n?a.Kp:null,"success"===n?a.Vp:null,o().createElement("span",{className:l.Z.content},r)):null}},978:(e,t,n)=>{n.d(t,{$D:()=>b,Jg:()=>v,RH:()=>x,Rj:()=>g,V2:()=>_,gK:()=>y});var r=n(8156),o=n.n(r),i=n(7233),a=n(6017),l=n(5132),c=n(318),u=n(5525),s=n(9787);function p(e){return p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p(e)}function d(){d=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,a=Object.create(i.prototype),l=new N(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function _(){}function b(){}function x(){}var w={};u(w,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&r.call(O,a)&&(w=O);var S=x.prototype=_.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(o,i,a,l){var c=f(e[o],e,i);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==p(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=h;return function(i,a){if(o===g)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var u=f(t,n,r);if("normal"===u.type){if(o=r.done?v:m,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(p(t)+" is not iterable")}return b.prototype=x,o(S,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:b,configurable:!0}),b.displayName=u(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,u(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),u(j.prototype,l,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),u(S,c,"Generator"),u(S,a,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function l(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}c((r=r.apply(e,t||[])).next())}))},m=[{title:"GET",value:u.fq.GET},{title:"POST",value:u.fq.POST},{title:"PUT",value:u.fq.PUT},{title:"DELETE",value:u.fq.DELETE}],g=function(e){var t=e.defaultValue,n=e.onBlur,r=e.onChange,i=e.require,c=void 0!==i&&i;return o().createElement(a.Z,{label:"名称",require:c},o().createElement(l.Z,{key:"interfaceName",defaultValue:t,onBlur:n,onChange:r,placeholder:"服务接口的标题"}))},v=function(e){var t=e.defaultValue,n=e.onBlur,r=e.onChange,i=e.require,c=void 0===i||i,u=e.validateError;return o().createElement(a.Z,{label:"地址",require:c},o().createElement(l.K,{defaultValue:t,onBlur:n,key:"address",onChange:function(e){return r(e)},validateError:u,placeholder:"服务接口的地址"}))},y=function(e){var t=e.defaultValue,n=e.onChange,r=e.require,i=void 0===r||r;return o().createElement(a.Z,{label:"请求方法",require:i},o().createElement(c.Z,{options:m,defaultValue:t,onChange:function(e){null==n||n(e)}}))},_=function(e){var t,n=e.CDN,a=e.value,l=e.unique,c=e.onChange,u=e.path,p=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(!1))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return f(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=p[0],g=p[1],v=(0,r.useRef)(null),y=(0,r.useCallback)((function(){return h(void 0,void 0,void 0,d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:g(!0);case 1:case"end":return e.stop()}}),e)})))}),[]),_=(0,r.useCallback)((function(){return h(void 0,void 0,void 0,d().mark((function e(){return d().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:g(!1);case 1:case"end":return e.stop()}}),e)})))}),[]);return o().createElement("div",{className:s.Z.wrap},m?null:o().createElement("div",{className:s.Z.sidebarPanelCodeIcon},o().createElement("span",null,o().createElement(i.J,{className:s.Z.icon,name:"plus",onClick:y}))),o().createElement(i.Z,{width:"100%",ref:v,key:l,eslint:{src:null==n?void 0:n.eslint},path:u,modal:{open:m,width:1200,title:"编辑代码",inside:!0,onOpen:y,maskClosable:!0,contentClassName:s.Z.modalContent,onClose:_},babel:{standalone:null==n?void 0:n.babel},loaderConfig:{paths:null==n?void 0:n.paths},language:"javascript",theme:"light",value:a,onChange:c}))},b=function(e){var t=e.defaultValue,n=e.onBlur,r=e.require,i=void 0!==r&&r,c=e.validateError;return o().createElement(a.Z,{label:"接口描述",require:i},o().createElement(l.Z,{defaultValue:t,onBlur:function(e){n(e)},key:"desc",validateError:c,placeholder:"接口描述"}))},x=function(e){var t=e.defaultValue,n=e.onBlur,r=e.onChange,i=e.require,c=void 0!==i&&i,u=e.validateError;return o().createElement(a.Z,{label:"文档链接",require:c},o().createElement(l.K,{style:{height:80},onBlur:function(e){n(e)},onChange:r,key:"doc",validateError:u,defaultValue:t}))}},318:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(7295);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const l=function(e){var t,n=e.options,l=e.defaultValue,c=e.onChange,u=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(l))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return a(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),s=u[0],p=u[1];return(0,r.useEffect)((function(){p(l)}),[l]),o().createElement("div",{className:i.Z.edt},n.map((function(e){return o().createElement("div",{key:e.value,className:"".concat(i.Z.opt," ").concat(e.value===s?i.Z.selected:""),onClick:function(){null==c||c(e.value),p(e.value)}},e.title)})))}},957:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(1436);function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const l=function(e){var t,n=e.defaultValue,l=void 0!==n&&n,c=e.onChange,u=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(l))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return a(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),s=u[0],p=u[1],d=(0,r.useCallback)((function(){p(!s),null==c||c(!s)}),[s,c]);return o().createElement("div",{className:i.Z.ct},o().createElement("button",{onClick:d,className:"".concat(i.Z.switch," ").concat(s?i.Z.checked:"")},o().createElement("div",{className:i.Z.handle})))}},7955:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(6673),a=null;const l=function(e){var t=e.children,n=e.item,l=e.draggable,c=e.onDrop,u=e.parent,s=(0,r.useRef)(null),p=(0,r.useRef)(0),d=(0,r.useRef)(0),f=(0,r.useRef)(!0);return(0,r.useLayoutEffect)((function(){var e=s.current;p.current=e.clientHeight,e.addEventListener("dragstart",(function(e){a=n})),e.addEventListener("dragover",(function(t){if(f.current&&(d.current=e.getBoundingClientRect().y,f.current=!1),a&&t.currentTarget&&(!n||(null==a?void 0:a.id)!==n.id)){var r=t.currentTarget;r.classList.remove(i.Z.hovering,i.Z.hoverTop,i.Z.hoverBottom),r.classList.add(i.Z.hovering,t.y>=d.current+p.current/2?i.Z.hoverBottom:i.Z.hoverTop)}t.preventDefault(),t.stopPropagation()})),e.addEventListener("dragleave",(function(e){!a||!e.currentTarget||n&&(null==a?void 0:a.id)===n.id||e.currentTarget.classList.remove(i.Z.hovering,i.Z.hoverTop,i.Z.hoverBottom),f.current=!0,e.stopPropagation()})),e.addEventListener("dragend",(function(e){!a||!e.currentTarget||n&&(null==a?void 0:a.id)===n.id||e.currentTarget.classList.remove(i.Z.hovering,i.Z.hoverTop,i.Z.hoverBottom),a=null,f.current=!0})),e.addEventListener("drop",(function(e){e.preventDefault(),!a||!e.currentTarget||n&&(null==a?void 0:a.id)===n.id||(e.currentTarget.classList.remove(i.Z.hovering,i.Z.hoverTop,i.Z.hoverBottom),c(a,u||n,u?"inner":e.y>=d.current+p.current/2?"bottom":"top")),a=null,f.current=!0}))}),[]),o().createElement("div",{ref:s,draggable:l},t)}},2544:(e,t,n)=>{n.r(t),n.d(t,{AddressInput:()=>r.Jg,Button:()=>l.Z,CDN:()=>m.G3,Collapse:()=>a.Z,Debug:()=>d.Z,DescriptionInput:()=>r.$D,DocInput:()=>r.RH,Dropdown:()=>c.Z,EditorWithFullScreen:()=>r.V2,FormItem:()=>i.Z,Input:()=>p.Z,MethodRadio:()=>r.gK,Methods:()=>m.fq,NameInput:()=>r.Rj,PanelWrap:()=>o.Z,RadioButtons:()=>u.Z,Switch:()=>s.Z,TextArea:()=>p.K,notice:()=>f.I,safeDecode:()=>h.oV,uuid:()=>h.Vj});var r=n(978),o=n(1817),i=n(6017),a=n(8250),l=n(5619),c=n(6233),u=n(318),s=n(957),p=n(5132),d=n(5874),f=n(5196),h=n(6178),m=n(5525)},1817:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(8156),o=n.n(r),i=n(7111),a=n.n(i),l=n(5619),c=n(3295);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}const s=(0,r.forwardRef)((function(e,t){var n=e.children,i=(e.style,e.className),s=void 0===i?"":i,p=e.title,d=void 0===p?"":p,f=e.extra,h=void 0===f?null:f,m=e.onClose,g=(0,r.useRef)({}),v=(0,r.useCallback)((function(){return Object.values(g.current).forEach((function(e){return null==e?void 0:e()}))}),[]);return(0,r.useImperativeHandle)(t,(function(){return{registerBlur:function(e,t){g.current=Object.assign(Object.assign({},g.current),function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==u(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===u(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},e,t))}}}),[]),a().createPortal(o().createElement("div",{"data-id":"plugin-panel",className:"".concat(c.Z.pluginPanelContainer," ").concat(s),onClick:v},o().createElement("div",{className:c.Z.pluginPanelTitle},o().createElement("div",null,d),o().createElement("div",null,h,o().createElement(l.Z,{size:"small",onClick:m},"关 闭"))),o().createElement("div",{className:c.Z.pluginPanelContent},n)),document.querySelector("div[data-id=plugin-root-panel]"))}))},6639:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8156),o=n.n(r),i=n(7203);const a=function(e){var t=e.content,n=e.children;return o().createElement("span",{className:i.Z.tooltipContainer},n,o().createElement("div",{className:i.Z.tooltip},t),o().createElement("div",{className:i.Z.arrow}))}},5525:(e,t,n)=>{n.d(t,{Aj:()=>a,Cq:()=>u,G3:()=>d,MK:()=>o,Nd:()=>h,Q1:()=>l,SL:()=>f,Ys:()=>i,d9:()=>g,fq:()=>r,iO:()=>s,iz:()=>p,tn:()=>c,zi:()=>m});var r,o="@mybricks/plugins/service",i="export default function ({ params, data, headers, url, method }) {\n  // 设置请求query、请求体、请求头\n  return { params, data, headers, url, method };\n }\n",a="export default function (result, { method, url, params, data, headers }, { throwError }) {\n  // return {\n  //  total: result.all,\n  //  dataSource: result.list.map({id, name} => ({\n  //     value:id, label: name\n  //  }))\n  // }\n  return result;\n}\n",l="export default function ({ response, config }, { throwError }) {\n  // if (response.code !== 0) {\n  //    throwError(response.errMsg);\n  //    throwError({ message: response.errMsg || '我是复杂错误对象' });\n  // }\n  return response\n}\n",c="export default function handleError({ error, response, config }, { throwError }) {\n  // 错误抛出时预处理数据，请注意：必须调用 throwError 抛出错误;\n  // config：请求入参；error：错误对象；response：响应原始对象；response.status：获取 HTTP 状态码，response.data：获取接口返回值\n  throwError(response.data.message || error.message || error);\n}\n",u={HTTP:"http",TG:"http-tg",KDEV:"http-kdev",FOLDER:"folder",IMPORT:"import-from-clipboard"},s="separator",p="global-setting",d={eslint:"/mfs/editor_assets/eslint/8.15.0/eslint.js",paths:{vs:"/mfs/editor_assets/monaco-editor/0.33.0/min/vs"},babel:"/mfs/editor_assets/babel/babel-standalone.min.js"},f=function(){d=void 0},h=[{key:"predicate",title:"标记组生效标识",description:"标识标记组满足对应条件生效",needMarkValue:!0},{key:"output",title:"返回内容",description:"当标记组生效时，返回此标记对应内容"}],m={predicate:"生效标识",output:"返回内容"},g={predicate:["number","string","boolean"],output:["any"]};!function(e){e.GET="GET",e.POST="POST",e.PUT="PUT",e.DELETE="DELETE"}(r||(r={}))},3887:(e,t,n)=>{n.d(t,{Z:()=>r});const r={connectors:[]}},9493:(e,t,n)=>{n.d(t,{Dp:()=>d,JG:()=>s,Kp:()=>h,Od:()=>c,PD:()=>p,Qx:()=>a,Vp:()=>m,cK:()=>g,eP:()=>l,qv:()=>i,t8:()=>u,vU:()=>f});var r=n(8156),o=n.n(r),i=o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32"},o().createElement("path",{d:"M764.41958 521.462537l37.594406-37.594405a202.037962 202.037962 0 0 0 59.588412-144.23976 169.302697 169.302697 0 0 0-53.45055-121.734266l-6.137862-6.137862a163.932068 163.932068 0 0 0-127.872128-53.962038 193.854146 193.854146 0 0 0-135.032967 60.0999l-38.105894 37.082917zM373.386613 254.977023l106.901099-102.297702a281.318681 281.318681 0 0 1 197.69031-84.13986 250.117882 250.117882 0 0 1 160.095904 53.962038l127.872128-122.501499L1022.977023 58.565435l-127.872128 127.872127a279.784216 279.784216 0 0 1-30.689311 360.599401l-100.251748 102.297702zM227.100899 530.157842a189.250749 189.250749 0 0 0-5.370629 265.718282l6.137862 6.137862a164.443556 164.443556 0 0 0 127.872128 53.706294 194.621379 194.621379 0 0 0 135.032967-59.844156l42.965035-43.476524L270.065934 486.937063zM0 967.224775l133.242757-139.892108a278.761239 278.761239 0 0 1 30.689311-360.343656L270.065934 359.064935l80.559441 81.070929L430.929071 359.064935l57.798202 58.053946L409.190809 498.701299l120.1998 120.967033 83.628372-83.884116 53.962038 55.496503-85.418581 85.93007 74.933066 75.444556-106.133866 106.901099a283.108891 283.108891 0 0 1-198.457542 84.651348 251.396603 251.396603 0 0 1-160.095904-53.706293L58.30969 1024z"})),a=o().createElement("svg",{width:"16",height:"16",viewBox:"0 0 1057 1024"},o().createElement("path",{d:"M342.016 214.016l468.010667 297.984-468.010667 297.984 0-596.010667z"})),l=o().createElement("svg",{viewBox:"64 64 896 896",focusable:"false","data-icon":"form",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},o().createElement("path",{d:"M904 512h-56c-4.4 0-8 3.6-8 8v320H184V184h320c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V520c0-4.4-3.6-8-8-8z"}),o().createElement("path",{d:"M355.9 534.9L354 653.8c-.1 8.9 7.1 16.2 16 16.2h.4l118-2.9c2-.1 4-.9 5.4-2.3l415.9-415c3.1-3.1 3.1-8.2 0-11.3L785.4 114.3c-1.6-1.6-3.6-2.3-5.7-2.3s-4.1.8-5.7 2.3l-415.8 415a8.3 8.3 0 00-2.3 5.6zm63.5 23.6L779.7 199l45.2 45.1-360.5 359.7-45.7 1.1.7-46.4z"})),c=o().createElement("svg",{viewBox:"64 64 896 896",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},o().createElement("path",{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"})),u=o().createElement("svg",{viewBox:"0 0 1024 1024",fill:"currentColor",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20"},o().createElement("path",{d:"M448.362667 166.826667l113.6 0.170666a64 64 0 0 1 63.893333 63.914667l0.042667 18.517333a301.461333 301.461333 0 0 1 62.101333 34.88l15.210667-8.746666a64 64 0 0 1 87.296 23.381333l56.938666 98.304a64 64 0 0 1-19.989333 85.397333l-3.477333 2.133334-15.274667 8.810666c2.624 24.234667 2.304 48.853333-1.130667 73.322667l10.794667 6.250667a64 64 0 0 1 25.216 84.117333l-1.770667 3.306667-53.333333 92.373333a64 64 0 0 1-84.117333 25.216l-3.328-1.792-14.741334-8.533333a298.538667 298.538667 0 0 1-59.626666 33.28v25.386666a64 64 0 0 1-59.989334 63.957334l-4.074666 0.128-113.6-0.170667a64 64 0 0 1-63.893334-63.893333l-0.064-30.613334a302.613333 302.613333 0 0 1-50.069333-29.696l-27.221333 15.658667a64 64 0 0 1-87.296-23.402667l-56.938667-98.282666a64 64 0 0 1 19.989333-85.418667l3.477334-2.133333 27.690666-15.936c-2.133333-20.266667-2.24-40.768-0.192-61.226667l-30.741333-17.770667A64 64 0 0 1 158.506667 393.6l1.792-3.306667 53.333333-92.373333a64 64 0 0 1 84.117333-25.216l3.306667 1.792 26.794667 15.466667a297.984 297.984 0 0 1 56.426666-34.666667v-24.362667a64 64 0 0 1 59.989334-63.978666l4.074666-0.128z m-0.085334 64l0.064 65.066666-36.778666 17.301334c-15.744 7.402667-30.613333 16.533333-44.309334 27.221333l-34.005333 26.538667-62.570667-36.138667-1.6-0.896-53.333333 92.373333 66.56 38.421334-4.138667 41.152c-1.6 15.978667-1.536 32.106667 0.149334 48.085333l4.394666 41.429333-63.786666 36.736 56.917333 98.282667 63.338667-36.416 33.6 24.597333a237.994667 237.994667 0 0 0 39.466666 23.424l36.736 17.258667 0.128 71.168 113.578667 0.170667-0.064-68.16 39.466667-16.426667a234.538667 234.538667 0 0 0 46.826666-26.112l33.578667-24.128 50.56 29.184 53.290667-92.394667-48.128-27.818666 5.973333-42.688c2.666667-19.093333 2.965333-38.421333 0.896-57.6l-4.48-41.450667 51.456-29.696-56.938667-98.282667-51.2 29.504-33.621333-24.512a238.037333 238.037333 0 0 0-48.938667-27.498666l-39.381333-16.341334-0.128-61.184-113.578667-0.170666z m127.381334 183.722666a128.170667 128.170667 0 0 1 46.890666 174.933334 127.829333 127.829333 0 0 1-174.762666 46.848 128.170667 128.170667 0 0 1-46.869334-174.933334 127.829333 127.829333 0 0 1 174.741334-46.848z m-119.317334 78.805334a64.170667 64.170667 0 0 0 23.466667 87.573333 63.829333 63.829333 0 0 0 87.296-23.402667 64.170667 64.170667 0 0 0-20.266667-85.589333l-3.2-1.984-3.306666-1.770667a63.829333 63.829333 0 0 0-83.989334 25.173334z"})),s=o().createElement("svg",{viewBox:"0 0 1024 1024",fill:"currentColor",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},o().createElement("path",{d:"M853.333333 224h-53.333333V170.666667c0-40.533333-34.133333-74.666667-74.666667-74.666667H170.666667C130.133333 96 96 130.133333 96 170.666667v554.666666c0 40.533333 34.133333 74.666667 74.666667 74.666667h53.333333V853.333333c0 40.533333 34.133333 74.666667 74.666667 74.666667h554.666666c40.533333 0 74.666667-34.133333 74.666667-74.666667V298.666667c0-40.533333-34.133333-74.666667-74.666667-74.666667zM160 725.333333V170.666667c0-6.4 4.266667-10.666667 10.666667-10.666667h554.666666c6.4 0 10.666667 4.266667 10.666667 10.666667v554.666666c0 6.4-4.266667 10.666667-10.666667 10.666667H170.666667c-6.4 0-10.666667-4.266667-10.666667-10.666667z m704 128c0 6.4-4.266667 10.666667-10.666667 10.666667H298.666667c-6.4 0-10.666667-4.266667-10.666667-10.666667v-53.333333H725.333333c40.533333 0 74.666667-34.133333 74.666667-74.666667V288H853.333333c6.4 0 10.666667 4.266667 10.666667 10.666667v554.666666z"}),o().createElement("path",{d:"M576 416h-96V320c0-17.066667-14.933333-32-32-32s-32 14.933333-32 32v96H320c-17.066667 0-32 14.933333-32 32s14.933333 32 32 32h96V576c0 17.066667 14.933333 32 32 32s32-14.933333 32-32v-96H576c17.066667 0 32-14.933333 32-32s-14.933333-32-32-32z"})),p=o().createElement("svg",{viewBox:"0 0 1024 1024",fill:"currentColor",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},o().createElement("path",{d:"M474 152m8 0l60 0q8 0 8 8l0 704q0 8-8 8l-60 0q-8 0-8-8l0-704q0-8 8-8Z"}),o().createElement("path",{d:"M168 474m8 0l672 0q8 0 8 8l0 60q0 8-8 8l-672 0q-8 0-8-8l0-60q0-8 8-8Z"})),d=o().createElement("svg",{viewBox:"64 64 896 896",focusable:"false","data-icon":"right",width:"16",height:"16",fill:"currentColor","aria-hidden":"true"},o().createElement("path",{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"})),f=o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},o().createElement("path",{d:"M721.877333 303.104a48.469333 48.469333 0 0 0-68.437333 0l-141.909333 141.909333-136.277334-136.234666a46.933333 46.933333 0 1 0-66.432 66.432l136.277334 136.277333-141.952 141.952a48.384 48.384 0 0 0 68.394666 68.352l141.952-141.909333 137.045334 137.002666a46.933333 46.933333 0 1 0 66.432-66.432l-137.045334-137.002666 141.952-141.952a48.469333 48.469333 0 0 0 0-68.394667M512 1024C229.248 1024 0 794.752 0 512 0 229.205333 229.248 0 512 0c282.794667 0 512 229.205333 512 512 0 282.752-229.205333 512-512 512",fill:"#FA5555"})),h=o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16"},o().createElement("path",{d:"M512 53.248c129.707008 3.412992 237.739008 48.299008 324.096 134.656S967.339008 382.292992 970.752 512c-3.412992 129.707008-48.299008 237.739008-134.656 324.096S641.707008 967.339008 512 970.752c-129.707008-3.412992-237.739008-48.299008-324.096-134.656S56.660992 641.707008 53.248 512c3.412992-129.707008 48.299008-237.739008 134.656-324.096S382.292992 56.660992 512 53.248z m0 196.608c-17.748992 0-32.427008 6.484992-44.032 19.456-11.604992 12.971008-16.724992 28.331008-15.36 46.08l23.552 262.144c1.364992 9.556992 5.291008 17.236992 11.776 23.04 6.484992 5.803008 14.507008 8.704 24.064 8.704s17.579008-2.900992 24.064-8.704c6.484992-5.803008 10.411008-13.483008 11.776-23.04l23.552-262.144c1.364992-17.748992-3.755008-33.108992-15.36-46.08-11.604992-12.971008-26.283008-19.456-44.032-19.456z m0 524.288c15.019008-0.683008 27.476992-5.803008 37.376-15.36 9.899008-9.556992 14.848-21.844992 14.848-36.864 0-15.019008-4.948992-27.476992-14.848-37.376-9.899008-9.899008-22.356992-14.848-37.376-14.848-15.019008 0-27.476992 4.948992-37.376 14.848-9.899008 9.899008-14.848 22.356992-14.848 37.376 0 15.019008 4.948992 27.307008 14.848 36.864 9.899008 9.556992 22.356992 14.676992 37.376 15.36z",fill:"#faad14"})),m=o().createElement("svg",{viewBox:"64 64 896 896",width:"1em",height:"1em",fill:"#52c41a","aria-hidden":"true"},o().createElement("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"})),g=o().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"21997",width:"12",height:"12"},o().createElement("path",{d:"M698.144169 0.005689h279.259488a44.372051 44.372051 0 0 1 32.880828 13.766713c9.101959 8.988185 13.652939 19.910536 13.652939 32.880828v279.202601c0 12.913405-4.55098 23.892643-13.652939 32.994602a44.770262 44.770262 0 0 1-32.880828 13.652939 44.770262 44.770262 0 0 1-32.937715-13.652939 44.770262 44.770262 0 0 1-13.652939-32.994602V159.00554L544.605494 545.099275a44.542713 44.542713 0 0 1-32.710166 13.539164 45.282247 45.282247 0 0 1-46.533767-46.533766c0-12.913405 4.437205-23.721981 13.42539-32.823941l386.264397-386.093735H698.030395a44.940924 44.940924 0 0 1-32.937715-13.652939 44.940924 44.940924 0 0 1-13.652939-32.880828c0-12.970292 4.607867-23.892643 13.652939-32.994602a44.940924 44.940924 0 0 1 32.937715-13.652939zM139.568306 0.005689h325.850142c12.856517 0 23.778869 4.55098 32.880828 13.766713 9.101959 8.988185 13.652939 19.910536 13.652939 32.880828a44.940924 44.940924 0 0 1-13.652939 32.880828 44.940924 44.940924 0 0 1-32.880828 13.652939H139.625193a44.713375 44.713375 0 0 0-32.937715 13.652939 44.713375 44.713375 0 0 0-13.652939 32.994602v744.654043c0 12.79963 4.55098 23.721981 13.652939 32.823941 9.101959 9.101959 20.081198 13.652939 32.937715 13.652939h744.654044a44.940924 44.940924 0 0 0 32.937715-13.652939 44.940924 44.940924 0 0 0 13.652939-32.880828V558.638439a44.940924 44.940924 0 0 1 13.652939-32.82394 44.940924 44.940924 0 0 1 32.880827-13.652939c12.856517 0 23.835756 4.55098 32.994603 13.652939 9.045072 9.101959 13.596052 20.02431 13.596052 32.82394v325.850142c0 38.512665-13.652939 71.336606-40.90193 98.642484-27.305878 27.305878-60.186706 40.845042-98.699371 40.845042H139.682081a134.310787 134.310787 0 0 1-98.756259-40.845042A134.481448 134.481448 0 0 1 0.023893 884.431694V139.777651C0.023893 101.264986 13.676832 68.497932 40.98271 41.192054c27.248991-27.305878 60.129818-40.958817 98.699371-40.958816L139.568306 0.005689z",fill:"currentColor"}))},5874:(e,t,n)=>{n.d(t,{Z:()=>j});var r=n(8156),o=n.n(r),i=n(6178),a=n(3505),l=n(7233),c=n(1754),u=n(100),s=n(4135),p=n(6621),d=n(6017),f=n(4113),h=n(5619),m=n(2544),g=n(6639),v=n(5525),y=n(7897);function _(){_=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),l=new N(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var d="suspendedStart",f="suspendedYield",h="executing",m="completed",g={};function v(){}function y(){}function x(){}var w={};u(w,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&r.call(O,a)&&(w=O);var S=x.prototype=v.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(o,i,a,l){var c=p(e[o],e,i);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==b(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=d;return function(i,a){if(o===h)throw new Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?m:f,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=m,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(b(t)+" is not iterable")}return y.prototype=x,o(S,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:y,configurable:!0}),y.displayName=u(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,u(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),u(j.prototype,l,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),u(S,c,"Generator"),u(S,a,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function x(e){return function(e){if(Array.isArray(e))return O(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||E(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||E(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function E(e,t){if(e){if("string"==typeof e)return O(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?O(e,t):void 0}}function O(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function S(e){var t=e.data,n="";try{n=JSON.stringify(t,null,2)}catch(e){console.log(e,"error")}return n?o().createElement("div",{style:{marginLeft:87}},o().createElement("div",{className:y.Z.title},"标记后的返回结果示例"),o().createElement(l.Z,{value:n,height:300,language:"json",theme:"light",options:{readOnly:!0}})):null}var k=o().createElement("svg",{viewBox:"0 0 1027 1024",width:"16",height:"16",fill:"currentColor"},o().createElement("path",{d:"M321.828571 226.742857c-14.628571-14.628571-36.571429-14.628571-51.2 0L7.314286 482.742857c-14.628571 14.628571-14.628571 36.571429 0 51.2l256 256c14.628571 14.628571 36.571429 14.628571 51.2 0 14.628571-14.628571 14.628571-36.571429 0-51.2L87.771429 512l234.057142-234.057143c7.314286-14.628571 7.314286-36.571429 0-51.2z m263.314286 0c-14.628571 0-36.571429 7.314286-43.885714 29.257143l-131.657143 497.371429c-7.314286 21.942857 7.314286 36.571429 29.257143 43.885714s36.571429-7.314286 43.885714-29.257143l131.657143-497.371429c7.314286-14.628571-7.314286-36.571429-29.257143-43.885714z m431.542857 256l-256-256c-14.628571-14.628571-36.571429-14.628571-51.2 0-14.628571 14.628571-14.628571 36.571429 0 51.2L936.228571 512l-234.057142 234.057143c-14.628571 14.628571-14.628571 36.571429 0 51.2 14.628571 14.628571 36.571429 14.628571 51.2 0l256-256c14.628571-14.628571 14.628571-43.885714 7.314285-58.514286z"}));const j=function(e){var t,n=e.model,l=e.onChangeModel,E=e.connect,O=e.registerBlur,j=w((0,r.useState)(),2),C=j[0],A=j[1],Z=w((0,r.useState)(!1),2),T=Z[0],N=Z[1],P=w((0,r.useState)(!1),2),R=P[0],L=P[1],M=(0,r.useRef)(),I=(0,r.useRef)(),F=(0,r.useRef)(),D=(0,r.useRef)(null),B=(0,r.useRef)(null),U=w((0,r.useState)(""),2),z=U[0],V=U[1],q=w((0,r.useState)(!1),2),H=q[0],J=q[1],K=w((0,r.useState)(!1),2),W=K[0],Y=K[1],G=w((0,r.useState)(!1),2),$=G[0],Q=G[1],X=w((0,r.useState)(!1),2),ee=X[0],te=X[1],ne=w((0,r.useState)("default"),2),re=ne[0],oe=ne[1],ie=(0,r.useRef)(""),ae=(0,r.useRef)(),le=(0,r.useMemo)((function(){var e;return null===(e=n.markList)||void 0===e?void 0:e.find((function(e){return e.id===re}))}),[n,re]),ce=(0,r.useCallback)((function(e){var t,n=e;(null===(t=e.markList)||void 0===t?void 0:t.length)||(n=Object.assign(Object.assign({},e),{outputKeys:void 0,excludeKeys:void 0,outputSchema:void 0,resultSchema:void 0,markList:[{title:"默认",id:"default",predicate:{},outputKeys:e.outputKeys||[],excludeKeys:e.excludeKeys||[],outputSchema:e.outputSchema||{},resultSchema:e.resultSchema}]})),e.params||(n=Object.assign(Object.assign({},n),{params:{type:"root",name:"root",children:[]}})),l(n)}),[l]),ue=(0,r.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,r=x(e),o=x(t);return n?(r=e.filter(Boolean).map((function(e){return e.split(".")})).filter((function(e){for(var t,r,o=n.properties||(null===(t=n.items)||void 0===t?void 0:t.properties),i=0;i<e.length;i++){var a=e[i];if(!o||!o[a])return!1;o=o[a].properties||(null===(r=o[a].items)||void 0===r?void 0:r.properties)}return!0})).map((function(e){return e.join(".")})),o=t.filter(Boolean).map((function(e){return e.split(".")})).filter((function(e){for(var t,r,o=n.properties||(null===(t=n.items)||void 0===t?void 0:t.properties),i=0;i<e.length;i++){var a=e[i];if(!o||!o[a])return!1;o=o[a].properties||(null===(r=o[a].items)||void 0===r?void 0:r.properties)}return!0})).map((function(e){return e.join(".")})),{outputKeys:r,excludeKeys:o}):{outputKeys:[],excludeKeys:[]}}),[]),se=function(){try{(0,i.rq)(I.current);var e=(0,i.WR)(n.params||{});(0,i.rq)(e),n.inputSchema=e;var t=JSON.parse(JSON.stringify(n.markList)),r=t.find((function(e){return e.id===re}));if(!r)throw Error("当前标记组不存在");var o=ue(r.outputKeys,r.excludeKeys,I.current),a=o.outputKeys,c=void 0===a?[]:a,u=o.excludeKeys,s=void 0===u?[]:u,p=(0,i.AE)((0,i.Zg)(M.current,c),s),d=(0,i.HD)(p);if(Array.isArray(c)&&c.length&&(c.length>1||1!==c.length||""!==c[0]))try{for(var f=x(c).map((function(e){return e.split(".")}));"[object Object]"===Object.prototype.toString.call(p)&&f.every((function(e){return!!e.length}))&&1===Object.values(p).length;)p=Object.values(p)[0],d=Object.values(d.properties)[0],f.forEach((function(e){return e.shift()}))}catch(e){}(0,i.rq)(d),A(p),r.outputKeys=c,r.excludeKeys=s,r.outputSchema=d,r.resultSchema=I.current,l(Object.assign(Object.assign({},n),{markList:t}))}catch(e){console.error(e);var h=e instanceof Error;V(h?(null==e?void 0:e.message)||e:"接口错误：".concat("string"==typeof e?e:"由全局响应错误拦截透出，值为 ".concat(JSON.stringify(e))))}Y(!1),Q(!1)},pe=(0,r.useCallback)((function(){Y(!1),Q(!1)}),[]),de=(0,r.useCallback)((function(){return Q((function(e){return!e}))}),[]),fe=(0,r.useCallback)((function(e){if(void 0!==e){var t=(0,i.WR)(e);(0,i.rq)(t),l(Object.assign(Object.assign({},n),{inputSchema:t,params:e}))}}),[n,l]),he=(0,r.useCallback)((function(e){var t,r,o,a=e.outputKeys,c=void 0===a?[]:a,u=e.excludeKeys,s=void 0===u?[]:u,p=e.resultSchema;try{var d=!1,h={};0===c.length?h=p:1===c.length&&""===c[0]?h={type:"any"}:(h="array"===p.type?{type:"array",items:"object"===(null===(t=p.items)||void 0===t?void 0:t.type)?{type:"object",properties:{}}:"array"===(null===(r=p.items)||void 0===r?void 0:r.type)?{type:"array",items:{}}:{type:null===(o=p.items)||void 0===o?void 0:o.type}}:{type:"object",properties:{}},c.forEach((function(e){var t,n,r,o,i=h.properties||(null===(t=h.items)||void 0===t?void 0:t.properties)||(null===(n=h.items)||void 0===n?void 0:n.items),a=p.properties||(null===(r=p.items)||void 0===r?void 0:r.properties)||(null===(o=p.items)||void 0===o?void 0:o.items),l=e.split(".");l.forEach((function(e,t){if(i&&a&&a[e])if(t===l.length-1)i[e]=Object.assign({},a[e]);else{var n=a[e].type;"array"===n?(i[e]=i[e]||Object.assign(Object.assign({},a[e]),{items:"object"===a[e].items.type?{type:"object",properties:{}}:"array"===a[e].items.type?{type:"array",items:{}}:{type:a[e].items.type}}),i=i[e].items.properties,a=a[e].items.properties):"object"===n?(i[e]=i[e]||Object.assign(Object.assign({},a[e]),{properties:{}}),i=i[e].properties,a=a[e].properties):(i[e]=Object.assign({},a[e]),i=i[e].properties,a=a[e].properties)}}))})),1===Object.keys(h.properties).length&&(d=!0)),s=s.map((function(e){return e.split(".")})).filter((function(e){for(var t,n,r=h.properties||(null===(t=h.items)||void 0===t?void 0:t.properties),o=0;o<e.length;o++){var i=e[o];if(!r||!r[i])return!1;r=r[i].properties||(null===(n=r[i].items)||void 0===n?void 0:n.properties)}return!0})).map((function(e){return e.join(".")}));var m=(0,f.Xh)(h);null==s||s.forEach((function(e){for(var t=e.split("."),n=t.length,r=m,o=0;o<n-1;o++)r=(r.properties||r.items.properties)[t[o]];try{Reflect.deleteProperty(r.properties||r.items.properties,t[n-1])}catch(e){}}));try{var g=(0,f.Xh)(M.current),v=(0,i.Zg)((0,i.AE)(g,s),c);if(d)try{for(var y=c.map((function(e){return e.split(".")}));"object"===m.type&&y.every((function(e){return!!e.length}))&&1===Object.values(m.properties||{}).length;)v=M.current?Object.values(v)[0]:v,m=Object.values(m.properties)[0],y.forEach((function(e){return e.shift()}))}catch(e){console.log(e)}void 0!==v&&A(M.current?v:void 0)}catch(e){}var _=n.markList.findIndex((function(t){return t.id===e.id}));e.outputKeys=c,e.excludeKeys=s,e.outputSchema=m,n.markList.splice(_,1,Object.assign({},e)),l(Object.assign(Object.assign({},n),{markList:x(n.markList)}))}catch(e){console.log(e)}}),[n]),me=(0,r.useCallback)((function(e){return F.current=e}),[]),ge=(0,r.useCallback)((function(){F.current=le.resultSchema,J(!0)}),[le]),ve=(0,r.useCallback)((function(){var e=n.markList.find((function(e){return e.id===re}));if(e){e.resultSchema=F.current;var t=ue(e.outputKeys,e.excludeKeys,e.resultSchema),r=t.outputKeys,o=t.excludeKeys;he(Object.assign(Object.assign({},e),{outputKeys:r,excludeKeys:o}))}J(!1)}),[re,n,he]),ye=(0,r.useCallback)((function(){F.current=void 0,J(!1)}),[]),_e=(0,r.useCallback)((function(){return te(!0)}),[]),be=(0,r.useCallback)((function(){return te(!1)}),[]),xe=(0,r.useCallback)((function(e){return ie.current=e.target.value}),[]),we=(0,r.useCallback)((function(){var e=(0,i.Vj)(),t=n.markList.find((function(e){return"default"===e.id}));l(Object.assign(Object.assign({},n),{markList:[].concat(x(n.markList),[{title:ie.current,id:e,outputKeys:[],excludeKeys:[],resultSchema:null==t?void 0:t.resultSchema,outputSchema:null==t?void 0:t.resultSchema}])})),ie.current="",te(!1),oe(e)}),[n]),Ee=(0,r.useCallback)((function(e){13!==e.keyCode&&"Enter"!==e.key||we()}),[we]),Oe=(0,r.useCallback)((function(e){var t,r=n.markList.findIndex((function(t){return t.id===e})),o=re===(null===(t=n.markList[r])||void 0===t?void 0:t.id);n.markList.splice(r,1),l(Object.assign(Object.assign({},n),{markList:x(n.markList)})),o&&oe(n.markList[r-1].id)}),[n,re]),Se=(0,r.useCallback)((function(e){oe(e),A(void 0)}),[]),ke=(0,r.useCallback)((function(e){var t,r;if(e.stopPropagation(),T)try{if(!n.inputSchema&&!(null===(t=D.current)||void 0===t?void 0:t.value))return void N(!T);var o=JSON.parse(null===(r=D.current)||void 0===r?void 0:r.value);if("object"===o.type&&o.properties){if(JSON.stringify(n.inputSchema)!==JSON.stringify(o)){var c=w((0,a.B)(o),2),u=c[0],s=c[1];if(!1===u)return(0,m.notice)("JSON 解析错误，".concat(s.length?s[0].msg+"，":"","此次变更被忽略"),{type:"warning"}),void N(!T);l(Object.assign(Object.assign({},n),{inputSchema:o,params:(0,i.xF)(o)}))}}else if("[object Object]"===Object.prototype.toString.call(o)){var p=(0,i.kl)(o),d=p.params,f=p.originSchema;l(Object.assign(Object.assign({},n),{inputSchema:f,params:d}))}else(0,m.notice)("JSON 描述不合法，此次变更被忽略",{type:"warning"})}catch(e){console.warn("JSON 解析错误",e),(0,m.notice)("JSON 解析错误，此次变更被忽略",{type:"warning"})}N(!T)}),[T]),je=(0,r.useCallback)((function(e){var t,r;if(e.stopPropagation(),R)try{if(!le.resultSchema&&!(null===(t=B.current)||void 0===t?void 0:t.value))return void L(!R);var o=JSON.parse(null===(r=B.current)||void 0===r?void 0:r.value);if(JSON.stringify(le.resultSchema)!==JSON.stringify(o)){var i=w((0,a.B)(o),2),l=i[0],c=i[1],u=void 0===c?[]:c;if(!1===l)return(0,m.notice)("JSON 解析错误，".concat(u.length?u[0].msg+",":"","此次变更被忽略"),{type:"warning"}),void L(!R);var s=(0,f.Xh)(n.markList).find((function(e){return e.id===le.id}));if(!s)return(0,m.notice)("当前标记组不存在，此次变更被忽略",{type:"warning"}),void L(!R);var p=ue(s.outputKeys,s.excludeKeys,o),d=p.outputKeys,h=p.excludeKeys;he(Object.assign(Object.assign({},s),{outputKeys:d,excludeKeys:h,resultSchema:o}))}}catch(e){console.warn("JSON 解析错误",e),(0,m.notice)("JSON 解析错误，此次变更被忽略",{type:"warning"})}L(!R)}),[R,le,n,he]);return(0,r.useEffect)((function(){V(""),ce(n)}),[n.id]),(0,r.useEffect)((function(){var e,t;ee&&(null===(t=null===(e=ae.current)||void 0===e?void 0:e.focus)||void 0===t||t.call(e))}),[ee]),o().createElement(o().Fragment,null,o().createElement("div",{className:y.Z.paramEditContainer},T?o().createElement(d.Z,{label:"请求参数",labelTop:!0},o().createElement("textarea",{ref:D,className:"".concat(y.Z.codeText,"  ").concat(y.Z.textEdt),cols:30,rows:10,defaultValue:JSON.stringify(n.inputSchema,null,2)}),o().createElement("div",null,"支持识别 JSON、JSON Schema 等描述协议")):o().createElement(r.Fragment,{key:n.id},o().createElement(d.Z,{label:"请求参数",labelTop:!0},o().createElement(u.Z,{value:n.params,onChange:fe})),o().createElement(d.Z,null,o().createElement(s.Z,{showTip:W,onCloseTip:pe,onToggleSchemaPreview:de,showPreviewSchema:$,onConfirmTip:se,onDebugClick:function(){return e=void 0,t=_().mark((function e(){var t,r,o,a,l;return _().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(e.prev=0,n.path){e.next=4;break}return(0,m.notice)("接口的请求路径不能为空",{type:"warning"}),e.abrupt("return");case 4:return t=n.params||{},r=(0,i.Sm)(t),["POST","PUT"].includes(n.method)&&(0,i.DW)(t)&&(o=new FormData,Object.keys(r).forEach((function(e){var t=r[e];Array.isArray(t)?t[0]instanceof File?t.forEach((function(t){return o.append(e,t)})):o.append(e,JSON.stringify(t)):"object"===b(t)?o.append(e,t instanceof File?t:JSON.stringify(t)):o.append(e,t)})),r=o),V(""),e.next=10,E({type:n.type||"http",mode:"test",id:n.id,connectorName:v.MK,content:n},r);case 10:M.current=e.sent,a=(0,i.HD)(M.current),I.current=a||{},le.resultSchema&&JSON.stringify(le.resultSchema)!==JSON.stringify(I.current)?Y(!0):se(),e.next=21;break;case 16:e.prev=16,e.t0=e.catch(0),console.error(e.t0),l=e.t0 instanceof Error,V(l?(null===e.t0||void 0===e.t0?void 0:e.t0.message)||e.t0:"接口错误：".concat("string"==typeof e.t0?e.t0:"由全局响应错误拦截透出，值为 ".concat(JSON.stringify(e.t0))));case 21:Q(!1);case 22:case"end":return e.stop()}}),e,null,[[0,16]])})),new(e||(e=Promise))((function(n,r){function o(e){try{a(t.next(e))}catch(e){r(e)}}function i(e){try{a(t.throw(e))}catch(e){r(e)}}function a(t){var r;t.done?n(t.value):(r=t.value,r instanceof e?r:new e((function(e){e(r)}))).then(o,i)}a((t=t.apply(void 0,[])).next())}));var e,t},params:n.params}))),o().createElement("div",{className:"".concat(y.Z.codeIcon," ").concat(T?y.Z.focus:""),onClick:ke},k)),$?o().createElement(d.Z,{label:"预览类型",labelTop:!0},o().createElement(c.Z,{outputKeys:[],excludeKeys:[],noMark:!0,schema:I.current})):H?o().createElement(d.Z,{label:"返回数据",labelTop:!0},o().createElement("div",{className:y.Z.buttonGroup},o().createElement("div",null),o().createElement("div",null,o().createElement(h.Z,{onClick:ye},"取消"),o().createElement(h.Z,{onClick:ve},"保存"))),o().createElement(p.Z,{key:n.id,schema:le.resultSchema,onChange:me})):o().createElement(o().Fragment,null,o().createElement(d.Z,{label:"返回数据",className:y.Z.scrollFormItem,labelTop:!0},R?o().createElement(o().Fragment,null,o().createElement("div",{className:y.Z.buttonGroup},o().createElement("div",null),o().createElement("div",{className:"".concat(y.Z.codeIcon," ").concat(y.Z.responseCodeIcon," ").concat(R?y.Z.focus:""),onClick:je},k)),o().createElement("textarea",{ref:B,className:"".concat(y.Z.codeText,"  ").concat(y.Z.textEdt),cols:30,rows:10,defaultValue:JSON.stringify(le.resultSchema,null,2)}),o().createElement("div",null,"支持识别JSON Schema 描述协议")):o().createElement(o().Fragment,null,o().createElement("div",{className:y.Z.buttonGroup},o().createElement("div",{className:y.Z.categoryContainer},o().createElement("div",{className:y.Z.buttons},null===(t=n.markList)||void 0===t?void 0:t.map((function(e){return o().createElement("div",{key:e.id,className:"".concat(y.Z.option," ").concat(e.id===re?y.Z.selected:""),onClick:function(){return Se(e.id)}},e.title,"default"!==e.id?o().createElement("div",{className:y.Z.optionCancelIcon,onClick:function(t){t.stopPropagation(),Oe(e.id)}},"✕"):null)}))),o().createElement(g.Z,{content:"添加数据标记组"},ee?o().createElement("div",{className:y.Z.iconRootClose,onClick:be},"✕"):o().createElement("div",{className:y.Z.iconRootAdder,onClick:_e},"+")),ee?o().createElement("div",{className:y.Z.markAdder},o().createElement("input",{className:y.Z.markInput,ref:ae,onKeyUp:Ee,onChange:xe}),o().createElement("button",{className:y.Z.button,onClick:we},"确定")):null),o().createElement("div",{className:y.Z.rightBox},o().createElement(g.Z,{content:"编辑返回数据类型"},o().createElement(h.Z,{style:{boxSizing:"border-box"},onClick:ge},"编辑")),o().createElement("div",{className:"".concat(y.Z.codeIcon," ").concat(y.Z.responseCodeIcon," ").concat(R?y.Z.focus:""),onClick:je},k))),le?o().createElement(c.Z,{key:le.id,mark:le,onMarkChange:he,schema:le.resultSchema,error:z,registerBlur:O}):null)),o().createElement(S,{data:C})))}},2383:(e,t,n)=>{n.d(t,{Z:()=>g});var r=n(8156),o=n.n(r),i=n(5874),a=n(5619),l=n(8250),c=n(6178),u=n(5525),s=n(4113),p=n(2544),d=n(1817);function f(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return h(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?h(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var m=function(){return{path:"",title:"",method:"POST",id:(0,c.Vj)(),type:"http",input:"",output:""}};function g(e){var t,n=e.sidebarContext,h=e.style,g=e.onSubmit,v=e.setRender,y=(0,r.useRef)(),_=f((0,r.useState)(n.formModel||m()),2),b=_[0],x=_[1],w=f((0,r.useState)([]),2),E=w[0],O=w[1],S=(0,r.useCallback)((function(){n.type="",n.activeId=void 0,n.isEdit=!1,v(n)}),[]);(0,r.useEffect)((function(){b.path&&O([])}),[b.path]);var k=(0,r.useMemo)((function(){return E.length&&E.includes("path")?"请填写完整的地址":""}),[E]),j=(0,r.useMemo)((function(){return"file:///".concat(Math.random(),"_code")}),[]);return o().createElement(d.Z,{ref:y,style:h,title:null==b?void 0:b.title,extra:o().createElement(a.Z,{type:"primary",size:"small",onClick:function(){(function(){var e,t="";if(b.path?O([]):(O(["path"]),t="接口的请求路径不能为空"),null===(e=b.markList)||void 0===e?void 0:e.length){var n=b.markList;if(n.find((function(e){return"default"===e.id}))){if(n.length>1){var r=n.find((function(e){return!e.predicate||!e.predicate.key||void 0===e.predicate.value||""===e.predicate.value}));r&&(t="数据标记组中【".concat(r.title,"】组的生效标识不存在或标识值为空"),(0,p.notice)(t))}}else t="数据标记组中【默认】组必须存在",(0,p.notice)(t)}else t="数据标记组必须存在",(0,p.notice)(t);return!t})()&&(n.formModel=b,g())}},"保 存"),onClose:S},o().createElement(l.Z,{header:"基本信息",defaultFold:!1},o().createElement(p.NameInput,{defaultValue:b.title,onChange:function(e){return x((function(t){return Object.assign(Object.assign({},t),{title:e.target.value})}))}}),o().createElement(p.AddressInput,{defaultValue:b.path,onChange:function(e){return x((function(t){return Object.assign(Object.assign({},t),{path:e.target.value})}))},validateError:k}),o().createElement(p.MethodRadio,{defaultValue:b.method,onChange:function(e){return x((function(t){return Object.assign(Object.assign({},t),{method:e})}))}})),o().createElement(l.Z,{header:"当开始请求"},o().createElement(p.EditorWithFullScreen,{unique:"request",CDN:u.G3,path:j+"request.js",onChange:(0,s.Ds)((function(e){x((function(t){return Object.assign(Object.assign({},t),{input:encodeURIComponent(e)})}))}),200),value:(0,c.oV)(b.input)})),o().createElement(l.Z,{header:"当返回响应"},o().createElement(p.EditorWithFullScreen,{unique:"response",CDN:u.G3,path:j+"response.js",onChange:(0,s.Ds)((function(e){x((function(t){return Object.assign(Object.assign({},t),{output:encodeURIComponent(e)})}))}),200),value:(0,c.oV)(b.output)})),o().createElement(l.Z,{header:"其他信息"},o().createElement(p.DescriptionInput,{defaultValue:b.desc,onBlur:function(e){return x((function(t){return Object.assign(Object.assign({},t),{desc:e.target.value})}))}}),o().createElement(p.DocInput,{onBlur:function(e){return x((function(t){return Object.assign(Object.assign({},t),{doc:e.target.value})}))},defaultValue:b.doc})),o().createElement(l.Z,{header:"接口调试",defaultFold:!1},o().createElement(i.Z,{model:b,connect:n.connector.test,onChangeModel:x,registerBlur:null===(t=y.current)||void 0===t?void 0:t.registerBlur})))}},9459:(e,t,n)=>{n.d(t,{Z:()=>p});var r=n(8156),o=n.n(r),i=n(1817),a=n(6017),l=n(5132),c=n(2544),u=n(5619);function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}const p=function(e){var t,n=e.onClose,p=e.style,d=e.folder,f=e.onSubmit,h=function(e){if(Array.isArray(e))return e}(t=(0,r.useState)(d.content.title))||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(t)||function(e,t){if(e){if("string"==typeof e)return s(e,2);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,2):void 0}}(t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),m=h[0],g=h[1],v=(0,r.useCallback)((function(){return f(Object.assign(Object.assign({},d),{content:Object.assign(Object.assign({},d.content),{title:m})}))}),[m,d,f]);return o().createElement(i.Z,{style:p,title:"新建文件夹",onClose:n,extra:o().createElement(u.Z,{type:"primary",size:"small",onClick:v},"保 存")},o().createElement(c.Collapse,{header:"基本信息",defaultFold:!1},o().createElement(a.Z,{label:"名称",require:!0},o().createElement(l.Z,{key:"interfaceName",defaultValue:d.content.title,onChange:function(e){return g(e.target.value)},placeholder:"文件夹的名称"}))))}},9642:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(8156),o=n.n(r),i=n(5525),a=n(7233),l=n(8250),c=n(4113),u=n(1817);function s(e){var t=e.onClose,n=e.style,r=e.data;return o().createElement(u.Z,{style:n,title:"全局配置",onClose:t},o().createElement(l.Z,{header:"当开始请求"},o().createElement(a.Z,{width:"100%",height:400,eslint:{src:null===i.G3||void 0===i.G3?void 0:i.G3.eslint},path:"file:///_global_req_code.js",babel:{standalone:null===i.G3||void 0===i.G3?void 0:i.G3.babel},loaderConfig:{paths:null===i.G3||void 0===i.G3?void 0:i.G3.paths},language:"javascript",theme:"light",value:decodeURIComponent(r.config.paramsFn),onChange:(0,c.Ds)((function(e){r.config.paramsFn!==e&&(r.config.paramsFn=e)}),100)})),o().createElement(l.Z,{header:"当返回响应"},o().createElement(a.Z,{width:"100%",height:300,language:"javascript",theme:"light",eslint:{src:null===i.G3||void 0===i.G3?void 0:i.G3.eslint},path:"file:///_global_res_code.js",babel:{standalone:null===i.G3||void 0===i.G3?void 0:i.G3.babel},loaderConfig:{paths:null===i.G3||void 0===i.G3?void 0:i.G3.paths},value:decodeURIComponent(r.config.resultFn),onChange:(0,c.Ds)((function(e){r.config.resultFn!==e&&(r.config.resultFn=e)}),100)})),o().createElement(l.Z,{header:"当接口响应错误时"},o().createElement(a.Z,{width:"100%",height:400,language:"javascript",theme:"light",path:"file:///_global_error_code.js",eslint:{src:null===i.G3||void 0===i.G3?void 0:i.G3.eslint},babel:{standalone:null===i.G3||void 0===i.G3?void 0:i.G3.babel},loaderConfig:{paths:null===i.G3||void 0===i.G3?void 0:i.G3.paths},value:decodeURIComponent(r.config.errorResultFn||i.tn),onChange:(0,c.Ds)((function(e){r.config.errorResultFn!==e&&(r.config.errorResultFn=e)}),100)})))}},6621:(e,t,n)=>{n.d(t,{Z:()=>f});var r=n(8156),o=n.n(r),i=n(9493),a=n(6178),l=n(7957),c=n(4113),u=n(4126);function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return p(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?p(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d={name:"root",type:"root",children:[]};function f(e){var t,n=e.schema,p=e.value,f=e.onChange,h=(0,r.useRef)(p),m=s((0,r.useState)((0,c.Xh)(d)),2),g=m[0],v=m[1];h.current=g;var y=(0,r.useCallback)((function(){v(Object.assign({},h.current));var e=(0,l.V)(h.current);f(e)}),[n,f]),_=(0,r.useCallback)((function(e){["minLength","maxLength","minimum","maximum"].forEach((function(t){Reflect.deleteProperty(e,t)})),e.children=[]}),[]),b=(0,r.useCallback)((function(e,t,n){e[t]!==n&&(e[t]=n,"type"===t&&(_(e),e.defaultValue="","array"===n&&(e.children=[{name:"items",type:"string",id:(0,a.Vj)()}])),function(e,t,n){Reflect.deleteProperty(e,"minError"),Reflect.deleteProperty(e,"maxError"),[["minLength","maxLength",!1],["maxLength","minLength",!0],["minItems","maxItems",!1],["maxItems","minItems",!0],["minimum","maximum",!1],["maximum","minimum",!0]].forEach((function(r){var o=s(r,3),i=o[0],a=o[1],l=o[2];!function(e,n,r,o,i){t===r&&void 0!==e[o]&&(i?n<e[o]:n>e[o])&&(t.startsWith("min")?e.minError=!0:e.maxError=!0)}(e,n,i,a,l)}))}(e,t,n),y())}),[]);(0,r.useEffect)((function(){v(n?(0,l.n)(n):(0,c.Xh)(d))}),[n]);var x=function(e,t){var n=(0,a.Vj)();if(!e||"object"!==e.type&&"array"!==e.type){t.children=t.children||[];var r="name".concat(t.children.length+1);t.children.push({id:n,type:"string",name:r})}else{e.children=e.children||[];var o="name".concat(e.children.length+1);"array"===e.type&&(o="".concat(e.children.length)),e.children.push({id:n,name:o,type:"string"})}y()},w=(0,r.useCallback)((function(e,t){return e.children.map((function(n){return E(n,e,t)}))}),[]),E=(0,r.useCallback)((function(e,t){var n,r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(!e)return null;var l,s=e.type;if("root"===s)return o().createElement("div",{className:u.Z.list},w(e,a+1));e.children&&(l=w(e,a+1));var p="array"===t.type,d=0===a&&(null===(r=null===(n=t.children)||void 0===n?void 0:n[Math.min((0,c.qr)(t.children,(function(e){var t=e.type;return"string"===t||"number"===t||"boolean"===t})),t.children.length-1)])||void 0===r?void 0:r.name)===e.name||"object"===s||p&&"items"===e.name&&("object"===s||"array"===s),f=!(p&&"items"===e.name);return o().createElement("div",{key:e.id,className:u.Z.ct},o().createElement("div",{className:u.Z.item},o().createElement("input",{style:{width:331-20*a},type:"text",value:p&&"items"!==e.name?"[".concat(e.name,"]"):e.name,disabled:p,onChange:function(t){return b(e,"name",t.target.value)}}),o().createElement("select",{className:u.Z.type,value:e.type,onChange:function(t){return b(e,"type",t.target.value)}},o().createElement("option",{label:"字符",value:"string"}),o().createElement("option",{label:"数字",value:"number"}),o().createElement("option",{label:"布尔",value:"boolean"}),o().createElement("option",{label:"对象",value:"object"}),o().createElement("option",{label:"列表",value:"array"})),o().createElement("div",{className:"".concat(u.Z.operate," ").concat(u.Z.flex)},f?o().createElement("span",{className:"".concat(u.Z.iconRemove),onClick:function(){return function(e,t){t.children=t.children.filter((function(t){return t.name!==e.name})),"array"===t.type&&t.children.forEach((function(e,n){e.name="".concat(n),e.defaultValue=t.children[n].defaultValue})),y()}(e,t)}},i.Od):null,d?o().createElement("span",{className:u.Z.iconAdder,onClick:function(){return x(e,t)}},"+"):null)),l)}),[f]);return o().createElement("div",{className:u.Z.editContainer},0===(null===(t=null==g?void 0:g.children)||void 0===t?void 0:t.length)?o().createElement("div",{className:u.Z.adder},o().createElement("span",{onClick:function(){return x(h.current,h.current)}},"+")):o().createElement(o().Fragment,null,o().createElement("div",{className:u.Z.header},o().createElement("p",{className:u.Z.fieldName},"字段名"),o().createElement("p",{className:u.Z.type},"类型"),o().createElement("p",{className:u.Z.operate},"操作")),o().createElement("div",{className:u.Z.content},E(h.current,h.current))))}},7957:(e,t,n)=>{n.d(t,{V:()=>a,n:()=>o});var r=n(6178);function o(e){var t={name:"root",type:"root",children:[]};return function e(t,n,o){var i,a=o;if(n&&(a={id:(0,r.Vj)(),name:n,type:t.type,children:[]},o.children.push(a)),"array"===t.type){var l={id:(0,r.Vj)(),name:"items",type:(null===(i=t.items)||void 0===i?void 0:i.type)||"object",children:[]};a.children.push(l),function(t,n){var r;Object.keys((null===(r=t.items)||void 0===r?void 0:r.properties)||{}).map((function(r){e(t.items.properties[r],r,n)}))}(t,l)}else"object"===t.type&&function(t,n){Object.keys(t.properties||{}).map((function(r){return e(t.properties[r],r,n)}))}(t,a)}(e,"",t),t}function i(e,t){["type","defaultValue","minItems","maxItems","minLength","maxLength","minimum","maximum"].forEach((function(n){void 0!==e[n]&&(t["defaultValue"===n?"default":n]=e[n])}))}function a(e){if(e){var t={},n=e.type;if("string"===n||"number"===n||"boolean"===n){var r={};return i(e,r),r}return e.children&&e.children.forEach((function(r){t.type=n,"object"===n||"root"===n?(t.type="object",t.properties=t.properties||{},t.properties[r.name]=a(r)):(i(e,t),t[r.name]=a(r))})),t}}},4135:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8156),o=n.n(r),i=n(5619),a=n(9344);function l(e){var t,n=e.onDebugClick,l=e.params,c=e.showTip,u=e.onCloseTip,s=e.onConfirmTip,p=e.onToggleSchemaPreview,d=e.showPreviewSchema,f=(0,r.useCallback)((function(e){return e.children.map((function(t){return h(t,e)}))}),[]),h=(0,r.useCallback)((function(e,t){var n,r,i,l;if(!e)return null;if("root"===e.type&&!e.children)return null;"root"===e.type&&(e.name=""),e.children&&(l=f(e));var c="array"===t.type,u="object"===e.type,s="root"===e.type,p="array"===e.type,d=u||s||p;return o().createElement("div",{className:a.Z.ct,key:e.id||"root"},o().createElement("div",{className:"".concat(a.Z.item," ").concat(s?a.Z.rootItem:"")},o().createElement("div",{style:{padding:"0 10px 0 2px"}},c?"[".concat(e.name,"]"):e.name,o().createElement("span",{className:a.Z.typeName},"(",function(e){switch(e){case"number":return"数字";case"string":return"字符";case"boolean":return"布尔";case"object":case"root":return"对象";case"array":return"列表";case"any":return"文件"}}(e.type),")")),d?null:o().createElement("input",{key:"any"===e.type?null===(n=e.defaultValue)||void 0===n?void 0:n.name:e.defaultValue,className:a.Z.column,type:"text",disabled:!0,value:"any"===e.type?null===(r=e.defaultValue)||void 0===r?void 0:r.name:e.defaultValue,title:"any"===e.type?null===(i=e.defaultValue)||void 0===i?void 0:i.name:e.defaultValue})),l)}),[]);return o().createElement("div",{className:a.Z.debug},o().createElement("div",{className:a.Z.content},(null===(t=null==l?void 0:l.children)||void 0===t?void 0:t.length)?h(Object.assign({type:"root"},l),Object.assign({type:"root"},l)):null),o().createElement("div",{className:a.Z.connectionButton},o().createElement(i.Z,{onClick:n,type:"primary",style:{marginTop:12}},"连接测试"),c?o().createElement("div",{className:a.Z.tipContainer},o().createElement("div",null,"响应值类型跟已有类型存在冲突，确定要替换为当前类型吗？",o().createElement("span",{className:a.Z.preview,onClick:p},d?"关闭类型预览":"预览最新类型")),o().createElement("div",{className:a.Z.buttonGroup},o().createElement(i.Z,{onClick:u},"取消"),o().createElement(i.Z,{onClick:s,type:"primary",style:{marginLeft:12}},"确认"))):null))}},100:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(8156),o=n.n(r),i=n(9493),a=n(6178),l=n(4113),c=n(1575);function u(e){var t,n,u=e.value,s=e.onChange,p=(0,r.useRef)(u);p.current=u;var d=function(){return s(Object.assign({},p.current))},f=function(e,t,n){e[t]!==n&&(e[t]=n,"type"===t&&(e.defaultValue="boolean"===n||"",e.children=[]),d())},h=function(e,t){return e.children.map((function(n){return m(n,e,t)}))},m=function(e,t){var n,r,u,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1;if(!e)return null;if("root"===e.type)return o().createElement("div",{className:c.Z.list},h(e,s+1));e.children&&(u=h(e,s+1));var p="array"===t.type,m=0===s&&(null===(r=null===(n=t.children)||void 0===n?void 0:n[Math.min((0,l.qr)(t.children,(function(e){var t=e.type;return"string"===t||"number"===t||"any"===t||"boolean"===t})),t.children.length-1)])||void 0===r?void 0:r.name)===e.name||"object"===e.type||"array"===e.type;return o().createElement("div",{key:e.id,className:c.Z.ct},o().createElement("div",{className:c.Z.item},o().createElement("input",{style:{width:270-20*s},type:"text",value:p?"[".concat(e.name,"]"):e.name,disabled:p,onChange:function(t){return f(e,"name",t.target.value)}}),o().createElement("select",{className:c.Z.column2,value:e.type,style:{color:"#8d7a34",cursor:"pointer"},onChange:function(t){return f(e,"type",t.target.value)}},o().createElement("option",{label:"字符",value:"string"}),o().createElement("option",{label:"数字",value:"number"}),o().createElement("option",{label:"布尔",value:"boolean"}),o().createElement("option",{label:"对象",value:"object"}),o().createElement("option",{label:"列表",value:"array"}),o().createElement("option",{label:"文件",value:"any"})),"any"===e.type?e.defaultValue&&Object.keys(e.defaultValue).length?o().createElement("span",{className:c.Z.uploadFileName},e.defaultValue.name,o().createElement("span",{className:c.Z.clear,onClick:function(){return f(e,"defaultValue",void 0)}},"X")):o().createElement("input",{className:c.Z.uploadFile,type:"file",onChange:function(t){var n;return f(e,"defaultValue",null===(n=t.target.files)||void 0===n?void 0:n[0])}}):"boolean"===e.type?o().createElement("select",{className:c.Z.column3,value:Number(e.defaultValue),style:{color:"#8d7a34",cursor:"pointer"},onChange:function(t){return f(e,"defaultValue",Boolean(Number(t.target.value)))}},o().createElement("option",{label:"true",value:1}),o().createElement("option",{label:"false",value:0})):o().createElement("input",{className:c.Z.column3,type:"text",disabled:"object"===e.type||"array"===e.type,value:e.defaultValue,onChange:function(t){return f(e,"defaultValue",t.target.value)}}),o().createElement("div",{className:"".concat(c.Z.column4," ").concat(c.Z.flex)},o().createElement("span",{className:"".concat(c.Z.iconRemove),onClick:function(){return function(e,t){t.children=t.children.filter((function(t){return t.name!==e.name})),"array"===t.type&&t.children.forEach((function(e,n){e.name="".concat(n),e.defaultValue=t.children[n].defaultValue})),d()}(e,t)}},i.Od),m?o().createElement("span",{className:c.Z.iconAdder,onClick:function(){return function(e,t){var n=(0,a.Vj)();if(!e||"object"!==e.type&&"array"!==e.type){t.children=t.children||[];var r="name".concat(t.children.length+1);t.children.push({id:n,type:"string",name:r})}else{e.children=e.children||[];var o="name".concat(e.children.length+1);"array"===e.type&&(o="".concat(e.children.length)),e.children.push({id:n,name:o,type:"string"})}d()}(e,t)}},"+"):null)),u)};return o().createElement(o().Fragment,null,o().createElement("div",null,0===(null===(t=null==u?void 0:u.children)||void 0===t?void 0:t.length)?null:o().createElement(o().Fragment,null,o().createElement("div",{className:c.Z.header},o().createElement("p",{className:c.Z.column1},"字段名"),o().createElement("p",{className:c.Z.column2},"类型"),o().createElement("p",{className:c.Z.column3},"调试值"),o().createElement("p",{className:c.Z.column4},"操作")),o().createElement("div",{className:c.Z.content},m(u,u))),(null===(n=null==u?void 0:u.children)||void 0===n?void 0:n.every((function(e){var t=e.type;return"object"===t||"array"===t})))?o().createElement("span",{className:c.Z.iconRootAdder,onClick:function(){return p.current.children.push({type:"string",id:(0,a.Vj)(),name:"name".concat(p.current.children.length+1)}),void d()}},"+"):null))}},1754:(e,t,n)=>{n.d(t,{Z:()=>f});var r=n(8156),o=n.n(r),i=n(4113),a=n(5525),l=n(2544),c=n(8327);function u(e){return function(e){if(Array.isArray(e))return d(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||p(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||p(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(e,t){if(e){if("string"==typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(e){var t,n,p,d=e.mark,f=e.onMarkChange,m=e.schema,g=e.error,v=e.noMark,y=e.registerBlur,_=(0,r.useRef)(),b=(0,r.useRef)(""),x=s((0,r.useState)(),2),w=x[0],E=x[1],O=s((0,r.useState)(a.Nd),2),S=O[0],k=O[1],j=(0,r.useCallback)((function(e){for(var t,n,r,o,i,c=a.d9[e]||[],s=(null===(t=b.current)||void 0===t?void 0:t.split("."))||[],p=m;s.length&&p;){var g=s.shift();p=(null===(n=p.properties)||void 0===n?void 0:n[g])||(null===(o=null===(r=p.items)||void 0===r?void 0:r.properties)||void 0===o?void 0:o[g])}if(p&&p.type&&!s.length)if(c.includes("any")||c.includes(p.type)&&!s.length)if("predicate"===e){var v={key:b.current,value:1,operator:"="};"boolean"===p.type?v.value=!0:"string"===p.type&&(v.value="success"),f(Object.assign(Object.assign({},d),{predicate:v}))}else{var y=d.outputKeys||[],_=d.excludeKeys||[];_.some((function(e){return e===b.current}))||(y=[].concat(u(y.filter((function(e){return!(e.includes(b.current)||b.current.includes(e))}))),[b.current]).filter((function(e){return""!==e}))),_=_.filter((function(e){return e!==b.current})),f(Object.assign(Object.assign({},d),{excludeKeys:_,outputKeys:y}))}else(0,l.notice)("【".concat(a.zi[e],"】所标识数据类型必须为 ").concat(null===(i=a.d9[e])||void 0===i?void 0:i.map((function(e){return h(e)})).join("、")));else(0,l.notice)("【".concat(a.zi[e],"】所标识数据类型不存在"))}),[d,m]),C=(0,r.useCallback)((function(e,t){var n,r,o,i,l=e.currentTarget,c=_.current.getBoundingClientRect(),u=l.getBoundingClientRect();b.current=t;for(var s=a.Nd,p=(null===(n=b.current)||void 0===n?void 0:n.split("."))||[],d=m;p.length&&d;){var f=p.shift();if("array"===(null==(d=(null===(r=d.properties)||void 0===r?void 0:r[f])||(null===(i=null===(o=d.items)||void 0===o?void 0:o.properties)||void 0===i?void 0:i[f]))?void 0:d.type)||!p.length&&"object"===(null==d?void 0:d.type)){s=a.Nd.filter((function(e){return"predicate"!==e.key}));break}}var h=u.y-c.y+l.offsetHeight,g=28*s.length+10;(h+g>c.height||u.top+g>document.body.clientHeight)&&(h-=g+l.offsetHeight),k(s),E({display:"block",left:u.x-c.x,top:h}),null==y||y("return-schema",(function(){return E(void 0)}))}),[y]),A=(0,r.useCallback)((function(e,t){var n;if((null===(n=d.predicate)||void 0===n?void 0:n.key)!==t){var r=d.outputKeys.filter((function(e){return e!==t})).filter((function(e){return""!==e})),o=d.excludeKeys;d.outputKeys.some((function(e){return e===t}))||(o=[].concat(u(o.filter((function(e){return!(e.includes(t)||t.includes(e))}))),[t])),f(Object.assign(Object.assign({},d),{outputKeys:r,excludeKeys:o}))}else f(Object.assign(Object.assign({},d),{predicate:{}}))}),[d]),Z=(0,r.useCallback)((function(e){E(void 0),y("return-schema",(function(){})),e.stopPropagation()}),[y]);if(g){var T="string"==typeof g?g:(null==g?void 0:g.message)||"接口错误：无具体错误信息";return o().createElement("div",{className:c.Z.errorInfo},o().createElement("span",null,T),o().createElement("div",null,(null===(n=(t=T).includes)||void 0===n?void 0:n.call(t,"Network Error"))?"请检查网络是否正常、当前请求是否存在跨域":(null===(p=t.includes)||void 0===p?void 0:p.call(t,"404"))?"请检查请求地址是否拼写错误":""))}return m?o().createElement("div",{className:c.Z.returnParams,style:v?{marginTop:0}:void 0,ref:_,onClick:Z},o().createElement("div",null,function e(t){var n,r,a,l,u,s,p,m=t.val,g=t.key,y=t.xpath,_=t.root;"array"===m.type?p=function(t,n){return t?e({val:t,xpath:n}):null}(m.items,y):"object"===m.type&&(p=function(t,n){return t?o().createElement(o().Fragment,null,Object.keys(t).map((function(r){var o=void 0!==n?n?"".concat(n,".").concat(r):r:void 0;return e({val:t[r],xpath:o,key:r})}))):null}(m.properties,y));var b=!(0,i.xb)(null==d?void 0:d.outputKeys),x=!b&&_||g&&b&&(null===(n=null==d?void 0:d.outputKeys)||void 0===n?void 0:n.includes(y)),w=(null===(r=null==d?void 0:d.predicate)||void 0===r?void 0:r.key)===y,E=void 0!==y&&!(null==d?void 0:d.excludeKeys.some((function(e){return y.startsWith(e)&&e!==y}))),O=void 0!==g&&(x&&!_||((null==d?void 0:d.outputKeys.some((function(e){return null==y?void 0:y.startsWith(e)})))||!b)&&!(null==d?void 0:d.excludeKeys.some((function(e){return y.startsWith(e)}))))||w,S=function(e){return f(Object.assign(Object.assign({},d),{predicate:Object.assign(Object.assign({},d.predicate),{value:e})}))};return o().createElement("div",{key:g,className:"".concat(c.Z.item," ").concat(_?c.Z.rootItem:""," ").concat(x||w?c.Z.markAsReturn:"")},x||w?o().createElement("div",{className:c.Z.marked,"data-content":w?x?"生效标识、返回内容":"生效标识":"返回内容"}):null,(null==d?void 0:d.excludeKeys.includes(y))&&g?o().createElement("div",{className:c.Z.exclude}):null,o().createElement("div",{className:c.Z.keyName},g,o().createElement("span",{className:c.Z.typeName},"(",h(m.type),")"),E&&g&&!v?o().createElement("button",{onClick:function(e){C(e,y),e.stopPropagation()}},"标记"):null,w&&!v?o().createElement(o().Fragment,null,o().createElement("span",{style:{marginLeft:"10px"}},"当值"),o().createElement("select",{value:null===(a=null==d?void 0:d.predicate)||void 0===a?void 0:a.operator,className:c.Z.markValueSelect,onChange:function(e){return f(Object.assign(Object.assign({},d),{predicate:Object.assign(Object.assign({},d.predicate),{operator:e.target.value})}))}},o().createElement("option",{value:"="},"等于"),o().createElement("option",{value:"!="},"不等于")),"string"===m.type?o().createElement("input",{value:null===(l=null==d?void 0:d.predicate)||void 0===l?void 0:l.value,className:c.Z.markValueInput,type:"text",onChange:function(e){return S(e.target.value)}}):null,"number"===m.type?o().createElement("input",{value:Number(null===(u=null==d?void 0:d.predicate)||void 0===u?void 0:u.value),className:c.Z.markValueInput,type:"number",onChange:function(e){return S(Number(e.target.value))}}):null,"boolean"===m.type?o().createElement("select",{value:Number(null===(s=null==d?void 0:d.predicate)||void 0===s?void 0:s.value),className:c.Z.markValueSelect,onChange:function(e){return S(Boolean(Number(e.target.value)))}},o().createElement("option",{value:1},"true"),o().createElement("option",{value:0},"false")):null,o().createElement("span",{style:{marginLeft:0}},"时生效")):null,O&&!v?o().createElement("button",{onClick:function(e){A(e,y),e.stopPropagation()}},"取消"):null),p)}({val:m,xpath:"",root:!0})),o().createElement("div",{className:c.Z.popMenu,style:w},S.map((function(e){return o().createElement("div",{className:c.Z.menuItem,key:e.key,onClick:function(){return j(e.key)},"data-mybricks-tip":{content:e.description}},e.title)})))):o().createElement("div",{className:c.Z.empty},"类型无效，请点击「连接测试」获取类型或手动编辑类型")}function h(e){switch(e){case"number":return"数字";case"string":return"字符";case"boolean":return"布尔";case"object":return"对象";case"array":return"列表"}}},8560:(e,t,n)=>{n.d(t,{Z:()=>d});var r=n(8156),o=n.n(r),i=n(9493),a=n(6233),l=n(5525),c=n(2663);function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function s(){s=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function p(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{p({},"")}catch(e){p=function(e,t,n){return e[t]=n}}function d(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,a=Object.create(i.prototype),l=new N(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function f(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=d;var h="suspendedStart",m="suspendedYield",g="executing",v="completed",y={};function _(){}function b(){}function x(){}var w={};p(w,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&r.call(O,a)&&(w=O);var S=x.prototype=_.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){p(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(o,i,a,l){var c=f(e[o],e,i);if("throw"!==c.type){var s=c.arg,p=s.value;return p&&"object"==u(p)&&r.call(p,"__await")?t.resolve(p.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(p).then((function(e){s.value=e,a(s)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=h;return function(i,a){if(o===g)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===y)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=g;var u=f(t,n,r);if("normal"===u.type){if(o=r.done?v:m,u.arg===y)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),y;var i=f(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,y;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,y):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,y)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(u(t)+" is not iterable")}return b.prototype=x,o(S,"constructor",{value:x,configurable:!0}),o(x,"constructor",{value:b,configurable:!0}),b.displayName=p(x,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===b||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,p(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),p(j.prototype,l,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(d(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),p(S,c,"Generator"),p(S,a,(function(){return this})),p(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,y):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),y}},t}var p=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function l(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}c((r=r.apply(e,t||[])).next())}))};function d(e){var t,n=this,r=e.ctx,u=e.setRender,d=e.blurMap;return o().createElement("div",{className:c.Z.toolbar},o().createElement("div",{className:c.Z.search},o().createElement("input",{type:"text",placeholder:"请输入名称搜索服务接口",onChange:function(e){return r.search(e.target.value)}})),(t=o().createElement("div",{className:c.Z.ct},r.addActions.map((function(e){var t=e.type,i=e.title;return t===l.iO?o().createElement("div",{className:c.Z["separator-divider"]}):o().createElement("div",{className:c.Z.item,onClick:function(){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"http";return p(n,void 0,void 0,s().mark((function t(){return s().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r.type=e,r.activeId=void 0,r.isEdit=!1,r.parent=null,r.formModel={type:e},u(r),e===l.Cq.HTTP?r.addDefaultService():e===l.Cq.FOLDER?r.addServiceFolder():e===l.Cq.IMPORT&&r.importService();case 7:case"end":return t.stop()}}),t)})))}(t)},key:t},i)}))),o().createElement(a.Z,{dropDownStyle:r.type?{right:0}:void 0,onBlur:function(e){return d.toolbar=e},overlay:t},o().createElement("div",{className:c.Z.icon,"data-mybricks-tip":"创建接口",onClick:function(){return Object.keys(d).filter((function(e){return"toolbar"!==e})).forEach((function(e){return d[e]()}))}},i.PD))))}},2453:(e,t,n)=>{n.d(t,{Z:()=>A});var r=n(8156),o=n.n(r),i=n(6178),a=n(5525),l=n(4113),c=n(1988),u=n(2383),s=n(8560),p=n(9493),d=n(9642),f=n(957),h=n(7955),m=n(1221),g=n(9459),v=n(2544),y=n(6233),_=n(5135);function b(e){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b(e)}function x(e){return function(e){if(Array.isArray(e))return S(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||O(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(){w=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i=t&&t.prototype instanceof v?t:v,a=Object.create(i.prototype),l=new N(r||[]);return o(a,"_invoke",{value:C(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var d="suspendedStart",f="suspendedYield",h="executing",m="completed",g={};function v(){}function y(){}function _(){}var x={};u(x,a,(function(){return this}));var E=Object.getPrototypeOf,O=E&&E(E(P([])));O&&O!==n&&r.call(O,a)&&(x=O);var S=_.prototype=v.prototype=Object.create(x);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function j(e,t){function n(o,i,a,l){var c=p(e[o],e,i);if("throw"!==c.type){var u=c.arg,s=u.value;return s&&"object"==b(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(s).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,l)}))}l(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function C(t,n,r){var o=d;return function(i,a){if(o===h)throw new Error("Generator is already running");if(o===m){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var c=A(l,r);if(c){if(c===g)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=m,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?m:f,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=m,r.method="throw",r.arg=u.arg)}}}function A(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,A(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function T(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function N(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(b(t)+" is not iterable")}return y.prototype=_,o(S,"constructor",{value:_,configurable:!0}),o(_,"constructor",{value:y,configurable:!0}),y.displayName=u(_,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,_):(e.__proto__=_,u(e,c,"GeneratorFunction")),e.prototype=Object.create(S),e},t.awrap=function(e){return{__await:e}},k(j.prototype),u(j.prototype,l,(function(){return this})),t.AsyncIterator=j,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new j(s(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(S),u(S,c,"Generator"),u(S,a,(function(){return this})),u(S,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,N.prototype={constructor:N,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(T),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),T(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;T(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function E(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||O(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function O(e,t){if(e){if("string"==typeof e)return S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?S(e,t):void 0}}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var k=function(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function l(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,l)}c((r=r.apply(e,t||[])).next())}))},j=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},C=[{key:"id",name:"标识",copy:!0},{key:"content.title",name:"标题"},{key:"content.method",name:"方法"},{key:"content.path",name:"路径"},{key:"content.doc",name:"文档链接",link:!0},{key:"updateTime",name:"更新时间",format:"YYYY-MM-DD HH:mm:ss"}];const A=function(e){var t,n,O=e.addActions,S=e.connector,A=e.data,Z=e.serviceListUrl,T=e.initialValue,N=void 0===T?{}:T,P=e.visibility,R=(0,r.useRef)(null),L=(0,r.useRef)({}),M=E((0,r.useState)(""),2),I=M[0],F=M[1],D=E((0,r.useState)([]),2),B=D[0],U=D[1],z=(0,r.useState)({activeId:"",type:"",isEdit:!1,formModel:{path:"",title:"",id:"",type:"",input:"",output:""},addActions:[{type:a.Cq.FOLDER,title:"文件夹"}].concat(O?O.some((function(e){return"default"===e.type}))?O:[{type:a.Cq.HTTP,title:"普通接口"}].concat(O):[{type:a.Cq.HTTP,title:"普通接口"}]).concat([{type:a.iO,title:""},{type:a.Cq.IMPORT,title:"导入"}]),connector:{add:function(e){return S.add(Object.assign({},e))},remove:function(e){return S.remove(e)},getAllByType:function(e){var t;return(null===(t=S.getAllByType)||void 0===t?void 0:t.call(S,e))||[]},update:function(e){return S.update(Object.assign({},e))},test:function(){return S.test.apply(S,arguments)}},search:F}),V=E(z,2),q=V[0],H=V[1],J=function(e,t){return k(void 0,void 0,void 0,w().mark((function n(){return w().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return n.abrupt("return",new Promise((function(n){var r=t||q.formModel,o=r.id,l=void 0===o?(0,i.Vj)():o,c=r.script,u=j(r,["id","script"]);if("create"===e){var s={id:l,type:q.formModel.type||q.type||a.Cq.HTTP,content:Object.assign({input:encodeURIComponent(a.Ys),output:encodeURIComponent(a.Aj),inputSchema:{type:"object"}},u),script:c,createTime:Date.now(),updateTime:Date.now()};if(q.parent){var p=(0,i.U6)(A.connectors,q.parent),d=p.index,f=p.parent;f&&f[d].children.push(s)}else A.connectors.push(s);q.connector.add({id:l,type:q.formModel.type||q.type||a.Cq.HTTP,title:u.title,connectorName:a.MK,script:void 0,globalMock:A.config.globalMock,inputSchema:u.inputSchema,markList:u.markList||[]})}else{var h=(0,i.U6)(A.connectors,{id:l}),m=h.index,g=h.parent;if(g){var v=Object.assign(Object.assign({},g[m]),{updateTime:Date.now(),content:Object.assign({},u)});null==g||g.splice(m,1,v);try{q.connector.update({id:l,title:u.title||v.content.title,type:v.type,connectorName:a.MK,script:void 0,globalMock:A.config.globalMock,inputSchema:v.content.inputSchema,markList:v.content.markList||[]})}catch(e){}}}n("")})));case 1:case"end":return n.stop()}}),n)})))},K=(0,r.useCallback)((function(e){return new Promise((function(t){var n,r=(0,i.U6)(A.connectors,e),o=r.index,a=r.parent;null==a||a.splice(o,1),(0,i.uF)(null!==(n=e.children)&&void 0!==n?n:[e]).forEach((function(e){try{q.connector.remove(e.id)}catch(e){}})),t("")}))}),[A]),W=(0,r.useRef)(),Y=(0,r.useCallback)((function(e){H((function(t){return Object.assign(Object.assign(Object.assign({},t),{formModel:Object.assign({},t.formModel)}),e)}))}),[]),G=(0,r.useCallback)((function(e){var t;if(q.isEdit&&e.id===q.activeId)Y({type:"",activeId:void 0,isEdit:!1});else{var n={isEdit:!0,activeId:e.id};if(e.type===a.Cq.TG)n.type=a.Cq.TG,n.formModel=Object.assign({id:e.id,type:e.type},e.content);else if(e.type===a.Cq.FOLDER)n.type=a.Cq.FOLDER,n.formModel=e;else{var r=null===(t=q.addActions.find((function(t){return t.type===e.type})))||void 0===t?void 0:t.noUseInnerEdit;n.type=r?e.type:a.Cq.HTTP,n.formModel=Object.assign(Object.assign({},JSON.parse(JSON.stringify(e.content))),{type:e.type,id:e.id,input:e.content.input||a.Ys,output:e.content.output||a.Aj})}Y(n)}}),[q]),$=(0,r.useCallback)((function(e,t){return k(void 0,void 0,void 0,w().mark((function n(){return w().wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return q.formModel=(0,l.Xh)(e.content),q.parent=t,q.formModel.title+=" 复制",q.formModel.id=(0,i.Vj)(),Y(q),n.next=7,J("create");case 7:case"end":return n.stop()}}),n)})))}),[]),Q=(0,r.useCallback)((function(e){return k(void 0,void 0,void 0,w().mark((function t(){var n;return w().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.type,a.Cq.HTTP,(n=(0,l.Xh)(e)).id=(0,i.Vj)(),(0,m.z)(JSON.stringify({formModel:n})),(0,v.notice)("导出成功",{type:"success",targetContainer:document.body});case 4:case"end":return t.stop()}}),t)})))}),[]),X=(0,r.useCallback)((function(e){return k(void 0,void 0,void 0,w().mark((function t(){return w().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!confirm(e.type===a.Cq.FOLDER?"确认删除文件夹 ".concat(e.content.title," 吗，其包含接口也将被删除"):"确认删除 ".concat(e.content.title," 吗"))){t.next=5;break}return t.next=3,K(e);case 3:q.type="",Y(q);case 5:case"end":return t.stop()}}),t)})))}),[q,A]);q.addDefaultService=(0,r.useCallback)((function(){return k(void 0,void 0,void 0,w().mark((function e(){var t;return w().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:q.isEdit=!1,q.type=a.Cq.HTTP,q.formModel={id:(0,i.Vj)(),title:"",type:(null===(t=q.formModel)||void 0===t?void 0:t.type)||a.Cq.HTTP,path:"",desc:"",method:"GET",input:encodeURIComponent(a.Ys),output:encodeURIComponent(a.Aj)},Y(q);case 4:case"end":return e.stop()}}),e)})))}),[q]),q.addServiceFolder=(0,r.useCallback)((function(){return k(void 0,void 0,void 0,w().mark((function e(){return w().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:q.isEdit=!1,q.type=a.Cq.FOLDER,q.formModel={id:(0,i.Vj)(),content:{title:"文件夹"},type:a.Cq.FOLDER,children:[]},Y(q);case 4:case"end":return e.stop()}}),e)})))}),[q]),q.importService=(0,r.useCallback)((function(){return k(void 0,void 0,void 0,w().mark((function e(){var t,n,r,o,l,c;return w().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=prompt("将导出的接口数据复制到输入框"),n=!1,null!=t&&""!=t){e.next=6;break}return e.abrupt("return");case 6:try{(r=JSON.parse(t)).formModel&&(n=!0,r.formModel=(0,i.LX)(r.formModel),q.parent?(o=(0,i.U6)(A.connectors,q.parent),l=o.index,(c=o.parent)&&c[l].children.push(r.formModel)):A.connectors.push(r.formModel),(0,i.uF)([r.formModel]).forEach((function(e){q.connector.add({id:e.id,type:e.type||a.Cq.HTTP,title:e.content.title,connectorName:a.MK,script:void 0,globalMock:A.config.globalMock,inputSchema:e.content.inputSchema,markList:e.content.markList||[]})})),(0,v.notice)("导入成功",{type:"success",targetContainer:document.body}))}catch(e){}case 7:n||(0,v.notice)("输入数据格式有误",{targetContainer:document.body});case 8:case"end":return e.stop()}}),e)})))}),[q]),q.updateService=J;var ee=(0,r.useCallback)((function(){q.type=a.iz,Y(q)}),[]),te=(0,r.useCallback)((function(){q.type="",q.isEdit=!1,q.activeId=void 0,q.formModel={},Y(q)}),[]),ne=function(){return k(void 0,void 0,void 0,w().mark((function e(){return w().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!q.isEdit){e.next=5;break}return e.next=3,J();case 3:e.next=7;break;case 5:return e.next=7,J("create");case 7:q.type="",q.activeId=void 0,q.formModel={},q.isEdit=!1,Y(q);case 12:case"end":return e.stop()}}),e)})))},re=function(e){if(q.isEdit){var t=(0,i.U6)(A.connectors,e),n=t.index,r=t.parent;null==r||r.splice(n,1,e)}else if(q.parent){var o=(0,i.U6)(A.connectors,q.parent),a=o.index,l=o.parent;l&&l[a].children.push(e)}else A.connectors.push(e);te()},oe=(0,r.useCallback)((function(e){U((function(t){return t.includes(e.id)?t.filter((function(t){return t!==e.id})):[].concat(x(t),[e.id])}))}),[]),ie=(0,r.useCallback)((function(e){window.open(e)}),[]),ae=(0,r.useCallback)((function(e,t){var n=t.key,r=t.format,i=t.copy,a=t.link,u=t.isTpl;if(r)return(0,c.p)(e[n],r);if(i)return o().createElement("span",{className:_.Z["sidebar-panel-list-item__copy"]},e[n]);if(a)return(0,l.U2)(e,n)?o().createElement("span",{onClick:function(){return ie((0,l.U2)(e,n))},className:_.Z["doc-link"]},"点击跳转"):"无";if(u){var s=e[n];return o().createElement(o().Fragment,null,o().createElement("span",null,"object"===b(s)?s.domain||"无":s||"无"),o().createElement("br",null),(0,l.U2)(e,[n,"laneId"])&&o().createElement("span",null,(0,l.U2)(e,[n,"laneId"])))}return(0,l.U2)(e,n,"无")}),[]),le=(0,r.useCallback)((function(){var e,t,n,r=q.addActions.find((function(e){return e.type===q.type&&e.render})),i=null;return r?i=(null==r?void 0:r.render({onClose:te,originConnectors:(0,l.Xh)(A.connectors),globalConfig:A.config,isEdit:q.isEdit,initService:q.isEdit?q.formModel:void 0,connectorService:{add:function(e){J("create",e)},remove:K,update:function(e){J("update",e)},test:function(e,t,n){return q.connector.test(Object.assign(Object.assign({},e),{connectorName:a.MK,mode:"test"}),t,n)}}}))||null:q.type===a.Cq.HTTP?i=o().createElement(u.Z,{sidebarContext:q,setRender:Y,onSubmit:ne,key:q.type+(null===(e=q.formModel)||void 0===e?void 0:e.id),globalConfig:A.config,style:{top:null===(t=R.current)||void 0===t?void 0:t.getBoundingClientRect().top}}):q.type===a.Cq.FOLDER&&(i=o().createElement(g.Z,{folder:q.formModel,onClose:te,onSubmit:re,style:{top:null===(n=R.current)||void 0===n?void 0:n.getBoundingClientRect().top}})),i}),[q,q.type,Z,J,re]),ce=(0,r.useCallback)((function(){var e;return q.type===a.iz?o().createElement(d.Z,{style:{top:null===(e=R.current)||void 0===e?void 0:e.getBoundingClientRect().top},onClose:te,data:A}):null}),[q]),ue=(0,r.useCallback)((function(e){return e.type===a.Cq.TG?C.filter((function(e){var t=e.key;return!["content.path","content.method","content.desc"].includes(t)})):C}),[]),se=(0,r.useCallback)((function(){var e;0===A.connectors.length&&(null===(e=N.serviceList)||void 0===e?void 0:e.length)&&(A.connectors=N.serviceList,N.serviceList.forEach((function(e){var t=e.content||{},n=t.title,r=t.inputSchema,o=t.outputSchema,i={id:e.id,type:q.formModel.type||q.type||a.Cq.HTTP,title:n,connectorName:a.MK,globalMock:A.config.globalMock,inputSchema:r,outputSchema:o};try{q.connector.add(i)}catch(e){console.log(e)}})))}),[]),pe=(0,r.useCallback)((function(e){A.config.globalMock=e}),[]),de=(0,r.useCallback)((function(){(0,m.z)(JSON.stringify({pluginData:A,designerData:q.addActions.reduce((function(e,t){return Object.assign(Object.assign({},e),(n={},r=t.type,o=q.connector.getAllByType(t.type),(r=function(e){var t=function(e,t){if("object"!==b(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==b(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===b(t)?t:String(t)}(r))in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,n));var n,r,o}),{})}))}),[A,q]);(0,r.useMemo)((function(){if(A){se();try{var e=(0,i.uF)(A.connectors);q.addActions.reduce((function(e,t){return[].concat(x(e),x(q.connector.getAllByType(t.type)))}),[]).forEach((function(t){var n=null==e?void 0:e.find((function(e){return e.id===t.id}));n?n.content.title!==t.title&&q.connector.update(Object.assign(Object.assign({},t),{title:n.content.title})):q.connector.remove(t.id)}))}catch(e){console.log("连接器数据 format 失败",e)}}}),[]);var fe=(0,r.useCallback)((function(e,t,n){var r=(0,i.U6)(A.connectors,e),o=r.parent,a=r.index;o.splice(a,1);var l=(0,i.U6)(A.connectors,t),c=l.parent,u=l.index;"inner"===n?t.children.push(e):c.splice("bottom"===n?u+1:u,0,e)}),[A]);return(0,r.useEffect)((function(){"hidden"===P&&te()}),[P]),o().createElement("div",{ref:R,"data-id":"plugin-root-panel",className:"".concat(_.Z["sidebar-panel"]," ").concat(_.Z["sidebar-panel-open"]),onClick:function(){return Object.values(L.current).forEach((function(e){return e()}))}},o().createElement("div",{className:"".concat(_.Z["sidebar-panel-view"])},o().createElement("div",{className:_.Z["sidebar-panel-header"]},o().createElement("div",{className:_.Z["sidebar-panel-header__title"]},o().createElement("span",{onDoubleClick:de},"服务连接"),o().createElement("div",{className:_.Z.rightOperate},o().createElement("div",{className:_.Z.globalMock,"data-mybricks-tip":"开启全局Mock，页面调试时所有接口将默认使用Mock能力"},o().createElement("span",{className:(null===(t=null==A?void 0:A.config)||void 0===t?void 0:t.globalMock)?_.Z.warning:""},"全局 Mock:"),o().createElement(f.Z,{defaultValue:null===(n=null==A?void 0:A.config)||void 0===n?void 0:n.globalMock,onChange:pe})),o().createElement("div",{className:_.Z.icon,onClick:ee,"data-mybricks-tip":"全局设置，可定义接口全局处理逻辑"},p.t8))),o().createElement(s.Z,{blurMap:L.current,searchValue:I,ctx:q,setRender:Y})),o().createElement("div",{className:_.Z["sidebar-panel-list"]},function e(t,n){return(null==t?void 0:t.length)?t.map((function(t){var r,i=B.includes(t.id);t.updateTime=(0,c.p)(t.updateTime||t.createTime);var l=t.type,u=q.addActions.find((function(e){return e.type===l})),s="接口";q.addActions.length>1&&(s=(null==u?void 0:u.title)||s);var d=(null===(r=null==u?void 0:u.getTitle)||void 0===r?void 0:r.call(u,t))||t.content.title;return o().createElement(o().Fragment,null,o().createElement(h.Z,{key:t.id,item:t,draggable:!0,onDrop:fe},o().createElement("div",{key:t.id,className:"".concat(_.Z["sidebar-panel-list-item"]," ").concat(q.activeId===t.id?_.Z.active:""," ").concat(q.isEdit?q.activeId===t.id?_.Z.chose:_.Z.disabled:"")},o().createElement("div",null,o().createElement("div",{onClick:function(){return oe(t)},className:_.Z["sidebar-panel-list-item__left"]},o().createElement("div",{className:"".concat(_.Z.icon," ").concat(i?_.Z.iconExpand:"")},p.Qx),o().createElement("div",{className:_.Z.tag},s),o().createElement("div",{className:_.Z.name},o().createElement("span",{"data-mybricks-tip":d||void 0},d))),o().createElement("div",{className:_.Z["sidebar-panel-list-item__right"]},o().createElement("div",{"data-mybricks-tip":"编辑",ref:W,className:_.Z.action,onClick:function(){return G(t)}},p.eP),l===a.Cq.FOLDER?o().createElement(y.Z,{dropDownStyle:{right:0},onBlur:function(e){return L.current["toolbar"+t.id]=e},overlay:o().createElement("div",{className:_.Z.dropdownItem},q.addActions.map((function(e){var n=e.type,r=e.title;return n===a.iO?o().createElement("div",{className:_.Z["separator-divider"]}):o().createElement("div",{className:_.Z.item,key:n,onClick:function(){q.activeId=void 0,q.parent=t,n===a.Cq.HTTP?q.addDefaultService():n===a.Cq.FOLDER?q.addServiceFolder():n===a.Cq.IMPORT?q.importService():(q.type=n,q.isEdit=!1,q.formModel={type:n},Y(q))}},r)})))},o().createElement("div",{className:_.Z.action,"data-mybricks-tip":"创建接口",onClick:function(){return Object.keys(L.current).filter((function(e){return e!=="toolbar".concat(t.id)})).forEach((function(e){return L.current[e]()}))}},p.PD)):o().createElement("div",{"data-mybricks-tip":"复制",className:_.Z.action,onClick:function(){return $(t,n)}},p.JG),o().createElement("div",{"data-mybricks-tip":"导出",className:_.Z.action,onClick:function(){return Q(t)}},p.cK),o().createElement("div",{"data-mybricks-tip":"删除",className:_.Z.action,onClick:function(){return X(t)}},p.Od))))),i?l===a.Cq.FOLDER?o().createElement("div",{className:_.Z.folderList},e(t.children,t)):o().createElement("div",{className:_.Z["sidebar-panel-list-item__expand"]},ue(t).map((function(e){return o().createElement("div",{className:_.Z["sidebar-panel-list-item__param"],key:e.key},o().createElement("span",{className:_.Z["sidebar-panel-list-item__name"],style:{width:e.width}},e.name,":"),o().createElement("span",{className:_.Z["sidebar-panel-list-item__content"]},ae(t,e)))}))):null)})):o().createElement(h.Z,{parent:n,item:null,draggable:!0,onDrop:fe},o().createElement("div",{className:_.Z.empty,style:n?{borderBottom:"1px solid #ccc"}:void 0},"暂无接口，请点击新建接口"))}((0,i.FS)(null==A?void 0:A.connectors,I),null))),le(),ce())}},8704:(__unused_webpack_module,__nested_webpack_exports__,__nested_webpack_require_173954__)=>{__nested_webpack_require_173954__.d(__nested_webpack_exports__,{call:()=>call});var axios__WEBPACK_IMPORTED_MODULE_2__=__nested_webpack_require_173954__(5274),_script__WEBPACK_IMPORTED_MODULE_0__=__nested_webpack_require_173954__(8543),_utils_lodash__WEBPACK_IMPORTED_MODULE_1__=__nested_webpack_require_173954__(4113);function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"===_typeof(t)?t:String(t)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}var __rest=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n},defaultFn=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Object.assign(Object.assign({},e),n)},httpRegExp=new RegExp("^(http|https)://");function call(connector,params,config){return new Promise((function(resolve,reject){try{var fn=connector.script?eval("(".concat(decodeURIComponent(connector.script),")")):getFetch(connector),_a=config||{},_a$before=_a.before,before=void 0===_a$before?defaultFn:_a$before,otherConfig=__rest(_a,["before"]);fn(params,{then:resolve,onError:reject},Object.assign(Object.assign({},otherConfig),{ajax:function(e){var t,n=before(Object.assign({},e)),r=n.url;return r||reject("请求路径不能为空"),connector.useProxy&&httpRegExp.test(r)&&(null===(t=r.match(/^https?:\/\/([^/#&?])+/g))||void 0===t?void 0:t[0])!==location.origin?(0,axios__WEBPACK_IMPORTED_MODULE_2__.Z)(Object.assign(Object.assign({},n),{url:"/paas/api/proxy",headers:Object.assign(Object.assign({},n.headers||{}),_defineProperty({},"x-target-url",n.url)),data:n.data})).then((function(e){var t;return null===(t=null==config?void 0:config.onResponseInterception)||void 0===t||t.call(config,e),e.data})).catch((function(e){var t;throw e.response&&(null===(t=null==config?void 0:config.onResponseInterception)||void 0===t||t.call(config,e.response)),e})):(0,axios__WEBPACK_IMPORTED_MODULE_2__.Z)(n||e).then((function(e){var t;return null===(t=null==config?void 0:config.onResponseInterception)||void 0===t||t.call(config,e),e.data})).catch((function(e){var t;throw e.response&&(null===(t=null==config?void 0:config.onResponseInterception)||void 0===t||t.call(config,e.response)),e}))}}))}catch(e){console.log("连接器错误",e),reject("连接器script错误.")}}))}var setData=function(e,t,n){var r=t.length;return function e(n,o,i){if(!n||o===r)return n;var a=t[o];return Array.isArray(n)?n.map((function(t,n){var r,a=i[n];return void 0===a?(r={},i.push(r)):r=a,e(t,o,r)})):o===r-1?(i[a]=n[a],n[a]):(n=n[a],Array.isArray(n)?i[a]=i[a]||[]:i[a]=i[a]||{},e(n,o+1,Array.isArray(i)?i:i[a]))}(e,0,n)},del=function(e,t){var n=t.length;!function e(r,o){if(r&&o!==n){var i=t[o];o===n-1&&Reflect.deleteProperty(r,i),Array.isArray(r)?r.forEach((function(t){e(t,o)})):e(r[i],o+1)}}(e,0)},pluginRun=function pluginRun(functionString){return eval("(() => { return ".concat(functionString?(0,_script__WEBPACK_IMPORTED_MODULE_0__.T)(functionString):"_ => _;","})()"))},getFetch=function(e){return function(t,n,r){var o=n.then,i=n.onError,a=e.method,l=e.path.trim(),c=e.outputKeys||[],u=e.excludeKeys||[],s="test"===e.mode,p=e.markList||[];p.length||p.push({title:"默认",id:"default",predicate:{},outputKeys:c,excludeKeys:u});try{s&&console.log("【连接器调试日志】接口传入参数：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(t));var d=l,f=["GET","DELETE"].includes(a)?{params:t,data:{},header:{},url:d,method:a}:{params:{},data:t,header:{},url:d,method:a};s&&console.log("【连接器调试日志】全局入参拦截器(执行前配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(f));var h=pluginRun(e.globalParamsFn)(f);s&&console.log("【连接器调试日志】全局入参拦截器(执行后配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(h)),h.url=h.url||d,h.method=h.method||a,s&&console.log("【连接器调试日志】接口自定义入参拦截器(执行前配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(h));var m=pluginRun(e.input)(h);if(s&&console.log("【连接器调试日志】接口自定义入参拦截器(执行后配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(m)),s&&console.log("【连接器调试日志】接口请求路径模板字符串处理(执行前配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(m)),"object"===_typeof(m.params||m.data)){var g=(m.params||m.data)instanceof FormData,v=[];m.url=(m.url||d).replace(/{([^}]+)}/g,(function(e,t){var n=t?t.split("."):[],r=m.params||m.data;n.length||i("请求路径中模板字符串错误");var o=0;for(v.push(n[0]);n.length;){var a=n.shift();if(!r)return void i("请求路径中模板字符串的参数(".concat(t,")缺失"));var l=r[a];if(r instanceof FormData&&(l=r.get(a),0===o&&n.length))try{l=JSON.parse(l)}catch(e){return void i("请求路径中模板字符串的参数(".concat(t,")缺失"))}null==l&&i("请求路径中模板字符串的参数(".concat(t,")缺失")),o++,r=l}return r})),g?(v.forEach((function(e){(m.params||m.data).delete(e)})),(m.params||m.data).delete("MYBRICKS_HOST")):(v.forEach((function(e){Reflect.deleteProperty(m.params||m.data||{},e)})),Reflect.deleteProperty(m.params||m.data||{},"MYBRICKS_HOST"))}s&&console.log("【连接器调试日志】接口请求路径模板字符串处理(执行后配置)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(m)),m.method=m.method||a;var y=!1,_=[],b=[],x="then";r.ajax(m).catch((function(t){if(e.globalErrorResultFn&&(t.response||"AxiosError"===t.name)){var n=t.response||{data:{}};!n.data&&(n.data={}),pluginRun(e.globalErrorResultFn)({error:t,response:n,config:m},{throwError:function(){y=!0,i.apply(void 0,arguments)}})}else i(t);throw Error("HTTP_FETCH_ERROR")})).then((function(t){s&&console.log("【连接器调试日志】全局出参拦截器(执行前数据)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(t));var n=pluginRun(e.globalResultFn)({response:t,config:m},{throwError:i});return s&&console.log("【连接器调试日志】全局出参拦截器(执行后数据)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(n)),n})).then((function(t){s&&console.log("【连接器调试日志】接口自定义出参拦截器(执行前数据)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(t));var n=pluginRun(e.output)(t,Object.assign({},m),{throwError:i});s&&console.log("【连接器调试日志】接口自定义出参拦截器(执行后数据)：",(0,_utils_lodash__WEBPACK_IMPORTED_MODULE_1__.Xh)(n));for(var r=0;r<p.length;r++){var o=p[r],a=o.id,l=o.predicate,c=void 0===l?{key:"",value:void 0}:l,u=o.excludeKeys,d=o.outputKeys;if(!c||!c.key||void 0===c.value){_=d,b=u,x="default"===a?"then":a;break}for(var f=n,h=c.key.split(".");f&&h.length;)f=f[h.shift()];if(!h.length&&("="===c.operator?f===c.value:f!==c.value)){_=d,b=u,x="default"===a?"then":a;break}}return n})).then((function(t){if("test"!==e.mode)return null==b||b.forEach((function(e){return del(t,e.split("."))})),t;o(t)})).then((function(e){var t=Array.isArray(e)?[]:{};if(void 0===_||0===_.length)t=e;else if(_.forEach((function(n){setData(e,n.split("."),t)})),Array.isArray(_)&&_.length&&(_.length>1||1!==_.length||""!==_[0]))try{for(var n=_.map((function(e){return e.split(".")}));"[object Object]"===Object.prototype.toString.call(t)&&n.every((function(e){return!!e.length}))&&1===Object.values(t).length;)t=Object.values(t)[0],n.forEach((function(e){return e.shift()}))}catch(e){console.log("connector format data error",e)}o(r.isMultipleOutputs?{__OUTPUT_ID__:x,__ORIGIN_RESPONSE__:t}:t)})).catch((function(t){"HTTP_FETCH_ERROR"===(null==t?void 0:t.message)?e.globalErrorResultFn&&!y&&i("全局拦截响应错误函数中必须调用 throwError 方法，请前往修改"):i((null==t?void 0:t.message)||t)}))}catch(e){return i(e)}}}},8543:(e,t,n)=>{n.d(t,{T:()=>i,i:()=>a});var r=n(5525),o=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n};function i(e){return e?decodeURIComponent(e).replace(/export\s+default.*function.*\(/,"function _RT_("):e}function a(e){return encodeURIComponent(function(e,{then:t,onError:n},r){return function(e,r){const o="__method__",i=__outputKeys__,a=__excludeKeys__,l=__isTestMode__,c=__globalErrorResultFn__,u=__markList__;u.length||u.push({title:"默认",id:"default",predicate:{},outputKeys:i,excludeKeys:a});try{const i="__path__",a=__globalParamsFn__(["GET","DELETE"].includes(o)?{params:e,data:{},header:{},url:i,method:o}:{params:{},data:e,header:{},url:i,method:o}),s=__hasGlobalResultFn__;a.url=a.url||i,a.method=a.method||o;const p=__input__(a);if("object"==typeof(p.params||p.data)){const e=(p.params||p.data)instanceof FormData,t=[];p.url=(p.url||i).replace(/{([^}]+)}/g,((e,r)=>{const o=r?r.split("."):[];let i=p.params||p.data;o.length||n("请求路径中模板字符串错误");let a=0;for(t.push(o[0]);o.length;){const t=o.shift();if(!i)return void n(`请求路径中模板字符串的参数(${r})缺失`);let l=i[t];if(i instanceof FormData&&(l=i.get(t),0===a&&o.length))try{l=JSON.parse(l)}catch(e){return void n(`请求路径中模板字符串的参数(${r})缺失`)}null==l&&n(`请求路径中模板字符串的参数(${r})缺失`),a++,i=l}return i})),e?(t.forEach((e=>{(p.params||p.data).delete(e)})),(p.params||p.data).delete("MYBRICKS_HOST")):(t.forEach((e=>{Reflect.deleteProperty(p.params||p.data||{},e)})),Reflect.deleteProperty(p.params||p.data||{},"MYBRICKS_HOST"))}p.method=p.method||o;let d=!1,f=[],h=[],m="then";r.ajax(p).catch((e=>{if(c&&(e.response||"AxiosError"===e.name)){const t=e.response||{data:{}};!t.data&&(t.data={}),c({error:e,response:t,config:p},{throwError:(...e)=>{d=!0,n(...e)}})}else n(e);throw Error("HTTP_FETCH_ERROR")})).then((e=>s?__globalResultFn__({response:e,config:p},{throwError:n}):e)).then((e=>{const t=__output__(e,Object.assign({},p),{throwError:n});for(let e=0;e<u.length;e++){const{id:n,predicate:r={key:"",value:void 0},excludeKeys:o,outputKeys:i}=u[e];if(!r||!r.key||void 0===r.value){f=i,h=o,m="default"===n?"then":n;break}let a=t,l=r.key.split(".");for(;a&&l.length;)a=a[l.shift()];if(!l.length&&("="===r.operator?a===r.value:a!==r.value)){f=i,h=o,m="default"===n?"then":n;break}}return t})).then((e=>{if(!l)return 0===h.length||h.forEach((t=>function(e,t){const n=t.length;!function e(r,o){if(!r||o===n)return;const i=t[o];o===n-1&&Reflect.deleteProperty(r,i),Array.isArray(r)?r.forEach((t=>{e(t,o)})):e(r[i],o+1)}(e,0)}(e,t.split(".")))),e;t(e)})).then((e=>{let n=Array.isArray(e)?[]:{};if(void 0===f||0===f.length)n=e;else if(f.forEach((t=>{!function(e,t,n){const r=t.length;!function e(n,o,i){if(!n||o===r)return n;const a=t[o];return Array.isArray(n)?n.map(((t,n)=>{const r=i[n];let a;return void 0===r?(a={},i.push(a)):a=r,e(t,o,a)})):o===r-1?(i[a]=n[a],n[a]):(n=n[a],Array.isArray(n)?i[a]=i[a]||[]:i[a]=i[a]||{},e(n,o+1,Array.isArray(i)?i:i[a]))}(e,0,n)}(e,t.split("."),n)})),Array.isArray(f)&&f.length&&(f.length>1||1!==f.length||""!==f[0]))try{let e=f.map((e=>e.split(".")));for(;"[object Object]"===Object.prototype.toString.call(n)&&e.every((e=>!!e.length))&&1===Object.values(n).length;)n=Object.values(n)[0],e.forEach((e=>e.shift()))}catch(e){console.log("connector format data error",e)}t(r.isMultipleOutputs?{__OUTPUT_ID__:m,__ORIGIN_RESPONSE__:n}:n)})).catch((e=>{e&&"HTTP_FETCH_ERROR"===e.message?c&&!d&&n("全局拦截响应错误函数中必须调用 throwError 方法，请前往修改"):n(e&&e.message||e)}))}catch(e){return n(e)}}(e,r)}.toString().replace("__input__",i(e.input)).replace("__output__",i(e.output)).replace("__globalResultFn__",e.globalResultFn?i(e.globalResultFn):void 0).replace("__globalErrorResultFn__",e.globalErrorResultFn?i(e.globalErrorResultFn):void 0).replace("__markList__",JSON.stringify((e.markList||[]).map((e=>{var{outputSchema:t,resultSchema:n}=e;return o(e,["outputSchema","resultSchema"])})))).replace("__hasGlobalResultFn__",JSON.stringify(!!e.globalResultFn)).replace("__method__",e.method).replace("__isTestMode__",JSON.stringify(e.isTestMode||!1)).replace("__path__",e.path.trim()).replace("__outputKeys__",JSON.stringify(e.outputKeys||[])).replace("__excludeKeys__",JSON.stringify(e.excludeKeys||[])).replace("__globalParamsFn__",i(e.globalParamsFn||r.Ys)))}},2566:(e,t,n)=>{n.d(t,{mock:()=>o});var r=n(6178);function o(e){return new Promise(((t,n)=>{const o=e.outputSchema||e.mockSchema;try{o?t({__OUTPUT_ID__:e.mockOutputId,__ORIGIN_RESPONSE__:(0,r.AS)(e.outputSchema)}):n("服务接口组件返回值类型不存在，不支持模拟数据")}catch(e){n("服务接口数据模拟失败")}}))}},1221:(e,t,n)=>{function r(e){var t=document.createElement("input");return document.body.appendChild(t),t.value=e,t.select(),document.execCommand("copy"),document.body.removeChild(t),!0}n.d(t,{z:()=>r})},6178:(e,t,n)=>{n.d(t,{AE:()=>c,AS:()=>f,DW:()=>p,FS:()=>E,HD:()=>h,LX:()=>w,Sm:()=>s,U6:()=>b,Vj:()=>d,WR:()=>u,Zg:()=>l,kl:()=>_,oV:()=>v,rq:()=>a,uF:()=>x,xF:()=>y});var r=n(4113),o=n(5525);function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e){e&&("object"===e.type?Object.values(e.properties).forEach((function(e){a(e)})):"array"===e.type?(0,r.xb)(e.items)?(Object.defineProperty(e,"type",{writable:!0,value:"array"}),Reflect.deleteProperty(e,"items")):"object"===e.items.type&&Object.values(e.items.properties).forEach((function(e){a(e)})):"unknown"===e.type&&Object.defineProperty(e,"type",{writable:!0,value:"string"}))}function l(e,t){var n=Array.isArray(e)?[]:{};return void 0===t||0===t.length?n=e:t.forEach((function(t){!function(e,t,n){var r=t.length;!function e(n,o,i){if(!n||o===r)return n;var a=t[o];return Array.isArray(n)?n.map((function(t,n){var r,a=i[n];return void 0===a?(r={},i.push(r)):r=a,e(t,o,r)})):o===r-1?(i[a]=n[a],n[a]):(n=n[a],Array.isArray(n)?i[a]=i[a]||[]:i[a]=i[a]||{},e(n,o+1,Array.isArray(i)?i:i[a]))}(e,0,n)}(e,t.split("."),n)})),n}function c(e,t){if(!t||0===t.length)return e;var n=(0,r.Xh)(e);return t.forEach((function(e){!function(e,t){var n=t.length;!function e(r,o){if(r&&o!==n){var i=t[o];o===n-1&&Reflect.deleteProperty(r,i),Array.isArray(r)?r.forEach((function(t){e(t,o)})):e(r[i],o+1)}}(e,0)}(n,e.split("."))})),n}function u(e){var t={type:e.type};switch(e.type){case"root":t.type="object",t.properties={},e.children.forEach((function(e){t.properties[e.name]=u(e)}));break;case"object":t.properties={},e.children.forEach((function(e){t.properties[e.name]=u(e)}));break;case"array":t.type="array",t.items=e.children[0]?u(e.children[0]):{}}return t}function s(e){if(!e)return{};var t={};return"string"===e.type||"any"===e.type?e.defaultValue||"":"number"===e.type?+e.defaultValue:"boolean"===e.type?Boolean(e.defaultValue):(e.children&&("array"===e.type&&(t=[]),e.children.forEach((function(e){t[e.name]=s(e)}))),t)}function p(e){if(!e)return!1;var t=!1;return"any"===e.type?e.defaultValue instanceof File:(e.children&&e.children.forEach((function(e){t=t||p(e)})),t)}function d(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6,t="",n=0;n<e;n++)t+="abcdefhijkmnprstwxyz".charAt(Math.floor(20*Math.random()));return"u_"+t}function f(e){return function(e){if(e){var t,n=e.type;if("string"===n||"number"===n)return function(e){var t=e.type;if(void 0!==e.default&&""!==e.default)return e.default;if("string"===t){var n=e.minLength,r=void 0===n?0:n,o=e.maxLength,i=+r,a=+(void 0===o?8:o),l=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6,t="",n=0;n<e;n++)t+="abcdefhijkmnprstwxyz".charAt(Math.floor(20*Math.random()));return t}(a).slice(a-Math.round(i+Math.random()*(a-i)));return l}var c=e.minimum,u=void 0===c?0:c,s=e.maximum,p=+u,d=+(void 0===s?100:s);return p+Math.round(Math.random()*(d-p))}(e);if("array"===n){t=[];for(var r=e.minItems,o=void 0===r?1:r,i=e.maxItems,a=void 0===i?5:i,l=o+Math.round(Math.random()*(a-o)),c=0;c<l;c++){var u=f(e.items);null!=u&&t.push(u)}}return"object"===e.type&&(t={},Object.keys(e.properties||{}).forEach((function(n){t[n]=f(e.properties[n])}))),t}}(e)}function h(e){var t={type:void 0};return m({schema:t,val:e}),t.type?t:void 0}function m(e){var t,n,r=e.schema,o=e.val,a=e.key,l=e.fromAry;if(Array.isArray(o)){var c=o.length?{}:void 0;a?(r[a]={type:"array",items:c},c&&(r[a].items=c)):(r.type="array",c&&(r.items=c)),function(e,t){if(e){var n=[];t.length>0&&t.forEach((function(t){var r=JSON.parse(JSON.stringify(e));m({schema:r,val:t,fromAry:!0}),n.push(r)})),g(e,n.filter(Boolean))}}(c,o)}else if("object"===i(o)&&o){var u;l&&(r.type="object",u=r.properties={});var s=l?u:{};l||(a?r[a]={type:"object",properties:s}:(r.type="object",r.properties=s)),t=s,n=o,Object.keys(n).map((function(e){return m({schema:t,val:n[e],key:e})}))}else{var p=null==o?"unknown":i(o);void 0===a?r.type=p:r[a]={type:p}}}var g=function e(t,n){if(!n)return t;for(var r,o=null,i=function(){var e=n[a];if(!e||!Object.keys(e).length)return 0;if(o)"object"===t.type&&"object"===e.type?Object.keys(e.properties||{}).forEach((function(n){var r=t.properties[n];(!r&&e.properties[n]||"unknown"===r.type&&"unknown"!==e.properties[n].type)&&(t.properties[n]=e.properties[n])})):"array"===t.type&&"array"===e.type&&(t.items&&Object.keys(t.items).length||(t.items=e.items||{}));else if("unknown"!==e.type){if("object"!==e.type&&"array"!==e.type)return delete t.properties,Object.assign(t,e),1;o=e,Object.assign(t,e)}else delete t.properties,Object.assign(t,e)},a=0;a<n.length&&(0===(r=i())||1!==r);a++);"object"===t.type?Object.keys(t.properties||{}).forEach((function(r){var o,i,a,l,c,u;"object"===(null===(i=null===(o=t.properties)||void 0===o?void 0:o[r])||void 0===i?void 0:i.type)?e(t.properties[r],n.filter(Boolean).map((function(e){var t;return null===(t=null==e?void 0:e.properties)||void 0===t?void 0:t[r]}))):"array"===(null===(l=null===(a=t.properties)||void 0===a?void 0:a[r])||void 0===l?void 0:l.type)&&((null===(u=null===(c=t.properties)||void 0===c?void 0:c[r])||void 0===u?void 0:u.items)||(t.properties[r].items={}),e(t.properties[r],n.filter(Boolean).map((function(e){var t;return null===(t=null==e?void 0:e.properties)||void 0===t?void 0:t[r]}))))})):"array"===t.type&&(t.items||(t.items={}),e(t.items,n.filter(Boolean).map((function(e){return(null==e?void 0:e.items)||{}}))))};function v(e){try{return decodeURIComponent(e)}catch(t){return e}}var y=function(e){var t={id:d(),name:"root",type:"root",children:[]};return function e(t,n){var r,o;if("object"===n.type)Object.keys(n.properties).forEach((function(r){var o,i=n.properties[r],a={type:i.type,name:r,id:d(),defaultValue:null!==(o=i.defaultValue)&&void 0!==o?o:"",children:[]};t.children.push(a),["array","object"].includes(i.type)&&e(a,i)}));else if("array"===n.type){if(!(null===(r=n.items)||void 0===r?void 0:r.type))return;var i={type:n.items.type,name:"0",id:d(),defaultValue:null!==(o=n.items.defaultValue)&&void 0!==o?o:"",children:[]};t.children.push(i),["array","object"].includes(n.items.type)&&e(i,n.items)}}(t,e),t},_=function(e){var t={id:d(),name:"root",type:"root",children:[]},n=h(e);return a(n),function e(t,n){n&&(Array.isArray(n)?n.forEach((function(n,r){var o=null==n?"string":Array.isArray(n)?"array":i(n),a={name:String(r),id:d(),defaultValue:["object","array"].includes(o)?"":n,children:[],type:o};t.children.push(a),e(a,n)})):"object"===i(n)&&Object.keys(n).forEach((function(r){var o=null===n[r]||void 0===n[r]?"string":Array.isArray(n[r])?"array":i(n[r]),a={name:r,id:d(),defaultValue:["object","array"].includes(o)?"":n[r],children:[],type:o};t.children.push(a),e(a,n[r])})))}(t,e),{params:t,originSchema:n}},b=function(e,t){var n=null,r=-1;return function e(i){if(!n){var a=i.findIndex((function(e){return e.id===t.id}));-1!==a?(n=i,r=a):i.filter((function(e){return e.type===o.Cq.FOLDER})).forEach((function(t){return e(t.children)}))}}(e),{parent:n,index:r}},x=function(e){var t=[];return function e(n){n.forEach((function(n){n.type===o.Cq.FOLDER?e(n.children):t.push(n)}))}(e),t},w=function(e){return function e(t){Array.isArray(t)?t.forEach((function(t){t.type===o.Cq.FOLDER?(e(t.children),t.id=d()):(t.id=d(),t.createTime=Date.now(),t.updateTime=Date.now())})):(t.id=d(),(t.createTime||t.updateTime)&&(t.createTime=Date.now(),t.updateTime=Date.now()),t.type===o.Cq.FOLDER&&e(t.children))}(e),e},E=function(e,t){return(null==e?void 0:e.length)?t?e.map((function e(n){if(n.content.title.includes(t))return n;if(n.type===o.Cq.FOLDER){var r=[];return n.children.forEach((function(t){var n=e(t);n&&r.push(n)})),r.length?Object.assign(Object.assign({},n),{children:r}):void 0}})).filter(Boolean):e:[]}},1327:(e,t,n)=>{function r(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i(e)}function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(null===e||"object"!==i(e))return e;var n=t.filter((function(t){return t.original===e}))[0];if(n)return n.copy;var o,c=Array.isArray(e)?[]:e instanceof FormData?new FormData:{};if(t.push({original:e,copy:c}),e instanceof FormData){var u,s=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=r(e))){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,l=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){c=!0,a=e},f:function(){try{l||null==n.return||n.return()}finally{if(c)throw a}}}}(l(e.entries()));try{for(s.s();!(u=s.n()).done;){var p=function(e){if(Array.isArray(e))return e}(o=u.value)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{for(i=(n=n.call(e)).next;!(c=(r=i.call(n)).done)&&(l.push(r.value),2!==l.length);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(o)||r(o,2)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=p[0],f=p[1];c.append(d,f)}}catch(e){s.e(e)}finally{s.f()}}else Object.keys(e).forEach((function(n){c[n]=a(e[n],t)}));return c}n.d(t,{X:()=>a});var l=function(e){if(Object.prototype.toString.call(e).includes("Iterator")){for(var t=e.next(),n=t.done?[]:[t.value];!t.done;)!(t=e.next()).done&&n.push(t.value);return n}return e}},3286:(e,t,n)=>{n.d(t,{D:()=>c});var r=n(7775),o=n(7624),i="Expected a function",a=Math.max,l=Math.min;function c(e,t,n){var c,u,s,p,d,f,h=0,m=!1,g=!1,v=!0;if("function"!=typeof e)throw new TypeError(i);function y(t){var n=c,r=u;return c=u=void 0,h=t,p=e.apply(r,n)}function _(e){var n=e-f;return void 0===f||n>=t||n<0||g&&e-h>=s}function b(){var e=(0,o.z)();if(_(e))return x(e);d=setTimeout(b,function(e){var n=t-(e-f);return g?l(n,s-(e-h)):n}(e))}function x(e){return d=void 0,v&&c?y(e):(c=u=void 0,p)}function w(){var e=(0,o.z)(),n=_(e);if(c=arguments,u=this,f=e,n){if(void 0===d)return function(e){return h=e,d=setTimeout(b,t),m?y(e):p}(f);if(g)return clearTimeout(d),d=setTimeout(b,t),y(f)}return void 0===d&&(d=setTimeout(b,t)),p}return t=Number(t)||0,(0,r.K)(n)&&(m=!!n.leading,s=(g="maxWait"in n)?a(Number(n.maxWait)||0,t):s,v="trailing"in n?!!n.trailing:v),w.cancel=function(){void 0!==d&&clearTimeout(d),h=0,c=f=u=d=void 0},w.flush=function(){return void 0===d?p:x((0,o.z)())},w}},1149:(e,t,n)=>{function r(e,t){if(!Array.isArray(e))return-1;for(var n=e.length-1;n>=0;n--)if(t(e[n]))return n;return-1}n.d(t,{Z:()=>r})},4522:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t,n){if(!t||!e)return e;var i,a=Array.isArray(t)?function(e){if(Array.isArray(e))return o(e)}(i=t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(i)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(i)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}():t.split("."),l=a.length;return function e(t,o){if(o===l)return t;var i=a[o];return null==(t=Array.isArray(t)?t.map((function(t){return function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!==r(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},i,e(t[i],o+1))})):t[i])?n:e(t,o+1)}(e,0)}n.d(t,{Z:()=>i})},4113:(e,t,n)=>{n.d(t,{Ds:()=>r.D,U2:()=>i.Z,Xh:()=>o.X,qr:()=>l.Z,xb:()=>a.Z});var r=n(3286),o=n(1327),i=n(4522),a=n(273),l=n(1149)},273:(e,t,n)=>{function r(e){return!e||(Array.isArray(e)?0===e.length:"[object Object]"!==Object.prototype.toString.call(e)||0===Object.keys(e).length)}n.d(t,{Z:()=>r})},7775:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){var t=r(e);return null!=e&&("object"==t||"function"==t)}n.d(t,{K:()=>o})},7624:(e,t,n)=>{n.d(t,{z:()=>r});var r=function(){return Date.now()}},1988:(e,t,n)=>{function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"YY-mm-dd HH:MM:SS";if("number"==typeof e&&(e=new Date(e)),"object"===r(e)&&e instanceof Date){var n,o={"Y+":e.getFullYear().toString(),"m+":(e.getMonth()+1).toString(),"d+":e.getDate().toString(),"H+":e.getHours().toString(),"M+":e.getMinutes().toString(),"S+":e.getSeconds().toString()};for(var i in o)(n=new RegExp("("+i+")").exec(t))&&(t=t.replace(n[1],1==n[1].length?o[i]:o[i].padStart(n[1].length,"0")));return t}return e}n.d(t,{p:()=>o})},3505:(e,t,n)=>{function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}n.d(t,{B:()=>a});var i=["number","boolean","string","any","follow"],a=function(e){var t=[],n=function e(n,o,a){var l=(null==o?void 0:o.length)?o[o.length-1]:"";if("type"in n&&"properties"!==l)return"object"===n.type?void 0===n.properties?(t.push({path:o,fieldName:"properties",msg:"type 为 object 的描述缺少 properties 属性"}),!1):e(n.properties,[].concat(r(o),["properties"]),a):"array"===n.type?"items"in n?e(n.items,[].concat(r(o),["items"]),a):(t.push({path:o,fieldName:"items",msg:"type 为 array 的描述缺少 properties 属性"}),!1):!!i.includes(n.type)||(t.push({path:o,fieldName:"type",msg:"type 类型不符合要求，需要为以下之一: number, boolean, string, any, follow, array, object"}),!1);if("properties"===o[o.length-1]){var c=[];for(var u in n){var s=e(n[u],[].concat(r(o),[u]),a);c.push(s),!1===s&&t.push({path:[].concat(r(o),[u]),fieldName:u,msg:"properties 中的 ".concat(u," 属性，缺少 type 类型")})}return!(c.length&&c.some((function(e){return!1===e})))}return!!n.type&&void 0}(e,[],t);return[n,t]}},1537:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".jsx-editor {\n  --string-color: #e37a11;\n  --language-keyword-color: #619ac3;\n  --global-variable-color: #fae56e;\n  --local-variable-color: #fae56e;\n  --unused-opacity: 0.5;\n  --grammar-color: #369b99;\n  --jsx-tag-color: #4595ce;\n  --jsx-attribute-color: #afd5f1;\n  --jsx-text-color: #efeeee;\n  --jsx-tag-angle-bracket: #888;\n  p {\n    color: var(--string-color) !important;\n  }\n\n  .mtk1 {\n    color: var(--local-variable-color);\n  }\n\n  .mtk5 {\n    color: var(--string-color);\n  }\n\n  .mtk8 {\n    color: var(--language-keyword-color);\n  }\n\n  .mtk9 {\n    color: var(--grammar-color);\n  }\n\n  .mtk22 {\n    color: var(--global-variable-color);\n  }\n\n  .monaco-editor.showUnused .squiggly-inline-unnecessary {\n    opacity: var(--unused-opacity);\n  }\n\n  .jsx-tag-angle-bracket {\n    color: var(--jsx-tag-angle-bracket);\n  }\n\n  .jsx-tag-name {\n    color: var(--jsx-tag-color);\n  }\n\n  .jsx-tag-attribute-key {\n    color: var(--jsx-attribute-color);\n  }\n\n  .jsx-text {\n    color: var(--jsx-text-color);\n  }\n}\n",""]);const i=o},6272:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,'.http_plugin_collapse_haKdx {\n  position: relative;\n}\n.http_plugin_collapse_haKdx .http_plugin_icon_NfOLM {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  justify-content: center;\n  margin: 8px 0;\n  transition: all 0.35s;\n}\n.http_plugin_collapse_haKdx .http_plugin_icon_NfOLM::before,\n.http_plugin_collapse_haKdx .http_plugin_icon_NfOLM::after {\n  display: block;\n  width: 50%;\n  content: "";\n  border-top: 1px dashed #cfcdcd92;\n  margin: 0 18px;\n}\n.http_plugin_collapse_haKdx .http_plugin_collapsed_FWaP\\+ {\n  height: 0;\n  transition: all 0.35s;\n  overflow: hidden;\n}\n',""]),o.locals={collapse:"http_plugin_collapse_haKdx",icon:"http_plugin_icon_NfOLM",collapsed:"http_plugin_collapsed_FWaP+"};const i=o},199:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_mask_eRPJ6 {\n  position: fixed;\n  inset: 0;\n  z-index: 1000;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.45);\n  overflow: hidden;\n}\n",""]),o.locals={mask:"http_plugin_mask_eRPJ6"};const i=o},8467:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_wrap_s5MHP {\n  position: fixed;\n  inset: 0;\n  overflow: hidden;\n  pointer-events: none;\n  outline: 0;\n}\n",""]),o.locals={wrap:"http_plugin_wrap_s5MHP"};const i=o},478:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_dialog_4xLUf {\n  position: relative;\n  top: 100px;\n  padding: 20px 24px;\n  background-color: #fff;\n  border-radius: 6px;\n  margin: 0 auto;\n  pointer-events: initial;\n  cursor: pointer;\n  max-height: 100vh;\n  max-width: 100vw;\n  overflow: auto;\n}\n.http_plugin_dialog_4xLUf .http_plugin_dialog-header_tBF2C {\n  font-size: 16px;\n  font-weight: 500;\n  user-select: none;\n  line-height: 1.25;\n  word-wrap: break-word;\n  margin: -20px -24px;\n  padding: 20px 24px;\n  margin-bottom: 0;\n}\n.http_plugin_dialog_4xLUf .http_plugin_dialog-content_6JtHe {\n  border-radius: 2px;\n  position: relative;\n}\n.http_plugin_dialog_4xLUf .http_plugin_close_CoW4b {\n  position: absolute;\n  top: 20px;\n  right: 20px;\n}\n.http_plugin_dialog_4xLUf .http_plugin_close_CoW4b svg {\n  display: inline-block;\n  width: 22px;\n  height: 22px;\n  line-height: 1;\n}\n.http_plugin_dialog_4xLUf .http_plugin_close_CoW4b svg:hover path {\n  fill: #ff000092;\n}\n",""]),o.locals={dialog:"http_plugin_dialog_4xLUf","dialog-header":"http_plugin_dialog-header_tBF2C","dialog-content":"http_plugin_dialog-content_6JtHe",close:"http_plugin_close_CoW4b"};const i=o},4293:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_icon_Y5N3B {\n  display: inline-block;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  -webkit-font-smoothing: antialiased;\n}\n.http_plugin_icon_Y5N3B:hover {\n  cursor: pointer;\n  transform: scale(1.2);\n  transition: all 0.35s;\n}\n.http_plugin_icon_Y5N3B svg {\n  display: inline-block;\n  width: 20px;\n  height: 20px;\n  line-height: 1;\n}\n.http_plugin_icon_Y5N3B svg:hover path {\n  fill: #1677ff;\n}\n",""]),o.locals={icon:"http_plugin_icon_Y5N3B"};const i=o},3780:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_wrap_YTanF {\n  position: relative;\n}\n.http_plugin_dialog-content_3Nys0 {\n  height: 500px;\n}\n",""]),o.locals={wrap:"http_plugin_wrap_YTanF","dialog-content":"http_plugin_dialog-content_3Nys0"};const i=o},7758:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_toolbar_U3P2R {\n  position: absolute;\n  bottom: 14px;\n  right: 18px;\n  display: flex;\n  align-items: center;\n  column-gap: 10px;\n  pointer-events: auto;\n}\n",""]),o.locals={toolbar:"http_plugin_toolbar_U3P2R"};const i=o},1771:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,'button.http_plugin_btn_35GaF {\n  display: inline-block;\n  font-weight: 400;\n  white-space: nowrap;\n  text-align: center;\n  margin-left: 8px;\n  color: rgba(0, 0, 0, 0.85);\n  border: 1px solid #d9d9d9;\n  background: #fff;\n  font-size: 12px;\n  height: 24px;\n  line-height: 1;\n  border-radius: 3px;\n  padding: 0 10px;\n  outline: 0;\n  cursor: pointer;\n  user-select: none;\n}\nbutton.http_plugin_btn_35GaF[type="primary"] {\n  color: #fff;\n  border-color: #fa6400;\n  background-color: #fa6400;\n  font-weight: bold;\n}\nbutton.http_plugin_btn_35GaF[type="default"] {\n  color: #fa6400;\n  border-color: #fa6400;\n}\n',""]),o.locals={btn:"http_plugin_btn_35GaF"};const i=o},6866:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_collapse_BSQER {\n  position: relative;\n  font-size: 12px;\n  height: auto;\n  padding: 0 6px 0;\n  border-radius: 6px;\n  border: 1px solid #eee;\n  margin-bottom: 12px;\n}\n.http_plugin_collapse_BSQER .http_plugin_header_Vv1Ta {\n  display: flex;\n  height: 30px;\n  align-items: center;\n  cursor: pointer;\n}\n.http_plugin_collapse_BSQER .http_plugin_header_Vv1Ta svg {\n  max-width: 10px;\n}\n.http_plugin_collapse_BSQER .http_plugin_header_Vv1Ta .http_plugin_icon_dE9w9 {\n  display: flex;\n  align-items: center;\n  margin-right: 6px;\n  transform: rotate(90deg);\n}\n.http_plugin_collapse_BSQER .http_plugin_header_Vv1Ta .http_plugin_fold_zmH-D {\n  transform: rotate(0);\n}\n.http_plugin_collapse_BSQER .http_plugin_content_Ye5fL {\n  height: 100%;\n}\n",""]),o.locals={collapse:"http_plugin_collapse_BSQER",header:"http_plugin_header_Vv1Ta",icon:"http_plugin_icon_dE9w9",fold:"http_plugin_fold_zmH-D",content:"http_plugin_content_Ye5fL"};const i=o},4647:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_dropdown_rYfiS {\n  position: relative;\n}\n.http_plugin_dropdown_rYfiS .http_plugin_content_gokc7 {\n  position: absolute;\n  z-index: 1;\n}\n.http_plugin_dropdown_rYfiS:hover .http_plugin_content_gokc7 {\n  display: inline-block;\n}\n",""]),o.locals={dropdown:"http_plugin_dropdown_rYfiS",content:"http_plugin_content_gokc7"};const i=o},6846:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_item_bJJv8 {\n  display: flex;\n  flex-direction: row;\n  margin: 14px 0;\n  align-items: center;\n  font-size: 12px;\n  position: relative;\n}\n.http_plugin_item_bJJv8.http_plugin_labelTop_KUYPc {\n  align-items: normal;\n}\n.http_plugin_item_bJJv8 label {\n  flex: 0 0 75px;\n  text-align: right;\n  padding-right: 5px;\n  font-size: 12px;\n}\n.http_plugin_item_bJJv8 label i {\n  font-style: normal;\n  color: #FF0000;\n}\n.http_plugin_item_bJJv8 .http_plugin_editor_F2d0T {\n  flex: 1;\n  display: flex;\n  position: relative;\n  padding-left: 6px;\n}\n.http_plugin_item_bJJv8 .http_plugin_textEdt_5gQ7r input,\n.http_plugin_item_bJJv8 .http_plugin_textEdt_5gQ7r textarea {\n  border: 1px solid #DDD;\n  border-radius: 3px;\n  padding: 5px;\n  background: #FFF;\n  width: 100%;\n  line-height: 1;\n}\n.http_plugin_item_bJJv8 .http_plugin_textEdt_5gQ7r textarea {\n  min-height: 50px;\n}\n.http_plugin_item_bJJv8 .http_plugin_content_ADEiD {\n  flex: 1;\n  padding-left: 6px;\n}\n",""]),o.locals={item:"http_plugin_item_bJJv8",labelTop:"http_plugin_labelTop_KUYPc",editor:"http_plugin_editor_F2d0T",textEdt:"http_plugin_textEdt_5gQ7r",content:"http_plugin_content_ADEiD"};const i=o},1053:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_input_B1NQM {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  font-size: 12px;\n}\n.http_plugin_input_B1NQM label {\n  width: 75px;\n  text-align: right;\n  padding-right: 5px;\n  font-size: 12px;\n}\n.http_plugin_input_B1NQM label i {\n  font-style: normal;\n  color: #FF0000;\n}\n.http_plugin_input_B1NQM .http_plugin_editor_ipRem {\n  flex: 1;\n  display: flex;\n  position: relative;\n}\n.http_plugin_input_B1NQM .http_plugin_textEdt_3cuh0 input,\n.http_plugin_input_B1NQM .http_plugin_textEdt_3cuh0 textarea {\n  border: 1px solid #DDD;\n  border-radius: 3px;\n  padding: 5px;\n  background: #FFF;\n  width: 100%;\n  line-height: 1;\n}\n.http_plugin_input_B1NQM .http_plugin_textEdt_3cuh0 input:focus,\n.http_plugin_input_B1NQM .http_plugin_textEdt_3cuh0 textarea:focus {\n  outline: 1px solid #fa6400;\n}\n.http_plugin_input_B1NQM .http_plugin_textEdt_3cuh0 textarea {\n  min-height: 50px;\n}\n.http_plugin_input_B1NQM .http_plugin_error_bKX\\+F {\n  border: 1px solid #FF0000 !important;\n  padding: 0;\n  margin-bottom: 4px;\n}\n.http_plugin_input_B1NQM .http_plugin_error_bKX\\+F::after {\n  content: attr(data-err);\n  color: red;\n  position: absolute;\n  bottom: -17px;\n}\n",""]),o.locals={input:"http_plugin_input_B1NQM",editor:"http_plugin_editor_ipRem",textEdt:"http_plugin_textEdt_3cuh0",error:"http_plugin_error_bKX+F"};const i=o},696:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,"@keyframes http_plugin_animation_ZEbqT {\n  0% {\n    top: -20px;\n  }\n  100% {\n    top: 20px;\n  }\n}\n.http_plugin_message_vZqSi {\n  position: absolute;\n  top: 20px;\n  left: 50%;\n  z-index: 1001;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10px 16px;\n  background: #fff;\n  border-radius: 2px;\n  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);\n  pointer-events: all;\n  animation: http_plugin_animation_ZEbqT 0.25s;\n}\n.http_plugin_message_vZqSi > svg {\n  flex-shrink: 0;\n}\n.http_plugin_message_vZqSi .http_plugin_content_IT5bL {\n  margin-left: 8px;\n}\n",""]),o.locals={message:"http_plugin_message_vZqSi",animation:"http_plugin_animation_ZEbqT",content:"http_plugin_content_IT5bL"};const i=o},2937:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_sidebarPanelCodeIcon_wIxqH {\n  position: absolute;\n  bottom: 12px;\n  right: 12px;\n  z-index: 1;\n  cursor: pointer;\n}\n.http_plugin_sidebarPanelCodeIcon_wIxqH .http_plugin_icon_KS6B\\+ svg {\n  width: 16px;\n  height: 16px;\n}\n.http_plugin_sidebarPanelCodeIcon_wIxqH .http_plugin_icon_KS6B\\+ svg path {\n  fill: #333;\n}\n.http_plugin_sidebarPanelCodeIcon_wIxqH .http_plugin_icon_KS6B\\+ svg:hover path {\n  fill: #fa6400;\n}\n.http_plugin_sidebarPanelCodeIcon_wIxqH .http_plugin_icon_KS6B\\+:hover {\n  transform: scale(1);\n}\n.http_plugin_modalContent_6eZvv {\n  height: 80vh;\n  min-height: 700px;\n}\n.http_plugin_wrap_SxS2- {\n  border: 1px solid #eeee;\n  background: #fff;\n}\n.http_plugin_sidebarPanelCodeIconFull_FEUZW {\n  position: fixed;\n  top: 48px;\n  right: 50px;\n  z-index: 10;\n  cursor: pointer;\n}\n.http_plugin_sidebarPanelCodeFull_72i6U {\n  position: fixed;\n  z-index: 9;\n  padding: 40px 42px 26px;\n  background-color: rgba(0, 0, 0, 0.45);\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n}\n.http_plugin_sidebarPanelCodeFull_72i6U .monaco-editor {\n  padding-top: 16px;\n}\n",""]),o.locals={sidebarPanelCodeIcon:"http_plugin_sidebarPanelCodeIcon_wIxqH",icon:"http_plugin_icon_KS6B+",modalContent:"http_plugin_modalContent_6eZvv",wrap:"http_plugin_wrap_SxS2-",sidebarPanelCodeIconFull:"http_plugin_sidebarPanelCodeIconFull_FEUZW",sidebarPanelCodeFull:"http_plugin_sidebarPanelCodeFull_72i6U"};const i=o},5302:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_edt_ocZuK {\n  display: flex;\n  flex-direction: row;\n  border-radius: 3px;\n  overflow: hidden;\n}\n.http_plugin_edt_ocZuK .http_plugin_opt_ynQHu {\n  padding: 3px 8px;\n  font-size: 12px;\n  background: #FFF;\n  cursor: pointer;\n  border: 1px solid #DDD;\n  display: flex;\n  align-items: center;\n}\n.http_plugin_edt_ocZuK .http_plugin_opt_ynQHu:not(:last-child) {\n  border-right: 1px solid #DDD;\n}\n.http_plugin_edt_ocZuK .http_plugin_selected_2I6EK {\n  background: #616C81;\n  color: #FFF;\n  font-weight: bold;\n  margin: -1px;\n}\n",""]),o.locals={edt:"http_plugin_edt_ocZuK",opt:"http_plugin_opt_ynQHu",selected:"http_plugin_selected_2I6EK"};const i=o},660:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_ct_VLaxB {\n  display: flex;\n  align-items: center;\n}\n.http_plugin_ct_VLaxB label {\n  width: 75px;\n  text-align: right;\n  padding-right: 5px;\n  font-size: 12px;\n}\n.http_plugin_ct_VLaxB label i {\n  font-style: normal;\n  color: #FF0000;\n}\n.http_plugin_ct_VLaxB .http_plugin_switch_xQgWL {\n  display: inline-block;\n  box-sizing: border-box;\n  position: relative;\n  padding-left: 6px;\n  background-color: rgba(0, 0, 0, 0.25);\n  border: 0;\n  border-radius: 100px;\n  cursor: pointer;\n  min-width: 28px;\n  height: 16px;\n  line-height: 16px;\n  margin: 0;\n  padding: 0;\n  color: rgba(0, 0, 0, 0.85);\n  transition: all 0.2s;\n}\n.http_plugin_ct_VLaxB .http_plugin_switch_xQgWL.http_plugin_checked_V90Jh {\n  background-color: #fa6400;\n}\n.http_plugin_ct_VLaxB .http_plugin_switch_xQgWL.http_plugin_checked_V90Jh .http_plugin_handle_56\\+K2 {\n  left: initial;\n  right: 2px;\n}\n.http_plugin_ct_VLaxB .http_plugin_switch_xQgWL .http_plugin_handle_56\\+K2 {\n  width: 12px;\n  height: 12px;\n  position: absolute;\n  transition: all 0.2s ease-in-out;\n  top: 2px;\n  left: 2px;\n}\n.http_plugin_ct_VLaxB .http_plugin_switch_xQgWL .http_plugin_handle_56\\+K2::before {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background-color: #fff;\n  border-radius: 9px;\n  box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);\n  content: '';\n}\n",""]),o.locals={ct:"http_plugin_ct_VLaxB",switch:"http_plugin_switch_xQgWL",checked:"http_plugin_checked_V90Jh",handle:"http_plugin_handle_56+K2"};const i=o},3837:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_hovering_FlAI0 {\n  position: relative;\n}\n.http_plugin_hovering_FlAI0 > div {\n  background: rgba(250, 100, 0, 0.1) !important;\n}\n.http_plugin_hoverTop_uVeJ7,\n.http_plugin_hoverBottom_02VbU {\n  position: relative;\n}\n.http_plugin_hoverTop_uVeJ7:before,\n.http_plugin_hoverBottom_02VbU:before {\n  content: '';\n  position: absolute;\n  top: -1px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: #fa6400;\n  z-index: 100;\n}\n.http_plugin_hoverBottom_02VbU:before {\n  top: unset;\n  bottom: 0;\n}\n",""]),o.locals={hovering:"http_plugin_hovering_FlAI0",hoverTop:"http_plugin_hoverTop_uVeJ7",hoverBottom:"http_plugin_hoverBottom_02VbU"};const i=o},8261:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_pluginPanelContainer_xDTz0 {\n  display: flex;\n  flex-direction: column;\n  box-shadow: 5px 0 10px -5px #ddd;\n  overflow: hidden;\n  width: 560px;\n  height: 100%;\n  position: absolute;\n  z-index: 1000;\n  top: 0;\n  left: 320px;\n  background-color: #f7f7f7;\n  border-left: 1px solid #ddd;\n  border-right: 1px solid #ddd;\n}\n.http_plugin_pluginPanelTitle_sMkvZ {\n  font-size: 12px;\n  font-weight: bold;\n  padding: 0 10px;\n  line-height: 50px;\n  height: 50px;\n  background-color: #f7f7f7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  user-select: text;\n}\n.http_plugin_pluginPanelContent_PvIPa {\n  padding: 0 12px 12px;\n  overflow-y: auto;\n  height: calc(100% - 32px);\n  font-size: 12px;\n}\n",""]),o.locals={pluginPanelContainer:"http_plugin_pluginPanelContainer_xDTz0",pluginPanelTitle:"http_plugin_pluginPanelTitle_sMkvZ",pluginPanelContent:"http_plugin_pluginPanelContent_PvIPa"};const i=o},4628:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_tooltipContainer_yrOMQ {\n  position: relative;\n}\n.http_plugin_tooltipContainer_yrOMQ .http_plugin_tooltip_qQMkl {\n  display: none;\n}\n.http_plugin_tooltipContainer_yrOMQ:hover .http_plugin_tooltip_qQMkl {\n  display: block;\n  max-width: 250px;\n  overflow: hidden;\n  position: absolute;\n  top: calc(100% + 2px);\n  right: 0;\n  z-index: 1;\n  min-height: 24px;\n  height: auto;\n  padding: 3px 10px;\n  color: #fff;\n  text-align: left;\n  text-decoration: none;\n  white-space: nowrap;\n  background-color: #616C81;\n  border-radius: 4px;\n  font-size: 12px;\n  font-variant: tabular-nums;\n  line-height: 1.5715;\n  list-style: none;\n  font-feature-settings: 'tnum';\n}\n.http_plugin_tooltipContainer_yrOMQ:hover .http_plugin_arrow_A6CLc {\n  position: absolute;\n  top: calc(100% - 2px);\n  opacity: 1;\n  right: 50%;\n  transform: translate(5px);\n  content: '';\n  border-left: 3px solid transparent;\n  border-right: 3px solid transparent;\n  border-bottom: 5px solid #616C81;\n}\n",""]),o.locals={tooltipContainer:"http_plugin_tooltipContainer_yrOMQ",tooltip:"http_plugin_tooltip_qQMkl",arrow:"http_plugin_arrow_A6CLc"};const i=o},2847:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_title_gZJHr {\n  font-size: 12px;\n  padding: 4px;\n  margin-top: -20px;\n}\n.http_plugin_formItem_5hQ3b label {\n  width: 75px;\n  text-align: right;\n  padding-right: 5px;\n  font-size: 12px;\n}\n.http_plugin_paramEditContainer_CmgwA {\n  position: relative;\n}\n.http_plugin_paramEditContainer_CmgwA .http_plugin_codeIcon_eMek4 {\n  right: 0;\n  top: -26px;\n}\n.http_plugin_codeText_tmlRE {\n  width: calc(100% - 12px);\n  margin-top: 4px;\n  padding: 5px;\n  border: 1px solid #CCC;\n  border-radius: 3px;\n  overflow: auto;\n  resize: vertical;\n}\n.http_plugin_codeIcon_eMek4 {\n  position: absolute;\n  cursor: pointer;\n  width: 34px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 3px;\n  margin-left: 6px;\n}\n.http_plugin_codeIcon_eMek4.http_plugin_focus_c5w8D,\n.http_plugin_codeIcon_eMek4:hover {\n  background-color: #AAA;\n}\n.http_plugin_codeIcon_eMek4.http_plugin_focus_c5w8D svg,\n.http_plugin_codeIcon_eMek4:hover svg {\n  fill: #FFFFFF;\n}\n.http_plugin_buttonGroup_-A8F6 {\n  display: flex;\n  justify-content: space-between;\n}\n.http_plugin_textEdt_jiCm1 {\n  background: #FFF;\n}\n.http_plugin_textEdt_jiCm1:focus {\n  outline: 1px solid #fa6400;\n}\n.http_plugin_responseCodeIcon_9Yu8x {\n  position: relative;\n  right: 0;\n  top: 0;\n}\n.http_plugin_categoryContainer_LZhsJ {\n  display: flex;\n  align-items: center;\n  height: 24px;\n  flex: 1;\n  flex-shrink: 0;\n}\n.http_plugin_categoryContainer_LZhsJ > * {\n  flex-shrink: 0;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp {\n  display: flex;\n  flex-direction: row;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK {\n  position: relative;\n  padding: 3px 8px;\n  font-size: 12px;\n  background: #FFF;\n  cursor: pointer;\n  border: 1px solid #DDD;\n  display: flex;\n  align-items: center;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:hover .http_plugin_optionCancelIcon_Msf2J {\n  display: flex;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:not(:last-child) {\n  border-right: 1px solid #DDD;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:not(:first-of-type) {\n  margin-left: -1px;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:first-of-type {\n  border-radius: 3px 0 0 3px;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:last-child {\n  border-radius: 0 3px 3px 0;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_option_O6mQK:first-of-type:last-child {\n  border-radius: 3px;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_selected_w2aPj {\n  background: #616C81;\n  color: #FFF;\n  font-weight: bold;\n  margin: -1px;\n}\n.http_plugin_categoryContainer_LZhsJ .http_plugin_buttons_AVezp .http_plugin_optionCancelIcon_Msf2J {\n  z-index: 1;\n  display: none;\n  position: absolute;\n  right: -6px;\n  top: -6px;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  font-size: 8px;\n  color: #FFFFFF;\n  background: #fa6400;\n}\n.http_plugin_rightBox_ZbPhu {\n  display: flex;\n  height: 100%;\n  align-items: center;\n}\n.http_plugin_iconRootClose_q7IpF,\n.http_plugin_iconRootAdder_hz5pj {\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: normal;\n  color: #555;\n  width: 22px;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  margin-left: 12px;\n}\n.http_plugin_iconRootClose_q7IpF:hover,\n.http_plugin_iconRootAdder_hz5pj:hover {\n  color: #fa6400;\n}\n.http_plugin_iconRootClose_q7IpF {\n  font-size: 12px;\n}\n.http_plugin_markAdder_1mg5P {\n  display: flex;\n}\n.http_plugin_markAdder_1mg5P .http_plugin_markInput_v66f9 {\n  border: 1px solid #DDD;\n  box-sizing: border-box;\n  border-radius: 3px 0 0 3px;\n  padding: 5px;\n  background: #FFF;\n  width: 80px;\n  line-height: 1;\n  height: 24px;\n}\n.http_plugin_markAdder_1mg5P .http_plugin_markInput_v66f9:focus {\n  border-color: #fa6400;\n}\n.http_plugin_markAdder_1mg5P .http_plugin_button_HE93O {\n  font-weight: 400;\n  text-align: center;\n  color: rgba(0, 0, 0, 0.85);\n  border: 1px solid #d9d9d9;\n  background: #fff;\n  font-size: 12px;\n  box-sizing: border-box;\n  height: 24px;\n  line-height: 1;\n  border-radius: 0 3px 3px 0;\n  border-left: none;\n  padding: 0 10px;\n  outline: 0;\n  cursor: pointer;\n  user-select: none;\n}\n.http_plugin_markAdder_1mg5P .http_plugin_button_HE93O:hover {\n  opacity: 0.8;\n}\n.http_plugin_scrollFormItem_9KMvr {\n  overflow: auto;\n  padding-top: 10px;\n}\n.http_plugin_scrollFormItem_9KMvr::-webkit-scrollbar {\n  display: none;\n}\n",""]),o.locals={title:"http_plugin_title_gZJHr",formItem:"http_plugin_formItem_5hQ3b",paramEditContainer:"http_plugin_paramEditContainer_CmgwA",codeIcon:"http_plugin_codeIcon_eMek4",codeText:"http_plugin_codeText_tmlRE",focus:"http_plugin_focus_c5w8D",buttonGroup:"http_plugin_buttonGroup_-A8F6",textEdt:"http_plugin_textEdt_jiCm1",responseCodeIcon:"http_plugin_responseCodeIcon_9Yu8x",categoryContainer:"http_plugin_categoryContainer_LZhsJ",buttons:"http_plugin_buttons_AVezp",option:"http_plugin_option_O6mQK",optionCancelIcon:"http_plugin_optionCancelIcon_Msf2J",selected:"http_plugin_selected_w2aPj",rightBox:"http_plugin_rightBox_ZbPhu",iconRootClose:"http_plugin_iconRootClose_q7IpF",iconRootAdder:"http_plugin_iconRootAdder_hz5pj",markAdder:"http_plugin_markAdder_1mg5P",markInput:"http_plugin_markInput_v66f9",button:"http_plugin_button_HE93O",scrollFormItem:"http_plugin_scrollFormItem_9KMvr"};const i=o},2145:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_editContainer_1QZp5 {\n  margin-top: 10px;\n}\n.http_plugin_adder_7d4HU {\n  font-size: 20px;\n  font-weight: normal;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  margin-top: -6px;\n}\n.http_plugin_adder_7d4HU > span {\n  cursor: pointer;\n}\n.http_plugin_item_PAjnY,\n.http_plugin_header_ezIv6 {\n  display: flex;\n  font-size: 12px;\n}\n.http_plugin_item_PAjnY .http_plugin_fieldName_RIzsL,\n.http_plugin_header_ezIv6 .http_plugin_fieldName_RIzsL {\n  width: 331px;\n}\n.http_plugin_item_PAjnY .http_plugin_type_KITjl,\n.http_plugin_header_ezIv6 .http_plugin_type_KITjl {\n  text-align: center;\n  width: 60px;\n  border: none;\n  background-color: transparent;\n}\n.http_plugin_item_PAjnY .http_plugin_defaultValue_LJrVo,\n.http_plugin_header_ezIv6 .http_plugin_defaultValue_LJrVo {\n  text-align: center;\n  width: 48px;\n}\n.http_plugin_item_PAjnY .http_plugin_range_5oHmV,\n.http_plugin_header_ezIv6 .http_plugin_range_5oHmV {\n  text-align: center;\n  width: 121px;\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  margin: 0 10px;\n}\n.http_plugin_item_PAjnY .http_plugin_range_5oHmV input,\n.http_plugin_header_ezIv6 .http_plugin_range_5oHmV input {\n  width: 60px;\n}\n.http_plugin_item_PAjnY .http_plugin_range_5oHmV .http_plugin_min_x083\\+,\n.http_plugin_header_ezIv6 .http_plugin_range_5oHmV .http_plugin_min_x083\\+ {\n  margin-right: 1px;\n}\n.http_plugin_item_PAjnY .http_plugin_range_5oHmV .http_plugin_max_\\+ugoa,\n.http_plugin_header_ezIv6 .http_plugin_range_5oHmV .http_plugin_max_\\+ugoa {\n  margin-left: 1px;\n}\n.http_plugin_item_PAjnY .http_plugin_range_5oHmV .http_plugin_error_jjRrf,\n.http_plugin_header_ezIv6 .http_plugin_range_5oHmV .http_plugin_error_jjRrf {\n  border: 1px solid red;\n}\n.http_plugin_item_PAjnY .http_plugin_operate_Ne4f5,\n.http_plugin_header_ezIv6 .http_plugin_operate_Ne4f5 {\n  width: 40px;\n  text-align: center;\n}\n.http_plugin_item_PAjnY p,\n.http_plugin_header_ezIv6 p {\n  margin-bottom: 0;\n}\n.http_plugin_content_RD8xB {\n  display: flex;\n}\n.http_plugin_item_PAjnY {\n  display: flex;\n  align-items: center;\n  margin-top: 10px;\n  height: 20px;\n}\n.http_plugin_item_PAjnY .http_plugin_label_vS3Z6 {\n  text-align: right;\n  padding: 0 5px;\n  color: #777;\n}\n.http_plugin_item_PAjnY input {\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  background: #fff;\n  padding: 5px;\n  line-height: 1;\n  text-align: left !important;\n  box-sizing: border-box;\n}\n.http_plugin_item_PAjnY input:focus {\n  outline: 1px solid #fa6400;\n}\n.http_plugin_item_PAjnY input[disabled] {\n  color: rgba(0, 0, 0, 0.25);\n  background-color: #f5f5f5;\n  border-color: #d9d9d9;\n  box-shadow: none;\n  cursor: not-allowed;\n}\n.http_plugin_item_PAjnY .http_plugin_iconRemove_S8VtZ {\n  height: 12px;\n  cursor: pointer;\n  margin-right: 8px;\n}\n.http_plugin_item_PAjnY .http_plugin_iconRemove_S8VtZ:hover {\n  color: #fa6400;\n}\n.http_plugin_item_PAjnY .http_plugin_iconAdder_jDca0 {\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: normal;\n  color: #555;\n  width: 12px;\n  height: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.http_plugin_item_PAjnY .http_plugin_iconAdder_jDca0:hover {\n  color: #fa6400;\n}\n.http_plugin_ct_ouJUF {\n  margin-left: 20px;\n}\n.http_plugin_list_bOsuy {\n  margin-left: -20px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.http_plugin_flex_8pEQw {\n  display: flex;\n  align-items: center;\n}\n.http_plugin_mockData_WvBPT {\n  margin: 0 10px 0 -80px;\n}\n.http_plugin_empty_YNMML {\n  position: relative;\n  color: #999;\n  font-style: italic;\n  font-size: 12px;\n}\n",""]),o.locals={editContainer:"http_plugin_editContainer_1QZp5",adder:"http_plugin_adder_7d4HU",item:"http_plugin_item_PAjnY",header:"http_plugin_header_ezIv6",fieldName:"http_plugin_fieldName_RIzsL",type:"http_plugin_type_KITjl",defaultValue:"http_plugin_defaultValue_LJrVo",range:"http_plugin_range_5oHmV",min:"http_plugin_min_x083+",max:"http_plugin_max_+ugoa",error:"http_plugin_error_jjRrf",operate:"http_plugin_operate_Ne4f5",content:"http_plugin_content_RD8xB",label:"http_plugin_label_vS3Z6",iconRemove:"http_plugin_iconRemove_S8VtZ",iconAdder:"http_plugin_iconAdder_jDca0",ct:"http_plugin_ct_ouJUF",list:"http_plugin_list_bOsuy",flex:"http_plugin_flex_8pEQw",mockData:"http_plugin_mockData_WvBPT",empty:"http_plugin_empty_YNMML"};const i=o},9832:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_item_ibWVW,\n.http_plugin_header_7V0Uh {\n  display: flex;\n  font-size: 12px;\n}\n.http_plugin_item_ibWVW .http_plugin_column_WlvRV,\n.http_plugin_header_7V0Uh .http_plugin_column_WlvRV {\n  text-align: center;\n  width: 78px;\n}\n.http_plugin_item_ibWVW .http_plugin_fileName_BBnMd,\n.http_plugin_header_7V0Uh .http_plugin_fileName_BBnMd {\n  text-align: center;\n  width: 78px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  word-break: break-all;\n}\n.http_plugin_item_ibWVW p,\n.http_plugin_header_7V0Uh p {\n  margin-bottom: 0;\n}\n.http_plugin_content_u-KJk {\n  display: flex;\n  margin-left: -30px;\n}\n.http_plugin_item_ibWVW {\n  display: flex;\n  align-items: center;\n  margin-top: 4px;\n  position: relative;\n}\n.http_plugin_item_ibWVW::before {\n  position: absolute;\n  left: -14px;\n  width: 12px;\n  top: 10px;\n  border-bottom: 1px solid #aaa;\n  content: '';\n}\n.http_plugin_item_ibWVW .http_plugin_label_2EWBI {\n  text-align: right;\n  padding: 0 5px;\n  color: #777;\n}\n.http_plugin_item_ibWVW .http_plugin_type_NJ-q9 {\n  width: 30px;\n  text-align: left;\n  padding-right: 5px;\n}\n.http_plugin_item_ibWVW input {\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  background: #fff;\n  padding: 5px;\n  line-height: 1;\n  text-align: left !important;\n}\n.http_plugin_item_ibWVW input:focus {\n  outline: 1px solid #fa6400;\n}\n.http_plugin_item_ibWVW input[disabled] {\n  color: rgba(0, 0, 0, 0.25);\n  background-color: #f5f5f5;\n  border-color: #d9d9d9;\n  box-shadow: none;\n  cursor: not-allowed;\n}\n.http_plugin_item_ibWVW .http_plugin_iconRemove_1oGEY {\n  cursor: pointer;\n}\n.http_plugin_item_ibWVW .http_plugin_iconAdder_03zzO {\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: normal;\n  color: #555;\n  width: 22px;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding-bottom: 3px;\n  margin-left: 5px;\n  margin-bottom: 3px;\n}\n.http_plugin_item_ibWVW .http_plugin_iconAdder_03zzO:hover {\n  color: #ffffff;\n  background-color: #fa6400;\n}\n.http_plugin_rootItem_Xp5ci::before {\n  display: none;\n}\n.http_plugin_ct_1v7re {\n  position: relative;\n  width: 100%;\n  margin-left: 12px;\n  padding: 0 5px 0 15px;\n}\n.http_plugin_ct_1v7re::before {\n  position: absolute;\n  left: 0;\n  top: -2px;\n  bottom: -2px;\n  border-left: 1px solid #aaa;\n  content: '';\n}\n.http_plugin_ct_1v7re:first-child::before {\n  display: none;\n}\n.http_plugin_ct_1v7re:last-child::before {\n  height: 13px;\n}\n.http_plugin_list_FFYRn {\n  margin-left: -12px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.http_plugin_flex_bLr30 {\n  display: flex;\n  align-items: center;\n}\n.http_plugin_typeName_HswdI {\n  font-style: italic;\n  color: #777;\n  padding-left: 3px;\n}\n.http_plugin_debug_zzVZi {\n  border-top: 1px solid #eee;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.http_plugin_connectionButton_vescM {\n  position: relative;\n}\n.http_plugin_connectionButton_vescM .http_plugin_tipContainer_3XAAg {\n  position: absolute;\n  top: 50px;\n  right: 0;\n  min-height: 100px;\n  padding: 16px;\n  background: #FFFFFF;\n  width: 240px;\n  opacity: 1;\n  z-index: 1;\n  border-radius: 4px;\n  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;\n}\n.http_plugin_connectionButton_vescM .http_plugin_tipContainer_3XAAg:after {\n  position: absolute;\n  top: -10px;\n  right: 30px;\n  content: '';\n  border-left: 6px solid transparent;\n  border-right: 6px solid transparent;\n  border-bottom: 10px solid #FFFFFF;\n}\n.http_plugin_connectionButton_vescM .http_plugin_tipContainer_3XAAg .http_plugin_preview_fL58- {\n  color: #fa6400;\n  text-decoration: underline;\n  cursor: pointer;\n}\n.http_plugin_connectionButton_vescM .http_plugin_buttonGroup_MR9pA {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 12px;\n}\n",""]),o.locals={item:"http_plugin_item_ibWVW",header:"http_plugin_header_7V0Uh",column:"http_plugin_column_WlvRV",fileName:"http_plugin_fileName_BBnMd",content:"http_plugin_content_u-KJk",label:"http_plugin_label_2EWBI",type:"http_plugin_type_NJ-q9",iconRemove:"http_plugin_iconRemove_1oGEY",iconAdder:"http_plugin_iconAdder_03zzO",rootItem:"http_plugin_rootItem_Xp5ci",ct:"http_plugin_ct_1v7re",list:"http_plugin_list_FFYRn",flex:"http_plugin_flex_bLr30",typeName:"http_plugin_typeName_HswdI",debug:"http_plugin_debug_zzVZi",connectionButton:"http_plugin_connectionButton_vescM",tipContainer:"http_plugin_tipContainer_3XAAg",preview:"http_plugin_preview_fL58-",buttonGroup:"http_plugin_buttonGroup_MR9pA"};const i=o},6283:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_item_AgYH6,\n.http_plugin_header_D3SgC {\n  display: flex;\n  font-size: 12px;\n}\n.http_plugin_item_AgYH6 .http_plugin_column1_eCaQ8,\n.http_plugin_header_D3SgC .http_plugin_column1_eCaQ8 {\n  width: 270px;\n}\n.http_plugin_item_AgYH6 .http_plugin_column2_RU9Ik,\n.http_plugin_header_D3SgC .http_plugin_column2_RU9Ik {\n  text-align: center;\n  width: 45px;\n  border: none;\n  background-color: transparent;\n}\n.http_plugin_item_AgYH6 .http_plugin_column3_wXNHV,\n.http_plugin_header_D3SgC .http_plugin_column3_wXNHV {\n  text-align: center;\n  width: 68px;\n}\n.http_plugin_item_AgYH6 .http_plugin_uploadFile_L6LNu,\n.http_plugin_header_D3SgC .http_plugin_uploadFile_L6LNu {\n  border: none;\n  text-align: center;\n  width: 68px;\n  background: none;\n  padding: 0;\n}\n.http_plugin_item_AgYH6 .http_plugin_uploadFileName_dkICC,\n.http_plugin_header_D3SgC .http_plugin_uploadFileName_dkICC {\n  border: none;\n  text-align: center;\n  width: 68px;\n  background: none;\n  padding: 0;\n}\n.http_plugin_item_AgYH6 .http_plugin_uploadFileName_dkICC .http_plugin_clear_Uay1j,\n.http_plugin_header_D3SgC .http_plugin_uploadFileName_dkICC .http_plugin_clear_Uay1j {\n  color: #fa6400;\n  cursor: pointer;\n  padding: 0 2px;\n  vertical-align: middle;\n}\n.http_plugin_item_AgYH6 .http_plugin_column4_zJpCa,\n.http_plugin_header_D3SgC .http_plugin_column4_zJpCa {\n  width: 46px;\n  margin-left: 8px;\n}\n.http_plugin_item_AgYH6 p,\n.http_plugin_header_D3SgC p {\n  margin-bottom: 0;\n}\n.http_plugin_content_3Ap3m {\n  display: flex;\n}\n.http_plugin_item_AgYH6 {\n  display: flex;\n  align-items: center;\n  margin-top: 4px;\n}\n.http_plugin_item_AgYH6 .http_plugin_label_v7aDm {\n  text-align: right;\n  padding: 0 5px;\n  color: #777;\n}\n.http_plugin_item_AgYH6 .http_plugin_type_ztsDl {\n  width: 30px;\n  text-align: left;\n  padding-right: 5px;\n}\n.http_plugin_item_AgYH6 input {\n  border: 1px solid #ddd;\n  border-radius: 3px;\n  background: #fff;\n  padding: 5px;\n  line-height: 1;\n  text-align: left !important;\n  box-sizing: border-box;\n}\n.http_plugin_item_AgYH6 input:focus {\n  outline: 1px solid #fa6400;\n}\n.http_plugin_item_AgYH6 input[disabled] {\n  color: rgba(0, 0, 0, 0.25);\n  background-color: #f5f5f5;\n  border-color: #d9d9d9;\n  box-shadow: none;\n  cursor: not-allowed;\n}\n.http_plugin_item_AgYH6 .http_plugin_iconRemove_APjEA {\n  cursor: pointer;\n}\n.http_plugin_item_AgYH6 .http_plugin_iconRemove_APjEA:hover {\n  color: #fa6400;\n}\n.http_plugin_item_AgYH6 .http_plugin_iconAdder_wElo7 {\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: normal;\n  color: #555;\n  width: 22px;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding-bottom: 3px;\n  margin-left: 5px;\n  margin-bottom: 3px;\n}\n.http_plugin_item_AgYH6 .http_plugin_iconAdder_wElo7:hover {\n  color: #fa6400;\n}\n.http_plugin_ct_dWePW {\n  width: 100%;\n  margin-left: 20px;\n}\n.http_plugin_list_ZnoCC {\n  margin-left: -20px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.http_plugin_flex_thC3Z {\n  display: flex;\n  align-items: center;\n}\n.http_plugin_iconRootAdder_1JqjU {\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: normal;\n  color: #555;\n  width: 22px;\n  height: 22px;\n  display: flex;\n  align-items: center;\n  margin: 0;\n}\n.http_plugin_iconRootAdder_1JqjU:hover {\n  color: #fa6400;\n}\n",""]),o.locals={item:"http_plugin_item_AgYH6",header:"http_plugin_header_D3SgC",column1:"http_plugin_column1_eCaQ8",column2:"http_plugin_column2_RU9Ik",column3:"http_plugin_column3_wXNHV",uploadFile:"http_plugin_uploadFile_L6LNu",uploadFileName:"http_plugin_uploadFileName_dkICC",clear:"http_plugin_clear_Uay1j",column4:"http_plugin_column4_zJpCa",content:"http_plugin_content_3Ap3m",label:"http_plugin_label_v7aDm",type:"http_plugin_type_ztsDl",iconRemove:"http_plugin_iconRemove_APjEA",iconAdder:"http_plugin_iconAdder_wElo7",ct:"http_plugin_ct_dWePW",list:"http_plugin_list_ZnoCC",flex:"http_plugin_flex_thC3Z",iconRootAdder:"http_plugin_iconRootAdder_1JqjU"};const i=o},6256:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_returnParams_TAMol {\n  position: relative;\n  padding: 0 5px 5px 15px;\n  font-size: 12px;\n  margin-left: -10px;\n  margin-top: 10px;\n}\n.http_plugin_returnParams_TAMol:hover .http_plugin_keyName_NIZYr button {\n  display: block !important;\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu {\n  margin-left: 10px;\n  padding: 0 5px 0 15px;\n  position: relative;\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu:before {\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  border-left: 1px solid #aaa;\n  content: '';\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu:last-child:before {\n  left: 0;\n  top: 0;\n  height: 15px;\n  border-left: 1px solid #aaa;\n  content: '';\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu .http_plugin_keyName_NIZYr {\n  position: relative;\n  display: flex;\n  align-items: center;\n  height: 28px;\n  flex-wrap: nowrap;\n  white-space: nowrap;\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu .http_plugin_keyName_NIZYr:before {\n  position: absolute;\n  left: -14px;\n  width: 12px;\n  top: 14px;\n  border-bottom: 1px solid #aaa;\n  content: '';\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu .http_plugin_keyName_NIZYr .http_plugin_typeName_H2Gtk {\n  font-style: italic;\n  color: #777;\n  padding-left: 3px;\n}\n.http_plugin_returnParams_TAMol .http_plugin_item_7UKiu .http_plugin_keyName_NIZYr button {\n  display: none;\n  margin: 0 0 0 5px;\n  padding: 2px;\n  font-size: 10px;\n  cursor: pointer;\n  line-height: 1;\n  border-radius: 3px;\n  background-color: #fff;\n  border: 1px solid #fa6400;\n}\n.http_plugin_returnParams_TAMol .http_plugin_rootItem_5l9Db {\n  margin-left: 0 !important;\n  padding-left: 0 !important;\n}\n.http_plugin_returnParams_TAMol .http_plugin_rootItem_5l9Db:before {\n  border-left-width: 0 !important;\n}\n.http_plugin_returnParams_TAMol .http_plugin_rootItem_5l9Db > .http_plugin_keyName_NIZYr {\n  margin-left: -8px;\n}\n.http_plugin_returnParams_TAMol .http_plugin_rootItem_5l9Db > .http_plugin_keyName_NIZYr:before {\n  border-bottom-width: 0 !important;\n}\n.http_plugin_returnParams_TAMol .http_plugin_markAsReturn_dwVeS .http_plugin_marked_GBr-r {\n  border-radius: 10px;\n  border-left: 2px solid #fa6400;\n  color: #fa6400;\n  font-size: 10px;\n  font-weight: bold;\n  padding: 1px 5px;\n  position: absolute;\n  left: -11px;\n  right: 0;\n  height: 100%;\n  background: rgba(250, 100, 0, 0.1);\n}\n.http_plugin_returnParams_TAMol .http_plugin_markAsReturn_dwVeS .http_plugin_marked_GBr-r:before {\n  position: absolute;\n  content: attr(data-content);\n  width: 49px;\n  height: 100%;\n  left: -50px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.http_plugin_returnParams_TAMol .http_plugin_markAsReturn_dwVeS .http_plugin_exclude_tgNHv {\n  position: absolute;\n  left: 0;\n  right: 0;\n  height: 100%;\n  background: linear-gradient(-45deg, #ccc 0, #ccc 25%, transparent 25%, transparent 50%, #ccc 50%, #ccc 75%, transparent 75%, transparent 100%);\n  background-size: 6px 6px;\n}\n.http_plugin_returnParams_TAMol .http_plugin_popMenu_7C\\+Mx {\n  display: none;\n  width: 120px;\n  position: absolute;\n  background: #fff;\n  padding: 5px;\n  box-shadow: 0 1px 5px #aaa;\n}\n.http_plugin_returnParams_TAMol .http_plugin_popMenu_7C\\+Mx .http_plugin_menuItem_iUZh8 {\n  padding: 5px 10px;\n  cursor: pointer;\n}\n.http_plugin_returnParams_TAMol .http_plugin_popMenu_7C\\+Mx .http_plugin_menuItem_iUZh8:hover {\n  background: #eee;\n}\n.http_plugin_errorInfo_7JMqN {\n  font-size: 14px;\n  font-style: italic;\n  color: #fa6400;\n  margin-right: 30px;\n  margin-top: 10px;\n}\n.http_plugin_empty_Tr\\+p4 {\n  position: relative;\n  color: #999;\n  font-style: italic;\n  font-size: 12px;\n  margin-top: 10px;\n}\n.http_plugin_markValueSelect_jiaei {\n  color: #8d7a34;\n  font-style: normal;\n  text-align: center;\n  text-decoration: underline;\n}\n.http_plugin_markValueInput_4nfn1 {\n  margin: 0 10px;\n  width: 60px;\n  border: 1px solid #CCC;\n  padding-left: 4px;\n  border-radius: 3px;\n}\n.http_plugin_markValueSelect_jiaei + .http_plugin_markValueInput_4nfn1 {\n  margin-left: 4px;\n}\n",""]),o.locals={returnParams:"http_plugin_returnParams_TAMol",keyName:"http_plugin_keyName_NIZYr",item:"http_plugin_item_7UKiu",typeName:"http_plugin_typeName_H2Gtk",rootItem:"http_plugin_rootItem_5l9Db",markAsReturn:"http_plugin_markAsReturn_dwVeS",marked:"http_plugin_marked_GBr-r",exclude:"http_plugin_exclude_tgNHv",popMenu:"http_plugin_popMenu_7C+Mx",menuItem:"http_plugin_menuItem_iUZh8",errorInfo:"http_plugin_errorInfo_7JMqN",empty:"http_plugin_empty_Tr+p4",markValueSelect:"http_plugin_markValueSelect_jiaei",markValueInput:"http_plugin_markValueInput_4nfn1"};const i=o},532:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_toolbar_v64U3 {\n  height: 30px;\n  background: #FFF;\n  border-bottom: 1px solid #DDD;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 15px 0 10px;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_search_wchsf {\n  border-radius: 3px;\n  height: 22px;\n  flex: 1;\n  padding: 0 5px 0 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_search_wchsf input {\n  flex: 1;\n  height: 100%;\n  padding: 5px;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_search_wchsf svg {\n  width: 13px;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_search_wchsf svg path {\n  stroke: #AAA;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_icon_xCTAs {\n  cursor: pointer;\n  color: #555;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_icon_xCTAs:hover {\n  color: #fa6400;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_icon_xCTAs:last-child {\n  margin-right: -4px;\n}\n.http_plugin_toolbar_v64U3 .http_plugin_disable_TvEuX {\n  opacity: 0.3;\n  pointer-events: none;\n}\n.http_plugin_center_6XF3p {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n}\n.http_plugin_center_6XF3p:hover {\n  color: #ffffff;\n  background-color: #fa6400;\n}\n.http_plugin_separator-divider_890VW {\n  border-top: 1px dashed #ccc;\n  margin: 4px 0px;\n}\n.http_plugin_clickAble_98mf0 {\n  cursor: pointer;\n}\n.http_plugin_ct_1rDNO {\n  background-color: #fff;\n  padding: 4px 0;\n  box-shadow: 0 0 10px 3px #ddd;\n  width: 80px;\n}\n.http_plugin_ct_1rDNO .http_plugin_item_tSH\\+- {\n  padding: 5px 12px;\n  cursor: pointer;\n}\n.http_plugin_ct_1rDNO .http_plugin_item_tSH\\+-:hover {\n  background-color: #f5f7f9;\n}\n",""]),o.locals={toolbar:"http_plugin_toolbar_v64U3",search:"http_plugin_search_wchsf",icon:"http_plugin_icon_xCTAs",disable:"http_plugin_disable_TvEuX",center:"http_plugin_center_6XF3p","separator-divider":"http_plugin_separator-divider_890VW",clickAble:"http_plugin_clickAble_98mf0",ct:"http_plugin_ct_1rDNO",item:"http_plugin_item_tSH+-"};const i=o},1582:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".http_plugin_sidebar-panel_GHUlq {\n  width: 320px;\n  background-color: #f7f7f7;\n  z-index: 9;\n  display: none;\n  height: 100%;\n}\n.http_plugin_sidebar-panel-view_0gxBI {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n}\n.http_plugin_sidebar-panel-edit_s4Z6O {\n  display: flex;\n  flex-direction: column;\n  box-shadow: 5px 0 10px -5px #ddd;\n  overflow: hidden;\n  width: 560px;\n  position: absolute;\n  z-index: 1000;\n  bottom: 26px;\n  left: 271px;\n  background-color: #f7f7f7;\n  border-left: 1px solid #ddd;\n  border-right: 1px solid #ddd;\n}\n.http_plugin_sidebar-panel-edit-open_4lOrY {\n  display: block;\n}\n.http_plugin_sidebar-panel-open_uPyBS {\n  display: flex;\n}\n.http_plugin_sidebar-panel-title_xa-Ws {\n  font-size: 12px;\n  font-weight: bold;\n  padding: 0 10px;\n  line-height: 50px;\n  height: 50px;\n  background-color: #f7f7f7;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  user-select: text;\n}\n.http_plugin_sidebar-panel-header_4JOSd {\n  background-color: #f7f7f7;\n  justify-content: space-between;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_icon_nACnc {\n  cursor: pointer;\n  font-size: 16px;\n  display: flex;\n  height: 22px;\n  align-items: center;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_icon_nACnc:hover {\n  color: #fa6400;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_rightOperate_PJ8Pf {\n  display: flex;\n  align-items: center;\n  height: 22px;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_globalMock_vv7tM {\n  display: flex;\n  align-items: center;\n  margin-right: 8px;\n  margin-top: 1px;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_globalMock_vv7tM .http_plugin_warning_Q4d6I {\n  color: #ff4d4f;\n}\n.http_plugin_sidebar-panel-header_4JOSd .http_plugin_globalMock_vv7tM span {\n  font-size: 12px;\n  font-weight: 666;\n  margin-right: 4px;\n}\n.http_plugin_sidebar-panel-header__title_thLVH {\n  font-size: 14px;\n  font-weight: 666;\n  height: 49px;\n  padding: 0 10px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #eee;\n}\n.http_plugin_sidebar-panel-header__title_thLVH span {\n  line-height: 22px;\n}\n.http_plugin_sidebar-panel-list_aq3Qk {\n  display: flex;\n  width: 100%;\n  overflow-y: auto;\n  flex: 1;\n  flex-direction: column;\n}\n.http_plugin_sidebar-panel-list_aq3Qk::-webkit-scrollbar {\n  display: none;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR {\n  border-bottom: 1px solid #ccc;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  font-size: 12px;\n  padding: 0 3px;\n  position: relative;\n  cursor: pointer;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR > div:first-child {\n  display: flex;\n  justify-content: space-between;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR.http_plugin_active_fNepW {\n  background-color: rgba(247, 247, 247, 0.4);\n}\n.http_plugin_sidebar-panel-list-item__name_vnnd\\+ {\n  display: inline-block;\n  color: #aaa;\n  padding-right: 3px;\n  text-align: right;\n  flex: 0 0 55px;\n}\n.http_plugin_sidebar-panel-list-item__content_Q3yhh {\n  flex: 1;\n  word-break: break-all;\n}\n.http_plugin_sidebar-panel-list-item__param_txKyf {\n  display: flex;\n  margin-bottom: 4px;\n  font-size: 12px;\n  padding: 0 6px;\n}\n.http_plugin_sidebar-panel-list-item__param_txKyf:last-child {\n  padding-bottom: 0;\n  margin-bottom: 0;\n}\n.http_plugin_sidebar-panel-list-item__expand_Xs3Ts {\n  background-color: #fff;\n  padding: 5px;\n  border-bottom: 1px solid #ccc;\n}\n.http_plugin_sidebar-panel-list-item__copy_pvSYY {\n  user-select: text;\n}\n.http_plugin_sidebar-panel-list-item__copy_pvSYY:hover {\n  color: #fa6400;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR:hover {\n  background-color: #f1f1f1;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_icon_nACnc {\n  cursor: pointer;\n  color: rgba(0, 0, 0, 0.85);\n  display: flex;\n  align-items: center;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_icon_nACnc svg {\n  max-width: 12px;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_iconExpand_RhA7T {\n  transform: rotate(90deg);\n  transition: 0.1s;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_tag_G68CQ {\n  padding: 0 8px 0 2px;\n  color: #fa6400;\n  min-width: 35px;\n  cursor: pointer;\n  flex-shrink: 0;\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_tag__no-address_71DkZ {\n  color: rgba(255, 0, 0, 0.8);\n}\n.http_plugin_sidebar-panel-list-item_sNxjR .http_plugin_name_W6ch7 {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.http_plugin_sidebar-panel-list-item__left_lkPc9 {\n  padding: 12px 0 12px 4px;\n  display: flex;\n  min-width: 130px;\n  flex: 1;\n}\n.http_plugin_sidebar-panel-list-item__left--tag_niOv5 {\n  display: none;\n  position: absolute;\n  right: 20px;\n  top: -14px;\n  transform: scale(0.8);\n}\n.http_plugin_sidebar-panel-list-item__right_lHXz6 {\n  display: flex;\n  flex: 0 0 78px;\n  align-items: center;\n  justify-content: space-between;\n  padding-right: 4px;\n  margin-right: 8px;\n}\n.http_plugin_sidebar-panel-list-item__right_lHXz6 .http_plugin_action_pC7aZ {\n  display: flex;\n  align-items: center;\n  font-size: 12px;\n  cursor: pointer;\n  color: rgba(0, 0, 0, 0.85);\n}\n.http_plugin_sidebar-panel-list-item__right_lHXz6 .http_plugin_action_pC7aZ:hover {\n  color: #fa6400;\n}\n.http_plugin_doc-link_CYmpa {\n  cursor: pointer;\n}\n.http_plugin_separator-divider_pi2b\\+ {\n  border-top: 1px dashed #ccc;\n  margin: 4px 0px;\n}\n.http_plugin_ct_1s7\\+J {\n  padding: 0 6px 0;\n  border-radius: 6px;\n  border: 1px solid #eee;\n  margin-bottom: 12px;\n}\n.http_plugin_disabled_KnU1j {\n  opacity: 0.5;\n}\n.http_plugin_chose_dNlu8 {\n  border-bottom-color: transparent;\n  box-shadow: 0 0 6px 2px #ddd;\n}\n.http_plugin_chose_dNlu8:hover {\n  background-color: transparent;\n}\n.http_plugin_dashedDivider_VWUMU {\n  border-top: 1px dashed #ccc;\n  margin: 8px 0;\n}\n.http_plugin_dividerText_CcMpK {\n  font-style: italic;\n  color: #999999;\n  margin-top: 12px;\n}\n.http_plugin_dropdownItem_DI7U5 {\n  background-color: #fff;\n  padding: 4px 0;\n  box-shadow: 0 0 10px 3px #ddd;\n  width: 80px;\n}\n.http_plugin_dropdownItem_DI7U5 .http_plugin_item_5mPYl {\n  padding: 5px 12px;\n  cursor: pointer;\n}\n.http_plugin_dropdownItem_DI7U5 .http_plugin_item_5mPYl:hover {\n  background-color: #f5f7f9;\n}\n.http_plugin_empty_Ho6NQ {\n  height: 42px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  color: #999;\n  font-style: italic;\n  font-size: 12px;\n}\n.http_plugin_folderList_voR2i {\n  position: relative;\n  padding: 0 0 0 12px;\n}\n.http_plugin_folderList_voR2i:before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 12px;\n  background: #eae9e9;\n}\n",""]),o.locals={"sidebar-panel":"http_plugin_sidebar-panel_GHUlq","sidebar-panel-view":"http_plugin_sidebar-panel-view_0gxBI","sidebar-panel-edit":"http_plugin_sidebar-panel-edit_s4Z6O","sidebar-panel-edit-open":"http_plugin_sidebar-panel-edit-open_4lOrY","sidebar-panel-open":"http_plugin_sidebar-panel-open_uPyBS","sidebar-panel-title":"http_plugin_sidebar-panel-title_xa-Ws","sidebar-panel-header":"http_plugin_sidebar-panel-header_4JOSd",icon:"http_plugin_icon_nACnc",rightOperate:"http_plugin_rightOperate_PJ8Pf",globalMock:"http_plugin_globalMock_vv7tM",warning:"http_plugin_warning_Q4d6I","sidebar-panel-header__title":"http_plugin_sidebar-panel-header__title_thLVH","sidebar-panel-list":"http_plugin_sidebar-panel-list_aq3Qk","sidebar-panel-list-item":"http_plugin_sidebar-panel-list-item_sNxjR",active:"http_plugin_active_fNepW","sidebar-panel-list-item__name":"http_plugin_sidebar-panel-list-item__name_vnnd+","sidebar-panel-list-item__content":"http_plugin_sidebar-panel-list-item__content_Q3yhh","sidebar-panel-list-item__param":"http_plugin_sidebar-panel-list-item__param_txKyf","sidebar-panel-list-item__expand":"http_plugin_sidebar-panel-list-item__expand_Xs3Ts","sidebar-panel-list-item__copy":"http_plugin_sidebar-panel-list-item__copy_pvSYY",iconExpand:"http_plugin_iconExpand_RhA7T",tag:"http_plugin_tag_G68CQ","tag__no-address":"http_plugin_tag__no-address_71DkZ",name:"http_plugin_name_W6ch7","sidebar-panel-list-item__left":"http_plugin_sidebar-panel-list-item__left_lkPc9","sidebar-panel-list-item__left--tag":"http_plugin_sidebar-panel-list-item__left--tag_niOv5","sidebar-panel-list-item__right":"http_plugin_sidebar-panel-list-item__right_lHXz6",action:"http_plugin_action_pC7aZ","doc-link":"http_plugin_doc-link_CYmpa","separator-divider":"http_plugin_separator-divider_pi2b+",ct:"http_plugin_ct_1s7+J",disabled:"http_plugin_disabled_KnU1j",chose:"http_plugin_chose_dNlu8",dashedDivider:"http_plugin_dashedDivider_VWUMU",dividerText:"http_plugin_dividerText_CcMpK",dropdownItem:"http_plugin_dropdownItem_DI7U5",item:"http_plugin_item_5mPYl",empty:"http_plugin_empty_Ho6NQ",folderList:"http_plugin_folderList_voR2i"};const i=o},3645:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var i=0;i<this.length;i++){var a=this[i][0];null!=a&&(o[a]=!0)}for(var l=0;l<e.length;l++){var c=[].concat(e[l]);r&&o[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),t.push(c))}},t}},1036:(e,t,n)=>{n.d(t,{S:()=>o,t:()=>i});var r={worker:'/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\n\nvar getTypescriptUrl=function(){var t="https://cdnjs.cloudflare.com/ajax/libs/typescript/4.6.4/typescript.min.js";try{return __TYPESCRIPT_CUSTOM_URL__||t}catch(r){return t}};self.importScripts([getTypescriptUrl()]);var Typescript=self.ts;\n\nvar JsxToken={angleBracket:"jsx-tag-angle-bracket",attributeKey:"jsx-tag-attribute-key",tagName:"jsx-tag-name",expressionBraces:"jsx-expression-braces",text:"jsx-text",orderTokenPrefix:"jsx-tag-order"};\n\nvar getRowAndColumn=function(n,o){for(var t=0,e=0;e+o[t]<n;)e+=o[t],t+=1;return {row:t+1,column:n-e}};var getNodeRange=function(n){return "function"==typeof n.getStart&&"function"==typeof n.getEnd?[n.getStart(),n.getEnd()]:void 0!==n.pos&&void 0!==n.end?[n.pos,n.end]:[0,0]};var calcPosition=function(n,o){var t=getNodeRange(n),e=t[0],r=t[1];return {indexes:[e,r],positions:[getRowAndColumn(e+1,o),getRowAndColumn(r,o)]}};\n\nvar disposeJsxElementOrFragment=function(n){var s=n.node,e=n.lines,t=n.classifications,o=n.config,a=n.context,i="".concat(JsxToken.orderTokenPrefix,"-").concat(a.jsxTagOrder);if(a.jsxTagOrder=a.jsxTagOrder+1>o.jsxTagCycle?1:a.jsxTagOrder+1,s.kind===Typescript.SyntaxKind.JsxSelfClosingElement){var r=calcPosition(s,e).positions,c=calcPosition(s.tagName,e).positions;t.push({start:r[0],end:r[0],tokens:[JsxToken.angleBracket,i]}),t.push({start:__assign(__assign({},r[1]),{column:r[1].column-1}),end:r[1],tokens:[JsxToken.angleBracket,i]}),t.push({start:c[0],end:c[1],tokens:[JsxToken.tagName,i]});}else {var p=s.kind===Typescript.SyntaxKind.JsxFragment?s.openingFragment:s.openingElement,g=s.kind===Typescript.SyntaxKind.JsxFragment?s.closingFragment:s.closingElement,l=calcPosition(p,e).positions,k=calcPosition(g,e).positions;if(t.push({start:l[0],end:l[0],tokens:[JsxToken.angleBracket,i]}),t.push({start:l[1],end:l[1],tokens:[JsxToken.angleBracket,i]}),t.push({start:k[0],end:__assign(__assign({},k[0]),{column:k[0].column+1}),tokens:[JsxToken.angleBracket,i]}),t.push({start:k[1],end:k[1],tokens:[JsxToken.angleBracket,i]}),s.kind===Typescript.SyntaxKind.JsxElement){var m=calcPosition(p.tagName,e).positions,x=calcPosition(g.tagName,e).positions;t.push({start:m[0],end:m[1],tokens:[JsxToken.tagName,i]}),t.push({start:x[0],end:x[1],tokens:[JsxToken.tagName,i]});}}};\n\nvar disposeJsxAttributeKey=function(o){var t=o.node,i=o.lines,s=o.classifications,e=calcPosition(t,i).positions;s.push({start:e[0],end:e[1],tokens:[JsxToken.attributeKey]});};\n\nvar disposeJsxExpression=function(s){var o=s.node,e=s.lines,n=s.classifications,i=calcPosition(o,e).positions;n.push({start:i[0],end:i[0],tokens:[JsxToken.expressionBraces]}),n.push({start:i[1],end:i[1],tokens:[JsxToken.expressionBraces]});};\n\nvar disposeJsxText=function(o){var s=o.node,i=o.lines,t=o.classifications,n=calcPosition(s,i).positions;t.push({start:n[0],end:n[1],tokens:[JsxToken.text]});};\n\nvar disposeNode=function(e){var s=e.node,i=e.index;[Typescript.SyntaxKind.JsxFragment,Typescript.SyntaxKind.JsxElement,Typescript.SyntaxKind.JsxSelfClosingElement].includes(s.kind)&&disposeJsxElementOrFragment(e),s.parent&&s.parent.kind===Typescript.SyntaxKind.JsxAttribute&&s.kind===Typescript.SyntaxKind.Identifier&&0===i&&disposeJsxAttributeKey(e),s.kind===Typescript.SyntaxKind.JsxExpression&&disposeJsxExpression(e),s.kind===Typescript.SyntaxKind.JsxText&&disposeJsxText(e);},walkAST=function(e){disposeNode(e);var s=0;Typescript.forEachChild(e.node,(function(i){return walkAST(__assign(__assign({},e),{node:i,index:s++}))}));},withDefaultConfig=function(e){var s=(e||{}).jsxTagCycle;return {jsxTagCycle:void 0===s?3:s}};var analysis=function(e,s,i){try{var t=[],n=Typescript.createSourceFile(e,s,Typescript.ScriptTarget.ES2020,!0),r=s.split("\\n").map((function(e){return e.length+1}));return walkAST({node:n,lines:r,context:{jsxTagOrder:1},classifications:t,config:withDefaultConfig(i),index:0}),t}catch(e){return (null==i?void 0:i.enableConsole)&&console.error(e),[]}};\n\nself.addEventListener("message",(function(s){var a=s.data,e=a.code,i=a.filePath,n=a.version,o=a.config;try{var l=analysis(i,e,o);self.postMessage({classifications:l,version:n,filePath:i});}catch(s){(null==o?void 0:o.enableConsole)&&console.error(s);}}));\n'},o=function(){return r},i=function(e,t,n){var r=this;this.createWorkerFromPureString=function(e,t){var n;window.URL=window.URL||window.webkitURL,e=e.replace("__TYPESCRIPT_CUSTOM_URL__",(null==t?void 0:t.customTypescriptUrl)?"'".concat(null==t?void 0:t.customTypescriptUrl,"'"):"undefined");try{n=new Blob([e],{type:"application/javascript"})}catch(t){window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder,(n=new window.BlobBuilder).append(e),n=n.getBlob()}var r=new Worker(URL.createObjectURL(n));return URL.revokeObjectURL(n),r},this.highlighterBuilder=function(e){var t=e.editor,n=e.filePath,o=void 0===n?t.getModel().uri.toString():n,i={current:[]},a=function(e){var n=e.data,a=n.classifications,l=n.version,c=n.filePath;requestAnimationFrame((function(){if(c===o&&l===t.getModel().getVersionId()){var e=i.current;i.current=t.deltaDecorations(e,a.map((function(e){return{range:new r.monaco.Range(e.start.row,e.start.column,e.end.row,e.end.column+1),options:{inlineClassName:e.tokens.join(" ")}}})))}}))};return r.worker.addEventListener("message",a),{highlighter:function(e){requestAnimationFrame((function(){var n=e||t.getModel().getValue();r.worker.postMessage({code:n,filePath:o,version:t.getModel().getVersionId()})}))},dispose:function(){r.worker.removeEventListener("message",a)}}},this.monaco=t,"string"==typeof e?this.worker=new Worker(e):e.worker&&"string"==typeof e.worker?this.worker=this.createWorkerFromPureString(e.worker,n):this.worker=e}},6237:(e,t,n)=>{function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function a(e){return function t(){for(var n=this,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return o.length>=e.length?e.apply(this,o):function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return t.apply(n,[].concat(o,r))}}}function l(e){return{}.toString.call(e).includes("Object")}function c(e){return"function"==typeof e}n.d(t,{Z:()=>g});var u=a((function(e,t){throw new Error(e[t]||e.default)}))({initialIsRequired:"initial state is required",initialType:"initial state should be an object",initialContent:"initial state shouldn't be an empty object",handlerType:"handler should be an object or a function",handlersType:"all handlers should be a functions",selectorType:"selector should be a function",changeType:"provided value of changes should be an object",changeField:'it seams you want to change a field in the state which is not specified in the "initial" state',default:"an unknown error accured in `state-local` package"}),s=function(e,t){return l(t)||u("changeType"),Object.keys(t).some((function(t){return n=e,r=t,!Object.prototype.hasOwnProperty.call(n,r);var n,r}))&&u("changeField"),t},p=function(e){c(e)||l(e)||u("handlerType"),l(e)&&Object.values(e).some((function(e){return!c(e)}))&&u("handlersType")},d=function(e){var t;e||u("initialIsRequired"),l(e)||u("initialType"),t=e,Object.keys(t).length||u("initialContent")};function f(e,t){return c(t)?t(e.current):t}function h(e,t){return e.current=i(i({},e.current),t),t}function m(e,t,n){return c(t)?t(e.current):Object.keys(n).forEach((function(n){var r;return null===(r=t[n])||void 0===r?void 0:r.call(t,e.current[n])})),n}const g={create:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};d(e),p(t);var n={current:e},r=a(m)(n,t),o=a(h)(n),i=a(s)(e),l=a(f)(n);return[function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(e){return e};return function(e){c(e)||u("selectorType")}(e),e(n.current)},function(e){!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.reduceRight((function(e,t){return t(e)}),e)}}(r,o,i,l)(e)}]}}},6515:(e,t,n)=>{var r=n(3379),o=n.n(r),i=n(1537);o()(i.Z,{insert:"head",singleton:!1}),i.Z.locals},7929:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(6272);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},3724:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(199);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},9480:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(8467);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},7033:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(478);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},5235:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(4293);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},8025:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(3780);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},4824:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(7758);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},3976:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(1771);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},8502:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(6866);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},1673:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(4647);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},7762:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(6846);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},2808:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(1053);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},393:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(696);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},9787:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(2937);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},7295:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(5302);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},1436:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(660);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},6673:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(3837);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},3295:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(8261);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},7203:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(4628);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},7897:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(2847);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},4126:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(2145);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},9344:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(9832);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},1575:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(6283);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},8327:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(6256);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},2663:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(532);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},5135:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3379),o=n.n(r),i=n(1582);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},3379:(e,t,n)=>{var r,o=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),i=[];function a(e){for(var t=-1,n=0;n<i.length;n++)if(i[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},r=[],o=0;o<e.length;o++){var l=e[o],c=t.base?l[0]+t.base:l[0],u=n[c]||0,s="".concat(c," ").concat(u);n[c]=u+1;var p=a(s),d={css:l[1],media:l[2],sourceMap:l[3]};-1!==p?(i[p].references++,i[p].updater(d)):i.push({identifier:s,updater:m(d,t),references:1}),r.push(s)}return r}function c(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var i=n.nc;i&&(r.nonce=i)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var u,s=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function p(e,t,n,r){var o=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=s(t,o);else{var i=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function d(e,t,n){var r=n.css,o=n.media,i=n.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var f=null,h=0;function m(e,t){var n,r,o;if(t.singleton){var i=h++;n=f||(f=c(t)),r=p.bind(null,n,i,!1),o=p.bind(null,n,i,!0)}else n=c(t),r=d.bind(null,n,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r));var n=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var o=a(n[r]);i[o].references--}for(var c=l(e,t),u=0;u<n.length;u++){var s=a(n[u]);0===i[s].references&&(i[s].updater(),i.splice(s,1))}n=c}}}},8156:e=>{e.exports=__WEBPACK_EXTERNAL_MODULE__8156__},7111:e=>{e.exports=__WEBPACK_EXTERNAL_MODULE__7111__},73:(e,t,n)=>{n.d(t,{Ik:()=>g,ML:()=>y,_m:()=>r.Z});var r=n(7521),o=n(8156),i={display:"flex",position:"relative",textAlign:"initial"},a={width:"100%"},l={display:"none"},c={container:{display:"flex",height:"100%",width:"100%",justifyContent:"center",alignItems:"center"}},u=function({children:e}){return o.createElement("div",{style:c.container},e)},s=(0,o.memo)((function({width:e,height:t,isEditorReady:n,loading:r,_ref:c,className:s,wrapperProps:p}){return o.createElement("section",{style:{...i,width:e,height:t},...p},!n&&o.createElement(u,null,r),o.createElement("div",{ref:c,style:{...a,...!n&&l},className:s}))})),p=function(e){(0,o.useEffect)(e,[])},d=function(e,t,n=!0){let r=(0,o.useRef)(!0);(0,o.useEffect)(r.current||!n?()=>{r.current=!1}:e,t)};function f(){}function h(e,t,n,r){return function(e,t){return e.editor.getModel(m(e,t))}(e,r)||function(e,t,n,r){return e.editor.createModel(t,n,r?m(e,r):void 0)}(e,t,n,r)}function m(e,t){return e.Uri.parse(t)}(0,o.memo)((function({original:e,modified:t,language:n,originalLanguage:i,modifiedLanguage:a,originalModelPath:l,modifiedModelPath:c,keepCurrentOriginalModel:u=!1,keepCurrentModifiedModel:m=!1,theme:g="light",loading:v="Loading...",options:y={},height:_="100%",width:b="100%",className:x,wrapperProps:w={},beforeMount:E=f,onMount:O=f}){let[S,k]=(0,o.useState)(!1),[j,C]=(0,o.useState)(!0),A=(0,o.useRef)(null),Z=(0,o.useRef)(null),T=(0,o.useRef)(null),N=(0,o.useRef)(O),P=(0,o.useRef)(E),R=(0,o.useRef)(!1);p((()=>{let e=r.Z.init();return e.then((e=>(Z.current=e)&&C(!1))).catch((e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e))),()=>A.current?function(){let e=A.current?.getModel();u||e?.original?.dispose(),m||e?.modified?.dispose(),A.current?.dispose()}():e.cancel()})),d((()=>{if(A.current&&Z.current){let t=A.current.getOriginalEditor(),r=h(Z.current,e||"",i||n||"text",l||"");r!==t.getModel()&&t.setModel(r)}}),[l],S),d((()=>{if(A.current&&Z.current){let e=A.current.getModifiedEditor(),r=h(Z.current,t||"",a||n||"text",c||"");r!==e.getModel()&&e.setModel(r)}}),[c],S),d((()=>{let e=A.current.getModifiedEditor();e.getOption(Z.current.editor.EditorOption.readOnly)?e.setValue(t||""):t!==e.getValue()&&(e.executeEdits("",[{range:e.getModel().getFullModelRange(),text:t||"",forceMoveMarkers:!0}]),e.pushUndoStop())}),[t],S),d((()=>{A.current?.getModel()?.original.setValue(e||"")}),[e],S),d((()=>{let{original:e,modified:t}=A.current.getModel();Z.current.editor.setModelLanguage(e,i||n||"text"),Z.current.editor.setModelLanguage(t,a||n||"text")}),[n,i,a],S),d((()=>{Z.current?.editor.setTheme(g)}),[g],S),d((()=>{A.current?.updateOptions(y)}),[y],S);let L=(0,o.useCallback)((()=>{if(!Z.current)return;P.current(Z.current);let r=h(Z.current,e||"",i||n||"text",l||""),o=h(Z.current,t||"",a||n||"text",c||"");A.current?.setModel({original:r,modified:o})}),[n,t,a,e,i,l,c]),M=(0,o.useCallback)((()=>{!R.current&&T.current&&(A.current=Z.current.editor.createDiffEditor(T.current,{automaticLayout:!0,...y}),L(),Z.current?.editor.setTheme(g),k(!0),R.current=!0)}),[y,g,L]);return(0,o.useEffect)((()=>{S&&N.current(A.current,Z.current)}),[S]),(0,o.useEffect)((()=>{!j&&!S&&M()}),[j,S,M]),o.createElement(s,{width:b,height:_,isEditorReady:S,loading:v,_ref:T,className:x,wrapperProps:w})}));var g=function(){let[e,t]=(0,o.useState)(r.Z.__getMonacoInstance());return p((()=>{let n;return e||(n=r.Z.init(),n.then((e=>{t(e)}))),()=>n?.cancel()})),e},v=new Map,y=(0,o.memo)((function({defaultValue:e,defaultLanguage:t,defaultPath:n,value:i,language:a,path:l,theme:c="light",line:u,loading:m="Loading...",options:g={},overrideServices:y={},saveViewState:_=!0,keepCurrentModel:b=!1,width:x="100%",height:w="100%",className:E,wrapperProps:O={},beforeMount:S=f,onMount:k=f,onChange:j,onValidate:C=f}){let[A,Z]=(0,o.useState)(!1),[T,N]=(0,o.useState)(!0),P=(0,o.useRef)(null),R=(0,o.useRef)(null),L=(0,o.useRef)(null),M=(0,o.useRef)(k),I=(0,o.useRef)(S),F=(0,o.useRef)(),D=(0,o.useRef)(i),B=function(e){let t=(0,o.useRef)();return(0,o.useEffect)((()=>{t.current=e}),[e]),t.current}(l),U=(0,o.useRef)(!1),z=(0,o.useRef)(!1);p((()=>{let e=r.Z.init();return e.then((e=>(P.current=e)&&N(!1))).catch((e=>"cancelation"!==e?.type&&console.error("Monaco initialization: error:",e))),()=>R.current?(F.current?.dispose(),b?_&&v.set(l,R.current.saveViewState()):R.current.getModel()?.dispose(),void R.current.dispose()):e.cancel()})),d((()=>{let r=h(P.current,e||i||"",t||a||"",l||n||"");r!==R.current?.getModel()&&(_&&v.set(B,R.current?.saveViewState()),R.current?.setModel(r),_&&R.current?.restoreViewState(v.get(l)))}),[l],A),d((()=>{R.current?.updateOptions(g)}),[g],A),d((()=>{!R.current||void 0===i||(R.current.getOption(P.current.editor.EditorOption.readOnly)?R.current.setValue(i):i!==R.current.getValue()&&(z.current=!0,R.current.executeEdits("",[{range:R.current.getModel().getFullModelRange(),text:i,forceMoveMarkers:!0}]),R.current.pushUndoStop(),z.current=!1))}),[i],A),d((()=>{let e=R.current?.getModel();e&&a&&P.current?.editor.setModelLanguage(e,a)}),[a],A),d((()=>{void 0!==u&&R.current?.revealLine(u)}),[u],A),d((()=>{P.current?.editor.setTheme(c)}),[c],A);let V=(0,o.useCallback)((()=>{if(L.current&&P.current&&!U.current){I.current(P.current);let r=l||n,o=h(P.current,i||e||"",t||a||"",r||"");R.current=P.current?.editor.create(L.current,{model:o,automaticLayout:!0,...g},y),_&&R.current.restoreViewState(v.get(r)),P.current.editor.setTheme(c),void 0!==u&&R.current.revealLine(u),Z(!0),U.current=!0}}),[e,t,n,i,a,l,g,y,_,c,u]);return(0,o.useEffect)((()=>{A&&M.current(R.current,P.current)}),[A]),(0,o.useEffect)((()=>{!T&&!A&&V()}),[T,A,V]),D.current=i,(0,o.useEffect)((()=>{A&&j&&(F.current?.dispose(),F.current=R.current?.onDidChangeModelContent((e=>{z.current||j(R.current.getValue(),e)})))}),[A,j]),(0,o.useEffect)((()=>{if(A){let e=P.current.editor.onDidChangeMarkers((e=>{let t=R.current.getModel()?.uri;if(t&&e.find((e=>e.path===t.path))){let e=P.current.editor.getModelMarkers({resource:t});C?.(e)}}));return()=>{e?.dispose()}}return()=>{}}),[A,C]),o.createElement(s,{width:x,height:w,isEditorReady:A,loading:m,_ref:L,className:E,wrapperProps:O})}))},8624:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(8113),o=n(4198),i=n(6672),a=n(1333);const l={http:o.Z,xhr:i.Z};r.Z.forEach(l,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const c={getAdapter:e=>{e=r.Z.isArray(e)?e:[e];const{length:t}=e;let n,o;for(let i=0;i<t&&(n=e[i],!(o=r.Z.isString(n)?l[n.toLowerCase()]:n));i++);if(!o){if(!1===o)throw new a.Z(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT");throw new Error(r.Z.hasOwnProp(l,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`)}if(!r.Z.isFunction(o))throw new TypeError("adapter is not a function");return o},adapters:l}},6672:(e,t,n)=>{n.d(t,{Z:()=>v});var r=n(8113),o=n(1992),i=n(8308),a=n(3343),l=n(5315),c=n(8738),u=n(2913),s=n(1333),p=n(9619),d=n(2312),f=n(9698),h=n(1150),m=n(2141);function g(e,t){let n=0;const r=(0,m.Z)(50,250);return o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-n,c=r(l);n=i;const u={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&i<=a?(a-i)/c:void 0,event:o};u[t?"download":"upload"]=!0,e(u)}}const v="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let m=e.data;const v=h.Z.from(e.headers).normalize(),y=e.responseType;let _;function b(){e.cancelToken&&e.cancelToken.unsubscribe(_),e.signal&&e.signal.removeEventListener("abort",_)}r.Z.isFormData(m)&&(f.Z.isStandardBrowserEnv||f.Z.isStandardBrowserWebWorkerEnv?v.setContentType(!1):v.setContentType("multipart/form-data;",!1));let x=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";v.set("Authorization","Basic "+btoa(t+":"+n))}const w=(0,l.Z)(e.baseURL,e.url);function E(){if(!x)return;const r=h.Z.from("getAllResponseHeaders"in x&&x.getAllResponseHeaders()),i={data:y&&"text"!==y&&"json"!==y?x.response:x.responseText,status:x.status,statusText:x.statusText,headers:r,config:e,request:x};(0,o.Z)((function(e){t(e),b()}),(function(e){n(e),b()}),i),x=null}if(x.open(e.method.toUpperCase(),(0,a.Z)(w,e.params,e.paramsSerializer),!0),x.timeout=e.timeout,"onloadend"in x?x.onloadend=E:x.onreadystatechange=function(){x&&4===x.readyState&&(0!==x.status||x.responseURL&&0===x.responseURL.indexOf("file:"))&&setTimeout(E)},x.onabort=function(){x&&(n(new s.Z("Request aborted",s.Z.ECONNABORTED,e,x)),x=null)},x.onerror=function(){n(new s.Z("Network Error",s.Z.ERR_NETWORK,e,x)),x=null},x.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const r=e.transitional||u.Z;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new s.Z(t,r.clarifyTimeoutError?s.Z.ETIMEDOUT:s.Z.ECONNABORTED,e,x)),x=null},f.Z.isStandardBrowserEnv){const t=(e.withCredentials||(0,c.Z)(w))&&e.xsrfCookieName&&i.Z.read(e.xsrfCookieName);t&&v.set(e.xsrfHeaderName,t)}void 0===m&&v.setContentType(null),"setRequestHeader"in x&&r.Z.forEach(v.toJSON(),(function(e,t){x.setRequestHeader(t,e)})),r.Z.isUndefined(e.withCredentials)||(x.withCredentials=!!e.withCredentials),y&&"json"!==y&&(x.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&x.addEventListener("progress",g(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&x.upload&&x.upload.addEventListener("progress",g(e.onUploadProgress)),(e.cancelToken||e.signal)&&(_=t=>{x&&(n(!t||t.type?new p.Z(null,e,x):t),x.abort(),x=null)},e.cancelToken&&e.cancelToken.subscribe(_),e.signal&&(e.signal.aborted?_():e.signal.addEventListener("abort",_)));const O=(0,d.Z)(w);O&&-1===f.Z.protocols.indexOf(O)?n(new s.Z("Unsupported protocol "+O+":",s.Z.ERR_BAD_REQUEST,e)):x.send(m||null)}))}},5274:(e,t,n)=>{n.d(t,{Z:()=>x});var r=n(8113),o=n(6524),i=n(5411),a=n(8636),l=n(6239),c=n(4510),u=n(9619),s=n(2629),p=n(9126),d=n(2112),f=n(5238),h=n(1333),m=n(7990),g=n(5511),v=n(1150),y=n(8624),_=n(2097);const b=function e(t){const n=new i.Z(t),l=(0,o.Z)(i.Z.prototype.request,n);return r.Z.extend(l,i.Z.prototype,n,{allOwnKeys:!0}),r.Z.extend(l,n,null,{allOwnKeys:!0}),l.create=function(n){return e((0,a.Z)(t,n))},l}(l.Z);b.Axios=i.Z,b.CanceledError=u.Z,b.CancelToken=s.Z,b.isCancel=p.Z,b.VERSION=d.q,b.toFormData=f.Z,b.AxiosError=h.Z,b.Cancel=b.CanceledError,b.all=function(e){return Promise.all(e)},b.spread=m.Z,b.isAxiosError=g.Z,b.mergeConfig=a.Z,b.AxiosHeaders=v.Z,b.formToJSON=e=>(0,c.Z)(r.Z.isHTMLForm(e)?new FormData(e):e),b.getAdapter=y.Z.getAdapter,b.HttpStatusCode=_.Z,b.default=b;const x=b},2629:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(9619);class o{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,o,i){n.reason||(n.reason=new r.Z(e,o,i),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new o((function(t){e=t})),cancel:e}}}const i=o},9619:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(1333);function o(e,t,n){r.Z.call(this,null==e?"canceled":e,r.Z.ERR_CANCELED,t,n),this.name="CanceledError"}n(8113).Z.inherits(o,r.Z,{__CANCEL__:!0});const i=o},9126:(e,t,n)=>{function r(e){return!(!e||!e.__CANCEL__)}n.d(t,{Z:()=>r})},5411:(e,t,n)=>{n.d(t,{Z:()=>f});var r=n(8113),o=n(3343),i=n(2881),a=n(4352),l=n(8636),c=n(5315),u=n(6011),s=n(1150);const p=u.Z.validators;class d{constructor(e){this.defaults=e,this.interceptors={request:new i.Z,response:new i.Z}}request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=(0,l.Z)(this.defaults,t);const{transitional:n,paramsSerializer:o,headers:i}=t;void 0!==n&&u.Z.assertOptions(n,{silentJSONParsing:p.transitional(p.boolean),forcedJSONParsing:p.transitional(p.boolean),clarifyTimeoutError:p.transitional(p.boolean)},!1),null!=o&&(r.Z.isFunction(o)?t.paramsSerializer={serialize:o}:u.Z.assertOptions(o,{encode:p.function,serialize:p.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let c=i&&r.Z.merge(i.common,i[t.method]);i&&r.Z.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),t.headers=s.Z.concat(c,i);const d=[];let f=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(f=f&&e.synchronous,d.unshift(e.fulfilled,e.rejected))}));const h=[];let m;this.interceptors.response.forEach((function(e){h.push(e.fulfilled,e.rejected)}));let g,v=0;if(!f){const e=[a.Z.bind(this),void 0];for(e.unshift.apply(e,d),e.push.apply(e,h),g=e.length,m=Promise.resolve(t);v<g;)m=m.then(e[v++],e[v++]);return m}g=d.length;let y=t;for(v=0;v<g;){const t=d[v++],n=d[v++];try{y=t(y)}catch(e){n.call(this,e);break}}try{m=a.Z.call(this,y)}catch(e){return Promise.reject(e)}for(v=0,g=h.length;v<g;)m=m.then(h[v++],h[v++]);return m}getUri(e){e=(0,l.Z)(this.defaults,e);const t=(0,c.Z)(e.baseURL,e.url);return(0,o.Z)(t,e.params,e.paramsSerializer)}}r.Z.forEach(["delete","get","head","options"],(function(e){d.prototype[e]=function(t,n){return this.request((0,l.Z)(n||{},{method:e,url:t,data:(n||{}).data}))}})),r.Z.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request((0,l.Z)(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}d.prototype[e]=t(),d.prototype[e+"Form"]=t(!0)}));const f=d},1333:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(8113);function o(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}r.Z.inherits(o,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:r.Z.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const i=o.prototype,a={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{a[e]={value:e}})),Object.defineProperties(o,a),Object.defineProperty(i,"isAxiosError",{value:!0}),o.from=(e,t,n,a,l,c)=>{const u=Object.create(i);return r.Z.toFlatObject(e,u,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),o.call(u,e.message,t,n,a,l),u.cause=e,u.name=e.name,c&&Object.assign(u,c),u};const l=o},1150:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(8113),o=n(4954);const i=Symbol("internals");function a(e){return e&&String(e).trim().toLowerCase()}function l(e){return!1===e||null==e?e:r.Z.isArray(e)?e.map(l):String(e)}function c(e,t,n,o,i){return r.Z.isFunction(o)?o.call(this,t,n):(i&&(t=n),r.Z.isString(t)?r.Z.isString(o)?-1!==t.indexOf(o):r.Z.isRegExp(o)?o.test(t):void 0:void 0)}class u{constructor(e){e&&this.set(e)}set(e,t,n){const i=this;function c(e,t,n){const o=a(t);if(!o)throw new Error("header name must be a non-empty string");const c=r.Z.findKey(i,o);(!c||void 0===i[c]||!0===n||void 0===n&&!1!==i[c])&&(i[c||t]=l(e))}const u=(e,t)=>r.Z.forEach(e,((e,n)=>c(e,n,t)));return r.Z.isPlainObject(e)||e instanceof this.constructor?u(e,t):r.Z.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?u((0,o.Z)(e),t):null!=e&&c(t,e,n),this}get(e,t){if(e=a(e)){const n=r.Z.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(r.Z.isFunction(t))return t.call(this,e,n);if(r.Z.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=a(e)){const n=r.Z.findKey(this,e);return!(!n||void 0===this[n]||t&&!c(0,this[n],n,t))}return!1}delete(e,t){const n=this;let o=!1;function i(e){if(e=a(e)){const i=r.Z.findKey(n,e);!i||t&&!c(0,n[i],i,t)||(delete n[i],o=!0)}}return r.Z.isArray(e)?e.forEach(i):i(e),o}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!c(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return r.Z.forEach(this,((o,i)=>{const a=r.Z.findKey(n,i);if(a)return t[a]=l(o),void delete t[i];const c=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(i):String(i).trim();c!==i&&delete t[i],t[c]=l(o),n[c]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return r.Z.forEach(this,((n,o)=>{null!=n&&!1!==n&&(t[o]=e&&r.Z.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[i]=this[i]={accessors:{}}).accessors,n=this.prototype;function o(e){const o=a(e);t[o]||(function(e,t){const n=r.Z.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[o]=!0)}return r.Z.isArray(e)?e.forEach(o):o(e),this}}u.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),r.Z.reduceDescriptors(u.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),r.Z.freezeMethods(u);const s=u},2881:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(8113);const o=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){r.Z.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}},5315:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(8474),o=n(4318);function i(e,t){return e&&!(0,r.Z)(t)?(0,o.Z)(e,t):t}},4352:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(5145),o=n(9126),i=n(6239),a=n(9619),l=n(1150),c=n(8624);function u(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new a.Z(null,e)}function s(e){return u(e),e.headers=l.Z.from(e.headers),e.data=r.Z.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),c.Z.getAdapter(e.adapter||i.Z.adapter)(e).then((function(t){return u(e),t.data=r.Z.call(e,e.transformResponse,t),t.headers=l.Z.from(t.headers),t}),(function(t){return(0,o.Z)(t)||(u(e),t&&t.response&&(t.response.data=r.Z.call(e,e.transformResponse,t.response),t.response.headers=l.Z.from(t.response.headers))),Promise.reject(t)}))}},8636:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8113),o=n(1150);const i=e=>e instanceof o.Z?e.toJSON():e;function a(e,t){t=t||{};const n={};function o(e,t,n){return r.Z.isPlainObject(e)&&r.Z.isPlainObject(t)?r.Z.merge.call({caseless:n},e,t):r.Z.isPlainObject(t)?r.Z.merge({},t):r.Z.isArray(t)?t.slice():t}function a(e,t,n){return r.Z.isUndefined(t)?r.Z.isUndefined(e)?void 0:o(void 0,e,n):o(e,t,n)}function l(e,t){if(!r.Z.isUndefined(t))return o(void 0,t)}function c(e,t){return r.Z.isUndefined(t)?r.Z.isUndefined(e)?void 0:o(void 0,e):o(void 0,t)}function u(n,r,i){return i in t?o(n,r):i in e?o(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:c,transformRequest:c,transformResponse:c,paramsSerializer:c,timeout:c,timeoutMessage:c,withCredentials:c,adapter:c,responseType:c,xsrfCookieName:c,xsrfHeaderName:c,onUploadProgress:c,onDownloadProgress:c,decompress:c,maxContentLength:c,maxBodyLength:c,beforeRedirect:c,transport:c,httpAgent:c,httpsAgent:c,cancelToken:c,socketPath:c,responseEncoding:c,validateStatus:u,headers:(e,t)=>a(i(e),i(t),!0)};return r.Z.forEach(Object.keys(Object.assign({},e,t)),(function(o){const i=s[o]||a,l=i(e[o],t[o],o);r.Z.isUndefined(l)&&i!==u||(n[o]=l)})),n}},1992:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(1333);function o(e,t,n){const o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(new r.Z("Request failed with status code "+n.status,[r.Z.ERR_BAD_REQUEST,r.Z.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}},5145:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8113),o=n(6239),i=n(1150);function a(e,t){const n=this||o.Z,a=t||n,l=i.Z.from(a.headers);let c=a.data;return r.Z.forEach(e,(function(e){c=e.call(n,c,l.normalize(),t?t.status:void 0)})),l.normalize(),c}},6239:(e,t,n)=>{n.d(t,{Z:()=>p});var r=n(8113),o=n(1333),i=n(2913),a=n(5238),l=n(6856),c=n(9698),u=n(4510);const s={transitional:i.Z,adapter:c.Z.isNode?"http":"xhr",transformRequest:[function(e,t){const n=t.getContentType()||"",o=n.indexOf("application/json")>-1,i=r.Z.isObject(e);if(i&&r.Z.isHTMLForm(e)&&(e=new FormData(e)),r.Z.isFormData(e))return o&&o?JSON.stringify((0,u.Z)(e)):e;if(r.Z.isArrayBuffer(e)||r.Z.isBuffer(e)||r.Z.isStream(e)||r.Z.isFile(e)||r.Z.isBlob(e))return e;if(r.Z.isArrayBufferView(e))return e.buffer;if(r.Z.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return(0,l.Z)(e,this.formSerializer).toString();if((c=r.Z.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return(0,a.Z)(c?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||o?(t.setContentType("application/json",!1),function(e,t,n){if(r.Z.isString(e))try{return(0,JSON.parse)(e),r.Z.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||s.transitional,n=t&&t.forcedJSONParsing,i="json"===this.responseType;if(e&&r.Z.isString(e)&&(n&&!this.responseType||i)){const n=!(t&&t.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw o.Z.from(e,o.Z.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:c.Z.classes.FormData,Blob:c.Z.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};r.Z.forEach(["delete","get","head","post","put","patch"],(e=>{s.headers[e]={}}));const p=s},2913:(e,t,n)=>{n.d(t,{Z:()=>r});const r={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1}},2112:(e,t,n)=>{n.d(t,{q:()=>r});const r="1.5.0"},7709:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(5238);function o(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function i(e,t){this._pairs=[],e&&(0,r.Z)(e,this,t)}const a=i.prototype;a.append=function(e,t){this._pairs.push([e,t])},a.toString=function(e){const t=e?function(t){return e.call(this,t,o)}:o;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const l=i},2097:(e,t,n)=>{n.d(t,{Z:()=>o});const r={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(r).forEach((([e,t])=>{r[t]=e}));const o=r},6524:(e,t,n)=>{function r(e,t){return function(){return e.apply(t,arguments)}}n.d(t,{Z:()=>r})},3343:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8113),o=n(7709);function i(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function a(e,t,n){if(!t)return e;const a=n&&n.encode||i,l=n&&n.serialize;let c;if(c=l?l(t,n):r.Z.isURLSearchParams(t)?t.toString():new o.Z(t,n).toString(a),c){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+c}return e}},4318:(e,t,n)=>{function r(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}n.d(t,{Z:()=>r})},8308:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(8113);const o=n(9698).Z.isStandardBrowserEnv?{write:function(e,t,n,o,i,a){const l=[];l.push(e+"="+encodeURIComponent(t)),r.Z.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),r.Z.isString(o)&&l.push("path="+o),r.Z.isString(i)&&l.push("domain="+i),!0===a&&l.push("secure"),document.cookie=l.join("; ")},read:function(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},4510:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(8113);const o=function(e){function t(e,n,o,i){let a=e[i++];const l=Number.isFinite(+a),c=i>=e.length;return a=!a&&r.Z.isArray(o)?o.length:a,c?(r.Z.hasOwnProp(o,a)?o[a]=[o[a],n]:o[a]=n,!l):(o[a]&&r.Z.isObject(o[a])||(o[a]=[]),t(e,n,o[a],i)&&r.Z.isArray(o[a])&&(o[a]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}(o[a])),!l)}if(r.Z.isFormData(e)&&r.Z.isFunction(e.entries)){const n={};return r.Z.forEachEntry(e,((e,o)=>{t(function(e){return r.Z.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),o,n,0)})),n}return null}},8474:(e,t,n)=>{function r(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}n.d(t,{Z:()=>r})},5511:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(8113);function o(e){return r.Z.isObject(e)&&!0===e.isAxiosError}},8738:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(8113);const o=n(9698).Z.isStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function o(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=o(window.location.href),function(e){const t=r.Z.isString(e)?o(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0}},4198:(e,t,n)=>{n.d(t,{Z:()=>r});const r=null},4954:(e,t,n)=>{n.d(t,{Z:()=>o});const r=n(8113).Z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),o=e=>{const t={};let n,o,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),o=e.substring(i+1).trim(),!n||t[n]&&r[n]||("set-cookie"===n?t[n]?t[n].push(o):t[n]=[o]:t[n]=t[n]?t[n]+", "+o:o)})),t}},2312:(e,t,n)=>{function r(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}n.d(t,{Z:()=>r})},2141:(e,t,n)=>{n.d(t,{Z:()=>r});const r=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,i=0,a=0;return t=void 0!==t?t:1e3,function(l){const c=Date.now(),u=r[a];o||(o=c),n[i]=l,r[i]=c;let s=a,p=0;for(;s!==i;)p+=n[s++],s%=e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),c-o<t)return;const d=u&&c-u;return d?Math.round(1e3*p/d):void 0}}},7990:(e,t,n)=>{function r(e){return function(t){return e.apply(null,t)}}n.d(t,{Z:()=>r})},5238:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(8113),o=n(1333),i=n(4198);function a(e){return r.Z.isPlainObject(e)||r.Z.isArray(e)}function l(e){return r.Z.endsWith(e,"[]")?e.slice(0,-2):e}function c(e,t,n){return e?e.concat(t).map((function(e,t){return e=l(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const u=r.Z.toFlatObject(r.Z,{},null,(function(e){return/^is[A-Z]/.test(e)})),s=function(e,t,n){if(!r.Z.isObject(e))throw new TypeError("target must be an object");t=t||new(i.Z||FormData);const s=(n=r.Z.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!r.Z.isUndefined(t[e])}))).metaTokens,p=n.visitor||g,d=n.dots,f=n.indexes,h=(n.Blob||"undefined"!=typeof Blob&&Blob)&&r.Z.isSpecCompliantForm(t);if(!r.Z.isFunction(p))throw new TypeError("visitor must be a function");function m(e){if(null===e)return"";if(r.Z.isDate(e))return e.toISOString();if(!h&&r.Z.isBlob(e))throw new o.Z("Blob is not supported. Use a Buffer instead.");return r.Z.isArrayBuffer(e)||r.Z.isTypedArray(e)?h&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function g(e,n,o){let i=e;if(e&&!o&&"object"==typeof e)if(r.Z.endsWith(n,"{}"))n=s?n:n.slice(0,-2),e=JSON.stringify(e);else if(r.Z.isArray(e)&&function(e){return r.Z.isArray(e)&&!e.some(a)}(e)||(r.Z.isFileList(e)||r.Z.endsWith(n,"[]"))&&(i=r.Z.toArray(e)))return n=l(n),i.forEach((function(e,o){!r.Z.isUndefined(e)&&null!==e&&t.append(!0===f?c([n],o,d):null===f?n:n+"[]",m(e))})),!1;return!!a(e)||(t.append(c(o,n,d),m(e)),!1)}const v=[],y=Object.assign(u,{defaultVisitor:g,convertValue:m,isVisitable:a});if(!r.Z.isObject(e))throw new TypeError("data must be an object");return function e(n,o){if(!r.Z.isUndefined(n)){if(-1!==v.indexOf(n))throw Error("Circular reference detected in "+o.join("."));v.push(n),r.Z.forEach(n,(function(n,i){!0===(!(r.Z.isUndefined(n)||null===n)&&p.call(t,n,r.Z.isString(i)?i.trim():i,o,y))&&e(n,o?o.concat(i):[i])})),v.pop()}}(e),t}},6856:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(8113),o=n(5238),i=n(9698);function a(e,t){return(0,o.Z)(e,new i.Z.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,o){return i.Z.isNode&&r.Z.isBuffer(e)?(this.append(t,e.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}},6011:(e,t,n)=>{n.d(t,{Z:()=>l});var r=n(2112),o=n(1333);const i={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{i[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const a={};i.transitional=function(e,t,n){function i(e,t){return"[Axios v"+r.q+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,r,l)=>{if(!1===e)throw new o.Z(i(r," has been removed"+(t?" in "+t:"")),o.Z.ERR_DEPRECATED);return t&&!a[r]&&(a[r]=!0,console.warn(i(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,l)}};const l={assertOptions:function(e,t,n){if("object"!=typeof e)throw new o.Z("options must be an object",o.Z.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const a=r[i],l=t[a];if(l){const t=e[a],n=void 0===t||l(t,a,e);if(!0!==n)throw new o.Z("option "+a+" must be "+n,o.Z.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new o.Z("Unknown option "+a,o.Z.ERR_BAD_OPTION)}},validators:i}},2004:(e,t,n)=>{n.d(t,{Z:()=>r});const r="undefined"!=typeof Blob?Blob:null},1951:(e,t,n)=>{n.d(t,{Z:()=>r});const r="undefined"!=typeof FormData?FormData:null},3358:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(7709);const o="undefined"!=typeof URLSearchParams?URLSearchParams:r.Z},9698:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(3358),o=n(1951),i=n(2004);const a=(()=>{let e;return("undefined"==typeof navigator||"ReactNative"!==(e=navigator.product)&&"NativeScript"!==e&&"NS"!==e)&&"undefined"!=typeof window&&"undefined"!=typeof document})(),l="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,c={isBrowser:!0,classes:{URLSearchParams:r.Z,FormData:o.Z,Blob:i.Z},isStandardBrowserEnv:a,isStandardBrowserWebWorkerEnv:l,protocols:["http","https","file","blob","url","data"]}},8113:(e,t,n)=>{n.d(t,{Z:()=>I});var r=n(6524);const{toString:o}=Object.prototype,{getPrototypeOf:i}=Object,a=(l=Object.create(null),e=>{const t=o.call(e);return l[t]||(l[t]=t.slice(8,-1).toLowerCase())});var l;const c=e=>(e=e.toLowerCase(),t=>a(t)===e),u=e=>t=>typeof t===e,{isArray:s}=Array,p=u("undefined"),d=c("ArrayBuffer"),f=u("string"),h=u("function"),m=u("number"),g=e=>null!==e&&"object"==typeof e,v=e=>{if("object"!==a(e))return!1;const t=i(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},y=c("Date"),_=c("File"),b=c("Blob"),x=c("FileList"),w=c("URLSearchParams");function E(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),s(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function O(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const S="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:__webpack_require__.g,k=e=>!p(e)&&e!==S,j=(C="undefined"!=typeof Uint8Array&&i(Uint8Array),e=>C&&e instanceof C);var C;const A=c("HTMLFormElement"),Z=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),T=c("RegExp"),N=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};E(n,((n,o)=>{let i;!1!==(i=t(n,o,e))&&(r[o]=i||n)})),Object.defineProperties(e,r)},P="abcdefghijklmnopqrstuvwxyz",R="0123456789",L={DIGIT:R,ALPHA:P,ALPHA_DIGIT:P+P.toUpperCase()+R},M=c("AsyncFunction"),I={isArray:s,isArrayBuffer:d,isBuffer:function(e){return null!==e&&!p(e)&&null!==e.constructor&&!p(e.constructor)&&h(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||h(e.append)&&("formdata"===(t=a(e))||"object"===t&&h(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&d(e.buffer),t},isString:f,isNumber:m,isBoolean:e=>!0===e||!1===e,isObject:g,isPlainObject:v,isUndefined:p,isDate:y,isFile:_,isBlob:b,isRegExp:T,isFunction:h,isStream:e=>g(e)&&h(e.pipe),isURLSearchParams:w,isTypedArray:j,isFileList:x,forEach:E,merge:function e(){const{caseless:t}=k(this)&&this||{},n={},r=(r,o)=>{const i=t&&O(n,o)||o;v(n[i])&&v(r)?n[i]=e(n[i],r):v(r)?n[i]=e({},r):s(r)?n[i]=r.slice():n[i]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&E(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:o}={})=>(E(t,((t,o)=>{n&&h(t)?e[o]=(0,r.Z)(t,n):e[o]=t}),{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,l;const c={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)l=o[a],r&&!r(l,e,t)||c[l]||(t[l]=e[l],c[l]=!0);e=!1!==n&&i(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:a,kindOfTest:c,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(s(e))return e;let t=e.length;if(!m(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:A,hasOwnProperty:Z,hasOwnProp:Z,reduceDescriptors:N,freezeMethods:e=>{N(e,((t,n)=>{if(h(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];h(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return s(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:O,global:S,isContextDefined:k,ALPHABET:L,generateString:(e=16,t=L.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&h(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(g(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=s(e)?[]:{};return E(e,((e,t)=>{const i=n(e,r+1);!p(i)&&(o[t]=i)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:M,isThenable:e=>e&&(g(e)||h(e))&&h(e.then)&&h(e.catch)}},4147:e=>{e.exports=JSON.parse('{"u2":"@mybricks/plugin-connector-http","i8":"1.2.13"}')}},__webpack_module_cache__={};function __nested_webpack_require_311591__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={id:e,exports:{}};return __webpack_modules__[e](n,n.exports,__nested_webpack_require_311591__),n.exports}__nested_webpack_require_311591__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __nested_webpack_require_311591__.d(t,{a:t}),t},__nested_webpack_require_311591__.d=(e,t)=>{for(var n in t)__nested_webpack_require_311591__.o(t,n)&&!__nested_webpack_require_311591__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__nested_webpack_require_311591__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__nested_webpack_require_311591__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__nested_webpack_require_311591__.nc=void 0;var __nested_webpack_exports__={};return(()=>{__nested_webpack_require_311591__.r(__nested_webpack_exports__),__nested_webpack_require_311591__.d(__nested_webpack_exports__,{Items:()=>l,call:()=>o.call,default:()=>s,mock:()=>a.mock});var e=__nested_webpack_require_311591__(2453),t=__nested_webpack_require_311591__(9493),n=__nested_webpack_require_311591__(3887),r=__nested_webpack_require_311591__(5525),o=__nested_webpack_require_311591__(8704),i=__nested_webpack_require_311591__(8543),a=__nested_webpack_require_311591__(2566),l=__nested_webpack_require_311591__(2544),c=__nested_webpack_require_311591__(4147),u=__nested_webpack_require_311591__(6178);function s(){var l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{name:r.MK,namespace:r.MK,title:"连接器",description:"连接器",data:n.Z,onLoad:function(){var e,t,n,o,i,a;this.data&&(!1===l.isPrivatization&&(0,r.SL)(),null===(e=this.data.connectors)||void 0===e||e.forEach((function(e){var t,n;(null===(n=null===(t=e.content)||void 0===t?void 0:t.markList)||void 0===n?void 0:n.length)||(e.content.markList=[{title:"默认",id:"default",predicate:{},outputKeys:e.content.outputKeys||[],excludeKeys:e.content.excludeKeys||[],outputSchema:e.content.outputSchema||{},resultSchema:e.content.resultSchema}],delete e.content.outputKeys,delete e.content.excludeKeys,delete e.content.outputSchema,delete e.content.resultSchema)})),this.data.config=this.data.config||{paramsFn:(null===(t=null==l?void 0:l.initialValue)||void 0===t?void 0:t.paramsFn)||r.Ys,resultFn:(null===(n=null==l?void 0:l.initialValue)||void 0===n?void 0:n.resultFn)||r.Q1,errorResultFn:(null===(o=null==l?void 0:l.initialValue)||void 0===o?void 0:o.errorResultFn)||r.tn},this.data.config.resultFn=this.data.config.resultFn||(null===(i=null==l?void 0:l.initialValue)||void 0===i?void 0:i.resultFn)||r.Q1,this.data.config.errorResultFn=this.data.config.errorResultFn||(null===(a=null==l?void 0:l.initialValue)||void 0===a?void 0:a.errorResultFn)||r.tn)},callConnector:function(e,t,n){var r,i=Object.assign(Object.assign({},this.data),{connectors:(0,u.uF)(this.data.connectors)});if("test"!==e.mode&&(i.config.globalMock||(null==n?void 0:n.openMock)))return(0,a.mock)(Object.assign(Object.assign({},e),n));var l="test"===e.mode?e:null!==(r=i.connectors.find((function(t){return t.id===e.id})))&&void 0!==r?r:e.script?e:null;if(l){var c=Object.assign({},l);return c.script||(c=Object.assign(Object.assign(Object.assign({},c),{globalParamsFn:i.config.paramsFn,globalResultFn:i.config.resultFn,globalErrorResultFn:i.config.errorResultFn}),l.content||{})),(0,o.call)(Object.assign(Object.assign({useProxy:!0},e),c),t,n)}return Promise.reject("接口不存在，请检查连接器插件中接口配置")},toJSON:function(e){var t=e.data,n=Object.assign(Object.assign({},t),{connectors:(0,u.uF)(t.connectors)});if(n.config||(n.config={}),null==l?void 0:l.pure){try{n.config.paramsFn=(0,i.T)(n.config.paramsFn),n.config.resultFn=(0,i.T)(n.config.resultFn),n.config.errorResultFn=(0,i.T)(n.config.errorResultFn),n.connectors=n.connectors.map((function(e){var t=e.type,r=e.id,o=e.content,a=o.input,l=o.output,c=o.method,u=o.path,s=o.markList;return{type:t,id:r,input:(0,i.T)(a),output:(0,i.T)(l),method:c,path:null==u?void 0:u.trim(),globalMock:n.config.globalMock,markList:s.map((function(e){return{id:e.id,title:e.title,predicate:e.predicate,excludeKeys:e.excludeKeys,outputKeys:e.outputKeys}}))}}))}catch(e){console.log("连接器 toJSON 错误",e)}return n}return n.connectors.map((function(e){return{id:e.id,type:e.type,title:e.content.title,globalMock:n.config.globalMock,script:(0,i.i)(Object.assign(Object.assign({},e.content),{globalParamsFn:n.config.paramsFn,globalResultFn:n.config.resultFn,globalErrorResultFn:n.config.errorResultFn}))}}))},getConnectorScript:function(e){var t="test"===e.mode,n=t?e:(0,u.uF)(this.data.connectors).find((function(t){return t.id===e.id}));try{return{id:n.id,type:n.type,title:n.content.title,script:(0,i.i)(Object.assign(Object.assign({isTestMode:t},n.content),{globalParamsFn:this.data.config.paramsFn,globalResultFn:this.data.config.resultFn,globalErrorResultFn:this.data.config.errorResultFn}))}}catch(e){throw Error("找不到对应连接器数据")}},contributes:{sliderView:{tab:{title:"连接器",icon:t.qv,apiSet:["connector"],render:function(t){return React.createElement(e.Z,Object.assign({},l,t))}}}}}}console.log("%c ".concat(c.u2," %c@").concat(c.i8),"color:#FFF;background:#fa6400","","")})(),__nested_webpack_exports__})(),module.exports=e(__webpack_require__(594),__webpack_require__(206))},961:(e,t,n)=>{var r;self,r=(e,t)=>(()=>{var n={354:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(156),o=n.n(r),i=n(668),a=n.n(i),l=n(896),c=n(962),u=n(957);function s(e){var t=this,n=(0,c.Z)(),i=n[0],s=n[1],h=(0,r.useRef)(null),m=(0,r.useState)(!1),g=m[0],v=m[1],y=function(t){if(t)try{var n=JSON.parse(t);if(!n.content&&(n.scenes||n.global))return console.error("请勿导入页面产物（ToJSON）数据，应导入页面协议（Dump）数据",n),void i.error("请勿导入页面产物（ToJSON）数据，应导入页面协议（Dump）数据");e.project.loadContent(n),i.success("导入完成")}catch(e){i.error("导入失败，非法数据格式，请检查 ".concat(e)),console.error("非法数据格式, 请检查",e)}else i.error("导入失败或数据为空，请检查")},_=(0,r.useCallback)((function(e){v(e)}),[]);return o().createElement(o().Fragment,null,o().createElement("div",{className:l.Z.toolsContainer},o().createElement("div",{className:l.Z.toolsTitle},"调试工具"),o().createElement("div",{className:l.Z.toolsContent},o().createElement("div",{className:l.Z.toolsItem},o().createElement("div",{className:l.Z.toolsItemTitle},"页面协议（Dump）"),o().createElement("div",{className:l.Z.toolsItemContent},o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return e=t,r=function(){var e,t;return function(e,t){var n,r,o,i,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:l(0),throw:l(1),return:l(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function l(i){return function(l){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return a.label++,{value:i[1],done:!1};case 5:a.label++,r=i[1],i=[0];continue;case 7:i=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){a=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){a.label=i[1];break}if(6===i[0]&&a.label<o[1]){a.label=o[1],o=i;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(i);break}o[2]&&a.ops.pop(),a.trys.pop();continue}i=t.call(e,a)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,l])}}}(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,navigator.clipboard.readText()];case 1:return t=n.sent(),y(t),[3,3];case 2:return e=n.sent(),console.warn(e,"剪切板读取失败，尝试切换为手动导入..."),(t=window.prompt("将导出的页面数据复制到输入框"))&&y(t),[3,3];case 3:return[2]}}))},new((n=void 0)||(n=Promise))((function(t,o){function i(e){try{l(r.next(e))}catch(e){o(e)}}function a(e){try{l(r.throw(e))}catch(e){o(e)}}function l(e){var r;e.done?t(e.value):(r=e.value,r instanceof n?r:new n((function(e){e(r)}))).then(i,a)}l((r=r.apply(e,[])).next())}));var e,n,r}},"从剪切板中导入"),o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return t=e.project.dump(),d(JSON.stringify(t)),void i.success("已导出到剪贴板");var t}},"导出到剪切板"),o().createElement("div",null,o().createElement("input",{style:{display:"none"},ref:h,type:"file",accept:"application/json",onChange:function(e){var t=e.target.files;if(t){var n=new FileReader;n.readAsText(t[0]),n.onload=function(e){var t,n=null===(t=e.target)||void 0===t?void 0:t.result;y(n)}}}}),o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return function(){var e;null===(e=h.current)||void 0===e||e.click()}()}},"从文件中导入")),o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return t=e.project.dump(),void f({name:"mybricks_dump_".concat(p(),".json"),content:t});var t}},"导出到文件"))),o().createElement("div",null),o().createElement("div",{className:l.Z.toolsItem},o().createElement("div",{className:l.Z.toolsItemTitle},o().createElement("div",null,"页面产物（ToJSON）"),o().createElement("div",null,o().createElement(u.Z,{onChange:_}))),g&&o().createElement("div",{className:l.Z.toolsItemContent},o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return t=e.project.toJSON(),d(JSON.stringify(t)),void i.success("已导出到剪贴板");var t}},"导出到剪切板"),o().createElement("button",{className:a()(l.Z.toolsIBtn,l.Z.toolsIBtnBlock),onClick:function(){return t=e.project.toJSON(),void f({name:"mybricks_tojson_".concat(p(),".json"),content:t});var t}},"导出到文件"))))),s)}function p(){var e=new Date;return e.getFullYear()+"-"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"-"+e.getDate()+" "+e.getHours()+":"+e.getMinutes()+":"+e.getSeconds()}function d(e){var t=document.createElement("input");return document.body.appendChild(t),t.value=e,t.select(),document.execCommand("copy"),document.body.removeChild(t),!0}function f(e){var t=e.content,n=e.name,r=document.createElement("a");r.download=n,r.style.display="none";var o=new Blob([JSON.stringify(t)]);r.href=URL.createObjectURL(o),document.body.appendChild(r),r.click(),document.body.removeChild(r)}},957:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(156),o=n.n(r),i=n(436);function a(e){var t=e.defaultChecked,n=void 0!==t&&t,a=e.onChange,l=(0,r.useState)(n),c=l[0],u=l[1],s=(0,r.useCallback)((function(){u(!c),a&&a(!c)}),[c]);return o().createElement("div",{className:i.Z.ct},o().createElement("button",{onClick:s,className:"".concat(i.Z.switch," ").concat(c?i.Z.checked:"")},o().createElement("div",{className:i.Z.handle})))}},533:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var r=n(156),o=n.n(r),i=n(668),a=n.n(i),l=n(382),c=o().createElement("svg",{viewBox:"64 64 896 896",focusable:"false","data-icon":"check-circle",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},o().createElement("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"})),u=o().createElement("svg",{viewBox:"64 64 896 896",focusable:"false","data-icon":"close-circle",width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},o().createElement("path",{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"})),s={success:l.Z.messageSuccess,error:l.Z.messageError};function p(e){var t=e.type;return o().createElement("span",{className:a()(l.Z.icon,t&&s[t])},"success"===t?c:"error"===t?u:void 0)}},962:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});var r=n(156),o=n.n(r),i=n(533),a=n(111),l=n(758),c=function(){return c=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},c.apply(this,arguments)},u=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))},s=0,p=(0,r.forwardRef)((function(e,t){var n=(0,r.useState)([]),i=n[0],s=n[1];(0,r.useImperativeHandle)(t,(function(){return{open:function(e){var t=e.type,n=e.content;p({type:t,content:n})}}}));var p=function(e){var t=e.type,n=e.content,r={id:d(),type:t,content:n};s((function(e){return u(u([],e,!0),[r],!1)}))},h=function(e){s((function(t){return t.filter((function(t){return t.id!==e}))}))};return(0,a.createPortal)(o().createElement("div",{className:l.Z.toast},i.map((function(e){return o().createElement(f,c({},e,{onClose:h,key:e.id}))}))),document.body)})),d=function(){return"toast"+(new Date).getTime()+"-"+s++};function f(e){var t=e.id,n=e.type,a=e.content,c=e.onClose,u=(0,r.useRef)();return(0,r.useEffect)((function(){return u.current=setTimeout((function(){c&&c(t)}),2e3),function(){clearTimeout(u.current)}}),[]),o().createElement("div",{className:l.Z.toastItem},o().createElement("div",{className:l.Z.toastItemContent},o().createElement(i.Z,{type:n}),o().createElement("span",{className:l.Z.toastItemContentText},a)))}function h(){var e=o().useRef(),t=o().createElement(p,{ref:e});return[(0,r.useMemo)((function(){return{open:function(t){var n,r=t.type,o=t.content;null===(n=e.current)||void 0===n||n.open({type:r,content:o})},success:function(t){var n;null===(n=e.current)||void 0===n||n.open({type:"success",content:t})},error:function(t){var n;null===(n=e.current)||void 0===n||n.open({type:"error",content:t})}}}),[]),t]}},668:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&e.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&e.push(l)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},235:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".ct-54b6b {\n  display: flex;\n  align-items: center;\n}\n.ct-54b6b label {\n  width: 75px;\n  text-align: right;\n  padding-right: 5px;\n  font-size: 12px;\n}\n.ct-54b6b label i {\n  font-style: normal;\n  color: #FF0000;\n}\n.ct-54b6b .switch-c5081 {\n  display: inline-block;\n  box-sizing: border-box;\n  position: relative;\n  padding-left: 6px;\n  background-color: rgba(0, 0, 0, 0.25);\n  border: 0;\n  border-radius: 100px;\n  cursor: pointer;\n  min-width: 28px;\n  height: 16px;\n  line-height: 16px;\n  margin: 0;\n  padding: 0;\n  color: rgba(0, 0, 0, 0.85);\n  transition: all 0.2s;\n}\n.ct-54b6b .switch-c5081.checked-57dd0 {\n  background-color: #fa6400;\n}\n.ct-54b6b .switch-c5081.checked-57dd0 .handle-e7af8 {\n  left: initial;\n  right: 2px;\n}\n.ct-54b6b .switch-c5081 .handle-e7af8 {\n  width: 12px;\n  height: 12px;\n  position: absolute;\n  transition: all 0.2s ease-in-out;\n  top: 2px;\n  left: 2px;\n}\n.ct-54b6b .switch-c5081 .handle-e7af8::before {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  background-color: #fff;\n  border-radius: 9px;\n  box-shadow: 0 2px 4px 0 rgba(0, 35, 11, 0.2);\n  content: '';\n}\n",""]),o.locals={ct:"ct-54b6b",switch:"switch-c5081",checked:"checked-57dd0",handle:"handle-e7af8"};const i=o},408:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".messageSuccess-37bc4 {\n  color: #52c41a;\n}\n.messageError-09e47 {\n  color: #ff4d4f;\n}\n.icon-aa344 {\n  display: inline-block;\n  vertical-align: text-bottom;\n  margin-inline-end: 8px;\n  font-size: 16px;\n  line-height: 0;\n  text-align: center;\n}\n",""]),o.locals={messageSuccess:"messageSuccess-37bc4",messageError:"messageError-09e47",icon:"icon-aa344"};const i=o},745:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,".toast-8db24 {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n  color: rgba(0, 0, 0, 0.88);\n  font-size: 14px;\n  line-height: 1.57142857;\n  list-style: none;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';\n  position: fixed;\n  top: 8px;\n  width: 100%;\n  pointer-events: none;\n  z-index: 1010;\n  left: 50%;\n  transform: translateX(-50%);\n}\n.toastItem-a3919 {\n  padding: 8px;\n  text-align: center;\n  transition: all 0.3;\n}\n.toastItemContent-7cfaa {\n  display: inline-block;\n  padding: 9px 12px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);\n  pointer-events: all;\n}\n.toastItemContentText-831e4 {\n  margin-left: 6px;\n}\n",""]),o.locals={toast:"toast-8db24",toastItem:"toastItem-a3919",toastItemContent:"toastItemContent-7cfaa",toastItemContentText:"toastItemContentText-831e4"};const i=o},752:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(645),o=n.n(r)()((function(e){return e[1]}));o.push([e.id,'.toolsContainer-9672f {\n  width: 320px;\n  height: 100%;\n}\n.toolsTitle-333ba {\n  height: 50px;\n  padding: 0 10px;\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n  font-weight: 600;\n  border-bottom: 1px solid #eee;\n}\n.toolsContent-b0262 {\n  padding: 12px 10px;\n  overflow-y: auto;\n  height: calc(100% - 50px);\n}\n.toolsItem-38e9b .toolsItemTitle-cb988 {\n  position: relative;\n  font-weight: 600;\n  font-size: 12px;\n  margin-bottom: 8px;\n  padding-left: 6px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.toolsItem-38e9b .toolsItemTitle-cb988:before {\n  position: absolute;\n  content: "";\n  top: 3px;\n  left: 0;\n  border-left: 3px solid #FA6400;\n  height: 12px;\n}\n.toolsIBtn-a4dc1 {\n  border: 1px solid #DDD;\n  border-radius: 3px;\n  padding: 4px 12px;\n  cursor: pointer;\n  background-color: #FFF;\n  font-size: 12px;\n  box-shadow: 0px 1px 3px 0px #ddd;\n}\n.toolsIBtn-a4dc1 + .toolsIBtn-a4dc1 {\n  margin-left: 4px;\n}\n.toolsIBtn-a4dc1.toolsIBtnBlock-c8787 {\n  width: 100%;\n  padding: 4px 0;\n  margin-bottom: 12px;\n  margin-left: 0;\n}\n',""]),o.locals={toolsContainer:"toolsContainer-9672f",toolsTitle:"toolsTitle-333ba",toolsContent:"toolsContent-b0262",toolsItem:"toolsItem-38e9b",toolsItemTitle:"toolsItemTitle-cb988",toolsIBtn:"toolsIBtn-a4dc1",toolsIBtnBlock:"toolsIBtnBlock-c8787"};const i=o},645:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var o={};if(r)for(var i=0;i<this.length;i++){var a=this[i][0];null!=a&&(o[a]=!0)}for(var l=0;l<e.length;l++){var c=[].concat(e[l]);r&&o[c[0]]||(n&&(c[2]?c[2]="".concat(n," and ").concat(c[2]):c[2]=n),t.push(c))}},t}},436:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(379),o=n.n(r),i=n(235);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},382:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(379),o=n.n(r),i=n(408);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},758:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(379),o=n.n(r),i=n(745);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},896:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(379),o=n.n(r),i=n(752);o()(i.Z,{insert:"head",singleton:!1});const a=i.Z.locals||{}},379:(e,t,n)=>{"use strict";var r,o=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),i=[];function a(e){for(var t=-1,n=0;n<i.length;n++)if(i[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},r=[],o=0;o<e.length;o++){var l=e[o],c=t.base?l[0]+t.base:l[0],u=n[c]||0,s="".concat(c," ").concat(u);n[c]=u+1;var p=a(s),d={css:l[1],media:l[2],sourceMap:l[3]};-1!==p?(i[p].references++,i[p].updater(d)):i.push({identifier:s,updater:m(d,t),references:1}),r.push(s)}return r}function c(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var i=n.nc;i&&(r.nonce=i)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var a=o(e.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(t)}return t}var u,s=(u=[],function(e,t){return u[e]=t,u.filter(Boolean).join("\n")});function p(e,t,n,r){var o=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=s(t,o);else{var i=document.createTextNode(o),a=e.childNodes;a[t]&&e.removeChild(a[t]),a.length?e.insertBefore(i,a[t]):e.appendChild(i)}}function d(e,t,n){var r=n.css,o=n.media,i=n.sourceMap;if(o?e.setAttribute("media",o):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var f=null,h=0;function m(e,t){var n,r,o;if(t.singleton){var i=h++;n=f||(f=c(t)),r=p.bind(null,n,i,!1),o=p.bind(null,n,i,!0)}else n=c(t),r=d.bind(null,n,t),o=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else o()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=(void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r));var n=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var o=a(n[r]);i[o].references--}for(var c=l(e,t),u=0;u<n.length;u++){var s=a(n[u]);0===i[s].references&&(i[s].updater(),i.splice(s,1))}n=c}}}},156:t=>{"use strict";t.exports=e},111:e=>{"use strict";e.exports=t},147:e=>{"use strict";e.exports=JSON.parse('{"u2":"@mybricks/plugin-tools","i8":"1.0.17"}')}},r={};function o(e){var t=r[e];if(void 0!==t)return t.exports;var i=r[e]={id:e,exports:{}};return n[e](i,i.exports,o),i.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.nc=void 0;var i={};return(()=>{"use strict";o.r(i),o.d(i,{default:()=>c});var e=o(156),t=o.n(e),n=o(354),r=o(147),a=function(){return a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)};console.log("%c ".concat(r.u2," %c@").concat(r.i8),"color:#FFF;background:#fa6400","","");var l=t().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"2385",width:"32",height:"32"},t().createElement("path",{d:"M847.40096 805.88416c8.92544 8.92544 8.92544 23.00032-0.2752 32.50048a22.95552 22.95552 0 0 1-31.97696-0.54912L674.84288 697.78304a22.72512 22.72512 0 0 1 0.5248-32.22528c8.704-8.65024 23.296-8.65024 32.52608 0l139.5072 140.3264zM785.34784 15.4624a188.09216 188.09216 0 0 1 44.17664 4.62976 195.55456 195.55456 0 0 1 43.07712 13.00096 38.23744 38.23744 0 0 1 19.776 50.10176 40.4096 40.4096 0 0 1-7.87456 11.92576c-28.97664 28.97536-57.95328 57.6768-86.70464 86.67776l4.89984 17.3248 4.32512 17.6256 17.87648 4.84992 17.3504 4.60032c28.70144-28.97536 57.7024-57.42592 86.67904-86.67776 14.62656-14.90048 39.0016-14.90048 54.17856 0a38.84544 38.84544 0 0 1 9.19936 16.256 197.32992 197.32992 0 0 1 11.648 38.47552v0.5504h0.2752a229.60768 229.60768 0 0 1 4.32512 44.40192 223.29728 223.29728 0 0 1-223.20768 222.92096c-4.32512 0-8.65024-0.2752-13.00096-0.2752l-42.25152 41.728 232.41088 232.40576 1.89952 2.1504a156.99456 156.99456 0 0 1-1.89952 220.23168l-0.5504 0.5248a156.69632 156.69632 0 0 1-110.50496 45.52064c-40.0768 0-80.9792-15.1744-111.05536-46.05056L507.45984 725.68448 269.87392 963.79136c-49.55264 49.55136-109.43104 53.9264-158.464 32.77568a155.91936 155.91936 0 0 1-50.09408-33.8752h-0.27648a158.7584 158.7584 0 0 1-33.57696-49.82656c-21.15072-49.024-16.80128-109.15328 33.024-158.48064v-0.5248L325.4016 488.95616l-96.95488-96.97792-86.12864-25.728a37.59744 37.59744 0 0 1-24.37632-21.4016L35.28704 151.16416a38.048 38.048 0 0 1 6.77504-44.40064l34.67776-34.39872 34.15168-34.1504a37.67552 37.67552 0 0 1 41.70112-8.12544l196.11008 83.72736a37.43616 37.43616 0 0 1 21.67552 24.37632l25.45152 86.12736 96.97408 96.95616 69.632-69.05216c-0.2752-4.89984-0.5248-9.50016-0.5248-13.02528h0.2496a222.2208 222.2208 0 0 1 65.28-158.17984v-0.27904a224.13952 224.13952 0 0 1 157.9072-65.28zM675.9424 557.7536l-114.3296 114.3296 232.4352 232.40704a80.75904 80.75904 0 0 0 114.304 0.2496l0.5248-0.2496c15.72608-15.17568 23.0272-36.608 23.0272-57.1776a79.96416 79.96416 0 0 0-21.952-55.5264l-1.6-1.62432-232.4096-232.40832z m-296.88832-122.45248l59.8528-59.5776-103.72992-104.00256a38.33984 38.33984 0 0 1-10.30144-18.976l-22.49984-76.12672L146.368 110.816 130.39232 126.5408l-15.97568 16.256 66.32832 156.032 78.5536 23.00032a33.73056 33.73056 0 0 1 16.2752 10.02624l103.48032 103.45344z m400.896-343.43552a145.92 145.92 0 0 0-98.60224 43.32544 145.408 145.408 0 0 0-43.32672 104.00384h0.5248v10.57536l1.0752 8.92544c2.72512 11.92448-1.0752 24.94976-10.27584 34.432L114.41664 807.76192h-0.2752c-23.82592 24.65024-26.55104 52.55168-16.80128 75.5776a93.87136 93.87136 0 0 0 17.60128 25.472 87.296 87.296 0 0 0 26.00064 17.6c23.02592 10.02496 50.65216 7.296 75.05408-16.5248l263.83744-263.8336 1.0752-1.0752 167.936-168.2048 1.0752-1.37472 80.7296-80.17792h0.2496a36.23808 36.23808 0 0 1 32.256-11.10016 79.7696 79.7696 0 0 0 11.40096 1.09952c2.6752 0.2752 6.47552 0.5248 10.80064 0.5248a148.64128 148.64128 0 0 0 104.02944-42.52544v-0.5248a145.73568 145.73568 0 0 0 42.51904-98.60224c-16.52608 16.77568-33.05216 33.024-49.57824 50.10176-9.45024 10.54976-23.82592 14.90048-38.4512 11.62496l-38.4768-10.52544-38.97728-10.57536c-13.02528-3.52512-23.57632-12.99968-27.10144-27.10016l-10.30016-38.99776-10.57536-39.0016c-2.944-11.92448-0.2752-26.55104 10.02496-36.0256 17.07648-17.32608 34.15168-34.40128 51.47776-51.72736z","p-id":"2386",fill:"#555555"}));function c(e){return{name:"@mybricks/plugins/tools",title:"调试工具",description:"调试工具",data:{},contributes:{sliderView:{tab:{title:"调试工具",icon:l,apiSet:["project"],render:function(r){return t().createElement(n.Z,a({},r,e))}}}}}}})(),i})(),e.exports=r(n(594),n(206))},717:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(601),o=n.n(r),i=n(314),a=n.n(i)()(o());a.push([e.id,".container-ee0ba {\n  font-size: 12px;\n  padding: 3px 5px;\n}\n.container-ee0ba .item-e9e5f .user-b01f9 {\n  display: flex;\n  height: 24px;\n  align-items: center;\n  flex-wrap: nowrap;\n  justify-content: space-between;\n  margin-bottom: 4px;\n  color: #95999e;\n}\n.container-ee0ba .item-e9e5f .user-b01f9 .userinfo-f873a {\n  display: inline-flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  flex-shrink: 1;\n  height: 24px;\n  line-height: 20px;\n}\n.container-ee0ba .item-e9e5f .user-b01f9 .userinfo-f873a .useravatar-ea630 {\n  margin-right: 4px;\n}\n.container-ee0ba .item-e9e5f .user-b01f9 .userinfo-f873a .useravatar-ea630 img {\n  width: 24px;\n  height: 24px;\n  line-height: 24px;\n  border-radius: 50%;\n}\n.container-ee0ba .item-e9e5f .footer-cb2ae {\n  margin-top: 4px;\n}\n.container-ee0ba .item-e9e5f .footer-cb2ae button {\n  cursor: pointer;\n  padding: 2px 5px;\n  margin-right: 5px;\n  background: #FFF;\n  border: 1px solid #DDD;\n  border-radius: 3px;\n}\n.container-ee0ba .item-e9e5f .footer-cb2ae button[data-type='danger'] {\n  color: #F55753;\n  border: 1px solid #F55753;\n  margin-right: 0px;\n  display: none;\n}\n.container-ee0ba .item-e9e5f .footer-cb2ae .default-b2fb9 {\n  display: flex;\n  justify-content: space-between;\n}\n.container-ee0ba .item-e9e5f .footer-cb2ae .default-b2fb9:hover button[data-type='danger'] {\n  display: block;\n}\n.container-ee0ba .item-e9e5f .buttonSet-e412b {\n  margin-top: 8px;\n  display: flex;\n  justify-content: flex-end;\n}\n.container-ee0ba .item-e9e5f .buttonSet-e412b button {\n  cursor: pointer;\n  min-width: 48px;\n  box-sizing: border-box;\n  margin-left: 8px;\n  border-radius: 4px;\n  height: 24px;\n  padding: 0 7px;\n  font-size: 12px;\n  line-height: 24px;\n  color: #1f2329;\n  background-color: #ffffff;\n  border: 1px solid transparent;\n  border-color: #e1e3e6;\n}\n.container-ee0ba .item-e9e5f .buttonSet-e412b button[data-btn-type='submit'] {\n  color: #ffffff;\n  background: #0c63fa;\n  border-color: #0c63fa;\n}\n.container-ee0ba .item-e9e5f .buttonSet-e412b button[disabled] {\n  cursor: not-allowed;\n  color: #00000040;\n  border-color: #d9d9d9;\n  background: #f5f5f5;\n}\n.container-ee0ba .item-e9e5f .fotmatTime-c678a {\n  display: flex;\n}\n.container-ee0ba .item-e9e5f .menu-dee0d {\n  position: relative;\n  font-size: 16px;\n  display: none;\n}\n.container-ee0ba .item-e9e5f .menu-dee0d .anticon-more {\n  transform: rotate(90deg);\n}\n.container-ee0ba .item-e9e5f .menu-dee0d .action-c99c8 {\n  right: 4px;\n  top: 24px;\n  z-index: 3001;\n  background: white;\n  border: #e0e0e0 solid 1px;\n  font-size: 12px;\n  margin: 0;\n  transition: opacity 0.4s;\n  box-shadow: 0 2px 4px #00000033;\n  outline: none;\n  padding: 2px 0;\n  position: absolute;\n  min-width: 60px;\n  text-align: center;\n  border-radius: 5px;\n  display: none;\n  color: rgba(0, 0, 0, 0.85);\n}\n.container-ee0ba .item-e9e5f .menu-dee0d .action-c99c8 div {\n  cursor: pointer;\n  height: 24px;\n  line-height: 24px;\n}\n.container-ee0ba .item-e9e5f .menu-dee0d .action-c99c8 div:hover {\n  background-color: #ececec;\n}\n.container-ee0ba .item-e9e5f .menu-dee0d div[data-note-icon] {\n  cursor: pointer;\n  display: inline-flex;\n  width: 24px;\n  height: 24px;\n  border-radius: 4px;\n  justify-content: center;\n  align-items: center;\n  color: rgba(0, 0, 0, 0.85);\n}\n.container-ee0ba .item-e9e5f .menu-dee0d div[data-note-icon]:hover {\n  background-color: rgba(61, 92, 128, 0.05);\n}\n.container-ee0ba .item-e9e5f .menu-dee0d div[data-note-icon='MoreOutlined']:hover .action-c99c8 {\n  display: block;\n}\n.container-ee0ba .editorTrigger-f43ba {\n  display: block;\n  height: 16px;\n  color: #95999e;\n  margin-top: 4px;\n  line-height: 16px;\n  cursor: pointer;\n  opacity: 0;\n}\n.container-ee0ba:hover .editorTrigger-f43ba {\n  opacity: 1;\n}\n.container-ee0ba[data-type-show-menu='false'] .item-e9e5f:hover .fotmatTime-c678a {\n  display: none;\n}\n.container-ee0ba[data-type-show-menu='false'] .item-e9e5f:hover .menu-dee0d {\n  display: flex;\n}\n.container-ee0ba[data-type-show-menu='true'] .editorTrigger-f43ba {\n  display: none;\n}\n",""]),a.locals={container:"container-ee0ba",item:"item-e9e5f",user:"user-b01f9",userinfo:"userinfo-f873a",useravatar:"useravatar-ea630",footer:"footer-cb2ae",default:"default-b2fb9",buttonSet:"buttonSet-e412b",fotmatTime:"fotmatTime-c678a",menu:"menu-dee0d",action:"action-c99c8",editorTrigger:"editorTrigger-f43ba"};const l=a},686:(e,t,n)=>{"use strict";n.d(t,{A:()=>l});var r=n(601),o=n.n(r),i=n(314),a=n.n(i)()(o());a.push([e.id,".main-b0e99 {\n  width: 100%;\n  height: 100%;\n  font-size: 12px;\n  display: flex;\n  flex-direction: column;\n}\n.main-b0e99 .toolbar-c2beb {\n  height: 40px;\n  padding: 5px 10px;\n  background-color: #F7F7F7;\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid #CCC;\n}\n.main-b0e99 .toolbar-c2beb .title-b6329 {\n  font-weight: bold;\n  margin-right: auto;\n  font-size: 14px;\n  margin-left: 10px;\n  font-style: italic;\n  color: #999;\n}\n.main-b0e99 .toolbar-c2beb .save-b869b {\n  border-radius: 3px;\n  padding: 3px 12px;\n  margin: 0 2px;\n  cursor: pointer;\n  background-color: #FA6400;\n  color: #FFF;\n  font-weight: bold;\n  border: 0;\n}\n",""]),a.locals={main:"main-b0e99",toolbar:"toolbar-c2beb",title:"title-b6329",save:"save-b869b"};const l=a},314:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,r,o,i){"string"==typeof e&&(e=[[null,e,void 0]]);var a={};if(r)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(a[c]=!0)}for(var u=0;u<e.length;u++){var s=[].concat(e[u]);r&&a[s[0]]||(void 0!==i&&(void 0===s[5]||(s[1]="@layer".concat(s[5].length>0?" ".concat(s[5]):""," {").concat(s[1],"}")),s[5]=i),n&&(s[2]?(s[1]="@media ".concat(s[2]," {").concat(s[1],"}"),s[2]=n):s[2]=n),o&&(s[4]?(s[1]="@supports (".concat(s[4],") {").concat(s[1],"}"),s[4]=o):s[4]="".concat(o)),t.push(s))}},t}},601:e=>{"use strict";e.exports=function(e){return e[1]}},353:function(e){e.exports=function(){"use strict";var e=6e4,t=36e5,n="millisecond",r="second",o="minute",i="hour",a="day",l="week",c="month",u="quarter",s="year",p="date",d="Invalid Date",f=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,h=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,m={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],n=e%100;return"["+e+(t[(n-20)%10]||t[n]||t[0])+"]"}},g=function(e,t,n){var r=String(e);return!r||r.length>=t?e:""+Array(t+1-r.length).join(n)+e},v={s:g,z:function(e){var t=-e.utcOffset(),n=Math.abs(t),r=Math.floor(n/60),o=n%60;return(t<=0?"+":"-")+g(r,2,"0")+":"+g(o,2,"0")},m:function e(t,n){if(t.date()<n.date())return-e(n,t);var r=12*(n.year()-t.year())+(n.month()-t.month()),o=t.clone().add(r,c),i=n-o<0,a=t.clone().add(r+(i?-1:1),c);return+(-(r+(n-o)/(i?o-a:a-o))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return{M:c,y:s,w:l,d:a,D:p,h:i,m:o,s:r,ms:n,Q:u}[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}},y="en",_={};_[y]=m;var b="$isDayjsObject",x=function(e){return e instanceof S||!(!e||!e[b])},w=function e(t,n,r){var o;if(!t)return y;if("string"==typeof t){var i=t.toLowerCase();_[i]&&(o=i),n&&(_[i]=n,o=i);var a=t.split("-");if(!o&&a.length>1)return e(a[0])}else{var l=t.name;_[l]=t,o=l}return!r&&o&&(y=o),o||!r&&y},E=function(e,t){if(x(e))return e.clone();var n="object"==typeof t?t:{};return n.date=e,n.args=arguments,new S(n)},O=v;O.l=w,O.i=x,O.w=function(e,t){return E(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var S=function(){function m(e){this.$L=w(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[b]=!0}var g=m.prototype;return g.parse=function(e){this.$d=function(e){var t=e.date,n=e.utc;if(null===t)return new Date(NaN);if(O.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var r=t.match(f);if(r){var o=r[2]-1||0,i=(r[7]||"0").substring(0,3);return n?new Date(Date.UTC(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)):new Date(r[1],o,r[3]||1,r[4]||0,r[5]||0,r[6]||0,i)}}return new Date(t)}(e),this.init()},g.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},g.$utils=function(){return O},g.isValid=function(){return!(this.$d.toString()===d)},g.isSame=function(e,t){var n=E(e);return this.startOf(t)<=n&&n<=this.endOf(t)},g.isAfter=function(e,t){return E(e)<this.startOf(t)},g.isBefore=function(e,t){return this.endOf(t)<E(e)},g.$g=function(e,t,n){return O.u(e)?this[t]:this.set(n,e)},g.unix=function(){return Math.floor(this.valueOf()/1e3)},g.valueOf=function(){return this.$d.getTime()},g.startOf=function(e,t){var n=this,u=!!O.u(t)||t,d=O.p(e),f=function(e,t){var r=O.w(n.$u?Date.UTC(n.$y,t,e):new Date(n.$y,t,e),n);return u?r:r.endOf(a)},h=function(e,t){return O.w(n.toDate()[e].apply(n.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),n)},m=this.$W,g=this.$M,v=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case s:return u?f(1,0):f(31,11);case c:return u?f(1,g):f(0,g+1);case l:var _=this.$locale().weekStart||0,b=(m<_?m+7:m)-_;return f(u?v-b:v+(6-b),g);case a:case p:return h(y+"Hours",0);case i:return h(y+"Minutes",1);case o:return h(y+"Seconds",2);case r:return h(y+"Milliseconds",3);default:return this.clone()}},g.endOf=function(e){return this.startOf(e,!1)},g.$set=function(e,t){var l,u=O.p(e),d="set"+(this.$u?"UTC":""),f=(l={},l[a]=d+"Date",l[p]=d+"Date",l[c]=d+"Month",l[s]=d+"FullYear",l[i]=d+"Hours",l[o]=d+"Minutes",l[r]=d+"Seconds",l[n]=d+"Milliseconds",l)[u],h=u===a?this.$D+(t-this.$W):t;if(u===c||u===s){var m=this.clone().set(p,1);m.$d[f](h),m.init(),this.$d=m.set(p,Math.min(this.$D,m.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},g.set=function(e,t){return this.clone().$set(e,t)},g.get=function(e){return this[O.p(e)]()},g.add=function(n,u){var p,d=this;n=Number(n);var f=O.p(u),h=function(e){var t=E(d);return O.w(t.date(t.date()+Math.round(e*n)),d)};if(f===c)return this.set(c,this.$M+n);if(f===s)return this.set(s,this.$y+n);if(f===a)return h(1);if(f===l)return h(7);var m=(p={},p[o]=e,p[i]=t,p[r]=1e3,p)[f]||1,g=this.$d.getTime()+n*m;return O.w(g,this)},g.subtract=function(e,t){return this.add(-1*e,t)},g.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return n.invalidDate||d;var r=e||"YYYY-MM-DDTHH:mm:ssZ",o=O.z(this),i=this.$H,a=this.$m,l=this.$M,c=n.weekdays,u=n.months,s=n.meridiem,p=function(e,n,o,i){return e&&(e[n]||e(t,r))||o[n].slice(0,i)},f=function(e){return O.s(i%12||12,e,"0")},m=s||function(e,t,n){var r=e<12?"AM":"PM";return n?r.toLowerCase():r};return r.replace(h,(function(e,r){return r||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return O.s(t.$y,4,"0");case"M":return l+1;case"MM":return O.s(l+1,2,"0");case"MMM":return p(n.monthsShort,l,u,3);case"MMMM":return p(u,l);case"D":return t.$D;case"DD":return O.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(n.weekdaysMin,t.$W,c,2);case"ddd":return p(n.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(i);case"HH":return O.s(i,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return m(i,a,!0);case"A":return m(i,a,!1);case"m":return String(a);case"mm":return O.s(a,2,"0");case"s":return String(t.$s);case"ss":return O.s(t.$s,2,"0");case"SSS":return O.s(t.$ms,3,"0");case"Z":return o}return null}(e)||o.replace(":","")}))},g.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},g.diff=function(n,p,d){var f,h=this,m=O.p(p),g=E(n),v=(g.utcOffset()-this.utcOffset())*e,y=this-g,_=function(){return O.m(h,g)};switch(m){case s:f=_()/12;break;case c:f=_();break;case u:f=_()/3;break;case l:f=(y-v)/6048e5;break;case a:f=(y-v)/864e5;break;case i:f=y/t;break;case o:f=y/e;break;case r:f=y/1e3;break;default:f=y}return d?f:O.a(f)},g.daysInMonth=function(){return this.endOf(c).$D},g.$locale=function(){return _[this.$L]},g.locale=function(e,t){if(!e)return this.$L;var n=this.clone(),r=w(e,t,!0);return r&&(n.$L=r),n},g.clone=function(){return O.w(this.$d,this)},g.toDate=function(){return new Date(this.valueOf())},g.toJSON=function(){return this.isValid()?this.toISOString():null},g.toISOString=function(){return this.$d.toISOString()},g.toString=function(){return this.$d.toUTCString()},m}(),k=S.prototype;return E.prototype=k,[["$ms",n],["$s",r],["$m",o],["$H",i],["$W",a],["$M",c],["$y",s],["$D",p]].forEach((function(e){k[e[1]]=function(t){return this.$g(t,e[0],e[1])}})),E.extend=function(e,t){return e.$i||(e(t,S,E),e.$i=!0),E},E.locale=w,E.isDayjs=x,E.unix=function(e){return E(1e3*e)},E.en=_[y],E.Ls=_,E.p={},E}()},873:(e,t,n)=>{var r=n(325).Symbol;e.exports=r},932:e=>{e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,o=Array(r);++n<r;)o[n]=t(e[n],n,e);return o}},882:e=>{e.exports=function(e,t,n,r){var o=-1,i=null==e?0:e.length;for(r&&i&&(n=e[++o]);++o<i;)n=t(n,e[o],o,e);return n}},74:e=>{e.exports=function(e){return e.split("")}},733:e=>{var t=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;e.exports=function(e){return e.match(t)||[]}},552:(e,t,n)=>{var r=n(873),o=n(40),i=n(350),a=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},171:e=>{e.exports=function(e){return function(t){return null==e?void 0:e[t]}}},160:e=>{e.exports=function(e,t,n){var r=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(n=n>o?o:n)<0&&(n+=o),o=t>n?0:n-t>>>0,t>>>=0;for(var i=Array(o);++r<o;)i[r]=e[r+t];return i}},556:(e,t,n)=>{var r=n(873),o=n(932),i=n(449),a=n(394),l=r?r.prototype:void 0,c=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return o(t,e)+"";if(a(t))return c?c.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}},754:(e,t,n)=>{var r=n(160);e.exports=function(e,t,n){var o=e.length;return n=void 0===n?o:n,!t&&n>=o?e:r(e,t,n)}},507:(e,t,n)=>{var r=n(754),o=n(698),i=n(912),a=n(222);e.exports=function(e){return function(t){t=a(t);var n=o(t)?i(t):void 0,l=n?n[0]:t.charAt(0),c=n?r(n,1).join(""):t.slice(1);return l[e]()+c}}},539:(e,t,n)=>{var r=n(882),o=n(828),i=n(645),a=RegExp("['’]","g");e.exports=function(e){return function(t){return r(i(o(t).replace(a,"")),e,"")}}},647:(e,t,n)=>{var r=n(171)({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});e.exports=r},840:(e,t,n)=>{var r="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g;e.exports=r},40:(e,t,n)=>{var r=n(873),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,l=r?r.toStringTag:void 0;e.exports=function(e){var t=i.call(e,l),n=e[l];try{e[l]=void 0;var r=!0}catch(e){}var o=a.call(e);return r&&(t?e[l]=n:delete e[l]),o}},698:e=>{var t=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},434:e=>{var t=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;e.exports=function(e){return t.test(e)}},350:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},325:(e,t,n)=>{var r=n(840),o="object"==typeof self&&self&&self.Object===Object&&self,i=r||o||Function("return this")();e.exports=i},912:(e,t,n)=>{var r=n(74),o=n(698),i=n(54);e.exports=function(e){return o(e)?i(e):r(e)}},54:e=>{var t="\\ud800-\\udfff",n="["+t+"]",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",o="\\ud83c[\\udffb-\\udfff]",i="[^"+t+"]",a="(?:\\ud83c[\\udde6-\\uddff]){2}",l="[\\ud800-\\udbff][\\udc00-\\udfff]",c="(?:"+r+"|"+o+")?",u="[\\ufe0e\\ufe0f]?",s=u+c+"(?:\\u200d(?:"+[i,a,l].join("|")+")"+u+c+")*",p="(?:"+[i+r+"?",r,a,l,n].join("|")+")",d=RegExp(o+"(?="+o+")|"+p+s,"g");e.exports=function(e){return e.match(d)||[]}},225:e=>{var t="\\ud800-\\udfff",n="\\u2700-\\u27bf",r="a-z\\xdf-\\xf6\\xf8-\\xff",o="A-Z\\xc0-\\xd6\\xd8-\\xde",i="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",a="["+i+"]",l="\\d+",c="["+n+"]",u="["+r+"]",s="[^"+t+i+l+n+r+o+"]",p="(?:\\ud83c[\\udde6-\\uddff]){2}",d="[\\ud800-\\udbff][\\udc00-\\udfff]",f="["+o+"]",h="(?:"+u+"|"+s+")",m="(?:"+f+"|"+s+")",g="(?:['’](?:d|ll|m|re|s|t|ve))?",v="(?:['’](?:D|LL|M|RE|S|T|VE))?",y="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\\ud83c[\\udffb-\\udfff])?",_="[\\ufe0e\\ufe0f]?",b=_+y+"(?:\\u200d(?:"+["[^"+t+"]",p,d].join("|")+")"+_+y+")*",x="(?:"+[c,p,d].join("|")+")"+b,w=RegExp([f+"?"+u+"+"+g+"(?="+[a,f,"$"].join("|")+")",m+"+"+v+"(?="+[a,f+h,"$"].join("|")+")",f+"?"+h+"+"+g,f+"+"+v,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",l,x].join("|"),"g");e.exports=function(e){return e.match(w)||[]}},58:(e,t,n)=>{var r=n(792),o=n(539)((function(e,t,n){return t=t.toLowerCase(),e+(n?r(t):t)}));e.exports=o},792:(e,t,n)=>{var r=n(222),o=n(808);e.exports=function(e){return o(r(e).toLowerCase())}},828:(e,t,n)=>{var r=n(647),o=n(222),i=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,a=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");e.exports=function(e){return(e=o(e))&&e.replace(i,r).replace(a,"")}},449:e=>{var t=Array.isArray;e.exports=t},346:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},394:(e,t,n)=>{var r=n(552),o=n(346);e.exports=function(e){return"symbol"==typeof e||o(e)&&"[object Symbol]"==r(e)}},222:(e,t,n)=>{var r=n(556);e.exports=function(e){return null==e?"":r(e)}},808:(e,t,n)=>{var r=n(507)("toUpperCase");e.exports=r},645:(e,t,n)=>{var r=n(733),o=n(434),i=n(222),a=n(225);e.exports=function(e,t,n){return e=i(e),void 0===(t=n?void 0:t)?o(e)?a(e):r(e):e.match(t)||[]}},72:e=>{"use strict";var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var i={},a=[],l=0;l<e.length;l++){var c=e[l],u=r.base?c[0]+r.base:c[0],s=i[u]||0,p="".concat(u," ").concat(s);i[u]=s+1;var d=n(p),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==d)t[d].references++,t[d].updater(f);else{var h=o(f,r);r.byIndex=l,t.splice(l,0,{identifier:p,updater:h,references:1})}a.push(p)}return a}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var i=r(e=e||[],o=o||{});return function(e){e=e||[];for(var a=0;a<i.length;a++){var l=n(i[a]);t[l].references--}for(var c=r(e,o),u=0;u<i.length;u++){var s=n(i[u]);0===t[s].references&&(t[s].updater(),t.splice(s,1))}i=c}}},659:e=>{"use strict";var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},540:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},868:e=>{"use strict";e.exports=function(e,t){Object.keys(t).forEach((function(n){e.setAttribute(n,t[n])}))}},825:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},113:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},594:e=>{"use strict";e.exports=React},206:e=>{"use strict";e.exports=ReactDOM},942:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e="",t=0;t<arguments.length;t++){var n=arguments[t];n&&(e=a(e,i(n)))}return e}function i(e){if("string"==typeof e||"number"==typeof e)return e;if("object"!=typeof e)return"";if(Array.isArray(e))return o.apply(null,e);if(e.toString!==Object.prototype.toString&&!e.toString.toString().includes("[native code]"))return e.toString();var t="";for(var n in e)r.call(e,n)&&e[n]&&(t=a(t,n));return t}function a(e,t){return t?e?e+" "+t:e+t:e}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={id:e,exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";var e={};__webpack_require__.r(e),__webpack_require__.d(e,{hasBrowserEnv:()=>In,hasStandardBrowserEnv:()=>Fn,hasStandardBrowserWebWorkerEnv:()=>Bn});var t=__webpack_require__(594),n=__webpack_require__.n(t),r=__webpack_require__(206);const o={notesMap:{},operationMap:{},id:""};var i=__webpack_require__(353),a=__webpack_require__.n(i);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}var c=Object.prototype.toString,u={array:"[object Array]",object:"[object Object]",undefined:"[object Undefined]"};function s(e,t){return c.call(e)===u[t.toLocaleLowerCase()]}function p(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(null===e||"object"!==l(e))return e;var n=t.filter((function(t){return t.original===e}))[0];if(n)return n.copy;var r=Array.isArray(e)?[]:{};return t.push({original:e,copy:r}),Object.keys(e).forEach((function(n){r[n]=p(e[n],t)})),r}function d(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"u_",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n="",r=0;r<t;r++)n+="abcdefhijkmnprstwxyz0123456789".charAt(Math.floor(30*Math.random()));return e+n}function f(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.document,t=e.getElementsByTagName("head")[0],n=e.createElement("style");return t.appendChild(n),n}function h(e,t,n){n&&t&&n.sheet.insertRule(e+"{"+t+"}",0)}function m(e,t){if(t){for(var n=function(e){var t,n;return(null==e||null===(t=e.sheet)||void 0===t?void 0:t.rules)||(null==e||null===(n=e.sheet)||void 0===n?void 0:n.cssRules)||[]}(t),r=[],o=0;o<n.length;o++){var i=n[o];(-1!==i.selectorText.indexOf(e)||i.selectorText.startsWith(e))&&r.push(o)}for(var a=r.length-1;a>=0;a--)t.sheet.deleteRule(r[a])}}function g(e,t,n){var r;return function(){var o=this,i=arguments,a=n&&!r;clearTimeout(r),r=setTimeout((function(){r=null,n||e.apply(o,i)}),t),a&&e.apply(o,i)}}function v(e){return v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},v(e)}function y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,w(r.key),r)}}function b(e,t,n){return t&&_(e.prototype,t),n&&_(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function x(e,t,n){return(t=w(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){var t=function(e,t){if("object"!=v(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=v(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==v(t)?t:t+""}var E=null,O=function(){return b((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window.document.body,o=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;y(this,e),x(this,"styleSheet",null),x(this,"input",null),x(this,"inputShadow",null),this.ele=t,this.document=n,this.positionDom=r,this.options=o,this.event=i,this.init()}),[{key:"init",value:function(){this.initSharedStyleSheet(),this.initDom()}},{key:"initSharedStyleSheet",value:function(){E||((E=f(this.document)).innerHTML="",f(this.document).innerHTML="\n  .llh-inputcontainer {\n    display: flex;\n    border-radius: 4px;\n  }\n  .llh-input {\n    background: #FFF;\n    width: 100%;\n    height: 100%;\n    border: 0px;\n  }\n  .llh-input:focus {\n    outline: none;\n    content: none;\n  }\n  .llh-input:empty:before {\n    content: attr(aria-placeholder);\n    color: #cccccc;\n    cursor: text;\n  }\n  .llh-input[contenteditable='true'] {\n    border: 1px solid #e4e3e3;\n    padding: 2px 4px;\n    border-radius: 4px;\n  }\n  .llh-input-at {\n    margin-left: 2px;\n    color: #0c63fa;\n    display: inline-block;\n    height: 18px;\n    line-height: 18px;\n  }\n  .llh-inputShadow {\n    z-index: -1;\n    position: absolute;\n    visibility: hidden;\n  }\n  .llh-input-popup {\n    border: 1px solid #ebedf0;\n    z-index: 1;\n    border-radius: 6px;\n    box-shadow: 0px 10px 10px 0px rgb(0 0 0 / 4%);\n  }\n  .llh-input-popup-list {\n    padding: 8px;\n    margin: 0;\n    list-style: none;\n    height: 240px;\n    width: 280px;\n    background-color: #ffffff;\n    overflow-y: scroll;\n    border-radius: 6px;\n  }\n  .llh-input-popup-list-item {\n    height: 36px;\n    display: flex;\n    align-items: center;\n    flex-flow: row;\n    padding: 0 6px;\n    border-radius: 4px;\n    font: 400 12px PingFangSC-Regular, 'PingFang SC';\n    color: #000000;\n    cursor: pointer;\n  }\n  .llh-input-popup-list-item-avater {\n    flex-shrink: 0;\n    height: 24px;\n    width: 24px;\n    border-radius: 50%;\n    background-size: cover;\n    background-repeat: no-repeat;\n  }\n  .llh-input-popup-list-item-msg {\n    margin-left: 8px;\n  }\n  .llh-input-popup-list-item-msg-name {\n    max-width: 214px;\n    display: inline-block;\n    width: 214px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .llh-input-popup-list-item-msg-dep {\n    max-width: 214px;\n    display: inline-block;\n    width: 214px;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    color: #aeafb2;\n  }\n",h("[data-at-user-id='".concat(this.options.user.id,"']"),"\n        background-color:#0c63fa!important;\n        color:#ffffff!important;\n        border-color:#0c63fa!important;\n        padding:0 4px!important;\n        border-radius:13px!important;\n      ",E)),this.styleSheet=E}},{key:"initDom",value:function(){this.ele.className="llh-inputcontainer";var e=new S(this),t=e.getEle();this.input=e,t.innerHTML=this.options.default.innerHTML,this.ele.appendChild(t);var n=new k(this),r=n.getEle();this.inputShadow=n,this.positionDom.appendChild(r)}},{key:"getEle",value:function(){return this.ele}},{key:"setEditable",value:function(e){var t=this.input.getEle();if(t.contentEditable=String(e),e){t.focus();var n=this.document.createRange();n.selectNodeContents(t.lastChild||t),n.collapse(!1);var r=this.document.getSelection();if(!r)return;if(0!==r.anchorOffset)return;r.removeAllRanges(),r.addRange(n)}}},{key:"updateDefaultOptions",value:function(e){var t=e.key,n=e.value;this.options.default[t]=n}},{key:"resetInnerHTML",value:function(){this.input.getEle().innerHTML=this.options.default.innerHTML}}])}(),S=function(){return b((function e(t){y(this,e),x(this,"ele",null),x(this,"searchTxt",""),x(this,"searchAts",[]),x(this,"popupEle",null),x(this,"popupEleSelected",""),x(this,"ats",[]),x(this,"lastSelection",null),x(this,"cursorId","llh-plugin-note-shadow-cursor-".concat((new Date).getTime())),x(this,"inputType",""),this.parent=t,this.init(),this.initPopup(),this.getAts()}),[{key:"initPopup",value:function(){var e=this.parent.document,t=e.createElement("div");t.className="llh-input-popup",t.style.visibility="hidden",t.style.position="fixed",t.style.zIndex="-1",t.onwheel=function(e){e.stopPropagation()};var n=e.createElement("div");n.className="llh-input-popup-list",t.appendChild(n),this.popupEle=t,this.parent.positionDom.appendChild(this.popupEle)}},{key:"init",value:function(){var e=this,t=this.parent,n=t.options,r=t.document,o=t.event,i=g((function(e){e()}),300,!1),a=g((function(e){e()}),100,!1),l=function(){var t=e.parent,r=t.document,o=t.inputShadow.getEle(),a=r.getSelection(),l=a.getRangeAt(0),c=a.anchorNode,u=a.anchorOffset,s=c.textContent,p=s.slice(0,u).lastIndexOf("@");if(-1!==p){var d=s.slice(p,l.endOffset),f=d.replace(/\s/g,""),h=f.length;if(d.length!==h||h<2)return e.searchTxt=f,void e.closeAtsPopup();var m=r.createElement("span");m.id=e.cursorId,m.innerHTML="@",m.style.visibility="hidden";var g=r.createElement("span");g.innerHTML="@",g.style.visibility="hidden",a.extend(l.startContainer,p),a.collapseToStart();var v=r.getSelection(),y=v.getRangeAt(0);e.lastSelection={range:y,offset:1,selection:v},y.insertNode(m),y.insertNode(g);var _=m.getBoundingClientRect(),b=e.parent.positionDom.getBoundingClientRect();y.deleteContents(),v.extend(l.startContainer,l.startOffset),v.collapseToEnd(),m.style.width="".concat(_.width,"px"),m.style.height="".concat(_.height,"px"),m.style.top="".concat(_.top-b.top,"px"),m.style.left="".concat(_.left-b.left,"px"),m.style.position="fixed",o.innerHTML="",o.appendChild(m),y=(v=r.getSelection()).getRangeAt(0),e.searchTxt=f,(f=f.slice(1))&&i((function(){n.getAts(f).then((function(t){e.searchAts=t,e.drawAtsPopup()})).catch((function(e){console.warn("搜索用户失败: "+e)}))}))}else e.closeAtsPopup()},c=function(t){var n,i=r.getSelection(),a=i.getRangeAt(0);a.deleteContents(),a.insertNode(t),i.collapseToEnd(),l(),null===(n=o.onChange)||void 0===n||n.call(o,e.ele)},u=r.createElement("div");u.className="llh-input",u.contentEditable="true",u.ariaPlaceholder="请输入笔记，可@相关人员",u.oninput=function(t){var r,a=e.parent,l=a.document,c=a.inputShadow.getEle();if(null===(r=o.onChange)||void 0===r||r.call(o,e.ele),"@"===t.data){e.searchTxt="@";var u=l.getSelection(),s=u.getRangeAt(0),p=l.createElement("span");p.id=e.cursorId,p.innerHTML="@",p.style.visibility="hidden",s.insertNode(p);var d=p.getBoundingClientRect(),f=e.parent.positionDom.getBoundingClientRect();return u.extend(s.startContainer,s.startOffset),u.collapseToEnd(),p.style.width="".concat(d.width,"px"),p.style.height="".concat(d.height,"px"),p.style.top="".concat(d.top-f.top,"px"),p.style.left="".concat(d.left-f.left,"px"),p.style.position="fixed",c.innerHTML="",c.appendChild(p),s=(u=l.getSelection()).getRangeAt(0),e.lastSelection={range:s,offset:u.focusOffset,selection:u},e.closeAtsPopup(),i((function(){n.getAts("a").then((function(t){e.searchAts=t,e.drawAtsPopup()})).catch((function(e){console.warn("搜索用户失败: "+e)}))})),!1}if(e.searchTxt.startsWith("@")){var h=e.lastSelection,m=h.selection,g=h.offset,v=m.anchorNode,y=m.anchorOffset,_=v.textContent.slice(g-1,y),b=_.replace(/\s/g,"");_.length!==b.length?(b="",e.searchTxt=b):(e.searchTxt=b,b=b.slice(1).trim()),i(b?function(){n.getAts(b).then((function(t){e.searchAts=t||[],e.drawAtsPopup()})).catch((function(e){console.warn("搜索用户失败: "+e)}))}:function(){n.getAts("a").then((function(t){e.searchAts=t||[],e.drawAtsPopup()})).catch((function(e){console.warn("搜索用户失败: "+e)}))})}},u.onblur=function(){var t=u.textContent;setTimeout((function(){var n;t===u.textContent&&(e.closeAtsPopup(),null===(n=o.onBlur)||void 0===n||n.call(o,e.ele))}),100)},u.onkeydown=function(t){var n,r,o=t.code,i=e.parent.document;switch(o){case"Backspace":var a=i.getSelection().getRangeAt(0),l=null;if(a.startOffset<=1&&"llh-input-at"!==(null===(n=a.startContainer.parentElement)||void 0===n?void 0:n.className)&&"\n"!==a.startContainer.outerText?l=a.startContainer.previousElementSibling:"llh-input-at"===(null===(r=a.startContainer.parentElement)||void 0===r?void 0:r.className)&&(l=a.startContainer.parentElement),l&&l.getAttribute("data-at-user-id")&&(l.parentNode.removeChild(l),0===a.startOffset))return!1;break;case"ArrowUp":case"ArrowDown":if("visible"===e.popupEle.style.visibility){var c=e.searchAts.length,u="ArrowUp"===o,s=e.searchAts.findIndex((function(t){return t.id===e.popupEleSelected}));u?(s-=1)<0&&(s=c-1):(s+=1)===c&&(s=0);var p=e.popupEleSelected,d=e.searchAts[s].id;m('[data-user-id="'.concat(p,'"]'),e.parent.styleSheet),h("[data-user-id='".concat(d,"']"),"background-color: #f3f3f3",e.parent.styleSheet),e.popupEleSelected=d;var f=i.querySelector("[data-user-id='".concat(d,"']")),g=f.clientHeight,v=f.parentElement,y=v.clientHeight,_=e.popupEle.children[0],b=_.clientHeight,x=g*(s+1),w=_.scrollTop;return x>=b+w?v.scrollTo({top:x-y+8,left:0}):x<=w&&v.scrollTo({top:u?x-g:0,left:0}),!1}break;case"Enter":if("visible"===e.popupEle.style.visibility&&e.popupEleSelected&&"compositionstart"!==e.inputType){var E=e.searchAts.find((function(t){return t.id===e.popupEleSelected}));return e.addAt(E),!1}break;case"Space":t.preventDefault();var O=i.getSelection();if(O.rangeCount>0){var S=O.getRangeAt(0);S.deleteContents();var k=i.createTextNode(" ");S.insertNode(k),S.setStartAfter(k),S.setEndAfter(k),O.removeAllRanges(),O.addRange(S)}"visible"===e.popupEle.style.visibility&&e.closeAtsPopup()}},u.onkeyup=function(t){switch(t.code){case"Backspace":"compositionstart"===e.inputType||e.searchTxt||a((function(){l()}));break;case"ArrowLeft":case"ArrowRight":"compositionstart"!==e.inputType&&a((function(){l()}))}},u.onfocus=function(){l()},u.onpaste=function(e){var t,o=e.clipboardData,i=o.getData("text/plain");if(i)t=r.createTextNode(i),c(t);else{var a=o.items[0];if("file"===a.kind&&a.type.startsWith("image/")&&n.uploadImage){var l=a.getAsFile();null!=l&&l.size&&n.uploadImage(l).then((function(e){var n=e.url;(t=r.createElement("img")).src=n,t.style.width="100%",c(t)})).catch((function(e){console.warn("上传失败: "+e)}))}}return!1},u.addEventListener("compositionstart",(function(){e.inputType="compositionstart"}),!1),u.addEventListener("compositionend",(function(){e.inputType=""}),!1),this.ele=u}},{key:"drawAtsPopup",value:function(){var e=this;if(this.searchAts.length&&this.searchTxt.length>0){var t=this.parent.document,n=t.getElementById(this.cursorId).getBoundingClientRect(),r=n.top,o=n.left,i=n.height,a=this.parent.positionDom.getBoundingClientRect(),l=t.createDocumentFragment(),c=this.popupEle.children[0];c.innerHTML="";var u=null;m("[data-user-id",this.parent.styleSheet),this.searchAts.forEach((function(n){var r=n.name,o=n.username,i=n.orgDisplayName,a=n.thumbnailAvatarUrl,c=n.id;u||(u=c,e.popupEleSelected=c);var s=t.createElement("div");s.className="llh-input-popup-list-item ".concat(c),s.onmouseenter=function(t){var n=t.target.getAttribute("data-user-id");n!==String(e.popupEleSelected)&&(m('[data-user-id="'.concat(e.popupEleSelected,'"]'),e.parent.styleSheet),h("[data-user-id='".concat(n,"']"),"background-color: #f3f3f3",e.parent.styleSheet),e.popupEleSelected=n)},s.onclick=function(){e.addAt(n)},s.setAttribute("data-user-id",c),s.setAttribute("data-info",JSON.stringify({name:r,username:o,orgDisplayName:i,thumbnailAvatarUrl:a}));var p=t.createElement("div");p.className="llh-input-popup-list-item-avater",p.style.backgroundImage="url(".concat(a,")");var d=t.createElement("div");d.className="llh-input-popup-list-item-msg";var f=t.createElement("div");f.className="llh-input-popup-list-item-msg-name",f.innerText=r,s.appendChild(p),s.appendChild(d),d.appendChild(f),l.appendChild(s)})),c.appendChild(l),"hidden"===this.popupEle.style.visibility&&(this.popupEle.style.top="".concat(r+i-a.top,"px"),this.popupEle.style.left="".concat(o-a.left,"px"),this.popupEle.style.zIndex="9999",this.popupEle.style.visibility="visible"),h("[data-user-id='".concat(u,"']"),"background-color: #f3f3f3",this.parent.styleSheet)}else this.closeAtsPopup()}},{key:"closeAtsPopup",value:function(){this.popupEle.style.visibility="hidden",this.popupEle.style.zIndex="-1"}},{key:"addAt",value:function(e){var t=e.name,n=e.username,r=e.orgDisplayName,o=e.thumbnailAvatarUrl,i=e.id,a=this.parent.document,l=this.lastSelection,c=l.range,u=l.selection,s=c.startContainer;c.setStart(s,c.endOffset-1),c.setEnd(s,c.endOffset+this.searchTxt.length-1),c.deleteContents(),this.searchTxt="";var p=a.createElement("div"),d=a.createElement("span");p.className="llh-input-at",p.contentEditable="false",p.innerHTML="@"+t,p.setAttribute("data-at-user-id",i),p.setAttribute("data-info",JSON.stringify({name:t,username:n,orgDisplayName:r,thumbnailAvatarUrl:o})),p.setAttribute("data-is-me",String(this.parent.options.user.id===i)),d.innerHTML="&nbsp;";var f,h,m=a.createDocumentFragment();for(m.appendChild(p);f=d.firstChild;)h=m.appendChild(f);c.insertNode(m),u.extend(h,1),u.collapseToEnd(),this.closeAtsPopup()}},{key:"getEle",value:function(){return this.ele}},{key:"getAts",value:function(){var e=[],t=this.ele.querySelectorAll(".llh-input-at"),n={};return t.forEach((function(t){var r=t.getAttribute("data-at-user-name");if(!n[r]){n[r]=r;var o=t.getAttribute("data-info");o=JSON.parse(o),e.push(o)}})),this.ats=e,this.ats}}])}(),k=function(){return b((function e(t){y(this,e),x(this,"ele",null),this.parent=t,this.init()}),[{key:"init",value:function(){var e=this.parent.document.createElement("div");e.className="llh-inputShadow",this.ele=e}},{key:"getEle",value:function(){return this.ele}}])}();function j(e){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},j(e)}function C(e,t,n){return r=function(e,t){if("object"!=j(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=j(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==j(r)?r:r+"")in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e;var r}function A(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?A(Object(n),!0).forEach((function(t){C(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):A(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const T={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"};function N(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function P(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return N(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?N(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function R(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}var L=__webpack_require__(942),M=__webpack_require__.n(L);const I=(0,t.createContext)({});function F(e,t){(function(e){return"string"==typeof e&&-1!==e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var n=function(e){return"string"==typeof e&&-1!==e.indexOf("%")}(e);return e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),n&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t))}function D(e){return e<=1?"".concat(100*Number(e),"%"):e}function B(e){return 1===e.length?"0"+e:String(e)}function U(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*n*(t-e):n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function z(e){return V(e)/255}function V(e){return parseInt(e,16)}var q={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function H(e){var t,n,r,o={r:0,g:0,b:0},i=1,a=null,l=null,c=null,u=!1,s=!1;return"string"==typeof e&&(e=function(e){if(0===(e=e.trim().toLowerCase()).length)return!1;var t=!1;if(q[e])e=q[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var n=Y.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=Y.rgba.exec(e))?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=Y.hsl.exec(e))?{h:n[1],s:n[2],l:n[3]}:(n=Y.hsla.exec(e))?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=Y.hsv.exec(e))?{h:n[1],s:n[2],v:n[3]}:(n=Y.hsva.exec(e))?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=Y.hex8.exec(e))?{r:V(n[1]),g:V(n[2]),b:V(n[3]),a:z(n[4]),format:t?"name":"hex8"}:(n=Y.hex6.exec(e))?{r:V(n[1]),g:V(n[2]),b:V(n[3]),format:t?"name":"hex"}:(n=Y.hex4.exec(e))?{r:V(n[1]+n[1]),g:V(n[2]+n[2]),b:V(n[3]+n[3]),a:z(n[4]+n[4]),format:t?"name":"hex8"}:!!(n=Y.hex3.exec(e))&&{r:V(n[1]+n[1]),g:V(n[2]+n[2]),b:V(n[3]+n[3]),format:t?"name":"hex"}}(e)),"object"==typeof e&&(G(e.r)&&G(e.g)&&G(e.b)?(t=e.r,n=e.g,r=e.b,o={r:255*F(t,255),g:255*F(n,255),b:255*F(r,255)},u=!0,s="%"===String(e.r).substr(-1)?"prgb":"rgb"):G(e.h)&&G(e.s)&&G(e.v)?(a=D(e.s),l=D(e.v),o=function(e,t,n){e=6*F(e,360),t=F(t,100),n=F(n,100);var r=Math.floor(e),o=e-r,i=n*(1-t),a=n*(1-o*t),l=n*(1-(1-o)*t),c=r%6;return{r:255*[n,a,i,i,l,n][c],g:255*[l,n,n,a,i,i][c],b:255*[i,i,l,n,n,a][c]}}(e.h,a,l),u=!0,s="hsv"):G(e.h)&&G(e.s)&&G(e.l)&&(a=D(e.s),c=D(e.l),o=function(e,t,n){var r,o,i;if(e=F(e,360),t=F(t,100),n=F(n,100),0===t)o=n,i=n,r=n;else{var a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;r=U(l,a,e+1/3),o=U(l,a,e),i=U(l,a,e-1/3)}return{r:255*r,g:255*o,b:255*i}}(e.h,a,c),u=!0,s="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(i=e.a)),i=function(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}(i),{ok:u,format:e.format||s,r:Math.min(255,Math.max(o.r,0)),g:Math.min(255,Math.max(o.g,0)),b:Math.min(255,Math.max(o.b,0)),a:i}}var J="(?:".concat("[-\\+]?\\d*\\.\\d+%?",")|(?:").concat("[-\\+]?\\d+%?",")"),K="[\\s|\\(]+(".concat(J,")[,|\\s]+(").concat(J,")[,|\\s]+(").concat(J,")\\s*\\)?"),W="[\\s|\\(]+(".concat(J,")[,|\\s]+(").concat(J,")[,|\\s]+(").concat(J,")[,|\\s]+(").concat(J,")\\s*\\)?"),Y={CSS_UNIT:new RegExp(J),rgb:new RegExp("rgb"+K),rgba:new RegExp("rgba"+W),hsl:new RegExp("hsl"+K),hsla:new RegExp("hsla"+W),hsv:new RegExp("hsv"+K),hsva:new RegExp("hsva"+W),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function G(e){return Boolean(Y.CSS_UNIT.exec(String(e)))}var $=2,Q=.16,X=.05,ee=.05,te=.15,ne=5,re=4,oe=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function ie(e){var t=function(e,t,n){e=F(e,255),t=F(t,255),n=F(n,255);var r=Math.max(e,t,n),o=Math.min(e,t,n),i=0,a=r,l=r-o,c=0===r?0:l/r;if(r===o)i=0;else{switch(r){case e:i=(t-n)/l+(t<n?6:0);break;case t:i=(n-e)/l+2;break;case n:i=(e-t)/l+4}i/=6}return{h:i,s:c,v:a}}(e.r,e.g,e.b);return{h:360*t.h,s:t.s,v:t.v}}function ae(e){var t=e.r,n=e.g,r=e.b;return"#".concat(function(e,t,n,r){var o=[B(Math.round(e).toString(16)),B(Math.round(t).toString(16)),B(Math.round(n).toString(16))];return o.join("")}(t,n,r))}function le(e,t,n){var r;return(r=Math.round(e.h)>=60&&Math.round(e.h)<=240?n?Math.round(e.h)-$*t:Math.round(e.h)+$*t:n?Math.round(e.h)+$*t:Math.round(e.h)-$*t)<0?r+=360:r>=360&&(r-=360),r}function ce(e,t,n){return 0===e.h&&0===e.s?e.s:((r=n?e.s-Q*t:t===re?e.s+Q:e.s+X*t)>1&&(r=1),n&&t===ne&&r>.1&&(r=.1),r<.06&&(r=.06),Number(r.toFixed(2)));var r}function ue(e,t,n){var r;return(r=n?e.v+ee*t:e.v-te*t)>1&&(r=1),Number(r.toFixed(2))}function se(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=[],r=H(e),o=ne;o>0;o-=1){var i=ie(r),a=ae(H({h:le(i,o,!0),s:ce(i,o,!0),v:ue(i,o,!0)}));n.push(a)}n.push(ae(r));for(var l=1;l<=re;l+=1){var c=ie(r),u=ae(H({h:le(c,l),s:ce(c,l),v:ue(c,l)}));n.push(u)}return"dark"===t.theme?oe.map((function(e){var r,o,i,a=e.index,l=e.opacity;return ae((r=H(t.backgroundColor||"#141414"),i=100*l/100,{r:((o=H(n[a])).r-r.r)*i+r.r,g:(o.g-r.g)*i+r.g,b:(o.b-r.b)*i+r.b}))})):n}var pe={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1890FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},de={},fe={};Object.keys(pe).forEach((function(e){de[e]=se(pe[e]),de[e].primary=de[e][5],fe[e]=se(pe[e],{theme:"dark",backgroundColor:"#141414"}),fe[e].primary=fe[e][5]})),de.red,de.volcano,de.gold,de.orange,de.yellow,de.lime,de.green,de.cyan,de.blue,de.geekblue,de.purple,de.magenta,de.grey;var he={},me=[];function ge(e,t){}function ve(e,t){}function ye(e,t,n){t||he[n]||(e(!1,n),he[n]=!0)}function _e(e,t){ye(ge,e,t)}_e.preMessage=function(e){me.push(e)},_e.resetWarned=function(){he={}},_e.noteOnce=function(e,t){ye(ve,e,t)};const be=_e;var xe="data-rc-order",we="data-rc-priority",Ee="rc-util-key",Oe=new Map;function Se(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).mark;return e?e.startsWith("data-")?e:"data-".concat(e):Ee}function ke(e){return e.attachTo?e.attachTo:document.querySelector("head")||document.body}function je(e){return Array.from((Oe.get(e)||e).children).filter((function(e){return"STYLE"===e.tagName}))}function Ce(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("undefined"==typeof window||!window.document||!window.document.createElement)return null;var n=t.csp,r=t.prepend,o=t.priority,i=void 0===o?0:o,a=function(e){return"queue"===e?"prependQueue":e?"prepend":"append"}(r),l="prependQueue"===a,c=document.createElement("style");c.setAttribute(xe,a),l&&i&&c.setAttribute(we,"".concat(i)),null!=n&&n.nonce&&(c.nonce=null==n?void 0:n.nonce),c.innerHTML=e;var u=ke(t),s=u.firstChild;if(r){if(l){var p=(t.styles||je(u)).filter((function(e){if(!["prepend","prependQueue"].includes(e.getAttribute(xe)))return!1;var t=Number(e.getAttribute(we)||0);return i>=t}));if(p.length)return u.insertBefore(c,p[p.length-1].nextSibling),c}u.insertBefore(c,s)}else u.appendChild(c);return c}function Ae(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=ke(n),o=je(r),i=Z(Z({},n),{},{styles:o});!function(e,t){var n=Oe.get(e);if(!n||!function(e,t){if(!e)return!1;if(e.contains)return e.contains(t);for(var n=t;n;){if(n===e)return!0;n=n.parentNode}return!1}(document,n)){var r=Ce("",t),o=r.parentNode;Oe.set(e,o),e.removeChild(r)}}(r,i);var a,l,c,u=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=ke(t);return(t.styles||je(n)).find((function(n){return n.getAttribute(Se(t))===e}))}(t,i);if(u)return null!==(a=i.csp)&&void 0!==a&&a.nonce&&u.nonce!==(null===(l=i.csp)||void 0===l?void 0:l.nonce)&&(u.nonce=null===(c=i.csp)||void 0===c?void 0:c.nonce),u.innerHTML!==e&&(u.innerHTML=e),u;var s=Ce(e,i);return s.setAttribute(Se(i),t),s}var Ze=__webpack_require__(58),Te=__webpack_require__.n(Ze);function Ne(e){return"object"===j(e)&&"string"==typeof e.name&&"string"==typeof e.theme&&("object"===j(e.icon)||"function"==typeof e.icon)}function Pe(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.keys(e).reduce((function(t,n){var r=e[n];return"class"===n?(t.className=r,delete t.class):(delete t[n],t[Te()(n)]=r),t}),{})}function Re(e,t,r){return r?n().createElement(e.tag,Z(Z({key:t},Pe(e.attrs)),r),(e.children||[]).map((function(n,r){return Re(n,"".concat(t,"-").concat(e.tag,"-").concat(r))}))):n().createElement(e.tag,Z({key:t},Pe(e.attrs)),(e.children||[]).map((function(n,r){return Re(n,"".concat(t,"-").concat(e.tag,"-").concat(r))})))}function Le(e){return se(e)[0]}function Me(e){return e?Array.isArray(e)?e:[e]:[]}var Ie=["icon","className","onClick","style","primaryColor","secondaryColor"],Fe={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1},De=function(e){var n,r,o=e.icon,i=e.className,a=e.onClick,l=e.style,c=e.primaryColor,u=e.secondaryColor,s=R(e,Ie),p=Fe;if(c&&(p={primaryColor:c,secondaryColor:u||Le(c)}),function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"\n.anticon {\n  display: inline-flex;\n  alignItems: center;\n  color: inherit;\n  font-style: normal;\n  line-height: 0;\n  text-align: center;\n  text-transform: none;\n  vertical-align: -0.125em;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.anticon > * {\n  line-height: 1;\n}\n\n.anticon svg {\n  display: inline-block;\n}\n\n.anticon::before {\n  display: none;\n}\n\n.anticon .anticon-icon {\n  display: block;\n}\n\n.anticon[tabindex] {\n  cursor: pointer;\n}\n\n.anticon-spin::before,\n.anticon-spin {\n  display: inline-block;\n  -webkit-animation: loadingCircle 1s infinite linear;\n  animation: loadingCircle 1s infinite linear;\n}\n\n@-webkit-keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loadingCircle {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n",n=(0,t.useContext)(I).csp;(0,t.useEffect)((function(){Ae(e,"@ant-design-icons",{prepend:!0,csp:n})}),[])}(),n=Ne(o),r="icon should be icon definiton, but got ".concat(o),be(n,"[@ant-design/icons] ".concat(r)),!Ne(o))return null;var d=o;return d&&"function"==typeof d.icon&&(d=Z(Z({},d),{},{icon:d.icon(p.primaryColor,p.secondaryColor)})),Re(d.icon,"svg-".concat(d.name),Z({className:i,onClick:a,style:l,"data-icon":d.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},s))};De.displayName="IconReact",De.getTwoToneColors=function(){return Z({},Fe)},De.setTwoToneColors=function(e){var t=e.primaryColor,n=e.secondaryColor;Fe.primaryColor=t,Fe.secondaryColor=n||Le(t),Fe.calculated=!!n};const Be=De;function Ue(e){var t=P(Me(e),2),n=t[0],r=t[1];return Be.setTwoToneColors({primaryColor:n,secondaryColor:r})}var ze=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];Ue("#1890ff");var Ve=t.forwardRef((function(e,n){var r=e.className,o=e.icon,i=e.spin,a=e.rotate,l=e.tabIndex,c=e.onClick,u=e.twoToneColor,s=R(e,ze),p=t.useContext(I),d=p.prefixCls,f=void 0===d?"anticon":d,h=p.rootClassName,m=M()(h,f,C(C({},"".concat(f,"-").concat(o.name),!!o.name),"".concat(f,"-spin"),!!i||"loading"===o.name),r),g=l;void 0===g&&c&&(g=-1);var v=a?{msTransform:"rotate(".concat(a,"deg)"),transform:"rotate(".concat(a,"deg)")}:void 0,y=P(Me(u),2),_=y[0],b=y[1];return t.createElement("span",Z(Z({role:"img","aria-label":o.name},s),{},{ref:n,tabIndex:g,onClick:c,className:m}),t.createElement(Be,{icon:o,primaryColor:_,secondaryColor:b,style:v}))}));Ve.displayName="AntdIcon",Ve.getTwoToneColor=function(){var e=Be.getTwoToneColors();return e.calculated?[e.primaryColor,e.secondaryColor]:e.primaryColor},Ve.setTwoToneColor=Ue;const qe=Ve;var He=function(e,n){return t.createElement(qe,Z(Z({},e),{},{ref:n,icon:T}))};const Je=t.forwardRef(He),Ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var We=function(e,n){return t.createElement(qe,Z(Z({},e),{},{ref:n,icon:Ke}))};const Ye=t.forwardRef(We);var Ge=__webpack_require__(72),$e=__webpack_require__.n(Ge),Qe=__webpack_require__(825),Xe=__webpack_require__.n(Qe),et=__webpack_require__(659),tt=__webpack_require__.n(et),nt=__webpack_require__(868),rt=__webpack_require__.n(nt),ot=__webpack_require__(540),it=__webpack_require__.n(ot),at=__webpack_require__(113),lt=__webpack_require__.n(at),ct=__webpack_require__(717),ut={attributes:{title:"less"}};ut.styleTagTransform=lt(),ut.setAttributes=rt(),ut.insert=tt().bind(null,"head"),ut.domAPI=Xe(),ut.insertStyleElement=it(),$e()(ct.A,ut);const st=ct.A&&ct.A.locals?ct.A.locals:void 0;function pt(e){return pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pt(e)}var dt=["thumbnailAvatarUrl"];function ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ht(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ft(Object(n),!0).forEach((function(t){var r,o,i,a;r=e,o=t,i=n[t],a=function(e,t){if("object"!=pt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=pt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(o),(o="symbol"==pt(a)?a:a+"")in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function mt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return gt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?gt(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function vt(e){var r=e.index,o=e.notes,i=e.appCtx,l=e.document,c=e.noteViewDom,u=e.onEvent,s=mt((0,t.useState)(!1),2),p=s[0],f=s[1],h=mt((0,t.useState)(!1),2),m=h[0],g=h[1],v=mt((0,t.useState)(null),2),y=v[0],_=v[1];(0,t.useEffect)((function(){if(p&&y){var e=y.input;(void 0===e?{}:e).ele.innerText.trim()&&g(!0)}}),[y]);var b=(0,t.useCallback)((function(e){var t=e.type;switch(t){case"submit":var n=y.input,r=void 0===n?{}:n,a=r.ele,l=a.innerHTML,c=a.innerText,s=i.user,p=r.getAts();y.updateDefaultOptions({key:"innerHTML",value:l}),u({type:t,notes:{id:o.id||d(),innerText:encodeURIComponent(c),innerHTML:encodeURIComponent(l),operationTime:o.operationTime,operator:{username:s.userName,name:s.name,thumbnailAvatarUrl:s.avatar&&"/assets/default_avatar.png"!==s.avatar?s.avatar:"/default_avatar.png",email:s.email},ats:p}}),f(!1);break;case"cancel":u({type:t,notes:o}),o.operationTime&&(y.resetInnerHTML(),f(!1));break;case"edit":f(!0),g(!0),u({type:t,notes:o});break;case"delete":u({type:t,notes:o});break;case"comment":var h=o.operator,m="<div class='llh-input-at' contenteditable='false' data-at-user-name='".concat(h.username,"' data-info='").concat(JSON.stringify(h),"'>@").concat(h.name,"</div>&nbsp;");u({type:t,notes:{id:d(),innerText:"@".concat(h.name," "),innerHTML:m,operationTime:null,operator:{username:i.user.userName,name:i.user.name,thumbnailAvatarUrl:i.user.avatar&&"/assets/default_avatar.png"!==i.user.avatar?i.user.avatar:"/default_avatar.png",email:i.user.email}}})}}),[y]),x=(0,t.useMemo)((function(){var e=!0;if(o.operationTime||(f(!0),r&&(e=!1)),!e)return n().createElement(n().Fragment,null);var t,l=o,c=l.operator,u=l.operationTime,s=i.user;return n().createElement("div",{className:st.user},n().createElement("div",{className:st.userinfo},n().createElement("div",{className:st.useravatar},n().createElement("img",{src:c.thumbnailAvatarUrl})),n().createElement("div",{className:st.username},c.name||c.username||c.email)),n().createElement("div",{className:st.time},n().createElement("div",{className:st.fotmatTime},function(e){return a()(e).format("YYYY-MM-DD")===a()().format("YYYY-MM-DD")}(t=u||(new Date).getTime())?a()(t).format("HH:mm"):function(e){return a()(e).format("YYYY")===a()().format("YYYY")}(t)?a()(t).format("M月D日 HH:mm"):a()(t).format("YYYY年M月D日")),n().createElement("div",{className:st.menu},n().createElement("div",{"data-note-icon":"MessageOutlined","data-mybricks-tip":"回复"},n().createElement(Je,{onClick:function(){return b({type:"comment"})}})),s.email===c.email?n().createElement("div",{"data-note-icon":"MoreOutlined"},n().createElement(Ye,{"data-icon":"MoreOutlined"}),n().createElement("div",{className:st.action},n().createElement("div",{onClick:function(){return b({type:"edit"})}},"编辑"),n().createElement("div",{onClick:function(){return b({type:"delete"})}},"删除"))):n().createElement(n().Fragment,null))))}),[o]),w=(0,t.useMemo)((function(){return n().createElement(yt,{document:l,positionDom:c,options:{default:{innerHTML:""},user:{id:i.user.id},getAts:function(e){return new Promise((function(t,n){i.onSearchUser||n("无搜索用户服务，请先配置应用搜索用户服务"),i.onSearchUser(e).then((function(e){var n=[];Array.isArray(e)&&(n=e.map((function(e){var t=e.thumbnailAvatarUrl;return ht(ht({},function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,dt)),{},{thumbnailAvatarUrl:t&&"/assets/default_avatar.png"!==t?t:"/default_avatar.png"})}))),t(n)})).catch((function(e){n(e)}))}))},uploadImage:function(e){return i.onUpload?new Promise((function(t,n){i.onUpload(e).then((function(e){console.log(e,"onUpload-data");var n=e.url;t({url:n})})).catch((function(e){n(e)}))})):Promise.reject("应用未实现上传功能，请先配置应用上传功能")}},editable:p,ctx:{textareaRef:y,setTextareaRef:_},event:{onChange:function(e){e.innerHTML.replace(/&nbsp;/g,"").trim()?g(!0):g(!1)}},notes:o})}),[o,p]),E=(0,t.useMemo)((function(){return n().createElement("div",{className:st.buttonSet,style:{display:p?"flex":"none"}},n().createElement("button",{"data-btn-type":"cancel",onClick:function(){return b({type:"cancel"})}},"取消"),n().createElement("button",{"data-btn-type":"submit",onClick:function(){return b({type:"submit"})},disabled:!m},"确定"))}),[p,m,y]);return n().createElement("div",{className:st.item,style:{marginTop:r?"15px":"0px"}},x,w,E)}function yt(e){var r=e.document,o=e.positionDom,i=e.options,a=e.editable,l=e.ctx,c=e.event,u=e.notes,s=(0,t.useRef)();return(0,t.useEffect)((function(){var e,t=decodeURIComponent(u.innerHTML);s.current.input.ele.innerHTML=t,null===(e=l.textareaRef)||void 0===e||e.updateDefaultOptions({key:"innerHTML",value:t})}),[u.innerHTML,l.textareaRef]),(0,t.useEffect)((function(){var e;null===(e=s.current)||void 0===e||e.setEditable(a)}),[a]),n().createElement("div",{ref:function(e){e&&!s.current&&(s.current=new O(e,r,o,i,c),l.setTextareaRef(s.current))}})}function _t(e){return _t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_t(e)}function bt(e){return function(e){if(Array.isArray(e))return St(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Ot(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(n),!0).forEach((function(t){var r,o,i,a;r=e,o=t,i=n[t],a=function(e,t){if("object"!=_t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=_t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(o),(o="symbol"==_t(a)?a:a+"")in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Et(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(u)throw o}}return l}}(e,t)||Ot(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ot(e,t){if(e){if("string"==typeof e)return St(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?St(e,t):void 0}}function St(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function kt(e){var r=e.data,o=e.appCtx,i=e.noteId,a=e.document,l=e.noteViewDom,c=e.deleteNote,u=(0,t.useCallback)((function(){var e=o.user;return{id:d(),innerText:"",innerHTML:"",operationTime:null,operator:{username:e.userName,name:e.name,thumbnailAvatarUrl:e.avatar&&"/assets/default_avatar.png"!==e.avatar?e.avatar:"/default_avatar.png",email:e.email}}}),[]),f=Et((0,t.useState)(p(r.notesMap[i])||[u()]),2),h=f[0],m=f[1],g=Et((0,t.useState)("addComment"),2),v=g[0],y=g[1],_=Et((0,t.useState)({}),2),b=_[0],x=(_[1],Et((0,t.useState)({}),2)),w=x[0],E=x[1],O=Et((0,t.useState)(!0),2),S=O[0],k=O[1];(0,t.useMemo)((function(){s(h,"object")?m([h]):s(h,"undefined")?(k(!1),m([u()]),r.notesMap[i]=h):h[0].operationTime||k(!1),h.forEach((function(e){e.id||(e.id=d());var t=e.id;b[t]={type:void 0}})),E(p(b)),r.notesMap[i]=h,r.operationMap[i]=b}),[r.id]);var j=(0,t.useCallback)((function(e){var t,n=e.type,o=e.notes,a=void 0===o?u():o;switch(n){case"submit":var l=h.findIndex((function(e){return e.operationTime===a.operationTime}));if(-1!==l){h[l]=wt(wt({},a),{},{operationTime:a.operationTime||(new Date).getTime()});var s=bt(h);m(s),r.notesMap[i]=s,b[a.id]?b[a.id].type||(b[a.id].type=v):b[a.id]={type:v}}y(void 0),k(!0);break;case"cancel":if(!a.operationTime){var p=h.findIndex((function(e){return e.operationTime===a.operationTime}));if(-1!==p){h.splice(p,1);var d=bt(h);m(d),r.notesMap[i]=d}}y(void 0),k(!0);break;case"comment":case"add":k(!1),h.push(a),m(bt(h)),y("addComment");break;case"edit":k(!1),y("updateComment");break;case"delete":var f=h.findIndex((function(e){return e.operationTime===a.operationTime}));if(-1!==f){h.splice(f,1);var g=bt(h);m(g),r.notesMap[i]=g}y(void 0),w[a.id]?b[a.id].type="deleteComment":delete b[a.id],k(!0)}null!==(t=r.notesMap[i])&&void 0!==t&&t.length||(c(i),delete r.notesMap[i])}),[]),C=(0,t.useMemo)((function(){return h.map((function(e,t){return n().createElement(vt,{key:String(e.operationTime),index:t,notes:e,appCtx:o,document:a,noteViewDom:l,onEvent:function(e){var t=e.type,n=e.notes;j({type:t,notes:n})}})}))}),[v,h,b,w]),A=(0,t.useMemo)((function(){return n().createElement("div",{className:st.editorTrigger,onClick:function(){return j({type:"add"})}},"添加评论...")}),[]);return n().createElement("div",{className:st.container,"data-type-show-menu":JSON.stringify(!S)},C,A)}function jt(e){return jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jt(e)}function Ct(e,t,n){var r;return r=function(e,t){if("object"!=jt(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=jt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(t),(t="symbol"==jt(r)?r:r+"")in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function At(){return At=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},At.apply(this,arguments)}var Zt=__webpack_require__(216),Tt=__webpack_require__.n(Zt),Nt=__webpack_require__(961),Pt=__webpack_require__.n(Nt),Rt=__webpack_require__(686),Lt={attributes:{title:"less"}};Lt.styleTagTransform=lt(),Lt.setAttributes=rt(),Lt.insert=tt().bind(null,"head"),Lt.domAPI=Xe(),Lt.insertStyleElement=it(),$e()(Rt.A,Lt);const Mt=Rt.A&&Rt.A.locals?Rt.A.locals:void 0;function It(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ft}=Object.prototype,{getPrototypeOf:Dt}=Object,Bt=(Ut=Object.create(null),e=>{const t=Ft.call(e);return Ut[t]||(Ut[t]=t.slice(8,-1).toLowerCase())});var Ut;const zt=e=>(e=e.toLowerCase(),t=>Bt(t)===e),Vt=e=>t=>typeof t===e,{isArray:qt}=Array,Ht=Vt("undefined"),Jt=zt("ArrayBuffer"),Kt=Vt("string"),Wt=Vt("function"),Yt=Vt("number"),Gt=e=>null!==e&&"object"==typeof e,$t=e=>{if("object"!==Bt(e))return!1;const t=Dt(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},Qt=zt("Date"),Xt=zt("File"),en=zt("Blob"),tn=zt("FileList"),nn=zt("URLSearchParams");function rn(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,o;if("object"!=typeof e&&(e=[e]),qt(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let a;for(r=0;r<i;r++)a=o[r],t.call(null,e[a],a,e)}}function on(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const an="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,ln=e=>!Ht(e)&&e!==an,cn=(un="undefined"!=typeof Uint8Array&&Dt(Uint8Array),e=>un&&e instanceof un);var un;const sn=zt("HTMLFormElement"),pn=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),dn=zt("RegExp"),fn=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};rn(n,((n,o)=>{let i;!1!==(i=t(n,o,e))&&(r[o]=i||n)})),Object.defineProperties(e,r)},hn="abcdefghijklmnopqrstuvwxyz",mn="0123456789",gn={DIGIT:mn,ALPHA:hn,ALPHA_DIGIT:hn+hn.toUpperCase()+mn},vn=zt("AsyncFunction"),yn={isArray:qt,isArrayBuffer:Jt,isBuffer:function(e){return null!==e&&!Ht(e)&&null!==e.constructor&&!Ht(e.constructor)&&Wt(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||Wt(e.append)&&("formdata"===(t=Bt(e))||"object"===t&&Wt(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Jt(e.buffer),t},isString:Kt,isNumber:Yt,isBoolean:e=>!0===e||!1===e,isObject:Gt,isPlainObject:$t,isUndefined:Ht,isDate:Qt,isFile:Xt,isBlob:en,isRegExp:dn,isFunction:Wt,isStream:e=>Gt(e)&&Wt(e.pipe),isURLSearchParams:nn,isTypedArray:cn,isFileList:tn,forEach:rn,merge:function e(){const{caseless:t}=ln(this)&&this||{},n={},r=(r,o)=>{const i=t&&on(n,o)||o;$t(n[i])&&$t(r)?n[i]=e(n[i],r):$t(r)?n[i]=e({},r):qt(r)?n[i]=r.slice():n[i]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&rn(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(rn(t,((t,r)=>{n&&Wt(t)?e[r]=It(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,i,a;const l={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),i=o.length;i-- >0;)a=o[i],r&&!r(a,e,t)||l[a]||(t[a]=e[a],l[a]=!0);e=!1!==n&&Dt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Bt,kindOfTest:zt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(qt(e))return e;let t=e.length;if(!Yt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:sn,hasOwnProperty:pn,hasOwnProp:pn,reduceDescriptors:fn,freezeMethods:e=>{fn(e,((t,n)=>{if(Wt(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Wt(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return qt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:on,global:an,isContextDefined:ln,ALPHABET:gn,generateString:(e=16,t=gn.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&Wt(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Gt(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=qt(e)?[]:{};return rn(e,((e,t)=>{const i=n(e,r+1);!Ht(i)&&(o[t]=i)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:vn,isThenable:e=>e&&(Gt(e)||Wt(e))&&Wt(e.then)&&Wt(e.catch)};function _n(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o)}yn.inherits(_n,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:yn.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const bn=_n.prototype,xn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{xn[e]={value:e}})),Object.defineProperties(_n,xn),Object.defineProperty(bn,"isAxiosError",{value:!0}),_n.from=(e,t,n,r,o,i)=>{const a=Object.create(bn);return yn.toFlatObject(e,a,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),_n.call(a,e.message,t,n,r,o),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const wn=_n;function En(e){return yn.isPlainObject(e)||yn.isArray(e)}function On(e){return yn.endsWith(e,"[]")?e.slice(0,-2):e}function Sn(e,t,n){return e?e.concat(t).map((function(e,t){return e=On(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const kn=yn.toFlatObject(yn,{},null,(function(e){return/^is[A-Z]/.test(e)})),jn=function(e,t,n){if(!yn.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=yn.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!yn.isUndefined(t[e])}))).metaTokens,o=n.visitor||u,i=n.dots,a=n.indexes,l=(n.Blob||"undefined"!=typeof Blob&&Blob)&&yn.isSpecCompliantForm(t);if(!yn.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(yn.isDate(e))return e.toISOString();if(!l&&yn.isBlob(e))throw new wn("Blob is not supported. Use a Buffer instead.");return yn.isArrayBuffer(e)||yn.isTypedArray(e)?l&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let l=e;if(e&&!o&&"object"==typeof e)if(yn.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(yn.isArray(e)&&function(e){return yn.isArray(e)&&!e.some(En)}(e)||(yn.isFileList(e)||yn.endsWith(n,"[]"))&&(l=yn.toArray(e)))return n=On(n),l.forEach((function(e,r){!yn.isUndefined(e)&&null!==e&&t.append(!0===a?Sn([n],r,i):null===a?n:n+"[]",c(e))})),!1;return!!En(e)||(t.append(Sn(o,n,i),c(e)),!1)}const s=[],p=Object.assign(kn,{defaultVisitor:u,convertValue:c,isVisitable:En});if(!yn.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!yn.isUndefined(n)){if(-1!==s.indexOf(n))throw Error("Circular reference detected in "+r.join("."));s.push(n),yn.forEach(n,(function(n,i){!0===(!(yn.isUndefined(n)||null===n)&&o.call(t,n,yn.isString(i)?i.trim():i,r,p))&&e(n,r?r.concat(i):[i])})),s.pop()}}(e),t};function Cn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function An(e,t){this._pairs=[],e&&jn(e,this,t)}const Zn=An.prototype;Zn.append=function(e,t){this._pairs.push([e,t])},Zn.toString=function(e){const t=e?function(t){return e.call(this,t,Cn)}:Cn;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Tn=An;function Nn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Pn(e,t,n){if(!t)return e;const r=n&&n.encode||Nn,o=n&&n.serialize;let i;if(i=o?o(t,n):yn.isURLSearchParams(t)?t.toString():new Tn(t,n).toString(r),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}const Rn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){yn.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Ln={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Mn={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Tn,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},In="undefined"!=typeof window&&"undefined"!=typeof document,Fn=(Dn="undefined"!=typeof navigator&&navigator.product,In&&["ReactNative","NativeScript","NS"].indexOf(Dn)<0);var Dn;const Bn="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,Un={...e,...Mn},zn=function(e){function t(e,n,r,o){let i=e[o++];if("__proto__"===i)return!0;const a=Number.isFinite(+i),l=o>=e.length;return i=!i&&yn.isArray(r)?r.length:i,l?(yn.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!a):(r[i]&&yn.isObject(r[i])||(r[i]=[]),t(e,n,r[i],o)&&yn.isArray(r[i])&&(r[i]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let i;for(r=0;r<o;r++)i=n[r],t[i]=e[i];return t}(r[i])),!a)}if(yn.isFormData(e)&&yn.isFunction(e.entries)){const n={};return yn.forEachEntry(e,((e,r)=>{t(function(e){return yn.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null},Vn={transitional:Ln,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=yn.isObject(e);if(o&&yn.isHTMLForm(e)&&(e=new FormData(e)),yn.isFormData(e))return r?JSON.stringify(zn(e)):e;if(yn.isArrayBuffer(e)||yn.isBuffer(e)||yn.isStream(e)||yn.isFile(e)||yn.isBlob(e))return e;if(yn.isArrayBufferView(e))return e.buffer;if(yn.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return jn(e,new Un.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Un.isNode&&yn.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=yn.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return jn(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(yn.isString(e))try{return(0,JSON.parse)(e),yn.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Vn.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(e&&yn.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw wn.from(e,wn.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Un.classes.FormData,Blob:Un.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};yn.forEach(["delete","get","head","post","put","patch"],(e=>{Vn.headers[e]={}}));const qn=Vn,Hn=yn.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Jn=Symbol("internals");function Kn(e){return e&&String(e).trim().toLowerCase()}function Wn(e){return!1===e||null==e?e:yn.isArray(e)?e.map(Wn):String(e)}function Yn(e,t,n,r,o){return yn.isFunction(r)?r.call(this,t,n):(o&&(t=n),yn.isString(t)?yn.isString(r)?-1!==t.indexOf(r):yn.isRegExp(r)?r.test(t):void 0:void 0)}class Gn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=Kn(t);if(!o)throw new Error("header name must be a non-empty string");const i=yn.findKey(r,o);(!i||void 0===r[i]||!0===n||void 0===n&&!1!==r[i])&&(r[i||t]=Wn(e))}const i=(e,t)=>yn.forEach(e,((e,n)=>o(e,n,t)));return yn.isPlainObject(e)||e instanceof this.constructor?i(e,t):yn.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?i((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&Hn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t):null!=e&&o(t,e,n),this}get(e,t){if(e=Kn(e)){const n=yn.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(yn.isFunction(t))return t.call(this,e,n);if(yn.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Kn(e)){const n=yn.findKey(this,e);return!(!n||void 0===this[n]||t&&!Yn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=Kn(e)){const o=yn.findKey(n,e);!o||t&&!Yn(0,n[o],o,t)||(delete n[o],r=!0)}}return yn.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Yn(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return yn.forEach(this,((r,o)=>{const i=yn.findKey(n,o);if(i)return t[i]=Wn(r),void delete t[o];const a=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();a!==o&&delete t[o],t[a]=Wn(r),n[a]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return yn.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&yn.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[Jn]=this[Jn]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Kn(e);t[r]||(function(e,t){const n=yn.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return yn.isArray(e)?e.forEach(r):r(e),this}}Gn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),yn.reduceDescriptors(Gn.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),yn.freezeMethods(Gn);const $n=Gn;function Qn(e,t){const n=this||qn,r=t||n,o=$n.from(r.headers);let i=r.data;return yn.forEach(e,(function(e){i=e.call(n,i,o.normalize(),t?t.status:void 0)})),o.normalize(),i}function Xn(e){return!(!e||!e.__CANCEL__)}function er(e,t,n){wn.call(this,null==e?"canceled":e,wn.ERR_CANCELED,t,n),this.name="CanceledError"}yn.inherits(er,wn,{__CANCEL__:!0});const tr=er,nr=Un.hasStandardBrowserEnv?{write(e,t,n,r,o,i){const a=[e+"="+encodeURIComponent(t)];yn.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),yn.isString(r)&&a.push("path="+r),yn.isString(o)&&a.push("domain="+o),!0===i&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function rr(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const or=Un.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function r(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=r(window.location.href),function(e){const t=yn.isString(e)?r(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0};function ir(e,t){let n=0;const r=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,i=0,a=0;return t=void 0!==t?t:1e3,function(l){const c=Date.now(),u=r[a];o||(o=c),n[i]=l,r[i]=c;let s=a,p=0;for(;s!==i;)p+=n[s++],s%=e;if(i=(i+1)%e,i===a&&(a=(a+1)%e),c-o<t)return;const d=u&&c-u;return d?Math.round(1e3*p/d):void 0}}(50,250);return o=>{const i=o.loaded,a=o.lengthComputable?o.total:void 0,l=i-n,c=r(l);n=i;const u={loaded:i,total:a,progress:a?i/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&i<=a?(a-i)/c:void 0,event:o};u[t?"download":"upload"]=!0,e(u)}}const ar={http:null,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let r=e.data;const o=$n.from(e.headers).normalize();let i,a,{responseType:l,withXSRFToken:c}=e;function u(){e.cancelToken&&e.cancelToken.unsubscribe(i),e.signal&&e.signal.removeEventListener("abort",i)}if(yn.isFormData(r))if(Un.hasStandardBrowserEnv||Un.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if(!1!==(a=o.getContentType())){const[e,...t]=a?a.split(";").map((e=>e.trim())).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...t].join("; "))}let s=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(t+":"+n))}const p=rr(e.baseURL,e.url);function d(){if(!s)return;const r=$n.from("getAllResponseHeaders"in s&&s.getAllResponseHeaders());!function(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new wn("Request failed with status code "+n.status,[wn.ERR_BAD_REQUEST,wn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),u()}),(function(e){n(e),u()}),{data:l&&"text"!==l&&"json"!==l?s.response:s.responseText,status:s.status,statusText:s.statusText,headers:r,config:e,request:s}),s=null}if(s.open(e.method.toUpperCase(),Pn(p,e.params,e.paramsSerializer),!0),s.timeout=e.timeout,"onloadend"in s?s.onloadend=d:s.onreadystatechange=function(){s&&4===s.readyState&&(0!==s.status||s.responseURL&&0===s.responseURL.indexOf("file:"))&&setTimeout(d)},s.onabort=function(){s&&(n(new wn("Request aborted",wn.ECONNABORTED,e,s)),s=null)},s.onerror=function(){n(new wn("Network Error",wn.ERR_NETWORK,e,s)),s=null},s.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const r=e.transitional||Ln;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new wn(t,r.clarifyTimeoutError?wn.ETIMEDOUT:wn.ECONNABORTED,e,s)),s=null},Un.hasStandardBrowserEnv&&(c&&yn.isFunction(c)&&(c=c(e)),c||!1!==c&&or(p))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&nr.read(e.xsrfCookieName);t&&o.set(e.xsrfHeaderName,t)}void 0===r&&o.setContentType(null),"setRequestHeader"in s&&yn.forEach(o.toJSON(),(function(e,t){s.setRequestHeader(t,e)})),yn.isUndefined(e.withCredentials)||(s.withCredentials=!!e.withCredentials),l&&"json"!==l&&(s.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&s.addEventListener("progress",ir(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&s.upload&&s.upload.addEventListener("progress",ir(e.onUploadProgress)),(e.cancelToken||e.signal)&&(i=t=>{s&&(n(!t||t.type?new tr(null,e,s):t),s.abort(),s=null)},e.cancelToken&&e.cancelToken.subscribe(i),e.signal&&(e.signal.aborted?i():e.signal.addEventListener("abort",i)));const f=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(p);f&&-1===Un.protocols.indexOf(f)?n(new wn("Unsupported protocol "+f+":",wn.ERR_BAD_REQUEST,e)):s.send(r||null)}))}};yn.forEach(ar,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const lr=e=>`- ${e}`,cr=e=>yn.isFunction(e)||null===e||!1===e,ur=e=>{e=yn.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let i=0;i<t;i++){let t;if(n=e[i],r=n,!cr(n)&&(r=ar[(t=String(n)).toLowerCase()],void 0===r))throw new wn(`Unknown adapter '${t}'`);if(r)break;o[t||"#"+i]=r}if(!r){const e=Object.entries(o).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(lr).join("\n"):" "+lr(e[0]):"as no adapter specified";throw new wn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function sr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new tr(null,e)}function pr(e){return sr(e),e.headers=$n.from(e.headers),e.data=Qn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ur(e.adapter||qn.adapter)(e).then((function(t){return sr(e),t.data=Qn.call(e,e.transformResponse,t),t.headers=$n.from(t.headers),t}),(function(t){return Xn(t)||(sr(e),t&&t.response&&(t.response.data=Qn.call(e,e.transformResponse,t.response),t.response.headers=$n.from(t.response.headers))),Promise.reject(t)}))}const dr=e=>e instanceof $n?{...e}:e;function fr(e,t){t=t||{};const n={};function r(e,t,n){return yn.isPlainObject(e)&&yn.isPlainObject(t)?yn.merge.call({caseless:n},e,t):yn.isPlainObject(t)?yn.merge({},t):yn.isArray(t)?t.slice():t}function o(e,t,n){return yn.isUndefined(t)?yn.isUndefined(e)?void 0:r(void 0,e,n):r(e,t,n)}function i(e,t){if(!yn.isUndefined(t))return r(void 0,t)}function a(e,t){return yn.isUndefined(t)?yn.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function l(n,o,i){return i in t?r(n,o):i in e?r(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:l,headers:(e,t)=>o(dr(e),dr(t),!0)};return yn.forEach(Object.keys(Object.assign({},e,t)),(function(r){const i=c[r]||o,a=i(e[r],t[r],r);yn.isUndefined(a)&&i!==l||(n[r]=a)})),n}const hr={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{hr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const mr={};hr.transitional=function(e,t,n){function r(e,t){return"[Axios v1.6.8] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,i)=>{if(!1===e)throw new wn(r(o," has been removed"+(t?" in "+t:"")),wn.ERR_DEPRECATED);return t&&!mr[o]&&(mr[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,i)}};const gr={assertOptions:function(e,t,n){if("object"!=typeof e)throw new wn("options must be an object",wn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const i=r[o],a=t[i];if(a){const t=e[i],n=void 0===t||a(t,i,e);if(!0!==n)throw new wn("option "+i+" must be "+n,wn.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new wn("Unknown option "+i,wn.ERR_BAD_OPTION)}},validators:hr},vr=gr.validators;class yr{constructor(e){this.defaults=e,this.interceptors={request:new Rn,response:new Rn}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t;Error.captureStackTrace?Error.captureStackTrace(t={}):t=new Error;const n=t.stack?t.stack.replace(/^.+\n/,""):"";e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=fr(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&gr.assertOptions(n,{silentJSONParsing:vr.transitional(vr.boolean),forcedJSONParsing:vr.transitional(vr.boolean),clarifyTimeoutError:vr.transitional(vr.boolean)},!1),null!=r&&(yn.isFunction(r)?t.paramsSerializer={serialize:r}:gr.assertOptions(r,{encode:vr.function,serialize:vr.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=o&&yn.merge(o.common,o[t.method]);o&&yn.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=$n.concat(i,o);const a=[];let l=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(l=l&&e.synchronous,a.unshift(e.fulfilled,e.rejected))}));const c=[];let u;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let s,p=0;if(!l){const e=[pr.bind(this),void 0];for(e.unshift.apply(e,a),e.push.apply(e,c),s=e.length,u=Promise.resolve(t);p<s;)u=u.then(e[p++],e[p++]);return u}s=a.length;let d=t;for(p=0;p<s;){const e=a[p++],t=a[p++];try{d=e(d)}catch(e){t.call(this,e);break}}try{u=pr.call(this,d)}catch(e){return Promise.reject(e)}for(p=0,s=c.length;p<s;)u=u.then(c[p++],c[p++]);return u}getUri(e){return Pn(rr((e=fr(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}yn.forEach(["delete","get","head","options"],(function(e){yr.prototype[e]=function(t,n){return this.request(fr(n||{},{method:e,url:t,data:(n||{}).data}))}})),yn.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(fr(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}yr.prototype[e]=t(),yr.prototype[e+"Form"]=t(!0)}));const _r=yr;class br{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new tr(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new br((function(t){e=t})),cancel:e}}}const xr=br,wr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(wr).forEach((([e,t])=>{wr[t]=e}));const Er=wr,Or=function e(t){const n=new _r(t),r=It(_r.prototype.request,n);return yn.extend(r,_r.prototype,n,{allOwnKeys:!0}),yn.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(fr(t,n))},r}(qn);Or.Axios=_r,Or.CanceledError=tr,Or.CancelToken=xr,Or.isCancel=Xn,Or.VERSION="1.6.8",Or.toFormData=jn,Or.AxiosError=wn,Or.Cancel=Or.CanceledError,Or.all=function(e){return Promise.all(e)},Or.spread=function(e){return function(t){return e.apply(null,t)}},Or.isAxiosError=function(e){return yn.isObject(e)&&!0===e.isAxiosError},Or.mergeConfig=fr,Or.AxiosHeaders=$n,Or.formToJSON=e=>zn(yn.isHTMLForm(e)?new FormData(e):e),Or.getAdapter=ur,Or.HttpStatusCode=Er,Or.default=Or;const Sr=Or;function kr(e){return kr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kr(e)}function jr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jr(Object(n),!0).forEach((function(t){var r,o,i,a;r=e,o=t,i=n[t],a=function(e,t){if("object"!=kr(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=kr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(o),(o="symbol"==kr(a)?a:a+"")in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Ar=window.mybricks.SPADesigner;function Zr(){var e,r=(0,t.useRef)(null),i=function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),n=window.location.search.substr(1).match(t);return null!=n?unescape(n[2]):null};return n().createElement("div",{className:Mt.main},n().createElement("div",{className:Mt.toolbar},n().createElement("div",{className:Mt.title},"<定制您自己的无代码设计解决方案>"),n().createElement("button",{className:Mt.save,onClick:function(){var e,t,n=null===(e=r.current)||void 0===e?void 0:e.dump(),o=null===(t=r.current)||void 0===t?void 0:t.toJSON(),a={id:i("pageId"),designerJson:JSON.stringify(n),userJson:JSON.stringify(o)};Sr.post("/linkus-sys/sysPageCode/update",a).then((function(e){console.log("保存成功！")}))}},"保存"),n().createElement("button",{className:Mt.save,onClick:function(){var e,t=null===(e=r.current)||void 0===e?void 0:e.toJSON();t&&(localStorage.setItem("--mybricks-toJSON--",JSON.stringify(t)),window.location.href="/02vue-cli/designer-spa/preview.html")}},"预览")),n().createElement(Ar,{ref:r,config:{comLibLoader:function(){return new Promise((function(e){e(["https://assets.mybricks.world/comlibs/mybricks.basic-comlib/1.1.30/2024-04-22_20-58-43/edit.js","https://assets.mybricks.world/comlibs/mybricks.normal-pc/1.5.90/2024-04-29_19-10-37/edit.js","https://assets.mybricks.world/comlibs/mybricks.normal-pc-chart/1.0.22/2024-04-28_16-38-16/edit.js","https://assets.mybricks.world/comlibs/mybricks.pro-pc/1.0.73/2024-04-17_19-50-04/edit.js"])}))},pageContentLoader:function(){return new Promise((function(e){var t=i("pageId");Sr.get("/linkus-sys/sysPageCode/getData?id="+t).then((function(t){var n;console.log("调用成功"),console.log(t);var r=null===(n=t.data)||void 0===n?void 0:n.designerJson;console.log(r),e(r?JSON.parse(r):null)}))}))},geoView:{scenes:{adder:[{type:"normal",title:"页面",inputs:[{id:"open",title:"打开",schema:{type:"any"}}]},{type:"popup",title:"对话框",template:{namespace:"mybricks.basic-comlib.popup",deletable:!1,asRoot:!0}},{type:"popup",title:"抽屉",template:{namespace:"mybricks.basic-comlib.drawer",deletable:!1,asRoot:!0}}]}},toplView:{title:"交互",vars:{},fx:{},cards:{main:{inputs:[{id:"open",title:"打开",schema:{type:"any"}}]}}},com:{env:{i18n:function(e){return e},callConnector:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r.current.getPlugin(e.connectorName).callConnector(Cr(Cr({},e),{},{useProxy:!1}),t,n)}}},plugins:[Tt()({useProxy:!1,isPrivatization:!1}),Pt()(),(e={user:{id:"0",name:"我",email:"<EMAIL>",avatar:"https://my.mybricks.world/icon.png"},onSearchUser:function(e){return new Promise((function(t){console.log("查询内容，@后字符: ",e),t([{id:"0",name:"我",email:"<EMAIL>",thumbnailAvatarUrl:"https://my.mybricks.world/icon.png"},{id:"1",name:"用户1",email:"<EMAIL>",thumbnailAvatarUrl:"https://my.mybricks.world/icon.png"},{id:"2",name:"用户2",email:"<EMAIL>",thumbnailAvatarUrl:"https://my.mybricks.world/icon.png"}])}))}},Ct(Ct(Ct(Ct(Ct(Ct({name:"@mybricks/plugins/note",title:"note",author:"LiangLihao"},"author.zh","梁李昊"),"version","1.0.0"),"description","note"),"data",o),"contributes",{geoView:{note:{type:"default",render:function(t){var r=t.data;return r.operationMap||(r.operationMap={}),n().createElement(kt,At({},t,{appCtx:e}))}}}}),"beforeDump",(function(e){var t=e.data,n=t.notesMap,r=t.operationMap,o=void 0===r?{}:r;Object.keys(n).forEach((function(e){var t=n[e],r={};if(s(t,"object")){var o=n[e],i=o.ats;o.operator,o.innerText,Array.isArray(i)&&(delete n[e].ats,n[e]=[n[e]])}else s(t,"array")&&n[e].forEach((function(e){var t=e.id,n=e.ats;e.operator,e.innerText,r[t]=e,Array.isArray(n)&&delete e.ats}))})),Object.keys(o).forEach((function(e){var t=o[e];Object.keys(t).forEach((function(e){t[e].type})),delete o[e]})),t.id=d()})))]}}))}var Tr=document.getElementById("root");if(!Tr)throw new Error("缺少ID为root的dom节点");(0,r.render)(n().createElement(Zr,null),Tr)})()})();