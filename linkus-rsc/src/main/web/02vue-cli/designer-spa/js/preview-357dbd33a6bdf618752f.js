/*! For license information please see preview-357dbd33a6bdf618752f.js.LICENSE.txt */
(()=>{var __webpack_modules__={25:function(module,__unused_webpack_exports,__webpack_require__){var t;t=(__WEBPACK_EXTERNAL_MODULE__359__,__WEBPACK_EXTERNAL_MODULE__318__)=>(()=>{var __webpack_modules__={21:function(module,__unused_webpack_exports,__nested_webpack_require_482__){var React=__nested_webpack_require_482__(359),t;t=__WEBPACK_EXTERNAL_MODULE__359__=>(()=>{"use strict";var __webpack_modules__={356:(__unused_webpack_module,__nested_webpack_exports__,__nested_webpack_require_370__)=>{function __WEBPACK_DEFAULT_EXPORT__({data,outputs,inputs,onError}){inputs.from(((val,relOutpus)=>{var _a;const script=null===(_a=data.exchange)||void 0===_a?void 0:_a.script;if(script){let fn,returnVal,isOk;try{eval(`fn = ${script}`),returnVal=fn(val),isOk=!0}catch(e){console.error(e),onError(`数据转换错误:${e.message}`,e)}isOk&&outputs.to(returnVal)}else onError("未配置转换规则")}))}__nested_webpack_require_370__.d(__nested_webpack_exports__,{Z:()=>__WEBPACK_DEFAULT_EXPORT__})},359:e=>{e.exports=__WEBPACK_EXTERNAL_MODULE__359__}},__webpack_module_cache__={};function __nested_webpack_require_927__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](n,n.exports,__nested_webpack_require_927__),n.exports}__nested_webpack_require_927__.d=(e,t)=>{for(var n in t)__nested_webpack_require_927__.o(t,n)&&!__nested_webpack_require_927__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__nested_webpack_require_927__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__nested_webpack_require_927__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __nested_webpack_exports__={};return(()=>{__nested_webpack_require_927__.r(__nested_webpack_exports__),__nested_webpack_require_927__.d(__nested_webpack_exports__,{default:()=>f,getCom:()=>m});const e=JSON.parse('{"title":"Fn计算","namespace":"mybricks.core-comlib.fn","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"Fn计算","icon":"./icon.png","runtime":"./Fn.tsx","rtType":"js","visibility":false}'),t=JSON.parse('{"visibility":false,"title":"变量","namespace":"mybricks.core-comlib.var","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"变量","icon":"./icon.png","data":"./data.json","runtime":"./Var.tsx","editors":"./editors.tsx","rtType":"js","inputs":[{"id":"get","title":"读取","schema":{"type":"any"},"rels":["return"]},{"id":"set","title":"赋值","schema":{"type":"follow"},"rels":["return"]},{"id":"reset","title":"重置","schema":{"type":"any"}}],"outputs":[{"id":"return","title":"完成","schema":{"type":"unknown"}},{"id":"changed","title":"当值发生变化","schema":{"type":"unknown"}}]}');function n(e){if(e)try{const t=Object.prototype.toString.call(e);return["[object Object]","[object Array]"].includes(t)?JSON.parse(JSON.stringify(e)):e}catch(t){return e}return e}const o=JSON.parse('{"title":"类型转换","visibility":false,"namespace":"mybricks.core-comlib.type-change","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"类型转换","icon":"./icon.png","runtime":"./rt.tsx","editors":"./editors.tsx","rtType":"js","inputs":[{"id":"from","title":"从","schema":{"type":"follow"},"rels":["to"]}],"outputs":[{"id":"to","title":"到","schema":{"type":"follow"},"conMax":1,"editable":true}]}');var r=__nested_webpack_require_927__(356);const i=JSON.parse('{"deprecated":true,"visibility":false,"title":"连接器","namespace":"mybricks.core-comlib.connector","author":"MyBricks","author_name":"MyBricks","version":"1.0.0","description":"连接器","runtime":"./runtime.ts","editors":"./editors.tsx","icon":"./icon.svg","rtType":"js-autorun","inputs":[{"id":"call","rels":["then","catch"],"title":"调用","schema":{"type":"object"}}],"outputs":[{"id":"then","title":"结果","schema":{"type":"unknown"}},{"id":"catch","title":"发生错误","schema":{"type":"string"}}]}');function s({env:e,data:t,outputs:n,onError:o},r={}){if(t.connector)try{e.callConnector(t.connector,r,t.connectorConfig).then((e=>{n.then(e)})).catch((e=>{n.catch(e)}))}catch(e){console.error(e),n.catch(`执行错误 ${e.message||e}`)}else n.catch("没有选择接口")}const a=JSON.parse('{"visibility":false,"title":"作用域输入","namespace":"mybricks.core-comlib.frame-input","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"作用域输入","rtType":"js","runtime":"./runtime.ts","editors":"./editor.tsx","inputs":[{"id":"getCurValue","title":"获取","schema":{"type":"any"},"rels":["return"]}],"outputs":[{"id":"return","title":"结果","schema":{"type":"follow"}}]}'),c=JSON.parse('{"visibility":false,"title":"打开对话框","namespace":"mybricks.core-comlib.scenes","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"切换场景","icon":"./icon.png","runtime":"./runtime.ts","data":"./data.json","rtType":"js","inputs":[{"id":"open","title":"打开","schema":{"type":"any"}}]}'),l=JSON.parse('{"title":"模块","namespace":"mybricks.core-comlib.module","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"模块","icon":"./icon.png","data":"./data.json","runtime":"./runtime.tsx","editors":"./editors.ts"}');var u=__nested_webpack_require_927__(359);const d=JSON.parse('{"title":"组","namespace":"mybricks.core-comlib.group","author":"CheMingjun","author_name":"车明君","version":"1.0.0","description":"组","runtime":"./runtime.tsx","editors":"./editors.ts","slots":[{"title":"内容","id":"content","layout":"smart"}],"outputs":[{"id":"click","title":"单击","schema":{"type":"number"}},{"id":"dblClick","title":"双击","schema":{"type":"number"}}]}'),p={id:"mybricks-core-comlib",title:"Mybrics核心组件库",author:"CheMingjun",icon:"",version:"1.0.1",comAray:[h({comDef:e,rt:function({inputs:e,outputs:t}){}}),h({comDef:t,rt:function({env:e,data:t,outputs:o,inputs:r,_notifyBindings:i}){r.get(((e,o)=>{const r=n(void 0!==t.val?t.val:t.initValue);o.return(r)})),r.set(((e,r)=>{t.val=e;const s=n(e);o.changed(s,!0),i(s),r.return(s)})),r.reset((()=>{const e=t.initValue;t.val=e;const r=n(e);o.changed(r,!0),i(r)}))}}),h({comDef:o,rt:r.Z}),h({comDef:i,rt:function({env:e,data:t,inputs:n,outputs:o,onError:r}){e.runtime&&(t.immediate?s({env:e,data:t,outputs:o}):n.call((n=>{s({env:e,data:t,outputs:o,onError:r},n)})))}}),h({comDef:a,rt:function({env:e,inputs:t}){e.runtime&&t.getCurValue(((e,t)=>{t.return(e)}))}}),h({comDef:c,rt:function({env:e,data:t,inputs:n,_inputs:o,_inputsCallable:r}){n.open((n=>{e.canvas.open(t._sceneId,n,"popup"===t._sceneShowType?null:t.openType),r._open(n)}))}}),h({comDef:l,rt:function({env:e,data:t,inputs:n,outputs:o}){const[r,i]=(0,u.useState)(),s=(0,u.useMemo)((()=>{const r=e.getModuleJSON(t.definedId);return e.renderModule(r,{ref(s){if(i(s),e.runtime){const{inputs:e,outputs:i}=r,a=t.configs;for(let e in a)s.inputs[e](a[e]);e.forEach((({id:e})=>{n[e]((t=>{s.inputs[e](t)}))})),i.forEach((({id:e})=>{s.outputs(e,o[e])}))}s.run()},disableAutoRun:!0})}),[]);return(0,u.useMemo)((()=>{if(r&&e.edit){const e=t.configs;for(let t in e)r.inputs[t](e[t])}}),[t.configs,r]),s}}),h({comDef:d,rt:function({slots:e,outputs:t}){const n=(0,u.useCallback)((()=>{t.click()}),[]),o=(0,u.useCallback)((()=>{t.dblClick()}),[]);return React.createElement("div",{style:{width:"100%",height:"100%",overflow:"visible"},onClick:n,onDoubleClick:o},e.content.render())}})]},f=p;function m(e){return p.comAray.find((t=>t.namespace===e))}function h({comDef:e,rt:t,data:n}){return Object.assign(e,{runtime:t,data:n})}})(),__nested_webpack_exports__})(),module.exports=t(__nested_webpack_require_482__(359))},998:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(601),r=n.n(o),i=n(609),s=n.n(i)()(r());s.push([e.id,".error-c0f46 {\n  font-size: 12px;\n  color: #f5222d;\n  overflow: hidden;\n  white-space: pre-wrap;\n}\n",""]),s.locals={error:"error-c0f46"};const a=s},942:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(601),r=n.n(o),i=n(609),s=n.n(i)()(r());s.push([e.id,"@keyframes slipInto-fb013 {\n  0% {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  100% {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n.main-a6e42 {\n  animation: slipInto-fb013 0.1s ease-in-out 1;\n}\n",""]),s.locals={main:"main-a6e42",slipInto:"slipInto-fb013"};const a=s},432:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(601),r=n.n(o),i=n(609),s=n.n(i)()(r());s.push([e.id,".container-ff9f5 {\n  position: fixed;\n  right: 20px;\n  top: 20px;\n  display: flex;\n  flex-direction: column;\n  z-index: 9999;\n}\n.container-ff9f5 .itemWrap-a3069 {\n  position: relative;\n}\n.container-ff9f5 .item-c8a43 {\n  padding: 16px;\n  background-color: #fff;\n  margin-bottom: 8px;\n  border-radius: 4px;\n  max-width: 50vw;\n  max-height: 50vh;\n  overflow: auto;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);\n  font-size: 12px;\n}\n.container-ff9f5 .errorItem-e6fc8 {\n  color: #f5222d;\n}\n.container-ff9f5 .closeIcon-c5e91 {\n  position: absolute;\n  right: 16px;\n  top: 16px;\n  width: 24px;\n  height: 24px;\n  padding: 2px;\n  cursor: pointer;\n}\n.container-ff9f5 .closeIcon-c5e91:hover {\n  border-radius: 4px;\n  background-color: rgba(0, 0, 0, 0.12);\n}\n",""]),s.locals={container:"container-ff9f5",itemWrap:"itemWrap-a3069",item:"item-c8a43",errorItem:"errorItem-e6fc8",closeIcon:"closeIcon-c5e91"};const a=s},644:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(601),r=n.n(o),i=n(609),s=n.n(i)()(r());s.push([e.id,"/**\n * MyBricks Opensource\n * https://mybricks.world\n * This source code is licensed under the MIT license.\n *\n * CheMingjun @2019\n * <EMAIL>\n */\n.slot-ced90 {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n.lyFlexColumn-f7fd6 {\n  display: flex;\n  flex-direction: column;\n}\n.lyFlexRow-c8e04 {\n  display: flex;\n  flex-direction: row;\n}\n.justifyContentFlexStart-b95f6 {\n  justify-content: flex-start;\n}\n.justifyContentFlexCenter-e1ca1 {\n  justify-content: center;\n}\n.justifyContentFlexFlexEnd-ef832 {\n  justify-content: flex-end;\n}\n.justifyContentFlexSpaceAround-ad880 {\n  justify-content: space-around;\n}\n.justifyContentFlexSpaceBetween-a2f09 {\n  justify-content: space-between;\n}\n.alignItemsFlexStart-a8880 {\n  align-items: flex-start;\n}\n.alignItemsFlexCenter-c6d62 {\n  align-items: center;\n}\n.alignItemsFlexFlexEnd-d7b4c {\n  align-items: flex-end;\n}\n.debugFocus-def53 {\n  outline: 1px dashed red;\n  outline-offset: -3px;\n}\n.com-f9041 {\n  flex-shrink: 0;\n  min-height: 0;\n}\n.comHeightAuto-f7f16 > div {\n  flex: 1;\n}\n.flex-ca980 {\n  flex: 1;\n  min-height: 0;\n  position: relative;\n}\n.flex-ca980 > div {\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n}\n.flex-ca980 > * {\n  height: 100%;\n}\n.error-d4d00 {\n  font-size: 12px;\n  color: #f5222d;\n  overflow: hidden;\n}\n.errorRT-e9378 {\n  padding: 5px;\n  border: 1px dashed #f5222d;\n}\n.errorRT-e9378 .tt-d634d {\n  font-size: 12px;\n  color: #f5222d;\n  font-weight: bold;\n  margin-bottom: 5px;\n}\n.errorRT-e9378 .info-d57d6 {\n  color: #f5222d;\n  margin-bottom: 5px;\n}\n",""]),s.locals={slot:"slot-ced90",lyFlexColumn:"lyFlexColumn-f7fd6",lyFlexRow:"lyFlexRow-c8e04",justifyContentFlexStart:"justifyContentFlexStart-b95f6",justifyContentFlexCenter:"justifyContentFlexCenter-e1ca1",justifyContentFlexFlexEnd:"justifyContentFlexFlexEnd-ef832",justifyContentFlexSpaceAround:"justifyContentFlexSpaceAround-ad880",justifyContentFlexSpaceBetween:"justifyContentFlexSpaceBetween-a2f09",alignItemsFlexStart:"alignItemsFlexStart-a8880",alignItemsFlexCenter:"alignItemsFlexCenter-c6d62",alignItemsFlexFlexEnd:"alignItemsFlexFlexEnd-d7b4c",debugFocus:"debugFocus-def53",com:"com-f9041",comHeightAuto:"comHeightAuto-f7f16",flex:"flex-ca980",error:"error-d4d00",errorRT:"errorRT-e9378",tt:"tt-d634d",info:"info-d57d6"};const a=s},621:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(601),r=n.n(o),i=n(609),s=n.n(i)()(r());s.push([e.id,".debugger-fed31 {\n  width: 100%;\n  height: 100%;\n  background-color: #1b1b1b73;\n}\n.debugger-fed31 .titlebar-f7fe6 {\n  font-size: 12px;\n  line-height: 23px;\n  position: absolute;\n  top: 10%;\n  height: 23px;\n  padding: 0px 10px;\n  left: 50%;\n  transform: translateX(-50%);\n  display: flex;\n  align-items: center;\n  background-color: #faf8df;\n}\n.debugger-fed31 .titlebar-f7fe6 .resume-ebcb6 {\n  display: flex;\n  padding-left: 4px;\n  cursor: pointer;\n}\n.debugger-fed31 .titlebar-f7fe6 .resume-ebcb6 svg path {\n  fill: #FA6400;\n}\n",""]),s.locals={debugger:"debugger-fed31",titlebar:"titlebar-f7fe6",resume:"resume-ebcb6"};const a=s},609:e=>{"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",o=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),o&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),o&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,o,r,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(o)for(var a=0;a<this.length;a++){var c=this[a][0];null!=c&&(s[c]=!0)}for(var l=0;l<e.length;l++){var u=[].concat(e[l]);o&&s[u[0]]||(void 0!==i&&(void 0===u[5]||(u[1]="@layer".concat(u[5].length>0?" ".concat(u[5]):""," {").concat(u[1],"}")),u[5]=i),n&&(u[2]?(u[1]="@media ".concat(u[2]," {").concat(u[1],"}"),u[2]=n):u[2]=n),r&&(u[4]?(u[1]="@supports (".concat(u[4],") {").concat(u[1],"}"),u[4]=r):u[4]="".concat(r)),t.push(u))}},t}},601:e=>{"use strict";e.exports=function(e){return e[1]}},490:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var o=n(62),r=n.n(o),i=n(36),s=n.n(i),a=n(892),c=n.n(a),l=n(173),u=n.n(l),d=n(464),p=n.n(d),f=n(998),m={};f.Z&&f.Z.locals&&(m.locals=f.Z.locals);var h,g=0,y={};y.styleTagTransform=p(),y.setAttributes=c(),y.insert=function(e,t){(t.target||document.head).appendChild(e)},y.domAPI=s(),y.insertStyleElement=u(),m.use=function(e){return y.options=e||{},g++||(h=r()(f.Z,y)),m},m.unuse=function(){g>0&&! --g&&(h(),h=null)};const v=m},650:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var o=n(62),r=n.n(o),i=n(36),s=n.n(i),a=n(892),c=n.n(a),l=n(173),u=n.n(l),d=n(464),p=n.n(d),f=n(942),m={};f.Z&&f.Z.locals&&(m.locals=f.Z.locals);var h,g=0,y={};y.styleTagTransform=p(),y.setAttributes=c(),y.insert=function(e,t){(t.target||document.head).appendChild(e)},y.domAPI=s(),y.insertStyleElement=u(),m.use=function(e){return y.options=e||{},g++||(h=r()(f.Z,y)),m},m.unuse=function(){g>0&&! --g&&(h(),h=null)};const v=m},759:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var o=n(62),r=n.n(o),i=n(36),s=n.n(i),a=n(892),c=n.n(a),l=n(173),u=n.n(l),d=n(464),p=n.n(d),f=n(432),m={};f.Z&&f.Z.locals&&(m.locals=f.Z.locals);var h,g=0,y={};y.styleTagTransform=p(),y.setAttributes=c(),y.insert=function(e,t){(t.target||document.head).appendChild(e)},y.domAPI=s(),y.insertStyleElement=u(),m.use=function(e){return y.options=e||{},g++||(h=r()(f.Z,y)),m},m.unuse=function(){g>0&&! --g&&(h(),h=null)};const v=m},232:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var o=n(62),r=n.n(o),i=n(36),s=n.n(i),a=n(892),c=n.n(a),l=n(173),u=n.n(l),d=n(464),p=n.n(d),f=n(644),m={};f.Z&&f.Z.locals&&(m.locals=f.Z.locals);var h,g=0,y={};y.styleTagTransform=p(),y.setAttributes=c(),y.insert=function(e,t){(t.target||document.head).appendChild(e)},y.domAPI=s(),y.insertStyleElement=u(),m.use=function(e){return y.options=e||{},g++||(h=r()(f.Z,y)),m},m.unuse=function(){g>0&&! --g&&(h(),h=null)};const v=m},423:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var o=n(62),r=n.n(o),i=n(36),s=n.n(i),a=n(892),c=n.n(a),l=n(173),u=n.n(l),d=n(464),p=n.n(d),f=n(621),m={};f.Z&&f.Z.locals&&(m.locals=f.Z.locals);var h,g=0,y={};y.styleTagTransform=p(),y.setAttributes=c(),y.insert=function(e,t){(t.target||document.head).appendChild(e)},y.domAPI=s(),y.insertStyleElement=u(),m.use=function(e){return y.options=e||{},g++||(h=r()(f.Z,y)),m},m.unuse=function(){g>0&&! --g&&(h(),h=null)};const v=m},62:e=>{"use strict";var t=[];function n(e){for(var n=-1,o=0;o<t.length;o++)if(t[o].identifier===e){n=o;break}return n}function o(e,o){for(var i={},s=[],a=0;a<e.length;a++){var c=e[a],l=o.base?c[0]+o.base:c[0],u=i[l]||0,d="".concat(l," ").concat(u);i[l]=u+1;var p=n(d),f={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)t[p].references++,t[p].updater(f);else{var m=r(f,o);o.byIndex=a,t.splice(a,0,{identifier:d,updater:m,references:1})}s.push(d)}return s}function r(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,r){var i=o(e=e||[],r=r||{});return function(e){e=e||[];for(var s=0;s<i.length;s++){var a=n(i[s]);t[a].references--}for(var c=o(e,r),l=0;l<i.length;l++){var u=n(i[l]);0===t[u].references&&(t[u].updater(),t.splice(u,1))}i=c}}},173:e=>{"use strict";e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},892:(e,t,n)=>{"use strict";e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},36:e=>{"use strict";e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var o="";n.supports&&(o+="@supports (".concat(n.supports,") {")),n.media&&(o+="@media ".concat(n.media," {"));var r=void 0!==n.layer;r&&(o+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),o+=n.css,r&&(o+="}"),n.media&&(o+="}"),n.supports&&(o+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(o,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},464:e=>{"use strict";e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},901:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var o=n(358),r=n(599),i=n(273),s=function(e,t,n,o){return new(n||(n=Promise))((function(r,i){function s(e){try{c(o.next(e))}catch(e){i(e)}}function a(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((o=o.apply(e,t||[])).next())}))};const a="_rootFrame_";function c(e,{observable:t}){const{json:n,getComDef:c,env:l,ref:u,onError:d,logger:p,debug:f,debugLogger:m,_isNestedRender:h,_context:g}=e,y=e.scenesOperate||l.scenesOperate,{slot:v,coms:b={},comsAutoRun:_={},cons:w=[],pinRels:E={},pinProxies:x={},pinValueProxies:S={},type:I}=n,O=l,j={},C={},k={},P={},R={};Object.keys(w).forEach((e=>{const t=w[e],{startPinParentKey:n}=t[0];n&&(R[n]=e)}));const A={},T={},N={},L={},F={},D={};function B(e,t,i){if(!h)if("com"===e){const{com:e,pinHostId:s,val:a,fromCon:c,notifyAll:l,comDef:u,conId:d}=t;m?m("com","output",{id:e.id,pinHostId:s,val:(0,r.Am)(a),fromCon:c,notifyAll:l,comDef:u,sceneId:n.id,conId:d},i):(0,o.J2)(e.title,u,s,a)}else if("frame"===e){const{comId:e,frameId:o,pinHostId:s,val:a,sceneId:c,conId:l}=t;m&&m("frame","output",{comId:e,frameId:o,pinHostId:s,val:(0,r.Am)(a),sceneId:c||n.id,conId:l},i)}}function M(e,t){if(h)return;const{com:i,pinHostId:s,val:a,frameKey:c,finishPinParentKey:l,comDef:u,conId:d}=e;d&&(m?m("com","input",{id:i.id,pinHostId:s,val:(0,r.Am)(a),frameKey:c,finishPinParentKey:l,comDef:u,sceneId:n.id,conId:d},t):(0,o.Wg)(i.title,u,s,a))}function $(e,t,n,o){var i,s,a;const c=x[e.comId+"-"+e.pinId];let l=e;if(c){const s="mybricks.core-comlib.frame-output"===(null===(i=l.def)||void 0===i?void 0:i.namespace);if(s)l=Object.assign(Object.assign({},e),{type:c.type,frameId:c.frameId,pinId:c.pinId,direction:"inner-input",comId:(e.targetFrameKey||e.frameKey).split("-")[0]});else{if(N[`${c.frameId}-${c.pinId}`]=n,o&&o.finishPinParentKey!==l.startPinParentKey)return;if("frame"===c.type){const e=Z(l.comId,t);let o;if(o={id:(0,r.Vj)(10,16),frameId:c.frameId,parent:t,proxyComProps:e},"mybricks.core-comlib.fn"===l.def.namespace){const{configs:t}=e.data;t&&Object.entries(t).forEach((([e,t])=>{const{frameId:n,comId:r,pinId:i}=c,s=w[(r?`${r}-${n}`:`${n}`)+"-"+e];s&&U({logProps:null,cons:s,val:t,curScope:o})}))}return G({options:c,value:n,scope:o,comProps:e}),void(s||V({frameId:c.frameId,scope:o}))}}}if("com"===l.type)o?o.finishPinParentKey===l.startPinParentKey&&J(l,n,t):J(l,n,t);else{if("frame"!==l.type)throw new Error("尚未实现");if(o&&o.finishPinParentKey!==l.startPinParentKey)return;if(l.comId){if("inner-input"===l.direction){const e=C[l.comId+"-"+l.frameId+"-"+((null===(s=null==t?void 0:t.parent)||void 0===s?void 0:s.id)?t.parent.id+"-":"")+l.pinId]||C[l.frameKey+"-"+l.pinId];e&&e(n)}else if("inner-output"===l.direction&&"joint"===l.pinType){const e=w[l.comId+"-"+l.frameId+"-"+l.pinId];e&&U({logProps:null,cons:e,val:n})}}else{const e=null==t?void 0:t.proxyComProps;if(e){const o=e.outputs[l.pinId];if(o)return void o(n,t.parent)}null===(a=P[l.pinId])||void 0===a||a.call(P,n)}}}function U({logProps:e,cons:t,val:n,curScope:o,fromCon:r,notifyAll:c,fromCom:l,isAutoRun:u}){!h&&f&&t.sort(((e,t)=>e.isBreakpoint&&!t.isBreakpoint?-1:!e.isBreakpoint&&t.isBreakpoint?1:0)),t.forEach((t=>s(this,void 0,void 0,(function*(){var s,d;const{comId:p,pinId:m,pinType:y,timerPinInputId:v,frameKey:_}=t,w=b[p];if(r){if(r.finishPinParentKey!==t.startPinParentKey)return}else if(!(0,i.$)(l,t,e))return;if(h||!f||!t.isIgnored){if(!h&&f&&(null===(s=g.debuggerPanel)||void 0===s?void 0:s.hasBreakpoint(t))){let n=!0;yield null===(d=g.debuggerPanel)||void 0===d?void 0:d.wait(t,(()=>{n=!1,e&&(e[1].conId=t.id,e&&B(...e,!0))})),n&&e&&(e[1].conId=t.id,e&&B(...e))}else e&&B(...e);if("timer"===y)q({value:n,curScope:o,inReg:t,notifyAll:c,fromCon:r});else if(c){const e=t.frameKey;if(e===a||u)K({pinId:m,value:n,component:w,curScope:null,comId:p,val:n,timerPinInputId:v,frameKey:e,inReg:t,notifyAll:c,fromCon:r});else{const[i,s]=e.split("-");if(L[e]){const n=L[e],o=n[l.id];o?o[t.id]=t:n[l.id]={[t.id]:t}}else(L[e]={})[l.id]={[t.id]:t};if(l.parentComId)K({pinId:m,value:n,component:w,curScope:o,comId:i,val:n,timerPinInputId:v,frameKey:e,inReg:t,notifyAll:c,fromCon:r});else{const o=j[`${i}-${s}`];if(o){const s=Object.entries(o),a=s.length;s.forEach((([o,s])=>{a>1&&"slot"===o||("scope"===(null==s?void 0:s.type)?s.curScope?K({pinId:m,value:n,component:w,curScope:s.curScope,comId:i,val:n,timerPinInputId:v,frameKey:e,inReg:t,notifyAll:c,fromCon:r}):s.pushTodo((o=>{K({pinId:m,value:n,component:w,curScope:o,comId:i,val:n,timerPinInputId:v,frameKey:e,inReg:t,notifyAll:c,fromCon:r})})):K({pinId:m,value:n,component:w,curScope:s.curScope,comId:i,val:n,timerPinInputId:v,frameKey:e,inReg:t,notifyAll:c,fromCon:r}))}))}}}}else K({pinId:m,value:n,component:w,curScope:o,comId:p,val:n,timerPinInputId:v,frameKey:_,inReg:t,notifyAll:c,fromCon:r})}}))))}function q({pinId:e,value:t,curScope:n,inReg:o,notifyAll:r,fromCon:i}){let s=n;const c=e?Object.assign(Object.assign({},o),{pinId:e}):o;if(r){const e=c.frameKey;if(!e)throw new Error("数据异常，请检查toJSON结果.");$(c,e===a?{}:n,t,i)}else{const e=c.frameKey.split("-");if(e.length>=2&&!s){const n=z(e[0],e[1],null,!1);"scope"===(null==n?void 0:n.type)?n.curScope?$(c,n.curScope,t,i):n.pushTodo((e=>{$(c,e,t,i)})):$(c,s,t,i)}else $(c,s,t,i)}}function K({pinId:e,value:t,component:n,curScope:o,comId:r,val:i,timerPinInputId:s,frameKey:a,inReg:c,notifyAll:l,fromCon:u}){const{isReady:d,isMultipleInput:p,pinId:f,value:m,cb:h}=function({pinId:e,value:t,component:n,curScope:o,comId:r,val:i}){const s=e.indexOf("."),a={pinId:e,value:t,isReady:!0,isMultipleInput:!1,cb:null};if(n&&-1!==s){const t=n.id+""+((null==o?void 0:o.id)?`-${o.id}`:""),{inputs:r}=n,c=e.substring(0,s);a.pinId=c;const l=e.substring(s+1);let u=A[t];u||(u=A[t]={}),u[l]=i;const d=new RegExp(`${c}.`),p=r.filter((e=>!!e.match(d)));Object.keys(u).length===p.length?(a.value=u,a.isMultipleInput=!0,a.cb=()=>{Reflect.deleteProperty(A,t)}):a.isReady=!1}return a}({pinId:e,value:t,component:n,curScope:o,comId:r,val:i});if(d){const e={pinId:p?f:null,value:m,curScope:o,inReg:c,notifyAll:l,fromCon:u};if(s){const t=s+"-"+a+((null==o?void 0:o.id)?`-${o.id}`:""),n=T[t];if(n){const{ready:o,todo:r}=n;if(o){let n=!1;Object.entries(r).forEach((([t,o])=>{t===f?(q(e),n=!0):o()})),n||q(e),null==h||h(),Reflect.deleteProperty(T,t)}else r[f]=()=>{null==h||h(),q(e)}}else T[t]={ready:!1,todo:{[f]:()=>{null==h||h(),q(e)}}}}else null==h||h(),q(e)}}function Z(e,n){var o,r,i;const s=b[e];if(!s)return null;const u=e+(s.frameId||a);let m,g=j[u];g||(g=j[u]={});let v=n;for(!v&&s.parentComId&&s.frameId&&(v=null===(r=null===(o=j[`${s.parentComId}-${s.frameId}`])||void 0===o?void 0:o.slot)||void 0===r?void 0:r.curScope);v;){const t=v.id+"-"+e;if(v.frameId===s.frameId){m=v.id;const e=g[t];if(e)return e;{const e=v.parentComId;if(!e)break;if(e===s.paramId||e===s.parentComId)break}}v=v.parent}const _=(m?m+"-":"")+e,S=g[_];if(S)return S;const I=s.def,O=s.model,C=JSON.parse(JSON.stringify(O.data)),k=JSON.parse(JSON.stringify(O.style));k.__model_style__=!0;const P={},R={},A={},T={},L=function(e){return new Proxy({},{ownKeys:e=>s.inputs,getOwnPropertyDescriptor:e=>({enumerable:!0,configurable:!0}),get:(t,n)=>function(t){if("[object Symbol]"===Object.prototype.toString.call(n))return;const o=null==e?void 0:e.inputs;if(o){const e=o[n];"function"==typeof e&&e(t)}P[n]=t;const r=R[n];r&&(r.forEach((({val:e,fromCon:n,fromScope:o})=>{t(e,new Proxy({},{get:(e,t)=>function(e){if("[object Symbol]"===Object.prototype.toString.call(t))return;const r=q()[t];if("function"!=typeof r)throw new Error(`outputs.${t} not found`);r(e,o||v,n)}}))})),R[n]=void 0)}})},M=new Proxy({},{get:(t,o)=>function(t){if("[object Symbol]"===Object.prototype.toString.call(o))return;const r=E[e+"-"+o];if(r){const i={},s={};return r.forEach((e=>{i[e]=t=>{s[e]=t}})),Promise.resolve().then((()=>{J({comId:e,def:I,pinId:o},t,n,s)})),i}J({comId:e,def:I,pinId:o},t,n)}}),$=new Proxy({},{get:(t,n)=>function(t){if("[object Symbol]"===Object.prototype.toString.call(n))return;const o=x[e+"-"+n];o&&(null==y||y.inputs(Object.assign(Object.assign({},o),{value:t,parentScope:z})))}}),q=function(t){return new Proxy({},{ownKeys:e=>s.outputs,getOwnPropertyDescriptor:e=>({enumerable:!0,configurable:!0}),get(o,r,i){const a=function(o,i,a,u){if("[object Symbol]"===Object.prototype.toString.call(r))return;const d="boolean"==typeof i&&i;if(d){if(s.parentComId){const t=`${s.parentComId}-${s.frameId}`;D[t]?D[t][e]=!0:D[t]={[e]:!0}}if(F[`${s.id}${(null==n?void 0:n.id)?`-${n.id}`:""}`]=o,s.global&&!u)return void(null==y||y.exeGlobalCom({com:s,value:o,pinId:r}))}const p=arguments,f=null==t?void 0:t.outputs;if(f){const e=f[r];"function"==typeof e&&e(o)}let m;i&&"object"==typeof i&&(m=i);const h=c(I);if(!h)return;const g=O.outputEvents;let v;if(g){const t=g[r];if(t&&Array.isArray(t)){const n=t.find((e=>e.active));if(n){const{type:t}=n;switch(t){case"none":v=[];break;case"fx":const i=x[e+"-"+r];if("frame"===(null==i?void 0:i.type)){const e=`${i.frameId}-${i.pinId}`;v=w[e]||[],N[e]=o}else v=[];break;case"defined":break;default:if(v=[],Array.isArray(null==l?void 0:l.events)){const e=l.events.find((e=>{if(e.type===t)return e}));e&&"function"==typeof e.exe&&e.exe({options:n.options})}}}}}v=v||w[e+"-"+r],(null==v?void 0:v.length)?U(p.length>=3&&void 0===u?{logProps:["com",{com:s,pinHostId:r,val:o,fromCon:a,notifyAll:d,comDef:h}],cons:v,val:o,curScope:m,fromCon:a,fromCom:s}:{logProps:["com",{com:s,pinHostId:r,val:o,fromCon:a,notifyAll:d,comDef:h}],cons:v,val:o,curScope:m||n,fromCon:a,notifyAll:d,fromCom:s}):B("com",{com:s,pinHostId:r,val:o,fromCon:a,notifyAll:d,comDef:h})};return a.getConnections=()=>w[e+"-"+r]||[],a}})},K=new Proxy({},{get:(e,t,n)=>function(e){if("[object Symbol]"===Object.prototype.toString.call(t))return;A[t]=e;const n=T[t];n&&(n.forEach((t=>{e(t)})),T[t]=void 0)}}),W=new Proxy({},{get:(t,o,r)=>function(t){if("[object Symbol]"===Object.prototype.toString.call(o))return;const r=w[e+"-"+o];r?U({logProps:["com",{com:s,pinHostId:o,val:t,comDef:I}],cons:r,val:t,curScope:n}):B("com",{com:s,pinHostId:o,val:t,comDef:I})}}),H=null===(i=I.rtType)||void 0===i?void 0:i.match(/^js/gi),z={id:s.id,title:s.title,frameId:s.frameId,parentComId:s.parentComId,data:H?C:t(C),style:H?k:t(k),_inputRegs:P,addInputTodo:(e,t,n,o)=>{let r=R[e];r||(R[e]=r=[]),r.push({val:t,fromCon:n,fromScope:o})},inputs:L(),inputsCallable:M,_inputsCallable:$,outputs:q(),_inputs:K,_outputs:W,clone(e){const t={inputs:L(e),outputs:q(e)};return Object.setPrototypeOf(t,this),t},_notifyBindings:function(e){if(s.global)return void(null==y||y._notifyBindings(e,s));const{bindingsTo:t}=s.model;if(t)for(let n in t){const o=Z(n);if(o){const r=t[n];Array.isArray(r)&&r.forEach((t=>{let n=o;const r=t.split(".");r.forEach(((t,o)=>{o!==r.length-1?n=n[t]:n[t]=e}))}))}}},logger:p,onError:!h&&f?t=>d({comId:e,error:t,title:s.title}):d};return g[_]=z,z}function W(e,t){let n=N[`${e}${t?`-${t.id}-${t.frameId}`:""}`];return void 0===n&&(null==t?void 0:t.parent)&&(n=W(e,t.parent)),(0,r.bV)(n)}function J(e,t,o,r){var i;const{comId:s,def:l,pinId:u,pinType:m,frameKey:g,finishPinParentKey:v,timerPinInputId:_,targetFrameKey:x}=e;if("ext"===m){const n=j[s]||Z(s,o);if("show"===u)n.style.display="";else if("hide"===u)n.style.display="none";else if("showOrHide"===u){const e=n.style;void 0===t?"none"===e.display?e.display="":e.display="none":e.display=t?"":"none"}const r=c(l);if(!r)return;M({com:n,val:t,pinHostId:u,frameKey:g,finishPinParentKey:v,comDef:r,conId:e.id})}else if("config"===m){const n=Z(s,o),r=c(l);if(!r)return;M({com:n,pinHostId:u,val:t,frameKey:g,finishPinParentKey:v,comDef:r,conId:e.id});const{extBinding:i}=e,a=i.split(".");let d=n;a.forEach(((e,n)=>{n!==a.length-1?d=d[e]:d[e]=t}))}else if("timer"===m){const n=Z(s,o),r=c(l);if(!r)return;M({com:n,pinHostId:u,val:t,frameKey:g,finishPinParentKey:v,comDef:r,conId:e.id});const i=_+"-"+g+((null==o?void 0:o.id)?`-${o.id}`:""),a=T[i];if(a){const{todo:e}=a;Object.entries(e).forEach((([e,t])=>t())),Reflect.deleteProperty(T,i)}else T[i]={ready:!0,todo:{}}}else if(null===(i=l.rtType)||void 0===i?void 0:i.match(/^js/gi)){const r=b[s];if(r){const i=Z(s,o),m=c(l);if(!m)return;if(r.global){const e=null==y?void 0:y.getGlobalComProps(s);e&&(i.data=e.data)}const b=null==o?void 0:o.id,_=(b?b+"-":"")+s;r.inputs.find((e=>e===u))?M({com:r,val:t,pinHostId:u,frameKey:g,finishPinParentKey:v,comDef:m,conId:e.id}):Object.entries(t).forEach((([t,n])=>{M({com:r,val:n,pinHostId:`${u}.${t}`,frameKey:g,finishPinParentKey:v,comDef:m,conId:e.id})})),k[_]||(k[_]=!0,m.runtime({env:O,data:i.data,inputs:i.inputs,outputs:i.outputs,_notifyBindings:i._notifyBindings,_inputsCallable:i._inputsCallable,logger:p,onError:!h&&f?e=>d({comId:s,error:e,title:r.title}):d}));const w=i._inputRegs[u];"function"==typeof w&&w(t,new Proxy({},{get:(t,r)=>function(t){if("[object Symbol]"!==Object.prototype.toString.call(r)){if(S){const e=S[`${s}-${u}`];if(e){const r=`${e.frameId===n.id?a:x||g}-${e.pinId}`;void 0===(t=W(r,o))&&(t=W(r,null))}}i.outputs[r](t,o,e)}}}))}}else{const n=Z(s,o);if(!n)return;const i=c(l);if(!i)return;M({com:n,pinHostId:u,val:t,frameKey:g,finishPinParentKey:v,comDef:i,conId:e.id});const a=n._inputRegs[u];if("function"==typeof a){let i;i=r||new Proxy({},{get:(t,r)=>function(t){"[object Symbol]"!==Object.prototype.toString.call(r)&&n.outputs[r](t,o,e)}}),a(t,i)}else n.addInputTodo(u,t,e,o)}if(v){const e=w[R[v]];e&&!E[`${s}-${u}`]&&U({logProps:null,cons:e,val:void 0})}}function H(e,t){let n;return(null==e?void 0:e.comAry)&&e.comAry.find((e=>{if(e.id===t)return n=e,e;if(e.slots)for(let o in e.slots)if(n=H(e.slots[o],t),n)return n})),n}function z(e,t,n,o){const r=`${e}-${t}`;let i=j[r];i||(i=j[r]={});let s=n?n.id:"slot",a=i[s];if(o&&!a&&console.log("不应该再走到这儿了: ",{comId:e,slotId:t,scope:n,notifyAll:o}),!a){const o=H(v,e);if(!(null==o?void 0:o.slots))return null;const c=null==o?void 0:o.slots[t],l={};let u;if(n){const n=j[e+"-"+t];n&&(u=n.todo)}const d={scope:n,todo:u},p=new Proxy({},{get:(e,t)=>function(e){"[object Symbol]"!==Object.prototype.toString.call(t)&&(l[t]=e)}}),f=new Proxy({},{get(n,o){const r=function(n,r){if("[object Symbol]"===Object.prototype.toString.call(o))return;const i=e+"-"+t+"-"+o,s=w[i];N[`${i}${r?`-${r.id}-${r.frameId}`:""}`]=n,s?U({logProps:["frame",{comId:e,frameId:t,pinHostId:o,val:n}],cons:s,val:n,curScope:r||d.scope}):B("frame",{comId:e,frameId:t,pinHostId:o,val:n})};return r.getConnections=()=>w[e+"-"+t+"-"+o]||[],r}}),m=new Proxy({},{get:(o,i,a)=>function(o){var a;"[object Symbol]"!==Object.prototype.toString.call(i)&&(C[`${e}-${t}-${null===(a=null==n?void 0:n.parent)||void 0===a?void 0:a.id}-${i}`]=o,C[s+"-"+i]=o,C[r+"-"+i]=o)}});let h={};a=i[s]={type:null==c?void 0:c.type,run(n){let o=d.scope;n&&o!==n&&(d.scope=n,o=n);const r=(null==o?void 0:o.id)||"none";if(h[r]||(h[r]=!0,V({comId:e,frameId:t,scope:o})),Array.isArray(d.todo)&&(d.todo.forEach((e=>{Promise.resolve().then((()=>{e(o)}))})),d.todo=void 0),o&&"slot"!==s){const n=L[`${e}-${t}`];Promise.resolve().then((()=>{n&&Object.entries(n).forEach((([e,t])=>{if(!b[e].parentComId){const n=Object.entries(t).map((([e,t])=>t));n.length&&U({logProps:null,cons:n,val:F[e],curScope:o,notifyAll:!0,fromCom:b[e],isAutoRun:!0})}}))}))}},destroy(){if(n){const e=`${n.parentComId}-${n.frameId}`,t=D[e];t&&Object.keys(t).forEach((e=>{Reflect.deleteProperty(F,`${e}-${n.id}`)}))}Reflect.deleteProperty(i,s)},_inputs:p,_inputRegs:l,inputs:f,outputs:m,get curScope(){return d.scope},setCurScope(e){d.scope=e},get todo(){return d.todo},pushTodo(e){d.todo||(d.todo=[]),d.todo.push(e)}}}return a}function V(e){const{comId:t,frameId:n,scope:o}=e,r=_[t?`${t}-${n}`:`${n}`];r&&r.forEach((e=>{const{id:t,def:n}=e,r=b[t];if(r){const e=Z(t,o),i=c(n);if(!i)return;i.runtime({env:O,data:e.data,inputs:e.inputs,outputs:e.outputs,_inputsCallable:e._inputsCallable,logger:p,onError:!h&&f?e=>d({comId:t,error:e,title:r.title}):d})}}))}function G({options:e,value:t,scope:o,log:r=!0,comProps:i}){const{frameId:s,comId:c,pinId:l,sceneId:u}=e,d=w[(c?`${c}-${s}`:`${s}`)+"-"+l];N[`${s}-${l}`]=t,d?U({logProps:["frame",{comId:c,frameId:s,pinHostId:l,val:t,sceneId:u}],cons:d,val:t,curScope:o}):(r&&B("frame",{comId:c,frameId:s,pinHostId:l,val:t,sceneId:u}),s!==a&&(n.id===s?P[l](t):null==y||y.open({frameId:s,todo:{pinId:l,value:t},comProps:i,parentScope:o.proxyComProps})))}const X={get({comId:e,slotId:t,scope:n,_ioProxy:o}){let r;if(o&&(o.inputs||o.outputs||o._inputs||o._outputs)&&(r=o),t)return z(e,t,n);{const t=Z(e,n);return r?t.clone(r):t}},getComInfo:e=>b[e]};return"function"==typeof u&&u({style:null==v?void 0:v.style,run(){V({frameId:a})},inputs:new Proxy({},{get:(e,t)=>function(e,n=void 0,o=!0){"[object Symbol]"!==Object.prototype.toString.call(t)&&G({options:{frameId:a,pinId:t,sceneId:n},value:e,scope:void 0,log:o})}}),outputs(e,t){P[e]=t},get:X.get,getComInfo:X.getComInfo}),X}},273:(e,t,n)=>{"use strict";function o(e,t,n){var o,r;const i=null===(o=null==e?void 0:e.def)||void 0===o?void 0:o.namespace,s=null===(r=null==n?void 0:n[1])||void 0===r?void 0:r.pinHostId;return!(i&&s&&("test.text"===i&&"testOut"===s||"fangzhou.normal-pc.table-blocks"===i&&"rowSelection"===s)&&t.startPinParentKey)}n.d(t,{$:()=>o})},358:(e,t,n)=>{"use strict";n.d(t,{J2:()=>s,JE:()=>r,Wg:()=>i});let o=!1;function r(){o=!0}function i(e,t,n,r){if(o)return;let i;try{i=JSON.stringify(r)}catch(e){i=r}console.log(`%c [MyBricks] 输入项 %c ${e||t.title||t.namespace} | ${n} -> ${i}`,"color:#FFF;background:#000","","")}function s(e,t,n,r){if(o)return;let i;try{i=JSON.stringify(r)}catch(e){i=r}console.log(`%c [MyBricks] 输出项 %c ${e||t.title||t.namespace} | ${n} -> ${i}`,"color:#FFF;background:#fa6400","","")}},599:(e,t,n)=>{"use strict";function o(e){return e&&"object"==typeof e}function r(e){return"number"==typeof e&&!isNaN(e)}function i(e,t){const n=e.split("."),o=t.split("."),r=n.length,i=o.length,s=Math.min(r,i);let a=0;for(;a<s;a++){let e=parseInt(n[a]),t=parseInt(o[a]);if(e>t)return 1;if(e<t)return-1}if(r>i){for(let e=a;e<r;e++)if(0!=parseInt(n[e]))return 1;return 0}if(r<i){for(let e=a;e<i;e++)if(0!=parseInt(o[e]))return-1;return 0}return 0}function s(e=5,t=8){const n="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),o=[];if(t=t||n.length,e)for(let r=0;r<e;r++)o[r]=n[0|Math.random()*t];else{let e;o[8]=o[13]=o[18]=o[23]="-",o[14]="4";for(let t=0;t<36;t++)o[t]||(e=0|16*Math.random(),o[t]=n[19==t?3&e|8:e])}return o.join("")}function a(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}n.d(t,{Am:()=>m,Kn:()=>o,Lc:()=>a,Ok:()=>f,Q1:()=>p,Vj:()=>s,bV:()=>y,hj:()=>r,o4:()=>g,p$:()=>v,yC:()=>i});const c=function(e,t,n,o){return function(r,i){var s,a,c,l,u=parseFloat(i);return!i||u<=t?r:"".concat((s=u/e,a=n,c=Math.pow(10,a+1),l=Math.floor(s*c),10*Math.round(l/10)/c)).concat(o)}},l=c(12,0,5,"rem"),u=c(3.75,0,5,"vw"),d=/"[^"]+"|'[^']+'|url\([^)]+\)|(\d*\.?\d+)px/g,p=e=>e.replace(d,l),f=e=>e.replace(d,u);function m(e){return e}let h;function g(){var e;return h||(h=(null===(e=document.getElementById("_mybricks-geo-webview_"))||void 0===e?void 0:e.shadowRoot)||document.head)}function y(e){if(e&&"object"==typeof e)try{return e instanceof FormData?e:JSON.parse(JSON.stringify(e))}catch(t){return e}return e}function v(e,t=[]){const n=Object.prototype.toString.call(e);if(null===e||"object"!=typeof e||n.startsWith("[object HTML"))return e;const o=t.filter((t=>t.original===e))[0];if(o)return o.copy;const r=Array.isArray(e)?[]:{};return t.push({original:e,copy:r}),Object.getOwnPropertyNames(e).forEach((n=>{const o=Object.getOwnPropertyDescriptor(e,n);o&&"function"==typeof o.get?Object.defineProperty(r,n,{get:o.get,enumerable:o.enumerable,configurable:o.configurable}):r[n]=v(e[n],t)})),r}},987:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(359),r=n.n(o),i=n(878);const s=n(490).Z.locals;class a extends r().PureComponent{constructor(){super(...arguments),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){var t;return{hasError:!0,error:(null==e?void 0:e.stack)||(null==e?void 0:e.message)||(null===(t=null==e?void 0:e.toString)||void 0===t?void 0:t.call(e))}}componentDidCatch(e,t){var n,o,r,s;null===(o=null===(n=this.props.options)||void 0===n?void 0:n.errorHandler)||void 0===o||o.call(n,e,t),i.Z.error(e),this.setState({error:(null==e?void 0:e.stack)||(null==e?void 0:e.message)||(null===(r=null==e?void 0:e.toString)||void 0===r?void 0:r.call(e)),errorInfo:(null==t?void 0:t.stack)||(null==t?void 0:t.message)||(null===(s=null==t?void 0:t.toString)||void 0===s?void 0:s.call(t))})}render(){const{hasError:e,error:t,errorInfo:n}=this.state,{children:o,errorTip:i}=this.props;return e?r().createElement("div",{className:s.error},r().createElement("div",null,i||"渲染错误"),r().createElement("div",null,t||n)):o}}},576:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var o=n(359),r=n.n(o),i=n(130),s=n(901),a=n(606),c=n(878),l=n(987);function u({json:e,options:t,style:n={},className:u="",root:d=!0,from:p}){const f=(0,i.Mk)(),{env:m,onError:h,logger:g,slot:y,getComDef:v}=(0,o.useMemo)((()=>{const{env:o,debug:r}=t,i=e.slot;return!o.canvas.isValid&&!t._isNestedRender&&r&&"scene"===p&&i&&(n.minHeight=i.style.height),"module"===e.type&&(i.style.backgroundColor=i.style.backgroundColor||"#ffffff00"),{env:o,onError:f.onError,logger:f.logger,getComDef:e=>f.getComDef(e),slot:i,_context:f}}),[]),[b,_,w]=(0,o.useMemo)((()=>{var n;try{let n,o=!0;return[(0,s.Z)({json:e,getComDef:v,events:t.events,env:m,ref(e){n=e,"function"==typeof t.ref&&(t.ref(e),o=!1)},onError:h,debug:t.debug,debugLogger:t.debugLogger,logger:g,scenesOperate:t.scenesOperate,_isNestedRender:t._isNestedRender,_context:f},{observable:f.observable}),n,o]}catch(e){throw console.error(e),c.Z.error(`导出的JSON.script执行异常: ${(null==e?void 0:e.stack)||(null==e?void 0:e.message)||(null===(n=null==e?void 0:e.toString)||void 0===n?void 0:n.call(e))}`),new Error("导出的JSON.script执行异常.")}}),[]);return(0,o.useLayoutEffect)((()=>{if(!t.disableAutoRun){if(w){const{inputs:n}=_,o=e.inputs;n&&Array.isArray(o)&&o.forEach((e=>{const{id:o,mockData:r}=e;let i;if(t.debug&&void 0!==r)try{i=JSON.parse(decodeURIComponent(r))}catch(e){i=r}n[o](i)}))}_.run()}}),[]),r().createElement(l.Z,{errorTip:"页面渲染错误",options:t},r().createElement(a.Z,{env:m,style:n,_env:t._env,slot:y,getComDef:v,context:b,className:u,createPortal:t.createPortal||(()=>{}),onError:h,logger:g,root:d,options:t}))}},704:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var o=n(359),r=n.n(o),i=n(576),s=n(130),a=n(901),c=n(650),l=function(e,t,n,o){return new(n||(n=Promise))((function(r,i){function s(e){try{c(o.next(e))}catch(e){i(e)}}function a(e){try{c(o.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}c((o=o.apply(e,t||[])).next())}))};const u=c.Z.locals;function d({json:e,options:t}){const n=(0,s.Mk)(),[i,c]=(0,o.useState)(0),[d,m]=(0,o.useState)([]),[h,g]=(0,o.useState)([]),{scenesMap:y,scenesOperateInputsTodo:v,themes:b,permissions:_,globalVarMap:w}=(0,o.useMemo)((()=>{if(t.sceneId){const n=e.scenes.findIndex((e=>e.id===t.sceneId));if(-1!==n){const t=e.scenes.splice(n,1);e.scenes.unshift(...t),"popup"===t[0].type&&m([t[0].id])}}const n=[];e.scenes.forEach(((e,t)=>{"popup"===e.type?t||m([e.id]):n.push(e)})),g(n);const{modules:o,definedComs:r}=e;return t.env.getDefinedComJSON||(t.env.getDefinedComJSON=e=>r[e].json),t.env.getModuleJSON=e=>{var t;return null===(t=null==o?void 0:o[e])||void 0===t?void 0:t.json},{scenesMap:e.scenes.reduce(((e,n,o)=>{var r;return Object.assign(Object.assign({},e),{[n.id]:{show:0===o,todo:[],json:n,disableAutoRun:!(!t.disableAutoRun&&!o),useEntryAnimation:!1,type:(null===(r=n.slot)||void 0===r?void 0:r.showType)||n.type}})}),{}),scenesOperateInputsTodo:{},themes:e.themes,permissions:e.permissions||[],globalVarMap:{}}}),[]);(0,o.useMemo)((()=>{if(t.ref){const e=t.ref;t.ref=n=>o=>(n(o),e.call(t,o))}else t.ref=e=>t=>{e(t)}}),[]);const{fxToJsonMap:E,currentFxFrameIdsMap:x}=(0,o.useMemo)((()=>{const t={},{global:n}=e;if(n){const{fxFrames:e}=n;Array.isArray(e)&&e.forEach((e=>{t[e.id]=e}))}return{fxToJsonMap:t,currentFxFrameIdsMap:{}}}),[]),S=(0,o.useCallback)((o=>{const r=y[o],i=t.env.hasPermission,{env:s}=t;s.themes=b,s.permissions=_,"function"==typeof i&&Object.defineProperty(s,"hasPermission",{get:function(){return e=>{if("string"==typeof e){const t=_.find((t=>t.id===e));return i({permission:t})}return i(e)}}}),s.canvas.open=(e,n,o)=>l(this,void 0,void 0,(function*(){var n,r;let i=y[e];if(!i){if("function"!=typeof t.scenesLoader)return void console.error(`缺少场景信息: ${e}`);const o=yield t.scenesLoader({id:e});if(i={disableAutoRun:!1,json:o,show:!1,parentScope:null,todo:[],type:(null===(n=o.slot)||void 0===n?void 0:n.showType)||o.type,useEntryAnimation:!1},y[e]=i,"popup"===o.type||g((e=>[...e,o])),v[e]){const{parentScope:t,todo:n}=v[e];i.parentScope=t,n.forEach((({value:e,pinId:t,parentScope:n})=>{i.todo=i.todo.concat({type:"inputs",todo:{pinId:t,value:e}})}))}}null===(r=t.scenesLoaded)||void 0===r||r.call(t,i.json),o?Object.entries(y).forEach((([n,r])=>{n===e?("blank"===o&&"redirect"!==t.sceneOpenType?r.useEntryAnimation=!0:r.useEntryAnimation=!1,r.show=!0,"popup"===r.type?m((t=>[...t,e])):c((e=>e+1))):(r.show=!1,r._refs=null,"popup"===r.type?m((e=>e.filter((e=>e!==r.json.id)))):c((e=>e+1)))})):i.show||("blank"===o&&"redirect"!==t.sceneOpenType?i.useEntryAnimation=!0:i.useEntryAnimation=!1,i.show=!0,"popup"===i.type?m((t=>[...t,e])):c((e=>e+1)))}));const u={open({todo:o,frameId:r,parentScope:i,comProps:s}){var l;const u=y[r];if(u)u.show||(u.show=!0,u.todo=u.todo.concat({type:"inputs",todo:o}),u.parentScope=i,"popup"===u.type?m((e=>[...e,r])):c((e=>e+1)));else{const c=E[r];if(c)if(x[i.id]){const{_refs:e}=x[i.id];x[i.id].parentScope=i;const t=null===(l=null==s?void 0:s.data)||void 0===l?void 0:l.configs;t&&Object.entries(t).forEach((([t,n])=>{e.inputs[t](n,void 0,!1)})),e.inputs[o.pinId](o.value,void 0,!1),e.run()}else{x[i.id]={};const{id:r}=c,{env:l}=t,u={open({todo:o,frameId:r,parentScope:i,comProps:s}){var c;const l=E[r];if(l)if(x[i.id]){const{_refs:e}=x[i.id];x[i.id].parentScope=i;const t=null===(c=null==s?void 0:s.data)||void 0===c?void 0:c.configs;t&&Object.entries(t).forEach((([t,n])=>{e.inputs[t](n,void 0,!1)})),e.inputs[o.pinId](o.value,void 0,!1),e.run()}else{x[i.id]={};const{env:r}=t,c={open({todo:e,frameId:t,parentScope:n,comProps:o}){var r;const i=x[n.id];if(null==i?void 0:i._refs){x[n.id].parentScope=n;const t=null===(r=null==o?void 0:o.data)||void 0===r?void 0:r.configs;t&&Object.entries(t).forEach((([e,t])=>{i._refs.inputs[e](t,void 0,!1)})),i._refs.inputs[e.pinId](e.value,void 0,!1),i._refs.run()}},inputs({frameId:e,parentScope:t,value:n,pinId:o}){const r=y[e];r?(r.parentScope=t,r._refs?r._refs.inputs[o](n):r.todo=r.todo.concat({type:"inputs",todo:{pinId:o,value:n}})):v[e]?v[e].todo.push({frameId:e,parentScope:t,value:n,pinId:o}):v[e]={parentScope:t,todo:[{value:n,pinId:o}]}},_notifyBindings(e,t){const{bindingsTo:n}=t.model;if(n)for(let t in n)for(let o in y){const r=y[o];if(r.json.coms[t])if(r._refs)f(r._refs,t,n[t],e);else{const o=n[t];r.todo=r.todo.concat({type:"globalVar",todo:{comId:t,bindings:o,value:e}})}}},getGlobalComProps(t){var n;return(null===(n=y[e.scenes[0].id]._refs)||void 0===n?void 0:n.get({comId:t}))||{data:{val:w[t]}}},exeGlobalCom({com:e,value:t,pinId:n}){const o=e.id;w[o]=t,Object.keys(y).forEach((e=>{const r=y[e];if(r.show&&r._refs){const e=r._refs.get({comId:o});e&&e.outputs[n](t,!0,null,!0)}}))}};(0,a.Z)({json:l,getComDef:e=>n.getComDef(e),events:t.events,env:r,ref(e){var t;x[i.id]._refs=e,x[i.id].parentScope=i;const{inputs:n,outputs:r}=e;l.outputs.forEach((e=>{r(e.id,(t=>{var n;null===(n=x[i.id].parentScope)||void 0===n||n.outputs[e.id](t)}))}));const a=null===(t=null==s?void 0:s.data)||void 0===t?void 0:t.configs;a&&Object.entries(a).forEach((([t,n])=>{e.inputs[t](n,void 0,!1)})),e.inputs[o.pinId](o.value,void 0,!1),e.run()},onError:n.onError,debug:t.debug,debugLogger:t.debugLogger,logger:n.logger,scenesOperate:c,_context:n},{observable:n.observable})}},inputs({frameId:e,parentScope:t,value:n,pinId:o}){const r=y[e];r?(r.parentScope=t,r._refs?r._refs.inputs[o](n):r.todo=r.todo.concat({type:"inputs",todo:{pinId:o,value:n}})):v[e]?v[e].todo.push({frameId:e,parentScope:t,value:n,pinId:o}):v[e]={parentScope:t,todo:[{value:n,pinId:o}]}},_notifyBindings(e,t){const{bindingsTo:n}=t.model;if(n)for(let t in n)for(let o in y){const r=y[o];if(r.json.coms[t])if(r._refs)f(r._refs,t,n[t],e);else{const o=n[t];r.todo=r.todo.concat({type:"globalVar",todo:{comId:t,bindings:o,value:e}})}}},getGlobalComProps(t){var n;return(null===(n=y[e.scenes[0].id]._refs)||void 0===n?void 0:n.get({comId:t}))||{data:{val:w[t]}}},exeGlobalCom({com:e,value:t,pinId:n}){const o=e.id;w[o]=t,Object.keys(y).forEach((e=>{const r=y[e];if(r.show&&r._refs){const e=r._refs.get({comId:o});e&&e.outputs[n](t,!0,null,!0)}}))}};(0,a.Z)({json:c,getComDef:e=>n.getComDef(e),events:t.events,env:l,ref(e){var t;x[i.id]._refs=e,x[i.id].parentScope=i;const{inputs:n,outputs:r}=e;c.outputs.forEach((e=>{r(e.id,(t=>{var n;null===(n=x[i.id].parentScope)||void 0===n||n.outputs[e.id](t)}))}));const a=null===(t=null==s?void 0:s.data)||void 0===t?void 0:t.configs;a&&Object.entries(a).forEach((([t,n])=>{e.inputs[t](n,void 0,!1)})),e.inputs[o.pinId](o.value,void 0,!1),e.run()},onError:n.onError,debug:t.debug,debugLogger:t.debugLogger,logger:n.logger,scenesOperate:u,_context:n},{observable:n.observable})}}},inputs({frameId:e,parentScope:t,value:n,pinId:o}){const r=y[e];r?(r.parentScope=t,r._refs?r._refs.inputs[o](n):r.todo=r.todo.concat({type:"inputs",todo:{pinId:o,value:n}})):v[e]?v[e].todo.push({frameId:e,parentScope:t,value:n,pinId:o}):v[e]={parentScope:t,todo:[{value:n,pinId:o}]}},_notifyBindings(e,t){const{bindingsTo:n}=t.model;if(n)for(let t in n)for(let o in y){const r=y[o];if(r.json.coms[t])if(r._refs)f(r._refs,t,n[t],e);else{const o=n[t];r.todo=r.todo.concat({type:"globalVar",todo:{comId:t,bindings:o,value:e}})}}},getGlobalComProps(t){var n;return(null===(n=y[e.scenes[0].id]._refs)||void 0===n?void 0:n.get({comId:t}))||{data:{val:w[t]}}},exeGlobalCom({com:e,value:t,pinId:n}){const o=e.id;w[o]=t,Object.keys(y).forEach((e=>{const r=y[e];if(r.show&&r._refs){const e=r._refs.get({comId:o});e&&e.outputs[n](t,!0,null,!0)}}))}};return s.scenesOperate=u,Object.assign(Object.assign({},t),{env:s,get disableAutoRun(){return r.disableAutoRun},ref:t.ref((e=>{e.canvas=s.canvas,r._refs=e;const n=r.todo,{inputs:i,outputs:a}=e,l=r.disableAutoRun;r.json.outputs.forEach((e=>{a(e.id,(t=>{var n,o,i;"apply"===e.id?null===(n=r.parentScope)||void 0===n||n.outputs[e.id](t):"module"!==r.type&&l?(r.show=!1,r.todo=[],r._refs=null,null===(o=r.parentScope)||void 0===o||o.outputs[e.id](t),r.parentScope=null,"popup"===r.type?m((e=>e.filter((e=>e!==r.json.id)))):c((e=>e+1))):null===(i=r.parentScope)||void 0===i||i.outputs[e.id](t)}))})),n.length?(n.forEach((({type:t,todo:n})=>{if("inputs"===t)Promise.resolve().then((()=>{i[n.pinId](n.value,o)}));else if("globalVar"===t){const{comId:t,value:o,bindings:r}=n;f(e,t,r,o)}})),r.todo=[]):l||(r.disableAutoRun=!0,Promise.resolve().then((()=>{var e,n;null===(n=null===(e=r.json.inputs)||void 0===e?void 0:e.forEach)||void 0===n||n.call(e,(e=>{const{id:n,mockData:o}=e;let r;if(t.debug&&void 0!==o)try{r=JSON.parse(decodeURIComponent(o))}catch(e){r=o}i[n](r)}))}))),l&&Promise.resolve().then((()=>{e.run()}))})),_env:{loadCSSLazy(){},currentScenes:{close(){r.show=!1,r.todo=[],r._refs=null,"popup"===r.type?m((e=>e.filter((e=>e!==r.json.id)))):c((e=>e+1))}}},scenesOperate:u})}),[]),I=(0,o.useMemo)((()=>h.length?h.map((e=>{const{id:t}=e,n=y[t];return n.show&&r().createElement(p,{key:t,json:Object.assign(Object.assign({},e),{scenesMap:y}),options:S(t),className:n.useEntryAnimation?u.main:"",style:"popup"===n.type?{position:"absolute",top:0,left:0,backgroundColor:"#ffffff00"}:{}})})):null),[i,h]),O=(0,o.useMemo)((()=>d.length?d.map((e=>{const t=y[e].json,{id:n}=t;return r().createElement(p,{key:t.id,json:Object.assign(Object.assign({},t),{scenesMap:y}),options:S(n),style:{position:"absolute",top:0,left:0,backgroundColor:"#ffffff00"}})})):null),[d]);return(0,o.useEffect)((()=>{n.setPerformanceRender("end",(new Date).getTime())}),[]),r().createElement(r().Fragment,null,I,O)}function p({json:e,options:t,style:n={},className:o=""}){return r().createElement(i.Z,{json:e,options:t,style:n,className:o,from:"scene"})}function f(e,t,n,o){const r=e.get({comId:t});r&&Array.isArray(n)&&n.forEach((e=>{let t=r;const n=e.split(".");n.forEach(((e,r)=>{r!==n.length-1?t=t[e]:t[e]=o}))}))}},878:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});var o=n(359),r=n.n(o),i=n(318),s=n.n(i);const a=n(759).Z.locals;let c=!1;var l;!function(e){e.Error="error"}(l||(l={}));const u="__fz-debug-info-container__";let d,p=document.getElementById(u),f=0;const m=({content:e,type:t})=>{const[n,i]=(0,o.useState)([]),s=e=>{const t=`__fz-notification-id-${++f}__`;e=Object.assign({_nid:t},e),i((t=>[...t,e]))},c=e=>{i((t=>t.filter((t=>t._nid!==e))))};return(0,o.useEffect)((()=>{d={add:s,remove:c},e&&d.add({content:e,type:t})}),[]),r().createElement("div",{className:a.container},n.map((e=>r().createElement("div",{key:e._nid,className:a.itemWrap},r().createElement("div",{className:`${a.item} ${a.errorItem}`},r().createElement("svg",{className:a.closeIcon,viewBox:"0 0 1045 1024",xmlns:"http://www.w3.org/2000/svg",onClick:()=>d.remove(e._nid)},r().createElement("path",{d:"M282.517333 213.376l-45.354666 45.162667L489.472 512 237.162667 765.461333l45.354666 45.162667L534.613333 557.354667l252.096 253.269333 45.354667-45.162667-252.288-253.44 252.288-253.482666-45.354667-45.162667L534.613333 466.624l-252.096-253.226667z",fill:"#555555"})),e.content)))))},h=["ResizeObserver loop limit exceeded"],g={init:e=>{c=!1!==e,c&&(window.onerror=function(e,t,n,o,r){if("string"!=typeof e||!h.includes(e))return!1})},error:e=>{var t;if(c){const n=(null==e?void 0:e.stack)||(null==e?void 0:e.message)||(null===(t=null==e?void 0:e.toString)||void 0===t?void 0:t.call(e))||e;if("string"==typeof n&&h.includes(n))return;((e,t)=>{let n={content:"",type:t};if("string"==typeof e)n.content=e;else try{n.content=JSON.stringify(e)}catch(t){console.error("showNotification JSON.stringify Error",t,{message:e})}n.content&&(p?null==d||d.add(n):(p=document.createElement("div"),p.setAttribute("id",u),document.body.appendChild(p),s().render(r().createElement(m,{content:n.content,type:n.type}),p)))})(n,l.Error)}}}},606:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var o=n(359),r=n.n(o),i=n(599),s=n(232),a=n(987),c=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n};const l=s.Z.locals;function u({com:e,index:t,env:n,getComDef:o,context:i,scope:s,inputs:a,outputs:c,_inputs:l,_outputs:d,_env:f,template:m,onError:h,logger:g,createPortal:y,options:v}){const{id:b,elements:_,style:w}=e;if(_)return r().createElement("div",{key:b,style:w},_.map((e=>u({com:e,index:t,env:n,getComDef:o,context:i,scope:s,inputs:a,outputs:c,_inputs:l,_outputs:d,_env:f,template:m,onError:h,logger:g,createPortal:y,options:v}))));{const r=p({com:e,env:n,getComDef:o,context:i,scope:s,inputs:a,outputs:c,_inputs:l,_outputs:d,index:t,_env:f,template:m,onError:h,logger:g,createPortal:y,options:v});return null==r?void 0:r.jsx}}function d({scope:e,root:t,slot:n,style:o={},createPortal:i,className:s,params:a,inputs:c,outputs:l,_inputs:d,_outputs:f,wrapper:m,template:y,env:v,_env:b,getComDef:_,context:w,onError:E,logger:x,options:S}){const{style:I,comAry:O,layoutTemplate:j}=n;if("smart"===I.layout&&j){const p=null==a?void 0:a.style,m=Object.assign(I,p||{});return r().createElement("div",{"data-isslot":"1",className:`${g(m)}${t&&s?` ${s}`:""}`,style:Object.assign(Object.assign({overflow:t?"hidden auto":"hidden"},h(m,!!p,t,"module"===n.type,v.edit)),o)},j.map(((t,n)=>u({com:t,index:n,env:v,getComDef:_,context:w,scope:e,inputs:c,outputs:l,_inputs:d,_outputs:f,_env:b,template:y,onError:E,logger:x,createPortal:i,options:S}))))}const C=[];if(O.forEach(((t,n)=>{const o=p({com:t,env:v,getComDef:_,context:w,scope:e,inputs:c,outputs:l,_inputs:d,_outputs:f,index:n,_env:b,template:y,onError:E,logger:x,createPortal:i,options:S});o&&C.push(o)})),"function"==typeof m)return m(C);{const e=null==a?void 0:a.style,i=Object.assign(I,e||{});return r().createElement("div",{"data-isslot":"1",className:`${g(i)}${t&&s?` ${s}`:""}`,style:Object.assign(Object.assign({},h(i,!!e,t,"module"===n.type,v.edit)),o)},C.map((e=>e.jsx)))}}function p({com:e,env:t,getComDef:n,context:o,scope:i,inputs:s,outputs:a,_inputs:c,_outputs:d,index:p,_env:m,template:h,onError:g,logger:y,createPortal:v,options:b}){var _,w;const{id:E,def:x,name:S,children:I,brother:O}=e,j=o.getComInfo(E),{hasPermission:C,permissions:k}=t,P=null===(w=null===(_=null==j?void 0:j.model)||void 0===_?void 0:_.permissions)||void 0===w?void 0:w.id;if(P&&"function"==typeof C){const e=C(P);if(!e||"boolean"!=typeof e&&!e.permission){const t=k.find((e=>e.id===P));return"hintLink"===((null==e?void 0:e.type)||(null==t?void 0:t.register.noPrivilege))?{id:E,name:S,jsx:r().createElement("div",{key:E},r().createElement("a",{href:(null==e?void 0:e.hintLinkUrl)||t.hintLink,target:"_blank",style:{textDecoration:"underline"}},(null==e?void 0:e.hintLinkTitle)||t.register.title)),style:{}}:void 0}}if(n(x)){const _=o.get({comId:E,scope:i,_ioProxy:{inputs:s,outputs:a,_inputs:c,_outputs:d}});if(_){const l=E+(i?i.id:"")+p;return{id:E,jsx:r().createElement(f,{key:l,com:e,getComDef:n,context:o,scope:i,props:_,env:t,_env:m,template:h,onError:g,logger:y,createPortal:v,options:b},null==O?void 0:O.map(((e,r)=>u({com:e,index:r,env:t,getComDef:n,context:o,scope:i,inputs:s,outputs:a,_inputs:c,_outputs:d,_env:m,template:h,onError:g,logger:y,createPortal:v})))),name:S,inputs:_.inputsCallable,style:_.style}}return{id:E,jsx:r().createElement("div",{className:l.error},"未找到组件(",x.namespace,"@",x.version," - ",E,")定义."),name:S,style:{}}}return{id:E,jsx:r().createElement("div",{className:l.error},"未找到组件(",x.namespace,"@",x.version,")定义."),name:S,style:{}}}function f({com:e,props:t,scope:n,template:s,env:c,createPortal:u,_env:d,getComDef:p,context:f,onError:h,logger:g,children:v,options:b}){const{id:_,def:w,name:E,slots:x={}}=e,{data:S,title:I,style:O,inputs:j,outputs:C,_inputs:k,_outputs:P,_notifyBindings:R}=t;(0,o.useMemo)((()=>{const{pxToRem:e,pxToVw:t}=c,n=function({env:e,style:t,def:n}){var o;const r=null===(o=null==e?void 0:e.themes)||void 0===o?void 0:o.comThemes;if(!r)return t.styleAry;let i=t.styleAry;const{themesId:s}=t,{namespace:a}=n;if(s||i)if("_defined"===s);else{const e=r[a];if(Array.isArray(e)){const t=e.find((({id:e})=>e===s));t&&(i=t.styleAry)}}else{const e=r[a];if(Array.isArray(e)){const t=e.find((({isDefault:e})=>e));t&&(i=t.styleAry)}}return i}({env:c,def:w,style:O});if(Array.isArray(n)){const o=(0,i.o4)();if(!o.querySelector(`style[id="${_}"]`)){const r=document.createElement("style");let i="";r.id=_,n.forEach((({css:n,selector:o,global:r})=>{":root"===o&&(o="> *:first-child"),Array.isArray(o)?o.forEach((o=>{i+=y({id:_,css:n,selector:o,global:r,configPxToRem:e,configPxToVw:t})})):i+=y({id:_,css:n,selector:o,global:r,configPxToRem:e,configPxToVw:t})})),r.innerHTML=i,o?o.appendChild(r):document.head.appendChild(r)}}Reflect.deleteProperty(O,"styleAry"),Reflect.deleteProperty(O,"themesId")}),[]);const A=p(w),T=new Proxy(x,{get(e,t){const o=x[t];if(!o)return;let i;n&&(i={id:n.id+"-"+n.frameId,frameId:t,parentComId:_,parent:n});const s=f.get({comId:_,slotId:t,scope:i});return{render(e){const a=null==e?void 0:e.scope;return a&&(i={id:a.id+"-"+a.frameId,frameId:t,parentComId:_,parent:a}),o?r().createElement(m,{key:null==e?void 0:e.key,props:s,currentScope:i,slotId:t,slot:o,params:e,style:O,onError:h,createPortal:u,parentComId:_,logger:g,env:c,_env:d,scope:n,getComDef:p,context:f,options:b}):r().createElement("div",{className:l.error},`组件(namespace=${w.namespace}）的插槽(id=${t})`," 未找到.")},get size(){var e,n;return!!(null===(n=null===(e=x[t])||void 0===e?void 0:e.comAry)||void 0===n?void 0:n.length)},_inputs:s._inputs,inputs:s.inputs,outputs:s.outputs}}}),N=(0,o.useMemo)((()=>{if(t.frameId&&t.parentComId){let e=(null==n?void 0:n.parentScope)||n;const o=f.get({comId:t.parentComId,slotId:t.frameId,scope:(null==e?void 0:e.parent)?e:null});if(o)return{get _inputs(){return new Proxy({},{get:(e,t)=>o._inputRegs[t]})}}}}),[]),L=function({style:e,id:t}){const n=[t,l.com];return 1!==e.flex||e.widthFull||n.push(l.flex),e.heightAuto&&n.push(l.comHeightAuto),n.join(" ")}({style:O,id:_}),F=function({style:e}){const t={},{width:n,height:o,maxWidth:r,flexX:s,minWidth:a,minHeight:c,rotation:l}=e;return n||s?(0,i.hj)(n)?t.width=n+"px":n&&(t.width=n):t.width="100%",(0,i.hj)(o)?t.height=o+"px":o&&(t.height=o),r&&(t.maxWidth=r),a&&(t.minWidth=a),c&&(t.minHeight=c),(0,i.hj)(l)&&(t.transform=`rotate(${l}deg)`,t.transformOrigin="center center"),t}({style:O}),D=function({style:e}){const t={},{width:n,margin:o,marginTop:r,marginLeft:s,marginRight:a,marginBottom:c}=e;return o?{margin:o}:((0,i.hj)(r)&&(t.marginTop=r+"px"),(0,i.hj)(s)&&("number"==typeof n||s<0?t.marginLeft=s+"px":t.paddingLeft=s+"px"),(0,i.hj)(a)&&("number"==typeof n||a<0?t.marginRight=a+"px":t.paddingRight=a+"px"),(0,i.hj)(c)&&(t.marginBottom=c+"px"),t)}({style:O}),B={};if(["fixed","absolute"].includes(O.position)){const{top:e,left:t,right:n,bottom:o}=O;(e||(0,i.hj)(e))&&(B.top=(0,i.hj)(e)?e+"px":e),(o||(0,i.hj)(o))&&(B.bottom=(0,i.hj)(o)?o+"px":o),(t||(0,i.hj)(t))&&(B.left=(0,i.hj)(t)?t+"px":t),(n||(0,i.hj)(n))&&(B.right=(0,i.hj)(n)?n+"px":n),"fixed"===O.position?B.zIndex=1e3:"absolute"===O.position&&(B.zIndex=1)}let M=A.runtime({id:_,env:c,_env:d,data:S,name:E,title:I,style:O,inputs:j,outputs:C,_inputs:k,_outputs:P,_notifyBindings:R,slots:T,createPortal:u,parentSlot:N,onError:h,logger:g});return"function"==typeof s&&(M=s({id:_,jsx:M,name:E,scope:n})),M=M?r().createElement("div",{id:_,key:_,style:Object.assign(Object.assign(Object.assign(Object.assign({display:O.display,position:O.position,flex:O.flex,flexDirection:O.flexDirection,flexShrink:O.flexShrink},B),F),D),O.ext||{}),className:L},r().createElement(a.Z,{errorTip:`组件 (namespace = ${w.namespace}@${w.version}）渲染错误`,options:b},M,v)):null,M}function m({slotId:e,parentComId:t,props:n,currentScope:s,slot:a,params:c,scope:l,env:u,createPortal:p,_env:f,style:m,getComDef:h,context:g,onError:y,logger:v,options:b}){const _=(0,o.useRef)(null),{curScope:w,curProps:E}=(0,o.useMemo)((()=>{let o=s,r=n,l=!1;if(o||"scope"===(null==a?void 0:a.type)&&(o={id:(0,i.Vj)(10,16),frameId:e,parentComId:t},l=!0),c){const n=c.inputValues;if("object"==typeof n){l||(o=Object.assign(Object.assign({},o),{id:o.id+"-"+(0,i.Vj)(10,16),parentScope:o})),r=g.get({comId:t,slotId:e,scope:o});for(let e in n)r.inputs[e](n[e],o)}}return r.run(o),{curScope:o,curProps:r}}),[]);return(0,o.useEffect)((()=>{const e=null==c?void 0:c.inputValues;if(e)if(_.current){if("object"==typeof e&&JSON.stringify(_.current)!==JSON.stringify(e)){_.current=e;for(let t in e)E.inputs[t](e[t],w);E.run()}}else _.current=e}),[null==c?void 0:c.inputValues]),(0,o.useEffect)((()=>()=>{E.destroy()}),[]),(0,o.useMemo)((()=>r().createElement(d,{scope:w,env:u,createPortal:p,_env:f,slot:a,params:c,wrapper:null==c?void 0:c.wrap,template:null==c?void 0:c.itemWrap,getComDef:h,context:g,inputs:null==c?void 0:c.inputs,outputs:null==c?void 0:c.outputs,_inputs:null==c?void 0:c._inputs,_outputs:null==c?void 0:c._outputs,onError:y,logger:v,options:b})),[])}function h(e,t,n,o,r){const{paddingLeft:i,paddingTop:s,paddingRight:a,paddingBottom:l,background:u,backgroundColor:d,backgroundImage:p,backgroundPosition:f,backgroundRepeat:m,backgroundSize:h,position:g,rowGap:y,columnGap:v,borderBottomColor:b,borderBottomLeftRadius:_,borderBottomRightRadius:w,borderBottomStyle:E,borderBottomWidth:x,borderLeftColor:S,borderLeftStyle:I,borderLeftWidth:O,borderRightColor:j,borderRightStyle:C,borderRightWidth:k,borderTopColor:P,borderTopLeftRadius:R,borderTopRightRadius:A,borderTopStyle:T,borderTopWidth:N,boxShadow:L}=e,F=c(e,["paddingLeft","paddingTop","paddingRight","paddingBottom","background","backgroundColor","backgroundImage","backgroundPosition","backgroundRepeat","backgroundSize","position","rowGap","columnGap","borderBottomColor","borderBottomLeftRadius","borderBottomRightRadius","borderBottomStyle","borderBottomWidth","borderLeftColor","borderLeftStyle","borderLeftWidth","borderRightColor","borderRightStyle","borderRightWidth","borderTopColor","borderTopLeftRadius","borderTopRightRadius","borderTopStyle","borderTopWidth","boxShadow"]);let D={rowGap:y,columnGap:v,paddingLeft:i||0,paddingTop:s||0,paddingRight:a||0,paddingBottom:l||0,backgroundColor:d||(n?"#ffffff":void 0),backgroundImage:p,backgroundPosition:f,backgroundRepeat:m,backgroundSize:h,borderBottomColor:b,borderBottomLeftRadius:_,borderBottomRightRadius:w,borderBottomStyle:E,borderBottomWidth:x,borderLeftColor:S,borderLeftStyle:I,borderLeftWidth:O,borderRightColor:j,borderRightStyle:C,borderRightWidth:k,borderTopColor:P,borderTopLeftRadius:R,borderTopRightRadius:A,borderTopStyle:T,borderTopWidth:N,boxShadow:L};if(u)if("object"==typeof u){const{background:e,backgroundImage:t,backgroundColor:n,backgroundRepeat:o,backgroundSize:r}=u;D.backgroundRepeat=o,D.backgroundSize=r,e?D.background=e:(D.backgroundImage=t,D.backgroundColor=n)}else D.background=u;return o&&(e.heightAuto?D.height="fit-content":e.heightFull?D.height="100%":D.height=e.height,e.widthAuto?D.width="fit-content":e.widthFull?D.width="100%":D.width=e.width),t&&(D=Object.assign(D,F)),D}function g(e){var t,n;const o=[l.slot,"slot"],r=e;if(r){"flex-column"==(null===(t=r.layout)||void 0===t?void 0:t.toLowerCase())?o.push(l.lyFlexColumn):"flex-row"==(null===(n=r.layout)||void 0===n?void 0:n.toLowerCase())&&o.push(l.lyFlexRow);const e=r.justifyContent;e&&("FLEX-START"===e.toUpperCase()?o.push(l.justifyContentFlexStart):"CENTER"===e.toUpperCase()?o.push(l.justifyContentFlexCenter):"FLEX-END"===e.toUpperCase()?o.push(l.justifyContentFlexFlexEnd):"SPACE-AROUND"===e.toUpperCase()?o.push(l.justifyContentFlexSpaceAround):"SPACE-BETWEEN"===e.toUpperCase()&&o.push(l.justifyContentFlexSpaceBetween));const i=r.alignItems;i&&("FLEX-START"===i.toUpperCase()?o.push(l.alignItemsFlexStart):"CENTER"===i.toUpperCase()?o.push(l.alignItemsFlexCenter):"FLEX-END"===i.toUpperCase()&&o.push(l.alignItemsFlexFlexEnd))}return o.join(" ")}function y({id:e,css:t,selector:n,global:o,configPxToRem:r,configPxToVw:s}){return`\n    ${o?"":`#${e} `}${n.replace(/\{id\}/g,`${e}`)} {\n      ${Object.keys(t).map((e=>{let n=t[e];return r&&"string"==typeof n&&-1!==n.indexOf("px")?n=(0,i.Q1)(n):s&&"string"==typeof n&&-1!==n.indexOf("px")&&(n=(0,i.Ok)(n)),`${(0,i.Lc)(e)}: ${n};`})).join("\n")}\n    }\n  `}},130:(e,t,n)=>{"use strict";n.d(t,{Mk:()=>S,sY:()=>I});var o=n(359),r=n.n(o),i=n(576),s=n(147),a=n(704),c=n(383),l=n(196),u=n(232),d=n(650),p=n(490),f=n(759),m=n(358),h=n(878),g=n(901),y=n(599),v=n(21),b=n.n(v);function _(e,t){e.use({target:t})}n(257),console.log(`%c ${s.u2} %c@${s.i8}`,"color:#FFF;background:#fa6400","","");class w{constructor(e){this.options=e,this.performance={render:{start:window.MYBRICKS_PC_FMP_START||(new Date).getTime(),end:null,time:null},callConnectorTimes:[]},this.onCompleteCallBacks=[],this.comDefs={},this.onError=e=>{console.error(e),h.Z.error(e)},this.observable=l.L;const{env:t,debug:n,observable:o,onError:r,plugins:i=[]}=e;this.mode=n?"development":"production",o?this.observable=o:(0,l.O)({pxToRem:t.pxToRem,pxToVw:t.pxToVw});const s=(0,y.o4)();_(u.Z,s),_(d.Z,s),_(p.Z,s),_(f.Z,s);const a=null==t?void 0:t.pxToRem;if(a){const{enableAdaptive:e=!1,landscapeWidth:t=1440}=a,n=document.documentElement;if(e){const e=()=>{n.style.fontSize=n.clientWidth/(t/12)+"px"};e(),window.addEventListener("resize",e)}else n.style.fontSize="12px"}const{onCompleteCallBacks:m}=this;if(t.runtime||(t.runtime={onComplete(e){m.push(e)}}),t.i18n||(t.i18n=e=>e),t.renderModule){const e=t.renderModule;t.renderModule=(n,o)=>e(n,Object.assign(Object.assign({},o),{env:t,_isNestedRender:!0}))}else t.renderModule=(e,n)=>I(e,Object.assign(Object.assign({},n),{env:t,_isNestedRender:!0}));if(t.renderCom){const e=t.renderCom;t.renderCom=(t,n)=>e(t,Object.assign(Object.assign({},n),{_isNestedRender:!0}))}else t.renderCom=(e,t)=>I(e,Object.assign(Object.assign({},t),{_isNestedRender:!0}));const g=document.body;t.canvas||(t.canvas={get type(){return g.clientWidth<=414?"mobile":"pc"}}),"canvasElement"in t||(t.canvasElement=g),window.RENDER_WEB_PERFORMANCE={getPerformance:this.getPerformance.bind(this)},this.initOther(),this.initComdefs(),[new c.Z].concat(i).forEach((e=>{e.apply(this)}))}initOther(){const{env:e}=this.options;e.silent&&(0,m.JE)(),h.Z.init(e.showErrorNotification),this.logger={info:console.info,trace:console.trace,warn:console.warn,error:e=>{console.error(e),h.Z.error(e)}}}initComdefs(){const e=(t,n)=>{t.forEach((t=>{t.comAray?e(t.comAray,n):n[`${t.namespace}-${t.version}`]=t}))},{comDefs:t}=this.options,{comDefs:n}=this;t&&Object.assign(n,t);let o=[...window.__comlibs_edit_||[],...window.__comlibs_rt_||[]];o.push(b()),o.forEach((t=>{const o=t.comAray;o&&Array.isArray(o)&&e(o,n)}))}getComDef(e){const{comDefs:t}=this,n=t[e.namespace+"-"+e.version]||t[e.namespace];if(!n){const n=[];for(let o in t)o.startsWith(e.namespace+"-")&&n.push(t[o]);if(n&&n.length>0){n.sort(((e,t)=>(0,y.yC)(e.version,t.version)));const t=n[0];return console.warn(`【Mybricks】组件${e.namespace+"@"+e.version}未找到，使用${t.namespace}@${t.version}代替.`),t}return console.error(`组件${e.namespace+"@"+e.version}未找到，请确定是否存在该组件以及对应的版本号.`),null}return n}getPerformance(){return this.performance}setPerformanceRender(e,t){const n=this.performance.render;n[e]=t,"end"===e&&(n.time=t-n.start)}setPerformanceCallConnectorTimes(e){this.performance.callConnectorTimes.push(e)}}const E=(0,o.createContext)({});function x({children:e,value:t}){return(0,o.useEffect)((()=>()=>{t.onCompleteCallBacks.forEach((e=>{e()}))}),[]),r().createElement(E.Provider,{value:t},e)}function S(){return(0,o.useContext)(E)}function I(e,t){var n;let o=e;if(t.env.edit||null===(n=t.env.runtime)||void 0===n||n.debug,o){let e=null;if("scenes"in o?((t._isNestedRender||t.debug)&&(t.env=(0,y.p$)(t.env)),e=r().createElement(a.Z,{json:o,options:t})):o.slot&&(t.env.edit&&o.type,e=r().createElement(i.Z,{json:o,options:t,root:"module"!==o.type})),!e){const e=new w(t);return(0,g.Z)({json:o,getComDef:t=>e.getComDef(t),events:t.events,env:t.env,ref(e){const{inputs:n}=e,r=o.inputs;n&&Array.isArray(r)&&r.forEach((e=>{const{id:o,mockData:r}=e;let i;if(t.debug&&void 0!==r)try{i=JSON.parse(decodeURIComponent(r))}catch(e){i=r}n[o](i)})),e.run()},onError:e.onError,debug:t.debug,debugLogger:t.debugLogger,logger:e.logger,scenesOperate:t.scenesOperate,_context:e},{observable:e.observable}),null}return t._isNestedRender?e:r().createElement(x,{value:new w(t)},e)}return null}},119:(e,t,n)=>{"use strict";n.d(t,{ep:()=>o.e,in:()=>s,kU:()=>i,st:()=>r.s});var o=n(965),r=n(60);const i=new WeakMap,s=new WeakMap},60:(e,t,n)=>{"use strict";n.d(t,{s:()=>r});var o=n(119);const r=new class{constructor(){this.reactionStack=[]}regist(e){let t=this.getCurrentReaction();t&&o.ep.registReaction(t,e)}autoRun(e,t){const{reactionStack:n}=this;if(-1===n.indexOf(e))try{return n.push(e),t()}finally{n.pop()}}getCurrentReaction(){const{reactionStack:e}=this;return e[e.length-1]}}},965:(e,t,n)=>{"use strict";n.d(t,{e:()=>s});var o=n(599),r=n(119);function i(e){e()}const s=new class{constructor(){this.taskMap=new WeakMap,this.reactionToTaskMap=new WeakMap}addTask(e){this.taskMap.set(e,new Map)}deleteTask(e){if(!(0,o.Kn)(e))return;const t=r.kU.get(e);r.kU.delete(e),r.in.delete(t),this.taskMap.get(t),this.taskMap.delete(t)}deleteReaction(e){let t=this.reactionToTaskMap.get(e);t&&(this.reactionToTaskMap.delete(e),t.forEach((t=>{t.forEach((t=>{t.delete(e)}))})))}registReaction(e,{target:t,key:n}){const o=this.taskMap.get(t);if(o){let t=o.get(n);t||(t=new Set,o.set(n,t)),t.has(e)||t.add(e);let r=this.reactionToTaskMap.get(e);r||(r=new Set,this.reactionToTaskMap.set(e,r)),r.has(o)||r.add(o)}}getReactions({target:e,key:t}){const n=this.taskMap.get(e);return n&&n.get(t)||[]}runTask(e){const t=this.getReactions(e);t.size&&t.forEach(i)}}},787:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var o=n(196),r=n(599),i=n(119);const s={get:function(e,t){const n=e[t];if(["$$typeof","constructor"].includes(t)||e.__model_style__&&"display"!==t)return n;if(i.st.regist({target:e,key:t}),n instanceof FormData)return n;const s=i.in.get(n);return(0,r.Kn)(n)?s||(0,o.L)(n):s||n},set:function(e,t,n){(0,r.Kn)(n)&&(n=i.kU.get(n)||n);const o=Object.hasOwnProperty.call(e,t),s=e[t];e[t]=n;let a=!1;switch(!0){case!o||Array.isArray(e)&&"length"===t:case n!==s:a=!0}return a&&(i.ep.runTask({target:e,key:t}),i.ep.deleteTask(i.in.get(s))),!0}}},196:(e,t,n)=>{"use strict";n.d(t,{L:()=>f,O:()=>u});var o=n(359),r=n.n(o),i=n(119),s=n(599),a=n(787);const c="__render-web-createElement__";let l;function u(e){const{pxToRem:t,pxToVw:n}=e;r()[c]||(r()[c]=!0,l=r().createElement,r().createElement=function(...e){let[i,a]=e;if(a){const c=a.style;if(t&&c?Object.keys(c).forEach((e=>{const t=c[e];"string"==typeof t&&-1!==t.indexOf("px")&&(c[e]=(0,s.Q1)(t))})):n&&c&&Object.keys(c).forEach((e=>{const t=c[e];"string"==typeof t&&-1!==t.indexOf("px")&&(c[e]=(0,s.Ok)(t))})),e.length>0&&"function"==typeof i){if(!i.prototype||!(i.prototype instanceof r().Component)&&void 0===i.prototype.isReactComponent){const t=function(e,t=!1){let n=e[d];if(!n){t?(n=(0,o.memo)(e),n.render=p(e.render,!1)):n=p(e);try{e[d]=n}catch(e){}}const r=Object.getOwnPropertyNames(e);return r&&r.forEach((o=>{try{t&&"render"===o||(n[o]=e[o])}catch(e){console.error(e)}})),n}(i);e.splice(0,1,t)}return l(...e)}return"object"==typeof i&&(i.$$typeof,Symbol.for("react.forward_ref")),l(...e)}return l(...e)})}const d="__enhanced__";function p(e,t=!0){function n(t,n){var r;const i=(0,o.useRef)(null),[,s]=(0,o.useState)([]);let a;return(0,o.useMemo)((()=>{i.current||(i.current=new m((()=>s([]))))}),[]),(0,o.useEffect)((()=>()=>{var e;null===(e=i.current)||void 0===e||e.destroy(),i.current=null}),[]),null===(r=i.current)||void 0===r||r.track((()=>{a=e(t,n)})),a}return n.displayName=e.displayName||e.name,t?(0,o.memo)(n):n}function f(e){return(0,s.Kn)(e)?i.kU.has(e)?e:i.in.get(e)||function(e){const t=a.Z,n=new Proxy(e,t);return i.in.set(e,n),i.kU.set(n,e),i.ep.addTask(e),n}(e):{}}class m{constructor(e){this.update=e}track(e){i.st.autoRun(this.update,e)}destroy(){i.ep.deleteReaction(this.update)}}},383:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var o=n(359),r=n.n(o),i=n(318),s=n.n(i),a=n(599),c=n(423);const l=c.Z.locals,u=r().createElement("svg",{viewBox:"0 0 1024 1024",version:"1.1",xmlns:"http://www.w3.org/2000/svg","p-id":"10635",width:"12",height:"12"},r().createElement("path",{d:"M219.428571 73.142857v877.714286H73.142857V73.142857zM365.714286 73.142857l585.142857 438.857143-577.828572 438.857143L365.714286 599.771429","p-id":"10636",fill:"#707070"}));function d({resume:e}){return r().createElement("div",{className:l.debugger},r().createElement("div",{className:l.titlebar},r().createElement("div",null,"已在交互视图中暂停"),r().createElement("div",{className:l.resume,onClick:e},u)))}class p{constructor(){}apply(e){const{options:t,mode:n}=e,{env:o,debug:r,onError:i}=t;if("development"===n&&"function"==typeof r){const n=new f(o),{log:s,onResume:l}=r({resume:()=>{n.next()},ignoreAll:e=>{n.setIgnoreWait(e),e&&n.next(!0)}});n.setResume(l),e.debuggerPanel=n,t.debugLogger=s,e.onError=i,c.Z.use({target:(0,a.o4)()})}}}class f{constructor(e){this.env=e,this.isReact18=!!s().createRoot,this.resume=()=>{},this._pending=!1,this._ignoreWait=!1,this._waitCount=0,this._waitBreakpointIds=[],this._waitIdToResolvesMap={}}setResume(e){this.resume=e}open(){if(this.dom)this.dom.style.visibility="visible";else{const e=document.createElement("div"),t=this.env.canvasElement;if(this.canvas=t,this.dom=e,e.style.position="absolute",e.style.width="100%",e.style.height="100%",e.style.top="0",e.style.left="0",e.style.zIndex="100000",e.style.visibility="visible",t.appendChild(e),this.isReact18){const t=s().createRoot(e);t.render(r().createElement(d,{resume:this.resume})),this.root=t}else s().render(r().createElement(d,{resume:this.resume}),e)}}close(){this.dom&&(this.dom.style.visibility="hidden")}destroy(){this.dom&&requestAnimationFrame((()=>{this.isReact18?this.root.unmount():s().unmountComponentAtNode(this.dom),this.root=null,this.canvas.removeChild(this.dom),this.canvas=null,this.dom=null}))}hasBreakpoint(e){return!this._ignoreWait&&(this._pending||e.isBreakpoint)}wait(e,t){return new Promise((n=>{if(this._ignoreWait)n();else{const o=this._waitBreakpointIds.length>0;if(o||t(),this.open(),this._pending=!0,e.isBreakpoint){if(o){const e=this._waitBreakpointIds[0];this._waitIdToResolvesMap[e].push(t)}const r=this._waitCount+++e.id;this._waitBreakpointIds.unshift(r),this._waitIdToResolvesMap[r]=[n]}else{const e=this._waitBreakpointIds[0];this._waitIdToResolvesMap[e].push(n)}}}))}next(e=!1){if(e)for(;this._waitBreakpointIds.length;)this.next();else{const e=this._waitBreakpointIds.pop(),t=this._waitIdToResolvesMap[e];t&&t.forEach((e=>e())),this._waitBreakpointIds.length||(this._pending=!1,this.close())}}setIgnoreWait(e){this._ignoreWait=e}}},257:(e,t,n)=>{"use strict";n(343),n(984)},984:(e,t,n)=>{},628:(e,t,n)=>{},549:(e,t,n)=>{"use strict";n(628)},343:(e,t,n)=>{"use strict";n(549)},359:e=>{"use strict";e.exports=__WEBPACK_EXTERNAL_MODULE__359__},318:e=>{"use strict";e.exports=__WEBPACK_EXTERNAL_MODULE__318__},147:e=>{"use strict";e.exports=JSON.parse('{"u2":"@mybricks/render-web","i8":"1.2.75"}')}},__webpack_module_cache__={};function __nested_webpack_require_76299__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={id:e,exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__nested_webpack_require_76299__),n.exports}__nested_webpack_require_76299__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __nested_webpack_require_76299__.d(t,{a:t}),t},__nested_webpack_require_76299__.d=(e,t)=>{for(var n in t)__nested_webpack_require_76299__.o(t,n)&&!__nested_webpack_require_76299__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__nested_webpack_require_76299__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__nested_webpack_require_76299__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},__nested_webpack_require_76299__.nc=void 0;var __nested_webpack_exports__={};return(()=>{"use strict";__nested_webpack_require_76299__.r(__nested_webpack_exports__),__nested_webpack_require_76299__.d(__nested_webpack_exports__,{render:()=>e.sY});var e=__nested_webpack_require_76299__(130)})(),__nested_webpack_exports__})(),module.exports=t(__webpack_require__(594),__webpack_require__(206))},594:e=>{"use strict";e.exports=React},206:e=>{"use strict";e.exports=ReactDOM}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var n=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e].call(n.exports,n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var n in t)__webpack_require__.o(t,n)&&!__webpack_require__.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";var e={};__webpack_require__.r(e),__webpack_require__.d(e,{hasBrowserEnv:()=>re,hasStandardBrowserEnv:()=>ie,hasStandardBrowserWebWorkerEnv:()=>ae});var t=__webpack_require__(594),n=__webpack_require__.n(t),o=__webpack_require__(206),r=__webpack_require__(25);function i(e,t){return function(){return e.apply(t,arguments)}}const{toString:s}=Object.prototype,{getPrototypeOf:a}=Object,c=(l=Object.create(null),e=>{const t=s.call(e);return l[t]||(l[t]=t.slice(8,-1).toLowerCase())});var l;const u=e=>(e=e.toLowerCase(),t=>c(t)===e),d=e=>t=>typeof t===e,{isArray:p}=Array,f=d("undefined"),m=u("ArrayBuffer"),h=d("string"),g=d("function"),y=d("number"),v=e=>null!==e&&"object"==typeof e,b=e=>{if("object"!==c(e))return!1;const t=a(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},_=u("Date"),w=u("File"),E=u("Blob"),x=u("FileList"),S=u("URLSearchParams");function I(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let o,r;if("object"!=typeof e&&(e=[e]),p(e))for(o=0,r=e.length;o<r;o++)t.call(null,e[o],o,e);else{const r=n?Object.getOwnPropertyNames(e):Object.keys(e),i=r.length;let s;for(o=0;o<i;o++)s=r[o],t.call(null,e[s],s,e)}}function O(e,t){t=t.toLowerCase();const n=Object.keys(e);let o,r=n.length;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,C=e=>!f(e)&&e!==j,k=(P="undefined"!=typeof Uint8Array&&a(Uint8Array),e=>P&&e instanceof P);var P;const R=u("HTMLFormElement"),A=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),T=u("RegExp"),N=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),o={};I(n,((n,r)=>{let i;!1!==(i=t(n,r,e))&&(o[r]=i||n)})),Object.defineProperties(e,o)},L="abcdefghijklmnopqrstuvwxyz",F="0123456789",D={DIGIT:F,ALPHA:L,ALPHA_DIGIT:L+L.toUpperCase()+F},B=u("AsyncFunction"),M={isArray:p,isArrayBuffer:m,isBuffer:function(e){return null!==e&&!f(e)&&null!==e.constructor&&!f(e.constructor)&&g(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||g(e.append)&&("formdata"===(t=c(e))||"object"===t&&g(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&m(e.buffer),t},isString:h,isNumber:y,isBoolean:e=>!0===e||!1===e,isObject:v,isPlainObject:b,isUndefined:f,isDate:_,isFile:w,isBlob:E,isRegExp:T,isFunction:g,isStream:e=>v(e)&&g(e.pipe),isURLSearchParams:S,isTypedArray:k,isFileList:x,forEach:I,merge:function e(){const{caseless:t}=C(this)&&this||{},n={},o=(o,r)=>{const i=t&&O(n,r)||r;b(n[i])&&b(o)?n[i]=e(n[i],o):b(o)?n[i]=e({},o):p(o)?n[i]=o.slice():n[i]=o};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&I(arguments[e],o);return n},extend:(e,t,n,{allOwnKeys:o}={})=>(I(t,((t,o)=>{n&&g(t)?e[o]=i(t,n):e[o]=t}),{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,o)=>{e.prototype=Object.create(t.prototype,o),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,o)=>{let r,i,s;const c={};if(t=t||{},null==e)return t;do{for(r=Object.getOwnPropertyNames(e),i=r.length;i-- >0;)s=r[i],o&&!o(s,e,t)||c[s]||(t[s]=e[s],c[s]=!0);e=!1!==n&&a(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:c,kindOfTest:u,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const o=e.indexOf(t,n);return-1!==o&&o===n},toArray:e=>{if(!e)return null;if(p(e))return e;let t=e.length;if(!y(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let o;for(;(o=n.next())&&!o.done;){const n=o.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const o=[];for(;null!==(n=e.exec(t));)o.push(n);return o},isHTMLForm:R,hasOwnProperty:A,hasOwnProp:A,reduceDescriptors:N,freezeMethods:e=>{N(e,((t,n)=>{if(g(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const o=e[n];g(o)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},o=e=>{e.forEach((e=>{n[e]=!0}))};return p(e)?o(e):o(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:O,global:j,isContextDefined:C,ALPHABET:D,generateString:(e=16,t=D.ALPHA_DIGIT)=>{let n="";const{length:o}=t;for(;e--;)n+=t[Math.random()*o|0];return n},isSpecCompliantForm:function(e){return!!(e&&g(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,o)=>{if(v(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[o]=e;const r=p(e)?[]:{};return I(e,((e,t)=>{const i=n(e,o+1);!f(i)&&(r[t]=i)})),t[o]=void 0,r}}return e};return n(e,0)},isAsyncFn:B,isThenable:e=>e&&(v(e)||g(e))&&g(e.then)&&g(e.catch)};function $(e,t,n,o,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),o&&(this.request=o),r&&(this.response=r)}M.inherits($,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:M.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const U=$.prototype,q={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{q[e]={value:e}})),Object.defineProperties($,q),Object.defineProperty(U,"isAxiosError",{value:!0}),$.from=(e,t,n,o,r,i)=>{const s=Object.create(U);return M.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),$.call(s,e.message,t,n,o,r),s.cause=e,s.name=e.name,i&&Object.assign(s,i),s};const K=$;function Z(e){return M.isPlainObject(e)||M.isArray(e)}function W(e){return M.endsWith(e,"[]")?e.slice(0,-2):e}function J(e,t,n){return e?e.concat(t).map((function(e,t){return e=W(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const H=M.toFlatObject(M,{},null,(function(e){return/^is[A-Z]/.test(e)})),z=function(e,t,n){if(!M.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const o=(n=M.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!M.isUndefined(t[e])}))).metaTokens,r=n.visitor||l,i=n.dots,s=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&M.isSpecCompliantForm(t);if(!M.isFunction(r))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(M.isDate(e))return e.toISOString();if(!a&&M.isBlob(e))throw new K("Blob is not supported. Use a Buffer instead.");return M.isArrayBuffer(e)||M.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,r){let a=e;if(e&&!r&&"object"==typeof e)if(M.endsWith(n,"{}"))n=o?n:n.slice(0,-2),e=JSON.stringify(e);else if(M.isArray(e)&&function(e){return M.isArray(e)&&!e.some(Z)}(e)||(M.isFileList(e)||M.endsWith(n,"[]"))&&(a=M.toArray(e)))return n=W(n),a.forEach((function(e,o){!M.isUndefined(e)&&null!==e&&t.append(!0===s?J([n],o,i):null===s?n:n+"[]",c(e))})),!1;return!!Z(e)||(t.append(J(r,n,i),c(e)),!1)}const u=[],d=Object.assign(H,{defaultVisitor:l,convertValue:c,isVisitable:Z});if(!M.isObject(e))throw new TypeError("data must be an object");return function e(n,o){if(!M.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+o.join("."));u.push(n),M.forEach(n,(function(n,i){!0===(!(M.isUndefined(n)||null===n)&&r.call(t,n,M.isString(i)?i.trim():i,o,d))&&e(n,o?o.concat(i):[i])})),u.pop()}}(e),t};function V(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function G(e,t){this._pairs=[],e&&z(e,this,t)}const X=G.prototype;X.append=function(e,t){this._pairs.push([e,t])},X.toString=function(e){const t=e?function(t){return e.call(this,t,V)}:V;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Q=G;function Y(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ee(e,t,n){if(!t)return e;const o=n&&n.encode||Y,r=n&&n.serialize;let i;if(i=r?r(t,n):M.isURLSearchParams(t)?t.toString():new Q(t,n).toString(o),i){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+i}return e}const te=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){M.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},ne={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},oe={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:Q,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},re="undefined"!=typeof window&&"undefined"!=typeof document,ie=(se="undefined"!=typeof navigator&&navigator.product,re&&["ReactNative","NativeScript","NS"].indexOf(se)<0);var se;const ae="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ce={...e,...oe},le=function(e){function t(e,n,o,r){let i=e[r++];if("__proto__"===i)return!0;const s=Number.isFinite(+i),a=r>=e.length;return i=!i&&M.isArray(o)?o.length:i,a?(M.hasOwnProp(o,i)?o[i]=[o[i],n]:o[i]=n,!s):(o[i]&&M.isObject(o[i])||(o[i]=[]),t(e,n,o[i],r)&&M.isArray(o[i])&&(o[i]=function(e){const t={},n=Object.keys(e);let o;const r=n.length;let i;for(o=0;o<r;o++)i=n[o],t[i]=e[i];return t}(o[i])),!s)}if(M.isFormData(e)&&M.isFunction(e.entries)){const n={};return M.forEachEntry(e,((e,o)=>{t(function(e){return M.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),o,n,0)})),n}return null},ue={transitional:ne,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",o=n.indexOf("application/json")>-1,r=M.isObject(e);if(r&&M.isHTMLForm(e)&&(e=new FormData(e)),M.isFormData(e))return o?JSON.stringify(le(e)):e;if(M.isArrayBuffer(e)||M.isBuffer(e)||M.isStream(e)||M.isFile(e)||M.isBlob(e))return e;if(M.isArrayBufferView(e))return e.buffer;if(M.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(r){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return z(e,new ce.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,o){return ce.isNode&&M.isBuffer(e)?(this.append(t,e.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((i=M.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return z(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return r||o?(t.setContentType("application/json",!1),function(e,t,n){if(M.isString(e))try{return(0,JSON.parse)(e),M.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ue.transitional,n=t&&t.forcedJSONParsing,o="json"===this.responseType;if(e&&M.isString(e)&&(n&&!this.responseType||o)){const n=!(t&&t.silentJSONParsing)&&o;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw K.from(e,K.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ce.classes.FormData,Blob:ce.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};M.forEach(["delete","get","head","post","put","patch"],(e=>{ue.headers[e]={}}));const de=ue,pe=M.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),fe=Symbol("internals");function me(e){return e&&String(e).trim().toLowerCase()}function he(e){return!1===e||null==e?e:M.isArray(e)?e.map(he):String(e)}function ge(e,t,n,o,r){return M.isFunction(o)?o.call(this,t,n):(r&&(t=n),M.isString(t)?M.isString(o)?-1!==t.indexOf(o):M.isRegExp(o)?o.test(t):void 0:void 0)}class ye{constructor(e){e&&this.set(e)}set(e,t,n){const o=this;function r(e,t,n){const r=me(t);if(!r)throw new Error("header name must be a non-empty string");const i=M.findKey(o,r);(!i||void 0===o[i]||!0===n||void 0===n&&!1!==o[i])&&(o[i||t]=he(e))}const i=(e,t)=>M.forEach(e,((e,n)=>r(e,n,t)));return M.isPlainObject(e)||e instanceof this.constructor?i(e,t):M.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?i((e=>{const t={};let n,o,r;return e&&e.split("\n").forEach((function(e){r=e.indexOf(":"),n=e.substring(0,r).trim().toLowerCase(),o=e.substring(r+1).trim(),!n||t[n]&&pe[n]||("set-cookie"===n?t[n]?t[n].push(o):t[n]=[o]:t[n]=t[n]?t[n]+", "+o:o)})),t})(e),t):null!=e&&r(t,e,n),this}get(e,t){if(e=me(e)){const n=M.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=n.exec(e);)t[o[1]]=o[2];return t}(e);if(M.isFunction(t))return t.call(this,e,n);if(M.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=me(e)){const n=M.findKey(this,e);return!(!n||void 0===this[n]||t&&!ge(0,this[n],n,t))}return!1}delete(e,t){const n=this;let o=!1;function r(e){if(e=me(e)){const r=M.findKey(n,e);!r||t&&!ge(0,n[r],r,t)||(delete n[r],o=!0)}}return M.isArray(e)?e.forEach(r):r(e),o}clear(e){const t=Object.keys(this);let n=t.length,o=!1;for(;n--;){const r=t[n];e&&!ge(0,this[r],r,e,!0)||(delete this[r],o=!0)}return o}normalize(e){const t=this,n={};return M.forEach(this,((o,r)=>{const i=M.findKey(n,r);if(i)return t[i]=he(o),void delete t[r];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(r):String(r).trim();s!==r&&delete t[r],t[s]=he(o),n[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return M.forEach(this,((n,o)=>{null!=n&&!1!==n&&(t[o]=e&&M.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[fe]=this[fe]={accessors:{}}).accessors,n=this.prototype;function o(e){const o=me(e);t[o]||(function(e,t){const n=M.toCamelCase(" "+t);["get","set","has"].forEach((o=>{Object.defineProperty(e,o+n,{value:function(e,n,r){return this[o].call(this,t,e,n,r)},configurable:!0})}))}(n,e),t[o]=!0)}return M.isArray(e)?e.forEach(o):o(e),this}}ye.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),M.reduceDescriptors(ye.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),M.freezeMethods(ye);const ve=ye;function be(e,t){const n=this||de,o=t||n,r=ve.from(o.headers);let i=o.data;return M.forEach(e,(function(e){i=e.call(n,i,r.normalize(),t?t.status:void 0)})),r.normalize(),i}function _e(e){return!(!e||!e.__CANCEL__)}function we(e,t,n){K.call(this,null==e?"canceled":e,K.ERR_CANCELED,t,n),this.name="CanceledError"}M.inherits(we,K,{__CANCEL__:!0});const Ee=we,xe=ce.hasStandardBrowserEnv?{write(e,t,n,o,r,i){const s=[e+"="+encodeURIComponent(t)];M.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),M.isString(o)&&s.push("path="+o),M.isString(r)&&s.push("domain="+r),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Se(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const Ie=ce.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function o(n){let o=n;return e&&(t.setAttribute("href",o),o=t.href),t.setAttribute("href",o),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=o(window.location.href),function(e){const t=M.isString(e)?o(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0};function Oe(e,t){let n=0;const o=function(e,t){e=e||10;const n=new Array(e),o=new Array(e);let r,i=0,s=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=o[s];r||(r=c),n[i]=a,o[i]=c;let u=s,d=0;for(;u!==i;)d+=n[u++],u%=e;if(i=(i+1)%e,i===s&&(s=(s+1)%e),c-r<t)return;const p=l&&c-l;return p?Math.round(1e3*d/p):void 0}}(50,250);return r=>{const i=r.loaded,s=r.lengthComputable?r.total:void 0,a=i-n,c=o(a);n=i;const l={loaded:i,total:s,progress:s?i/s:void 0,bytes:a,rate:c||void 0,estimated:c&&s&&i<=s?(s-i)/c:void 0,event:r};l[t?"download":"upload"]=!0,e(l)}}const je={http:null,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let o=e.data;const r=ve.from(e.headers).normalize();let i,s,{responseType:a,withXSRFToken:c}=e;function l(){e.cancelToken&&e.cancelToken.unsubscribe(i),e.signal&&e.signal.removeEventListener("abort",i)}if(M.isFormData(o))if(ce.hasStandardBrowserEnv||ce.hasStandardBrowserWebWorkerEnv)r.setContentType(!1);else if(!1!==(s=r.getContentType())){const[e,...t]=s?s.split(";").map((e=>e.trim())).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...t].join("; "))}let u=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";r.set("Authorization","Basic "+btoa(t+":"+n))}const d=Se(e.baseURL,e.url);function p(){if(!u)return;const o=ve.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders());!function(e,t,n){const o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(new K("Request failed with status code "+n.status,[K.ERR_BAD_REQUEST,K.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),l()}),(function(e){n(e),l()}),{data:a&&"text"!==a&&"json"!==a?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:o,config:e,request:u}),u=null}if(u.open(e.method.toUpperCase(),ee(d,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,"onloadend"in u?u.onloadend=p:u.onreadystatechange=function(){u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))&&setTimeout(p)},u.onabort=function(){u&&(n(new K("Request aborted",K.ECONNABORTED,e,u)),u=null)},u.onerror=function(){n(new K("Network Error",K.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const o=e.transitional||ne;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new K(t,o.clarifyTimeoutError?K.ETIMEDOUT:K.ECONNABORTED,e,u)),u=null},ce.hasStandardBrowserEnv&&(c&&M.isFunction(c)&&(c=c(e)),c||!1!==c&&Ie(d))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&xe.read(e.xsrfCookieName);t&&r.set(e.xsrfHeaderName,t)}void 0===o&&r.setContentType(null),"setRequestHeader"in u&&M.forEach(r.toJSON(),(function(e,t){u.setRequestHeader(t,e)})),M.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),a&&"json"!==a&&(u.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",Oe(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",Oe(e.onUploadProgress)),(e.cancelToken||e.signal)&&(i=t=>{u&&(n(!t||t.type?new Ee(null,e,u):t),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(i),e.signal&&(e.signal.aborted?i():e.signal.addEventListener("abort",i)));const f=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(d);f&&-1===ce.protocols.indexOf(f)?n(new K("Unsupported protocol "+f+":",K.ERR_BAD_REQUEST,e)):u.send(o||null)}))}};M.forEach(je,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const Ce=e=>`- ${e}`,ke=e=>M.isFunction(e)||null===e||!1===e,Pe=e=>{e=M.isArray(e)?e:[e];const{length:t}=e;let n,o;const r={};for(let i=0;i<t;i++){let t;if(n=e[i],o=n,!ke(n)&&(o=je[(t=String(n)).toLowerCase()],void 0===o))throw new K(`Unknown adapter '${t}'`);if(o)break;r[t||"#"+i]=o}if(!o){const e=Object.entries(r).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(Ce).join("\n"):" "+Ce(e[0]):"as no adapter specified";throw new K("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return o};function Re(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ee(null,e)}function Ae(e){return Re(e),e.headers=ve.from(e.headers),e.data=be.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Pe(e.adapter||de.adapter)(e).then((function(t){return Re(e),t.data=be.call(e,e.transformResponse,t),t.headers=ve.from(t.headers),t}),(function(t){return _e(t)||(Re(e),t&&t.response&&(t.response.data=be.call(e,e.transformResponse,t.response),t.response.headers=ve.from(t.response.headers))),Promise.reject(t)}))}const Te=e=>e instanceof ve?{...e}:e;function Ne(e,t){t=t||{};const n={};function o(e,t,n){return M.isPlainObject(e)&&M.isPlainObject(t)?M.merge.call({caseless:n},e,t):M.isPlainObject(t)?M.merge({},t):M.isArray(t)?t.slice():t}function r(e,t,n){return M.isUndefined(t)?M.isUndefined(e)?void 0:o(void 0,e,n):o(e,t,n)}function i(e,t){if(!M.isUndefined(t))return o(void 0,t)}function s(e,t){return M.isUndefined(t)?M.isUndefined(e)?void 0:o(void 0,e):o(void 0,t)}function a(n,r,i){return i in t?o(n,r):i in e?o(void 0,n):void 0}const c={url:i,method:i,data:i,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t)=>r(Te(e),Te(t),!0)};return M.forEach(Object.keys(Object.assign({},e,t)),(function(o){const i=c[o]||r,s=i(e[o],t[o],o);M.isUndefined(s)&&i!==a||(n[o]=s)})),n}const Le={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Le[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const Fe={};Le.transitional=function(e,t,n){function o(e,t){return"[Axios v1.6.8] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,r,i)=>{if(!1===e)throw new K(o(r," has been removed"+(t?" in "+t:"")),K.ERR_DEPRECATED);return t&&!Fe[r]&&(Fe[r]=!0,console.warn(o(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,i)}};const De={assertOptions:function(e,t,n){if("object"!=typeof e)throw new K("options must be an object",K.ERR_BAD_OPTION_VALUE);const o=Object.keys(e);let r=o.length;for(;r-- >0;){const i=o[r],s=t[i];if(s){const t=e[i],n=void 0===t||s(t,i,e);if(!0!==n)throw new K("option "+i+" must be "+n,K.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new K("Unknown option "+i,K.ERR_BAD_OPTION)}},validators:Le},Be=De.validators;class Me{constructor(e){this.defaults=e,this.interceptors={request:new te,response:new te}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t;Error.captureStackTrace?Error.captureStackTrace(t={}):t=new Error;const n=t.stack?t.stack.replace(/^.+\n/,""):"";e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Ne(this.defaults,t);const{transitional:n,paramsSerializer:o,headers:r}=t;void 0!==n&&De.assertOptions(n,{silentJSONParsing:Be.transitional(Be.boolean),forcedJSONParsing:Be.transitional(Be.boolean),clarifyTimeoutError:Be.transitional(Be.boolean)},!1),null!=o&&(M.isFunction(o)?t.paramsSerializer={serialize:o}:De.assertOptions(o,{encode:Be.function,serialize:Be.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let i=r&&M.merge(r.common,r[t.method]);r&&M.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete r[e]})),t.headers=ve.concat(i,r);const s=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,d=0;if(!a){const e=[Ae.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=s.length;let p=t;for(d=0;d<u;){const e=s[d++],t=s[d++];try{p=e(p)}catch(e){t.call(this,e);break}}try{l=Ae.call(this,p)}catch(e){return Promise.reject(e)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return ee(Se((e=Ne(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}M.forEach(["delete","get","head","options"],(function(e){Me.prototype[e]=function(t,n){return this.request(Ne(n||{},{method:e,url:t,data:(n||{}).data}))}})),M.forEach(["post","put","patch"],(function(e){function t(t){return function(n,o,r){return this.request(Ne(r||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:o}))}}Me.prototype[e]=t(),Me.prototype[e+"Form"]=t(!0)}));const $e=Me;class Ue{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const o=new Promise((e=>{n.subscribe(e),t=e})).then(e);return o.cancel=function(){n.unsubscribe(t)},o},e((function(e,o,r){n.reason||(n.reason=new Ee(e,o,r),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new Ue((function(t){e=t})),cancel:e}}}const qe=Ue,Ke={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ke).forEach((([e,t])=>{Ke[t]=e}));const Ze=Ke,We=function e(t){const n=new $e(t),o=i($e.prototype.request,n);return M.extend(o,$e.prototype,n,{allOwnKeys:!0}),M.extend(o,n,null,{allOwnKeys:!0}),o.create=function(n){return e(Ne(t,n))},o}(de);We.Axios=$e,We.CanceledError=Ee,We.CancelToken=qe,We.isCancel=_e,We.VERSION="1.6.8",We.toFormData=z,We.AxiosError=K,We.Cancel=We.CanceledError,We.all=function(e){return Promise.all(e)},We.spread=function(e){return function(t){return e.apply(null,t)}},We.isAxiosError=function(e){return M.isObject(e)&&!0===e.isAxiosError},We.mergeConfig=Ne,We.AxiosHeaders=ve,We.formToJSON=e=>le(M.isHTMLForm(e)?new FormData(e):e),We.getAdapter=Pe,We.HttpStatusCode=Ze,We.default=We;const Je=We;function He(e){return He="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},He(e)}function ze(){ze=function(){return t};var e,t={},n=Object.prototype,o=n.hasOwnProperty,r=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},s=i.iterator||"@@iterator",a=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function l(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function(e,t,n){return e[t]=n}}function u(e,t,n,o){var i=t&&t.prototype instanceof y?t:y,s=Object.create(i.prototype),a=new P(o||[]);return r(s,"_invoke",{value:O(e,n,a)}),s}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var p="suspendedStart",f="suspendedYield",m="executing",h="completed",g={};function y(){}function v(){}function b(){}var _={};l(_,s,(function(){return this}));var w=Object.getPrototypeOf,E=w&&w(w(R([])));E&&E!==n&&o.call(E,s)&&(_=E);var x=b.prototype=y.prototype=Object.create(_);function S(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function I(e,t){function n(r,i,s,a){var c=d(e[r],e,i);if("throw"!==c.type){var l=c.arg,u=l.value;return u&&"object"==He(u)&&o.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,s,a)}),(function(e){n("throw",e,s,a)})):t.resolve(u).then((function(e){l.value=e,s(l)}),(function(e){return n("throw",e,s,a)}))}a(c.arg)}var i;r(this,"_invoke",{value:function(e,o){function r(){return new t((function(t,r){n(e,o,t,r)}))}return i=i?i.then(r,r):r()}})}function O(t,n,o){var r=p;return function(i,s){if(r===m)throw Error("Generator is already running");if(r===h){if("throw"===i)throw s;return{value:e,done:!0}}for(o.method=i,o.arg=s;;){var a=o.delegate;if(a){var c=j(a,o);if(c){if(c===g)continue;return c}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(r===p)throw r=h,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);r=m;var l=d(t,n,o);if("normal"===l.type){if(r=o.done?h:f,l.arg===g)continue;return{value:l.arg,done:o.done}}"throw"===l.type&&(r=h,o.method="throw",o.arg=l.arg)}}}function j(t,n){var o=n.method,r=t.iterator[o];if(r===e)return n.delegate=null,"throw"===o&&t.iterator.return&&(n.method="return",n.arg=e,j(t,n),"throw"===n.method)||"return"!==o&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+o+"' method")),g;var i=d(r,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,g;var s=i.arg;return s?s.done?(n[t.resultName]=s.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function C(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function k(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function P(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(C,this),this.reset(!0)}function R(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var r=-1,i=function n(){for(;++r<t.length;)if(o.call(t,r))return n.value=t[r],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(He(t)+" is not iterable")}return v.prototype=b,r(x,"constructor",{value:b,configurable:!0}),r(b,"constructor",{value:v,configurable:!0}),v.displayName=l(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===v||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,l(e,c,"GeneratorFunction")),e.prototype=Object.create(x),e},t.awrap=function(e){return{__await:e}},S(I.prototype),l(I.prototype,a,(function(){return this})),t.AsyncIterator=I,t.async=function(e,n,o,r,i){void 0===i&&(i=Promise);var s=new I(u(e,n,o,r),i);return t.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},S(x),l(x,c,"Generator"),l(x,s,(function(){return this})),l(x,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var o in t)n.push(o);return n.reverse(),function e(){for(;n.length;){var o=n.pop();if(o in t)return e.value=o,e.done=!1,e}return e.done=!0,e}},t.values=R,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(k),!t)for(var n in this)"t"===n.charAt(0)&&o.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function r(o,r){return a.type="throw",a.arg=t,n.next=o,r&&(n.method="next",n.arg=e),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var s=this.tryEntries[i],a=s.completion;if("root"===s.tryLoc)return r("end");if(s.tryLoc<=this.prev){var c=o.call(s,"catchLoc"),l=o.call(s,"finallyLoc");if(c&&l){if(this.prev<s.catchLoc)return r(s.catchLoc,!0);if(this.prev<s.finallyLoc)return r(s.finallyLoc)}else if(c){if(this.prev<s.catchLoc)return r(s.catchLoc,!0)}else{if(!l)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return r(s.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&o.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var s=i?i.completion:{};return s.type=e,s.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),k(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var r=o.arg;k(n)}return r}}throw Error("illegal catch attempt")},delegateYield:function(t,n,o){return this.delegate={iterator:R(t),resultName:n,nextLoc:o},"next"===this.method&&(this.arg=e),g}},t}function Ve(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Ge(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ve(Object(n),!0).forEach((function(t){var o,r,i,s;o=e,r=t,i=n[t],s=function(e,t){if("object"!=He(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var o=n.call(e,"string");if("object"!=He(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r),(r="symbol"==He(s)?s:s+"")in o?Object.defineProperty(o,r,{value:i,enumerable:!0,configurable:!0,writable:!0}):o[r]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ve(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Xe(e,t,n,o,r,i,s){try{var a=e[i](s),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(o,r)}function Qe(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,s,a=[],c=!0,l=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(a.push(o.value),a.length!==t);c=!0);}catch(e){l=!0,r=e}finally{try{if(!c&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw r}}return a}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ye(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ye(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ye(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function et(){var e="",n=Qe((0,t.useState)(null),2),o=n[0],i=n[1],s=Qe((0,t.useState)(null),2),a=(s[0],s[1]);function c(){var t;return t=ze().mark((function t(){var n;return ze().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Je.get("/linkus-sys-user/sysUserCtrl/queryByLoginName.action");case 3:n=t.sent,i(n.data),console.log("----调用中----"),e=localStorage.getItem("--mybricks-toJSON--"),t.next=12;break;case 9:t.prev=9,t.t0=t.catch(0),a(t.t0.message);case 12:return t.prev=12,t.finish(12);case 14:case"end":return t.stop()}}),t,null,[[0,9,12,14]])})),c=function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function s(e){Xe(i,o,r,s,a,"next",e)}function a(e){Xe(i,o,r,s,a,"throw",e)}s(void 0)}))},c.apply(this,arguments)}if(console.log("----调用之前----"),(0,t.useEffect)((function(){!function(){c.apply(this,arguments)}()}),[]),console.log("----调用后----"),console.log(o),o)return(0,r.render)(e,{env:{i18n:function(e){return e},uploadFile:function(e){},callConnector:function(t,n){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=window[t.connectorName]||window["@mybricks/plugins/service"],i=(e.plugins[t.connectorName]||[]).find((function(e){return e.id===t.id}));return i?r.call(Ge(Ge({},t),i),n,o):Promise.reject("找不到对应连接器 Script 执行脚本.")}}})}var tt=document.getElementById("root");if(!tt)throw new Error("缺少ID为root的dom节点");(0,o.render)(n().createElement(et,null),tt)})()})();