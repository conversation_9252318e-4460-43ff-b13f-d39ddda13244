<!DOCTYPE html>
<html>
  <head>
    <title>政企业务服务支撑系统</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="css/data_styles.css" type="text/css" rel="stylesheet"/>
    <link href="css/detailpage_styles.css" type="text/css" rel="stylesheet"/>
    <!--
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/详情页/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  -->
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (矩形) -->
      <div id="u48" class="ax_default box_3">
        <div id="u48_div" class=""></div>
        <div id="u48_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u49" class="ax_default _一级标题">
        <div id="u49_div" class=""></div>
        <div id="u49_text" class="text ">
          <p><span>政企业务服务支撑系统</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u50" class="ax_default label">
        <div id="u50_div" class=""></div>
        <div id="u50_text" class="text ">
          <p><span>GESOS</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u51" class="ax_default _三级标题">
        <div id="u51_div" class=""></div>
        <div id="u51_text" class="text ">
          <p><span>产品信息</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u52" class="ax_default _三级标题">
        <div id="u52_div" class=""></div>
        <div id="u52_text" class="text ">
          <p><span>产品概述</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u53" class="ax_default _文本段落">
        <div id="u53_div" class=""></div>
        <div id="u53_text" class="text ">
          <p>政企业务服务支撑系统基于数据中台的政企运营场景应用，从数据中台采集数据并整合，构建政企运营分析模型库，根据一定的算法处理汇聚再形成政企运营标签库、指标库，形成符合政企运营要求的信息，最终达到业务流程透明化、企业内部协同管控高效化、数据驱动的运营智慧化。通过面向不同维度、不同视角的指挥大屏，由点及面实现一体化看数和调度机制。通过透明化实时查询、售中售后透明化视图致力于解决一线需求。为政企运营人员提供多维度分析信息，赋能一线智慧化运营。</p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u54" class="ax_default _形状">
        <img id="u54_img" class="img " src="images/u9.svg"/>
        <div id="u54_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u55" class="ax_default _形状">
        <img id="u55_img" class="img " src="images/u9.svg"/>
        <div id="u55_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u56" class="ax_default _文本段落">
        <div id="u56_div" class=""></div>
        <div id="u56_text" class="text ">
          <p><span>定制产品</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u57" class="ax_default _文本段落">
        <div id="u57_div" class=""></div>
        <div id="u57_text" class="text ">
          <p><span>产品分类</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u58" class="ax_default _文本段落">
        <div id="u58_div" class=""></div>
        <div id="u58_text" class="text ">
          <p><span>目录编码</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u59" class="ax_default _文本段落">
        <div id="u59_div" class=""></div>
        <div id="u59_text" class="text ">
          <p><span>版本号</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u60" class="ax_default" data-left="626" data-top="250" data-width="60" data-height="122">

        <!-- Unnamed (矩形) -->
        <div id="u61" class="ax_default _文本段落">
          <div id="u61_div" class=""></div>
          <div id="u61_text" class="text ">
            <p><span>归属部门</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u62" class="ax_default _文本段落">
          <div id="u62_div" class=""></div>
          <div id="u62_text" class="text ">
            <p><span>CTC</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u63" class="ax_default image">
          <img id="u63_img" class="img " src="images/u18.png"/>
          <div id="u63_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u64" class="ax_default _文本段落">
        <div id="u64_div" class=""></div>
        <div id="u64_text" class="text ">
          <p><span>联系方式</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u65" class="ax_default _文本段落">
        <div id="u65_div" class=""></div>
        <div id="u65_text" class="text ">
          <p><span>P1912190001</span></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u66" class="ax_default _文本段落">
        <div id="u66_div" class=""></div>
        <div id="u66_text" class="text ">
          <p><span>v1.0</span></p>
        </div>
      </div>

      <!-- Unnamed (组合) -->
      <div id="u67" class="ax_default" data-left="813" data-top="250" data-width="71" data-height="122">

        <!-- Unnamed (矩形) -->
        <div id="u68" class="ax_default _文本段落">
          <div id="u68_div" class=""></div>
          <div id="u68_text" class="text ">
            <p><span>产品负责人</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u69" class="ax_default _文本段落">
          <div id="u69_div" class=""></div>
          <div id="u69_text" class="text ">
            <p><span>杨武</span></p>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u70" class="ax_default image">
          <img id="u70_img" class="img " src="images/u25.png"/>
          <div id="u70_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u71" class="ax_default _文本段落">
        <div id="u71_div" class=""></div>
        <div id="u71_text" class="text ">
          <p><span><EMAIL></span></p><p><span>18900727318</span></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u72" class="ax_default image">
        <img id="u72_img" class="img " src="images/u27.png"/>
        <div id="u72_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u73" class="ax_default image">
        <img id="u73_img" class="img " src="images/u28.png"/>
        <div id="u73_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u74" class="ax_default image">
        <img id="u74_img" class="img " src="images/u29.png"/>
        <div id="u74_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u75" class="ax_default image">
        <img id="u75_img" class="img " src="images/u30.png"/>
        <div id="u75_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <!--<script src="resources/scripts/axure/ios.js"></script>-->
  </body>
</html>
