(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-134232eb"],{1799:function(e,a,t){},2532:function(e,a,t){"use strict";var o=t("23e7"),s=t("5a34"),i=t("1d80"),r=t("ab13");o({target:"String",proto:!0,forced:!r("includes")},{includes:function(e){return!!~String(i(this)).indexOf(s(e),arguments.length>1?arguments[1]:void 0)}})},"5a34":function(e,a,t){var o=t("44e7");e.exports=function(e){if(o(e))throw TypeError("The method doesn't accept regular expressions");return e}},"9a38":function(e,a,t){"use strict";t("a876")},a876:function(e,a,t){},ab13:function(e,a,t){var o=t("b622"),s=o("match");e.exports=function(e){var a=/./;try{"/./"[e](a)}catch(t){try{return a[s]=!1,"/./"[e](a)}catch(o){}}return!1}},ba22:function(e,a,t){"use strict";t("1799")},baa5:function(e,a,t){var o=t("23e7"),s=t("e58c");o({target:"Array",proto:!0,forced:s!==[].lastIndexOf},{lastIndexOf:s})},d857:function(e,a,t){"use strict";t.r(a);var o=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"proResAllocation-warp"},[t("div",{staticClass:"fifth-line"},[e._m(0),t("div",{staticClass:"bottom-first-line-txt"},[e._v("项目")]),t("span",{staticClass:"checkName"},[e._v(e._s(e.projectRadio))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:function(a){return e.selectPrj()}}}),t("van-popup",{staticStyle:{height:"700px","padding-top":"4px"},attrs:{round:"",position:"bottom","show-count":!1},model:{value:e.projectPopupShow,callback:function(a){e.projectPopupShow=a},expression:"projectPopupShow"}},[t("div",{staticClass:"list-div"},[t("div",{staticClass:"list-title"},[t("input",{ref:"prjName",attrs:{type:"text",placeholder:"请输入项目编码或项目名称"},on:{input:e.prjSearch}}),t("span",{on:{click:e.prjSearch}},[e._v("搜索")])]),e.showDefaultPrj?t("div",{staticClass:"list-body"},[t("van-list",{attrs:{finished:e.defaultPrjFinished,"finished-text":"没有更多了!","immediate-check":!1},on:{load:function(a){e.defaultPrjLoading=!1}},model:{value:e.defaultPrjLoading,callback:function(a){e.defaultPrjLoading=a},expression:"defaultPrjLoading"}},[t("van-radio-group",{model:{value:e.defaultProjectRadio,callback:function(a){e.defaultProjectRadio=a},expression:"defaultProjectRadio"}},e._l(e.prjList,(function(a,o){return t("div",{staticClass:"radio-box",on:{click:function(t){return e.defaultPrjRadioClick(a)}}},[t("van-radio",{attrs:{name:a.projectName,"icon-size":26}},["已关闭"===a.closedDateValue?t("span",{staticStyle:{color:"#cccccc"}},[e._v("已关闭")]):e._e(),t("span",{staticClass:"radio-idName"},[e._v(e._s(a.projectCode))]),t("span",{staticClass:"radio-bandType"},[e._v(e._s("/"+a.projectName))])])],1)})),0)],1)],1):e._e(),e.showDefaultPrj?e._e():t("div",{staticClass:"list-body"},[t("van-list",{attrs:{finished:e.prjFinished,"finished-text":"没有更多了!","immediate-check":!1},on:{load:function(a){return e.prjOnLoad()}},model:{value:e.prjLoading,callback:function(a){e.prjLoading=a},expression:"prjLoading"}},[t("van-radio-group",{model:{value:e.projectRadio,callback:function(a){e.projectRadio=a},expression:"projectRadio"}},e._l(e.prjList,(function(a,o){return t("div",{staticClass:"radio-box",on:{click:function(t){return e.prjRadioClick(a)}}},[t("van-radio",{attrs:{name:a.projectName,"icon-size":26}},[t("span",{staticClass:"radio-idName"},[e._v(e._s(a.projectCode))]),t("span",{staticClass:"radio-bandType"},[e._v(e._s("/"+a.projectName))])])],1)})),0)],1)],1)])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.prjGroupShow,expression:"prjGroupShow"}],staticClass:"prjGroup"},[e._m(1),t("div",{staticClass:"bottom-first-line-txt"},[e._v("组织架构")]),t("span",{staticClass:"checkName"},[e._v(e._s(e.prjGroupChecked))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:e.selectPrjGroup}}),t("van-popup",{staticStyle:{height:"500px","padding-top":"4px"},attrs:{round:"",position:"bottom","show-count":!1},model:{value:e.prjGroupPopupShow,callback:function(a){e.prjGroupPopupShow=a},expression:"prjGroupPopupShow"}},[t("div",{staticClass:"list-div"},[t("div",{staticClass:"list-title"},[t("div",{staticClass:"title"},e._l(e.prjGroupRoute,(function(a,o){return t("a",{attrs:{id:"level"},on:{click:function(t){return e.toThisLevel(a.id,o)}}},[e._v(" "+e._s(o>0?">":"")+e._s(a.name)+" ")])})),0)]),t("div",{staticClass:"list-body"},[t("van-radio-group",{directives:[{name:"show",rawName:"v-show",value:e.firstRadioShow,expression:"firstRadioShow"}],on:{change:e.firstRadioChange},model:{value:e.prjGroupChecked,callback:function(a){e.prjGroupChecked=a},expression:"prjGroupChecked"}},e._l(e.groupList,(function(a,o){return t("div",{staticClass:"radio-box",on:{click:function(t){return e.prjGroupRadioClick(a.id)}}},[t("van-radio",{directives:[{name:"show",rawName:"v-show",value:!!a.title,expression:"!!item.title"}],attrs:{name:a.title,"icon-size":26}},[t("span",{staticClass:"radio-idName"},[e._v(e._s(a.title))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:a.children&&a.children.length>0,expression:"item.children && item.children.length >0"}],staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:function(t){return e.firstJiantouClick(a)}}})],1)})),0)],1)])])],1),t("div",{staticClass:"fourth-line"},[t("div",{staticClass:"fourth-line-type"},[e._v("申请类型")]),t("div",{staticClass:"fourth-line-txt"},[t("span",[e._v(e._s(e.checked?"进入项目":"退出项目"))]),t("van-switch",{attrs:{size:"24px"},model:{value:e.checked,callback:function(a){e.checked=a},expression:"checked"}})],1)]),t("div",{staticClass:"background-line"}),t("div",{staticClass:"third-line"},[e._m(2),t("div",{staticClass:"bottom-first-line-txt"},[e._v("人员")]),t("span",{staticClass:"checkName"},[e._v(e._s(e.peopleRadio))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:function(a){return e.selectPeople()}}}),t("van-popup",{staticStyle:{height:"700px","padding-top":"4px"},attrs:{round:"",position:"bottom","show-count":!1},model:{value:e.peoplePopupShow,callback:function(a){e.peoplePopupShow=a},expression:"peoplePopupShow"}},[t("div",{staticClass:"list-div"},[t("div",{staticClass:"list-title"},[t("input",{ref:"peopleName",attrs:{type:"text",placeholder:"请输入工号/姓名/NT账号"}}),t("span",{on:{click:e.peopleSearch}},[e._v("搜索")])]),t("div",{staticClass:"list-body"},[t("van-list",{attrs:{finished:e.peopleFinished,"finished-text":"没有更多了!","immediate-check":!1},on:{load:function(a){return e.peopleOnLoad()}},model:{value:e.peopleLoading,callback:function(a){e.peopleLoading=a},expression:"peopleLoading"}},[t("van-radio-group",{model:{value:e.peopleRadio,callback:function(a){e.peopleRadio=a},expression:"peopleRadio"}},e._l(e.peopleColumns,(function(a,o){return t("div",{staticClass:"radio-box",on:{click:function(t){return e.peopleRadioClick(a)}}},[t("van-radio",{attrs:{name:a.lastName,"icon-size":26}},[t("span",{staticClass:"people-idName"},[e._v(e._s(a.employeeNumber+" "+a.lastName))]),t("span",{staticClass:"peopel-bandType"},[e._v(e._s(a.bandType?"等级:"+a.bandType+"级":" "))])])],1)})),0)],1)],1)])])],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.checked,expression:"checked"}],staticClass:"bottom-first-line"},[e._m(3),t("div",{staticClass:"bottom-first-line-txt"},[e._v("角色")]),t("div",{staticClass:"line-select-txt"},[t("span",[e._v(e._s(e.prjResAllocationParam.role))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:function(a){return e.selectRole()}}}),t("VantPopSelect",{attrs:{popupShow:e.rolePopupShow,title:"角色",defaultIndex:0,columns:e.roleColumns},on:{onConfirm:e.roleConfirm,onCancel:e.roleCancel}})],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.checked,expression:"checked"}],staticClass:"bottom-second-line"},[e._m(4),t("div",{staticClass:"bottom-second-line-txt"},[e._v("任务阶段")]),t("div",{staticClass:"line-select-txt"},[t("span",[e._v(e._s(e.prjResAllocationParam.stage))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:e.stageSelect}}),t("VantPopSelect",{attrs:{popupShow:e.stagePopupShow,title:"技能","default-index":0,columns:e.stageColumns},on:{onConfirm:e.stageConfirm,onCancel:e.stageCancel}})],1)]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.checked,expression:"checked"}],staticClass:"bottom-second-line"},[t("div",{staticClass:"star"},[t("div",{directives:[{name:"show",rawName:"v-show",value:e.labelIsMust,expression:"labelIsMust"}],staticClass:"star-rating"},[e._m(5)])]),t("div",{directives:[{name:"show",rawName:"v-show",value:e.labelIsMust,expression:"labelIsMust"}],staticClass:"bottom-second-line-txt"},[e._v("产品标签")]),t("div",{directives:[{name:"show",rawName:"v-show",value:!e.labelIsMust,expression:"!labelIsMust"}],staticClass:"days-txt"},[e._v("产品标签")]),t("div",{staticClass:"line-select-txt"},[t("span",[e._v(e._s(e.prjResAllocationParam.label))]),t("div",{staticClass:"second-line-jiantou ico-top fontSize24 iconfont icon-youjiantou",on:{click:e.labelSelect}}),t("VantPopSelect",{attrs:{popupShow:e.labelPopupShow,title:"产品标签","default-index":2,columns:e.labelColumns},on:{onConfirm:e.labelConfirm,onCancel:e.labelCancel}})],1)]),t("div",{staticClass:"background-line"}),t("div",{staticClass:"bottom-third-line"},[e._m(6),t("div",{staticClass:"bottom-third-line-txt"},[e._v("开始日期")]),t("div",{staticClass:"line-select-txt"},[t("span",[e._v(e._s(e.prjResAllocationParam.startTime))]),t("div",{staticClass:"bottom-third-line-riqi ico-top fontSize24 iconfont icon-riqi",on:{click:function(a){return e.selectStartTime()}}}),t("VantPopDate",{attrs:{popupShow:e.startTimePopupShow,title:"开始时间",type:"date",startMinDate:e.startMinDate,startMaxDate:e.startMaxDate},on:{onConfirm:e.startTimeConfirm,onCancel:e.startTimeCancel}})],1)]),t("div",{staticClass:"bottom-fourth-line"},[e._m(7),t("div",{staticClass:"bottom-third-line-txt"},[e._v("结束日期")]),t("div",{staticClass:"line-select-txt"},[t("span",[e._v(e._s(e.prjResAllocationParam.endTime))]),t("div",{staticClass:"bottom-fourth-line-riqi ico-top fontSize24 iconfont icon-riqi",on:{click:function(a){return e.selectEndTime()}}}),t("VantPopDate",{attrs:{popupShow:e.endTimePopupShow,title:"结束时间",type:"date",startMinDate:e.startMinDate,startMaxDate:e.startMaxDate},on:{onConfirm:e.endTimeConfirm,onCancel:e.endTimeCancel}})],1)]),t("van-overlay",{attrs:{show:e.isLoading},on:{click:function(e){}}},[t("div",{staticClass:"loading"},[t("van-loading",{attrs:{type:"spinner",color:"#1989fa",size:"38px"}})],1)])],1)},s=[function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star-rating-top"},[t("span")])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])},function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"star"},[t("div",{staticClass:"star-rating"},[t("div",{staticClass:"star-rating-top"},[t("span")])])])}],i=(t("99af"),t("4160"),t("caad"),t("a15b"),t("baa5"),t("a434"),t("b0c0"),t("d3b7"),t("25f0"),t("2532"),t("159b"),t("dd7b")),r=t("501f"),n=t("40d6"),l=t("ab9e"),c=t("d399"),p={name:"prjResAllocation",components:{VantPopSelect:i["a"],VantPopDate:r["a"]},data:function(){return{currentUser:{},checked:!0,prjResAllocationParam:{prjId:"",prjCode:"",prjType:"",prjGroupId:"",projectName:"",applyType:"",people:{},role:"",roleId:"",skill:"",skillId:"",startTime:"",endTime:"",message:"",sourceId:"",lastOprtTypeId:"632d79cf62e0ea33fc9b890b",label:"",labelId:"",stage:"",stageId:"",projectStatusCode:"",prjPrdTagType:""},peoplePopupShow:!1,projectPopupShow:!1,prjGroupPopupShow:!1,prjGroupShow:!1,rolePopupShow:!1,skillPopupShow:!1,startTimePopupShow:!1,endTimePopupShow:!1,firstRadioShow:!0,startMinDate:new Date(2003,0,1),startMaxDate:new Date(2043,0,1),showPrjList:!1,showPeopleList:!1,chosenCoupon:-1,coupons:[],disabledCoupons:[],prjList:[],managedPrjList:[],peopleColumns:[],projectRadio:"",peopleRadio:"",prjGroupChecked:"",prjGroupList:[],groupList:[],prjGroupRoute:[{name:"所属小组",id:-1}],roleColumns:[],roleList:[],skillList:[],selectDate:[],checkboxPeoples:[],isLoading:!1,prjLoading:!1,prjFinished:!1,peopleLoading:!1,getDataLoading:!1,peopleFinished:!1,prjSearchParam:{pageSize:20,pageIndex:0},peopleSearchParam:{pageSize:20,pageIndex:0},prjIsSearch:!1,stagePopupShow:!1,labelPopupShow:!1,stageColumns:[],stageList:[],labelColumns:[],labelList:[],labelShow:!0,showDefaultPrj:!0,defaultPrjLoading:!1,defaultPrjFinished:!0,defaultProjectRadio:"",labelIsMust:!0,labelCanSelect:!0,buList:["ACC","EBC","DBC","DSaaS","亚信科技IDI DSaaS","亚信科技IDI DCU","DCU","iDigital","艾瑞数智CB","艾瑞数智MR","艾瑞数智IDD","艾瑞数智TMT","艾瑞数智CI","艾瑞数智CL","艾瑞数智RSC"]}},watch:{getCurrentUser:function(e,a){var t=this;t.currentUser=e}},computed:{index:function(e){function a(){return e.apply(this,arguments)}return a.toString=function(){return e.toString()},a}((function(){return index})),getCurrentUser:function(){return this.$store.state.currentUser}},created:function(){var e=this;e.currentUser=e.$store.state.currentUser,window.popVue=this,document.documentElement.addEventListener("touchstart",(function(e){e.touches.length>1&&e.preventDefault()}),{passive:!1});var a=0;document.documentElement.addEventListener("touchend",(function(e){var t=Date.now();t-a<=300&&e.preventDefault(),a=t}),{passive:!1})},beforeDestroy:function(){l["a"].$off("prjResAllocation")},mounted:function(){var e=this;l["a"].$on("prjResAllocation",(function(){var a=e,t="";""===a.prjResAllocationParam.prjId?t="请填写项目！":a.prjResAllocationParam.prjGroupId?a.prjResAllocationParam.people?!a.prjResAllocationParam.roleId&&a.checked?t="请填写角色！":a.prjResAllocationParam.startTime?a.prjResAllocationParam.endTime?a.prjResAllocationParam.endTime<a.prjResAllocationParam.startTime&&(t="结束日期必须大于等于开始日期！"):t="请填写结束日期！":t="请填写开始日期！":t="请填写人员！":t="请填写组织架构！",t?Object(c["a"])(t):a.checked?a.checkEmpsMdApplyConflict():a.prjResAllocationOut()}))},methods:{selectPeople:function(){var e=this;e.prjResAllocationParam.prjId?e.peoplePopupShow=!0:Object(c["a"])("请先选择项目!")},selectStartTime:function(){var e=this;e.prjResAllocationParam.bgId?e.startTimePopupShow=!0:Object(c["a"])("请先选择项目！")},selectEndTime:function(){var e=this;e.prjResAllocationParam.bgId?e.endTimePopupShow=!0:Object(c["a"])("请先选择项目！")},selectRole:function(){var e=this;e.prjResAllocationParam.prjId&&e.prjResAllocationParam.people&&e.prjResAllocationParam.people.userId?e.queryPrjUserRole():Object(c["a"])("请先选择项目和人员！")},selectPrj:function(){var e=this;e.projectPopupShow=!0,e.showDefaultPrj=!0,e.queryMyPrjInfo()},stageSelect:function(){var e=this;e.prjResAllocationParam.prjId?e.listTaskPhase():Object(c["a"])("请先选择具体项目后再进行任务阶段的选择!")},stageConfirm:function(e,a){var t=this;t.prjResAllocationParam.stage=e,t.prjResAllocationParam.stageId=t.stageList[a].taskPhaseId,t.stagePopupShow=!1},stageCancel:function(){var e=this;e.stagePopupShow=!1},labelSelect:function(){var e=this;e.labelCanSelect&&(e.prjResAllocationParam.prjId&&e.prjResAllocationParam.people.jobCode?e.labelPopupShow=!0:Object(c["a"])("请先选择具体的项目和人员再进行产品标签的选择"))},labelConfirm:function(e,a){var t=this;t.prjResAllocationParam.label=e,t.prjResAllocationParam.labelId=t.labelList[a].codeName,t.labelPopupShow=!1},labelCancel:function(){var e=this;e.labelPopupShow=!1},prjOnLoad:function(){var e=this;e.prjLoading=!0,e.prjSearchParam.pageIndex=e.prjSearchParam.pageIndex+1;var a=e.$refs.prjName.value;e.queryPrjInfo(a)},peopleOnLoad:function(){var e=this;if(!e.peopleFinished&&!e.getDataLoading){e.peopleSearchParam.pageIndex=e.peopleSearchParam.pageIndex+1;var a=e.$refs.peopleName.value;e.getDataLoading=!0,e.peopleLoading=!0,e.queryAiEmpByPage(a)}},getPeopleNameByCheckboxResult:function(){var e=this,a="";e.checkboxPeoples.forEach((function(e){a+=e.lastName+","}));var t=a.lastIndexOf(",");return t>0&&(a=a.substring(0,t)),a},firstJiantouClick:function(e){var a=this;a.groupList=e.children;var t={name:e.title,id:e.id};a.prjGroupRoute.push(t)},firstRadioChange:function(){var e=this;e.checkRadioFlag=!1,e.prjGroupPopupShow=!1},toThisLevel:function(e,a){var t=this;if(0===a)return t.groupList=t.prjGroupList.concat([]),void t.prjGroupRoute.splice(1,t.prjGroupRoute.length-1);if(a!==t.prjGroupRoute.length-1){var o=t.getCheckedNodeAndParents(t.prjGroupList,[],e);t.groupList=o.pop().children,t.prjGroupRoute.splice(a+1,t.prjGroupRoute.length-1-a)}},getCheckedNodeAndParents:function(e,a,t){for(var o=this,s=0;s<e.length;s++){var i=e[s];if(i.id===t)return a.push(i),a;if(i.children&&i.children.length>0){a.push(i);var r=o.getCheckedNodeAndParents(i.children,a,t);if(r)return r;a.pop()}}return!1},prjRadioClick:function(e){var a=this;a.prjResAllocationParam.label="",a.prjResAllocationParam.labelId="",a.prjResAllocationParam.stage="",a.stageColumns=[],a.prjResAllocationParam.prjId=e.dmpPrj.cid,a.prjResAllocationParam.prjCode=e.dmpPrj.codeName,a.prjResAllocationParam.prjType=e.projectType,a.prjResAllocationParam.bgId=e.bgId,a.prjResAllocationParam.projectStatusCode=e.projectStatusCode,a.prjResAllocationParam.prjPrdTagType=e.prjPrdTagType,a.getLimitDate(),a.prjGroupShow=!0,a.projectPopupShow=!1,a.queryPrjGroupByPrjId()},defaultPrjRadioClick:function(e){var a=this;a.prjResAllocationParam.label="",a.prjResAllocationParam.labelId="",a.prjResAllocationParam.stage="",a.stageColumns=[],a.prjResAllocationParam.role="",a.prjResAllocationParam.roleId="",a.roleColumns=[],a.prjResAllocationParam.prjId=e.dmpPrj.cid,a.prjResAllocationParam.prjCode=e.dmpPrj.codeName,a.prjResAllocationParam.prjType=e.projectType,a.prjResAllocationParam.bgId=e.bgId,a.prjResAllocationParam.projectStatusCode=e.projectStatusCode,a.prjResAllocationParam.prjPrdTagType=e.prjPrdTagType,a.projectRadio=a.defaultProjectRadio,a.prjGroupShow=!0,a.projectPopupShow=!1,a.getLimitDate(),a.queryPrjGroupByPrjId()},peopleRadioClick:function(e){var a=this;a.prjResAllocationParam.label="",a.prjResAllocationParam.labelId="",a.prjResAllocationParam.role="",a.prjResAllocationParam.roleId="",a.roleColumns=[],a.prjResAllocationParam.people=e.dmpUser,a.listPrdTag(),a.peoplePopupShow=!1},prjGroupRadioClick:function(e){var a=this;a.checkRadioFlag?(a.prjGroupChecked="",a.prjResAllocationParam.prjGroupId=""):(a.checkRadioFlag=!0,a.prjResAllocationParam.prjGroupId=e)},prjSearch:function(){var e=this,a=e.$refs.prjName.value;e.selectPrjFromPrjList(a)},selectPrjFromPrjList:function(e){var a=this,t=/[\u4E00-\u9FA5]+/;a.prjList=[],t.test(e)?a.managedPrjList.forEach((function(t){t.projectName&&t.projectName.includes(e)&&a.prjList.push(t)})):a.managedPrjList.forEach((function(t){t.projectCode&&t.projectCode.includes(e.toUpperCase())&&a.prjList.push(t)}))},peopleSearch:function(){var e=this;e.peopleColumns=[];var a=e.$refs.peopleName.value;e.queryAiEmpByPage(a)},selectPrjGroup:function(){var e=this;e.prjGroupPopupShow=!0,e.firstRadioShow=!0,e.prjGroupRoute=[{name:"所属小组",id:-1}],e.queryPrjGroupByPrjId()},roleConfirm:function(e,a){var t=this;t.prjResAllocationParam.role=e,t.prjResAllocationParam.roleId=t.roleList[a].cid,t.rolePopupShow=!1},roleCancel:function(){var e=this;e.rolePopupShow=!1},startTimeConfirm:function(e){var a=this,t=a.$moment(new Date(e)).format("YYYY-MM-DD");a.prjResAllocationParam.startTime=t,a.startTimePopupShow=!1},startTimeCancel:function(){var e=this;e.startTimePopupShow=!1},endTimeConfirm:function(e){var a=this,t=a.$moment(new Date(e)).format("YYYY-MM-DD");a.prjResAllocationParam.endTime=t,a.endTimePopupShow=!1},endTimeCancel:function(){var e=this;e.endTimePopupShow=!1},queryPrjInfo:function(e){var a=this,t=e.toUpperCase(),o={searchKey:t,pageSize:a.prjSearchParam.pageSize,pageIndex:a.prjSearchParam.pageIndex,bgId:a.currentUser.bgId};Object(n["v"])(o).then((function(e){var t=e.data||[];a.prjLoading=!1,a.prjIsSearch=!1,"[]"!==JSON.stringify(e)?a.prjList=a.prjList.concat(t):a.prjFinished=!0})).catch((function(e){a.prjLoading=!1,a.prjIsSearch=!1,"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},queryPrjUserRole:function(){var e=this,a={empIdList:e.prjResAllocationParam.people.userId,prjId:e.prjResAllocationParam.prjId};Object(n["w"])(a).then((function(a){if(a&&a.success){var t=a.data[e.prjResAllocationParam.people.userId]||[];e.roleList=t,1===t.length?(e.prjResAllocationParam.role=e.roleList[0].name,e.prjResAllocationParam.roleId=e.roleList[0].cid):(e.rolePopupShow=!0,e.roleColumns=[],t.forEach((function(a){return e.roleColumns.push(a.name)})))}else Object(c["a"])(a.message)})).catch((function(e){"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},queryAiEmpByPage:function(e){var a=this,t={},o=/[\u4E00-\u9FFF]+/,s=/^[0-9]*$/,i=/^[a-zA-Z]+[0-9]*/;t=o.test(e)?{bgId:a.currentUser.bgId,ccId:"",jobCode:"",loginName:"",userName:e,pageIndex:a.peopleSearchParam.pageIndex,pageSize:a.peopleSearchParam.pageSize}:s.test(e)?{bgId:a.currentUser.bgId,ccId:"",jobCode:e,loginName:"",userName:"",pageIndex:a.peopleSearchParam.pageIndex,pageSize:a.peopleSearchParam.pageSize}:i.test(e)?{bgId:a.currentUser.bgId,ccId:"",jobCode:"",loginName:e,userName:"",pageIndex:a.peopleSearchParam.pageIndex,pageSize:a.peopleSearchParam.pageSize}:{bgId:a.currentUser.bgId,ccId:"",jobCode:"",loginName:"",userName:"",pageIndex:a.peopleSearchParam.pageIndex,pageSize:a.peopleSearchParam.pageSize},Object(n["k"])(t).then((function(e){var t=e.data||{};t?(a.peopleColumns=a.peopleColumns.concat(t.objectList||[]),a.peopleFinished=!1):a.peopleFinished=!0,a.$nextTick((function(){setTimeout((function(){a.getDataLoading=!1,a.peopleLoading=!1}),3e3)}))})).catch((function(e){a.$nextTick((function(){setTimeout((function(){a.getDataLoading=!1,a.peopleLoading=!1}),3e3)})),"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},queryPrjGroupByPrjId:function(){var e=this,a={prjId:e.prjResAllocationParam.prjId};Object(n["u"])(a).then((function(a){var t=a.data||{};if("{}"!==JSON.stringify(t)){var o=[];Object(l["c"])([t],o,"label","value"),e.prjGroupList=o.children,e.groupList=o.children,e.prjResAllocationParam.prjGroupId=e.groupList[0].id,e.prjGroupChecked=e.groupList[0].title}})).catch((function(e){"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},checkEmpsMdApplyConflict:function(){var e=this;if(!e.isLoading){var a=[],t=e.prjResAllocationParam.startTime,o=e.prjResAllocationParam.endTime,s={inRangDates:t+","+o,empId:e.currentUser.id};a.push(s),e.isLoading=!0,Object(n["c"])(a).then((function(a){if(a&&a.success)if("{}"===JSON.stringify(a.data))e.prjResAllocation();else{e.isLoading=!1;var t=[];for(var o in a.data)if(a.data.hasOwnProperty(o)){var s=a.data[o].emp.userName+"/"+a.data[o].emp.jobCode+"在 "+a.data[o].bulkDates.join(",");t.push(s)}alert(t+"有在途的申请单，请先完成在途申请单再提交新的申请")}else e.isLoading=!1,Object(c["a"])(a.message)})).catch((function(a){e.isLoading=!1,a&&a.message&&Object(c["a"])(a.message)}))}},prjResAllocation:function(){var e=this,a={};""!==e.prjResAllocationParam.labelId?a[e.prjResAllocationParam.people.userId]={inRangDates:[e.prjResAllocationParam.startTime,e.prjResAllocationParam.endTime].toString(),roleId:e.prjResAllocationParam.roleId,skillId:e.prjResAllocationParam.stageId,prdId:e.prjResAllocationParam.labelId}:a[e.prjResAllocationParam.people.userId]={inRangDates:[e.prjResAllocationParam.startTime,e.prjResAllocationParam.endTime].toString(),roleId:e.prjResAllocationParam.roleId,skillId:e.prjResAllocationParam.stageId};var t={prjId:e.prjResAllocationParam.prjId,prjGroupId:e.prjResAllocationParam.prjGroupId,sourceId:e.$store.state.sourceList[2].id,lastOprtTypeId:e.prjResAllocationParam.lastOprtTypeId,desc:e.prjResAllocationParam.desc,optMuEmpMap:a};Object(n["i"])(t).then((function(a){e.applyAjax=!1;var t=a||{};e.isLoading=!1,"{}"!==JSON.stringify(t)&&("success"==t.status?(e.prjResAllocationParam={prjId:"",bgId:"",prjCode:"",prjType:"",projectName:"",applyType:"",people:{},role:"",roleId:"",skill:"",skillId:"",startTime:"",endTime:"",message:"",sourceId:"",lastOprtTypeId:"632d79cf62e0ea33fc9b890b",label:"",labelId:"",stage:"",stageId:""},e.projectRadio="",e.prjGroupChecked="",e.peopleRadio="",a.data&&a.data.failInReasonVoList&&a.data.failInReasonVoList.length>0?a.data.failInReasonVoList.forEach((function(e){alert(e.userName+" 角色为"+e.roleName+",任务阶段为"+e.taskPhaseName+",申请日期为"+e.applyDate+"的申请"+e.refuseType+(e.allRefuseReason?"。全部拒绝原因是"+e.allRefuseReason+"。":"")+(e.partRefuseReason?"。部分拒绝原因是"+e.partRefuseReason+"。":""))})):Object(c["a"])("申请成功！")):Object(c["a"])(t.message))})).catch((function(a){e.isLoading=!1,"ECONNABORTED"===a.code?Object(c["a"])("请求超时"):a&&a.message&&Object(c["a"])(a.message)}))},prjResAllocationOut:function(){var e=this;e.isLoading=!0;var a={prjId:e.prjResAllocationParam.prjId,empIds:e.currentUser.id,startDate:e.prjResAllocationParam.startTime,endDate:e.prjResAllocationParam.endTime,sourceId:e.$store.state.sourceList[2].id,lastOprtTypeId:e.prjResAllocationParam.lastOprtTypeId};Object(n["j"])(a).then((function(a){e.applyAjax=!1;var t=a||{};e.isLoading=!1,"{}"!==JSON.stringify(t)&&("success"==t.status?(Object(c["a"])("申请成功！"),e.prjResAllocationParam={prjId:"",prjCode:"",prjType:"",projectName:"",applyType:"",people:{},role:"",roleId:"",skill:"",skillId:"",startTime:"",endTime:"",message:"",sourceId:"",lastOprtTypeId:"632d79cf62e0ea33fc9b890b",label:"",labelId:"",stage:"",stageId:""},e.projectRadio="",e.prjGroupChecked="",e.peopleRadio=""):Object(c["a"])(t.message))})).catch((function(a){e.isLoading=!1,"ECONNABORTED"===a.code?Object(c["a"])("请求超时"):a&&a.message&&Object(c["a"])(a.message)}))},listTaskPhase:function(){var e=this,a=e.prjResAllocationParam.prjCode;Object(n["g"])(a).then((function(a){if("success"===a.status){var t=a.data||[];e.stageList=t,e.stageColumns=[],t.forEach((function(a){e.stageColumns.push(a.taskPhaseName)})),e.stagePopupShow=!0}else Object(c["a"])(a.message)})).catch((function(e){"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},listPrdTag:function(){var e=this,a={prjCode:e.prjResAllocationParam.prjCode,jobCodeList:[e.prjResAllocationParam.people.jobCode]};e.labelIsMust=!0,Object(n["f"])(a).then((function(a){if(a&&a.success&&a.data&&a.data[0]&&a.data[0].prdTagList){for(var t=a.data[0].prdTagList,o=0;o<t.length;o++){var s=t[o].name;"空"===s&&(e.labelIsMust=!1)}e.labelCanSelect=!1!==a.data[0].isModify;var i=!0===a.data[0].isDefault;if(i){var r=t.length-1,n=t[r];e.prjResAllocationParam.label=n.name,e.prjResAllocationParam.labelId=n.codeName}i||1!==a.data[0].prdTagList.length||(e.prjResAllocationParam.label=a.data[0].prdTagList[0].name,e.prjResAllocationParam.labelId=a.data[0].prdTagList[0].codeName),e.labelList=a.data[0].prdTagList,e.labelColumns=[],e.labelList.forEach((function(a){e.labelColumns.push(a.name)}))}else Object(c["a"])(a.message)})).catch((function(e){"ECONNABORTED"===e.code?Object(c["a"])("请求超时"):e&&e.message&&Object(c["a"])(e.message)}))},queryMyPrjInfo:function(e){var a=this,t=null!=e?{searchKey:e}:{};a.findPrjLoading=!0,Object(n["t"])(t).then((function(e){a.findPrjLoading=!1,e&&e.success?(e.data.forEach((function(e,a){null!=e.dmpPrj&&(e["prjId"]=e.dmpPrj.cid),"未关闭"==e["closedDateValue"]&&"涉及成本预算"==e["hasCostBudgetValue"]&&(e.subDesc="",e.no=0,e.prjEditAuth=!0),"不涉及成本预算"==e["hasCostBudgetValue"]&&(e.subDesc="不涉及预算    ",e.no=1,e.prjEditAuth=!1),"已关闭"==e["closedDateValue"]&&(e.subDesc="已关闭    ",e.no=2,e.prjEditAuth=!1)})),a.prjList=e.data,a.managedPrjList=e.data,a.prjList.sort((function(e,a){return e.no-a.no})),a.managedPrjList.sort((function(e,a){return e.no-a.no}))):Object(c["a"])(e.message)})).catch((function(e){}))},getLimitDate:function(){var e=this,a=e.prjResAllocationParam.bgId;Object(n["e"])(a).then((function(a){if(a&&a.success){var t=a.data;e.startMinDate=new Date(t);var o=e.startMinDate.getFullYear()+1;e.startMaxDate=new Date(o,11,31)}}))}}},d=p,u=(t("ba22"),t("9a38"),t("2877")),m=Object(u["a"])(d,o,s,!1,null,"e1a55cb8",null);a["default"]=m.exports},e58c:function(e,a,t){"use strict";var o=t("fc6a"),s=t("a691"),i=t("50c4"),r=t("a640"),n=t("ae40"),l=Math.min,c=[].lastIndexOf,p=!!c&&1/[1].lastIndexOf(1,-0)<0,d=r("lastIndexOf"),u=n("indexOf",{ACCESSORS:!0,1:0}),m=p||!d||!u;e.exports=m?function(e){if(p)return c.apply(this,arguments)||0;var a=o(this),t=i(a.length),r=t-1;for(arguments.length>1&&(r=l(r,s(arguments[1]))),r<0&&(r=t+r);r>=0;r--)if(r in a&&a[r]===e)return r||0;return-1}:c}}]);