//依赖defConst.js
Vue.component('filter-table', {
	template: 	'<div>																																		  '+
		'	<i-table v-if="(!!tableOverFlow && !!from && from == \'bizReportAnalysisCheckQuery\')" :style="tableOverFlowWidth? overFlowWidth3 : overFlowWidth2" ref="headTable" border :class="headClass.substr(1)" :data="filters" :columns="tableColumnsFilters" stripe          			  '+
		'		    @on-selection-change="headCheckBoxClick" @on-sort-change="onSortChange"></i-table>												  '+
		'	<i-table v-else-if="tableOverFlow" :style="tableOverFlowWidth? overFlowWidth1 : overFlowWidth2" ref="headTable" border :class="headClass.substr(1)" :data="filters" :columns="tableColumnsFilters" stripe          			  '+
		'		    @on-selection-change="headCheckBoxClick" @on-sort-change="onSortChange"></i-table>												  '+
		'	<i-table v-else ref="headTable" border :class="headClass.substr(1)" :data="filters" :columns="tableColumnsFilters" stripe          			  '+
		'		    @on-selection-change="headCheckBoxClick" @on-sort-change="onSortChange"></i-table>												  '+
		'	<i-table v-if="tableOverFlow" :stripe="!notStripe" ref="dataTable" border :class="dataClass.substr(1)" :row-class-name="rowClassName" :show-header=false :data="data" :columns="columns" :loading="loading" '+
		'			:height="height-10" @on-selection-change="dataCheckBoxClick" @on-row-dblclick="viewDetail" @on-row-click="selectRow"></i-table>												  '+
		'	<i-table v-else ref="dataTable" :stripe="!notStripe" border :class="dataClass.substr(1)" :row-class-name="rowClassName" :show-header=false :data="data" :columns="columns" :loading="loading" '+
		'			:height="height" @on-selection-change="dataCheckBoxClick" @on-row-dblclick="viewDetail" @on-row-click="selectRow"></i-table>												  '+
		'</div>'																													 				  ,
	props:['columns',//列描述数据对象
		'data',   //表格数据
		'selectDataMap',//下拉选项值集合
		'rowClassName',
		'loading',
		'height',
		'tableOverFlow',
		'tableOverFlowWidth',
		'eventScope',
		'rowClickSelect', //单击行即选中
		'disableDblclick', //禁用双击跳转业务视图
		'from',//来自哪个页面 batchPlanOperate.jsp用
		'prdId',//产品ID batchPlanOperate.jsp用
		'isLazy',
		'notStripe'
	],
	data: function(){
		return {
			filters: [{
				title: ''
			}],
			tableColumnsFilters: [],
			headClass : '.headClass',
			dataClass : '.dataClass',
			selectSourceData : [],
			overFlowWidth1: 'width: 2550px',
			overFlowWidth2: 'width: 1826px',
			overFlowWidth3: 'width: 3261px',
			tableMultipleSelectChange: false,
			tableMultipleSelectChangeValue: [],

			oprts: [{ id: '5bc581c4900add501a1412bd', name: '=' }, { id: '5bc58929900add501a1412be', name: '>' },
				{ id: '5bc58a7a900add501a1412c0', name: '<' }
			],
		}
	},
	methods:{
		headCheckBoxClick : function(selection){

			var sf = this;
			//头部表格复选框选择以后,数据表格复选框全部置为已选择状态,否则,全部置为非选择状态
			if(selection.length == 0){
				sf.$refs.dataTable.selectAll(false);
			}else{
				sf.$refs.dataTable.selectAll(true);
			}
			sf.selectData(sf.$refs.dataTable.getSelection());
		},
		dataCheckBoxClick : function(selection){

			var sf = this;
			var checkBox = $($(sf.headClass).find(".ivu-checkbox")[0]);
			if(sf.data && sf.data.length != 0 && sf.data.length == selection.length){
				checkBox.attr("class","ivu-checkbox ivu-checkbox-checked");
			}else{
				checkBox.attr("class","ivu-checkbox");
			}
			sf.selectData(selection);
		},
		selectData : function(selection){
			if(!!this.eventScope && this.eventScope == "linkedBizsPopup.js"){
				Vue.evtHub.$emit('select-data-linkedBizsPopup', selection);
			}else{
				Vue.evtHub.$emit('select-data', {
					selection : selection
				});
			}
		},
		viewDetail :  function(data,index){
			if(this.disableDblclick){
				Vue.evtHub.$emit('db-click-data-row', data);
				return;
			}
			var key = 'f_'+DEF_CONST.DEF_ID_FIELD_BIZ_ID;
			var bizId = data[key];
			//打补丁在查询定制页面bizId不为主键字段
			if(null == bizId) bizId = data["bizId"];
			if (!bizId){
				bizId = data["id"];
			}
			var bizViewUrl = linkus.location.biz +"/forward.action?t=biz/prdBizView&bizId=" + bizId;
			window.open(bizViewUrl,'_blank');
		},
		selectRow : function(data,index){
			var sf = this;
			Vue.evtHub.$emit('row-click', {
				rowIndex: index,
				rowData: data
			});
			if(sf.rowClickSelect && !data._disabled){
				sf.$refs.dataTable.toggleSelect(index);
			}
		},
		//重新加载数据
		load : function(fieldId,value,oprtId, type) {
			if(!!this.eventScope && this.eventScope == "linkedBizsPopup.js"){
				Vue.evtHub.$emit('load-data-linkedBizsPopup', {
					fieldId: fieldId,
					oprtId : oprtId,
					value: value,
					type: type
				});
			}else{
				Vue.evtHub.$emit('load-data', {
					fieldId: fieldId,
					oprtId : oprtId,
					value: value,
					type: type
				});
				this.$emit("update:loadData",{
					fieldId: fieldId,
					oprtId : oprtId,
					value: value,
					type: type});
			}
		},
		//验证输入框的值
		validInputValue : function(column, inputValue,oprtId, type) {

			var sf = this;
			var key = column.key;
			if(null != key){
				if(!!sf.from && sf.from == "bizReportAnalysisCheckQuery") { //评审查询页面
					sf.load(key, inputValue, oprtId, type);
					return;
				}
				var keyArr = key.split("_");
				if( null != keyArr && keyArr.length > 1 ){
					sf.load(keyArr[1],inputValue,oprtId, type);
				}else{
					sf.load(keyArr[0],inputValue,oprtId, type);
				}
			}

		},
		//排序
		onSortChange : function(column){

			Vue.evtHub.$emit('sort-change', {
				column : column
			});
		},
		//渲染过滤控件
		getRender : function(column, index, isChildrenColumn){

			var sf = this;
			var render;
			if('Input' == column.filter.type){

				render = function(h) {

					var defaultValue = column.filter.defaultValue;
					var inputValue = '';
					if (!!defaultValue) {
						inputValue = defaultValue;
					}
					var oldInputValue = inputValue;
					return h('Input', {
						props: {
							placeholder: '输入' + column.title,
							icon: 'ios-search-strong',
							value: inputValue,
							clearable: true,
						},
						on: {
							input:  function(val) {
								inputValue = val;
								if(!val) {
									oldInputValue = inputValue;
									sf.validInputValue(column, inputValue,DEF_CONST.DEF_ID_OPERATOR_LIKE);
								}
							},
							'on-click': function() {
								oldInputValue = inputValue;
								sf.validInputValue(column, inputValue,DEF_CONST.DEF_ID_OPERATOR_LIKE);
							},
							'on-enter': function(){
								oldInputValue = inputValue;
								sf.validInputValue(column, inputValue,DEF_CONST.DEF_ID_OPERATOR_LIKE);
							},
							/*'on-clear': function() {
                                oldInputValue = inputValue;
                                sf.validInputValue(column, inputValue,DEF_CONST.DEF_ID_OPERATOR_LIKE);
                            },*/
							'on-blur':function(){
								if(oldInputValue != inputValue){
									oldInputValue = inputValue;
									sf.validInputValue(column, inputValue,DEF_CONST.DEF_ID_OPERATOR_LIKE);
								}
							}
						}
					})
				};
			}
			else if('Select' == column.filter.type){
				render = function(h) {
					var key = column.key;
					var selectSourceData = [];
					if(null != key){
						var keyArr = key.split("_");
						if(keyArr.length>1){
							selectSourceData = sf.selectDataMap[keyArr[1]];
						}else{
							selectSourceData = sf.selectDataMap[keyArr[0]];
						}
					}

					if(!selectSourceData) selectSourceData = [];
					var options = selectSourceData.map(function(item) {
						return h("Option",{
							props:{
								value : item.value,
								label : item.label
							}
						})
					});
					return h('Select', {
						props: {
							placeholder: '请选择',
							filterable : true,
							clearable  : true,
							transfer   : true,
							//是否多选
							multiple	 : column.filter.multiple,
							transferClassName : "transfer-select-class",
						},
						on: {
							'on-change': function(option) {
								if(!!sf.from && sf.from == 'batchPlanOperate.html') {
									if(!column.filter.multiple){
										sf.validInputValue(column, option,DEF_CONST.DEF_ID_OPERATOR_IN);
									}else{
										sf.tableMultipleSelectChange = true;
										sf.tableMultipleSelectChangeValue = option;
									}
								}else {
									sf.validInputValue(column, option,DEF_CONST.DEF_ID_OPERATOR_IN);
								}
							},
							'on-open-change':function () {
								if(!!sf.from && sf.from == 'batchPlanOperate.html') {
									if(column.filter.multiple && sf.tableMultipleSelectChange){
										sf.validInputValue(column, sf.tableMultipleSelectChangeValue,DEF_CONST.DEF_ID_OPERATOR_IN);
										sf.tableMultipleSelectChange = false;
										sf.tableMultipleSelectChangeValue = [];
									}
								}
							}
						},

					},options);
				}
			}
			else if('DatePicker' == column.filter.type){
				var planHolder = '选择' + column.title;
				if(column.filter.placeHolder){
					planHolder = column.filter.placeHolder;
				}
				var type =  column.filter.datePickerType ? column.filter.datePickerType : 'daterange';
				var format =  column.filter.datePickerType ? '' : 'yyyy-MM-dd';
				var defaultsValue =  column.filter.defaultsValue ? column.filter.defaultsValue : '';
				var options =  column.filter.options ? column.filter.options : {};
				render = function(h) {

					return h('DatePicker', {
						props: {
							placeholder: planHolder,
							value:defaultsValue,
							type:type,
							format: format,
							options: options,
							placement: column.placement,
							confirm: false,
							transfer:true
						},
						on: {
							'on-change': function(date) {
								if(column.filter.datePickerType){
									sf.validInputValue(column, date,DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
								}else{
									var valueObject = {};
									valueObject["valueType"] = "betweenValue";
									valueObject["data"] = date[0]+"~"+date[1];
									if(date[0] == ''){
										sf.validInputValue(column, '',DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
									}else{
										sf.validInputValue(column, JSON.stringify(valueObject),DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
									}
								}

							}
						}
					});
				}
			}
			else if('DateTimePicker' == column.filter.type){
				var planHolder = '选择' + column.title;
				if(column.filter.placeHolder){
					planHolder = column.filter.placeHolder;
				}
				var type =  column.filter.datePickerType ? column.filter.datePickerType : 'datetimerange';
				var format =  column.filter.datePickerType ? '' : 'yyyy-MM-dd HH:mm:ss';
				var defaultsValue =  column.filter.defaultsValue ? column.filter.defaultsValue : '';
				var options =  column.filter.options ? column.filter.options : {};
				render = function(h) {

					return h('DatePicker', {
						props: {
							placeholder: planHolder,
							value:defaultsValue,
							type:type,
							format: format,
							options: options,
							placement: column.placement,
							confirm: false,
							transfer:true,
						},
						on: {
							'on-change': function(date) {
								if(!!sf.from && sf.from == 'businessNodeAnalysis' && !!date && date.length == 2
									&& !!date[0] && !!date[1] && type == 'datetimerange') {
									var endDates = date[1].split(" ");
									var endDateStr = endDates[1];
									if(endDateStr == '00:00:00') {
										date[1] = endDates[0] + ' ' + '23:59:59';
										column.filter.defaultsValue = date;
										if(!isChildrenColumn) {
											sf.$set(sf.columns, index, column);
										}else {
											var childrens = sf.columns[index].children;
											sf.$set(childrens, index, column);
										}
									}
								}
								if(column.filter.datePickerType){
									sf.validInputValue(column, date,DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
								}else{
									var valueObject = {};
									valueObject["valueType"] = "betweenValue";
									valueObject["data"] = date[0]+"~"+date[1];
									if(date[0] == ''){
										sf.validInputValue(column, '',DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
									}else{
										sf.validInputValue(column, JSON.stringify(valueObject),DEF_CONST.DEF_ID_OPERATOR_DATE_OPRTOR);
									}
								}

							}
						}
					});
				}
			}
			else if ('InputWithOperator' == column.filter.type) {
				render = function(h) {
					var valuesArray = [];

					var defaultValue = column.filter.defaultValue;
					var inputValue = defaultValue || '';
					var oldInputValue = inputValue;

					var OptionsData = [
						{ value: 'greaterOperator', label: '>' },
						{ value: 'lessOperator', label: '<' },
						{ value: 'equalOperator', label: '=' }
					];
					var defaultSelectValue = 'equalOperator';
					valuesArray[0] = defaultSelectValue;
					var options = OptionsData.map(function(item) {
						return h("Option", {
							props: {
								value: item.value,
								label: item.label
							}
						});
					});
					return h('div',[
						h('Select', {
							props: {
								filterable : true,
								transfer   : true,
								transferClassName : "transfer-select-class",
								value : column.filter.defaultOperatorValue,
							},
							style:{ width:'60px'},
							on: {
								'on-change': function(option) {
									valuesArray[0] = option;
									column.filter.defaultOperatorValue = valuesArray[0];
									sf.validInputValue(column, valuesArray,DEF_CONST.DEF_ID_OPERATOR_IN, "operator");
								}
							},
						},options),
						h('Input', {
							props: {
								placeholder: '输入' + column.title,
								icon: 'ios-search-strong',
								value: inputValue,
								clearable: true,
							},
							style:{ width:'120px'},
							on: {
								input:  function(val) {
									valuesArray[1] = val;
									if(!val) {
										oldInputValue = valuesArray[1];
										sf.validInputValue(column, valuesArray,DEF_CONST.DEF_ID_OPERATOR_LIKE, "num");
									}
								},
								'on-click': function() {
									oldInputValue = valuesArray[1];
									sf.validInputValue(column, valuesArray,DEF_CONST.DEF_ID_OPERATOR_LIKE, "num");
								},
								'on-enter': function(){
									oldInputValue = valuesArray[1];
									sf.validInputValue(column, valuesArray,DEF_CONST.DEF_ID_OPERATOR_LIKE, "num");
								},
								'on-blur':function(){
									if(oldInputValue != valuesArray[1]){
										oldInputValue = valuesArray[1];
										sf.validInputValue(column, valuesArray,DEF_CONST.DEF_ID_OPERATOR_LIKE, "num");
									}
								}
							}
						})]
					)
				};
			}
			return render;
		},
		renderTableHeader : function(){
			var sf = this;
			sf.tableColumnsFilters = [];
			for (var index = 0;index<sf.columns.length; index++) {
				var filter = {};
				/**
				 * 因为是采用的两个表的形式,过滤表中显示查询的条件输组件,表头显示的是数据表的表头,渲染的数据是传入的columns中的filter字段
				 * 数据表隐藏表头,只显示数据,渲染的数据是传入的columns中的key值
				 */
				//将传入的列描述数据对象(columns) 的title和width 复制给 过滤表的列描述数据对象(tableColumnsFilters)
				sf.$set(filter, 'key', sf.columns[index].key);
				if(sf.columns[index].key == 'selection'){
					sf.$set(filter, 'type', 'selection');
					sf.$set(filter, 'align', 'center');
				}else{
					sf.$set(filter, 'title', sf.columns[index].title);
				}
				if (sf.columns[index].width) {
					sf.$set(filter, 'width', sf.columns[index].width);
				}

				if(sf.columns[index].renderHeader) {
					sf.$set(filter, 'renderHeader', sf.columns[index].renderHeader);
				}

				if(!!sf.columns[index].children && sf.columns[index].children.length > 0) {
					var childrens = sf.columns[index].children;
					var filterChildrens = [];
					for(var j = 0; j < childrens.length; j++) {
						var filterChildren = {};
						var item = childrens[j];
						sf.$set(filterChildren, 'key', item.key);
						sf.$set(filterChildren, 'title', item.title);
						if(!!item.width) {
							sf.$set(filterChildren, 'width', item.width);
						}
						if(!!item.filter) {
							sf.$set(filterChildren, 'filter', item.filter);
						}
						if(!!item.placement) {
							sf.$set(filterChildren, 'placement', item.placement);
						}
						if(!!item.align) {
							sf.$set(filterChildren, 'align', item.align);
						}
						if(!!item.sortable) {
							sf.$set(filterChildren, 'sortable', item.sortable);
						}
						if(!sf.columns[index].filter && !!item.filter) {
							render = sf.getRender(filterChildren, j, true);
							sf.$set(filterChildren, 'render', render);
						}
						filterChildrens.push(filterChildren);
					}
					sf.$set(filter, 'children', filterChildrens);
				}
				if (sf.columns[index].align) {
					sf.$set(filter, 'align', sf.columns[index].align);
				}
				if (sf.columns[index].fixed) {
					sf.$set(filter, 'fixed', sf.columns[index].fixed);
				}
				var render = function(val) {};
				if(!!sf.columns[index].sortable){
					sf.$set(filter, 'sortable', sf.columns[index].sortable);
				}
				//如果存在 过滤选项
				if(!!sf.columns[index].filter) {
					render = sf.getRender(sf.columns[index], index, false);
					sf.$set(filter, 'render', render);
				}
				sf.tableColumnsFilters.push(filter);
			}
		}

	},
	created : function(){
		var sf = this;
		sf.$nextTick(function(){
			sf.renderTableHeader();
		});
	},
	watch:{
		columns : function() {
			var sf = this;
			sf.$nextTick(function(){
				sf.renderTableHeader();
			});
		},
		data : function(){
			var sf = this;
			sf.$refs.headTable.selectAll(false);
		}
	},
	mounted: function(){
		var sf = this;
		sf.$nextTick(function () {
			//隐藏过滤列的那个复选框
			var checkBox = $($(".headClass").find(".ivu-checkbox-inner")[1]);
			checkBox.attr("style","display:none");

			//使表头的滚动条与数据的滚动条同步滚动
			$(".dataClass").find(".ivu-table-body").scroll(function(e){
				$(".headClass").find(".ivu-table-body").scrollLeft($(this).scrollLeft());
				$(".headClass").find(".ivu-table-body").scrollTop($(this).scrollTop());


				var scrollTop = e.target.scrollTop;
				var scrollHeight =e.target.scrollHeight;
				var clientHeight = e.target.clientHeight;

				if((scrollHeight - scrollTop) - clientHeight  < 5 ){
					Vue.evtHub.$emit('data-scroll-bottom');
				}
			});

			//无数据时滚动-参考CmcVerOperate.jsp页面
			$(".dataClass").find(".ivu-table-tip").scroll(function(event){
				$(".headClass").find(".ivu-table-body").scrollLeft($(this).scrollLeft());
				$(".headClass").find(".ivu-table-body").scrollTop($(this).scrollTop());
			});
		});
	}
});





