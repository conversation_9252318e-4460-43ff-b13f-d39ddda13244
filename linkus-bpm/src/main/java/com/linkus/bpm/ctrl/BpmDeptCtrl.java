package com.linkus.bpm.ctrl;

import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.bpm.model.vo.DeptVo;
import com.linkus.bpm.service.IBpmDeptService;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RequestMapping("/bpmDept")
@RestController
public class BpmDeptCtrl extends CommonController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IBpmDeptService bpmDeptService;

    @PostMapping("/createDept.action")
    public CommonResult<TeSysDef> createDept(@RequestParam ObjectId parentDefId
            ,@RequestParam (required = false)ObjectId deptRespUserId, @RequestParam String deptName
            , @RequestParam(required = false) String desc, @RequestParam(required = false) Integer defNo) {
        TeSysUser sysUser = getSysUser();
        TeSysDef dept = bpmDeptService.createDept(parentDefId,deptRespUserId,deptName, desc, defNo,sysUser);
        return CommonResult.success(dept);
    }

    @PostMapping("/updateDept.action")
    public CommonResult<TeSysDef> updateDept(@RequestParam ObjectId defId
            , @RequestParam(required = false) ObjectId deptRespUserId
            , @RequestParam(required = false) ObjectId updateParentDefId
            , @RequestParam(required = false) String updateDeptName
            , @RequestParam(required = false) ObjectId updateUserId
            , @RequestParam(required = false) String desc
            , @RequestParam(required = false) Integer defNo) {
        TeSysUser sysUser = getSysUser();
        TeSysDef dept = bpmDeptService.updateDept(defId,deptRespUserId, updateParentDefId, updateDeptName, updateUserId,desc,defNo,sysUser);
        return CommonResult.success(dept);
    }

    @PostMapping("/deleteDept.action")
    public CommonResult<Void> deleteDept(@RequestParam ObjectId defId
            , @RequestParam(required = false) String desc) {
        TeSysUser sysUser = getSysUser();
        bpmDeptService.deleteDept(defId, desc, sysUser);
        return CommonResult.success();
    }

    @PostMapping("/queryRecord.action")
    public CommonResult<PageBean> queryRecord(@RequestParam(required = false) String startDate
            , @RequestParam(required = false) String endDate, @RequestParam(required = false) String keyWord
    , @RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        PageBean pageBean = bpmDeptService.queryRecord(startDate, endDate, keyWord, pageNo, pageSize);
        return CommonResult.success(pageBean);
    }

    @GetMapping ("/exportRecord.action")
    public void exportRecord(@RequestParam(required = false) String startDate
            , @RequestParam(required = false) String endDate, @RequestParam(required = false) String keyWord
            , HttpServletResponse response) throws IOException {
        bpmDeptService.exportRecord(startDate, endDate, keyWord,response);
    }

    @PostMapping("/queryDept.action")
    public CommonResult<List<DeptVo>> queryDept(@RequestParam(required = false) ObjectId defId) {
        TeSysUser sysUser = getSysUser();
        List<DeptVo> deptVos = bpmDeptService.queryDept(defId,sysUser);
        return CommonResult.success(deptVos);
    }

    @GetMapping ("/exportDept.action")
    public void exportDept(@RequestParam(required = false) ObjectId defId
            , HttpServletResponse response) throws IOException {
        TeSysUser sysUser = getSysUser();
        bpmDeptService.exportDept(defId,sysUser,response);
    }

    // 获取当前用户登录信息
    private TeSysUser getSysUser() {
        String casLoginUserName = getLoginName();
        if (StringUtil.isNull(casLoginUserName)) {
            return null;
        }
        return sysUserService.queryByLoginName(casLoginUserName);
    }
}
