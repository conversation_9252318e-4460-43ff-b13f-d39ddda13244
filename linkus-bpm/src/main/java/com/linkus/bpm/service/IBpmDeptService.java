package com.linkus.bpm.service;

import com.linkus.base.util.PageBean;
import com.linkus.bpm.model.vo.DeptVo;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface IBpmDeptService {

    TeSysDef createDept(ObjectId parentDefId,ObjectId deptRespUserId, String deptName, String desc,Integer defNo, TeSysUser loginUser);

    TeSysDef updateDept(ObjectId defId,ObjectId deptRespUserId, ObjectId updateParentDefId, String updateDeptName
            ,ObjectId updateUserId, String desc,Integer defNo, TeSysUser loginUser);

    void deleteDept(ObjectId defId,String desc, TeSysUser loginUser);

    PageBean queryRecord(String startDate, String endDate, String keyWord,Integer pageNo,Integer pageSize);
    void exportRecord(String startDate, String endDate, String keyWord, HttpServletResponse response) throws IOException;

    List<DeptVo> queryDept(ObjectId defId,TeSysUser sysUser);

    void exportDept(ObjectId defId,TeSysUser sysUser, HttpServletResponse response) throws IOException;

}
