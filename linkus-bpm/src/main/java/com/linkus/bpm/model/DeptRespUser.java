package com.linkus.bpm.model;

import org.bson.types.ObjectId;

import com.linkus.sysuser.model.TeSysUser;

/**
 * 部门责任人信息
 * 
 * <AUTHOR>
 *
 */
public class DeptRespUser {
	// 部门id
	private ObjectId deptId;
	// 部门名称
	private String deptName;
	// 部门负责人
	private TeSysUser resp;
	// 需要默认选择
	private boolean checked;

	public ObjectId getDeptId() {
		return deptId;
	}

	public void setDeptId(ObjectId deptId) {
		this.deptId = deptId;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public TeSysUser getResp() {
		return resp;
	}

	public void setResp(TeSysUser resp) {
		this.resp = resp;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

}
