"""
转换器测试
"""
import pytest
from pathlib import Path
from app.core.converters import <PERSON>x<PERSON>onverter, XlsxConverter, PptxConverter, MarkdownConverter


class TestDocxConverter:
    """Word文档转换器测试"""
    
    def test_supported_extensions(self):
        """测试支持的扩展名"""
        converter = DocxConverter()
        assert '.docx' in converter.supported_extensions
    
    def test_validate_input(self):
        """测试输入验证"""
        converter = DocxConverter()
        
        # 测试不存在的文件
        assert not converter.validate_input(Path("nonexistent.docx"))
        
        # 测试不支持的格式
        assert not converter.validate_input(Path("test.txt"))


class TestXlsxConverter:
    """Excel转换器测试"""
    
    def test_supported_extensions(self):
        """测试支持的扩展名"""
        converter = XlsxConverter()
        assert '.xlsx' in converter.supported_extensions
        assert '.xls' in converter.supported_extensions


class TestPptxConverter:
    """PowerPoint转换器测试"""
    
    def test_supported_extensions(self):
        """测试支持的扩展名"""
        converter = PptxConverter()
        assert '.pptx' in converter.supported_extensions
        assert '.ppt' in converter.supported_extensions


class TestMarkdownConverter:
    """Markdown转换器测试"""
    
    def test_supported_extensions(self):
        """测试支持的扩展名"""
        converter = MarkdownConverter()
        assert '.md' in converter.supported_extensions
        assert '.markdown' in converter.supported_extensions
    
    def test_extract_document_info(self):
        """测试文档信息提取"""
        converter = MarkdownConverter()
        
        # 测试基本标题提取
        content = "# 测试标题\n\n这是测试内容"
        info = converter._extract_document_info(content, Path("test.md"))
        assert info['title'] == "测试标题"
        assert info['lines_count'] == 3
        assert info['word_count'] == 6 