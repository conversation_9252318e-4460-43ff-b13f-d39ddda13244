package com.linkus.biz.kanban.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.util.DateUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;

import java.io.Serializable;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class TaskSprintVo implements Serializable {
    private static final long serialVersionUID = -6803936463459035033L;

    /**
     * 计算迭代工时
     *
     * @param taskSprint
     * @return
     */
    public static Integer calcWorkloadTotal(TaskSprintVo taskSprint) {
        if (Objects.isNull(taskSprint.sprintDayNum)
                || Objects.isNull(taskSprint.memberCount)
                || Objects.isNull(taskSprint.mhPerDay)) {
            return null;
        }
        return taskSprint.sprintDayNum * taskSprint.memberCount * taskSprint.mhPerDay;
    }

    /**
     * 迭代Id
     */
    private ObjectId id;

    /**
     * 迭代编码
     */
    private String code;

    /**
     * 迭代名称
     */
    private String name;

    /**
     * 迭代周期
     */
    private String cycle;

    /**
     * 成员数
     */
    private Integer memberCount;

    /**
     * 团队
     */
    private TeIdName scrumTeam;

    /**
     * 团队名称
     */
    private String scrumTeamName;

    /**
     * 计划工时
     */
    private Double planEffort;

    /**
     * 迭代工时
     * 迭代天数 * 成员数 * 日标准工时
     */
    private Integer workloadTotal;

    /**
     * 迭代天数
     */
    private Integer sprintDayNum;

    /**
     * 迭代周期
     */
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private List<Date> sprintDays;

    /**
     * 每日标准工时数（h）
     */
    private Integer mhPerDay;

    /**
     * 成员
     */
    private List<UserInputPercentVo> members;

    /**
     * 用户故事
     */
    private List<TaskUserStoryVo> userStorys;

    /**
     * 故事数
     */
    private int userStoryCount;

    /**
     * 任务数
     */
    private int taskCount;

    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCycle() {
        return cycle;
    }

    private void setCycle(String cycle) {
        this.cycle = cycle;
    }

    public Integer getMemberCount() {
        return memberCount;
    }

    private void setMemberCount(Integer memberCount) {
        this.memberCount = memberCount;
        this.setWorkloadTotal(TaskSprintVo.calcWorkloadTotal(this));
    }

    public TeIdName getScrumTeam() {
        return scrumTeam;
    }

    public void setScrumTeam(TeIdName scrumTeam) {
        this.scrumTeam = scrumTeam;
        if (Objects.nonNull(scrumTeam)) {
            this.setScrumTeamName(scrumTeam.getName());
        }
    }

    public String getScrumTeamName() {
        return scrumTeamName;
    }

    private void setScrumTeamName(String scrumTeamName) {
        this.scrumTeamName = scrumTeamName;
    }

    public Double getPlanEffort() {
        return planEffort;
    }

    public void setPlanEffort(Double planEffort) {
        this.planEffort = planEffort;
    }

    public Integer getWorkloadTotal() {
        return workloadTotal;
    }

    private void setWorkloadTotal(Integer workloadTotal) {
        this.workloadTotal = workloadTotal;
    }

    public Integer getSprintDayNum() {
        return sprintDayNum;
    }

    private void setSprintDayNum(Integer sprintDayNum) {
        this.sprintDayNum = sprintDayNum;
        this.setWorkloadTotal(TaskSprintVo.calcWorkloadTotal(this));
    }

    public List<Date> getSprintDays() {
        return sprintDays;
    }

    public void setSprintDays(List<Date> sprintDays) {
        this.sprintDays = sprintDays;
        if (CollectionUtils.isNotEmpty(sprintDays)) {
            sprintDays.sort(new Comparator<Date>() {
                @Override
                public int compare(Date o1, Date o2) {
                    if (o1 == null) {
                        return -1;
                    }
                    if (o2 == null) {
                        return 1;
                    }
                    return o1.compareTo(o2);
                }
            });
            int count = sprintDays.size();
            this.setCycle(DateUtil.formatDate2Str(sprintDays.get(0), DateUtil.DATE_FORMAT)
                    + "~"
                    + DateUtil.formatDate2Str(sprintDays.get(count - 1), DateUtil.DATE_FORMAT)
            );
            this.setSprintDayNum(count);
        }
    }

    public Integer getMhPerDay() {
        return mhPerDay;
    }

    public void setMhPerDay(Integer mhPerDay) {
        this.mhPerDay = mhPerDay;
        this.setWorkloadTotal(TaskSprintVo.calcWorkloadTotal(this));
    }

    public List<UserInputPercentVo> getMembers() {
        return members;
    }

    public void setMembers(List<UserInputPercentVo> members) {
        this.members = members;
        if (CollectionUtils.isNotEmpty(members)) {
            this.setMemberCount(members.size());
        }
    }

    public List<TaskUserStoryVo> getUserStorys() {
        return userStorys;
    }

    public void setUserStorys(List<TaskUserStoryVo> userStorys) {
        this.userStorys = userStorys;
        this.setUserStoryCount(CollectionUtils.isNotEmpty(userStorys) ? userStorys.size() : 0);
        if (CollectionUtils.isNotEmpty(userStorys)) {
            int taskCount = 0;
            for (TaskUserStoryVo userStory : userStorys) {
                taskCount += userStory.getTaskCount();
            }
            this.setTaskCount(taskCount);
        }
    }

    public int getUserStoryCount() {
        return userStoryCount;
    }

    private void setUserStoryCount(int userStoryCount) {
        this.userStoryCount = userStoryCount;
    }

    public int getTaskCount() {
        return taskCount;
    }

    private void setTaskCount(int taskCount) {
        this.taskCount = taskCount;
    }
}
