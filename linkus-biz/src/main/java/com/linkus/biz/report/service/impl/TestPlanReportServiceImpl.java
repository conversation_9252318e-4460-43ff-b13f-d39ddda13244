package com.linkus.biz.report.service.impl;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.DbJoin;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_EX;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_L;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeIdNameCnBt;
import com.linkus.base.exception.BaseException;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.dao.IBizDao;
import com.linkus.biz.report.model.TestPlanReport;
import com.linkus.biz.report.service.ITestPlanReportService;
import com.linkus.biz.testcase.model.BizCaseConstants;
import com.linkus.biz.testcase.model.TeTestBizCase;
import com.linkus.biz.testplan.dao.ITestBizCaseDao;
import com.linkus.biz.testplan.dao.ITestPlanDao;
import com.linkus.biz.testplan.model.TeTestPlan;
import com.linkus.biz.testplan.service.ITestPlanService;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysUser;




import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.AggregationOptions;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.text.DecimalFormat;
import java.util.*;

@Service
public class TestPlanReportServiceImpl implements ITestPlanReportService {

	@Autowired
	private ITestPlanService testPlanService;

	@Autowired
	private ITestBizCaseDao testBizCaseDao;

	@Autowired
	private IBizDao bizDao;

	@Autowired
	private MongoTemplate mongoTemplate;

	@Autowired
	private ITestPlanDao testPlanDao;

	@Autowired
	private ISysUserDao sysUserDao;
	@Override
	public List<ObjectId> getTestPlanIdsByRecursion(ObjectId prdId, List<ObjectId> testPlanIds){
		if (null == testPlanIds) {
			throw new BaseException("产品id为空");
		}
		
		List<ObjectId> testPlanIdList = new ArrayList<ObjectId>();
		List<TeTestPlan> testPlans = testPlanService.getTestPlanForReport(prdId);
		//如果包含产品ID则返回全量测试计划
		if(testPlanIds.contains(prdId)) {
			for(TeTestPlan t : testPlans) {
				testPlanIdList.add(t.getId());
			}
		}else {
			for(ObjectId testPlanId : testPlanIds) {
				this.recursiveTestPlan(testPlanId, testPlans, testPlanIdList);
			}
		}
		
		return testPlanIdList;
	}
	
	private void recursiveTestPlan(ObjectId rootId, List<TeTestPlan> testPlans, List<ObjectId> testPlanIdList) {
		if(!testPlanIdList.contains(rootId)) {
			testPlanIdList.add(rootId);
		}
		for(TeTestPlan t : testPlans) {
			ObjectId pId = t.getParentId();
			if(null != pId && pId.equals(rootId)) {
				testPlanIdList.add(t.getId());
				this.recursiveTestPlan(t.getId(), testPlans, testPlanIdList);
			}
		}
	}
	
	public List<TestPlanReport> queryTestPlanReport(ObjectId prdId, List<ObjectId> testPlanIdss,ObjectId linkedBizId,
													Date startDate, Date endDate) {
		boolean inPeriod = startDate != null && endDate != null
				&& DateUtil.compareDate(startDate, endDate) <= 0;
		TeTestPlan currentTestPlanCond = new TeTestPlan();
		List<TeTestPlan> currentTestPlans = new ArrayList<>();
		//选择关联业务，查询结果为所选择关联业务下的用例执行结果
		List<ObjectId> realTestPlanIds = new ArrayList<ObjectId>();
		if(StringUtil.isNotNull(linkedBizId)){
			realTestPlanIds =  getRealTestPlanIds(testPlanIdss,linkedBizId);
		}else{
			realTestPlanIds = testPlanIdss;
		}
		for (ObjectId testPlanId : realTestPlanIds){
			currentTestPlanCond.setId(testPlanId);
			currentTestPlanCond.setIsValid(true);
			currentTestPlans.add(testPlanService.getTestPlan(currentTestPlanCond).get(0)) ;
		}
		if (currentTestPlans.isEmpty()) {
			return Collections.emptyList();
		}

		// 测试计划id列表
		List<ObjectId> planIds = new ArrayList<>();
		for (TeTestPlan testPlan : currentTestPlans) {
			planIds.add(testPlan.getId());
		}
		// 查询所有子测试计划
		List<TeTestPlan> allChildTestPlans = getAllChildTestPlans(planIds);
		// 测试计划id和所有子计划及计划本身id映射
		Map<ObjectId, Set<ObjectId>> idToChildIdsMapping = new HashMap<>();
		// 所有测试计划id
		Set<ObjectId> allTestPlanIds = new HashSet<>();
		allTestPlanIds.addAll(planIds);
		for (ObjectId planId : planIds) {
			Set<ObjectId> childIds = idToChildIdsMapping.get(planId);
			if (null == childIds) {
				childIds = new HashSet<>();
				// 添加自身
				childIds.add(planId);
				idToChildIdsMapping.put(planId, childIds);
			}
			for (TeTestPlan testPlan : allChildTestPlans) {
				allTestPlanIds.add(testPlan.getId());
				String parentPath = testPlan.getParentPath();
				if (StringUtil.isNull(parentPath)) {
					continue;
				}
				// 是计划的子计划
				if (parentPath.contains(planId.toString())) {
					childIds.add(testPlan.getId());
				}
			}
		}

		// 测试计划id和测试计划用例数量映射
		Map<ObjectId, Integer> idToCountMapping = new HashMap<>();
		com.mongodb.client.MongoCursor<org.bson.Document> cursor = getTestPlanCase(new ArrayList<>(allTestPlanIds));
		if (cursor != null) {
			while (cursor.hasNext()) {
				org.bson.Document dbObject = cursor.next();
				ObjectId testPlanId = (ObjectId) dbObject.get(DFN.common__id.n());
				Integer count = (Integer) dbObject.get(DFN.group_count.n());
				for (ObjectId planId : idToChildIdsMapping.keySet()) {
					Set<ObjectId> childIds = idToChildIdsMapping.get(planId);
					if (childIds.contains(testPlanId)) {
						Integer c = idToCountMapping.get(planId);
						if (null == c) {
							idToCountMapping.put(planId, count);
						} else {
							idToCountMapping.put(planId, c + count);
						}
					}
				}
			}
		}

		// 查询所有已执行测试用例
		List<TeTestBizCase> testBizCases2 = getAllExecutedTestBizCase(new ArrayList<ObjectId>(allTestPlanIds));
		// 测试计划id和已经执行的测试计划用例集合映射
		Map<ObjectId, List<TeTestBizCase>> idToTestBizCasesMapping = new HashMap<>();
		// 测试计划id和业务id映射
		Map<ObjectId, Set<ObjectId>> idToBizIdsMapping = new HashMap<>();
		Set<ObjectId> allBizIds = new HashSet<>();

		// 处理测试计划和已执行测试用例关系
		for (ObjectId planId : idToChildIdsMapping.keySet()) {
			Set<ObjectId> childIds = idToChildIdsMapping.get(planId);
			for (TeTestBizCase teTestBizCase : testBizCases2) {
				if (childIds.contains(teTestBizCase.getTestPlanId())) {
					List<TeTestBizCase> teTestBizCases = idToTestBizCasesMapping.get(planId);
					if (null == teTestBizCases) {
						teTestBizCases = new ArrayList<>();
						teTestBizCases.add(teTestBizCase);
						idToTestBizCasesMapping.put(planId, teTestBizCases);
					} else {
						teTestBizCases.add(teTestBizCase);
					}
				}
			}
		}

		


		List<TestPlanReport> testPlanReports = new ArrayList<>();

		// 用例总数：
		// 所选计划及其子计划（向下递归）的所有用例数（不剔重）。先从“testPlan”中取出isValid为true下的且parentId为该执行计划ID及其所有子执行计划ID下的所有记录，
		// 再从“biz”中取出isValid为true下的且bizType.cid为ObjectId("5c403daae0ee7730f883fcac")即用例管理下的且linkedTestPlans.cid为该测试计划ID下的所有记录数，
		// 然后再将每个测试计划下的记录数相加，得到用例总数。
		// 注：这里不能把所有测试计划放一起去查，否则会出问题，这样会把2个不同测试计划下的同一个用例只统计成1次，但应该统计成2次。

		// 合计行
		TestPlanReport total = new TestPlanReport();
		total.setTestPlanName("合计");

		int index = 1;
		for (TeTestPlan tp : currentTestPlans) {
			TestPlanReport report = new TestPlanReport();
			report.setNum(index++);
			report.setTestPlanId(tp.getId());
			report.setTestPlanName(tp.getName());

			int testCaseCount = null != idToCountMapping.get(tp.getId()) ? idToCountMapping.get(tp.getId()) : 0;
			// 用例总数
			report.setTestCaseCount(testCaseCount);
			total.setTestCaseCount(total.getTestCaseCount() + report.getTestCaseCount());

			List<TeTestBizCase> testBizCases = idToTestBizCasesMapping.get(tp.getId());
			if (null == testBizCases) {
				testBizCases = new ArrayList<>();
			}
			// 组合排重
			Map<String, List<TeTestBizCase>> execedTestCaseMap = new HashMap<>();
			for (TeTestBizCase tbc : testBizCases) {
				ObjectId tpId = tbc.getTestPlanId();
				ObjectId testCaseId = tbc.getTestCase() == null ? null : tbc.getTestCase().getCid();
				if (testCaseId == null) {
					continue;
				}
				String key = tpId.toString() + testCaseId.toString();
				if (execedTestCaseMap.containsKey(key)) {
					execedTestCaseMap.get(key).add(tbc);
				}
				else {
					List<TeTestBizCase> tbcs = new ArrayList<>();
					tbcs.add(tbc);
					execedTestCaseMap.put(key, tbcs);
				}
			}

			// 累计执行数
			report.setExecedTestCaseCount(execedTestCaseMap.size());
			total.setExecedTestCaseCount(
					total.getExecedTestCaseCount() + report.getExecedTestCaseCount());

			// 累计未执行数
			report.setUnexecedTestCaseCount(testCaseCount - execedTestCaseMap.size());
			total.setUnexecedTestCaseCount(
					total.getUnexecedTestCaseCount() + report.getUnexecedTestCaseCount());

			// 累计通过数/失败数：
			// 属于累计执行数 and 用例执行最新（执行时间最大）的执行结果=
			// 通过。先从“testPlan”中取出isValid为true下的且parentId为该执行计划ID及其所有子执行计划ID下
			// 的所有记录，即所有子测试计划ID，再从“testBizCase”取出isValid为true下的且testPlanId为该所有子测试计划ID下的且runTime最大的且result为succeeded下
			// 的所有记录数。
			
			// 周期内执行数
			int execedTestCaseCountInPeriod = 0;
			// 累计通过数
			int successedTestCaseCount = 0;
			// 周期内通过数
			int successedTestCaseCountInPeriod = 0;
			// 累计失败数
			int failedTestCaseCount = 0;
			// 周期内失败数
			int failedTestCaseCountInPeriod = 0;
			// 累计已阻塞数
			int blockedTestCaseCount = 0;
			// 周期内已阻塞数
			int blockedTestCaseCountInPeriod = 0;
			// 累计执行中数
			int performingTestCaseCount = 0;
			// 周期内执行中数
			int performingTestCaseCountInPeriod = 0;
			// 累计na数
			int naTestCaseCount = 0;
			// 周期内NA数
			int naTestCaseCountInPeriod = 0;

			for (Map.Entry<String, List<TeTestBizCase>> entry : execedTestCaseMap.entrySet()) {
				List<TeTestBizCase> tbcs = entry.getValue();
				// 统计周期内执行数
				if (inPeriod) {
					for (TeTestBizCase tbc : tbcs) {
						Date runTime = tbc.getRunTime();
						if (runTime != null && DateUtil.compareDate(runTime, startDate) >= 0
								&& DateUtil.compareDate(runTime, endDate) <= 0) {
							execedTestCaseCountInPeriod++;
							break;
						}
					}
				}

				TeTestBizCase tbcs0 = tbcs.get(0);
				Date runTime0 = tbcs0.getRunTime();
				boolean isTbcs0InPeriod = inPeriod && runTime0 != null
						&& DateUtil.compareDate(runTime0, startDate) >= 0
						&& DateUtil.compareDate(runTime0, endDate) <= 0;

				if (BizCaseConstants.BIZ_CASE_SUCCESS.equals(tbcs0.getResult())) {
					successedTestCaseCount++;
					if (isTbcs0InPeriod) {
						successedTestCaseCountInPeriod++;
					}
				}

				if (BizCaseConstants.BIZ_CASE_FAILED.equals(tbcs0.getResult())) {
					failedTestCaseCount++;
					if (isTbcs0InPeriod) {
						failedTestCaseCountInPeriod++;
					}
				}

				if (BizCaseConstants.BIZ_CASE_BLOCKED.equals(tbcs0.getResult())) {
					blockedTestCaseCount++;
					if (isTbcs0InPeriod) {
						blockedTestCaseCountInPeriod++;
					}
				}

				if (BizCaseConstants.BIZ_CASE_PERFORMING.equals(tbcs0.getResult())) {
					performingTestCaseCount++;
					if (isTbcs0InPeriod) {
						performingTestCaseCountInPeriod++;
					}
				}

				if (BizCaseConstants.BIZ_CASE_NA.equals(tbcs0.getResult())) {
					naTestCaseCount++;
					if (isTbcs0InPeriod) {
						naTestCaseCountInPeriod++;
					}
				}
			}
			report.setSuccessedTestCaseCount(successedTestCaseCount);
			total.setSuccessedTestCaseCount(
					total.getSuccessedTestCaseCount() + report.getSuccessedTestCaseCount());
			report.setFailedTestCaseCount(failedTestCaseCount);
			total.setFailedTestCaseCount(
					total.getFailedTestCaseCount() + report.getFailedTestCaseCount());
			report.setBlockedTestCaseCount(blockedTestCaseCount);
			total.setBlockedTestCaseCount(
					total.getBlockedTestCaseCount() + report.getBlockedTestCaseCount());
			// 用例总数中，执行状态= 通过、失败、执行中 or 已阻塞
			report.setPerformingTestCaseCount(performingTestCaseCount);
			total.setPerformingTestCaseCount(total.getPerformingTestCaseCount() + report.getPerformingTestCaseCount());
			report.setNaTestCaseCount(naTestCaseCount);
			total.setNaTestCaseCount(total.getNaTestCaseCount() + report.getNaTestCaseCount());

			// 累计执行率：累计执行数/用例总数×100%
			if (report.getTestCaseCount() > 0) {
				double execRate = report.getExecedTestCaseCount() * 1.00
						/ report.getTestCaseCount();
				report.setExecRate(formatRate(execRate));
			}

			// 累计通过率：累计通过数 / （用例总数 - 累计NA数）×100%
			if (report.getTestCaseCount() - report.getNaTestCaseCount() > 0) {
				double successedRate = report.getSuccessedTestCaseCount() * 1.00
						/ (report.getTestCaseCount() - report.getNaTestCaseCount());
				report.setSuccessedRate(formatRate(successedRate));
			}

			if (report.getExecedTestCaseCount() > 0) {
				// 已执行通过率：累计通过数/累计执行数×100%
				double execedSuccessRate = report.getSuccessedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedSuccessRate(formatRate(execedSuccessRate));

				// 已执行失败率：累计失败数/累计执行数×100%
				double execedFailRate = report.getFailedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedFailRate(formatRate(execedFailRate));

				// 已执行阻塞率：累计已阻塞数/累计执行数×100%
				double execedBlockRate = report.getBlockedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedBlockRate(formatRate(execedBlockRate));

				// 已执行执行中率：累计执行中数/累计执行数×100%
				double execedPerformRate = report.getPerformingTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedPerformRate(formatRate(execedPerformRate));

				// 已执行NA率：累计NA数/累计执行数×100%
				double execedNaRate = report.getNaTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedNaRate(formatRate(execedNaRate));
			}

			if (inPeriod) {
				// 周期内执行数
				report.setExecedTestCaseCountInPeriod(execedTestCaseCountInPeriod);
				total.setExecedTestCaseCountInPeriod(total.getExecedTestCaseCountInPeriod()
						+ report.getExecedTestCaseCountInPeriod());
				// 周期内通过数
				report.setSuccessedTestCaseCountInPeriod(successedTestCaseCountInPeriod);
				total.setSuccessedTestCaseCountInPeriod(total.getSuccessedTestCaseCountInPeriod()
						+ report.getSuccessedTestCaseCountInPeriod());
				// 周期内失败数
				report.setFailedTestCaseCountInPeriod(failedTestCaseCountInPeriod);
				total.setFailedTestCaseCountInPeriod(total.getFailedTestCaseCountInPeriod()
						+ report.getFailedTestCaseCountInPeriod());
				// 周期内已阻塞数
				report.setBlockedTestCaseCountInPeriod(blockedTestCaseCountInPeriod);
				total.setBlockedTestCaseCountInPeriod(total.getBlockedTestCaseCountInPeriod()
						+ report.getBlockedTestCaseCountInPeriod());
				// 周期内执行中数
				report.setPerformingTestCaseCountInPeriod(
						performingTestCaseCountInPeriod + report.getSuccessedTestCaseCountInPeriod()
								+ report.getFailedTestCaseCountInPeriod()
								+ report.getBlockedTestCaseCountInPeriod());
				total.setPerformingTestCaseCountInPeriod(total.getPerformingTestCaseCountInPeriod()
						+ report.getPerformingTestCaseCountInPeriod());
				// 周期内NA数
				report.setNaTestCaseCountInPeriod(naTestCaseCountInPeriod);
				total.setNaTestCaseCountInPeriod(
						total.getNaTestCaseCountInPeriod() + report.getNaTestCaseCountInPeriod());
			}
			else {
				report.setExecedTestCaseCountInPeriod(report.getExecedTestCaseCount());
				report.setSuccessedTestCaseCountInPeriod(report.getSuccessedTestCaseCount());
				report.setFailedTestCaseCountInPeriod(report.getFailedTestCaseCount());
				report.setBlockedTestCaseCountInPeriod(report.getBlockedTestCaseCount());
				report.setPerformingTestCaseCountInPeriod(report.getPerformingTestCaseCount());
				report.setNaTestCaseCountInPeriod(report.getNaTestCaseCount());

				total.setExecedTestCaseCountInPeriod(total.getExecedTestCaseCountInPeriod()
						+ report.getExecedTestCaseCountInPeriod());
				total.setSuccessedTestCaseCountInPeriod(total.getSuccessedTestCaseCountInPeriod()
						+ report.getSuccessedTestCaseCountInPeriod());
				total.setFailedTestCaseCountInPeriod(total.getFailedTestCaseCountInPeriod()
						+ report.getFailedTestCaseCountInPeriod());
				total.setBlockedTestCaseCountInPeriod(total.getBlockedTestCaseCountInPeriod()
						+ report.getBlockedTestCaseCountInPeriod());
				total.setPerformingTestCaseCountInPeriod(total.getPerformingTestCaseCountInPeriod()
						+ report.getPerformingTestCaseCountInPeriod());
				total.setNaTestCaseCountInPeriod(
						total.getNaTestCaseCountInPeriod() + report.getNaTestCaseCountInPeriod());
			}
			
			testPlanReports.add(report);
		}
		getTaskPlanRateTotle(total);
		testPlanReports.add(total);
		return testPlanReports;
	}

    @Override
    public List<TestPlanReport> queryTestPlanCaseReps(ObjectId theTestPlanId, String testPlanName, Date startDate, Date endDate) {
        boolean inPeriod = startDate != null && endDate != null
                && DateUtil.compareDate(startDate, endDate) <= 0;
        TeTestPlan currentTestPlanCond = new TeTestPlan();
        List<TeTestPlan> currentTestPlans = new ArrayList<>();
        currentTestPlanCond.setId(theTestPlanId);
        currentTestPlanCond.setIsValid(true);
        currentTestPlanCond.setName(testPlanName);
        TeTestPlan currentTestPlan=testPlanService.getTestPlan(currentTestPlanCond).get(0);
        currentTestPlans.add(currentTestPlan);
        List<ObjectId> planIds = new ArrayList<>();
        for (TeTestPlan testPlan : currentTestPlans) {
            planIds.add(testPlan.getId());
        }
        // 查询所有子测试计划
        List<TeTestPlan> allChildTestPlans = getAllChildTestPlans(planIds);
        // 测试计划id和所有子计划及计划本身id映射
        Map<ObjectId, Set<ObjectId>> idToChildIdsMapping = new HashMap<>();
        // 所有测试计划id
        Set<ObjectId> allTestPlanIds = new HashSet<>();
        allTestPlanIds.add(theTestPlanId);
        Set<ObjectId> childIds = idToChildIdsMapping.get(theTestPlanId);
        if (null == childIds) {
                childIds = new HashSet<>();
                // 添加自身
                childIds.add(theTestPlanId);
                idToChildIdsMapping.put(theTestPlanId, childIds);
        }
        for (TeTestPlan testPlan : allChildTestPlans) {
        	allTestPlanIds.add(testPlan.getId());
        	String parentPath = testPlan.getParentPath();
        	if (StringUtil.isNull(parentPath)) {
        		continue;
        	}
                // 是计划的子计划
			if (parentPath.contains(theTestPlanId.toString())) {
				childIds.add(testPlan.getId());
			}
        }
        // 用例责任人和测试计划用例数量映射
        Map<ObjectId, Integer> respIdToCountMapping = new HashMap<>();
        Map<ObjectId, String> idToNameMapping=new HashMap<>();
		List<ObjectId> userIdList = new ArrayList<>();
		com.mongodb.client.MongoCursor<org.bson.Document> cursor = getTestPlanCaseResp(new ArrayList<>(allTestPlanIds), null);
        ObjectId noRespId=ObjectId.get();
		idToNameMapping.put(noRespId,"无责任人");
		if (cursor != null) {
            while (cursor.hasNext()) {
                org.bson.Document dbObject = cursor.next();
				ObjectId userId= (ObjectId) dbObject.get(DFN.common__id.n());
				if (userId==null){
					userId=noRespId;
				}else {
					userIdList.add(userId);
				}
				Integer count = (Integer) dbObject.get(DFN.group_count.n());
				Integer c = respIdToCountMapping.get(userId);
				if (null == c) {
					respIdToCountMapping.put(userId, count);
				} else {
					respIdToCountMapping.put(userId, c + count);
				}
            }
		}
        List<IDbCondition> conds = new ArrayList<>();
        List<DbFieldName> fieldNames = new ArrayList<>();
        conds.add(new DC_I<ObjectId>(DFN.sysUser_id,userIdList));
        fieldNames.add(DFN.sysUser__userName);
        fieldNames.add(DFN.sysUser__loginName);
        List<TeSysUser> resps = sysUserDao.findByFieldAndConds(conds,fieldNames);
		for (TeSysUser resp : resps) {
			ObjectId userId = resp.getId();
			String userName = resp.getUserName()==null?"":resp.getUserName();
			String loginName = resp.getLoginName()==null?"":resp.getLoginName();
			String name=userName+"/"+loginName;
			idToNameMapping.put(userId,name);
		}
		// 查询所有已执行测试用例
        List<TeTestBizCase> testBizCases = getAllExecutedTestBizCase(new ArrayList<ObjectId>(allTestPlanIds));
        Set<ObjectId> allBizIds = new HashSet<>();
		Map<ObjectId, Set<ObjectId>> idToBizIdsMapping = new HashMap<>();
        // 用例责任人和测试计划用例集合映射
		Map<ObjectId, List<TeTestBizCase>> respIdToTestBizCasesMapping = new HashMap<>();
        // 处理测试计划和已执行测试用例关系
		for (TeTestBizCase testBizCase : testBizCases) {
			if (null != testBizCase.getTestCase()) {
				allBizIds.add(testBizCase.getTestCase().getCid());
			}
			if (testBizCase.getResp()!=null){
				ObjectId userId = testBizCase.getResp().getUserId();
				List<TeTestBizCase> testBizCases1 = respIdToTestBizCasesMapping.get(userId);
				if (null == testBizCases1) {
					testBizCases1 = new ArrayList<>();
					testBizCases1.add(testBizCase);
					respIdToTestBizCasesMapping.put(userId, testBizCases1);
				} else {
					testBizCases1.add(testBizCase);
				}
			}else {
				List<TeTestBizCase> teTestBizCases = respIdToTestBizCasesMapping.get(noRespId);
				if (null == teTestBizCases) {
					teTestBizCases = new ArrayList<>();
					teTestBizCases.add(testBizCase);
					respIdToTestBizCasesMapping.put(noRespId, teTestBizCases);
				} else if(!teTestBizCases.contains(testBizCase)) {
					teTestBizCases.add(testBizCase);
				}
			}
		}
        List<TestPlanReport> testPlanReports = new ArrayList<>();
        //无责任人行
		TestPlanReport noRespReport = new TestPlanReport();
        // 合计行
        TestPlanReport total = new TestPlanReport();
        total.setTestCaseReps("合计");
		int index = 1;
		for (ObjectId respUserId : respIdToCountMapping.keySet()) {
			TestPlanReport report = new TestPlanReport();
			if (!respUserId.equals(noRespId)){
				report.setNum(index++);
			}
			report.setTestCaseReps(idToNameMapping.get(respUserId));
			report.setTestPlanId(currentTestPlan.getId());
			report.setTestPlanName(currentTestPlan.getName());
			int testCaseCount = null != respIdToCountMapping.get(respUserId) ? respIdToCountMapping.get(respUserId) : 0;
			// 用例总数
			report.setTestCaseCount(testCaseCount);
			total.setTestCaseCount(total.getTestCaseCount() + report.getTestCaseCount());
			List<TeTestBizCase> testBizCases1 = respIdToTestBizCasesMapping.get(respUserId);
			if (null == testBizCases1) {
				testBizCases1 = new ArrayList<>();
			}
			Map<String, List<TeTestBizCase>> execedTestCaseMap = new HashMap<>();
			for (TeTestBizCase tbc : testBizCases1) {
				String id=getUUID();
				ObjectId testCaseId = tbc.getTestCase() == null ? null : tbc.getTestCase().getCid();
				if (testCaseId == null) {
					continue;
				}
				if(!allBizIds.contains(testCaseId)){
					continue;
				}
				String key = id + testCaseId.toString();
				if (execedTestCaseMap.containsKey(key)) {
					execedTestCaseMap.get(key).add(tbc);
				}
				else {
					List<TeTestBizCase> tbcs = new ArrayList<>();
					tbcs.add(tbc);
					execedTestCaseMap.put(key, tbcs);
				}
			}
			// 累计执行数
			report.setExecedTestCaseCount(execedTestCaseMap.size());
			total.setExecedTestCaseCount(
					total.getExecedTestCaseCount() + report.getExecedTestCaseCount());
			// 累计未执行数
			report.setUnexecedTestCaseCount(testCaseCount - execedTestCaseMap.size());
			total.setUnexecedTestCaseCount(
					total.getUnexecedTestCaseCount() + report.getUnexecedTestCaseCount());

			// 累计通过数/失败数：
			// 属于累计执行数 and 用例执行最新（执行时间最大）的执行结果=
			// 通过。先从“testPlan”中取出isValid为true下的且parentId为该执行计划ID及其所有子执行计划ID下
			// 的所有记录，即所有子测试计划ID，再从“testBizCase”取出isValid为true下的且testPlanId为该所有子测试计划ID下的且runTime最大的且result为succeeded下
			// 的所有记录数。

			// 周期内执行数
			int execedTestCaseCountInPeriod = 0;
			// 累计通过数
			int successedTestCaseCount = 0;
			// 周期内通过数
			int successedTestCaseCountInPeriod = 0;
			// 累计失败数
			int failedTestCaseCount = 0;
			// 周期内失败数
			int failedTestCaseCountInPeriod = 0;
			// 累计已阻塞数
			int blockedTestCaseCount = 0;
			// 周期内已阻塞数
			int blockedTestCaseCountInPeriod = 0;
			// 累计执行中数
			int performingTestCaseCount = 0;
			// 周期内执行中数
			int performingTestCaseCountInPeriod = 0;
			// 累计na数
			int naTestCaseCount = 0;
			// 周期内NA数
			int naTestCaseCountInPeriod = 0;
			for (Map.Entry<String, List<TeTestBizCase>> entry : execedTestCaseMap.entrySet()) {
				List<TeTestBizCase> tbcs = entry.getValue();
				// 统计周期内执行数
				if (inPeriod) {
					for (TeTestBizCase tbc : tbcs) {
						Date runTime = tbc.getRunTime();
						if (runTime != null && DateUtil.compareDate(runTime, startDate) >= 0
								&& DateUtil.compareDate(runTime, endDate) <= 0) {
							execedTestCaseCountInPeriod++;
							break;
						}
					}
				}
				TeTestBizCase tbcs0 = tbcs.get(0);
				Date runTime0 = tbcs0.getRunTime();
				boolean isTbcs0InPeriod = inPeriod && runTime0 != null
						&& DateUtil.compareDate(runTime0, startDate) >= 0
						&& DateUtil.compareDate(runTime0, endDate) <= 0;
				if (BizCaseConstants.BIZ_CASE_SUCCESS.equals(tbcs0.getResult())) {
					successedTestCaseCount++;
					if (isTbcs0InPeriod) {
						successedTestCaseCountInPeriod++;
					}
				}
				if (BizCaseConstants.BIZ_CASE_FAILED.equals(tbcs0.getResult())) {
					failedTestCaseCount++;
					if (isTbcs0InPeriod) {
						failedTestCaseCountInPeriod++;
					}
				}
				if (BizCaseConstants.BIZ_CASE_BLOCKED.equals(tbcs0.getResult())) {
					blockedTestCaseCount++;
					if (isTbcs0InPeriod) {
						blockedTestCaseCountInPeriod++;
					}
				}
				if (BizCaseConstants.BIZ_CASE_PERFORMING.equals(tbcs0.getResult())) {
					performingTestCaseCount++;
					if (isTbcs0InPeriod) {
						performingTestCaseCountInPeriod++;
					}
				}
				if (BizCaseConstants.BIZ_CASE_NA.equals(tbcs0.getResult())) {
					naTestCaseCount++;
					if (isTbcs0InPeriod) {
						naTestCaseCountInPeriod++;
					}
				}
			}
			report.setSuccessedTestCaseCount(successedTestCaseCount);
			total.setSuccessedTestCaseCount(
					total.getSuccessedTestCaseCount() + report.getSuccessedTestCaseCount());
			report.setFailedTestCaseCount(failedTestCaseCount);
			total.setFailedTestCaseCount(
					total.getFailedTestCaseCount() + report.getFailedTestCaseCount());
			report.setBlockedTestCaseCount(blockedTestCaseCount);
			total.setBlockedTestCaseCount(
					total.getBlockedTestCaseCount() + report.getBlockedTestCaseCount());
			// 用例总数中，执行状态= 通过、失败、执行中 or 已阻塞
			report.setPerformingTestCaseCount(performingTestCaseCount);
			total.setPerformingTestCaseCount(total.getPerformingTestCaseCount() + report.getPerformingTestCaseCount());
			report.setNaTestCaseCount(naTestCaseCount);
			total.setNaTestCaseCount(total.getNaTestCaseCount() + report.getNaTestCaseCount());
			// 累计执行率：累计执行数/用例总数×100%
			if (report.getTestCaseCount() > 0) {
				double execRate = report.getExecedTestCaseCount() * 1.00
						/ report.getTestCaseCount();
				report.setExecRate(formatRate(execRate));
			}
			// 累计通过率：累计通过数 / （用例总数 - 累计NA数）×100%
			if (report.getTestCaseCount() - report.getNaTestCaseCount() > 0) {
				double successedRate = report.getSuccessedTestCaseCount() * 1.00
						/ (report.getTestCaseCount() - report.getNaTestCaseCount());
				report.setSuccessedRate(formatRate(successedRate));
			}
			if (report.getExecedTestCaseCount() > 0) {
				// 已执行通过率：累计通过数/累计执行数×100%
				double execedSuccessRate = report.getSuccessedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedSuccessRate(formatRate(execedSuccessRate));

				// 已执行失败率：累计失败数/累计执行数×100%
				double execedFailRate = report.getFailedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedFailRate(formatRate(execedFailRate));

				// 已执行阻塞率：累计已阻塞数/累计执行数×100%
				double execedBlockRate = report.getBlockedTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedBlockRate(formatRate(execedBlockRate));

				// 已执行执行中率：累计执行中数/累计执行数×100%
				double execedPerformRate = report.getPerformingTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedPerformRate(formatRate(execedPerformRate));

				// 已执行NA率：累计NA数/累计执行数×100%
				double execedNaRate = report.getNaTestCaseCount() * 1.00
						/ report.getExecedTestCaseCount();
				report.setExecedNaRate(formatRate(execedNaRate));
			}

			if (inPeriod) {
				// 周期内执行数
				report.setExecedTestCaseCountInPeriod(execedTestCaseCountInPeriod);
				total.setExecedTestCaseCountInPeriod(total.getExecedTestCaseCountInPeriod()
						+ report.getExecedTestCaseCountInPeriod());
				// 周期内通过数
				report.setSuccessedTestCaseCountInPeriod(successedTestCaseCountInPeriod);
				total.setSuccessedTestCaseCountInPeriod(total.getSuccessedTestCaseCountInPeriod()
						+ report.getSuccessedTestCaseCountInPeriod());
				// 周期内失败数
				report.setFailedTestCaseCountInPeriod(failedTestCaseCountInPeriod);
				total.setFailedTestCaseCountInPeriod(total.getFailedTestCaseCountInPeriod()
						+ report.getFailedTestCaseCountInPeriod());
				// 周期内已阻塞数
				report.setBlockedTestCaseCountInPeriod(blockedTestCaseCountInPeriod);
				total.setBlockedTestCaseCountInPeriod(total.getBlockedTestCaseCountInPeriod()
						+ report.getBlockedTestCaseCountInPeriod());
				// 周期内执行中数
				report.setPerformingTestCaseCountInPeriod(
						performingTestCaseCountInPeriod + report.getSuccessedTestCaseCountInPeriod()
								+ report.getFailedTestCaseCountInPeriod()
								+ report.getBlockedTestCaseCountInPeriod());
				total.setPerformingTestCaseCountInPeriod(total.getPerformingTestCaseCountInPeriod()
						+ report.getPerformingTestCaseCountInPeriod());
				// 周期内NA数
				report.setNaTestCaseCountInPeriod(naTestCaseCountInPeriod);
				total.setNaTestCaseCountInPeriod(
						total.getNaTestCaseCountInPeriod() + report.getNaTestCaseCountInPeriod());
			}
			else {
				report.setExecedTestCaseCountInPeriod(report.getExecedTestCaseCount());
				report.setSuccessedTestCaseCountInPeriod(report.getSuccessedTestCaseCount());
				report.setFailedTestCaseCountInPeriod(report.getFailedTestCaseCount());
				report.setBlockedTestCaseCountInPeriod(report.getBlockedTestCaseCount());
				report.setPerformingTestCaseCountInPeriod(report.getPerformingTestCaseCount());
				report.setNaTestCaseCountInPeriod(report.getNaTestCaseCount());

				total.setExecedTestCaseCountInPeriod(total.getExecedTestCaseCountInPeriod()
						+ report.getExecedTestCaseCountInPeriod());
				total.setSuccessedTestCaseCountInPeriod(total.getSuccessedTestCaseCountInPeriod()
						+ report.getSuccessedTestCaseCountInPeriod());
				total.setFailedTestCaseCountInPeriod(total.getFailedTestCaseCountInPeriod()
						+ report.getFailedTestCaseCountInPeriod());
				total.setBlockedTestCaseCountInPeriod(total.getBlockedTestCaseCountInPeriod()
						+ report.getBlockedTestCaseCountInPeriod());
				total.setPerformingTestCaseCountInPeriod(total.getPerformingTestCaseCountInPeriod()
						+ report.getPerformingTestCaseCountInPeriod());
				total.setNaTestCaseCountInPeriod(
						total.getNaTestCaseCountInPeriod() + report.getNaTestCaseCountInPeriod());
			}
			if (noRespId.equals(respUserId)){
				noRespReport=report;
				continue;
			}
			testPlanReports.add(report);
		}
		noRespReport.setNum(index++);
		getTaskPlanRateTotle(total);
		if (noRespReport.getTestCaseCount()!=0){
			testPlanReports.add(noRespReport);
		}
		testPlanReports.add(total);
		return testPlanReports;
    }

    private void getTaskPlanRateTotle(TestPlanReport total) {
		if (total.getTestCaseCount() > 0) {
			double execRate = total.getExecedTestCaseCount() * 1.00 / total.getTestCaseCount();
			total.setExecRate(formatRate(execRate));
		}
		
		if (total.getTestCaseCount() - total.getNaTestCaseCount() > 0) {
			double successedRate = total.getSuccessedTestCaseCount() * 1.00 / (total.getTestCaseCount() - total.getNaTestCaseCount());
			total.setSuccessedRate(formatRate(successedRate));
		}
		
		if (total.getExecedTestCaseCount() > 0) {
			double execedSuccessRate = total.getSuccessedTestCaseCount() * 1.00 / total.getExecedTestCaseCount();
			total.setExecedSuccessRate(formatRate(execedSuccessRate));
			
			double execedFailRate = total.getFailedTestCaseCount() * 1.00 / total.getExecedTestCaseCount();
			total.setExecedFailRate(formatRate(execedFailRate));
			
			double execedBlockRate = total.getBlockedTestCaseCount() * 1.00 / total.getExecedTestCaseCount();
			total.setExecedBlockRate(formatRate(execedBlockRate));
			
			double execedPerformRate = total.getPerformingTestCaseCount() * 1.00 / total.getExecedTestCaseCount();
			total.setExecedPerformRate(formatRate(execedPerformRate));

			double execedNaRate = total.getNaTestCaseCount() * 1.00 / total.getExecedTestCaseCount();
			total.setExecedNaRate(formatRate(execedNaRate));
		}
	}
	
    private List<ObjectId>  getRealTestPlanIds(List<ObjectId> testPlanIdss,ObjectId linkedBizId){
    	List<ObjectId> realTestPlanIds =  new ArrayList<ObjectId>();
    	if(StringUtil.isNull(linkedBizId)){
    		return realTestPlanIds;
    	}
		List<ObjectId>  linkedBizIds = new ArrayList<ObjectId>();
		linkedBizIds.add(linkedBizId);
		TeBiz  teBiz = bizDao.getBizById(linkedBizId);
		if(StringUtil.isNotNull(teBiz) && StringUtil.isNotNull(teBiz.getLinkedTestPlans()) && teBiz.getLinkedTestPlans().size()>0){
			for (TeIdNameCnBt teIdName : teBiz.getLinkedTestPlans()) {
				if(StringUtil.isNotNull(teIdName.getCid())){
					if(testPlanIdss.contains(teIdName.getCid())){
						realTestPlanIds.add(teIdName.getCid());
					}
				}
			}
		}
    	return realTestPlanIds;
    }

	private String formatRate(double rate) {
		DecimalFormat fmt = new DecimalFormat("#.00");
		String percent = fmt.format(rate * 100);
		if (percent.equals(".00")) {
			percent = "0";
		}
		return percent + "%";
	}

	/**
	 * 根据测试计划id查询所有子测试计划
	 *
	 * @param planIds
	 * @return
	 */
	private List<TeTestPlan> getAllChildTestPlans(List<ObjectId> planIds) {
		if (CollectionUtils.isEmpty(planIds)) {
			return Collections.EMPTY_LIST;
		}
		List<IDbCondition> conds = new ArrayList<IDbCondition>();
		conds.add(new DC_E(DFN.common_isValid, true));
		List<IDbCondition> orConds = new ArrayList<>();
		for (ObjectId planId : planIds) {
			orConds.add(new DC_L(DFN.testPlan_parentPath, planId.toString()));
		}
		conds.add(new DC_OR(orConds));
		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.common__id);
		fieldNames.add(DFN.testPlan_parentPath);
		return testPlanDao.findByFieldAndConds(conds, fieldNames, null);
	}

	/**
	 * 根据测试计划id列表查询所有已执行测试用例
	 * 
	 * @param testPlanIds
	 * @return
	 */
	private List<TeTestBizCase> getAllExecutedTestBizCase(List<ObjectId> testPlanIds) {
		List<IDbCondition> testBizCaseConds = new ArrayList<>();
		testBizCaseConds.add(new DC_I<ObjectId>(DFN.testBizCase_testPlanId, testPlanIds));
		testBizCaseConds.add(new DC_E(DFN.testBizCase_isValid, true));
		testBizCaseConds.add(new DC_EX(DFN.testBizCase_runUser.dot(DFN.common_userId), true));
		testBizCaseConds.add(new DC_EX(DFN.testBizCase_runTime, true));

		List<DbJoin> joins = new ArrayList<>();
		List<IDbCondition> bizConds = new ArrayList<>();
		bizConds.add(new DC_E(DFN.join_biz.dot(DFN.biz_isValid), true));
		// testBizCase关联biz表，过滤掉isValid为false的，即已删除的业务
		joins.add(new DbJoin(DBT.BIZ, DFN.biz_id, DFN.testBizCase_case.dot(DFN.testBizCase_cid), bizConds, DFN.join_biz.n(), false));

		List<DbFieldName> fieldNames = new ArrayList<>();
		fieldNames.add(DFN.testBizCase_testPlanId);
		fieldNames.add(DFN.testBizCase_case);
		fieldNames.add(DFN.testBizCase_runTime);
		fieldNames.add(DFN.testBizCase_result);
		fieldNames.add(DFN.testBizCase_resp);
		Sort runTimeDescSort = Sort.by(Direction.DESC, DFN.testBizCase_runTime.n());
		return testBizCaseDao.queryMultJoin(testBizCaseConds, joins, fieldNames, runTimeDescSort, null, TeTestBizCase.class);
	}

	/**
	 * 关联查询测试计划用例
	 *
	 * @param testPlanIds
	 * @return
	 */
	private com.mongodb.client.MongoCursor<org.bson.Document> getTestPlanCase(List<ObjectId> testPlanIds) {
		if (CollectionUtils.isEmpty(testPlanIds)) {
			return null;
		}
		List<org.bson.Document> aggregate = new ArrayList<>();
		org.bson.Document testBizCaseMatch = new org.bson.Document();
		testBizCaseMatch.append(DFN.common_isValid.n(), true);
		testBizCaseMatch.append(DFN.testBizCase_bizCaseCode.n(), new org.bson.Document("$exists", false));
		testBizCaseMatch.append(DFN.testBizCase_testPlanId.n(), new org.bson.Document("$in", testPlanIds));
		aggregate.add(new org.bson.Document("$match", testBizCaseMatch));
		org.bson.Document lookupBiz = new org.bson.Document()
				.append("from", DBT.BIZ.n())
				.append("localField", DFN.testBizCase_case.dot(DFN.common_cid).n())
				.append("foreignField", DFN.biz_id.n())
				.append("as", DFN.joinDocAlias.n());
		aggregate.add(new org.bson.Document("$lookup", lookupBiz));
		aggregate.add(new org.bson.Document("$unwind", new org.bson.Document("path", DFN.joinDocAlias.$n())));
		org.bson.Document bizMatch = new org.bson.Document();
		bizMatch.append(DFN.joinDocAlias.dot(DFN.common_isValid).n(), true);
		bizMatch.append(DFN.joinDocAlias.dot(DFN.biz_linkedTestPlans).n(), new org.bson.Document("$exists", true));
		bizMatch.append(DFN.joinDocAlias.dot(DFN.biz_bizType).dot(DFN.common_cid).n(), SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT);
		aggregate.add(new org.bson.Document("$match", bizMatch));
		org.bson.Document group = new org.bson.Document()
				.append(DFN.common__id.n(), DFN.testBizCase_testPlanId.$n())
				.append(DFN.group_count.n(), new org.bson.Document("$sum", 1));
		aggregate.add(new org.bson.Document("$group", group));
		// MongoDB4.0
		com.mongodb.client.MongoCursor<org.bson.Document> bizCaseOutput = mongoTemplate.getCollection(DBT.TEST_BIZ_CASE.n()).aggregate(aggregate)
				.cursor();
		return bizCaseOutput;
	}

	/**
	 * 关联查询测试计划用例责任人
	 *
	 * @param testPlanIds
	 * @return
	 */
	private com.mongodb.client.MongoCursor<org.bson.Document> getTestPlanCaseResp(List<ObjectId> testPlanIds, ObjectId prjId) {
		if (CollectionUtils.isEmpty(testPlanIds)) {
			return null;
		}
		List<org.bson.Document> aggregate = new ArrayList<>();
		org.bson.Document testBizCaseMatch = new org.bson.Document();
		testBizCaseMatch.append(DFN.common_isValid.n(), true);
		testBizCaseMatch.append(DFN.testBizCase_bizCaseCode.n(), new org.bson.Document("$exists", false));
		testBizCaseMatch.append(DFN.testBizCase_testPlanId.n(), new org.bson.Document("$in", testPlanIds));
		aggregate.add(new org.bson.Document("$match", testBizCaseMatch));
		org.bson.Document lookupBiz = new org.bson.Document()
				.append("from", DBT.BIZ.n())
				.append("localField", DFN.testBizCase_case.dot(DFN.common_cid).n())
				.append("foreignField", DFN.biz_id.n())
				.append("as", DFN.joinDocAlias.n());
		aggregate.add(new org.bson.Document("$lookup", lookupBiz));
		aggregate.add(new org.bson.Document("$unwind", new org.bson.Document("path", DFN.joinDocAlias.$n())));
		org.bson.Document bizMatch = new org.bson.Document();
		bizMatch.append(DFN.joinDocAlias.dot(DFN.common_isValid).n(), true);
		bizMatch.append(DFN.joinDocAlias.dot(DFN.biz_linkedTestPlans).n(), new org.bson.Document("$exists", true));
		bizMatch.append(DFN.joinDocAlias.dot(DFN.biz_bizType).dot(DFN.common_cid).n(), SysDefConstants.DEF_ID_BIZ_TYPE_TEST_CASE_MGT);
		if (null != prjId) {
			bizMatch.append(DFN.joinDocAlias.dot(DFN.biz_prj.dot(DFN.biz_cid)).n(), prjId);
		}
		aggregate.add(new org.bson.Document("$match", bizMatch));
		org.bson.Document group = new org.bson.Document()
				.append(DFN.common__id.n(), DFN.testBizCase_resp.dot(DFN.common_userId).$n())
				.append(DFN.group_count.n(), new org.bson.Document("$sum", 1));
		aggregate.add(new org.bson.Document("$group", group));
		// MongoDB4.0
		com.mongodb.client.MongoCursor<org.bson.Document> bizCaseOutput = mongoTemplate.getCollection(DBT.TEST_BIZ_CASE.n()).aggregate(aggregate)
				.cursor();
		return bizCaseOutput;
	}

	public static String getUUID() {
		UUID uuid = UUID.randomUUID();
		String str = uuid.toString();
		String uuidStr = str.replace("-", "");
		return uuidStr;
	}


}
