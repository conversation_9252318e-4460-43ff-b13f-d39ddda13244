package com.linkus.biz.ctrl.model.vo;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeIdNameCnBt;
import com.linkus.base.db.mongo.model.TeIdNameCnPtIds;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.util.JsonUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.TeBiz2BizType;
import com.linkus.biz.db.model.TeBiz2NodeInfo;
import com.linkus.biz.field.model.vo.Field;
import com.linkus.biz.kanban.model.vo.ExpandField;
import com.linkus.biz.prj.model.TeBiz2HandleLog;
import com.linkus.cmpt.flow.model.TeFlowTask;
import com.linkus.sys.model.SysDef;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

//业务视图专用,勿改
public class BizView {
	
	private ObjectId id;										// id
	private Boolean isValid;								// 有/无效
	private String prjDefId;								// 项目ID
	private SysDef bizType;									// 业务类型 {业务类型ID, 类型类型名称},即问题管理
	private SysDef subBizType;
	private String code;									// 业务编号(自增长的数字，必须是全系统唯一),要做的,是系统级的
	private String name;									// 业务名称
	private String desc;									// 业务描述
	//TODO
	private SysDef status;									// 由ObjectId更改为对象 业务状态ObjectId(),也是系统级的
	
	private SysDef prdCtlg;
	private SysDef prd;
	private String issueType;								// 问题类别ObjectId(), 分范围管理、项目管理、人力资源、项目实施等.
	private List<TeUser> respUser;							// 如果不是系统的用户，则只有userName，其它都为空 (责任人,可以是多个)
	private List<TeBiz2HandleLog> handleLog;				// 处理过程
	
	//TODO
	private List<String> preBizs;							//父id
	private String remark;									//备注
	private Boolean needSurvey;								//是否调研
	private List<TeIdName> linkedPrjGroup;					//计划调研对象
	private Integer donePercent;							//完成进度
	private String needHelp;								//需协调解决事项
	private String conclusion;								//不知道啥
	
	private String issueCode;			 					// 问题编码，格式WT_123，自然数增长
	private String riskAffect;								// 风险影响
	private String relyOn;									// 依赖问题类型ObjectId(), CMC问题管理中问题类别依赖的另一个问题类别
	private String subSystem;								// 所属子系统
	private String submitDept;								// 提出部门
	private String issuePriority;							// 问题优先级ObjectId()，分重要紧急和重要不紧急
	private String proposal;								// 解决建议
	private String issueStatus;								// 问题状态ObjectId()，分打开、挂起、关闭和延期
	private String resolveStep;								// 解决措施
	private String issueSummary;							// 问题总结
	private String customerResp;							// 局方负责人
	private String customerCoordinator;						// 局方协调
	private Date planStartDate;								// 计划开始时间
	private Date planEndDate;								// 计划结束时间
	private Date actualCloseTime;							// 实际关闭时间
	private Date addTime;									// 数据创建时间
	private TeUser addUser;									// 数据创建人 {ObjectId(), 登录名即NT账号, 用户姓名, 工号}
	private List<TeUser> curResps;                          //当前责任人
	private List<ObjectId> file;							// 业务关联附件
	private List<Field> fields = new ArrayList<Field>();
	private String flowId;
	private String flowTmplId;
	private List<TeIdNameCnBt> linkedBizs;					// 关联业务
	private List<TeIdNameCnBt> linkedTestPlans;					// 关联测试计划
	private List<TeIdNameCnBt> hangUpBizs;					// 挂接业务
	private TeFlowTask hangOutTask;
	private ObjectId hangUpBizId;							// 主业务id
	private TeIdNameCn hangUpBiz;							// 主业务
	private String allPrdCtlg;    //业务的全路径
	private String isChild;    								// 是否是子业务
	private String curRespsName;    						// 当前责任人名称
    private TeIdNameCn prj;  // 项目信息
    
    // 标识是否所有的父业务都处于特殊状态，用于支持业务视图显示操作
    // 当子业务已经完成的时候，需要在子业务的业务视图上显示父业务操作节点
    // 当所有的父业务都处于特殊状态的时候，则不显示操作节点
    private Boolean isAllParentBizsInSpecialStatus;
    
    /**
     * 局方需求单号
     */
    private String custReqNo;
    /**
     * 局方需求提出人
     */
    private String custReqAddUser;
    /**
     * 局方需求提出部门
     */
    private String custReqAddDept;
    
    /**
     * 节点信息
     */
    private Map<ObjectId, TeBiz2NodeInfo> nodeInfo;
    
    /**
     * 分析说明
     */
    private String analysisDesc;
    
    /**
     * 抄送邮件
     */
    private String ccMails;
    
    /**
     * 研发工作量
     */
    private String devWorkNum;

	/**
	 * 需求配置类型
	 */
	private ObjectId demandSetTypeId;

	/**
	 * 拓展字段
	 */
	private List<ExpandField> expandField;


    //	CTC_公共技术支持  相关字段
	/**
	 * 字段-服务
	 */
	private String service;
	/**
	 * 字段-研发测试
	 */
	private String rdTest;
	/**
	 * 字段-版本号
	 */
	private String verNo;
	/**
	 * 字段-研发云类型
	 */
	private List<Object> rdCloudType;
	/**
	 * 字段-开发地址
	 */
	private String devAddress;
	/**
	 * 字段-研发云状态
	 */
	private List<Object> rdCloudStatus;
	/**
	 * 字段-实际工作量
	 */
	private String actualWorkDay;

	/**
	 * 字段值-测试用例-前置条件
	 */
	private String preConditions;
	/**
	 * 字段值-测试用例-预期结果
	 */
	private String expectedResults;
	/**
	 * 字段值-优先级
	 */
	private String priority;

	/**
	 * 字段值-测试用例-测试步骤
	 */
	private String testcaseSteps;

	public BizView(){
	}

	public String getAllPrdCtlg() {
		return allPrdCtlg;
	}

	public void setAllPrdCtlg(String allPrdCtlg) {
		this.allPrdCtlg = allPrdCtlg;
	}

	public List<TeIdNameCnBt> getLinkedTestPlans() {
		return linkedTestPlans;
	}

	public void setLinkedTestPlans(List<TeIdNameCnBt> linkedTestPlans) {
		this.linkedTestPlans = linkedTestPlans;
	}

	public BizView(TeBiz teBiz){
		this.id = teBiz.getId();
		this.isValid = teBiz.getIsValid();
		this.prjDefId = null == teBiz.getPrjDefId() ? null : teBiz.getPrjDefId().toHexString();
		TeBiz2BizType type = teBiz.getBizType();
		if(null != type){
			SysDef bizType = new SysDef();
			bizType.setId(type.getCid());
			bizType.setDefName(type.getName());
			bizType.setCodeName(type.getCodeName());
			this.bizType = bizType;
		}
		TeBiz2BizType subType = teBiz.getSubBizType();
		if(null != subType){
			SysDef subBizType = new SysDef();
			subBizType.setId(subType.getCid());
			subBizType.setDefName(subType.getName());
			subBizType.setCodeName(subType.getCodeName());
			this.subBizType = subBizType;
		}
		this.code = teBiz.getCode();
		this.name = teBiz.getName();
		this.desc = teBiz.getDesc();
		TeIdNameCn statusT = teBiz.getStatus();
		if(null != statusT){
			SysDef status = new SysDef();
			status.setId(statusT.getCid());
			status.setDefName(statusT.getName());
			status.setCodeName(statusT.getCodeName());
			this.status = status;
		}
		TeIdNameCnPtIds prdCtlgT = teBiz.getPrdCtlg();
		if(null != prdCtlgT){
			SysDef prdCtlg = new SysDef();
			prdCtlg.setId(prdCtlgT.getCid());
			prdCtlg.setDefName(prdCtlgT.getName());
			if(StringUtil.isNotNull(prdCtlgT.getCodeName())){
				List<ObjectId> pp = StringUtil.transIds2List(prdCtlgT.getCodeName(), ",", ObjectId.class);
				if(pp != null && !pp.isEmpty()){
					pp.remove(pp.size() - 1);
					if(pp.size() > 0){
						prdCtlg.setParentDefPath("," + StringUtil.join(pp, ",") + ",");
					}
				}
			}
			this.prdCtlg = prdCtlg;
		}
		TeIdName prdT = teBiz.getPrd();
		if(null != prdT){
			SysDef prd = new SysDef();
			prd.setId(prdT.getCid());
			prd.setDefName(prdT.getName());
			this.prd = prd;
		}
		this.issueType = null ==  teBiz.getIssueType() ? null : teBiz.getIssueType().toHexString();
		List<TeUser> respUsers = teBiz.getRespUser();
		this.respUser = respUsers;
		//TODO handleLog
		this.handleLog = teBiz.getHandleLog();
		List<ObjectId> preBizList = teBiz.getPreBizs();
		if(null != preBizList && !preBizList.isEmpty()){
			List<String> preBizs = new ArrayList<String>();
			for(ObjectId objectId : preBizList){
				preBizs.add(objectId.toHexString());
			}
			this.preBizs = preBizs;
		}
		this.remark = teBiz.getRemark();
		this.needSurvey = teBiz.getNeedSurvey();
		//TODO linkedPrjGroup
		this.linkedPrjGroup = teBiz.getLinkedPrjGroup();
		this.donePercent = teBiz.getDonePercent();
		this.needHelp = teBiz.getNeedHelp();
		this.conclusion = teBiz.getConclusion();
		this.issueCode = teBiz.getIssueCode();
		this.riskAffect = teBiz.getRiskAffect();
		this.relyOn = null == teBiz.getRelyOn() ? null : teBiz.getRelyOn().toHexString();
		this.subSystem = teBiz.getSubSystem();
		this.submitDept = teBiz.getSubmitDept();
		this.issuePriority = null == teBiz.getIssuePriority() ? null : teBiz.getIssuePriority().toHexString();
		this.proposal = teBiz.getProposal();
		this.issueStatus = null == teBiz.getIssueStatus() ? null : teBiz.getIssueStatus().toHexString();
		this.resolveStep = teBiz.getResolveStep();
		this.issueSummary = teBiz.getIssueSummary();
		this.customerResp = teBiz.getCustomerResp();
		this.customerCoordinator = teBiz.getCustomerCoordinator();
		this.planStartDate = teBiz.getPlanStartDate();
		this.planEndDate = teBiz.getPlanEndDate();
		this.actualCloseTime = teBiz.getActualCloseTime();
		this.addTime = teBiz.getAddTime();
		TeUser addUer = teBiz.getAddUser();
		// 解决业务视图addUser不存在的情况导致的页面异常
		if(addUer == null) {
			addUser = new TeUser();
		}
		this.addUser = addUer;
		ObjectId flowId= teBiz.getFlowId();
		if(null != flowId){
			this.flowId = flowId.toHexString();
		}
		this.linkedBizs = teBiz.getLinkedBizs();
		if(teBiz.getHangUpBiz() != null) {
			this.hangUpBizId = teBiz.getHangUpBiz().getCid();
		}
		this.hangUpBiz = teBiz.getHangUpBiz();
		this.curResps = teBiz.getCurResps();
		//this.linkedTestPlans = teBiz.getLinkedTestPlans();
        TeIdNameCn prjT = teBiz.getPrj();
        if(null != prjT){
            this.prj = prjT;
        }
        Map<ObjectId, Object> custFieldInfo = teBiz.getCustFieldInfo();
        if(custFieldInfo != null){
            this.custReqNo = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_CUST_REQ_NO), String.class);
            this.custReqAddUser = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_CUST_REQ_ADD_USER), String.class);
            this.custReqAddDept = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_CUST_REQ_ADD_DEPT), String.class);
        
            this.analysisDesc = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_ANALYSIS_DESC), String.class);
            
            this.devWorkNum = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_DEV_WORK_NUM), String.class);

			this.demandSetTypeId = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_DEMAND_SET_TYPE), ObjectId.class);

			/*
			* 资源开通-新增字段
			* */
			this.service = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_SERVICE), String.class);
			this.rdTest = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_RD_OR_TEST), String.class);
			this.verNo = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_VER_NO), String.class);
			this.rdCloudType = JsonUtil.toBean(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_RD_CLOUD_TYPE), String.class), List.class);
			this.devAddress = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_DEV_ADDRESS), String.class);
			this.rdCloudStatus = JsonUtil.toBean(StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_RD_CLOUD_STATUS), String.class), List.class);
			this.actualWorkDay = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_ACTUAL_WORK_DAY), String.class);

			/**
			 * 测试步骤
			 */
			this.testcaseSteps = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_BIZ_TEST_STEPS), String.class);
			/**
			 * 优先级
			 */
			this.priority = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_CASE_LEVEL), String.class);
			/**
			 * 预期结果
			 */
			this.expectedResults = StringUtil.to(custFieldInfo.get(SysDefConstants.DEF_ID_BIZ_EXPECT_OUTPUT), String.class);
			/**
			 * 前置条件
			 */
			this.preConditions = StringUtil.to(custFieldInfo.get(SysDefConstants.FIELD_ID_PRE_CONDITION), String.class);

		}
        
        if(null != teBiz.getCcMails()) {
        	this.ccMails = teBiz.getCcMails();
        }
        
        Map<ObjectId, TeBiz2NodeInfo> nodeInfo = teBiz.getNodeInfo();
        if(null != nodeInfo) {
        	this.nodeInfo = nodeInfo;
        }        
    }

	public ObjectId getId() {
		return id;
	}
	public void setId(ObjectId id) {
		this.id = id;
	}
	public Boolean getIsValid() {
		return isValid;
	}
	public void setIsValid(Boolean isValid) {
		this.isValid = isValid;
	}
	public String getPrjDefId() {
		return prjDefId;
	}
	public void setPrjDefId(String prjDefId) {
		this.prjDefId = prjDefId;
	}
	public SysDef getBizType() {
		return bizType;
	}
	public void setBizType(SysDef bizType) {
		this.bizType = bizType;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDesc() {
		return desc;
	}
	public void setDesc(String desc) {
		this.desc = desc;
	}
	public SysDef getStatus() {
		return status;
	}
	public void setStatus(SysDef status) {
		this.status = status;
	}
	public SysDef getPrdCtlg() {
		return prdCtlg;
	}
	public void setPrdCtlg(SysDef prdCtlg) {
		this.prdCtlg = prdCtlg;
	}
	public SysDef getPrd() {
		return prd;
	}
	public void setPrd(SysDef prd) {
		this.prd = prd;
	}

	public String getIssueType() {
		return issueType;
	}
	public void setIssueType(String issueType) {
		this.issueType = issueType;
	}
	public List<TeUser> getRespUser() {
		return respUser;
	}
	public void setRespUser(List<TeUser> respUser) {
		this.respUser = respUser;
	}
	public List<TeBiz2HandleLog> getHandleLog() {
		return handleLog;
	}
	public void setHandleLog(List<TeBiz2HandleLog> handleLog) {
		this.handleLog = handleLog;
	}
	public List<String> getPreBizs() {
		return preBizs;
	}
	public void setPreBizs(List<String> preBizs) {
		this.preBizs = preBizs;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public Boolean getNeedSurvey() {
		return needSurvey;
	}
	public void setNeedSurvey(Boolean needSurvey) {
		this.needSurvey = needSurvey;
	}
	public List<TeIdName> getLinkedPrjGroup() {
		return linkedPrjGroup;
	}
	public void setLinkedPrjGroup(List<TeIdName> linkedPrjGroup) {
		this.linkedPrjGroup = linkedPrjGroup;
	}
	public Integer getDonePercent() {
		return donePercent;
	}
	public void setDonePercent(Integer donePercent) {
		this.donePercent = donePercent;
	}
	public String getNeedHelp() {
		return needHelp;
	}
	public void setNeedHelp(String needHelp) {
		this.needHelp = needHelp;
	}
	public String getConclusion() {
		return conclusion;
	}
	public void setConclusion(String conclusion) {
		this.conclusion = conclusion;
	}
	public String getIssueCode() {
		return issueCode;
	}
	public void setIssueCode(String issueCode) {
		this.issueCode = issueCode;
	}
	public String getRiskAffect() {
		return riskAffect;
	}
	public void setRiskAffect(String riskAffect) {
		this.riskAffect = riskAffect;
	}
	public String getRelyOn() {
		return relyOn;
	}
	public List<TeUser> getCurResps() {
		return curResps;
	}

	public void setCurResps(List<TeUser> curResps) {
		this.curResps = curResps;
	}

	public void setRelyOn(String relyOn) {
		this.relyOn = relyOn;
	}
	public String getSubSystem() {
		return subSystem;
	}
	public void setSubSystem(String subSystem) {
		this.subSystem = subSystem;
	}
	public String getSubmitDept() {
		return submitDept;
	}
	public void setSubmitDept(String submitDept) {
		this.submitDept = submitDept;
	}
	public String getIssuePriority() {
		return issuePriority;
	}
	public void setIssuePriority(String issuePriority) {
		this.issuePriority = issuePriority;
	}
	public String getProposal() {
		return proposal;
	}
	public void setProposal(String proposal) {
		this.proposal = proposal;
	}
	public String getIssueStatus() {
		return issueStatus;
	}
	public void setIssueStatus(String issueStatus) {
		this.issueStatus = issueStatus;
	}
	public String getResolveStep() {
		return resolveStep;
	}
	public void setResolveStep(String resolveStep) {
		this.resolveStep = resolveStep;
	}
	public String getIssueSummary() {
		return issueSummary;
	}
	public void setIssueSummary(String issueSummary) {
		this.issueSummary = issueSummary;
	}
	public String getCustomerResp() {
		return customerResp;
	}
	public void setCustomerResp(String customerResp) {
		this.customerResp = customerResp;
	}
	public String getCustomerCoordinator() {
		return customerCoordinator;
	}
	public void setCustomerCoordinator(String customerCoordinator) {
		this.customerCoordinator = customerCoordinator;
	}
	public Date getPlanStartDate() {
		return planStartDate;
	}
	public void setPlanStartDate(Date planStartDate) {
		this.planStartDate = planStartDate;
	}
	public Date getPlanEndDate() {
		return planEndDate;
	}
	public void setPlanEndDate(Date planEndDate) {
		this.planEndDate = planEndDate;
	}
	public Date getActualCloseTime() {
		return actualCloseTime;
	}
	public void setActualCloseTime(Date actualCloseTime) {
		this.actualCloseTime = actualCloseTime;
	}
	public Date getAddTime() {
		return addTime;
	}
	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}
	public TeUser getAddUser() {
		return addUser;
	}
	public void setAddUser(TeUser addUser) {
		this.addUser = addUser;
	}
	public List<ObjectId> getFile() {
		return file;
	}
	public void setFile(List<ObjectId> file) {
		this.file = file;
	}

	public List<Field> getFields() {
		return fields;
	}

	public void setFields(List<Field> fields) {
		this.fields = fields;
	}

	public String getFlowId() {
		return flowId;
	}

	public void setFlowId(String flowId) {
		this.flowId = flowId;
	}

	public String getFlowTmplId() {
		return flowTmplId;
	}

	public void setFlowTmplId(String flowTmplId) {
		this.flowTmplId = flowTmplId;
	}

	public List<TeIdNameCnBt> getLinkedBizs() {
		return linkedBizs;
	}

	public void setLinkedBizs(List<TeIdNameCnBt> linkedBizs) {
		this.linkedBizs = linkedBizs;
	}

	public List<TeIdNameCnBt> getHangUpBizs() {
		return hangUpBizs;
	}

	public void setHangUpBizs(List<TeIdNameCnBt> hangUpBizs) {
		this.hangUpBizs = hangUpBizs;
	}

	public TeFlowTask getHangOutTask() {
		return hangOutTask;
	}

	public void setHangOutTask(TeFlowTask hangOutTask) {
		this.hangOutTask = hangOutTask;
	}

	public ObjectId getHangUpBizId() {
		return hangUpBizId;
	}

	public void setHangUpBizId(ObjectId hangUpBizId) {
		this.hangUpBizId = hangUpBizId;
	}

	public SysDef getSubBizType() {
		return subBizType;
	}

	public void setSubBizType(SysDef subBizType) {
		this.subBizType = subBizType;
	}

	public TeIdNameCn getHangUpBiz() {
		return hangUpBiz;
	}

	public void setHangUpBiz(TeIdNameCn hangUpBiz) {
		this.hangUpBiz = hangUpBiz;
	}

	public String getIsChild() {
		return isChild;
	}

	public void setIsChild(String isChild) {
		this.isChild = isChild;
	}

	public String getCurRespsName() {
		return curRespsName;
	}

	public void setCurRespsName(String curRespsName) {
		this.curRespsName = curRespsName;
	}

    public TeIdNameCn getPrj() {
        return prj;
    }

    public void setPrj(TeIdNameCn prj) {
        this.prj = prj;
    }

    public String getCustReqNo() {
        return custReqNo;
    }

    public void setCustReqNo(String custReqNo) {
        this.custReqNo = custReqNo;
    }

    public String getCustReqAddUser() {
        return custReqAddUser;
    }

    public void setCustReqAddUser(String custReqAddUser) {
        this.custReqAddUser = custReqAddUser;
    }

    public String getCustReqAddDept() {
        return custReqAddDept;
    }

    public void setCustReqAddDept(String custReqAddDept) {
        this.custReqAddDept = custReqAddDept;
    }

	public Boolean getIsAllParentBizsInSpecialStatus() {
		return isAllParentBizsInSpecialStatus;
	}

	public void setIsAllParentBizsInSpecialStatus(Boolean isAllParentBizsInSpecialStatus) {
		this.isAllParentBizsInSpecialStatus = isAllParentBizsInSpecialStatus;
	}

	public Map<ObjectId, TeBiz2NodeInfo> getNodeInfo() {
		return nodeInfo;
	}

	public void setNodeInfo(Map<ObjectId, TeBiz2NodeInfo> nodeInfo) {
		this.nodeInfo = nodeInfo;
	}

	public String getAnalysisDesc() {
		return analysisDesc;
	}

	public void setAnalysisDesc(String analysisDesc) {
		this.analysisDesc = analysisDesc;
	}

	public String getCcMails() {
		return ccMails;
	}

	public void setCcMails(String ccMails) {
		this.ccMails = ccMails;
	}

	public String getDevWorkNum() {
		return devWorkNum;
	}

	public void setDevWorkNum(String devWorkNum) {
		this.devWorkNum = devWorkNum;
	}

	public ObjectId getDemandSetTypeId() {
		return demandSetTypeId;
	}

	public void setDemandSetTypeId(ObjectId demandSetTypeId) {
		this.demandSetTypeId = demandSetTypeId;
	}

	public List<ExpandField> getExpandField() {
		return expandField;
	}

	public void setExpandField(List<ExpandField> expandField) {
		this.expandField = expandField;
	}

	/*
	 * CTC_公共技术支持
	 * 资源开通  相关字段 get  set方法
	 * start
	 * */
	public String getService() {
		return service;
	}
	public void setService(String service) {
		this.service = service;
	}
	public String getRdTest() {
		return rdTest;
	}
	public void setRdTestId(String rdTestId) {
		this.rdTest = rdTest;
	}
	public String getVerNo() {
		return verNo;
	}
	public void setVerNo(String verNo) {
		this.verNo = verNo;
	}
	public List<Object> getRdCloudType() {
		return rdCloudType;
	}
	public void setRdCloudType(List<Object> rdCloudType) {
		this.rdCloudType = rdCloudType;
	}
	public String getDevAddress() {
		return devAddress;
	}
	public void setDevAddress(String devAddress) {
		this.devAddress = devAddress;
	}
	public List<Object> getRdCloudStatus() {
		return rdCloudStatus;
	}
	public void setRdCloudStatus(List<Object> rdCloudStatus) {
		this.rdCloudStatus = rdCloudStatus;
	}
	public String getActualWorkDay() {
		return actualWorkDay;
	}
	public void setActualWorkDay(String actualWorkDay) {
		this.actualWorkDay = actualWorkDay;
	}
	/*
	 * CTC_公共技术支持
	 * 资源开通  相关字段 get  set方法
	 * end
	 * */

	public String getTestcaseSteps() {
		return testcaseSteps;
	}

	public void setTestcaseSteps(String testcaseSteps) {
		this.testcaseSteps = testcaseSteps;
	}

	public String getPriority() {
		return priority;
	}

	public void setPriority(String priority) {
		this.priority = priority;
	}

	public String getExpectedResults() {
		return expectedResults;
	}

	public void setExpectedResults(String expectedResults) {
		this.expectedResults = expectedResults;
	}

	public String getPreConditions() {
		return preConditions;
	}

	public void setPreConditions(String preConditions) {
		this.preConditions = preConditions;
	}
}
