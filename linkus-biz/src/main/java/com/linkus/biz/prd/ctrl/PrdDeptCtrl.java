package com.linkus.biz.prd.ctrl;

import com.linkus.base.db.base.field.DFN;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.biz.prd.model.bo.PrdCtlg;
import com.linkus.biz.prd.service.IPrdDeptService;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTree;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.model.TeSysDefRoleUser2User;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import com.linkus.sysuser.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/prd/dept")
public class PrdDeptCtrl extends BaseCtrl {

    @Resource
    private ISysUserService sysUserService;
    @Resource
    private IPrdDeptService prdDeptService;
    @Resource
    private ISysDefService sysDefService;
    @Resource
    private ISysDefRoleUserService sysDefRoleUserService;

    /**
     * 查询团队树根目录
     */
    @RequestMapping("/getDeptRoot")
    public CommonResult<PrdCtlg> getDeptRoot(@RequestParam(value = "prdId") ObjectId prdId) {
        TeSysUser loginUser = getTeSysUser();
        return CommonResult.success(prdDeptService.getDeptRoot(prdId, loginUser));
    }

    /**
     * 获取团队小组树
     */
    @RequestMapping("/getLevel2PrdDeptTree")
    public CommonResult<SysDefTree> getLevel2PrdDeptTree(@RequestParam(value = "deptRootId") ObjectId deptRootId) {
        Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n());
        SysDefTree tree = sysDefService.getLevel2SysDefTreeById(deptRootId, sort, SysDefTypeCodeName.DEPT.getValue());
        return CommonResult.success(tree);
    }

    /**
     * 查询展开二级树
     */
    @RequestMapping("/getPrdDeptChildTree")
    public CommonResult<List<SysDefTree>> getPrdDeptChildTree(@RequestParam(value = "currentDeptId") ObjectId currentDeptId) {
        if (null == currentDeptId) {
            return CommonResult.success(Collections.emptyList());
        }
        Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n());
        List<SysDefTree> tree = sysDefService.getSysDefTreeByNode(currentDeptId, sort, SysDefTypeCodeName.DEPT.getValue());
        return CommonResult.success(tree);
    }

    /**
     * 查询展开二级树
     * 【由于getPrdDeptChildTree返回值前端不好取值，重构该方法，用BaseCtrl的returnResult返回结果】
     */
    @RequestMapping("/getPrdDept2ChildTree")
    public void getPrdDept2ChildTree(@RequestParam(value = "current.id", required = false) ObjectId currentDeptId) {
        if (null != currentDeptId) {
            Sort sort = Sort.by(Sort.Direction.ASC, DFN.sysDef__defNo.n());
            List<SysDefTree> tree = sysDefService.getSysDefTreeByNode(currentDeptId, sort, SysDefTypeCodeName.DEPT.getValue());
            returnResult(tree);
        }
    }

    /**
     * 创建小组
     */
    @RequestMapping("/createDeptGroup")
    public CommonResult<String> createDeptGroup(@RequestParam(value = "parentId") ObjectId parentId,
                                                @RequestParam(value = "deptGroupName") String deptGroupName,
                                                @RequestParam(value = "srcDefId") ObjectId srcDefId) {
        List<SysDef> sysDefList = sysDefService.getSysDefsByParent(parentId, SysDefTypeCodeName.DEPT);
        Integer maxDefNo = 0;
        if (null != sysDefList && !sysDefList.isEmpty()) {
            for (SysDef sysDef : sysDefList) {
                if (sysDef.getDefNo() != null && sysDef.getDefNo() > maxDefNo) {
                    maxDefNo = sysDef.getDefNo();
                }
                if (sysDef.getDefName().equals(deptGroupName)) {
                    throw BusinessException.initExc("父目录下已存在该部门小组名称，请修改后再提交！");
                }
            }
        }
        TeSysUser loginUser = getTeSysUser();
        SysDef sysDef = sysDefService.createDef4ParentId(SysDefTypeCodeName.DEPT,
                srcDefId,
                deptGroupName,
                parentId,
                null,
                maxDefNo + 1,
                loginUser.trans2User());
        return CommonResult.success(sysDef.getId().toHexString());
    }

    /**
     * 修改小组
     */
    @RequestMapping("/updatePrdDept")
    public CommonResult<Void> updatePrdDept(@RequestParam(value = "prdDeptId") ObjectId prdDeptId,
                                            @RequestParam(value = "prdDeptName") String prdDeptName) {
        prdDeptService.updatePrdDept(prdDeptId, prdDeptName);
        return CommonResult.success();
    }

    /**
     * 删除小组
     */
    @RequestMapping("/deletePrdDept")
    public CommonResult<Void> deletePrdDept(@RequestParam(value = "prdDeptId") ObjectId prdDeptId) {
        prdDeptService.deletePrdDept(prdDeptId);
        return CommonResult.success();
    }

    /**
     * 获取当前团队树下人员
     */
    @RequestMapping("/getPrdDeptUsersByDeptId")
    public CommonResult<List<Map<String, Object>>> getPrdDeptUsersByDeptId(@RequestParam(value = "prdDeptId") ObjectId prdDeptId) {
        return CommonResult.success(prdDeptService.getPrdDeptUsersByDeptId(prdDeptId));
    }

    /**
     * 添加小组成员
     */
    @RequestMapping("/addRoleUser")
    public CommonResult<Void> addRoleUser(@RequestParam(value = "prdDeptId") ObjectId prdDeptId,
                                          @RequestParam(value = "userId") ObjectId userId) {
        TeSysUser loginUser = getTeSysUser();
        prdDeptService.addRoleUser(prdDeptId, userId, loginUser);
        return CommonResult.success();
    }

    /**
     * 删除小组成员
     */
    @RequestMapping("/deleteRoleUsers")
    public CommonResult<Void> deleteRoleUsers(@RequestParam("roleUserIds[]") List<ObjectId> roleUserIds) {
        if (CollectionUtils.isEmpty(roleUserIds)) {
            throw BusinessException.initExc("用户id列表为空！");
        }
        sysDefRoleUserService.removeByIds(roleUserIds);
        return CommonResult.success();
    }

    /**
     * 获取父级目录业务管理员
     */
    @RequestMapping("/getPrdCtlgAdmin")
    public CommonResult<List<TeSysDefRoleUser2User>> getPrdCtlgAdmin(@RequestParam("prdCtlgId") ObjectId prdCtlgId) {
        return CommonResult.success(prdDeptService.getPrdCtlgAdmin(prdCtlgId));
    }

    /**
     * 导入团队小组人员
     */
    @RequestMapping("/importTeamMenbers")
    public CommonResult<Map<String, Object>> importTeamMenbers(HttpServletRequest request, MultipartFile file,
                                                               @RequestParam("rootCtlgId") ObjectId rootCtlgId) {

        if (file == null) {
            throw BusinessException.initExc("上传文件为空!");
        }
        if (null == rootCtlgId) {
            throw BusinessException.initExc("根目录ID为空!");
        }

        TeSysUser loginUser = getTeSysUser();
        Map<String, Object> resultMap = prdDeptService.importTeamMenbers(file, rootCtlgId, loginUser);
        return CommonResult.success(resultMap);
    }

    /**
     * 导出团队小组人员
     */
    @RequestMapping("/exportTeamMenbers")
    public void exportTeamMenbers(@RequestParam("rootCtlgId") ObjectId rootCtlgId) {

        if (null == rootCtlgId) {
            throw BusinessException.initExc("根目录ID为空!");
        }

        String[][] dataArray = prdDeptService.exportTeamMenbers(rootCtlgId);
        ExcelUtils.exportExcelData(dataArray, "团队小组人员", getResponse());
    }

    /**
     * 获取当前用户登录信息
     */
    private TeSysUser getTeSysUser() {
        String casLoginUserName = getCasLoginUser();
        return sysUserService.queryByLoginName(casLoginUserName);
    }
}
