package com.linkus.biz.oprtMroLog.ctrl;


import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.PageBean;
import com.linkus.biz.oprtMroLog.model.QueryParam;
import com.linkus.biz.oprtMroLog.service.IOprtAndMroLogService;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/oprtMroLogCtrl")
public class OprtAndMroLogCtrl extends CommonController {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IOprtAndMroLogService oprtAndMroLogService;

    /**
     * 权限控制
     */
    @GetMapping("/accessControl")
    public CommonResult<List<Map<String,Object>>> accessControl(@RequestParam("roleIds[]") List<ObjectId> roleIds, @RequestParam("prdCtlgId") ObjectId prdCtlgId){
        TeUser loginUser = getLoginUser();
        return CommonResult.success(oprtAndMroLogService.accessControl(roleIds,prdCtlgId,loginUser.getUserId()));
    }

    /**
     * 查询
     */
    @RequestMapping("/query")
    public CommonResult<PageBean> query(@RequestBody QueryParam param){
        TeUser loginUser = getLoginUser();
        return CommonResult.success(oprtAndMroLogService.query(param,loginUser.getUserId()));
    }

    /**
     * 查询周期清单
     */
    @RequestMapping("/queryWeek")
    public CommonResult<List<String>> queryWeek(){
        TeUser loginUser = getLoginUser();
        return CommonResult.success(oprtAndMroLogService.queryWeek(loginUser.getUserId()));
    }

    @RequestMapping("/initNewWeek")
    public CommonResult<Void> initNewWeek(@RequestParam(value = "prdCtlgId",required = false) ObjectId prdCtlgId){
        TeSysUser loginUser = getTeSysUser();
        oprtAndMroLogService.initNewWeek(prdCtlgId,loginUser);
        return CommonResult.success();
    }

    /**
     * 导出
     */
    @RequestMapping("/export")
    public CommonResult<Void> export(@RequestBody QueryParam param)throws IOException {
        TeUser loginUser = getLoginUser();
        HttpServletResponse response = getResponse();
        oprtAndMroLogService.export(param,loginUser.getUserId(),response);
        return CommonResult.success();
    }

    /**
     * 获取当前登录人信息
     */
    private TeUser getLoginUser(){
        String loginName = getLoginName();
        TeSysUser sysUser = sysUserService.queryByLoginName(loginName);
        TeUser user = sysUser.trans2User();
        return user;
    }

    private TeSysUser getTeSysUser(){
        String loginName = getLoginName();
        TeSysUser sysUser = sysUserService.queryByLoginName(loginName);
        return sysUser;
    }

}
