package com.linkus.biz.dbd.model.vo;

/**
 * 员工任务数据
 * <AUTHOR>
 *
 */
public class PrdDbdUserTaskBarChartDataVo implements Comparable<PrdDbdUserTaskBarChartDataVo> {
	// 用户姓名
	private String userName;
	// 总数
	private int totalCount;
	// 已完成任务数
	private int completedCount;
	// 未完成任务数
	private int unCompletedCount;

	@Override
	public int compareTo(PrdDbdUserTaskBarChartDataVo o) {
		return this.userName.compareTo(o.getUserName());
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public void setTotalCount(int totalCount) {
		this.totalCount = totalCount;
	}

	public int getCompletedCount() {
		return completedCount;
	}

	public void setCompletedCount(int completedCount) {
		this.completedCount = completedCount;
	}

	public int getUnCompletedCount() {
		return unCompletedCount;
	}

	public void setUnCompletedCount(int unCompletedCount) {
		this.unCompletedCount = unCompletedCount;
	}

}
