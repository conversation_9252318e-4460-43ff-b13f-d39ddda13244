package com.linkus.biz.bulkadd.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.bulkadd.service.IBulkAddValidateDictDataService;
import com.linkus.event.dao.IPrjInfoDao;
import com.linkus.event.model.TePrjInfo;


@Service("PrjBulkAddValidateDictDataServiceImpl")
public class PrjBulkAddValidateDictDataServiceImpl implements IBulkAddValidateDictDataService{
	@Autowired
	private IPrjInfoDao prjInfoDao;
	@Override
	public Map<Object, Object> getDictDatas(ObjectId fieldId,List<Object> excelPrjCodeDatas) {
		Map<Object, Object> dictDatasMap = new HashMap<Object, Object>();
		if( null != excelPrjCodeDatas && excelPrjCodeDatas.size() > 0){
			List<String> prjNameCodes = new ArrayList<String>();
			for(Object excelPrjCode : excelPrjCodeDatas){
				if(StringUtil.isNotNull(excelPrjCode)) {
					prjNameCodes.add(StringUtil.getNotNullStr(excelPrjCode));
				}
			}
			if(null != prjNameCodes && prjNameCodes.size() > 0){
				List<IDbCondition> conds = new ArrayList<IDbCondition>();
				conds.add(new DC_I(DFN.prjInfo__prjCode, prjNameCodes));
				conds.add(new DC_E(DFN.common_isValid, false, true));
				List<TePrjInfo> prjInfos = prjInfoDao.findByConds(conds, null);
				if(prjInfos.size() > 0) {
					for(TePrjInfo prjInfo : prjInfos) {
						dictDatasMap.put(prjInfo.getPrjCode(), prjInfo.getPrjId());
					}
				}
			}
		}
		
		return dictDatasMap;
	}

}
