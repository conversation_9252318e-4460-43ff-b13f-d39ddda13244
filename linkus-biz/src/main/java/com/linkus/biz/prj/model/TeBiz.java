//package com.linkus.biz.prj.model;
//
//import java.util.Date;
//import java.util.List;
//
//import org.bson.types.ObjectId;
//import org.springframework.data.annotation.Id;
//import org.springframework.data.mongodb.core.mapping.Document;
//import org.springframework.format.annotation.DateTimeFormat;
//
//@Document(collection = "biz")
//@Deprecated
//public class TeBiz {
//
//	@Id
//	private String id;										// id
//	private Boolean isValid;								// 有/无效
//	private ObjectId prjDefId;								// 项目ID
//	private TeBiz2Type type;								// 业务类型 {业务类型ID, 类型类型名称},即问题管理
//	private Integer code;									// 业务编号(自增长的数字，必须是全系统唯一),要做的,是系统级的
//	private String name;									// 业务名称
//	private String desc;									// 业务描述
//	//TODO
//	private TeBiz2Status status;							// 由ObjectId更改为对象 业务状态ObjectId(),也是系统级的
//
//	private ObjectId issueType;								// 问题类别ObjectId(), 分范围管理、项目管理、人力资源、项目实施等.
//	private List<TeBiz2RespUser> respUser;					// 如果不是系统的用户，则只有userName，其它都为空 (责任人,可以是多个)
//	private List<TeBiz2HandleLog> handleLog;				// 处理过程
//
//	//TODO
//	private List<ObjectId> preBizs;							//父id
//	private String remark;									//备注
//	private Boolean needSurvey;								//是否调研
//	private List<TeBiz2LinkedPrjGroup> linkedPrjGroup;		//计划调研对象
//	private Integer donePercent;							//完成进度
//	private String needHelp;								//需协调解决事项
//	private String conclusion;								//不知道啥
//
//	private String issueCode;			 					// 问题编码，格式WT_123，自然数增长
//	private String riskAffect;								// 风险影响
//	private ObjectId relyOn;								// 依赖问题类型ObjectId(), CMC问题管理中问题类别依赖的另一个问题类别
//	private String subSystem;								// 所属子系统
//	private String submitDept;								// 提出部门
//	private ObjectId issuePriority;							// 问题优先级ObjectId()，分重要紧急和重要不紧急
//	private String proposal;								// 解决建议
//	private ObjectId issueStatus;							// 问题状态ObjectId()，分打开、挂起、关闭和延期
//	private String resolveStep;								// 解决措施
//	private String issueSummary;							// 问题总结
//	private String customerResp;							// 局方负责人
//	private String customerCoordinator;						// 局方协调
//	@DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
//	private Date planStartDate;								// 计划开始时间
//	@DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
//	private Date planEndDate;								// 计划结束时间
//	@DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
//	private Date actualCloseTime;							// 实际关闭时间
//	@DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
//	private Date addTime;									// 数据创建时间
//	private TeBiz2AddUser addUser;							// 数据创建人 {ObjectId(), 登录名即NT账号, 用户姓名, 工号}
//	private List<ObjectId> file;							// 业务关联附件
//
//	public String getId() {
//		return id;
//	}
//
//	public void setId(String id) {
//		this.id = id;
//	}
//
//	public Boolean getIsValid() {
//		return isValid;
//	}
//
//	public void setIsValid(Boolean isValid) {
//		this.isValid = isValid;
//	}
//
//	public ObjectId getPrjDefId() {
//		return prjDefId;
//	}
//
//	public void setPrjDefId(ObjectId prjDefId) {
//		this.prjDefId = prjDefId;
//	}
//
//	public TeBiz2Type getType() {
//		return type;
//	}
//
//	public void setType(TeBiz2Type type) {
//		this.type = type;
//	}
//
//	public Integer getCode() {
//		return code;
//	}
//
//	public void setCode(Integer code) {
//		this.code = code;
//	}
//
//	public String getName() {
//		return name;
//	}
//
//	public void setName(String name) {
//		this.name = name;
//	}
//
//	public String getDesc() {
//		return desc;
//	}
//
//	public void setDesc(String desc) {
//		this.desc = desc;
//	}
//
//
//	public ObjectId getIssueType() {
//		return issueType;
//	}
//
//	public void setIssueType(ObjectId issueType) {
//		this.issueType = issueType;
//	}
//
//	public List<TeBiz2RespUser> getRespUser() {
//		return respUser;
//	}
//
//	public void setRespUser(List<TeBiz2RespUser> respUser) {
//		this.respUser = respUser;
//	}
//
//	public List<TeBiz2HandleLog> getHandleLog() {
//		return handleLog;
//	}
//
//	public void setHandleLog(List<TeBiz2HandleLog> handleLog) {
//		this.handleLog = handleLog;
//	}
//
//	public String getIssueCode() {
//		return issueCode;
//	}
//
//	public void setIssueCode(String issueCode) {
//		this.issueCode = issueCode;
//	}
//
//	public String getRiskAffect() {
//		return riskAffect;
//	}
//
//	public void setRiskAffect(String riskAffect) {
//		this.riskAffect = riskAffect;
//	}
//
//	public ObjectId getRelyOn() {
//		return relyOn;
//	}
//
//	public void setRelyOn(ObjectId relyOn) {
//		this.relyOn = relyOn;
//	}
//
//	public String getSubSystem() {
//		return subSystem;
//	}
//
//	public void setSubSystem(String subSystem) {
//		this.subSystem = subSystem;
//	}
//
//	public String getSubmitDept() {
//		return submitDept;
//	}
//
//	public void setSubmitDept(String submitDept) {
//		this.submitDept = submitDept;
//	}
//
//	public ObjectId getIssuePriority() {
//		return issuePriority;
//	}
//
//	public void setIssuePriority(ObjectId issuePriority) {
//		this.issuePriority = issuePriority;
//	}
//
//	public String getProposal() {
//		return proposal;
//	}
//
//	public void setProposal(String proposal) {
//		this.proposal = proposal;
//	}
//
//	public ObjectId getIssueStatus() {
//		return issueStatus;
//	}
//
//	public void setIssueStatus(ObjectId issueStatus) {
//		this.issueStatus = issueStatus;
//	}
//
//	public String getResolveStep() {
//		return resolveStep;
//	}
//
//	public void setResolveStep(String resolveStep) {
//		this.resolveStep = resolveStep;
//	}
//
//	public String getIssueSummary() {
//		return issueSummary;
//	}
//
//	public void setIssueSummary(String issueSummary) {
//		this.issueSummary = issueSummary;
//	}
//
//	public String getCustomerResp() {
//		return customerResp;
//	}
//
//	public void setCustomerResp(String customerResp) {
//		this.customerResp = customerResp;
//	}
//
//	public String getCustomerCoordinator() {
//		return customerCoordinator;
//	}
//
//	public void setCustomerCoordinator(String customerCoordinator) {
//		this.customerCoordinator = customerCoordinator;
//	}
//
//	public Date getPlanStartDate() {
//		return planStartDate;
//	}
//
//	public void setPlanStartDate(Date planStartDate) {
//		this.planStartDate = planStartDate;
//	}
//
//	public Date getPlanEndDate() {
//		return planEndDate;
//	}
//
//	public void setPlanEndDate(Date planEndDate) {
//		this.planEndDate = planEndDate;
//	}
//
//	public Date getActualCloseTime() {
//		return actualCloseTime;
//	}
//
//	public void setActualCloseTime(Date actualCloseTime) {
//		this.actualCloseTime = actualCloseTime;
//	}
//
//	public Date getAddTime() {
//		return addTime;
//	}
//
//	public void setAddTime(Date addTime) {
//		this.addTime = addTime;
//	}
//
//	public TeBiz2AddUser getAddUser() {
//		return addUser;
//	}
//
//	public void setAddUser(TeBiz2AddUser addUser) {
//		this.addUser = addUser;
//	}
//
//	public List<ObjectId> getFile() {
//		return file;
//	}
//
//	public void setFile(List<ObjectId> file) {
//		this.file = file;
//	}
//
//	public List<ObjectId> getPreBizs() {
//		return preBizs;
//	}
//
//	public void setPreBizs(List<ObjectId> preBizs) {
//		this.preBizs = preBizs;
//	}
//
//	public String getRemark() {
//		return remark;
//	}
//
//	public void setRemark(String remark) {
//		this.remark = remark;
//	}
//
//	public Boolean getNeedSurvey() {
//		return needSurvey;
//	}
//
//	public void setNeedSurvey(Boolean needSurvey) {
//		this.needSurvey = needSurvey;
//	}
//
//	public List<TeBiz2LinkedPrjGroup> getLinkedPrjGroup() {
//		return linkedPrjGroup;
//	}
//
//	public void setLinkedPrjGroup(List<TeBiz2LinkedPrjGroup> linkedPrjGroup) {
//		this.linkedPrjGroup = linkedPrjGroup;
//	}
//
//	public Integer getDonePercent() {
//		return donePercent;
//	}
//
//	public void setDonePercent(Integer donePercent) {
//		this.donePercent = donePercent;
//	}
//
//	public String getNeedHelp() {
//		return needHelp;
//	}
//
//	public void setNeedHelp(String needHelp) {
//		this.needHelp = needHelp;
//	}
//
//	public String getConclusion() {
//		return conclusion;
//	}
//
//	public void setConclusion(String conclusion) {
//		this.conclusion = conclusion;
//	}
//
//	public TeBiz2Status getStatus() {
//		return status;
//	}
//
//	public void setStatus(TeBiz2Status status) {
//		this.status = status;
//	}
//
//
//
//}
