<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%
    String ver = "20190422";
%>
<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>每月完成概况</title>

    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <link href="../../01css/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>

    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
<script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>

    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css"/>

    <link href="../../01css/icomoon/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css" />
    <script src="../../01css/font/iconfont.js"></script>

    <script type="text/javascript" src="../../bizQuery/cnpt/queryPrd.js?ver=<%=ver%>"></script>
    <script type="text/javascript" src="../../bizQuery/cnpt/queryPrdBizType.js?ver=<%=ver%>"></script>
    <script type="text/javascript" src="../../bizQuery/cnpt/queryPrjAndPrjSetSelect.js?ver=<%=ver%>"></script>
    <script type="text/javascript" src="../../00scripts/00lib/vue/comboxTree/prdCtlgMultiSelect.js?ver=<%=ver%>"></script>
    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <style>
        [v-cloak] {
            display: none;
        }

        .ivu-cascader .ivu-select-dropdown {
            max-width: calc(100vw - 532px)
        }

        .chart-text {
            text-align: center;
            font-size: 16px;
        }

        .chart-text .num {
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
        }

        body {
            overflow-y: hidden;
        }

        .detailModal .ivu-modal{
            top: 100px;
        }

        .pd-side16 .ivu-table .ivu-table-cell a{
            text-decoration: underline;
        }

        .viewTable .ivu-table-header thead tr:first-child > th,
        .viewTable .ivu-table-header thead tr:nth-child(2) > th:nth-child(12),
        .viewTable .ivu-table-header thead tr:nth-child(2) > th:nth-child(18),
        .viewTable .ivu-table-header thead tr:nth-child(2) > th:nth-child(23),
        .viewTable .ivu-table-header thead tr:nth-child(2) > th:nth-child(28),
        .viewTable .ivu-table-header thead tr:nth-child(2) > th:nth-child(33){
            border-right: 1px solid #d4d4d4 !important;
        }

        .ivu-radio-group-button .ivu-radio-wrapper-checked:first-child,
        .ivu-radio-group-button .ivu-radio-wrapper-checked,
        .ivu-radio-checked .ivu-radio-inner {
            border-color: #f90;
        }

        .ivu-radio-group-button .ivu-radio-wrapper-checked{
            color: #f90;
            box-shadow: -1px 0 0 0 #f90;
        }

        .ivu-radio-group-button .ivu-radio-wrapper-checked:hover,
        .ivu-radio-group-button .ivu-radio-wrapper-checked:active,
        .ivu-radio-group-button .ivu-radio-wrapper-checked:focus{
            border-color: #f90;
            color: #f90;
        }

        .ivu-radio-group-button .ivu-radio-wrapper-checked:before{
            background: #f90;
        }

        .ivu-radio-inner:after{
            background: rgba(255, 153, 0, 0.2);
        }

        .ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus,
        .ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus:first-child{
            box-shadow: 0 0 0 2px #ff990033;
        }

        .ivu-radio-group-button .ivu-radio-wrapper:after,
        .ivu-radio-group-button .ivu-radio-wrapper-checked.ivu-radio-focus:after{
            background: rgba(255, 153, 0, 0.2);
        }

        .detailModal .ivu-table-wrapper>.ivu-spin-fix{
            border: none;
        }

        .ctlgSelectDiv{
            width: calc(33% - 6em) !important;
        }

        .ivu-modal .ivu-modal-content .ivu-page-options .ivu-select-dropdown {
            top: -110px !important;
        }

        .includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner{
            border-color: #f90;
            background-color: #f90;
        }

        .includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
            border-color: #f90;
        }

        .includeChildCtlgs .ivu-checkbox-focus {
            box-shadow: 0 0 0 2px rgba(255, 153, 0, .2);
        }

        .subPrdctlg {
            margin-right: 0px;
        }

        .ivu-icon-ios-arrow-down:before {
            content: "\f3d0";
        }

        .ivu-icon-ios-browsers:before {
            content: "\f3f0";
        }

        .ivu-icon-ios-cube:before {
            content: "\f318";
        }

        .ivu-icon-ios-search:before {
            content: "\f21f";
        }

        .ivu-icon-md-menu:before {
            content: "\f20d";
        }

        .ivu-icon-ios-list-box-outline:before {
            content: "\f453";
        }

        .ivu-icon-ios-settings:before {
            content: "\f43d";
        }

        .ivu-icon-ios-arrow-forward:before {
            content: "\f10a";
        }

        .ivu-cascader-menu .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-icon-ios-arrow-back:before {
            content: "\f3d2";
        }

        .ivu-date-picker-prev-btn-arrow-double i:after {
            content: "\F3D2";
        }

        .ivu-date-picker-header .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-date-picker-header .ivu-date-picker-next-btn-arrow-double i:after {
            content: "\F3D3";
            margin-left: -8px;
        }

        .ivu-page .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-page .ivu-icon-ios-arrow-down:before {
            content: "\f104";
        }

        .ivu-page-item-active a, .ivu-page-item-active:hover a {
            color: #fff !important;
        }

        .headClass .ivu-table-overflowX{
            overflow-x: hidden;
        }

        .ivu-cascader .ivu-select-dropdown {
            max-width: calc(100vw - 532px)
        }

        .chart-text {
            text-align: center;
            font-size: 16px;
        }

        .chart-text .num {
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
        }

        body {
            overflow-y: hidden;
        }

        .includeChildCtlgs .ivu-checkbox-checked .ivu-checkbox-inner{
            border-color: #f90;
            background-color: #f90;
        }

        .ivu-page-item-jump-next:after,.ivu-page-item-jump-prev:after{
            content: "\002E\002E\002E";
        }

        .includeChildCtlgs .ivu-checkbox:hover .ivu-checkbox-inner {
            border-color: #f90;
        }

        .includeChildCtlgs .ivu-checkbox-focus {
            box-shadow: 0 0 0 2px rgba(255, 153, 0, .2);
        }

        .includeChildCtlgs .ivu-checkbox-wrapper{
            margin-right: 0px !important;
        }

        .node-select .ivu-select-selection {
            height: 32px;
            overflow: auto;
        }

        .autoInput:hover {
            background: #f3f3f3
        }

        .autoInputBss:hover {
            background: #f3f3f3
        }

        .dmp_report_warp .filter_warp .filter_col .includeChildCtlgs label{
            width: 7em;
        }

        .filter_component_tree .ctlgTree{
            width: calc((100% - 7em) - 24px) !important;
        }

        .dmp_report_warp .filter_warp .filter_col .filter_component_tree .ivu-checkbox-wrapper{
            width: auto;
        }

        .dmp_report_warp .filter_warp.filter_col_4 > div:nth-child(n+4) {
            margin-top: 8px;
        }

        .report_warp_height .filter_tree .data_area {
            height: calc(100% - 180px);
        }

        .ctlgTreeAsyn{
            width: calc((100% - 6em) - 32px)!important;
        }

        .ctlgSelectDiv .ivu-tree-title{
            margin-top: 9px;
        }

        .prdCtlg .ivu-icon-ios-loading:before{
            content: "\F29A";
        }

        .ivu-select-selection .ivu-select-arrow:nth-of-type(1){
            display: inline-block;
        }

        /*强制表格文本一行超出后打点表示 */
        .lineClamp2 .ivu-tooltip-rel{
            word-break: break-all;;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        .ivu-icon-ios-alert:before {
            content: "\f35b";
        }

        .ivu-icon-ios-checkmark-circle:before {
            content: "\f120";
        }

        .ivu-icon-ios-close-circle:before {
            content: "\f128";
        }

        .ivu-modal-confirm-head-icon-error {
            color: #f30;
        }

        .ivu-modal-confirm-head-icon-warning {
            color: #f90;
        }

        .ivu-icon-ios-information-circle:before {
            content: "\F149";
        }
        .ivu-icon-ios-close:before {
            content: "\f404";
        }

        .ivu-icon-ios-help-circle:before {
            content: "\f142";
        }

        .ivu-icon-ios-loading:before {
            content: "\F29C";
        }
        .ivu-icon-md-arrow-dropup:before {
            content: "\f10d";
        }

        .ivu-icon-md-arrow-dropdown:before {
            content: "\f104";
        }

        .ivu-icon-ios-close:before {
            content: "\f404";
        }

        .ivu-icon-ios-alert:before {
            content: "\f35b";
        }

        .ivu-icon-ios-checkmark-circle:before {
            content: "\f120";
        }

        .ivu-icon-ios-close-circle:before {
            content: "\f128";
        }

        .ivu-icon-ios-close:before {
            content: "\f404";
        }

        .ivu-icon-ios-help-circle:before {
            content: "\f142";
        }

        .ivu-icon-ios-loading:before {
            content: "\F29C";
        }

        .ivu-icon-md-arrow-dropup:before {
            content: "\f10d";
        }

        .ivu-icon-md-arrow-dropdown:before {
            content: "\f104";
        }
        .ivu-icon-ios-close:before {
            content: "\f178";
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm {
            display: inline-block;
            margin-right: 12px;
            vertical-align: middle;
            position: relative;
            top: -2px;
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm .ivu-icon {
            font-size: 28px;
            color: #f90;
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-title {
            font-size: 16px;
        }
        .ivu-modal-confirm .ivu-modal-confirm-body {
            font-size: 14px;
            padding-left: 48px;
        }
        .detail_export {
            margin-left: calc(100% - 57px);
        }
        .ivu-table-tip {
            overflow-x: auto;
            overflow-y: hidden;
        }
        .ivu-icon-ios-add:before {
            content: "\f489";
        }
        .ivu-icon-ios-remove:before {
            content: "\f2f4";
        }
        .ivu-table-cell-tree {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 1px solid #dcdee2;
            border-radius: 2px;
            background-color: #fff;
            line-height: 14px;
            cursor: pointer;
            vertical-align: top;
            transition: color .2s ease-in-out,border-color .2s ease-in-out;
        }
        .ivu-table-cell-tree-empty {
            cursor: default;
            color: transparent;
            background-color: transparent;
            border-color: transparent;
        }
        .ivu-table-cell-tree .ivu-icon {
            color: #dcdee2;
            font-size: 14px;
            padding-left: 3px;
        }
        .ivu-table-cell-tree-level {
            display: inline-block;
            height: 16px;
        }
        .data_area .view_table .ivu-tooltip {
            width: calc(100% - 74px);
        }
        .isNoShowHeadClass.main {
            min-width: 100% !important;
            width: 100%;
        }
        .isNoShowHeadClass .blank-name-tab .tab_blank span {
            padding: 0 8px;
        }
        .isNoShowHeadClass .blank-name {
            padding: 14px 16px;
        }
        .isNoShowHeadClass .blank-name .top {
            font-size: 14px;
            color: #495060;
            display: flex;
            align-items: center;
        }
        .isNoShowHeadClass .blank-name .top .iconfont {
            font-size: 20px;
            padding-right: 6px;
        }

        .filter_component_flex{
            display: flex;
        }

        .filter_component_flex span.horLine{
            display: flex;
            align-items: center;
            padding: 0 8px;
        }

        .filter_component_flex span.horLine:after{
            content: '';
            width: 4px;
            height: 1px;
            background-color: #dbe3eb;
        }

        .filter_component_select_height .ivu-select-selection{
            height: 32px;
            overflow: auto;
        }
    </style>
</head>

<body style="overflow: hidden;">
    <%@include file="../menu/headMenu.jsp"%>
  <div :class="[ !isShowHead? 'isNoShowHeadClass main bg-light-grey report_body_height' : 'main bg-light-grey report_body_height' ]" id="main" style="height: 100%; overflow: hidden;" v-cloak>
		<div class="layout-content layout-container bg-white report_warp_height"
             style="width: 100%; position: relative" :style="{minWidth: (!isShowHead ? '100%' : '1190px')}">
			<div class="blank-name blank-name-tab" style="display: flex; align-items: baseline;">
				<h1 :style="{display: (!isShowHead ? 'none' : 'inline-block')}">每月业务完成概况</h1>
				<span :style="{display: (!isShowHead ? 'none' : 'inline-block')}" @click="openHelpDoc"
                      class="iconfont icon-qbzuocesvg02 mr-rg10" style="color: #ff9900;cursor: pointer;font-size:16px;
                      line-height: inherit;margin-left: 4px;"></span>

                <div v-if="!isShowHead" class="top" @click="route"><span class="iconfont icon-packUp"></span>项目效能 / 每月业务完成概况</div>

			</div>


            <div class="dmp_report_warp filter_one">
                <div class="filter_warp filter_col_3">
                    <div class="filter_col">
                        <label>产品</label>
                        <div class="filter_component filter_component_select_height" style="display: flex">
                            <i-Select v-model="selectedPrdIds" multiple style="width: calc(100% - 70px)" @on-change="selectedPrdIdsChange" transfer  placeholder="请选择产品" clearable filterable>
                                <i-Option v-for="prd in prdList" :value="prd.id" :key="prd.id">{{prd.defName}}</i-Option>
                            </i-Select>
                            <div  style="display: inline-block;font-size: 12px; margin-left: 20px;">
                                <Checkbox :disabled="prdList.length === 0" class="subPrdctlg" v-model="prdIsSelectAll" @on-change="prdSelectAll">全选</Checkbox>
                            </div>
                        </div>
                    </div>                   

                    <div class="filter_col">
                        <label>目录</label>
                        <div class="filter_component filter_component_tree prdCtlg">
                            <prd-ctlg-multi-select ref="multitreeRef" :on-value-change="prdCtlgChange" :init-prd-id="prdId" :check-strictly="true"></prd-ctlg-multi-select>

                            <div class="includeChildCtlgs" style="display: inline-block;font-size: 12px; margin-left: 20px;">
                                <Checkbox class="subPrdctlg" v-model="includeChildCtlgs">包含子目录</Checkbox>
                            </div>
                        </div>
                    </div>

                    <div class="filter_col">
                        <label>业务类型</label>
                        <div class="filter_component">
                            <i-Select v-model="bizType"  placeholder="请选择业务类型">
                                <i-Option v-for="item in bizTypeList" :value="item.id" :key="item.id">{{item.defName}}</i-Option>
                            </i-Select>
                        </div>
                    </div>

                    <div class="filter_col">
                        <label>周期时间</label>
                        <div class="filter_component filter_component_flex">
                            <Date-Picker @on-change="monthChangeStar" :clearable="false" :options="dateDisabledStar" v-model="startDate" type="month" placeholder="时间起" style="width: 50%"></Date-Picker>
                            <span class="horLine"></span>
                            <Date-Picker :clearable="false" :options="dateDisabledEnd" v-model="endDate" type="month" placeholder="时间止" style="width: 50%"></Date-Picker>
                        </div>
                    </div>
                    

                    <div class="view_button" >
                        <i-button type="primary" @click="getList()">查询</i-button>
                        <i-button type="primary" @click="exportTableData()">导出</i-button>
                    </div>

                </div>

                <div class="data_area" ref="dataArea" >
                    <i-table border stripe :columns="columns" :data="tableDatas"
                             @on-sort-change="onSortChange"
                             :loading="tableLoading" :height="tableHeight"  class="view_table table-noborder lineClamp2">
                    </i-table>
                </div>
            </div>
		</div>
	</div>


   
</body>

<script>
Date.prototype.format = function(fmt) {
    var o = {
        "M+" : this.getMonth()+1,                 //月份
        "d+" : this.getDate(),                    //日
        "h+" : this.getHours(),                   //小时
        "m+" : this.getMinutes(),                 //分
        "s+" : this.getSeconds(),                 //秒
        "q+" : Math.floor((this.getMonth()+3)/3), //季度
        "S"  : this.getMilliseconds()             //毫秒
    };
    if(/(y+)/.test(fmt)) {
        fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length));
    }
    for(var k in o) {
        if(new RegExp("("+ k +")").test(fmt)){
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
        }
    }
    return fmt;
};
/* 控制登录失效后页面跳转登录页 */
verifyLogin();

var prjBizHttpRequest = linkus.location.prjbiz;
var vue = new Vue({
    el: '#main',
    data: function() {
        var sf = this;
    	return {
            queryUrl:"/bscPrjUserAssessCtr/listDevFuncInfo.action",
            exportUrl:"/bscPrjUserAssessCtr/exportDevFuncInfo.action",
            currentName:"devFunc",

            pageNum: 0,
            pageSize: 50,
            pageSizeOpts: [50,100,200,500],
            queryDataTotal: 0,
            tableLoading: false,
            tableHeight: 250,
                		
            prdList: [],
            prdId:null,
            selectedPrdIds: [],
            prdIsSelectAll:false,


            selectedPrdCtlgIds:[],
            includeChildCtlgs:true,

            bizType:'5ae2cb70900add501a141175',
            bizTypeList:[
                {
                    id: "5ae2cb70900add501a141175",
                    defName: "需求管理"
                },
                {
                    id: "5baa0aec900add501a14128c",
                    defName: "缺陷管理"
                },
            ],

            startDate:'',
            endDate:new Date(),
            dateDisabledStar:{
                disabledDate (date) {
                    return date && date.valueOf() > sf.endDate;
                }
            },
            dateDisabledEnd:{
                disabledDate (date) {
                    var currentDate = new Date();
                    var starYear = (new Date(sf.startDate)).getFullYear();
                    var nextYear = parseInt(starYear)+1;
                    var current = new Date(sf.startDate);
                    var month = (current.getMonth() + 1 ) < 10 ? '0'+(current.getMonth() + 1) : current.getMonth() + 1;
                    var next12Date =new Date(nextYear+'-'+month);
                    return date && (date.valueOf() < new Date(sf.startDate) || date.valueOf() > currentDate || date.valueOf() > next12Date )  ;
                }
            },
            monthList:[],
            columns:[],
            columnsBase: [
                {
                    key:'index',
                    title:'序号',
                    width:60,
                    render: (h, params) => {
                        debugger
                        if(params.row._index === sf.tableDatas.length-1){
                            return h('span','汇总')
                        }else{
                            return h('span',params.row._index+1)
                        }
                    }
                },
                {
                    title: '上级领导',
                    key: 'superiorLeader',
                    width:120,
                },
                {
                    title: '上级部门',
                    key: 'superiorDept',
                    width:120,
                },
                {
                    title: '部门经理',
                    key: 'deptManager',
                    width:120,
                },
                {
                    title: '部门',
                    key: 'dept',
                    width:120,
                    render: (h, params) => {
                        if(params.row.dept != '汇总'){
                            return h('span',params.row.dept)
                        }
                    }
                },
                {
                    title: '产品目录',
                    key: 'ctlg',
                    width:120,
                },
                {
                    title: '有效业务总数',
                    key: 'effectiveBizCount',
                    width:120,
                },
                {
                    title: '在途业务数',
                    key: 'openBizCount',
                    width:120,
                },
                {
                    title: '已交付业务数',
                    key: 'closeBizCount',
                    width:120,
                },
            ],
            tableDatas: [],
            sortAsc:null,
            sortKey:null,

            uploadDesc: "分析-质量管控分析-BSC项目人员效能评估套表（帮助）",


            isShowHead: true,

    	};    	       
    },
    watch: {

    },

    created: function () {
        var sf = this;
        var date = new Date();
        var year = date.getFullYear();
        sf.getMyPrdList();
        sf.startDate =  year+'-01';
    },

    mounted: function () {
        var sf = this;
        sf.columns = sf.columnsBase.concat([]);

        sf.tableHeight = document.body.clientHeight - 235;
    },

    methods: {

        //传值给父页面展开/收起左侧栏
        route: function() {
            var sf = this;
            window.parent.postMessage({"expandOrShrink": true}, '*');
        },

        //获取url参数
        getUrlPara: function(strName){
            var reg = new RegExp("(^|&)"+strName+"=([^&]*)(&|$)","i");
            var r = decodeURI(window.location.search).substr(1).match(reg);
            if(r!=null) {
                return r[2];
            }
            return null;
        },

        getDateMonth:function (date){
            var year = date.getFullYear();
            var month = date.getMonth() + 1
            month = month < 10 ? '0'+month:month;
            return year+'-'+month;
        },

        monthChangeStar:function (){
            var sf = this;
            var starYear = (new Date(sf.startDate)).getFullYear();
            if(sf.completeDate(new Date(sf.startDate),new Date(sf.endDate),12)){
                var nextYear = (new Date(sf.startDate)).getFullYear()+1;
                var startDate = new Date(sf.startDate);
                var month = (startDate.getMonth() + 1 ) < 10 ? '0'+(startDate.getMonth() + 1) : startDate.getMonth() + 1;
                var next12Date =new Date(nextYear+'-'+month);
                if(sf.compareDate(new Date(),next12Date) && sf.compareDate(new Date(sf.endDate),next12Date)){
                    sf.endDate = next12Date
                }
            }
        },
        compareDate:function(date1,date2){
            var oDate1 = new Date(date1);
            var oDate2 = new Date(date2);
            if(oDate1.getTime() > oDate2.getTime()){
                return true; //第一个大
            } else {
                return false; //第二个大
            }
        },

        completeDate:function(time1 , time2 , m) {
            var diffyear = time2.getFullYear() - time1.getFullYear() ;
            var diffmonth = diffyear * 12 + time2.getMonth() - time1.getMonth() ;
            if(diffmonth <= m ){
                return false ;
            }else{
                return true ;
            }
        },


        monthChange(){
            var sf = this;
            var start = sf.getDateMonth(sf.startDate);
            var end = sf.getDateMonth(sf.endDate);

            var result = [];
            var s = start.split('-');
            var e = end.split('-');
            var min = new Date();
            var max = new Date();
            min.setFullYear(s[0], s[1] * 1 - 1, 1); //开始日期
            max.setFullYear(e[0], e[1] * 1 - 1, 1); //结束日期
            var curr = min;
            while (curr <= max) {
                var month = curr.getMonth();
                result.push(sf.getDateMonth(curr));
                curr.setMonth(month + 1)
            }
            sf.monthList = result;
        },

        /* 查询产品清单 */
        getMyPrdList : function(){
            var sf = this;
            $.ajax({
                url: prjBizHttpRequest +'/prdCtrl/getMyPrdList.action',
                data: {},
                type: 'post',
                dataType: 'JSON',
                success: function(data) {
                    sf.prdList = data || [];
                },
                error: function(data) {
                    sf.$Message.error({
                        content: '查询产品清单失败，请联系管理员！',
                        duration: 3
                    });
                }
            });
        },

        prdSelectAll:function (value){
            var sf = this;
            var selectedPrdIds = [];
            if(value){
                sf.prdList.forEach(function(item){
                    selectedPrdIds.push(item.id);
                });
                sf.selectedPrdIds = selectedPrdIds;
            }else{
                sf.selectedPrdIds = [];
            }
        },

        selectedPrdIdsChange(){
            var sf = this;
            if(sf.selectedPrdIds.length != sf.prdList.length){
                sf.prdIsSelectAll = false;
            }else{
                sf.prdIsSelectAll = true;
            }
            if(sf.selectedPrdIds.length === 1){
                sf.prdId = sf.selectedPrdIds[0];
            }else{
                sf.prdId = null;
            }
        },

        /* 选择产品目录 */
        prdCtlgChange: function(prdCtlgIds, selectedData){
            var sf = this;
            sf.selectedPrdCtlgIds = [];
            if(!sf.$refs.multitreeRef.displayText && (!!prdCtlgIds && prdCtlgIds.length > 0)) {
                return;
            }
            sf.selectedPrdCtlgIds = prdCtlgIds;
        },



        //跳转到帮助页面
    	openHelpDoc:function(){
			var sf = this;
			window.open(prjBizHttpRequest + '/forward.action?t=biz/bizHelpDoc&pageName=' + sf.uploadDesc);
		},		

        //跳转到业务视图页面
        getLinkRender: function (h, bizId, bizCode) {
            var url = linkus.location.prjbiz + "/forward.action?t=biz/prdBizView&bizId=" + bizId;
            return h('Tooltip', {
                props: {placement: 'top', transfer: true}
            }, [
                h('a', {
                    attrs: {
                        href: url,
                        target: '_blank',
                        rel: 'noopener'
                    }
                }, bizCode),
                h('span', {slot: 'content', style: {whiteSpace: 'normal', wordBreak: 'break-all'}},
                    bizCode)
            ]);
        },

        onSortChange:function(param){
            var sf = this;
            sf.pageNum = 0;
            sf.pageSize = 50;
            if(param.order == "asc"){
                sf.sortAsc = true;
            }else if(param.order == "desc" ){
                sf.sortAsc = false;
            }else{
                sf.sortAsc = null;
            }
            sf.sortKey = param.key;
        },
        /* 改变页码 */
        onPageNumChange: function(pageNum) {
            var sf = this;
            sf.pageNum = pageNum - 1;
            sf.getList();
        },

        /* 改变每页条数 */
        onPageSizeChange: function(pageSize) {
            var sf = this;
            sf.pageNum = 0;
            sf.pageSize = pageSize;
            sf.getList();
        },

        handleParam:function(){
            var sf = this;
            var endYear = (new Date(sf.endDate)).getFullYear();
            var endMonth = (new Date(sf.endDate)).getMonth()+1;
            var endDay = sf.getMonthDays(endYear,endMonth);
            endDay = endDay < 10 ? '0'+endDay : endDay;
            var param = {
                prdIds:sf.selectedPrdIds,
                prdCtlgIds:sf.selectedPrdCtlgIds,
                bizTypeId:sf.bizType,
                include:sf.includeChildCtlgs,
                startDate:sf.getDateMonth(new Date(sf.startDate))+'-01',
                endDate:sf.getDateMonth(new Date(sf.endDate))+'-'+endDay,
            };

            return param;
        },

        formatDate:function(date) {
            var y = date.getFullYear();
            var MM = date.getMonth() + 1;
            MM = MM < 10 ? ('0' + MM) : MM;
            return y + '-' + MM;
        },

        getMonthDays:function(year,month){
            var thisDate = new Date(year,month,0);  //当天数为0 js自动处理为上一月的最后一天
            return thisDate.getDate();
        },

        getCount:function (list,month){
            var data = list.filter(function(item){
               return  item.month === month;
            })[0];
            return data;
        },


        /* 导出列表数据 */
        exportTableData: function () {
            var sf = this;

            if(sf.selectedPrdIds.length === 0){
                sf.$Message.error({
                    content: '请先选择产品',
                    duration: 3
                });
                return;
            };

            sf.$Message.warning({
                content  : '正在导出数据，请勿重复点击导出按钮，请稍等！',
                duration : 3
            });
            var param = sf.handleParam();
            $.ajax({
                url: linkus.location.report +'/ctcDevEffectCtrl/exportMothBizCompleteStatus.action',
                data: JSON.stringify(param),
                type: 'post',
                headers:{'Content-Type':'application/json'},
                xhrFields: { responseType: "blob" },
                success: function(res) {
                    sf.blobDataExport(res,'测试.xlsx')

                },
                error: function(data) {
                    sf.$Message.error({
                        content: '查询清单失败，请联系管理员！',
                        duration: 3
                    });
                }
            });
        },

        blobDataExport:function(downRes,fileName) {
            var sf =this;
            var blob = new Blob([downRes])
            var url = window.URL.createObjectURL(blob);
            var aLink = document.createElement("a");
            aLink.href = url;
            aLink.style.display = "none";
            aLink.setAttribute('download',fileName);
            document.body.appendChild(aLink);
            aLink.click();
            document.body.removeChild(aLink);
            window.URL.revokeObjectURL(url);
        },


        getList:function (){
            var sf = this;
            if(sf.selectedPrdIds.length === 0){
                sf.$Message.error({
                    content: '请先选择产品',
                    duration: 3
                });
                return;
            };

            var startDate = new Date(sf.startDate);
            var endDate = new Date(sf.endDate);
            var result = [];
            while (startDate <= endDate) {
                result.push(sf.formatDate(startDate));
                startDate.setMonth(startDate.getMonth() + 1);
            };
            var columnsMonth = [];
            result.forEach(function (item){
                var col = {
                    title: item,
                    children:[
                        {
                            title: '新增',
                            width: 100,
                            render: (h, params) => {
                                const month = (new Date(item)).getMonth() + 1;
                                console.log(params);
                                var data = sf.getCount(params.row.detailList,month);
                                return h('span',data.insert)
                            }
                        },
                        {
                            title: '完成',
                            width: 100,
                            render: (h, params) => {
                                const month = (new Date(item)).getMonth() + 1;
                                var data = sf.getCount(params.row.detailList,month);
                                return h('span',data.closed)
                            }
                        },
                        {
                            title: '未完成',
                            width: 100,
                            render: (h, params) => {
                                const month = (new Date(item)).getMonth() + 1;
                                var data = sf.getCount(params.row.detailList,month);
                                return h('span',data.notClosed)
                            }
                        }
                    ],
                };
                columnsMonth.push(col);
            });
            sf.columns = sf.columnsBase.concat(columnsMonth);
            sf.tableLoading = true;
            var param = sf.handleParam();
            $.ajax({
                url: linkus.location.report +'/ctcDevEffectCtrl/queryMothBizCompleteStatus.action',
                data: JSON.stringify(param),
                type: 'post',
                headers:{'Content-Type':'application/json'},
                success: function(res) {
                    sf.tableLoading = false;
                    if (res.success) {
                        sf.tableDatas = res.data;
                    } else {
                        sf.$Message.error({
                            content: res.message,
                            duration: 3
                        });
                    }

                },
                error: function(data) {
                    sf.$Message.error({
                        content: '查询清单失败，请联系管理员！',
                        duration: 3
                    });
                }
            });
        }
    },
    
});

document.addEventListener('click',function(e){
    var ctlgTreeDiv = $('.ctlgTree');
    var clickDiv = e.srcElement || e.target;
    var contains = ctlgTreeDiv.has($(clickDiv));
    if(contains.length > 0){
        vue.treeShowFlag = true;
    }else{
        vue.treeShowFlag = false;
    }
});
</script>

</html>