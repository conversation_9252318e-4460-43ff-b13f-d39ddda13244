<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <title>项目度量</title>

    <script src="../../00scripts/00lib/ie/browser.js"></script>

    <link href="../../01css/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/DmpStandardStyle.css" rel="stylesheet" type="text/css"/>
    <link href="../../01css/standardStyle.css" rel="stylesheet" type="text/css"/>

    <script src="../../00scripts/00lib/jquery/1.9.1/jquery.min.js"></script>
    <script src="../../00scripts/00lib/jquery/1.9.1/jquery-migrate-3.4.0.min.js"></script>

    <script src="../../00scripts/00lib/vue/vue.min.js"></script>

    <script src="../../00scripts/00lib/iview/4.4.0/iview.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../00scripts/00lib/iview/styles/iview.css"/>

    <link href="../../01css/icomoon/style.css" rel="stylesheet" type="text/css" />
    <link href="../../01css/font/iconfont.css" rel="stylesheet" type="text/css" />
    <script src="../../01css/font/iconfont.js"></script>

    <script type="text/javascript" src="../../00scripts/00lib/utils/ajax.js"></script>

    <!-- 本地路由 -->
    <script src="../../00scripts/location/location.js" type="text/javascript"></script>
    <script src="../../00scripts/location/location-portal.js" type="text/javascript"></script>
    <script src="../../00scripts/location/verifyLogin.js"></script>

    <!-- echarts -->
    <script src="../../00scripts/00lib/echarts5.3/echarts.min.js"></script>
    <script src="../../00scripts/clipboard/dist/clipboard.js"></script>

    <script type="text/javascript" src="../../common/defConst.js"></script>
    <script type="text/javascript" src="../../common/filterTable.js"></script>

    <style>
        [v-cloak] {
            display: none;
        }

        .ivu-modal-confirm-head-icon-error {
            color: #f30;
        }

        .ivu-modal-confirm-head-icon-warning {
            color: #f90;
        }

        .ivu-icon-ios-information-circle:before {
            content: "\F149";
        }

        .ivu-icon-ios-alert:before {
            content: "\f35b";
        }

        .ivu-icon-ios-checkmark-circle:before {
            content: "\f120";
        }

        .ivu-icon-ios-help-circle:before {
            content: "\f142";
        }

        .ivu-icon-ios-loading:before {
            content: "\F29C";
        }

        .ivu-icon-md-arrow-dropup:before {
            content: "\f10d";
        }

        .ivu-icon-md-arrow-dropdown:before {
            content: "\f104";
        }
        .ivu-icon-ios-close:before {
            content: "\f178";
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm {
            display: inline-block;
            margin-right: 12px;
            vertical-align: middle;
            position: relative;
            top: -2px;
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-icon.ivu-modal-confirm-head-icon-confirm .ivu-icon {
            font-size: 28px;
            color: #f90;
        }
        .ivu-modal-confirm .ivu-modal-confirm-head .ivu-modal-confirm-head-title {
            font-size: 16px;
        }
        .ivu-modal-confirm .ivu-modal-confirm-body {
            font-size: 14px;
            padding-left: 48px;
        }

        /*.ivu-icon-ios-close-circle:before{
            content: "\f177" !important;
        }*/

        .ivu-icon-ios-add:before {
            content: "\f489";
        }
        .ivu-icon-ios-remove:before {
            content: "\f2f4";
        }

        .ivu-icon-ios-arrow-down:before {
            content: "\f3d0";
        }

        .ivu-icon-ios-browsers:before {
            content: "\f3f0";
        }

        .ivu-icon-ios-cube:before {
            content: "\f318";
        }

        .ivu-icon-ios-search:before {
            content: "\f21f";
        }

        .ivu-icon-md-menu:before {
            content: "\f20d";
        }

        .ivu-icon-ios-list-box-outline:before {
            content: "\f453";
        }

        .ivu-icon-ios-settings:before {
            content: "\f43d";
        }

        .ivu-icon-ios-arrow-forward:before {
            content: "\f10a";
        }

        .ivu-cascader-menu .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-icon-ios-arrow-back:before {
            content: "\f3d2";
        }

        .ivu-date-picker-prev-btn-arrow-double i:after {
            content: "\F3D2";
        }

        .ivu-date-picker-header .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-date-picker-header .ivu-date-picker-next-btn-arrow-double i:after {
            content: "\F3D3";
            margin-left: -8px;
        }

        .ivu-page .ivu-icon-ios-arrow-forward:before {
            content: "\f3d3";
        }

        .ivu-page .ivu-icon-ios-arrow-down:before {
            content: "\f104";
        }

        .ivu-page-item-active a, .ivu-page-item-active:hover a {
            color: #fff !important;
        }

        /*强制表格文本两行超出后打点表示 */
        .lineClamp1 .ivu-tooltip-rel{
            word-break: break-all;
            white-space: normal;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        .lineClamp1 .ivu-tooltip {
            vertical-align: middle;
        }

        .prj_select_class .ivu-select-dropdown {
            width: 100%;
            z-index: 9999999;
        }

        .content_area .content_area_top {
            display: flex;
            padding: 10px 16px;
        }

        .content_area .content_area_top .content_area_top_left,.content_area_top_right {
            width: calc(50% - 8px);
            height: 280px;
            background: linear-gradient(180deg,#ffffff, #f5f8fb);
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgba(198,213,229,0.40);
        }

        .content_area .content_area_top .content_area_top_left {
            margin-right: 16px;
        }

        .content_area {
            overflow-y: auto;
            height: calc(100% - 82px);
        }

        .content_area .content_area_middle {
            /*height: 341px;*/
            height: calc(100% - 34px);
            background: linear-gradient(180deg,#ffffff, #f5f8fb);
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgb(198 213 229 / 40%);
            margin-left: 16px;
            margin-right: 16px;
            margin-top: 6px;
        }

        .content_area_top .content_text {
            display: flex;
            /*justify-content: center;*/
        }

        .content_area_top .content_text .content_node_wrap {
            display: flex;
            margin-left: 36px;
            margin-right: 36px;
        }

        .content_area_top .content_area_split {
            display: flex;
            justify-content: center;
            padding-top: 16px;
        }

        .content_area_top .content_area_split > div {
            width: 90%;
            height: 1px;
            background: #dbe3eb;
        }

        .content_area_top .content_text .content_survey {
            display: inline-block;
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            color: #555555;
            line-height: 22px;
            padding-top: 16px;
            /*padding-right: 48px;*/
            padding-left: 16px;
        }

        .content_area_top .content_text .content_node_text {
            display: block;
            font-size: 12px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: 400;
            text-align: center;
            color: #a5acc4;
            line-height: 22px;
        }

        .content_area_top .content_text .content_node_num {
            display: block;
            width: 29px;
            height: 27px;
            font-size: 24px;
            font-family: Georgia, Georgia-Bold;
            font-weight: 700;
            text-align: right;
            color: #262626;
            line-height: 22px;
        }

        .content_area_top .content_text .content_split {
            width: 1px;
            height: 36px;
            background: #e7eef5;
            margin-left: 45px;
            margin-right: 45px;
            margin-top: 22px;
        }

        .content_area_top .content_text .content_node {
            margin-top: 15px;
            cursor: pointer;
        }

        .content_area_middle .content_burn_down_chart_cond {
            display: flex;
            padding-top: 16px;
            padding-left: 12px;
            /*justify-content: space-between;*/
        }

        .content_burn_down_chart_cond .content_node_name_text {
            display: flex;
            z-index: 1000000;
        }

        .content_burn_down_chart_cond .content_node_name_text .content_node_title {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            color: #555555;
            line-height: 22px;
            padding-right: 16px;
        }

        .content_burn_down_chart_cond .content_node_name_text .content_node_div {
            font-size: 14px;
            font-family: PingFangSC-Regular;
            color: #a5acc4;
            line-height: 22px;
            cursor: pointer;
        }

        .content_burn_down_chart_cond .content_node_name_text .content_node_div.selected {
            color: #3883e5;
            font-weight: 500;
        }

        .content_burn_down_chart_cond .content_node_name_text .content_node_div:not(:last-child) {
            padding-right: 8px;
        }

        .content_burn_down_chart_cond .content_node_name_text .content_split_line {
            width: 1px;
            height: 10px;
            background: #dbe3eb;
            margin-right: 8px;
            margin-top: 6px;
        }

        .content_burn_down_chart_cond .content_node_button {
            display: flex;
            padding-right: 16px;
            /*z-index: 1000000;*/
            margin-left: auto;
        }

        .content_burn_down_chart_cond .content_node_button .change_button {
            width: 48px;
            height: 24px;
            border: 1px solid #dbe3eb;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            color: #555555;
        }

        .content_burn_down_chart_cond .content_node_button .change_button.selected {
            border: 1px solid #3883e5;
            color: #3883e5;
            font-weight: 500;
        }

        .content_burn_down_chart_cond .content_node_button .view_page {
            font-size: 14px;
            margin-top: -2px;
        }

        .content_burn_down_chart_cond .content_node_button .buttonDay {
            border-radius: 4px 0px 0px 4px;
        }

        .content_burn_down_chart_cond .content_node_button .buttonWeek {
            border-radius: 0px 4px 4px 0px;
        }

        .dmp_report_warp .filter_warp .filter_col > label {
            display: flex;
        }

        .dmp_report_warp .filter_warp .filter_col > label .date_img {
            color: #3883e5;
            font-weight: bolder;
            padding-left: 2px;
            font-size: 14px;
        }

        .content_area .content_area_trend {
            display: flex;
            padding: 10px 16px;
            margin-top: 6px;
        }

        .content_area .content_area_trend .content_area_req_trend {
            margin-right: 16px;
        }

        .content_area .content_area_trend .content_area_req_trend, .content_area_bug_trend {
            width: calc(50% - 8px);
            height: 300px;
            background: linear-gradient(180deg,#ffffff, #f5f8fb);
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgb(198 213 229 / 40%);
        }

        .content_area .content_area_severity {
            display: flex;
            padding: 0px 16px;
            margin-top: 6px;
            margin-bottom: 16px;
        }

        .content_area .content_area_severity .content_area_req_severity {
            margin-right: 16px;
        }

        .content_area .content_area_severity .content_area_req_severity, .content_area_bug_severity {
            width: calc(50% - 8px);
            height: 300px;
            background: linear-gradient(180deg,#ffffff, #f5f8fb);
            border: 1px solid #ffffff;
            border-radius: 4px;
            box-shadow: 0px 4px 18px 0px rgb(198 213 229 / 40%);
        }

        .content_area_trend_text {
            display: flex;
            margin-top: 16px;
            align-items: center;
        }

        .content_area_trend_text .content_area_trend_img {
            color: #3883e5;
            font-weight: bolder;
            font-size: 16px;
            padding-right: 8px;
            padding-left: 14px;
        }

        .content_area_trend_text .content_area_trend_title {
            font-size: 14px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            color: #555555;
            line-height: 22px;
        }

        .content_area_severity .view_table, .content_area_middle .view_table {
            margin-left: 16px;
            margin-right: 16px;
            margin-top: 12px;
            border: none !important;
        }

        .content_area_severity .view_table .ivu-table th {
            background-color: #fff;
            border-right: none !important;
            border-top: 1px solid #f1f5f9 !important;
        }

        .content_area_severity .view_table .ivu-table thead tr:first-child th :last-child {
            color: #666666;
            font-weight: 400;
        }

        .content_area_severity .view_table .ivu-table:before, .content_area_severity .view_table .ivu-table:after, .content_area_middle .view_table .ivu-table:before, .content_area_middle .view_table .ivu-table:after {
            display: none !important;
        }

        .content_area_severity .ivu-table-stripe .ivu-table-body tr:nth-child(2n) td, .content_area_severity  .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
            background-color: #fff;
        }

        .content_area_severity .ivu-table-stripe .ivu-table-body tr:nth-child(2n+1) td, .content_area_severity  .ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n+1) td {
            background-color: #f1f5f9;
        }
        .content_area_severity .ivu-table-sort i.on {
            color: #2d8cf0 !important;
        }
        .content_area_severity .ivu-table-sort i {
            color: #c8cddc !important;
        }

        .content_area::-webkit-scrollbar, .echarts_wrap::-webkit-scrollbar, .ivu-table-body::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }
        .content_area::-webkit-scrollbar-thumb, .echarts_wrap::-webkit-scrollbar-thumb, .ivu-table-body::-webkit-scrollbar-thumb {
            background: #bfbfbf;
            border-radius: 10px;
        }
        .content_area::-webkit-scrollbar-track, .echarts_wrap::-webkit-scrollbar-track, .ivu-table-body::-webkit-scrollbar-track {
            background: #efefef;
            border-radius: 2px;
        }
        .content_area::-webkit-scrollbar-thumb:hover, .echarts_wrap::-webkit-scrollbar-thumb:hover, .ivu-table-body::-webkit-scrollbar-thumb:hover {
            background: #979797;
        }
        .content_area::-webkit-scrollbar-corner, .echarts_wrap::-webkit-scrollbar-corner, .ivu-table-body::-webkit-scrollbar-corner {
            background: #179a16;
        }

        .content_area_trend_text .icon-export {
            color: #3883e5;
            padding-right: 14px;
            margin-left: auto;
        }
        .detailModalClass .icon-export {
            line-height: 2;
            font-size: 20px;
            margin-left: auto;
            color: #3883e5;
        }
        .dmp_report_warp .filter_warp .prj_filter_col.filter_col {
            width: 50%;
        }
        .ivu-select-multiple .ivu-select-selection {
            overflow-y: auto;
            height: 32px;
        }

        .summaryModalClass .summaryReportWrap .summaryReportConentWrap {
            font-size: 14px;
            margin-bottom: 12px;
        }

        .summaryModalClass .copy-summary-button {
            text-align: center;
        }
        .summaryModalClass {
            z-index: 10000000 !important;
        }
        .reqTimelinessRateErrorMessage {
            font-size: 16px;
            padding: 0 20px;
            color: #E65D4E;
            font-weight: bold;
        }
        .content_burn_down_chart_cond .content_node_month_cond {
            display: inline-flex;
            font-size: 14px;
            line-height: 23px;
            padding-left: 16px;
        }
        .content_burn_down_chart_cond .content_node_month_cond label {
            display: inline-block;
            width: 7em;
        }
        .content_burn_down_chart_cond .content_node_month_cond .ivu-date-picker {
            margin-top: -4px;
        }
        .content_burn_down_chart_cond .content_node_export_cond {
            cursor: pointer;
            font-size: 14px;
            padding-left: 14px;
            color: #3883e5;
        }

        .content_node_type_cond {
            display: flex;
            padding-left: 8px;
        }

        .content_node_type_cond .change_button {
            width: 34px;
            height: 24px;
            border: 1px solid #dbe3eb;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            font-size: 12px;
            color: #555555;
        }

        .content_node_type_cond .change_button.selected {
            border: 1px solid #3883e5;
            color: #3883e5;
            font-weight: 500;
        }

        .content_node_type_cond .buttonMonth {
            border-radius: 4px 0 0 4px;
        }

        .content_node_type_cond .buttonTotal {
            border-radius: 0 4px 4px 0;
        }
        .view_page .ivu-page-options-sizer {
            margin-right: 0px;
        }
        .view_table .ivu-table th[colspan='1'] {
            border-bottom: 1px solid #dddee1 !important;
        }
        .filter_component .ivu-cascader .ivu-select-dropdown {
            z-index: 9999999;
        }
        .content_area_middle .assessTableTitle {
            margin-top: 12px;
            padding-left: 16px;
            font-size: 14px;
        }
        .assessCondClass {
            display: flex;
            align-items: center;
        }
        .assessCondClass .assessIndexPageClass {
            margin-top: 12px;
            margin-left: auto;
            padding-right: 16px;
        }
        .content_area.digitalContent_area {
            /*height: calc(100% - 136px);*/
        }
        .dataClass .ivu-table-cell > span {
            padding-left: 18px;
        }
        .dataClass .index-table-info-column .ivu-table-cell > span {
            padding-left: 0;
        }

        .ivu-table .demo-table-red-row td {
            background-color: #ff0019 !important;
        }
    </style>
</head>

<body style="overflow: hidden;">
<%@include file="../menu/headMenu.jsp"%>
<div :class="[ !isShowHead? 'isNoShowHeadClass main bg-light-grey report_body_height' : 'main bg-light-grey report_body_height' ]"
     id="main" style="height: 100%; overflow: hidden;" v-cloak>
    <div class="layout-content layout-container bg-white report_warp_height"
         style="width: 100%; position: relative" :style="{minWidth: (!isShowHead ? '100%' : '1190px')}">
        <div class="dmp_report_warp filter_two">
            <div class="filter_warp filter_col_4">

                <div class="filter_col" >
                    <label>省份</label>
                    <div class="filter_component">
                        <Cascader v-model="provFltList" :data="provTreeData" @on-change="provChanged" placeholder="请选择省份" clearable filterable></Cascader>
                    </div>
                </div>

                <div class="filter_col" >
                    <label>管控类型</label>
                    <div class="filter_component">
                        <i-Select class="prj_select_class" @on-change="prjMgtTypeChanged" v-model="selectPrjMgtTypes" placeholder="请选择管理类型" clearable filterable multiple>
                            <i-Option v-for="item in prjMgtTypeList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col" >
                    <label>状态</label>
                    <div class="filter_component">
                        <i-Select class="prj_select_class" @on-change="prjStatusChanged" v-model="selectPrjStatuses" placeholder="请选择状态" clearable filterable multiple>
                            <i-Option v-for="item in prjStatusList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col" >
                    <label>归属年份</label>
                    <div class="filter_component">
                        <Date-Picker type="year" v-model="selectYear" format="yyyy" @on-change="yearChanged" placeholder="请选择归属年份" style="width: 100%;"></Date-Picker>
                    </div>
                </div>

                <div class="filter_col prj_filter_col" >
                    <label>项目集</label>
                    <div class="filter_component">
                        <i-Select class="prj_select_class" ref="prj" @on-change="onPrjChanged" v-model="selectPrjId" placeholder="请选择项目集" clearable filterable
                                  :remote-method="queryPrjInfo" :loading="queryPrjLoading">
                            <i-Option v-for="prj in searchPrjList" :value="prj.cid" :key="prj.cid">{{ prj.codeName }}/{{ prj.name }}</i-Option>
                        </i-Select>
                    </div>
                </div>

                <div class="filter_col" v-show="tabName == 'prjNoticeBoard'">
                    <label>周期<span class="date_img">T</span></label>
                    <div class="filter_component">
                        <Date-Picker transfer @on-change="onCycleTimeChanged" clearable type="daterange" style="width: 100%" v-model="selectDatePeriod"
                                     placeholder="请选择周期时间" placement="bottom-end"></Date-Picker>
                    </div>
                </div>

                <div class="filter_col" v-show="tabName == 'digitalProductionEvaluation'">
                    <label>度量月份</label>
                    <div class="filter_component">
                        <Date-Picker @on-change="changeMeasureMonth" transfer clearable filterable type="month" style="width: 100%" v-model="selectMeasureMonth"
                                     placeholder="请选择度量月份" placement="bottom-end"></Date-Picker>
                    </div>
                </div>

                <%--<div class="filter_col" v-show="tabName == 'digitalProductionEvaluation'">
                    <label>度量体系</label>
                    <div class="filter_component">
                        <i-Select v-model="selectMsmtSysId" placeholder="请选择度量体系" clearable filterable @on-change="changeMsmtSys">
                            <i-Option v-for="item in msmtSysList" :value="item.id" :key="item.id">{{ item.defName }}</i-Option>
                        </i-Select>
                    </div>
                </div>--%>

                <div class="view_button">
                    <i-button type="primary" @click="queryData">查询</i-button>
                    <i-button v-show="tabName == 'prjNoticeBoard'" type="primary" @click="openSummaryModal">查看总结</i-button>
                    <i-button v-show="tabName == 'digitalProductionEvaluation'" type="primary" @click="exportPrjDigitalInfo('single')">导出单个</i-button>
                    <i-button v-show="tabName == 'digitalProductionEvaluation'" type="primary" @click="exportPrjDigitalInfo('all')">导出全部</i-button>
                </div>
            </div>

            <div :class="[ tabName == 'digitalProductionEvaluation'? 'content_area digitalContent_area' : 'content_area' ]">
                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_top">
                    <div class="content_area_top_left">
                        <div class="content_text">
                            <div class="content_survey">需求概况</div>
                            <div class="content_node_wrap">
                                <div class="content_node" @click="openReqSurveyDetail">
                                    <span class="content_node_text">开发中</span>
                                    <span class="content_node_num">{{ devReqNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openReqSurveyDetail">
                                    <span class="content_node_text">开发超期</span>
                                    <span :class="!!devReqDelayNum && devReqDelayNum > 0 ? 'content_node_num red' : 'content_node_num'">{{ devReqDelayNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openReqSurveyDetail">
                                    <span class="content_node_text">测试中</span>
                                    <span class="content_node_num">{{ testReqNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openReqSurveyDetail">
                                    <span class="content_node_text">测试超期</span>
                                    <span :class="!!testReqDelayNum && testReqDelayNum > 0 ? 'content_node_num red' : 'content_node_num'">{{ testReqDelayNum }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="content_area_split"><div></div></div>
                        <div style="display: flex;align-items: center;">
                            <div id="echarts_req" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div id="echarts_task" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div v-show="!reqTimelinessRateErrorMessage" id="echarts_req_timeliness" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div v-show="!!reqTimelinessRateErrorMessage" class="reqTimelinessRateErrorMessage">{{ reqTimelinessRateErrorMessage }}</div>
                            <div id="echarts_task_timeliness" style="height: 240px;width: 25%;display: inline-block;"></div>
                        </div>
                    </div>
                    <div class="content_area_top_right">
                        <div class="content_text">
                            <div class="content_survey">缺陷概况</div>
                            <div class="content_node_wrap">
                                <div class="content_node" @click="openBugSurveyDetail">
                                    <span class="content_node_text">修复中</span>
                                    <span class="content_node_num">{{ repairNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openBugSurveyDetail">
                                    <span class="content_node_text">验证中</span>
                                    <span class="content_node_num">{{ verifyNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openBugSurveyDetail">
                                    <span class="content_node_text">超期未修复</span>
                                    <span :class="!!repairDelayNum && repairDelayNum > 0 ? 'content_node_num red' : 'content_node_num'">{{ repairDelayNum }}</span>
                                </div>
                                <div class="content_split"></div>
                                <div class="content_node" @click="openBugSurveyDetail">
                                    <span class="content_node_text">超期未验证</span>
                                    <span :class="!!verifyDelayNum && verifyDelayNum > 0 ? 'content_node_num red' : 'content_node_num'">{{ verifyDelayNum }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="content_area_split"><div></div></div>
                        <div style="display: flex;align-items: center;">
                            <div id="echarts_escape" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div id="echarts_solve" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div id="echarts_bug_repair" style="height: 240px;width: 25%;display: inline-block;"></div>
                            <div id="echarts_bug_verify" style="height: 240px;width: 25%;display: inline-block;"></div>
                        </div>
                    </div>
                </div>

                <div v-show="tabName == 'prjUserPerformance'" class="content_area_middle">
                    <div class="content_burn_down_chart_cond">
                          <div class="content_node_name_text">
                              <div class="content_node_title">项目人员绩效</div>
                              <div class="content_split_line"></div>
                              <div :class="[ selectedNode == 'devUser' ? 'content_node_div content_design_node selected' : 'content_node_div content_design_node' ]"
                                   @click="changeNode('devUser')">开发人员</div>
                              <div class="content_split_line"></div>
                              <div :class="[ selectedNode == 'testUser' ? 'content_node_div content_dev_node selected' : 'content_node_div content_dev_node' ]"
                                   @click="changeNode('testUser')">测试人员</div>
                              <div class="content_split_line"></div>
                              <div :class="[ selectedNode == 'reqUser' ? 'content_node_div content_test_node selected' : 'content_node_div content_test_node' ]"
                                   @click="changeNode('reqUser')">需求人员</div>
                          </div>
                        <div class="content_node_month_cond">
                            <label>度量开始月份：</label>
                            <Date-Picker @on-change="changeMeasureStartMonth" clearable type="month" style="width: calc(100% - 8em);" v-model="selectMeasureStartMonth"
                                         placeholder="请选择度量开始月份" placement="bottom-end"></Date-Picker>
                        </div>

                        <div class="content_node_month_cond">
                            <label>度量结束月份：</label>
                            <Date-Picker @on-change="changeMeasureEndMonth" clearable type="month" style="width: calc(100% - 8em);" v-model="selectMeasureEndMonth"
                                         placeholder="请选择度量结束月份" placement="bottom-end"></Date-Picker>
                        </div>

                        <div class="content_node_type_cond">
                            <span :class="[ selectedTypeBtn == 'month' ? 'change_button buttonMonth selected' : 'change_button buttonMonth' ]" @click="changeTypeBtn('month')">按月</span>
                            <span :class="[ selectedTypeBtn == 'total' ? 'change_button buttonTotal selected' : 'change_button buttonTotal' ]" @click="changeTypeBtn('total')">累计</span>
                        </div>

                        <span class="content_node_export_cond" @click="exportPrjPerformanceList">导出</span>
                        <div class="content_node_button">
                            <Page class="view_page" size="small" :total="prjPerformanceTableDataTotal" :page-size="prjPerformanceTablePageSize" placement="bottom"
                                  :current="prjPerformanceTablePageNum + 1" :page-size-opts="prjPerformanceTablePageSizeOpts" show-sizer show-total
                                  @on-change="onPrjPerformanceTablePageNumChange" @on-page-size-change="onPrjPerformanceTablePageSizeChange"></Page>
                            <%--<span :class="[ selectedBtn == 'detail' ? 'change_button buttonDay selected' : 'change_button buttonDay' ]" @click="changeBtn('detail')">明细</span>--%>
                            <%--<span :class="[ selectedBtn == 'sum' ? 'change_button buttonWeek selected' : 'change_button buttonWeek' ]" @click="changeBtn('sum')">汇总</span>--%>
                        </div>
                    </div>
                    <i-table
                            :height="prjPerformanceTableHeight"
                            class="view_table lineClamp1 prjPerformanceTableClass"
                            :columns="prjPerformanceColumns"
                            :data="prjPerformanceDatas"
                            :loading="prjPerformanceLoading"
                            stripe border @on-sort-change="prjPerformanceTableSortChange">
                    </i-table>
                </div>

                <div v-show="tabName == 'digitalProductionEvaluation'" class="content_area_middle">
                    <div class="assessTotalTableClass">
                        <div class="assessTableTitle">
                            项目集评估总结果
                        </div>
                        <i-table
                                :height="assessTotalTableHeight"
                                class="view_table lineClamp1 assessTotalTableClass"
                                :columns="assessTotalColumns"
                                :data="assessTotalDatas"
                                :loading="assessTotalLoading"
                                stripe border @on-row-click="rowClickAssessTotalTable">
                        </i-table>
                    </div>
                    <div class="assessDetailTableClass">
                        <div class="assessTableTitle">
                            评估结果明细
                        </div>
                        <i-table
                                :height="assessDetailTableHeight"
                                class="view_table lineClamp1 assessDetailTableClass"
                                :columns="assessDetailColumns"
                                :data="assessDetailDatas"
                                :loading="assessDetailLoading"
                                stripe border @on-row-click="rowClickAssessDetailTable">
                        </i-table>
                    </div>
                    <div class="assessIndexTableClass">
                        <div class="assessCondClass">
                            <div class="assessTableTitle">
                                指标明细： {{ assessIndexName }}
                            </div>
                            <div class="assessIndexPageClass">
                                <Page class="view_page" size="small" :total="assessIndexTableDataTotal" :page-size="assessIndexTablePageSize" placement="bottom"
                                      :current="assessIndexTablePageNum + 1" :page-size-opts="assessIndexTablePageSizeOpts" show-sizer show-total
                                      @on-change="onAssessIndexTablePageNumChange" @on-page-size-change="onAssessIndexTablePageSizeChange"></Page>
                            </div>
                        </div>
                        <filter-table
                                :height="assessIndexTableHeight"
                                class="view_table lineClamp1 assessIndexTableClass"
                                :columns="assessIndexColumns"
                                :data="assessIndexDatas"
                                :loading="assessIndexLoading"
                                stripe border :disable-dblclick="true" :row-class-name="assessIndexRowClassName">
                        </filter-table>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_trend">
                    <div class="content_area_req_trend">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">需求存量/新增/完成趋势</div>
                        </div>
                        <div id="echarts_req_trend" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                    <div class="content_area_bug_trend">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">缺陷存量/新增/完成趋势</div>
                        </div>
                        <div id="echarts_bug_trend" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_trend">
                    <div class="content_area_req_trend">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">任务及时率</div>
                        </div>
                        <div id="echarts_task_promptness" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                    <div class="content_area_bug_trend">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">缺陷修复/验证及时率</div>
                        </div>
                        <div id="echarts_bug_timeliness_rate" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">非结构化任务报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportTaskCompletionReport"></span>
                        </div>
                        <i-table
                                :height="taskCompletionTableHeight"
                                class="view_table lineClamp1 taskCompletionTableClass"
                                :columns="taskCompletionColumns"
                                :data="taskCompletionDatas"
                                :loading="taskCompletionLoading"
                                @on-sort-change="taskCompletionSortChange"
                                stripe border>
                        </i-table>
                    </div>

                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text" style="padding-left: 14px;">
                            <div class="content_area_trend_title">缺陷按严重程度分类</div>
                        </div>
                        <div id="echarts_bug_severity" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">待开发任务报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportDevOrTaskList('dev')"></span>
                        </div>
                        <i-table
                                :height="devTaskTableHeight"
                                class="view_table lineClamp1 devTaskTableClass"
                                :columns="devTaskColumns"
                                :data="devTaskDatas"
                                :loading="devTaskLoading"
                                @on-sort-change="devTaskSortChange"
                                stripe border>
                        </i-table>
                    </div>

                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">开发待修复缺陷报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportDevUnRepairedDefectReport"></span>
                        </div>
                        <i-table
                                :height="devUnRepairedDefectTableHeight"
                                class="view_table lineClamp1 devUnRepairedDefectTableClass"
                                :columns="devUnRepairedDefectColumns"
                                :data="devUnRepairedDefectDatas"
                                :loading="devUnRepairedDefectLoading"
                                @on-sort-change="devUnRepairedDefectSortChange"
                                stripe border>
                        </i-table>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">

                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">待测试任务报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportDevOrTaskList('test')"></span>
                        </div>
                        <i-table
                                :height="testTaskTableHeight"
                                class="view_table lineClamp1 testTaskTableClass"
                                :columns="testTaskColumns"
                                :data="testTaskDatas"
                                :loading="testTaskLoading"
                                @on-sort-change="testTaskSortChange"
                                stripe border>
                        </i-table>
                    </div>

                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">测试待验证缺陷报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportTestUnVerifyDefectReport"></span>
                        </div>
                        <i-table
                                :height="testUnVerifyDefectTableHeight"
                                class="view_table lineClamp1 testUnVerifyDefectTableClass"
                                :columns="testUnVerifyDefectColumns"
                                :data="testUnVerifyDefectDatas"
                                :loading="testUnVerifyDefectLoading"
                                @on-sort-change="testUnVerifyDefectSortChange"
                                stripe border>
                        </i-table>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text" style="padding-left: 14px;">
                            <div class="content_area_trend_title">存量开发任务按成员排名</div>
                        </div>
                        <div class="echarts_wrap" style="height: calc(100% - 35px);width: 100%;display: inline-block;overflow-y: auto;overflow-x: hidden;">
                            <div id="echarts_store_dev_Task" style="width: 100%;display: inline-block;"></div>
                        </div>
                    </div>
                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text" style="padding-left: 14px;">
                            <div class="content_area_trend_title">待修复缺陷按成员排名</div>
                        </div>
                        <div class="echarts_wrap" style="height: calc(100% - 35px);width: 100%;display: inline-block;overflow-y: auto;overflow-x: hidden;">
                            <div id="echarts_repair_defect" style="width: 100%;display: inline-block;"></div>
                        </div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text" style="padding-left: 14px;">
                            <div class="content_area_trend_title">存量测试任务按成员排名</div>
                        </div>
                        <div class="echarts_wrap" style="height: calc(100% - 35px);width: 100%;display: inline-block;overflow-y: auto;overflow-x: hidden;">
                            <div id="echarts_store_test_Task" style="width: 100%;display: inline-block;"></div>
                        </div>
                    </div>
                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text" style="padding-left: 14px;">
                            <div class="content_area_trend_title">待验证缺陷按成员排名</div>
                        </div>
                        <div class="echarts_wrap" style="height: calc(100% - 35px);width: 100%;display: inline-block;overflow-y: auto;overflow-x: hidden;">
                            <div id="echarts_verify_defect" style="width: 100%;display: inline-block;"></div>
                        </div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">评审覆盖率</div>
                        </div>
                        <div id="echarts_req_severity" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">缺陷回退率</div>
                        </div>
                        <div id="echarts_bug_fallback_rate" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">任务单一次通过率</div>
                        </div>
                        <div id="echarts_firstTime_Pass" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">开发任务缺陷密度</div>
                        </div>
                        <div id="echarts_devTask_defectDensity" style="height: calc(100% - 40px);width: 100%;display: inline-block;"></div>
                    </div>
                </div>

                <div v-show="tabName == 'prjNoticeBoard'" class="content_area_severity">
                    <div class="content_area_req_severity" style="border: none;box-shadow: none;background: none;border-radius: 0;">
                    </div>
                    <div class="content_area_bug_severity">
                        <div class="content_area_trend_text">
                            <div class="content_area_trend_img">T</div>
                            <div class="content_area_trend_title">测试用例报表</div>
                            <span class="iconfont icon-export" title="导出" @click="exportTestCaseTable"></span>
                        </div>
                        <i-table
                                :height="testCaseTableHeight"
                                class="view_table lineClamp1 testCaseTableClass"
                                :columns="testCaseColumns"
                                :data="testCaseDatas"
                                :loading="testCaseLoading"
                                @on-sort-change="testCaseSortChange"
                                stripe border>
                        </i-table>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <Modal :mask-closable="false" @on-cancel="closeDetailModal" v-model="detailModal" :title="detailTitle" width="1105" class-name="vertical-center-modal view_modal_inner detailModalClass">
    	<i-table border stripe :columns="detailColumns" :data="detailTableDatas" :loading="detailTableLoading" :height="detailTableHeight"
    			class="view_table table-noborder lineClamp1">
       </i-table>
    	<div style="margin-top:10px;display: flex;">
            <Page :total="detailTableTotal" transfer :page-size="detailPageSize" placement="top" :current="detailPageIndex + 1" :page-size-opts="detailPageSizeOpts"
                  show-sizer show-total @on-change="deatilPageChange" @on-page-size-change="detailPageSizeChange">
            </Page>
            <span v-show="!!isExport" class="iconfont icon-export" title="导出" @click="exportDefectDetailTable"></span>
        </div>
    	<div slot="footer">
        </div>
    </Modal>

    <Modal :mask-closable="false" @on-cancel="closeSummaryModal" v-model="isSummaryModal" :title="summaryModalTitle" width="1000" class-name="vertical-center-modal view_modal_inner summaryModalClass">

        <div class="summaryReportWrap" id="summaryReportWrap">
            <div class="summaryReportConentWrap">
                <div class="summaryReportTitle">1、项目需求单和开发测试任务:</div>
                <div class="summaryReportConent">需求交付流程中，项目累计需求{{ prjProgQualReportVo.reqNum }}个，开发中{{ prjProgQualReportVo.devReqNum }}个，测试中{{ prjProgQualReportVo.testReqNum }}个，开发超期{{ prjProgQualReportVo.devDelayReqNum }}个，测试超期{{ prjProgQualReportVo.testDelayReqNum }}个；项目累计开发任务{{ prjProgQualReportVo.devTaskNum }}个，截至当前已完成{{ prjProgQualReportVo.devCompleteTaskNum }}个任务，
                    任务完成率{{ prjProgQualReportVo.devCompleteRate }}%，超期未完成{{ prjProgQualReportVo.devTaskDelayNum }}个任务，任务及时率{{ prjProgQualReportVo.devTimelinessRate }}%；项目累计测试任务{{ prjProgQualReportVo.testTaskNum }}个，截至当前已完成{{ prjProgQualReportVo.testCompleteTaskNum }}个任务，
                    任务完成率{{ prjProgQualReportVo.testCompleteRate }}%，超期未完成{{ prjProgQualReportVo.testTaskDelayNum }}个任务，任务及时率{{ prjProgQualReportVo.testTimelinessRate }}%；</div>
            </div>

            <div class="summaryReportConentWrap">
                <div class="summaryReportTitle">2、项目缺陷:</div>
                <div class="summaryReportConent">当前累计缺陷{{ prjProgQualReportVo.bugNum }}个，缺陷解决率{{ prjProgQualReportVo.bugResRate }}%，
                    {{ prjProgQualReportVo.repairNum }}个待修复缺陷，{{ prjProgQualReportVo.verifyNum }}个待验证缺陷，超期未修复缺陷{{ prjProgQualReportVo.repairDelayNum }}个，
                    超期未验证缺陷{{ prjProgQualReportVo.verifyDelayNum }}个，缺陷修复及时率{{ prjProgQualReportVo.repairTimelinessRate }}%，缺陷验证及时率{{ prjProgQualReportVo.verifyTimelinessRate }}%，
                    未关闭严重缺陷{{ prjProgQualReportVo.unCloseHighLevelBugNum }}个，项目缺陷逃逸率{{ prjProgQualReportVo.rwBugRate }}%;</div>
            </div>

            <div class="summaryReportConentWrap">
                <div class="summaryReportTitle">3、项目事务型任务管理:</div>
                <div class="summaryReportConent">任务管理流程中，当前累计事务型任务{{ prjProgQualReportVo.taskMgrNum }}个，累计完成{{ prjProgQualReportVo.taskMgrCompleteNum }}个，
                    处理完成率{{ prjProgQualReportVo.taskMgrCompleteRate }}%，超期未完成{{ prjProgQualReportVo.taskMgrDelayNum }}个，累计任务处理及时率{{ prjProgQualReportVo.taskMgrTimelinessRate }}%;</div>
            </div>

            <div class="summaryReportConentWrap">
                <div class="summaryReportTitle">4、项目代码质量:</div>
                <div class="summaryReportConent">项目累计变更代码行{{ prjProgQualReportVo.codeChangeLineNum }}行，项目开发活跃度{{ prjProgQualReportVo.devActivityRate }}%，
                    开发人员当前存在{{ prjProgQualReportVo.highLevelViolateNum }}个高等级违规，千行代码违规密度{{ prjProgQualReportVo.densityThousandsCodeViolations }}KLOC，结合业务测试缺陷，
                    加权千行代码缺陷密度为{{ prjProgQualReportVo.weightDensityThousandLines }}。</div>
            </div>
        </div>

        <div class="copy-summary-button">
            <i-button type="primary" @click="copySummaryReport" class="copySummaryBtn" data-clipboard-action="copy" data-clipboard-target="#summaryReportWrap">复制总结</i-button>
        </div>

        <div slot="footer">
        </div>
    </Modal>

</div>
</body>
<script src="../../biz/bscPrjEffectEvaluation/prjMeasure.js"></script>
</html>
