package com.linkus.om.health.service.impl;

import com.itextpdf.text.pdf.PdfStructTreeController.returnType;
import com.linkus.base.constants.SysDefTypeConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.condition.impl.mini.DC_R;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.om.health.service.IHealthDelayService;
import com.linkus.om.health.vo.HealthDelayVo;
import com.linkus.om.health.vo.HealthNotUploadVo;
import com.linkus.om.pmc.dao.IOmsHealthCheckDao;
import com.linkus.om.pmc.model.TeItemInfo;
import com.linkus.om.pmc.model.TeOmsHealthCheck;
import com.linkus.om.pmc.model.TeltemInfo2;
import com.linkus.sys.dao.ISysCalDao;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysCal;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.xml.transform.Result;
import java.util.*;

/**
 * 健康度延迟统计
 *
 * <AUTHOR>
 */
@Service("HealthDelayServiceImpl")
public class HealthDelayServiceImpl implements IHealthDelayService {

    @Autowired
    private IOmsHealthCheckDao healthCheckDao;

    @Autowired
    private ISysDefService sysDefService;
    @Autowired
    private SysDefDao sysDefDao;
    @Autowired
    private ISysCalDao sysCalDao;

    @Override
    public Map<String, Object> queryHealthDelay(ObjectId userId,String provCodeName, String selectDate,String sbuId) {
        Map<String, Object> result = new HashMap<>(4);
        //获取bu信息
        SysDef sbuDef = sysDefService.getSysDefByCodeName(sbuId, SysDefTypeCodeName.AI_BU);
        if (sbuDef == null){
            result.put("用户BU不存在", null);
            return result;
        }
        Map<String, List<HealthDelayVo>> list = new HashMap<>(32);
        List<TeIdNameCn> provList = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        if (StringUtil.isNotNull(provCodeName)){
            conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.omsHealthCheck_codeName),provCodeName));
        }
        if (StringUtil.isNotNull(selectDate)){
            conds.add(new DC_R(DFN.omsHealthCheck_ymd,selectDate));
        }
        conds.add(new DC_E(DFN.omsHealthCheck_sbu.dot(DFN.common_cid),sbuDef.getId()));
        conds.add(new DC_E(DFN.omsHealthCheck_itemInfo.dot(DFN.omsHealthCheck_isDelay),true));
        List<TeOmsHealthCheck> teOmsHealthCheckList = healthCheckDao.findByConds(conds, null);
        //按照时间分组排列
        Map<String, List<TeOmsHealthCheck>> healthMap = new HashMap<>(16);
        //按照省份数量分组
        Map<ObjectId, Integer> healthCount = new HashMap<>(16);
        for (TeOmsHealthCheck teOmsHealthCheck : teOmsHealthCheckList) {
            List<TeOmsHealthCheck> tempList = healthMap.get(teOmsHealthCheck.getYmd());
            if (null == tempList){
                tempList = new ArrayList<>();
                tempList.add(teOmsHealthCheck);
                healthMap.put(teOmsHealthCheck.getYmd(),tempList);
            } else {
                tempList.add(teOmsHealthCheck);
            }
            Integer count = healthCount.get(teOmsHealthCheck.getProv().getCid());
            if (null == count || count == 0){
            count = 1;
            } else {
                count ++;
            }
            healthCount.put(teOmsHealthCheck.getProv().getCid(),count);
        }

        List<SysDef> sysDefs = sysDefService.queryProvince(userId, provCodeName);
        for (SysDef sysDef : sysDefs) {
            TeIdNameCn prov = new TeIdNameCn();
            prov.setCid(sysDef.getId());
            prov.setName(sysDef.getDefName());
            prov.setCodeName(sysDef.getCodeName());
            if (sysDef.getSrcDef() != null && sbuDef.getId().equals(sysDef.getSrcDef().getId())){
                provList.add(prov);
            }
        }
        for (String date : healthMap.keySet()) {
            String day = date.substring(date.length() - 2);
            List<HealthDelayVo> voList = new ArrayList<>();
            List<TeOmsHealthCheck> teOmsHealthChecks = healthMap.get(date);
            for (TeOmsHealthCheck teOmsHealthCheck : teOmsHealthChecks) {
                int no = 0;
                HealthDelayVo vo = new HealthDelayVo();
                List<TeItemInfo> itemInfo = teOmsHealthCheck.getItemInfo();
                for (TeItemInfo teItemInfo : itemInfo) {
                    if (null != teItemInfo.getDelay() && teItemInfo.getDelay()){
                        no++;
                    }
                }
                TeIdNameCn prov = teOmsHealthCheck.getProv();
                String province = prov.getName();
                String provCode = prov.getCodeName();
                ObjectId provId = prov.getCid();
                vo.setDelayNum(no);
                vo.setProvince(province);
                vo.setProvCodeName(provCode);
                vo.setProvId(provId);
                voList.add(vo);
            }
            list.put(day,voList);
        }
        result.put("list",list);
        result.put("header",provList);
        result.put("total",healthCount);
        return result;
    }
    @Override
    public Map<String, Object> queryNotUploadDay(ObjectId userId, String provCodeName, String selectDate,String sbuId){
        // 代码格式调整  变量命名还是保持原开发者
        // 代码主要修改逻辑由省份名字匹配各项数据改为省份CID匹配
        Map<String,Object> results = new HashMap<>();
        //获取bu信息
        SysDef sbuDef = sysDefService.getSysDefByCodeName(sbuId, SysDefTypeCodeName.AI_BU);
        if (sbuDef == null){
            results.put("用户BU不存在", null);
            return results;
        }
        if (null == userId) {
            results.put("用户未登录", null);
            return results;
        }
        if (null == provCodeName || null == selectDate) {
            results.put("请输入正确参数", null);
            return results;
        } else {
             List<HealthNotUploadVo> result = new ArrayList<>();
                List<IDbCondition>  conds = new ArrayList<>();
            conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeId), StringUtil.toObjectId(SysDefTypeConstants.PROV_DEF_ID)));
            conds.add(new DC_E(DFN.sysDef__codeName,provCodeName));
            conds.add(new DC_E(DFN.sysDef__srcDef.dot(DFN.sysDef__srcDefId),sbuDef.getId()));
            conds.add(new DC_E(DFN.common_isValid,true));

            List<TeSysDef> teSysDefs = sysDefDao.findByConds(conds,null);
                Map<ObjectId,Integer> proMap = new HashMap<ObjectId, Integer>();
                Map<ObjectId,ObjectId> proASIdMap = new HashMap<>();
                List<ObjectId> tempList = new ArrayList<>();
                List<TeSysDef> sysDefs = new ArrayList<>();
                for (TeSysDef sysDef : teSysDefs) {
                    if (null == sysDef.getDefDesc()){
                        proMap.put(sysDef.getId(),null);
                        if (null != sysDef.getCndtItems() && sysDef.getCndtItems().size() > 0){
                            tempList.add(sysDef.getCndtItems().get(0).getCid());
                        }

                        sysDefs.add(sysDef);
                    }
                }
                conds.clear();
                conds.add(new DC_I<ObjectId>(DFN.common__id,tempList));
                List<TeSysDef> asIds=sysDefDao.findByConds(conds,null);
                List<ObjectId> parentDefIds=new ArrayList<>();
                for(TeSysDef t:asIds){
                    parentDefIds.add(t.getParentDefId());
                }
                conds.clear();
                conds.add(new DC_I<ObjectId>(DFN.common__id,parentDefIds));
                List<TeSysDef> aslist = sysDefDao.findByConds(conds,null);
                // 省份ID匹配对应工程部
                for (TeSysDef sysDef:sysDefs){
                    for (TeSysDef asId:asIds){
                        if (sysDef.getCndtItems() != null&&sysDef.getCndtItems().size() > 0 && sysDef.getCndtItems().get(0).getCid().equals(asId.getId())){
                            proASIdMap.put(sysDef.getId(),asId.getParentDefId());
                        }
                    }
                }
                // 省份ID匹配对应大区
                Map<ObjectId,String> proAsMap = new HashMap<>();
                    for (ObjectId s : proASIdMap.keySet()){
                        for (TeSysDef t : aslist){
                            if (proASIdMap.get(s).equals(t.getId())){
                                proAsMap.put(s,t.getDefName());
                            }
                        }
                    }
                List<TeSysCal> workdays = sysCalDao.getWorkDays(selectDate);
                ArrayList<String> days = new ArrayList<>();
                for (TeSysCal t : workdays) {
                    days.add(t.getDate());
                }
                conds.clear();
                conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.omsHealthCheck_codeName),provCodeName));
                conds.add(new DC_I<String>(DFN.omsHealthCheck_ymd,days));
                conds.add(new DC_E(DFN.omsHealthCheck_sbu.dot(DFN.common_cid),sbuDef.getId()));
                List<TeOmsHealthCheck> teOmsHealthCheckList = healthCheckDao.findByConds(conds, null);
                for (ObjectId prc : proMap.keySet()){
                Set<String> tempset = new HashSet<>();
                    // 省份ID匹配该月上传报告天数
                for (TeOmsHealthCheck t : teOmsHealthCheckList) {
                    if (prc.equals(t.getProv().getCid()) && provCodeName.equals(t.getProv().getCodeName())){
                        tempset.add(t.getYmd());
                    }
                }
               //和BA 李红霞确认健康度统计上传每日最多一次有效  tempset.size() <= days.size()
                proMap.put(prc,days.size()-tempset.size());
            }
                    for (TeSysDef t : sysDefs){
                        HealthNotUploadVo vo = new HealthNotUploadVo();
                            vo.setBigRegionName(proAsMap.get(t.getId()));
                            vo.setDays(proMap.get(t.getId()));
                            vo.setProvId(t.getId());
                        if (null != t.getCndtItems() && t.getCndtItems().size() > 0)
                            vo.setRegionName(t.getCndtItems().get(0).getName());
                            vo.setProvince(t.getDefName());
                            result.add(vo);
                    }
                results.put("result",result);
                return results;
        }
        
       
    }

    @Override
    public List<String> getNotUploadDayDetail(String provCodeName, String selectDate, String province) {
        if (null==provCodeName|null==selectDate|null==province) {
            return null;
        }else {
            List<IDbCondition> conds=new ArrayList<>();
            conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.omsHealthCheck_codeName),provCodeName));
            conds.add(new DC_R(DFN.omsHealthCheck_ymd,selectDate));
            //查询该月未上传天数明细也由省份匹配改为ID匹配
            conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.common_cid),StringUtil.toObjectId(province)));
            List<TeOmsHealthCheck> omsHealthCheckList =healthCheckDao.findByConds(conds,null);
            Set<String> dataset=new HashSet<>();
            for(TeOmsHealthCheck t:omsHealthCheckList){
                dataset.add(t.getYmd());
            }
            List<TeSysCal> workdays=sysCalDao.getWorkDays(selectDate);
            ArrayList<String> days = new ArrayList<>();
            for(TeSysCal t:workdays) {
                days.add(t.getDate());
            }
            
            days.removeAll(dataset);
            return days;
        }
        
    }

    @Override
    public List<TeltemInfo2> getLineChartDetail(String provName, String provCodeName, String ymd) {
        List<TeltemInfo2> info2List = new ArrayList<>();
        List<IDbCondition> conds = new ArrayList<>();
        if (StringUtil.isNotNull(provCodeName)){
            conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.omsHealthCheck_codeName),provCodeName));
        }
        if (StringUtil.isNotNull(provName)){
            conds.add(new DC_E(DFN.omsHealthCheck_prov.dot(DFN.omsHealthCheck_name),provName));
        }
        if (StringUtil.isNotNull(ymd)){
            conds.add(new DC_E(DFN.omsHealthCheck_ymd,ymd));
        }
        conds.add(new DC_E(DFN.omsHealthCheck_itemInfo.dot(DFN.omsHealthCheck_isDelay),true));
        List<TeOmsHealthCheck> omsHealthCheckList = healthCheckDao.findByConds(conds, null);
        if (null != omsHealthCheckList && !omsHealthCheckList.isEmpty()){
            TeOmsHealthCheck teOmsHealthCheck = omsHealthCheckList.get(0);
            List<TeItemInfo> itemInfo = teOmsHealthCheck.getItemInfo();
            for (TeItemInfo teItemInfo : itemInfo) {
                if (null != teItemInfo.getDelay() && teItemInfo.getDelay()){
                    TeltemInfo2 info2 = new TeltemInfo2();
                    BeanUtils.copyProperties(teItemInfo,info2);
                    info2.setSbuName(teOmsHealthCheck.getSbu() == null ? null : teOmsHealthCheck.getSbu().getName());
                    info2.setBigRegionName(teOmsHealthCheck.getBigRegion() == null ? null : teOmsHealthCheck.getBigRegion().getName());
                    info2.setRegionName(teOmsHealthCheck.getRegion() == null ? null : teOmsHealthCheck.getRegion().getName());
                    info2.setProvName(teOmsHealthCheck.getProv().getName());
                    info2.setYmd(teOmsHealthCheck.getYmd());
                    if (null != teItemInfo.getDelay() && teItemInfo.getDelay()){
                        info2.setIsDelay("延迟");
                    } else {
                        info2.setIsDelay("不延迟");
                    }
                    if (null != teItemInfo.getNormal() && teItemInfo.getNormal()) {
                        info2.setIsNormal("正常");
                    } else {
                        info2.setIsNormal("异常");
                    }
                    info2List.add(info2);
                }
            }
        }
        return info2List;
    }
}
