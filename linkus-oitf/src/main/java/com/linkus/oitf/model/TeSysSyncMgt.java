package com.linkus.oitf.model;

import java.util.Date;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.linkus.base.db.mongo.QueryField;
import com.linkus.base.db.mongo.model.TeIdNameCn;

@Document(collection = "sysSyncMgt")
public class TeSysSyncMgt {
	@Id
	@QueryField
	private ObjectId id;
	@QueryField
	private TeIdNameCn syncType;
	@QueryField
	private String verDesc;
	@QueryField
	private String year;
	@QueryField
	private String month;
	@QueryField
	private TeUser syncUser; 
	@QueryField
	private Boolean isValid;
	@QueryField
	private Date syncTime;
	public ObjectId getId() {
		return id;
	}
	public void setId(ObjectId id) {
		this.id = id;
	}
	public TeIdNameCn getSyncType() {
		return syncType;
	}
	public void setSyncType(TeIdNameCn syncType) {
		this.syncType = syncType;
	}
	public String getVerDesc() {
		return verDesc;
	}
	public void setVerDesc(String verDesc) {
		this.verDesc = verDesc;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getMonth() {
		return month;
	}
	public void setMonth(String month) {
		this.month = month;
	}
	public TeUser getSyncUser() {
		return syncUser;
	}
	public void setSyncUser(TeUser syncUser) {
		this.syncUser = syncUser;
	}
	public Boolean getIsValid() {
		return isValid;
	}
	public void setIsValid(Boolean isValid) {
		this.isValid = isValid;
	}
	public Date getSyncTime() {
		return syncTime;
	}
	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}
	
}
