package com.linkus.oitf.model;

import java.util.Date;

import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import com.linkus.base.db.mongo.QueryField;

@Document(collection = "itfArHkActual")
public class TeItfArHkActual {
	@Id
	@QueryField
	private ObjectId id;       	
	@QueryField
	private String pid; 
	@QueryField
	private String phaseId; 
	@QueryField
	private Date returnMoneyDate; 
	@QueryField
	private Double phaseMoney; 
	@QueryField
	private Date syncTime;
	public ObjectId getId() {
		return id;
	}
	public void setId(ObjectId id) {
		this.id = id;
	}
	public String getPid() {
		return pid;
	}
	public void setPid(String pid) {
		this.pid = pid;
	}
	public String getPhaseId() {
		return phaseId;
	}
	public void setPhaseId(String phaseId) {
		this.phaseId = phaseId;
	}
	public Date getReturnMoneyDate() {
		return returnMoneyDate;
	}
	public void setReturnMoneyDate(Date returnMoneyDate) {
		this.returnMoneyDate = returnMoneyDate;
	}
	public Double getPhaseMoney() {
		return phaseMoney;
	}
	public void setPhaseMoney(Double phaseMoney) {
		this.phaseMoney = phaseMoney;
	}
	public Date getSyncTime() {
		return syncTime;
	}
	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}	
	
	
}
