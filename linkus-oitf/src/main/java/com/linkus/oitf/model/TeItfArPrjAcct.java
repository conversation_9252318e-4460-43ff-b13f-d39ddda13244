package com.linkus.oitf.model;

import com.linkus.oitf.model.inner.AcctReceived;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;
import java.util.List;

@Document(collection = "itfArPrjAcct")
public class TeItfArPrjAcct {
	
	@Id
	private ObjectId id;
	
	private String phaseId;
	
	private String projectId;
	
	private String pactId;
	
	private String dutyPerson;
	
	private String phaseName;
	
	private String phaseType;
	
	private String phase;
	
	private Double phaseMoney;
	
	private Double phaseMoneyRmb;
	
	private String relativeDay;
	
	private String oughtDate;
	
	private String glDate;
	
	private String triggerStatus;
	
	private String checkedFlag;
	
	private String gatherNotice;
	
	private String billAwoke;
	
	private String completeStandard;
	
	private String version;
	
	private Double oughtAmount;
	
	private String planOughtDate;
	
	private String invoiceAmount;
	
	private String returnDate;
	
	private String returnMoney;
	
	private String returnAmount;
	
	private List<AcctReceived> acctReceived;
	
	private String moneyType;
	
	private Date syncTime;

	//2022/10/27 DMP_REQ_6955 新增
	private String phaseStatus;

	public String getPhaseStatus() {
		return phaseStatus;
	}

	public void setPhaseStatus(String phaseStatus) {
		this.phaseStatus = phaseStatus;
	}

	public ObjectId getId() {
		return id;
	}

	public void setId(ObjectId id) {
		this.id = id;
	}

	public String getPhaseId() {
		return phaseId;
	}

	public void setPhaseId(String phaseId) {
		this.phaseId = phaseId;
	}

	public String getProjectId() {
		return projectId;
	}

	public void setProjectId(String projectId) {
		this.projectId = projectId;
	}

	public String getPactId() {
		return pactId;
	}

	public void setPactId(String pactId) {
		this.pactId = pactId;
	}

	public String getDutyPerson() {
		return dutyPerson;
	}

	public void setDutyPerson(String dutyPerson) {
		this.dutyPerson = dutyPerson;
	}

	public String getPhaseName() {
		return phaseName;
	}

	public void setPhaseName(String phaseName) {
		this.phaseName = phaseName;
	}

	public String getPhaseType() {
		return phaseType;
	}

	public void setPhaseType(String phaseType) {
		this.phaseType = phaseType;
	}

	public String getPhase() {
		return phase;
	}

	public void setPhase(String phase) {
		this.phase = phase;
	}

	public Double getPhaseMoney() {
		return phaseMoney;
	}

	public void setPhaseMoney(Double phaseMoney) {
		this.phaseMoney = phaseMoney;
	}

	public String getRelativeDay() {
		return relativeDay;
	}

	public void setRelativeDay(String relativeDay) {
		this.relativeDay = relativeDay;
	}

	public String getOughtDate() {
		return oughtDate;
	}

	public void setOughtDate(String oughtDate) {
		this.oughtDate = oughtDate;
	}

	public String getGlDate() {
		return glDate;
	}

	public void setGlDate(String glDate) {
		this.glDate = glDate;
	}

	public String getTriggerStatus() {
		return triggerStatus;
	}

	public void setTriggerStatus(String triggerStatus) {
		this.triggerStatus = triggerStatus;
	}

	public String getCheckedFlag() {
		return checkedFlag;
	}

	public void setCheckedFlag(String checkedFlag) {
		this.checkedFlag = checkedFlag;
	}

	public String getGatherNotice() {
		return gatherNotice;
	}

	public void setGatherNotice(String gatherNotice) {
		this.gatherNotice = gatherNotice;
	}

	public String getBillAwoke() {
		return billAwoke;
	}

	public void setBillAwoke(String billAwoke) {
		this.billAwoke = billAwoke;
	}

	public String getCompleteStandard() {
		return completeStandard;
	}

	public void setCompleteStandard(String completeStandard) {
		this.completeStandard = completeStandard;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public Double getOughtAmount() {
		return oughtAmount;
	}

	public void setOughtAmount(Double oughtAmount) {
		this.oughtAmount = oughtAmount;
	}

	public String getPlanOughtDate() {
		return planOughtDate;
	}

	public void setPlanOughtDate(String planOughtDate) {
		this.planOughtDate = planOughtDate;
	}

	public String getInvoiceAmount() {
		return invoiceAmount;
	}

	public void setInvoiceAmount(String invoiceAmount) {
		this.invoiceAmount = invoiceAmount;
	}

	public String getReturnDate() {
		return returnDate;
	}

	public void setReturnDate(String returnDate) {
		this.returnDate = returnDate;
	}

	public String getReturnMoney() {
		return returnMoney;
	}

	public void setReturnMoney(String returnMoney) {
		this.returnMoney = returnMoney;
	}

	public String getReturnAmount() {
		return returnAmount;
	}

	public void setReturnAmount(String returnAmount) {
		this.returnAmount = returnAmount;
	}

	public List<AcctReceived> getAcctReceived() {
		return acctReceived;
	}

	public void setAcctReceived(List<AcctReceived> acctReceived) {
		this.acctReceived = acctReceived;
	}

	public Date getSyncTime() {
		return syncTime;
	}

	public void setSyncTime(Date syncTime) {
		this.syncTime = syncTime;
	}

	public String getMoneyType() {
		return moneyType;
	}

	public void setMoneyType(String moneyType) {
		this.moneyType = moneyType;
	}

	public Double getPhaseMoneyRmb() {
		return phaseMoneyRmb;
	}

	public void setPhaseMoneyRmb(Double phaseMoneyRmb) {
		this.phaseMoneyRmb = phaseMoneyRmb;
	}
	
}
