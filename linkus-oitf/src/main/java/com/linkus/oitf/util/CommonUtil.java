package com.linkus.oitf.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.X509TrustManager;

import com.linkus.base.util.StringUtil;
import com.linkus.oitf.constant.ThirdPartItf;


public class CommonUtil {
	
	/**
	 * 获取公司OA服务器时间
	 */
	public static String getServerDate() {
		String url = ThirdPartItf.SERVER_DATE_ADDREE;
		String jsonStr = null;
		try {
			jsonStr = httpGetMethod(url);
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
		
		jsonStr = jsonStr.trim();
		return jsonStr;
	}
	
	/**
	 * 请求调用
	 * @param path 请求链接
	 * @return 响应字符串
	 * @throws UnsupportedEncodingException
	 * @throws IOException
	 */
	public static String httpGetMethod(String path) throws UnsupportedEncodingException, IOException{
		trustAllHosts();
		URL url = new URL(path);
		HttpURLConnection con = (HttpURLConnection) url.openConnection();
		con.setRequestMethod("GET");
		con.setDoInput(true);
		//设置请求超时
		con.setConnectTimeout(30000);  	//连接主机的超时时间设置为30秒
		con.setReadTimeout(60000);  	//读取数据的超时时间设置为60秒
		
		StringBuffer buffer = new StringBuffer();//读取返回内容
		try {
			BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));
			String temp;
			while ( (temp = br.readLine()) != null ) {
				buffer.append(temp);
				buffer.append("\n");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return buffer.toString();
	}
	
	public static String trustAllHosts() {
		try {
			HttpsURLConnection.setDefaultHostnameVerifier(new HostnameVerifier() {
				@Override
				public boolean verify(String hostname,
						SSLSession session) {
					return true;
				}
			});
			SSLContext context = SSLContext.getInstance("TLS");

			context.init(null, new X509TrustManager[] { new X509TrustManager() {
				public void checkClientTrusted(X509Certificate[] chain,
						String authType) throws CertificateException {
				}

				public void checkServerTrusted(X509Certificate[] chain,
						String authType) throws CertificateException {
				}

				public X509Certificate[] getAcceptedIssuers() {
					return new X509Certificate[0];
				}
			} }, new SecureRandom());
			HttpsURLConnection.setDefaultSSLSocketFactory(context.getSocketFactory());
		} catch (Exception e) {
			e.printStackTrace();
		}
		return "";
	}
	
	/**
	 * 进行MD5加密
	 * @param info 要加密的信息
	 * @return String 加密后的字符串
	 */
	public static String encryptToMD5(String info) {
		byte[] digesta = null;
		try {
			MessageDigest alga = MessageDigest.getInstance("MD5"); 	//得到一个md5的信息摘要实例
			alga.update(info.getBytes()); 							//添加要进行计算摘要的信息
			digesta = alga.digest(); 								//得到该摘要
		} catch (NoSuchAlgorithmException e) {
			e.printStackTrace();
		}
		String rs = byte2hex(digesta); 								// 将摘要转为字符串
		return rs;
	}

	/**
	 * 将二进制转化为16进制字符串
	 * @param b 二进制字节数组
	 * @return String
	 */
	public static String byte2hex(byte[] b) {
		String hs = "";
		String stmp = "";
		for (int n = 0; n < b.length; n++) {
			stmp = (java.lang.Integer.toHexString(b[n] & 0XFF));
			if (stmp.length() == 1) {
				hs = hs + "0" + stmp;
			} else {
				hs = hs + stmp;
			}
		}
		return hs.toUpperCase();
	}
	
	/**
	 * 获取公司南京OA服务器时间
	 */
	public static String getServerDateOfNJ(String url) {
		if (!StringUtil.isNull(url)) {
			String jsonStr = null;
			try {
				jsonStr = httpGetMethod(url);
			} catch (UnsupportedEncodingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			
			jsonStr = jsonStr.trim();
			return jsonStr;
		}
		else return "";
	}
	
	
	/** 
     * 根据开始时间和结束时间返回时间段内的时间集合 
     *  
     * @param beginDate 
     * @param endDate 
     * @return List 
	 * @throws ParseException 
     */  
    public static List<String> getDatesBetweenTwoDate(String beginDate, String endDate) throws ParseException {
    	if ( StringUtil.isNull(beginDate) || StringUtil.isNull(endDate) ) {
    		return null;
    	}
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    	Date dBegin = sdf.parse(beginDate);  
        Date dEnd = sdf.parse(endDate);  
        List<String> dateList = new ArrayList<String>();  
        dateList.add(sdf.format(dBegin));				// 把开始时间加入集合  
        Calendar cal = Calendar.getInstance();  
        cal.setTime(dBegin);   					// 使用给定的 Date 设置此 Calendar 的时间  
        boolean bContinue = true;  
        while (bContinue) {  
            cal.add(Calendar.DAY_OF_MONTH, 1);  // 根据日历的规则，为给定的日历字段添加或减去指定的时间量  
            if (dEnd.after(cal.getTime())) {	// 测试此日期是否在指定日期之后  
            	String dateStr = sdf.format(cal.getTime());
                dateList.add(dateStr);  
            } else {  
                break;  
            }  
        }
        dateList.add(sdf.format(dEnd));// 把结束时间加入集合  
        return dateList;  
    }
    
    // 封装返回值map
 	public static Map<String, Object> doReturn(Map<String, Object> dataResult, String defName, String result, String infoDefName, String info) {
 		
 		if (null == dataResult) {
 			dataResult = new HashMap<String, Object>();
 		}
 		dataResult.put(defName, 	result);
 		dataResult.put(infoDefName, info);
 		
 		return dataResult;
 	}
    
    // 封装返回值map
 	public static Map<String, Object> doReturn(Map<String, Object> dataResult, String defName, String result, String info) {
 		
 		if (null == dataResult) {
 			dataResult = new HashMap<String, Object>();
 		}
 		dataResult.put(defName, result);
 		dataResult.put("info", 	 info);
 		
 		return dataResult;
 	}
 	
 	// 封装返回值map
 	public static Map<String, Object> doReturn(Map<String, Object> dataResult, String result, String info) {
 		
 		if (null == dataResult) {
 			dataResult = new HashMap<String, Object>();
 		}
 		dataResult.put("result", result);
 		dataResult.put("info", 	 info);
 		
 		return dataResult;
 	}
 	
 	// 封装返回值map
 	public static Map<String, Object> doReturn(Map<String, Object> dataResult, String result, Object dataList) {
 		
 		if (null == dataResult) {
 			dataResult = new HashMap<String, Object>();
 		}
 		dataResult.put("result", 	 result);
 		dataResult.put("dataList", dataList);
 		
 		return dataResult;
 	}
 	
}
