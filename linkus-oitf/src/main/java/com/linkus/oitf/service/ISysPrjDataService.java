package com.linkus.oitf.service;

import java.util.List;
import java.util.Map;

import com.linkus.oitf.model.TePrjInfo;
import org.bson.types.ObjectId;

public interface ISysPrjDataService {
  /**
   * 查询所有项目信息
   * @return
   */
  public List<TePrjInfo> queryAll();
  
  /**
   * 同步DMP项目到LinkUs系统中
   */
  public Map<String, Object> syncDmpPrjInfo2LinkUs();
  
  /**
   * 从CIT更新waitOrderProvision和orderSurplusCost两个字段
   */
  public void updateWaitOrderProvisionAndOrderSurplusCostFromCit();

    /**
     * 按项目同步净销售额信息
     * @param prjIds
     */
  void syncNetSalesFromAbp(List<ObjectId> prjIds);
}
