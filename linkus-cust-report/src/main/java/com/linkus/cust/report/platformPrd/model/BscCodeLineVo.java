package com.linkus.cust.report.platformPrd.model;

/**
 * @Author: feizj
 * @Description: 变更代码行
 * @Date: 2022/9/8 20:01
 */
public class BscCodeLineVo {

    /**
     * 开发人员变更代码行数
     */
    private Integer changeNum;
    /**
     * 开发团队变更代码行总行数
     */
    private Integer teamChangeNum;
    /**
     * 开发团队变更代码行总人数
     */
    private Integer teamChangeUserNum;
    /**
     * 开发团队人均变更代码行数
     */
    private Double avgChangeNum;
    /**
     * 开发人员变更代码行数比
     */
    private Double changeNumRate;
    /**
     * 开发人员变更代码行数得分
     */
    private Double changeNumScore;

    public Integer getChangeNum() {
        return changeNum;
    }

    public void setChangeNum(Integer changeNum) {
        this.changeNum = changeNum;
    }

    public Integer getTeamChangeNum() {
        return teamChangeNum;
    }

    public void setTeamChangeNum(Integer teamChangeNum) {
        this.teamChangeNum = teamChangeNum;
    }

    public Integer getTeamChangeUserNum() {
        return teamChangeUserNum;
    }

    public void setTeamChangeUserNum(Integer teamChangeUserNum) {
        this.teamChangeUserNum = teamChangeUserNum;
    }

    public Double getAvgChangeNum() {
        return avgChangeNum;
    }

    public void setAvgChangeNum(Double avgChangeNum) {
        this.avgChangeNum = avgChangeNum;
    }

    public Double getChangeNumRate() {
        return changeNumRate;
    }

    public void setChangeNumRate(Double changeNumRate) {
        this.changeNumRate = changeNumRate;
    }

    public Double getChangeNumScore() {
        return changeNumScore;
    }

    public void setChangeNumScore(Double changeNumScore) {
        this.changeNumScore = changeNumScore;
    }
}
