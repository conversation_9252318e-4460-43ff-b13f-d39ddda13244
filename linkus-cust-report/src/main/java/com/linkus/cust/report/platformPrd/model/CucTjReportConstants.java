package com.linkus.cust.report.platformPrd.model;

import org.bson.types.ObjectId;

/**
 * <AUTHOR>
 * @date 2020/02/18
 */
public class CucTjReportConstants {
  // 上报工作量
  public static final ObjectId DEF_ID_FIELD_CUC_TJ_REPORT_EFFORT = new ObjectId("5df6eb0dc56a424c334704c5");
  // 结算工作量
  public static final ObjectId DEF_ID_FIELD_CUC_TJ_CONSTARCT_EFFORT = new ObjectId("5df6ff6bc56a424c334704da");
  
  // 项目清单
  public static final String DEF_ID_USER_CUST_PARA_CUC_TJ_PRJ_LIST = "5df6eff7c56a424c334704c8";
  // 上报工作量
  public static final String DEF_ID_USER_CUST_PARA_CUC_TJ_REPORT_EFFORT = "5df6f5b4c56a424c334704d5";
  // 结算工作量
  public static final String DEF_ID_USER_CUST_PARA_CUC_TJ_CONSTRACT_EFFORT = "5df6f618c56a424c334704d6";

  // 以下为名称常量
  public static final String DEMAND_PROPOSER_STRING = "需求提出人";
  public static final String CUSTOMER_CODE = "客户编号";
  public static final String DEMAND_REFINEMENT_STRING = "需求细化";
  public static final String WORKLOAD_ORGANIZATION_STRING = "工作量整理";
  public static final String ONLINE_STRING = "上线";
}
