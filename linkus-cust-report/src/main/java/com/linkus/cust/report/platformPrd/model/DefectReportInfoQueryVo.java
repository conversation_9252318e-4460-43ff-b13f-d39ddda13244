package com.linkus.cust.report.platformPrd.model;

import lombok.Data;
import org.bson.types.ObjectId;

@Data
public class DefectReportInfoQueryVo {
    /**
     * 项目集id
     */
    private ObjectId prjId;

    /**
     * 分页参数
     */
    private Integer pageSize;

    private Integer pageNo;

    /**
     * 查询的节点id
     */
    private ObjectId nodeId;

    /**
     * 查询的人员的id
     */
    private ObjectId userId;

    /**
     * 查询的指标类型
     */
    private String searchType;

    private String startDate;

    private String endDate;

}
