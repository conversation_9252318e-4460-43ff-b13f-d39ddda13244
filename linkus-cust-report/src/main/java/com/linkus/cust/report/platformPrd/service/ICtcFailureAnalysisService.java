package com.linkus.cust.report.platformPrd.service;

import org.bson.types.ObjectId;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * CTC故障分析
 * <AUTHOR>
 * @date 2020/05/07
 */
public interface ICtcFailureAnalysisService {

    /**
     * 根据父目录汇总查询子目录的区省故障分析
     * @param firstPrdCtlgId 一级产品目录
     * @param secondPrdCtlgIds 二级产品目录
     * @param periodBegin 周期开始
     * @param periodEnd 周期结束
     * @return 区省故障分析
     *
     */
    List<Map<String, Object>> queryDistrictFailure(List<ObjectId> prdCtlgId, boolean isShowThird, Date periodBegin, Date periodEnd);

    /**
     * 根据父目录汇总查询子目录的产品线故障分析
     *
     * @param prdCtlgId 产品目录
     * @param periodBegin 周期开始
     * @param periodEnd 周期结束
     * @return 产品线故障分析
     *
     */
    List<Map<String, Object>> queryProductFailure(List<ObjectId> prdCtlgId, boolean isShowSec, Date periodBegin, Date periodEnd);

    /**
     * 导出区省故障分析
     * @param firstPrdCtlgId 一级产品目录
     * @param secondPrdCtlgIds 二级产品目录
     * @param periodBegin 周期开始
     * @param periodEnd 周期结束
     * @return 区省故障分析
     *
     */
    LinkedHashMap<String, List<LinkedHashMap<String, Object>>> exportDistrictFailure(List<ObjectId> prdCtlgId, boolean isShowSec, Date periodBegin, Date periodEnd);

    /**
     * 导出产品线故障分析
     *
     * @param prdCtlgId 产品目录
     * @param periodBegin 周期开始
     * @param periodEnd 周期结束
     * @return 产品线故障分析
     *
     */
    LinkedHashMap<String, List<LinkedHashMap<String, Object>>> exportProductFailure(List<ObjectId> prdCtlgId, boolean isShowSec, Date periodBegin, Date periodEnd);
}
