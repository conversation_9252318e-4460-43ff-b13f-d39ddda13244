package com.linkus.cust.report.platformPrd.model;

import com.linkus.sysuser.model.TeSysUser;
import lombok.Data;
import org.bson.types.ObjectId;

/**
 * @Author: feizj
 * @Description: BSC 开发/测试任务单完成数量
 * @Date: 2022/10/18 10:30
 */
@Data
public class BscPrjConfigParamVo {
    /**
     * 项目id
     */
    private ObjectId prjId;
    /**
     * 开发人员任务单完成数指标（周）
     */
    private Integer devTaskNumPerWeek;
    /**
     * 开发人员任务单完成数指标（月）
     */
    private Integer devTaskNumPerMonth;
    /**
     * 测试人员任务单完成数指标（周）
     */
    private Integer testTaskNumPerWeek;
    /**
     * 测试人员任务单完成数指标（月）
     */
    private Integer testTaskNumPerMonth;
    /**
     * 当前登录人
     */
    private TeSysUser loginUser;
    /**
     * 是否参与评估
     */
    private Boolean isPartipateEvaluation;
}
