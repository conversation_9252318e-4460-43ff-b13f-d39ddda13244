package com.linkus.cust.report.platformPrd.service;

import com.linkus.base.util.PageBean;
import com.linkus.cust.report.platformPrd.model.CtcFailureDetailParam;
import com.linkus.cust.report.platformPrd.model.CtcFailureDetailVo;
import org.bson.types.ObjectId;

import java.util.List;
import java.util.Map;

public interface ICtcFailureDetailService {
    PageBean queryFailureDetail(CtcFailureDetailParam param);

    List<Map<String, Object>> exportFailureDetail(CtcFailureDetailParam param);

    CtcFailureDetailVo queryFailureDetailById(ObjectId bizId);
}
