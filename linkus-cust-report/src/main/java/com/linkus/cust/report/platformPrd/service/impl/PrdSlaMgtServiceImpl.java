package com.linkus.cust.report.platformPrd.service.impl;

import com.linkus.base.constants.BizConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_I;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.base.table.DBT;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.shiro.LoginUserHolder;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.JsonUtil;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.db.model.TeBiz2NodeInfo;
import com.linkus.cust.constant.ReportConstant;
import com.linkus.cust.report.platformPrd.dao.impl.ReportBizOprtHistDaoImpl;
import com.linkus.cust.report.platformPrd.dao.impl.ReportBizOprtHistInfoDaoImpl;
import com.linkus.cust.report.platformPrd.model.OprtHistVo;
import com.linkus.cust.report.platformPrd.model.TeBizOprtHist;
import com.linkus.cust.report.platformPrd.model.TeBizOprtHistInfo;
import com.linkus.cust.report.platformPrd.service.IPrdSlaMgtService;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.utils.ReportUtil;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

@Service("PrdSlaMgtServiceImpl")
public class PrdSlaMgtServiceImpl implements IPrdSlaMgtService {

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ReportBizOprtHistDaoImpl bizOprtHistDaoImpl;

    @Autowired
    private ReportBizOprtHistInfoDaoImpl bizOprtHistInfoDaoImpl;


    /**
     * @param bizIds
     * @return
     */
    @Override
    public Map<ObjectId,Map<ObjectId,List<OprtHistVo>>> getBizOprtInfosByBizIds(List<ObjectId> bizIds){
        List<IDbCondition> conds  = new ArrayList<>();
        conds.add(new DC_I<>(DFN.bizOprtHist_bizId,bizIds));
        conds.add(new DC_E(DFN.bizOprtHist_oprt.dot(DFN.bizOprtHist_cid), BizConstants.BIZ_EDIT_OPT_ID));

        List<TeBizOprtHist> bizOprtHistList = bizOprtHistDaoImpl.findByConds(conds, Sort.by(Sort.Direction.DESC, DbFieldName.bizOprtHist_oprtTime.n()));
        if(CollectionUtils.isEmpty(bizOprtHistList)){
            return  null;
        }
        Map<ObjectId, ObjectId> oprtId2OprtUserIdMap = bizOprtHistList.stream().filter(o -> null != o.getOprtUser()).collect(Collectors.toMap(o -> o.getId(), o -> o.getOprtUser().getUserId()));
        Map<ObjectId, TeBizOprtHist> oprtId2BizOprtHistMap = bizOprtHistList.stream().collect(Collectors.toMap(o -> o.getId(), o->o));
        List<ObjectId> oprtIds = bizOprtHistList.stream().map(TeBizOprtHist::getId).collect(Collectors.toList());
        List<ObjectId> allBizIds = bizOprtHistList.stream().map(TeBizOprtHist::getBizId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(oprtId2OprtUserIdMap) || CollectionUtils.isEmpty(oprtId2BizOprtHistMap) || CollectionUtils.isEmpty(oprtIds) || CollectionUtils.isEmpty(allBizIds)){
            return  null;
        }
        List<ObjectId> userIds = bizOprtHistList.stream().filter(o -> null != o.getOprtUser()).map(TeBizOprtHist::getOprtUser).map(TeUser::getUserId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userIds)){
            return  null;
        }
        List<TeSysUser> userByIdList = sysUserService.queryUsersByIdListList(userIds);
        if(CollectionUtils.isEmpty(userByIdList)){
            return  null;
        }
        Map<ObjectId, TeSysUser> userId2UserMap = userByIdList.stream().collect(Collectors.toMap(u -> u.getId(), u -> u));
        if(CollectionUtils.isEmpty(userId2UserMap)){
            return  null;
        }
        Map<ObjectId, List<ObjectId>>  userId2OprtIdsMap = new HashMap<>();
        Map<ObjectId, List<TeBizOprtHist>>  userId2OprtHistsMap = new HashMap<>();
        Map<ObjectId, Map<ObjectId, List<ObjectId>>>  bizId2userId2OprtIdsMap = new HashMap<>();
        Map<ObjectId, Map<ObjectId, List<TeBizOprtHist>>>  bizId2userId2OprtHistsMap = new HashMap<>();
        for (TeBizOprtHist teBizOprtHist : bizOprtHistList) {
            if(null == teBizOprtHist.getOprtUser()) continue;
            ObjectId  userId = teBizOprtHist.getOprtUser().getUserId();
            List<ObjectId> _oprtIds = new ArrayList<>();
            List<TeBizOprtHist> _oprtHists = new ArrayList<>();
            for (TeBizOprtHist _oprtHist : bizOprtHistList) {
                if(userId.equals(teBizOprtHist.getOprtUser().getUserId())){
                    _oprtHists.add(_oprtHist);
                }
            }
            userId2OprtIdsMap.put(userId,_oprtIds);
            userId2OprtHistsMap.put(userId,_oprtHists);
        }
        Map<ObjectId, List<TeBizOprtHist>>  newBizId2UserId2OprtHistsMap = new HashMap<>();
        Map<ObjectId, List<ObjectId>>  newBizId2UserId2OprtIdsMap = new HashMap<>();
        for (TeBizOprtHist teBizOprtHist : bizOprtHistList) {
            ObjectId  _bizId = teBizOprtHist.getBizId();
            if(null == teBizOprtHist.getOprtUser())  continue;
            if(bizId2userId2OprtIdsMap.containsKey(_bizId) || bizId2userId2OprtHistsMap.containsKey(_bizId) )  continue;
            ObjectId  _userId = teBizOprtHist.getOprtUser().getUserId();
            List<TeBizOprtHist> _bizId2OprtHists = new ArrayList<>();
            List<ObjectId> oprdIds = new ArrayList<>();
            List<TeBizOprtHist> teBizOprtHists = userId2OprtHistsMap.get(_userId);
            for (TeBizOprtHist _oprtHist : teBizOprtHists) {
                if(_bizId.equals(_oprtHist.getBizId())){
                    _bizId2OprtHists.add(_oprtHist);
                    oprdIds.add(_oprtHist.getId());
                }
            }
            newBizId2UserId2OprtIdsMap.put(_userId,oprdIds);
            newBizId2UserId2OprtHistsMap.put(_userId,_bizId2OprtHists);
            bizId2userId2OprtIdsMap.put(_bizId,newBizId2UserId2OprtIdsMap);
            bizId2userId2OprtHistsMap.put(_bizId,newBizId2UserId2OprtHistsMap);

        }
        if(CollectionUtils.isEmpty(userId2OprtIdsMap)){
            return  null;
        }
        conds.clear();
        conds.add(new DC_I<>(DFN.bizOprtHistInfo_oprtId,oprtIds));
        conds.add(new DC_I<>(DFN.bizOprtHistInfo_field.dot(DFN.bizOprtHistInfo_fieldId),
                Arrays.asList(SysDefConstants.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME,SysDefConstants.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME,
                        SysDefConstants.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME)));
        List<TeBizOprtHistInfo> oprtHistInfos = bizOprtHistInfoDaoImpl.findByConds(conds,null);
        if(CollectionUtils.isEmpty(oprtHistInfos)){
            return  null;
        }
        Map<ObjectId, TeBizOprtHistInfo> oprtId2BizOprtHistInfoMap = oprtHistInfos.stream().collect(Collectors.toMap(o -> o.getOprtId(), o -> o));
        if(CollectionUtils.isEmpty(oprtId2BizOprtHistInfoMap)){
            return  null;
        }
        Map<ObjectId,Map<ObjectId,List<OprtHistVo>>> bizId2userId2OprtHistVoListMap = new HashMap<>();
        Map<ObjectId,List<OprtHistVo>> bizId2userId2OprtHistVoListMap2 = new HashMap<>();
        for (ObjectId bizIdKey : bizId2userId2OprtIdsMap.keySet()) {
            Map<ObjectId, List<ObjectId>>  valueNewBizId2UserId2OprtIdsMap = bizId2userId2OprtIdsMap.get(bizIdKey);
            Map<ObjectId, List<OprtHistVo>>  valueNewBizId2UserId2OprtHistVosMap = new HashMap<>();
            for (ObjectId userIdKey : valueNewBizId2UserId2OprtIdsMap.keySet()) {
                List<ObjectId>  valueOprtIds = valueNewBizId2UserId2OprtIdsMap.get(userIdKey);
                List<OprtHistVo>  oprtHistVos = new ArrayList<>();
                for (ObjectId valueOprtId : valueOprtIds) {
                    TeBizOprtHistInfo teBizOprtHistInfo = oprtId2BizOprtHistInfoMap.get(valueOprtId);
                    OprtHistVo  _oprtHistVo = new OprtHistVo();//有操作id
                    TeSysUser sysUser = userId2UserMap.get(userIdKey);
                    _oprtHistVo.setUserId(StringUtil.to(userIdKey,String.class));
                    _oprtHistVo.setLoginName(sysUser.getLoginName());
                    _oprtHistVo.setUserName(sysUser.getUserName());
                    BeanUtils.copyProperties( teBizOprtHistInfo,  _oprtHistVo);
                    oprtHistVos.add(_oprtHistVo);
                }
                valueNewBizId2UserId2OprtHistVosMap.put(userIdKey,oprtHistVos);
            }
            bizId2userId2OprtHistVoListMap.put(bizIdKey,valueNewBizId2UserId2OprtHistVosMap);
        }
        return bizId2userId2OprtHistVoListMap;
    }

    public Map<ObjectId,List<OprtHistVo>> getBizOprtInfosByBizIds2(List<ObjectId> bizIds){
        List<IDbCondition> conds  = new ArrayList<>();
        conds.add(new DC_I<>(DFN.bizOprtHist_bizId,bizIds));
        conds.add(new DC_E(DFN.bizOprtHist_oprt.dot(DFN.bizOprtHist_cid), BizConstants.BIZ_EDIT_OPT_ID));
        List<TeBizOprtHist> bizOprtHistList = bizOprtHistDaoImpl.findByConds(conds, Sort.by(Sort.Direction.DESC, DbFieldName.bizOprtHist_oprtTime.n()));
        if(CollectionUtils.isEmpty(bizOprtHistList)){
            return  null;
        }
        Map<ObjectId, ObjectId> oprtId2OprtUserIdMap = bizOprtHistList.stream().filter(o -> null != o.getOprtUser()).collect(Collectors.toMap(o -> o.getId(), o -> o.getOprtUser().getUserId()));
        Map<ObjectId, TeBizOprtHist> oprtId2BizOprtHistMap = bizOprtHistList.stream().collect(Collectors.toMap(o -> o.getId(), o->o));
        List<ObjectId> oprtIds = bizOprtHistList.stream().map(TeBizOprtHist::getId).collect(Collectors.toList());
        List<ObjectId> allBizIds = bizOprtHistList.stream().map(TeBizOprtHist::getBizId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(oprtId2OprtUserIdMap) || CollectionUtils.isEmpty(oprtId2BizOprtHistMap) || CollectionUtils.isEmpty(oprtIds) || CollectionUtils.isEmpty(allBizIds)){
            return  null;
        }
        List<ObjectId> userIds = bizOprtHistList.stream().filter(o -> null != o.getOprtUser()).map(TeBizOprtHist::getOprtUser).map(TeUser::getUserId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userIds)){
            return  null;
        }
        List<TeSysUser> userByIdList = sysUserService.queryUsersByIdListList(userIds);
        if(CollectionUtils.isEmpty(userByIdList)){
            return  null;
        }
        Map<ObjectId, TeSysUser> userId2UserMap = userByIdList.stream().collect(Collectors.toMap(u -> u.getId(), u -> u));
        if(CollectionUtils.isEmpty(userId2UserMap)){
            return  null;
        }
        conds.clear();
        conds.add(new DC_I<>(DFN.bizOprtHistInfo_oprtId,oprtIds));
        conds.add(new DC_I<>(DFN.bizOprtHistInfo_field.dot(DFN.bizOprtHistInfo_fieldId),
                Arrays.asList(SysDefConstants.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME,SysDefConstants.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME,
                        SysDefConstants.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME)));
        List<TeBizOprtHistInfo> oprtHistInfos = bizOprtHistInfoDaoImpl.findByConds(conds,null);
        if(CollectionUtils.isEmpty(oprtHistInfos)){
            return  null;
        }
        Map<ObjectId, TeBizOprtHistInfo> oprtId2BizOprtHistInfoMap = oprtHistInfos.stream().collect(Collectors.toMap(o -> o.getOprtId(), o -> o));
        if(CollectionUtils.isEmpty(oprtId2BizOprtHistInfoMap)){
            return  null;
        }
        List<OprtHistVo> _oprtHistVos = new ArrayList<>();
        for (TeBizOprtHist teBizOprtHist : bizOprtHistList) {
            if(null == teBizOprtHist.getOprtUser()) continue;
            ObjectId  userIdKey = teBizOprtHist.getOprtUser().getUserId();
            OprtHistVo  _oprtHistVo = new OprtHistVo();//有操作id
            TeSysUser sysUser = userId2UserMap.get(userIdKey);
            _oprtHistVo.setUserId(StringUtil.to(userIdKey,String.class));
            _oprtHistVo.setLoginName(sysUser.getLoginName());
            _oprtHistVo.setUserName(sysUser.getUserName());
            BeanUtils.copyProperties( teBizOprtHist,  _oprtHistVo);
            _oprtHistVo.setOprtTime(DateUtil.formatDate2Str(teBizOprtHist.getOprtTime(),DateUtil.DATETIME_FORMAT));
            ObjectId oprtId = _oprtHistVo.getId();
            if(null != oprtId2BizOprtHistInfoMap.get(oprtId)){
                _oprtHistVo.setField(oprtId2BizOprtHistInfoMap.get(oprtId).getField());
            }
            _oprtHistVos.add(_oprtHistVo);
        }
        if(CollectionUtils.isEmpty(_oprtHistVos)){
            return  null;
        }
        Map<ObjectId,List<OprtHistVo>> bizId2userId2OprtHistVoListMap2 = new HashMap<>();
        for (OprtHistVo oprtHistVo : _oprtHistVos) {
            if(null == oprtHistVo.getUserId()) continue;
            ObjectId  _bizId = oprtHistVo.getBizId();
            if(bizId2userId2OprtHistVoListMap2.containsKey(_bizId))  continue;
            List<OprtHistVo> resBizOprtHists = new ArrayList<>();
            for (OprtHistVo oprtHistVo2 : _oprtHistVos) {
                if(null == oprtHistVo2.getUserId()) continue;
                if(_bizId.equals(oprtHistVo2.getBizId())){
                    resBizOprtHists.add(oprtHistVo2);
                }
            }
            bizId2userId2OprtHistVoListMap2.put(_bizId,resBizOprtHists) ;
        }
        return bizId2userId2OprtHistVoListMap2;
    }

    @Override
    public PageBean prdSlaMgtJudgmentSignView(Map<String, Object> params,ObjectId userId){
        PageBean pageBean = new PageBean();
        Map<String, Object> sqlAndcountSql = this.verifyJudgmentSignParamsAndGetSqlAndcountSql(params,userId);
        List<Document> sql = StringUtil.to(sqlAndcountSql.get("sql"), List.class);
        List<Document> countSql = StringUtil.to(sqlAndcountSql.get("countSql"), List.class);
        MongoCursor<Document> countOutPut = this.mongoTemplate.getCollection(DBT.BIZ.n()).aggregate(countSql).cursor();
        Integer total = 0;
        if (null != countOutPut) {
            while (countOutPut.hasNext()) {
                Document countObject = countOutPut.next();
                total = StringUtil.toInteger(countObject.get("count"));
            }
        }
        pageBean.setCount((int)total);
        if (total > 0) {
            MongoCursor<Document> cursor = mongoTemplate.getCollection(DBT.BIZ.n()).aggregate(sql).cursor();
            List<Map<String,Object>>  outBizs = this.getPrdSlaJudgmentSignObjectList(cursor);
            pageBean.setObjectList(outBizs);
        }
        return pageBean;
    }
    private List<Map<String,Object>> getPrdSlaJudgmentSignObjectList(MongoCursor<Document> cursor){
        List<Map<String,Object>>  outBizs = new ArrayList<>();
        List<ObjectId>  bizIds = new ArrayList<>();
        if (null != cursor) {
            while (cursor.hasNext()) {
                Document output = cursor.next();
                Map<String,Object> outBiz = new HashMap<>();
                //产品目录 TODO
                bizIds.add(StringUtil.to(output.get("_id"), ObjectId.class));
                outBiz.put("bizId",StringUtil.to(output.get("_id"), ObjectId.class));
                outBiz.put("name",StringUtil.to(output.get("name"), String.class));
                outBiz.put("code",StringUtil.to(output.get("code"), String.class));
                outBiz.put("desc",StringUtil.to(output.get("desc"), String.class));
                if(null != output.get(DFN.biz_status.n())){
                    Map statusMap = StringUtil.to(output.get(DFN.biz_status.n()), Map.class);
                    TeIdNameCn status = new TeIdNameCn(StringUtil.to(statusMap.get("cid"), ObjectId.class),StringUtil.to(statusMap.get("name"), String.class),"");
                    outBiz.put("status",StringUtil.to(statusMap.get("name"), String.class));
                }
                outBiz.put("addTimeArange",DateUtil.formatDate2Str(StringUtil.to(output.get(DFN.biz_addTime.n()), Date.class),DateUtil.DATE_FORMAT));
                if(null != output.get(DFN.biz_addUser.n())){
                    Map addUserMap = StringUtil.to(output.get(DFN.biz_addUser.n()), Map.class);
                    TeUser addUser = new TeUser(StringUtil.to(addUserMap.get("userId"), ObjectId.class),StringUtil.to(addUserMap.get("loginName"),String.class),
                            StringUtil.to(addUserMap.get("userName"), String.class),StringUtil.to(addUserMap.get("jobCode"),String.class));
                    outBiz.put("addUser",StringUtil.to(addUserMap.get("loginName"), String.class));
                }
                //SLA需求单号	SLA需求优先级	来源BU
                //预计交付日期	集成测试计划开始时间	入库发布计划完成时间
                if(null != output.get(DFN.biz_custFieldInfo.n())){
                    Map<ObjectId, Object> custFieldInfo = (Map<ObjectId, Object>)output.get(DFN.biz_custFieldInfo.n());
                    if (null != custFieldInfo ){
                        outBiz.put("slaDemandCode",StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_CODE.toString()), String.class));
                        outBiz.put("slaDemandPrior", ReportUtil.custFieldFormatValue(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_PRIOR.toString())));
                        outBiz.put("demandSourceBu",ReportUtil.custFieldFormatValue(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_DEMAND_SOURCE_BU.toString())));
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME.toString()))
                            outBiz.put("evaluatedDeliveryTime",StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME.toString()), String.class)
//                                    DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME.toString()), Date.class),DateUtil.DATE_FORMAT)
                            );
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME.toString()))
                            outBiz.put("integratedTestPlanStartTime",//StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME.toString()), String.class)
                                    DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME.toString()), Date.class),DateUtil.DATE_FORMAT)
                            );
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME.toString()))
                            outBiz.put("wareHouseReleasePlanEndTime",//StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME.toString()), String.class)
                                    DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME.toString()), Date.class),DateUtil.DATE_FORMAT)
                            );
                    }
                }
                if(null != output.get(DFN.biz_nodeInfo.n())){
                    Map<ObjectId, TeBiz2NodeInfo> nodeInfo = (Map<ObjectId, TeBiz2NodeInfo>)output.get(DFN.biz_nodeInfo.n());
                    if(null != nodeInfo.get(ReportConstant.DEF_ID_NODE_DEMAND_ACCEPTOR.toString())){
                        Map<String, Object> teBiz2NodeInfo = (Map<String, Object>)nodeInfo.get(ReportConstant.DEF_ID_NODE_DEMAND_ACCEPTOR.toString());
                        outBiz.put("demandAcceptor",ReportUtil.custFieldFormatValue(teBiz2NodeInfo.get("taskResps")));
                    }
                }
                //关联子需求 todo
                String childCode = "";
                List joinList = StringUtil.to(output.get("_joinBiz_"), List.class);
                if(!CollectionUtils.isEmpty(joinList)){
                    for (Object child : joinList) {
                        ((Map<String,Object>)child).get("_id");
                        ((Map<String,Object>)child).get("code");
                        childCode += ((Map<String,Object>)child).get("code") + ",";
                    }
                }
                outBiz.put("childCode", StringUtil.isNotNull(childCode) ? childCode.substring(0,childCode.length()-1) : null);
                outBizs.add(outBiz);
            }
        }
        //加入变更历史信息 CommonResult<List<ItfDeptVO>> listCommonResult = bizOprtInfoClient.listItfDepts(ccCodes, null);
        if(!CollectionUtils.isEmpty(bizIds)){
            Map<ObjectId, List<OprtHistVo>> bizOprtInfosByBizIds2Map = getBizOprtInfosByBizIds2(bizIds);
            if(CollectionUtils.isEmpty(bizOprtInfosByBizIds2Map)){
                return  outBizs;
            }
            for (Map<String, Object> outBiz : outBizs) {
                Object bizId = outBiz.get("bizId");
                if(null != bizOprtInfosByBizIds2Map.get(bizId)){
                    outBiz.put("allBizOprHists",bizOprtInfosByBizIds2Map.get(bizId));
                }
            }
        }
        return  outBizs;
    }
    /**
     * 获取当前用户登录信息
     */
    private TeSysUser getLoginUser() {
        TeSysUser loginUser = sysUserService.queryByLoginName(LoginUserHolder.getLoginName());
        return loginUser;
    }

    private Document dateDBObject(String timeRange){
        if (StringUtil.isNotNull(timeRange)) {
            Map<String, Object> dateMap = JsonUtil.toMap(timeRange);
            Object date = dateMap.get("data");
            String[] dates = date.toString().split("~");
            if (dates.length == 2) {
                Date dateBegin = DateUtil.parseDate(dates[0]+DateUtil.DAY_START, DateUtil.DATETIME_FORMAT);
                Date dateEnd = DateUtil.parseDate(dates[1]+DateUtil.DAY_DEADLINE, DateUtil.DATETIME_FORMAT);
                return  new Document().append("$gt", dateBegin).append("$lt", dateEnd);
            }
        }
        return null;
    }
    private Map<String, Object> verifyJudgmentSignParamsAndGetSqlAndcountSql(Map<String, Object> param,ObjectId userId){
        //编号	产品目录	名称	业务状态	预计交付日期	集成测试计划开始时间	"入库发布计划完成时间
        //描述	提出时间	需求提出人	需求受理人	SLA需求单号	SLA需求优先级	来源BU	关联子需求
        //默认研判会签节点责任人是当前登陆人
        //产品名称 必选id 默认PRD_SLA
        List<ObjectId> prdIds = Arrays.asList(new ObjectId("6327e2e5e0ee77517b19c4b4"));//StringUtil.transIds2List((String)param.get("prdIds"), ",", ObjectId.class);
        Assert.isTrue(!CollectionUtils.isEmpty(prdIds),"产品不能为空！");
        Assert.isTrue(null != userId,"登陆人不能为空！");
        String code = StringUtil.to(param.get("code"), String.class);
        String name = StringUtil.to(param.get("name"), String.class);
        List<ObjectId> statusIds  = StringUtil.transIds2List((String)param.get("statusIds"), ",", ObjectId.class);//
        String integratedTestPlanStartTimeRange = StringUtil.to(param.get("integratedTestPlanStartTimeRange"), String.class);
        String desc = StringUtil.to(param.get("desc"), String.class);
        String addTimeRange = StringUtil.to(param.get("addTimeRange"), String.class);//提出时间
        String demandAcceptor = StringUtil.to(param.get("demandAcceptor"), String.class);//需求受理人
        String slaDemandCode = StringUtil.to(param.get("slaDemandCode"), String.class);//SLA需求单号
        ObjectId slaReqPrior = StringUtil.to(param.get("slaReqPrior"), ObjectId.class);//SLA需求优先级
        ObjectId sourceBu = StringUtil.to(param.get("sourceBu"), ObjectId.class);
        String childCode = StringUtil.to(param.get("childCode"), String.class);//关联子需求


        Map<String, Object> sqlAndcountSql = new HashMap<>();

        List<Document> sql = new ArrayList<>();
        List<Document> countSql = new ArrayList<>();
        Document mainConds = new Document();
        mainConds.put(DFN.biz_isValid.n(), true);
        mainConds.put(DFN.biz_prd.dot(DFN.common_cid).n(), new Document("$in", prdIds ));
        mainConds.put(DFN.biz_nodeInfo.dot(ReportConstant.BIZ_NODE_FIELD_STUDY_JUDGMENT_SIGN).dot(DFN.biz_nodeInfo_testDefId_taskResps).dot(DFN.common_userId).n(),
                userId);
        if(StringUtil.isNotNull(code))
            mainConds.put(DFN.biz_code.n(), new Document("$regex", code ) );
        if(StringUtil.isNotNull(name))
            mainConds.put(DFN.biz_name.n(), new Document("$regex", name ) );
        if(!CollectionUtils.isEmpty(statusIds))
            mainConds.put(DFN.biz_status.dot(DFN.common_cid).n(),new Document("$in", statusIds ));
        if(StringUtil.isNotNull(integratedTestPlanStartTimeRange))
            mainConds.put(DFN.biz_custFieldInfo.dot(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME).n(),dateDBObject(integratedTestPlanStartTimeRange));
        if(StringUtil.isNotNull(desc))
            mainConds.put(DFN.biz_desc.n(), new Document("$regex", desc ) );
        if(StringUtil.isNotNull(addTimeRange))
            mainConds.put(DFN.biz_addTime.n(),dateDBObject(addTimeRange));
        if(StringUtil.isNotNull(demandAcceptor))
            mainConds.put(DFN.biz_nodeInfo.dot(ReportConstant.DEF_ID_NODE_DEMAND_ACCEPTOR).dot(DFN.biz_nodeInfo_testDefId_taskResps).dot(DFN.common_userName).n(),
                    new Document("$regex", demandAcceptor ));
        if(StringUtil.isNotNull(slaDemandCode))
            mainConds.put(DFN.biz_custFieldInfo.dot(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_CODE).n(), new Document("$regex", slaDemandCode ) );
        if(StringUtil.isNotNull(slaReqPrior))
            mainConds.put(DFN.biz_custFieldInfo.dot(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_PRIOR).dot(DFN.common_cid).n(), slaReqPrior );
        if(StringUtil.isNotNull(sourceBu))
            mainConds.put(DFN.biz_custFieldInfo.dot(ReportConstant.BIZ_CUST_FIELD_DEMAND_SOURCE_BU).dot(DFN.common_cid).n(), sourceBu);


        String joinAlias = DFN.join_biz.n();
        Document lookUp = new Document()
                .append("from", DBT.BIZ.n())
                .append("localField", DFN.biz_id.n())
                .append("foreignField", DFN.biz_hangUpBiz.dot(DFN.common_cid).n())
                .append("as", joinAlias);

        Document childBizConds = new Document();
        childBizConds.put(DFN.join_biz.dot(DFN.common_isValid).n(), true);
        if(StringUtil.isNotNull(childCode)){
            childBizConds.put(DFN.join_biz.dot(DFN.biz_code).n(), new Document("$regex", childCode ));
        }
        Document projectConds = new Document();
        projectConds.put(DFN.biz_code.n(), DFN.biz_code.$n());
        projectConds.put(DFN.biz_prdCtlg.n(), 1);
        projectConds.put(DFN.biz_name.n(), 1);
        projectConds.put(DFN.biz_desc.n(), 1);
        projectConds.put(DFN.biz_status.n(), 1);
        projectConds.put(DFN.biz_nodeInfo.n(), 1);
        projectConds.put(DFN.biz_custFieldInfo.n(), 1);
        projectConds.put(DFN.biz_addTime.n(), 1);
        projectConds.put(DFN.biz_addUser.n(), 1);
        projectConds.put(DFN.join_biz.n(), 1);
        sql.add(new Document("$match", mainConds));
        sql.add(new Document("$lookup", lookUp));
        sql.add(new Document("$match", childBizConds));
        sql.add(new Document("$project", projectConds));
        sql.add(new Document("$sort", new Document(DFN.biz_addTime.n(), -1)));//按时间降序
        Map<String, Object> pager = null != param.get("pager") ? (Map<String, Object>) param.get("pager") : null;
        if (null != pager && null != pager.get("index") && null != pager.get("size")) {
            Integer index = StringUtil.toInteger(pager.get("index"));
            Integer size = StringUtil.toInteger(pager.get("size"));
            sql.add(new Document("$skip", index * size));
            sql.add(new Document("$limit", size));
        }
        countSql.add(new Document("$match", mainConds));
        countSql.add(new Document("$lookup", lookUp));
        countSql.add(new Document("$match", childBizConds));
        countSql.add(new Document("$project", projectConds));
        countSql.add(new Document("$sort", new Document(DFN.biz_addTime.n(), -1)));
        Document groupContent = new Document();
        groupContent.append("_id", null);
        groupContent.append("count", new Document("$sum", 1));
        countSql.add(new Document("$group", groupContent));
        sqlAndcountSql.put("sql", sql);
        sqlAndcountSql.put("countSql", countSql);
        return sqlAndcountSql;
    }

    @Override
    public PageBean prdSlaMgtJudgmentConfirmView(Map<String, Object> params,ObjectId userId){
        PageBean pageBean = new PageBean();
        Map<String, Object> sqlAndcountSql = this.verifyJudgmentConfirmParamsAndGetSqlAndcountSql(params, userId);
        List<Document> sql = StringUtil.to(sqlAndcountSql.get("sql"), List.class);
        List<Document> countSql = StringUtil.to(sqlAndcountSql.get("countSql"), List.class);
        MongoCursor<Document> countOutPut = this.mongoTemplate.getCollection(DBT.BIZ.n()).aggregate(countSql).cursor();
        Integer total = 0;
        if (null != countOutPut) {
            while (countOutPut.hasNext()) {
                Document countObject = countOutPut.next();
                total = StringUtil.toInteger(countObject.get("count"));
            }
        }
        pageBean.setCount((int)total);
        if (total > 0) {
            MongoCursor<Document> cursor = mongoTemplate.getCollection(DBT.BIZ.n()).aggregate(sql)
                    .cursor();
            List<Map<String,Object>>  outBizs = this.getPrdSlaJudgmentConfirmObjectList(cursor);
            pageBean.setObjectList(outBizs);
        }
        return pageBean;
    }
    private List<Map<String,Object>> getPrdSlaJudgmentConfirmObjectList(MongoCursor<Document> cursor){
        List<Map<String,Object>>  outBizs = new ArrayList<>();
        if (null != cursor) {
            while (cursor.hasNext()) {
                Document output = cursor.next();
                Map<String,Object> outBiz = new HashMap<>();
                //产品目录 TODO
                outBiz.put("bizId",StringUtil.to(output.get("_id"), ObjectId.class));
                outBiz.put("name",StringUtil.to(output.get("name"), String.class));
                outBiz.put("code",StringUtil.to(output.get("code"), String.class));
                outBiz.put("desc",StringUtil.to(output.get("desc"), String.class));
                if(null != output.get(DFN.biz_status.n())){
                    Map statusMap = StringUtil.to(output.get(DFN.biz_status.n()), Map.class);
                    TeIdNameCn status = new TeIdNameCn(StringUtil.to(statusMap.get("cid"), ObjectId.class),StringUtil.to(statusMap.get("name"), String.class),"");
                    outBiz.put("status",StringUtil.to(statusMap.get("name"), String.class));
                }
                outBiz.put("addTimeArange",DateUtil.formatDate2Str(StringUtil.to(output.get(DFN.biz_addTime.n()), Date.class),DateUtil.DATE_FORMAT));
                if(null != output.get(DFN.biz_addUser.n())){
                    Map addUserMap = StringUtil.to(output.get(DFN.biz_addUser.n()), Map.class);
                    TeUser addUser = new TeUser(StringUtil.to(addUserMap.get("userId"), ObjectId.class),StringUtil.to(addUserMap.get("loginName"),String.class),
                            StringUtil.to(addUserMap.get("userName"), String.class),StringUtil.to(addUserMap.get("jobCode"),String.class));
                    outBiz.put("addUser",StringUtil.to(addUserMap.get("loginName"), String.class));
                }
                //SLA需求单号	SLA需求优先级	来源BU
                //预计交付日期	集成测试计划开始时间	入库发布计划完成时间
                if(null != output.get(DFN.biz_custFieldInfo.n())){
                    Map<ObjectId, Object> custFieldInfo = (Map<ObjectId, Object>)output.get(DFN.biz_custFieldInfo.n());
                    if (null != custFieldInfo ){
                        outBiz.put("slaDemandCode",StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_CODE), String.class));
                        outBiz.put("slaDemandPrior", ReportUtil.custFieldFormatValue(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_SLA_DEMAND_PRIOR)));
                        outBiz.put("demandSourceBu",ReportUtil.custFieldFormatValue(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_DEMAND_SOURCE_BU)));
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME))
                            outBiz.put("evaluatedDeliveryTime",DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_EVALUATED_DELIVERY_TIME), Date.class),DateUtil.DATE_FORMAT));
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME))
                            outBiz.put("integratedTestPlanStartTime",DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_INTEGRATED_TEST_PLAN_START_TIME), Date.class),DateUtil.DATE_FORMAT));
                        if(null !=custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME))
                            outBiz.put("wareHouseReleasePlanEndTime",DateUtil.formatDate2Str(StringUtil.to(custFieldInfo.get(ReportConstant.BIZ_CUST_FIELD_WARE_HOUSE_RELEASE_PLAN_END_TIME), Date.class),DateUtil.DATE_FORMAT));
                    }
                }
                if(null != output.get(DFN.biz_nodeInfo.n())){
                    Map<ObjectId, TeBiz2NodeInfo> nodeInfo = (Map<ObjectId, TeBiz2NodeInfo>)output.get(DFN.biz_nodeInfo.n());
                    if(null != nodeInfo.get(ReportConstant.DEF_ID_NODE_DEMAND_ACCEPTOR.toString())){
                        Map<String, Object> biz2NodeInfo = (Map<String, Object>)nodeInfo.get(ReportConstant.DEF_ID_NODE_DEMAND_ACCEPTOR.toString());
                        outBiz.put("demandAcceptor",ReportUtil.custFieldFormatValue(biz2NodeInfo.get("taskResps")));
                    }
                    if(null != nodeInfo.get(ReportConstant.BIZ_NODE_FIELD_REQ_DISPATCH.toString())){
                        Map<String, Object> biz2NodeInfo = (Map<String, Object>)nodeInfo.get(ReportConstant.BIZ_NODE_FIELD_REQ_DISPATCH.toString());
                        outBiz.put("reqDispatchAcceptor",ReportUtil.custFieldFormatValue(biz2NodeInfo.get("taskResps")));
                    }
                    if(null != nodeInfo.get(SysDefConstants.DEF_ID_NODE_ADD_TEST.toString())){
                        Map<String, Object> biz2NodeInfo = (Map<String, Object>)nodeInfo.get(SysDefConstants.DEF_ID_NODE_ADD_TEST.toString());
                        outBiz.put("addTestAcceptor",ReportUtil.custFieldFormatValue(biz2NodeInfo.get("taskResps")));
                        outBiz.put("addTestPlanEndDate",DateUtil.formatDate2Str(StringUtil.to(biz2NodeInfo.get("planEndDate"),Date.class),DateUtil.DATE_FORMAT));
                    }
                }
                //关联子需求 todo
                String childCode = "";
                List joinList = StringUtil.to(output.get("_joinBiz_"), List.class);
                if(!CollectionUtils.isEmpty(joinList)){
                    for (Object child : joinList) {
                        ((Map<String,Object>)child).get("_id");
                        ((Map<String,Object>)child).get("code");
                        childCode += ((Map<String,Object>)child).get("code") + ",";
                    }
                }
                outBiz.put("childCode", StringUtil.isNotNull(childCode) ? childCode.substring(0,childCode.length()-1) : null);
                outBizs.add(outBiz);
            }
        }
        return  outBizs;
    }

    private Map<String, Object> verifyJudgmentConfirmParamsAndGetSqlAndcountSql(Map<String, Object> param,ObjectId userId){
        //编号	产品目录	名称	业务状态	提测计划完成时间
        //描述	提出时间	需求分派责任人	提测责任人
        //关联子需求
        //产品名称 必选id 默认PRD_SLA
        List<ObjectId> prdIds = Arrays.asList(new ObjectId("6327e2e5e0ee77517b19c4b4")); //StringUtil.transIds2List((String)param.get("prdIds"), ",", ObjectId.class);
        Assert.isTrue(!CollectionUtils.isEmpty(prdIds),"产品不能为空！");
        String code = StringUtil.to(param.get("code"), String.class);
        String name = StringUtil.to(param.get("name"), String.class);
        List<ObjectId> statusIds  = StringUtil.transIds2List((String)param.get("statusIds"), ",", ObjectId.class);//
        String addTestPlanEndTimeRange = StringUtil.to(param.get("addTestPlanEndTimeRange"), String.class);//提测计划完成时间
        String desc = StringUtil.to(param.get("desc"), String.class);
        String addTimeRange = StringUtil.to(param.get("addTimeRange"), String.class);//提出时间
        String reqDispatchResp = StringUtil.to(param.get("reqDispatchResp"), String.class);//需求分派责任人
        String addTestResp = StringUtil.to(param.get("addTestResp"), String.class);//提测责任人
        String grandsonCode = StringUtil.to(param.get("grandsonCode"), String.class);//关联子需求
        Map<String, Object> sqlAndcountSql = new HashMap<>();
        List<Document> sql = new ArrayList<>();
        List<Document> countSql = new ArrayList<>();
        Document mainConds = new Document();
        mainConds.put(DFN.biz_isValid.n(), true);//
        mainConds.put(DFN.biz_prd.dot(DFN.common_cid).n(), new Document("$in", prdIds ));
        mainConds.put(DFN.biz_nodeInfo.dot(SysDefConstants.DEF_ID_NODE_CONFIRM).dot(DFN.biz_nodeInfo_testDefId_taskResps).dot(DFN.common_userId).n(),
                userId);
        if(StringUtil.isNotNull(code))
            mainConds.put(DFN.biz_code.n(), new Document("$regex", code ) );
        if(StringUtil.isNotNull(name))
            mainConds.put(DFN.biz_name.n(), new Document("$regex", name ) );
        if(!CollectionUtils.isEmpty(statusIds))
            mainConds.put(DFN.biz_status.dot(DFN.common_cid).n(),new Document("$in", statusIds ));
        if(StringUtil.isNotNull(addTestPlanEndTimeRange))
            mainConds.put(DFN.biz_custFieldInfo.dot(ReportConstant.DEF_CUST_FIELD_ADD_TEST_PLAN_END_TIME).n(),dateDBObject(addTestPlanEndTimeRange));
        if(StringUtil.isNotNull(desc))
            mainConds.put(DFN.biz_desc.n(), new Document("$regex", desc ) );
        if(StringUtil.isNotNull(addTimeRange))
            mainConds.put(DFN.biz_addTime.n(),dateDBObject(addTimeRange));
        if(StringUtil.isNotNull(reqDispatchResp))
            mainConds.put(DFN.biz_nodeInfo.dot(ReportConstant.BIZ_NODE_FIELD_REQ_DISPATCH).dot(DFN.biz_nodeInfo_testDefId_taskResps).dot(DFN.common_userName).n(),
                    new Document("$regex", reqDispatchResp ));
        if(StringUtil.isNotNull(addTestResp))
            mainConds.put(DFN.biz_nodeInfo.dot(SysDefConstants.DEF_ID_NODE_ADD_TEST).dot(DFN.biz_nodeInfo_testDefId_taskResps).dot(DFN.common_userName).n(),
                    new Document("$regex", addTestResp ));
        String joinAlias = DFN.join_biz.n();
        Document lookUp = new Document()
                .append("from", DBT.BIZ.n())
                .append("localField", DFN.biz_id.n())
                .append("foreignField", DFN.biz_hangUpBiz.dot(DFN.common_cid).n())
                .append("as", joinAlias);
        Document lookUpGrandson = new Document()
                .append("from", DBT.BIZ.n())
                .append("localField", DFN.join_biz.dot(DFN.common__id).n())
                .append("foreignField", DFN.biz_hangUpBiz.dot(DFN.common_cid).n())
                .append("as", "grandsonBizs");

        Document childBizConds = new Document();
        childBizConds.put(DFN.join_biz.dot(DFN.common_isValid).n(), true);

        if(StringUtil.isNotNull(grandsonCode)){
            childBizConds.put("grandsonBizs.code", new Document("$regex", grandsonCode ));
        }
        Document projectConds = new Document();
        projectConds.put(DFN.biz_code.n(), DFN.biz_code.$n());
        projectConds.put(DFN.biz_prdCtlg.n(), 1);
        projectConds.put(DFN.biz_name.n(), 1);
        projectConds.put(DFN.biz_desc.n(), 1);
        projectConds.put(DFN.biz_status.n(), 1);
        projectConds.put(DFN.biz_nodeInfo.n(), 1);
        projectConds.put(DFN.biz_custFieldInfo.n(), 1);
        projectConds.put(DFN.biz_addTime.n(), 1);
        projectConds.put(DFN.biz_addUser.n(), 1);
        projectConds.put(DFN.join_biz.n(), 1);
        sql.add(new Document("$match", mainConds));
        sql.add(new Document("$lookup", lookUp));
        sql.add(new Document("$project", projectConds));
        sql.add(new Document("$sort", new Document(DFN.biz_addTime.n(), -1)));//按时间降序
        Map<String, Object> pager = null != param.get("pager") ? (Map<String, Object>) param.get("pager") : null;
        if (null != pager && null != pager.get("index") && null != pager.get("size")) {
            Integer index = StringUtil.toInteger(pager.get("index"));
            Integer size = StringUtil.toInteger(pager.get("size"));
            sql.add(new Document("$skip", index * size));
            sql.add(new Document("$limit", size));
        }
        countSql.add(new Document("$match", mainConds));
        countSql.add(new Document("$lookup", lookUp));
        countSql.add(new Document("$project", projectConds));
        countSql.add(new Document("$sort", new Document(DFN.biz_addTime.n(), -1)));
        Document groupContent = new Document();
        groupContent.append("_id", null);
        groupContent.append("count", new Document("$sum", 1));
        countSql.add(new Document("$group", groupContent));
        sqlAndcountSql.put("sql", sql);
        sqlAndcountSql.put("countSql", countSql);
        return sqlAndcountSql;
    }

}
