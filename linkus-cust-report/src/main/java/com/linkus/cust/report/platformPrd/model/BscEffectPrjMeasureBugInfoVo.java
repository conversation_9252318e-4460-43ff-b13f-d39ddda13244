package com.linkus.cust.report.platformPrd.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import org.bson.types.ObjectId;

@Data
@HeadRowHeight(20)
public class BscEffectPrjMeasureBugInfoVo {

    /**
     * 业务id
     */
    @ExcelIgnore
    private ObjectId bizId;

    /**
     * 业务编码
     */
    @ExcelProperty(value = "业务编码")
    @ColumnWidth(30)
    private String bizCode;

    /**
     * 业务类型
     */
    @ExcelProperty(value = "业务类型")
    @ColumnWidth(20)
    private String bizType;

    /**
     * 业务名称
     */
    @ExcelProperty(value = "业务名称")
    @ColumnWidth(30)
    private String bizName;

    /**
     * 提出人
     */
    @ExcelProperty(value = "提出人")
    @ColumnWidth(20)
    private String addUser;

    /**
     * 提出时间
     */
    @ExcelProperty(value = "提出时间")
    @ColumnWidth(20)
    private String addTime;

    /**
     * 严重程度
     */
    @ExcelProperty(value = "严重程度")
    @ColumnWidth(20)
    private String severity;

    /**
     * 业务状态
     */
    @ExcelProperty(value = "业务状态")
    @ColumnWidth(20)
    private String status;

    /**
     * 当前责任人
     */
    @ExcelProperty(value = "当前责任人")
    @ColumnWidth(20)
    private String currentResp;

    /**
     * 计划完成时间
     */
    @ExcelProperty(value = "计划完成时间")
    @ColumnWidth(20)
    private String planEndDate;

    public BscEffectPrjMeasureBugInfoVo() {
    }

}
