package com.linkus.evaluate.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.util.concurrent.AtomicDouble;
import com.linkus.base.db.base.BatchCondsUpsert;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.condition.impl.mini.DC_OR;
import com.linkus.base.db.base.condition.impl.mini.DC_R;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.base.field.DbFieldName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.db.mongo.model.bo.MongoResultBO;
import com.linkus.base.response.BusinessException;
import com.linkus.base.util.BigDecimalUtils;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.DoubleUtil;
import com.linkus.base.util.NumberUtils;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.bscQuality.service.IBscQualityService;
import com.linkus.cust.report.platformPrd.dao.ISysUserPfmDao;
import com.linkus.cust.report.platformPrd.model.po.PrjAppraiseScoreDetail;
import com.linkus.cust.report.platformPrd.model.po.PrjAppraiseScoreVo;
import com.linkus.cust.report.platformPrd.model.po.TeSysUserPfm;
import com.linkus.evaluate.constant.EvaluateConstant;
import com.linkus.evaluate.model.BscQualityEvalTypeEnum;
import com.linkus.evaluate.model.bo.ManagerEvalDetailBO;
import com.linkus.evaluate.model.vo.PrjManagerEvalDetailVO;
import com.linkus.evaluate.model.vo.PrjManagerEvalUpdateVO;
import com.linkus.evaluate.model.vo.PrjManagerQualityEvalVO;
import com.linkus.evaluate.model.vo.PrjPlQualityEvalVO;
import com.linkus.evaluate.model.vo.PrjQualityEvalQueryVO;
import com.linkus.evaluate.model.vo.PrjQualityEvalUpdateVO;
import com.linkus.evaluate.model.vo.PrjQualityEvalVO;
import com.linkus.evaluate.service.IBscPrjQualityEvalService;
import com.linkus.sys.dao.SysDefDao;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.utils.ReportUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/08/08
 */
@Service
public class BscPrjQualityEvalServiceImpl implements IBscPrjQualityEvalService {

    @Autowired
    private ISysUserPfmDao sysUserPfmDao;

    @Autowired
    private ISysUserDao sysUserDao;

    @Autowired
    private SysDefDao sysDefDao;

    @Resource
    private IBscQualityService qualityService;

    @Override
    public PageBean listPrjQualityEvaluate(PrjQualityEvalQueryVO qvo) {
        PageBean pageBean = new PageBean();
        MongoResultBO<PrjQualityEvalVO> resultBO = sysUserPfmDao.listPrjQualityEvaluate(qvo);
        if (qvo.getPageSize() != null && resultBO.getCount() == 0) {
            return pageBean;
        }
        for (PrjQualityEvalVO bo : resultBO.getData()) {
            bo.setPrjQualityEvalScore(BigDecimalUtils.getDoubleHalfNumNaN(bo.getPrjQualityEvalScore()));
        }
        pageBean.setCount(resultBO.getIntCount());
        pageBean.setObjectList(resultBO.getData());
        return pageBean;
    }

    @Override
    public void exportPrjQualityEvaluate(HttpServletResponse response, PrjQualityEvalQueryVO vo) throws IOException {
        PageBean pageBean = listPrjQualityEvaluate(vo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = "项目质量考评" + DateUtil.getCurrentTime() + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0)
                .head(PrjQualityEvalVO.class)
                .build();
        excelWriter.write(pageBean.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public PageBean listPrjPrdLineQualityEvaluate(PrjQualityEvalQueryVO qvo) {
        if (qvo.getPrjId() == null || StringUtils.isEmpty(qvo.getYm())) {
            throw BusinessException.initExc("参数不全");
        }
        PageBean pageBean = new PageBean();
        MongoResultBO<PrjPlQualityEvalVO> resultBO = sysUserPfmDao.listPrjPrdLineQualityEvaluate(qvo);
        if (qvo.getPageSize() != null && resultBO.getCount() == 0) {
            return pageBean;
        }
        // 设置总监的部门信息
        Set<ObjectId> managerUserIdSet = resultBO.getData().stream()
                .filter(vo -> vo.getEvalManagerUser() != null && vo.getEvalManagerUser().getUserId() != null)
                .map(PrjPlQualityEvalVO::getEvalManagerUser).map(TeUser::getUserId).collect(Collectors.toSet());
        Map<ObjectId, TeIdNameCn> userId2DeptMap = getUser2Dept(managerUserIdSet);
        
        for (PrjPlQualityEvalVO vo : resultBO.getData()) {
            ObjectId evalManagerUserId = vo.getEvalManagerUser() == null ? null : vo.getEvalManagerUser().getUserId();
            TeIdNameCn deptInfo = userId2DeptMap.get(evalManagerUserId);
            if (deptInfo != null) {
                vo.setEvalDept(deptInfo.getName());
            }
            // 评审类型
            vo.setEvalType(BscQualityEvalTypeEnum.getEvalTypeName(vo.getEvalType()));
            vo.setManMonthNum(BigDecimalUtils.getDoubleHalfNumNaN(vo.getManMonthNum()));
            // 分子
            vo.setNumerator(BigDecimalUtils.getDoubleHalfNumNaN(vo.getNumerator()));
            // 分母
            vo.setDenominator(BigDecimalUtils.getDoubleHalfNumNaN(vo.getDenominator()));
            // 量化数据
            vo.setCalcValue(BigDecimalUtils.getDoubleHalfNumNaN(vo.getCalcValue(),4));
            // 分子(修正)
            vo.setNumeratorEx(BigDecimalUtils.getDoubleHalfNumNaN(vo.getNumeratorEx()));
            // 分母(修正)
            vo.setDenominatorEx(BigDecimalUtils.getDoubleHalfNumNaN(vo.getDenominatorEx()));
            // 量化数据(修正)
            vo.setCalcValueEx(BigDecimalUtils.getDoubleHalfNumNaN(vo.getCalcValueEx(),4));
            // 量化数据得分
            vo.setCalcScore(BigDecimalUtils.getDoubleHalfNumNaN(vo.getCalcScore()));
            // 扣分
            vo.setDeductScore(BigDecimalUtils.getDoubleHalfNumNaN(vo.getDeductScore()));
            // 指标得分
            vo.setNormScore(BigDecimalUtils.getDoubleHalfNumNaN(vo.getNormScore()));
            // 总监得分
            vo.setScore(BigDecimalUtils.getDoubleHalfNumNaN(vo.getScore()));
            // 需要根据指标值确认 是否展示百分号
            if (EvaluateConstant.EVALUATE_PERCENT_ITEM_IDS.contains(vo.getItemInfoId())) {
                vo.setCalcValueStr(calcPercent(vo.getCalcValue()));
                vo.setCalcValueExStr(calcPercent(vo.getCalcValueEx()));
            } else {
                vo.setCalcValueStr(vo.getCalcValue() == null ? null : vo.getCalcValue().toString());
                vo.setCalcValueExStr(vo.getCalcValueEx() == null ? null : vo.getCalcValueEx().toString());
            }
        }
        pageBean.setCount(resultBO.getIntCount());
        pageBean.setObjectList(resultBO.getData());
        return pageBean;
    }

    @Override
    public void exportPrjPrdLineQualityEvaluate(HttpServletResponse response, PrjQualityEvalQueryVO vo) throws IOException {
        PageBean pageBean = listPrjPrdLineQualityEvaluate(vo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = "项目产品线质量考评" + DateUtil.getCurrentTime() + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0)
                .head(PrjPlQualityEvalVO.class)
                .build();
        excelWriter.write(pageBean.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public PageBean listPrjManagerQualityEvaluate(PrjQualityEvalQueryVO vo) {
        PageBean pageBean = new PageBean();
        MongoResultBO<TeSysUserPfm> resultBO = sysUserPfmDao.listPrjManagerQualityEvaluate(vo);
        if (vo.getPageSize() != null && resultBO.getCount() == 0) {
            return pageBean;
        }
        List<PrjManagerQualityEvalVO> list = new ArrayList<>();
        for (TeSysUserPfm pfm : resultBO.getData()) {
            double prjScore = calcManagerEvalPrjScore(pfm);
            PrjManagerQualityEvalVO evalVo = new PrjManagerQualityEvalVO();
            evalVo.setPfmId(pfm.getId());
            evalVo.setEvalManagerUserId(pfm.getEmp().getUserId());
            evalVo.setYm(pfm.getYm());
            evalVo.setManager(ReportUtil.mergeStr("/", pfm.getEmp().getUserName(), pfm.getEmp().getLoginName()));
            evalVo.setDept(pfm.getDef() == null ? null : pfm.getDef().getName());
            evalVo.setPrjScore(BigDecimalUtils.getDoubleHalfNumNaN(prjScore));
            evalVo.setKnowledgeScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_KNOWLEDGE_SCORE.getCid()));
            evalVo.setToolScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_TOOL_SCORE.getCid()));
            evalVo.setSystemScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_SYSTEM_SCORE.getCid()));
            evalVo.setAddScoreDesc(pfm.getText1());
            evalVo.setFailureDeductScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_FAILURE_DEDUCT_SCORE.getCid()));
            evalVo.setWorkOrderDeductScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_WORK_ORDER_DEDUCT_SCORE.getCid()));
            evalVo.setToolDeductScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_TOOL_DEDUCT_SCORE.getCid()));
            evalVo.setGroupDeductScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_GROUP_DEDUCT_SCORE.getCid()));
            evalVo.setProcessDeductScore(getManagerEvalScore(pfm.getItemInfo(),
                    EvaluateConstant.EVALUATE_ITEM_PROCESS_DEDUCT_SCORE.getCid()));
            evalVo.setDeductDesc(pfm.getText2());
            evalVo.setScore(DoubleUtil.formatDouble(pfm.getScore()));
            list.add(evalVo);
        }
        pageBean.setCount(resultBO.getIntCount());
        pageBean.setObjectList(list);
        return pageBean;
    }

    @Override
    public void exportPrjManagerQualityEvaluate(HttpServletResponse response, PrjQualityEvalQueryVO vo) throws IOException {
        PageBean pageBean = listPrjManagerQualityEvaluate(vo);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = "项目总监质量考评" + DateUtil.getCurrentTime() + ".xlsx";
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).build();
        WriteSheet sheet = EasyExcel.writerSheet(0)
                .head(PrjManagerQualityEvalVO.class)
                .build();
        excelWriter.write(pageBean.getObjectList(), sheet);
        excelWriter.finish();
    }

    @Override
    public PageBean listPrjManagerEvalDetail(PrjQualityEvalQueryVO vo) {
        if (StringUtils.isEmpty(vo.getYm()) || vo.getManagerUserId() == null) {
            throw BusinessException.initExc("参数不全");
        }
        PageBean pageBean = new PageBean();
        ManagerEvalDetailBO resultBO = sysUserPfmDao.listPrjManagerEvalDetail(vo);
        if (vo.getPageSize() != null && resultBO.getCount() == 0) {
            return pageBean;
        }
        List<PrjManagerEvalDetailVO> list = resultBO.getData();
        for (PrjManagerEvalDetailVO detailVO : list) {
            detailVO.setType(BscQualityEvalTypeEnum.getEvalTypeName(detailVO.getType()));
            detailVO.setManMonthNum(BigDecimalUtils.getDoubleHalfNumNaN(StringUtil.toDouble(detailVO.getManMonthNum())));
            detailVO.setScore(BigDecimalUtils.getDoubleHalfNumNaN(detailVO.getScore()));
        }
        if (vo.getPageSize() == null || list.size() < vo.getPageSize()) {
            Map<String, Object> totalInfo = resultBO.getTotalInfo();
            if (totalInfo != null) {
                PrjManagerEvalDetailVO totalCol = new PrjManagerEvalDetailVO();
                totalCol.setProv("当月合计得分");
                Double manMonthNum = DoubleUtil.getNotNull(totalInfo.get("manMonthNum"));
                Double score = DoubleUtil.getNotNull(totalInfo.get("score"));
                totalCol.setManMonthNum(BigDecimalUtils.getDoubleHalfNumNaN(manMonthNum));
                totalCol.setScore(BigDecimalUtils.getDoubleHalfNumNaN(score));
                list.add(totalCol);
            }
        }
        pageBean.setCount(resultBO.getIntCount() + 1);
        pageBean.setObjectList(list);
        return pageBean;
    }

    @Override
    public void updatePrdLineManager(PrjQualityEvalUpdateVO vo) {
        if (vo.getPrjId() == null || vo.getPfmId() == null) {
            throw BusinessException.initExc("参数不全");
        }
        TeSysUserPfm userPfm = sysUserPfmDao.findById(vo.getPfmId());
        if (userPfm == null || BooleanUtils.isNotTrue(userPfm.getIsValid())) {
            throw BusinessException.initExc("产品线考评不存在");
        }
        if (userPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(userPfm.getStatus().getCid())) {
            throw BusinessException.initExc("产品线考评已提交");
        }
        if (vo.getManagerUserId() == null) {
            sysUserPfmDao.updateById(userPfm.getId(),
                    Collections.singletonList(new UpdataData(DFN.sysUserPfm_manager, null)),
                    true);
            // 更新项目考评的scoreDetail中的user
            updatePrjPlManager(userPfm, null);
        } else {
            TeSysUser managerUser = sysUserDao.findById(vo.getManagerUserId());
            if (managerUser == null || BooleanUtils.isNotTrue(managerUser.getIsValid())) {
                throw BusinessException.initExc("项目总监不存在");
            }
            sysUserPfmDao.updateById(userPfm.getId(), Collections.singletonList(new UpdataData(DFN.sysUserPfm_manager, new TeUser(managerUser.getId(),
                    managerUser.getLoginName(), managerUser.getUserName(), managerUser.getUserName()))));
            // 更新项目考评的scoreDetail中的user
            updatePrjPlManager(userPfm, managerUser);
        }
    }
    @Override
    public void updatePrdLineIsValid(PrjQualityEvalUpdateVO vo) {
        if (vo.getPrjId() == null || vo.getPfmId() == null) {
            throw BusinessException.initExc("参数不全");
        }
        TeSysUserPfm plPfm = sysUserPfmDao.findById(vo.getPfmId());
        if (plPfm == null) {
            throw BusinessException.initExc("产品线考评不存在");
        }
        if (plPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(plPfm.getStatus().getCid())) {
            throw BusinessException.initExc("产品线考评已提交");
        }
        if (vo.getIsValid() != null) {
            List<UpdataData> updates = new ArrayList<>();
            updates.add(new UpdataData(DFN.sysUserPfm_isValid, vo.getIsValid()));
            sysUserPfmDao.updateById(vo.getPfmId(), updates);

            List<IDbCondition> conds = new ArrayList<>();
            conds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), EvaluateConstant.EVALUATE_TYPE_PRJ.getCid()));
            conds.add(new DC_E(DFN.sysUserPfm_prj.dot(DFN.common_cid), vo.getPrjId()));
            TeSysUserPfm prjPfm = sysUserPfmDao.findOne(conds);
            List<PrjAppraiseScoreDetail> scoreDetailList = prjPfm.getScoreDetail();
            if (CollectionUtils.isEmpty(scoreDetailList)) {
                return;
            }
            // 同产品线、同评估类型
            PrjAppraiseScoreDetail matchScoreDetail = scoreDetailList.stream()
                    .filter(scoreDetail -> (plPfm.getDef().getCid().equals(scoreDetail.getDef().getCid())
                            && plPfm.getText1().equals(scoreDetail.getType())))
                    .findFirst().orElse(null);
            if (matchScoreDetail == null) {
                return;
            }
            // 更新状态
            matchScoreDetail.setIsValid(vo.getIsValid());
            updates.clear();
            updates.add(new UpdataData(DFN.sysUserPfm_scoreDetail, scoreDetailList));
            sysUserPfmDao.updateById(prjPfm.getId(), updates);
        }
    }

    @Override
    public void updatePrdLineScoreDetail(PrjQualityEvalUpdateVO vo) {
        if (vo.getPrjId() == null || vo.getPfmId() == null || vo.getItemInfoId() == null) {
            throw BusinessException.initExc("参数不全");
        }
        TeSysUserPfm userPfm = sysUserPfmDao.findById(vo.getPfmId());
        if (userPfm == null) {
            throw BusinessException.initExc("产品线考评不存在");
        }
        if (userPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(userPfm.getStatus().getCid())) {
            throw BusinessException.initExc("产品线考评已提交");
        }
        List<PrjAppraiseScoreVo> itemInfoList = userPfm.getItemInfo();
        PrjAppraiseScoreVo matchItemInfo = itemInfoList.stream()
                .filter(itemInfo -> vo.getItemInfoId().equals(itemInfo.getItem().getCid()))
                .findFirst().orElse(null);
        if (matchItemInfo == null) {
            throw BusinessException.initExc("未找到考评项");
        }

        // 更新产品线指标信息
        if (vo.getNumerator() != null) {
            matchItemInfo.setNumerator(vo.getNumerator());
        }
        if (vo.getDenominator() != null) {
            if (NumberUtils.equalZero(vo.getDenominator())) {
                throw BusinessException.initExc("分母不能为0");
            }
            matchItemInfo.setDenominator(vo.getDenominator());
        }
        if (vo.getNumeratorEx() != null) {
            matchItemInfo.setNumeratorEx(vo.getNumeratorEx());
        }
        if (vo.getDenominatorEx() != null) {
            //if (NumberUtils.equalZero(vo.getDenominatorEx())) {
            //    throw BusinessException.initExc("分母(修正)不能为0");
            //}
            matchItemInfo.setDenominatorEx(vo.getDenominatorEx());
        }
//        if (vo.getCalcValue() != null) {
//            matchItemInfo.setCalcValue(vo.getCalcValue());
//        }
        if (vo.getDeductScore() != null) {
            matchItemInfo.setDeductScore(vo.getDeductScore());
        }
        if (vo.getDeductDesc() != null) {
            matchItemInfo.setDeductDesc(vo.getDeductDesc());
        }
        // 计算指标得分
        calcUserPfmScoreDetail(matchItemInfo);
        // 计算总监得分
        Double score = calcUserPfmScore(itemInfoList);
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.sysUserPfm_itemInfo, itemInfoList));
        updates.add(new UpdataData(DFN.sysUserPfm_score, score));
        sysUserPfmDao.updateById(userPfm.getId(), updates);
        // 计算项目考评的产品线得分
        updatePrjPlScore(userPfm);
    }

    @Override
    public void updatePrdLineYm(ObjectId prjId, String originalYm, String updatedYm) {
        TeSysUserPfm prjPfm = sysUserPfmDao.getYmPrjEval(prjId, originalYm);
        if (prjPfm == null) {
            throw BusinessException.initExc("未找到项目考评信息");
        }
        List<PrjAppraiseScoreDetail> scoreDetailList = prjPfm.getScoreDetail();
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            throw BusinessException.initExc("未找到产品线考评信息");
        }
        if (prjPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(prjPfm.getStatus().getCid())) {
            throw BusinessException.initExc("项目考评已提交，不能再次提交");
        }

        List<UpdataData> updates = new ArrayList<>();
        // 更新项目 考评月份
        updates.add(new UpdataData(DFN.sysUserPfm_ym, updatedYm));
        sysUserPfmDao.updateById(prjPfm.getId(), updates);
        // 更新产品线  考评月份
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), EvaluateConstant.EVALUATE_TYPE_PRJ_PL.getCid()));
        conds.add(new DC_E(DFN.sysUserPfm_prj.dot(DFN.common_cid), prjId));
        conds.add(new DC_E(DFN.sysUserPfm_ym, originalYm));
        sysUserPfmDao.updateByConds(conds, updates);
    }

    @Override
    public void submitPrdLineEval(ObjectId prjId, String ym) {
        TeSysUserPfm prjPfm = sysUserPfmDao.getYmPrjEval(prjId, ym);
        if (prjPfm == null) {
            throw BusinessException.initExc("未找到项目考评信息");
        }
        List<PrjAppraiseScoreDetail> scoreDetailList = prjPfm.getScoreDetail();
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            throw BusinessException.initExc("未找到产品线考评信息");
        }
        if (prjPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(prjPfm.getStatus().getCid())) {
            throw BusinessException.initExc("项目考评已提交，不能再次提交");
        }
        int num = 0;
        double totalScore = 0;
        for (PrjAppraiseScoreDetail scoreDetail : scoreDetailList) {
            if (BooleanUtils.isTrue(scoreDetail.getIsValid()) && (scoreDetail.getUser() == null || scoreDetail.getUser().getUserId() == null)) {
                throw BusinessException.initExc("有产品线总监未设置");
            }
            if(BooleanUtils.isTrue(scoreDetail.getIsValid())){
                num++;
                totalScore += DoubleUtil.getNotNull(scoreDetail.getScore());
            }
        }
        List<UpdataData> updates = new ArrayList<>();
        // 更新项目状态
        updates.add(new UpdataData(DFN.sysUserPfm_status, EvaluateConstant.EVALUATE_STATUS_SUBMIT));
        // 汇总项目质量考评总分：
        updates.add(new UpdataData(DFN.sysUserPfm_score, totalScore / num));
        sysUserPfmDao.updateById(prjPfm.getId(), updates);
        // 更新产品线状态
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), EvaluateConstant.EVALUATE_TYPE_PRJ_PL.getCid()));
        conds.add(new DC_E(DFN.sysUserPfm_prj.dot(DFN.common_cid), prjId));
        conds.add(new DC_E(DFN.sysUserPfm_ym, ym));
        updates.remove(1);
        sysUserPfmDao.updateByConds(conds, updates);
    }

    @Override
    public void reStartPrjAndPlEval(ObjectId prjId, String ym) {
        TeSysUserPfm prjPfm = sysUserPfmDao.getYmPrjEval(prjId, ym);
        if (prjPfm == null) {
            throw BusinessException.initExc("未找到项目考评信息");
        }
        if (prjPfm.getStatus() != null
                && EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid().equals(prjPfm.getStatus().getCid())) {
            throw BusinessException.initExc("项目考评已提交，不能重新出账");
        }
        qualityService.reEvaluate4BscQuality(Collections.singletonList(prjId),ym);
    }

    @Override
    public void startPrjManagerEval(String ym) {
        // 1、校验当月是否有未提交的项目
        if (validateIsHasUnSubmit(ym)) {
            throw BusinessException.initExc("所有项目都提交后才能启动总监考评");
        }
        // 2、根据所有的项目考评的scoreDetail，汇总项目总监
        // 3、记录项目总监的scoreDetail
        // srcDef为项目 def为产品线 type为考评类型：dev test score为产品线的得分
        List<TeSysUserPfm> managerPfmList = sysUserPfmDao.listPrjManagerEvalInfo(ym);
        // 4、设置总监的部门信息
        Set<ObjectId> managerUserIdSet = managerPfmList.stream().map(TeSysUserPfm::getEmp)
                .map(TeUser::getUserId).collect(Collectors.toSet());
        Map<ObjectId, TeIdNameCn> userId2DeptMap = getUser2Dept(managerUserIdSet);
        // 5、初始化itemInfo
        List<PrjAppraiseScoreVo> itemInfoList = initPrjManagerEvalItemInfo();
        Date now = DateUtil.now();
        if (CollectionUtils.isNotEmpty(managerPfmList)) {
            List<BatchCondsUpsert> batchCondsUpsertList = new ArrayList<>();
            for (TeSysUserPfm pfm : managerPfmList) {
                TeIdNameCn deptInfo = userId2DeptMap.get(pfm.getEmp().getUserId());
                // 计算项目质量考评得分
                double detailSumScore = calcManagerEvalPrjScore(pfm);

                List<IDbCondition> conds = new ArrayList<>();
                conds.add(new DC_E(DFN.common_isValid, true));
                conds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), EvaluateConstant.EVALUATE_TYPE_PRJ_MANAGER.getCid()));
                conds.add(new DC_E(DFN.sysUserPfm_ym, ym));
                conds.add(new DC_E(DFN.sysUserPfm_emp.dot(DFN.common_userId), pfm.getEmp().getUserId()));
                List<UpdataData> updates = new ArrayList<>();
                updates.add(new UpdataData(DFN.common_isValid, true));
                updates.add(new UpdataData(DFN.sysUserPfm_addTime, now, false, true));
                updates.add(new UpdataData(DFN.sysUserPfm_type, EvaluateConstant.EVALUATE_TYPE_PRJ_MANAGER));
                updates.add(new UpdataData(DFN.sysUserPfm_ym, ym));
                updates.add(new UpdataData(DFN.sysUserPfm_emp, pfm.getEmp()));
                updates.add(new UpdataData(DFN.sysUserPfm_def, deptInfo));
                updates.add(new UpdataData(DFN.sysUserPfm_itemInfo, itemInfoList));
                updates.add(new UpdataData(DFN.sysUserPfm_scoreDetail, pfm.getScoreDetail()));
                updates.add(new UpdataData(DFN.sysUserPfm_score, detailSumScore));
                batchCondsUpsertList.add(new BatchCondsUpsert(conds, updates));
            }
            sysUserPfmDao.batchSaveOrUpdate(batchCondsUpsertList);
        }
    }

    @Override
    public Double calcPrjManagerEval(PrjManagerEvalUpdateVO vo) {
        if (vo.getPfmId() == null) {
            throw BusinessException.initExc("参数不全");
        }
        TeSysUserPfm managerPfm = sysUserPfmDao.findById(vo.getPfmId());
        if (managerPfm == null || BooleanUtils.isNotTrue(managerPfm.getIsValid())) {
            throw BusinessException.initExc("项目总监考评不存在");
        }
        // 重新计算项目质量考评得分
        double detailSumScore = calcManagerEvalPrjScore(managerPfm);

        List<PrjAppraiseScoreVo> itemInfoList = managerPfm.getItemInfo();
        // 更新产品线指标信息
        double knowledgeScore = calcManagerItemInfoScore(itemInfoList, vo.getKnowledgeScore(),
                EvaluateConstant.EVALUATE_ITEM_KNOWLEDGE_SCORE.getCid());
        double toolScore = calcManagerItemInfoScore(itemInfoList, vo.getToolScore(),
                EvaluateConstant.EVALUATE_ITEM_TOOL_SCORE.getCid());
        double systemScore = calcManagerItemInfoScore(itemInfoList, vo.getSystemScore(),
                EvaluateConstant.EVALUATE_ITEM_SYSTEM_SCORE.getCid());
        double failureDeductScore = calcManagerItemInfoScore(itemInfoList, vo.getFailureDeductScore(),
                EvaluateConstant.EVALUATE_ITEM_FAILURE_DEDUCT_SCORE.getCid());
        double workOrderDeductScore = calcManagerItemInfoScore(itemInfoList, vo.getWorkOrderDeductScore(),
                EvaluateConstant.EVALUATE_ITEM_WORK_ORDER_DEDUCT_SCORE.getCid());
        double toolDeductScore = calcManagerItemInfoScore(itemInfoList, vo.getToolDeductScore(),
                EvaluateConstant.EVALUATE_ITEM_TOOL_DEDUCT_SCORE.getCid());
        double groupDeductScore = calcManagerItemInfoScore(itemInfoList, vo.getGroupDeductScore(),
                EvaluateConstant.EVALUATE_ITEM_GROUP_DEDUCT_SCORE.getCid());
        double processDeductScore = calcManagerItemInfoScore(itemInfoList, vo.getProcessDeductScore(),
                EvaluateConstant.EVALUATE_ITEM_PROCESS_DEDUCT_SCORE.getCid());

        // 计算总监得分
        Double score = detailSumScore + knowledgeScore + toolScore + systemScore
                - failureDeductScore - workOrderDeductScore - toolDeductScore - groupDeductScore - processDeductScore;
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.sysUserPfm_itemInfo, itemInfoList));
        updates.add(new UpdataData(DFN.sysUserPfm_score, score));
        if (StringUtils.isNotEmpty(vo.getAddScoreDesc())) {
            updates.add(new UpdataData(DFN.sysUserPfm_text1, vo.getAddScoreDesc()));
        }
        if (StringUtils.isNotEmpty(vo.getDeductDesc())) {
            updates.add(new UpdataData(DFN.sysUserPfm_text2, vo.getDeductDesc()));
        }
        sysUserPfmDao.updateById(managerPfm.getId(), updates);
        return score;
    }


    private double calcManagerEvalPrjScore(TeSysUserPfm managerPfm) {
        List<PrjAppraiseScoreDetail> scoreDetailList = managerPfm.getScoreDetail();
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return 0d;
        }
        AtomicDouble totalManMonthNum = new AtomicDouble(0d);
        AtomicDouble prjScore = new AtomicDouble(0d);
        scoreDetailList.forEach(scoreDetail -> {
            totalManMonthNum.addAndGet(scoreDetail.getNum());
            prjScore.addAndGet(DoubleUtil.getNotNull(scoreDetail.getScore()) * DoubleUtil.getNotNull(scoreDetail.getNum()));
        });
        return prjScore.get() / totalManMonthNum.get();
    }

    private double getManagerEvalScore(List<PrjAppraiseScoreVo> itemInfoList, ObjectId itemId) {
        if (CollectionUtils.isEmpty(itemInfoList) || itemId == null) {
            return 0d;
        }
        for (PrjAppraiseScoreVo vo : itemInfoList) {
            if (itemId.equals(vo.getItem().getCid())) {
                return BigDecimalUtils.getDoubleHalfNumNaN(vo.getScore());
            }
        }
        return 0d;
    }

    private double calcManagerItemInfoScore(List<PrjAppraiseScoreVo> itemInfoList, Double score, ObjectId itemId) {
        PrjAppraiseScoreVo matchItemInfo = null;
        for (PrjAppraiseScoreVo vo : itemInfoList) {
            if (itemId.equals(vo.getItem().getCid())) {
                matchItemInfo = vo;
                break;
            }
        }
        if (matchItemInfo == null) {
            return 0d;
        }
        if (score != null) {
            matchItemInfo.setScore(score);
        }
        return matchItemInfo.getScore();
    }

    private List<PrjAppraiseScoreVo> initPrjManagerEvalItemInfo() {
        List<PrjAppraiseScoreVo> list = new ArrayList<>();
        for (TeIdNameCn managerItemInfo : EvaluateConstant.EVALUATE_PRJ_MANAGER_ITEM_INFO_LIST) {
            PrjAppraiseScoreVo itemInfo = new PrjAppraiseScoreVo();
            itemInfo.setItem(managerItemInfo);
            itemInfo.setScore(0d);
            list.add(itemInfo);
        }
        return list;
    }

    private boolean validateIsHasUnSubmit(String ym) {
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysUserPfm_type.dot(DFN.common_cid), EvaluateConstant.EVALUATE_TYPE_PRJ.getCid()));
        conds.add(new DC_E(DFN.sysUserPfm_ym, ym));
        conds.add(new DC_E(DFN.sysUserPfm_status.dot(DFN.common_cid), EvaluateConstant.EVALUATE_STATUS_SUBMIT.getCid(), true));
        return sysUserPfmDao.countByConds(conds) > 0;
    }

    private void updatePrjPlManager(TeSysUserPfm plPfm, TeSysUser managerUser) {
        if (plPfm.getPrj() == null || plPfm.getPrj().getCid() == null
                || plPfm.getDef() == null || plPfm.getDef().getCid() == null) {
            return;
        }
        TeSysUserPfm prjPfm = sysUserPfmDao.getYmPrjEval(plPfm.getPrj().getCid(), plPfm.getYm());
        if (prjPfm == null) {
            return;
        }
        List<PrjAppraiseScoreDetail> scoreDetailList = prjPfm.getScoreDetail();
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return;
        }
        // 同产品线、同评估类型
        PrjAppraiseScoreDetail matchScoreDetail = scoreDetailList.stream()
                .filter(scoreDetail -> (plPfm.getDef().getCid().equals(scoreDetail.getDef().getCid())
                        && plPfm.getText1().equals(scoreDetail.getType())))
                .findFirst().orElse(null);
        if (matchScoreDetail == null) {
            return;
        }
        // 更新总监和评分
        if (managerUser == null) {
            matchScoreDetail.setUser(null);
        } else {
            matchScoreDetail.setUser(new TeUser(managerUser.getId(), managerUser.getLoginName(), managerUser.getUserName(), managerUser.getJobCode()));
        }
        matchScoreDetail.setScore(plPfm.getScore());
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.sysUserPfm_scoreDetail, scoreDetailList));
        sysUserPfmDao.updateById(prjPfm.getId(), updates);
    }

    private void updatePrjPlScore(TeSysUserPfm plPfm) {
        if (plPfm.getPrj() == null || plPfm.getPrj().getCid() == null
                || plPfm.getDef() == null || plPfm.getDef().getCid() == null) {
            return;
        }
        TeSysUserPfm prjPfm = sysUserPfmDao.getYmPrjEval(plPfm.getPrj().getCid(), plPfm.getYm());
        if (prjPfm == null) {
            return;
        }
        List<PrjAppraiseScoreDetail> scoreDetailList = prjPfm.getScoreDetail();
        if (CollectionUtils.isEmpty(scoreDetailList)) {
            return;
        }
        // 同产品线、同评估类型
        PrjAppraiseScoreDetail matchScoreDetail = scoreDetailList.stream()
                .filter(scoreDetail -> (plPfm.getDef().getCid().equals(scoreDetail.getDef().getCid())
                        && plPfm.getText1().equals(scoreDetail.getType())))
                .findFirst().orElse(null);
        if (matchScoreDetail == null) {
            return;
        }
        // 更新总监和评分
        matchScoreDetail.setScore(plPfm.getScore());
        List<UpdataData> updates = new ArrayList<>();
        updates.add(new UpdataData(DFN.sysUserPfm_scoreDetail, scoreDetailList));
        sysUserPfmDao.updateById(prjPfm.getId(), updates);
    }

    private Double calcUserPfmScore(List<PrjAppraiseScoreVo> itemInfoList) {
        double score = 0d;
        if (CollectionUtils.isEmpty(itemInfoList)) {
            return score;
        }
        for (PrjAppraiseScoreVo itemInfo : itemInfoList) {
            score += DoubleUtil.getNotNull(itemInfo.getScore());
        }
        return score;
    }

    private void calcUserPfmScoreDetail(PrjAppraiseScoreVo matchItemInfo) {
        //if (NumberUtils.equalZero(matchItemInfo.getDenominatorEx())) {
        //    throw BusinessException.initExc(String.format("%s分母修正不能为0", matchItemInfo.getItem().getName()));
        //}
        // 代码违规密度 需要根据分子分母 计算量化数据
        if (EvaluateConstant.DEV_CODE_VIOLATION_DENSITY.getCid().equals(matchItemInfo.getItem().getCid())) {
            if (NumberUtils.equalZero(matchItemInfo.getDenominator())) {
                throw BusinessException.initExc(String.format("%s分母不能为0", matchItemInfo.getItem().getName()));
            }
            if(matchItemInfo.getNumerator() != null && matchItemInfo.getDenominator() != null){
                matchItemInfo.setCalcValue(DoubleUtil.getNotNull(matchItemInfo.getNumerator())*1000 / DoubleUtil.getNotNull(matchItemInfo.getDenominator()));
            }
        }
        if(matchItemInfo.getNumeratorEx() != null && matchItemInfo.getDenominatorEx() != null){
            if (EvaluateConstant.DEV_CODE_VIOLATION_DENSITY.getCid().equals(matchItemInfo.getItem().getCid())) {
                matchItemInfo.setCalcValueEx(DoubleUtil.getNotNull(matchItemInfo.getNumeratorEx())*1000 / DoubleUtil.getNotNull(matchItemInfo.getDenominatorEx()));
            }else{
                matchItemInfo.setCalcValueEx(DoubleUtil.getNotNull(matchItemInfo.getNumeratorEx()) / DoubleUtil.getNotNull(matchItemInfo.getDenominatorEx()));
            }
            matchItemInfo.setCalcScore(getCalcScore(matchItemInfo.getItem().getCid(), matchItemInfo.getCalcValueEx(),matchItemInfo.getCalcScore()));
        }

        if (matchItemInfo.getCalcScore() == null) {
            return;
        }

        Double score = matchItemInfo.getCalcScore() - DoubleUtil.getNotNull(matchItemInfo.getDeductScore());
        matchItemInfo.setScore(Math.max(score, 0d));

    }

    private Double getCalcScore(ObjectId itemId, Double calcVaule, Double calcScore) {
        double score = calcScore;
        // 代码违规密度
        if (EvaluateConstant.DEV_CODE_VIOLATION_DENSITY.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 20d;
            }
            if (calcVaule >= 2.0d) {
                score = 0d;
            } else if (calcVaule >= 1.5d) {
                score = 4d;
            } else if (calcVaule >= 1.0d) {
                score = 8d;
            } else if (calcVaule >= 0.5d) {
                score = 12d;
            } else if (calcVaule >= 0.35d) {
                score = 16d;
            } else {
                score = 20d;
            }
        }
        // 严重缺陷占比
        if (EvaluateConstant.DEV_SERIOUS_DEFECTS_PROPORTION.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 20d;
            }
            if (calcVaule >= 0 && calcVaule < 0.05) {
                score = 20d;
            } else if (calcVaule >= 0.05 && calcVaule < 0.15) {
                score = 16d;
            } else if (calcVaule >= 0.15 && calcVaule < 0.3) {
                score = 12d;
            } else if (calcVaule >= 0.3 && calcVaule < 0.4) {
                score = 8d;
            } else if (calcVaule >= 0.4 && calcVaule < 0.5) {
                score = 4d;
            } else {
                score = 0d;
            }
        }
        // 缺陷回退率
        if (EvaluateConstant.DEV_DEFECT_REGRESS_RATE.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 10d;
            }
            if (calcVaule >= 0 && calcVaule < 0.01) {
                score = 10d;
            } else if (calcVaule >= 0.01 && calcVaule < 0.05) {
                score = 8d;
            } else if (calcVaule >= 0.05 && calcVaule < 0.15) {
                score = 6d;
            } else if (calcVaule >= 0.15 && calcVaule < 0.2) {
                score = 4d;
            } else if (calcVaule >= 0.2 && calcVaule < 0.25) {
                score = 2d;
            } else {
                score = 0d;
            }
        }
        // 缺陷密度
        if (EvaluateConstant.DEV_DEFECT_DENSITY.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 20d;
            }
            if (calcVaule >= 0 && calcVaule < 2) {
                score = 20d;
            } else if (calcVaule >= 2 && calcVaule < 10) {
                score = 16d;
            } else if (calcVaule >= 10 && calcVaule < 20) {
                score = 12d;
            } else if (calcVaule >= 20 && calcVaule < 25) {
                score = 8d;
            } else if (calcVaule >= 25 && calcVaule < 30) {
                score = 4d;
            } else {
                score = 0d;
            }
        }
        // 开发自测覆盖率
        if (EvaluateConstant.DEV_SELF_TEST_COVERAGE.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 10d;
            }
            if (calcVaule >= 0 && calcVaule < 0.2) {
                score = 0d;
            } else if (calcVaule >= 0.2 && calcVaule < 0.4) {
                score = 2d;
            } else if (calcVaule >= 0.4 && calcVaule < 0.6) {
                score = 4d;
            } else if (calcVaule >= 0.6 && calcVaule < 0.8) {
                score = 6d;
            } else if (calcVaule >= 0.8 && calcVaule < 1) {
                score = 8d;
            } else {
                score = 10d;
            }
        }
        // 开发任务及时率
        if (EvaluateConstant.DEV_task_timely.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 5d;
            }
            if (calcVaule >= 0 && calcVaule < 0.5) {
                score = 0d;
            } else if (calcVaule >= 0.5 && calcVaule < 0.6) {
                score = 1d;
            } else if (calcVaule >= 0.6 && calcVaule < 0.7) {
                score = 2d;
            } else if (calcVaule >= 0.7 && calcVaule < 0.8) {
                score = 3d;
            } else if (calcVaule >= 0.8 && calcVaule < 0.9) {
                score = 4d;
            } else {
                score = 5d;
            }
        }
        // 缺陷检出率
        if (EvaluateConstant.TEST_DEFECT_DETECTION_RATE.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 30d;
            }
            if (calcVaule >= 0 && calcVaule < 5) {
                score = 0d;
            } else if (calcVaule >= 5 && calcVaule < 10) {
                score = 6d;
            } else if (calcVaule >= 10 && calcVaule < 20) {
                score = 12d;
            } else if (calcVaule >= 20 && calcVaule < 35) {
                score = 18d;
            } else if (calcVaule >= 35 && calcVaule < 50) {
                score = 24d;
            } else {
                score = 30d;
            }
        }
        // 无效缺陷率
        if (EvaluateConstant.TEST_DEFECT_INVALIDATION_RATE.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 10d;
            }
            if (calcVaule >= 0 && calcVaule < 0.03) {
                score = 10d;
            } else if (calcVaule >= 0.03 && calcVaule < 0.06) {
                score = 8d;
            } else if (calcVaule >= 0.06 && calcVaule < 0.1) {
                score = 6d;
            } else if (calcVaule >= 0.1 && calcVaule < 0.12) {
                score = 4d;
            } else if (calcVaule >= 0.12 && calcVaule < 0.15) {
                score = 2d;
            } else {
                score = 0d;
            }
        }
        // 缺陷逃逸率
        if (EvaluateConstant.TEST_DEFECT_ESCAPE_RATE.getCid().equals(itemId)) {
            if (calcVaule == null) {
                return null;
            }
            if (Double.isNaN(calcVaule)) {
                return 50d;
            }
            if (calcVaule >= 0 && calcVaule < 0.02) {
                score = 50d;
            } else if (calcVaule >= 0.02 && calcVaule < 0.06) {
                score = 40d;
            } else if (calcVaule >= 0.06 && calcVaule < 0.1) {
                score = 30d;
            } else if (calcVaule >= 0.1 && calcVaule < 0.12) {
                score = 20d;
            } else if (calcVaule >= 0.12 && calcVaule < 0.14) {
                score = 10d;
            } else {
                score = 0d;
            }
        }
        return  score;
    }


    private String calcPercent(Double calcValue) {
        if (calcValue == null) {
            return null;
        }
        if(Double.isNaN(calcValue)){
            return calcValue +  EvaluateConstant.FLAG_PERCENT;
        }
        return BigDecimalUtils.getDoubleHalfNum(calcValue * 100, 1) + EvaluateConstant.FLAG_PERCENT;
    }

    private Map<ObjectId, TeIdNameCn> getUser2Dept(Set<ObjectId> managerUserIdSet) {
        List<TeSysUser> userList = sysUserDao.findUserByIdList(new ArrayList<>(managerUserIdSet));
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyMap();
        }
        List<String> ccIdList = new ArrayList<>();
        for (TeSysUser user : userList) {
            ccIdList.add(user.getCcId());
        }
        if (CollectionUtils.isEmpty(ccIdList)) {
            return Collections.emptyMap();
        }
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.common_isValid, true));
        conds.add(new DC_E(DFN.sysDef__defType.dot(DFN.sysDef__defTypeCodeName), EvaluateConstant.EIP_ORG_STRUCTURE_CODE));
        conds.add(new DC_E(DFN.sysDef__codeName, EvaluateConstant.EIP_ORG_STRUCTURE_LEVEL_3));
        List<IDbCondition> orConds = new ArrayList<>();
        for (String ccId : ccIdList) {
            orConds.add(new DC_R(DFN.sysDef__value, "," + ccId + ","));
        }
        conds.add(new DC_OR(orConds));
        List<DbFieldName> fieldNames = new ArrayList<>();
        fieldNames.add(DFN.common__id);
        fieldNames.add(DFN.sysDef__codeName);
        fieldNames.add(DFN.sysDef__defName);
        fieldNames.add(DFN.sysDef__value);
        List<TeSysDef> deptList = sysDefDao.findByFieldAndConds(conds, fieldNames);
        if (CollectionUtils.isEmpty(deptList)) {
            return Collections.emptyMap();
        }
        Map<String, ObjectId> ccId2DeptIdMap = new HashMap<>();
        Map<ObjectId, TeIdNameCn> deptId2InfoMap = new HashMap<>();
        for (TeSysDef dept : deptList) {
            List<String> ccIds = StringUtil.transIds2List(dept.getValue(), ",", String.class);
            if (CollectionUtils.isEmpty(ccIds)) {
                continue;
            }
            for (String ccId : ccIds) {
                if (StringUtils.isEmpty(ccId)) {
                    continue;
                }
                ccId2DeptIdMap.put(ccId, dept.getId());
            }
            deptId2InfoMap.put(dept.getId(), new TeIdNameCn(dept.getId(), dept.getDefName(), dept.getCodeName()));
        }
        Map<ObjectId, TeIdNameCn> userId2DeptMap = new HashMap<>();
        for (TeSysUser user : userList) {
            if (StringUtils.isEmpty(user.getCcId())) {
                continue;
            }
            ObjectId deptId = ccId2DeptIdMap.get(user.getCcId());
            if (deptId == null) {
                continue;
            }
            userId2DeptMap.put(user.getId(), deptId2InfoMap.get(deptId));
        }
        return userId2DeptMap;
    }
}
