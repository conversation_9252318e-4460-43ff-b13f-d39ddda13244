package com.linkus.evaluate.model.vo;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;
import org.bson.types.ObjectId;

import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DevTaskEvalDetailQueryVO {

    /**
     * 行数据
     * 中心架构
     */
    String centerArchitecture;

    /**
     * 行数据
     * 一级部门
     */
    String primaryDepartment;

    /**
     * 行数据
     * 二级部门
     */
    String secondaryDepartment;

    /**
     * 行数据
     * 项目编码/名称
     */
    String codeName;

    /**
     * 行数据
     * flowTask表id
     */
    List<ObjectId> flowTaskIds;

    /**
     * 自测/全部  true为自测 false为全部
     */
    Boolean completeFlag;

    /**
     * 页码
     */
    Integer pageNum;

    /**
     * 页数
     */
    Integer pageSize;

    /**
     * 是否查询全部
     */
    Boolean allFlag;

    /**
     * 查询全部时传值查部门
     */
    List<ObjectId> deptIds;
}
