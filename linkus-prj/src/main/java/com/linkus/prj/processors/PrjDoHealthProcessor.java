package com.linkus.prj.processors;

import com.linkus.prj.task.TaskScheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tech.powerjob.worker.core.processor.ProcessResult;
import tech.powerjob.worker.core.processor.TaskContext;
import tech.powerjob.worker.core.processor.sdk.BasicProcessor;
import tech.powerjob.worker.log.OmsLogger;

/**
 * cron = "0 0 7 * * ?"
 *
 * <AUTHOR>
 */
@Component
public class PrjDoHealthProcessor implements BasicProcessor {

    @Autowired
    private TaskScheduler taskScheduler;

    @Override
    public ProcessResult process(TaskContext context) throws Exception {

        OmsLogger omsLogger = context.getOmsLogger();
        try {
            omsLogger.info("start");
            taskScheduler.prjDoHealthJob();
            omsLogger.info("end");
            return new ProcessResult(true, "success");
        } catch (Exception ex) {
            omsLogger.error("exception", ex);
            return new ProcessResult(false, "exception:" + ex.getMessage());
        }

    }
}
