package com.linkus.prj.ctrl;

import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.prj.model.vo.PrjDvtMgtHandlerServiceEnum;
import com.linkus.prj.model.vo.PrjDvtMgtUpdateVo;
import com.linkus.prj.service.IPrjColorCardDvtService;
import com.linkus.prj.service.IPrjDvtMgtMergeService;
import com.linkus.prj.service.IPrjDvtMgtService;
import com.linkus.prj.service.IPrjDvtMgtSupportService;
import com.linkus.prj.vo.PrjDvtMgtMergeVo;
import com.linkus.prj.vo.PrjDvtMgtVo;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/prjDvtMgtCtrl")
public class PrjDvtMgtCtrl extends CommonController {
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private Map<String, IPrjDvtMgtSupportService> prjDvtMgtServiceMap;

    @Resource(name = "PrjDvtMgtServiceImpl")
    private IPrjDvtMgtService prjDvtMgtService;

    @Autowired
    private IPrjColorCardDvtService prjColorCardDvtService;
    @Autowired
    private IPrjDvtMgtMergeService prjDvtMgtMergeService;

    /**
     * <AUTHOR> @Description 查询项目进度延期数据
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/queryPrjDvtMgtData")
    @ResponseBody
    public CommonResult<Object> queryPrjDvtMgtData(@RequestBody PrjDvtMgtVo prjDvtMgtVo){
        //获取登陆人
        TeSysUser loginUser = getLoginUser();
        if (StringUtil.isNull(prjDvtMgtVo.getYm())){
            throw BusinessException.initExc("请选择月份！");
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDvtMgtVo.getTypeId());
        return CommonResult.success(handlerService.queryPrjDvtMgtData(prjDvtMgtVo, loginUser));
    }
    /**
     * <AUTHOR> @Description 导出项目进度延期数据
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/exportPrjDvtMgtData")
    @ResponseBody
    public void exportPrjDvtMgtData(@ModelAttribute PrjDvtMgtVo prjDvtMgtVo) throws IOException {
        //获取登陆人
        TeSysUser loginUser = getLoginUser();
        if (StringUtil.isNull(prjDvtMgtVo.getYm())){
            throw BusinessException.initExc("请选择月份！");
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDvtMgtVo.getTypeId());
        handlerService.exportPrjDvtMgtData(getResponse(), prjDvtMgtVo, loginUser);
    }


    /**
     * <AUTHOR> @Description 初始化项目进度延期数据
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/initPrjDvtMgtData")
    @ResponseBody
    public CommonResult<Void> initPrjDvtMgtData(
            @RequestParam(value = "sbuId",required = false)String sbuId,
            @RequestParam(value = "ym",required = false)String ym,
            @RequestParam(value = "typeId")ObjectId typeId
    ){
        TeSysUser loginUser = getLoginUser();
        if (StringUtil.isNull(sbuId)){
            sbuId = loginUser.getSbuId();
        }
        if (StringUtil.isNull(sbuId)){
            throw BusinessException.initExc("BU为空");
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(typeId);
        handlerService.initPrjDvtMgt(sbuId, ym, loginUser);
        return CommonResult.success();
    }

    @PostMapping("/initPrjDvtMgtByProgress")
    @ResponseBody
    public CommonResult<Void> initPrjDvtMgtByProgress(
            @RequestParam(value = "sbuId",required = false)String sbuId,
            @RequestParam(value = "ym",required = false)String ym,
            @RequestParam(value = "typeId") ObjectId typeId,
            @RequestParam(value = "progressDateList[]",required = false) List<String> progressDateList
    ){
        TeSysUser loginUser = getLoginUser();
        if (StringUtil.isNull(sbuId)){
            sbuId = loginUser.getSbuId();
        }
        if (StringUtil.isNull(sbuId)){
            throw BusinessException.initExc("BU为空");
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(typeId);
        handlerService.initPrjDvtMgtByProgress(sbuId, ym, loginUser, progressDateList);
        return CommonResult.success();
    }

    /**
     * <AUTHOR> @Description 进度延期反馈邮件
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/sendPrjDvtMgtDataMail")
    @ResponseBody
    public CommonResult<Void> sendPrjDvtMgtDataMail(@RequestParam(value = "devMgtIds[]") List<ObjectId> devMgtIds,
                                                  @RequestParam(value = "prjDevMgtTypeId") ObjectId prjDevMgtTypeId){
        TeSysUser loginUser = getLoginUser();
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDevMgtTypeId);
        handlerService.sendPrjDvtMgtDataMail(devMgtIds, prjDevMgtTypeId, loginUser);
        return CommonResult.success();
    }

    @PostMapping("/sendPrjJwCostOverspendMail")
    @ResponseBody
    public CommonResult<Void> sendPrjJwCostOverspendMail(@RequestParam(value = "devMgtIds[]") List<ObjectId> devMgtIds,
                                                    @RequestParam(value = "prjDevMgtTypeId") ObjectId prjDevMgtTypeId){
        TeSysUser loginUser = getLoginUser();
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDevMgtTypeId);
        handlerService.sendPrjJwCostOverspendMail(devMgtIds, prjDevMgtTypeId, loginUser);
        return CommonResult.success();
    }
    /**
     * <AUTHOR> @Description 删除项目进度延期数据
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/deletePrjDvtMgt")
    @ResponseBody
    public CommonResult<String> deletePrjDvtMgt(
            @RequestParam(value = "devMgtIds[]") List<ObjectId> devMgtIds,
            @RequestParam(value = "prjDevMgtTypeId") ObjectId prjDevMgtTypeId,
            @RequestParam(value = "invalidDesc",required = true)String invalidDesc
    ){
        TeSysUser loginUser = getLoginUser();
        if (loginUser == null){
            throw BusinessException.initExc("当前登陆人不存在");
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDevMgtTypeId);
        handlerService.deletePrjDvtMgt(devMgtIds,loginUser,invalidDesc);
        return CommonResult.success("删除成功");
    }

    /**
     * <AUTHOR> @Description 填写进度延期说明，备注，原因
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @PostMapping("/updatePrjDvtMgt")
    @ResponseBody
    public CommonResult<String> updatePrjDvtMgt(@RequestParam(value = "id")ObjectId id,
                                                             @RequestParam(value = "type")String type,
                                                             @RequestParam(value = "rectifyAction",required = false)String rectifyAction,
                                                             @RequestParam(value = "rectifyEndDate",required = false)String rectifyEndDate,
                                                             @RequestParam(value = "updata",required = false)String updata,
                                                             @RequestParam(value = "causeTypeId",required = false)ObjectId causeTypeId,
                                                             @RequestParam(value = "prjDevMgtTypeId") ObjectId prjDevMgtTypeId){
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDevMgtTypeId);
        handlerService.updatePrjDvtMgt(id,type,updata,causeTypeId,rectifyAction,rectifyEndDate);
        return CommonResult.success("修改成功");
    }

    @PostMapping("/updatePrjDvtMgtNew")
    @ResponseBody
    public CommonResult<String> updatePrjDvtMgt(@RequestBody PrjDvtMgtUpdateVo prjDvtMgtUpdateVo){
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDvtMgtUpdateVo.getTypeId());
        handlerService.updatePrjDvtMgt(prjDvtMgtUpdateVo);
        return CommonResult.success("修改成功");
    }
    /**
     * <AUTHOR> @Description 统计未填写延期说明的个数
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @GetMapping("/countNotFillIn")
    @ResponseBody
    public CommonResult<Map> countNotFillIn(@RequestParam(value = "ym") String ym){
        TeSysUser loginUser = getLoginUser();
        return CommonResult.success(prjDvtMgtService.countNotFillIn(ym,loginUser));
    }

    @GetMapping("/countNotFillInByTypeId")
    @ResponseBody
    public CommonResult<Long> countNotFillInByTypeId(@RequestParam(value = "prjDevMgtTypeId") ObjectId prjDevMgtTypeId,
                                                     @RequestParam(value = "ym") String ym){
        TeSysUser loginUser = getLoginUser();
        IPrjDvtMgtSupportService handlerService = getHandlerService(prjDevMgtTypeId);
        return CommonResult.success(handlerService.countNotFillInByTypeId(ym, loginUser));
    }

    /**
     * <AUTHOR> @Description 查询当前登录人是项目经理或项目管理员的项目（prjId）
     * @Date 9:43 2023/8/28
     * @Param
     * @return
     **/
    @GetMapping("/listPmOrAdminPrjId")
    @ResponseBody
    public CommonResult<List<ObjectId>> listPmOrAdminPrjId(){
        TeSysUser loginUser = getLoginUser();
        return CommonResult.success(prjDvtMgtService.listPmOrAdminPrjId(loginUser));
    }

    @GetMapping("/getLastMonthDelayDesc")
    @ResponseBody
    public CommonResult<Object> getLastMonthDelayDesc(@RequestParam(value = "ym")String ym,
                                                      @RequestParam(value = "prjId")ObjectId prjId,
                                                      @RequestParam(value = "typeId")ObjectId typeId,
                                                      @RequestParam(value = "rcdId", required = false) ObjectId rcdId){
        IPrjDvtMgtSupportService handlerService = getHandlerService(typeId);
        return CommonResult.success(handlerService.getLastMonthDesc(ym, prjId, typeId, rcdId));
    }

    /**
     * 导出全量(实时查询)
     * @param ym
     * @param typeId
     * @param sbuId
     * @return
     */
    @GetMapping("/exportAllPrjDeviation")
    @ResponseBody
    public CommonResult<Void> exportAllPrjDeviation(@RequestParam(value = "ym")String ym,
                                                      @RequestParam(value = "typeId")ObjectId typeId,
                                                      @RequestParam(value = "sbuId", required = false) String sbuId) throws IOException {
        TeSysUser loginUser = getLoginUser();
        if(StringUtils.isEmpty(sbuId)){
            sbuId = loginUser.getSbuId();
        }
        IPrjDvtMgtSupportService handlerService = getHandlerService(typeId);
        handlerService.exportAllPrjDeviation(getResponse(), ym, sbuId, typeId);
        return CommonResult.success();
    }

    private IPrjDvtMgtSupportService getHandlerService(ObjectId typeId){
        String handlerService = PrjDvtMgtHandlerServiceEnum.getHandlerService(typeId);
        if(handlerService == null){
            throw BusinessException.initExc("该异常偏差管理类型功能未开发");
        }
        IPrjDvtMgtSupportService supportService = prjDvtMgtServiceMap.get(handlerService);
        if(supportService == null){
            throw BusinessException.initExc("该异常偏差管理类型功能未开发");
        }
        return supportService;
    }

    private TeSysUser getLoginUser(){
        String loginName = getLoginName();
        if (StringUtil.isNull(loginName)){
            throw BusinessException.initExc("当前登陆人为空");
        }
        TeSysUser loginUser = sysUserService.queryByLoginName(loginName);
        if (loginUser == null){
            throw BusinessException.initExc("当前登陆人为空");
        }
        return loginUser;
    }

    /**
     * <AUTHOR> @Description 偏差管理统计
     * @Date 15:23 2024/1/19
     * @Param
     * @return
     **/
    @GetMapping("/devManageStatis")
    @ResponseBody
    public CommonResult<Object> devManageStatis(@RequestParam(value = "ym",required = true)String ym){
       return CommonResult.success(prjDvtMgtService.devManageStatis(ym));
    }

    /**
     * <AUTHOR> @Description 获取以删除数据
     * @Date 11:08 2024/1/22
     * @Param
     * @return
     **/
    @PostMapping("/getIsDeleteDatasInfo")
    @ResponseBody
    public CommonResult<Object> getIsDeleteDatasInfo(@RequestBody List<ObjectId> deleteIds){
        return CommonResult.success(prjDvtMgtService.getIsDeleteDatasInfo(deleteIds));
    }

    @PostMapping("/sendPrjDelaySignMail")
    @ResponseBody
    public CommonResult<Void> sendPrjDelaySignMail(
            @RequestParam(value = "sbuId",required = false)String sbuId,
            @RequestParam(value = "devMgtIds[]") List<ObjectId> devMgtIds
    ){
        TeSysUser loginUser = getLoginUser();
        prjDvtMgtService.sendPrjDelaySignMail(sbuId,devMgtIds);
        return CommonResult.success();
    }

    @PostMapping("/sendPrjColorCardAlertMail")
    @ResponseBody
    public CommonResult<Void> sendPrjColorCardAlertMail(@RequestParam(value = "sbuId",required = false)String sbuId){
        prjColorCardDvtService.sendPrjColorCardAlertMail(sbuId);
        return CommonResult.success();
    }
    /**
     * 查询异常偏差合并数据
     */
    @PostMapping("/getPrjDvtMgtMergeData")
    @ResponseBody
    public CommonResult<Object> getPrjDvtMgtMergeData(@RequestBody PrjDvtMgtMergeVo vo){
        TeSysUser loginUser = getLoginUser();
        if (loginUser == null){
            throw BusinessException.initExc("当前登陆人为空");
        }
        if (StringUtil.isNull(vo.getYm())){
            throw BusinessException.initExc("请选择月份");
        }
        PageBean pageBean = prjDvtMgtMergeService.getPrjDvtMgtMergeData(vo,loginUser);
        return CommonResult.success(pageBean);
    }

    @PostMapping("/exportPrjDvtMgtMergeData")
    @ResponseBody
    public void exportPrjDvtMgtMergeData(@ModelAttribute PrjDvtMgtMergeVo vo) throws IOException {
        TeSysUser loginUser = getLoginUser();
        if (loginUser == null){
            throw BusinessException.initExc("当前登陆人为空");
        }
        if (StringUtil.isNull(vo.getYm())){
            throw BusinessException.initExc("请选择月份");
        }
        prjDvtMgtMergeService.exportPrjDvtMgtMergeData(vo,loginUser,getResponse());
    }

    @PostMapping("/sendPrjDvtMgtMergeDataMail")
    @ResponseBody
    public CommonResult<Object> sendPrjDvtMgtMergeDataMail(@RequestBody PrjDvtMgtVo vo){
        if (StringUtil.isNull(vo.getYm())){
            throw BusinessException.initExc("请选择月份");
        }
        if (CollectionUtils.isEmpty(vo.getPrjIds())){
            throw BusinessException.initExc("请选择数据");
        }
        prjDvtMgtMergeService.sendPrjDvtMgtMergeDataMail(vo);
        return CommonResult.success("发送成功");
    }

    /**
     * 角标展示未反馈/分析的异常偏差数量
     */
    @GetMapping("/countPrjDvtMgtSubscript")
    @ResponseBody
    public CommonResult<Object> countPrjDvtMgtSubscript(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.countPrjDvtMgtSubscript(prjId,ym));
    }
    /**
     * 签约进度：取项目集下所有子项目信息，及其对应管理月份下“签约延期”数据
     */
    @GetMapping("/getDelaySignDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getDelaySignDvtMgtMergeData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getDelaySignDvtMgtDetailData(prjId,ym));
    }
    /**
     * 总体进度：取项目集最新版本过程管控计划下，所有目标里程碑任务，及其对应管理月份下“进度延期”数据
     */
    @GetMapping("/getProgressDelayDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getProgressDelayDvtMgtDetailData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getProgressDelayDvtMgtDetailData(prjId,ym));
    }
    /**
     * 里程碑进度：取项目集下所有子项目里程碑信息，及其对应管理月份下“色牌跟踪”数据
     */
    @GetMapping("/getPrjMstDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getPrjMstDvtMgtDetailData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getPrjMstDvtMgtDetailData(prjId,ym));
    }
    /**
     * 累计成本：取项目集截止管理月累计、人月均成本（参考 异常偏差-成本偏差，反馈填写）
     */
    @GetMapping("/getPrjCostDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getPrjCostDvtMgtDetailData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getPrjCostDvtMgtDetailData(prjId,ym));
    }
    /**
     * 效能：取项目集最新版本基准及经营计划数据
     */
    @GetMapping("/getEffectDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getEffectDvtMgtDetailData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getEffectDvtMgtDetailData(prjId,ym));
    }

    /**
     * 基准VS实施计划：取项目集最新版本基准及经营计划数据
     */
    @GetMapping("/getImplPlanDvtMgtDetailData")
    @ResponseBody
    public CommonResult<Object> getImplPlanDvtMgtDetailData(
            @RequestParam(value = "prjId",required = true)ObjectId prjId,
            @RequestParam(value = "ym",required = true)String ym
    ){
        return CommonResult.success(prjDvtMgtMergeService.getImplPlanDvtMgtDetailData(prjId,ym));
    }
}
