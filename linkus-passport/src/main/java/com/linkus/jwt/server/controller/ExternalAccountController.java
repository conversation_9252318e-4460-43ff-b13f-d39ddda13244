package com.linkus.jwt.server.controller;

import com.linkus.base.aop.LogAnnotation;
import com.linkus.base.aop.OperationTypeEnum;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.base.response.error.ErrorSsoEm;
import com.linkus.base.shiro.JwtUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.base.web.CookieUtil;
import com.linkus.jwt.server.model.JwtToken;
import com.linkus.jwt.server.model.LoginBO;
import com.linkus.jwt.server.model.LoginRequest;
import com.linkus.jwt.server.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 第三方用户授权
 */
@Slf4j
@RefreshScope
@RestController
@RequestMapping("/external")
public class ExternalAccountController extends CommonController {

    @Value("${spring.jwt.shiro.server.expireDate}")
    private String expireDate;

    @Autowired
    private LoginService loginService;

    /**
     * 自定义用户登录验证
     *
     * @param httpServletRequest
     * @param httpServletResponse
     * @param loginRequest
     * @return
     */
    @PostMapping(value = "/login")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "自定义用户登录验证", operation = OperationTypeEnum.LOGIN)
    public CommonResult<JwtToken> login(HttpServletRequest httpServletRequest,
                                        HttpServletResponse httpServletResponse,
                                        @RequestBody LoginRequest loginRequest) {
        if(StringUtil.isNull(loginRequest.getUsername())) {
            throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
        }
        if(StringUtil.isNull(loginRequest.getPassword())) {
            throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
        }

        LoginBO bo = LoginBO.builder(loginRequest)
                .rsa(true)
                .checkPassword(true)
                .checkCaptcha(true)
                .customUserCanLogin(true);

        return loginCheck(httpServletRequest, httpServletResponse, bo);
    }

    /**
     * 自定义用户短信登录验证
     *
     * @param httpServletRequest
     * @param httpServletResponse
     * @param loginRequest
     * @return
     */
    @PostMapping(value = "/sms/login")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "自定义用户短信登录验证", operation = OperationTypeEnum.LOGIN)
    public CommonResult<JwtToken> smsLogin(HttpServletRequest httpServletRequest,
                                           HttpServletResponse httpServletResponse,
                                           @RequestBody LoginRequest loginRequest) {

        if(StringUtil.isNull(loginRequest.getPhone())) {
            throw BusinessException.initExc(ErrorSsoEm.SMS_CAPTCHA_EMPTY);
        }
        if(StringUtil.isNull(loginRequest.getSmsCaptcha())) {
            throw BusinessException.initExc(ErrorSsoEm.SMS_CAPTCHA_EMPTY);
        }
        LoginBO bo = LoginBO.builder(loginRequest)
                .rsa(true)
                .checkSmsCaptcha(true)
                .customUserCanLogin(true);

        return loginCheck(httpServletRequest, httpServletResponse, bo);
    }

    /**
     * 发送验证码
     *
     * @param loginRequest
     * @return
     */
    @PostMapping(value = "/send/smsCode")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "发送登录验证码", operation = OperationTypeEnum.LOGIN)
    public CommonResult<String> smsCode(@RequestBody LoginRequest loginRequest) {
        loginService.sendSmsCode(loginRequest);
        return CommonResult.success("验证码短信已发送");
    }

    /**
     * 接口用户登录校验
     *
     * @param loginRequest
     * @return
     */
    @PostMapping(value = "/itf/login")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "接口用户登录校验", operation = OperationTypeEnum.LOGIN)
    public CommonResult<JwtToken> itfLogin(@RequestBody LoginRequest loginRequest) {

        if(StringUtil.isNull(loginRequest.getUsername())) {
            throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
        }
        if(StringUtil.isNull(loginRequest.getPassword())) {
            throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
        }

        LoginBO bo = LoginBO.builder(loginRequest)
                .checkPassword(true)
                .itfUserCanLogin(true);

        String loginName = loginService.loginCheck(bo).getLoginName();

        String token = JwtUtil.generateToken(loginName);

        JwtToken jwtToken = new JwtToken();
        jwtToken.setAccount(loginName);
        jwtToken.setSource("DMP_ITF");
        jwtToken.setTimestamp(Long.toString(new Date().getTime()));
        jwtToken.setToken(token);

        return CommonResult.success(jwtToken);
    }

    private CommonResult<JwtToken> loginCheck(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, LoginBO bo) {
        String loginName = loginService.loginCheck(bo).getLoginName();

        String token = JwtUtil.generateToken(loginName);
        CookieUtil.create(httpServletRequest, httpServletResponse, token, false, (int) (Long.parseLong(expireDate) / 1000));

        JwtToken jwtToken = new JwtToken();
        jwtToken.setAccount(loginName);
        jwtToken.setSource("DMP_THIRD");
        jwtToken.setTimestamp(Long.toString(new Date().getTime()));
        jwtToken.setToken(token);

        return CommonResult.success(jwtToken);
    }
}
