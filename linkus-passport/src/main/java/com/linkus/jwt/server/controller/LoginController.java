package com.linkus.jwt.server.controller;

import cn.hutool.core.codec.Base64;
import com.anji.captcha.service.CaptchaService;
import com.linkus.base.aop.LogAnnotation;
import com.linkus.base.aop.OperationTypeEnum;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.response.error.ErrorCmnEm;
import com.linkus.base.response.error.ErrorSsoEm;
import com.linkus.base.shiro.JwtUtil;
import com.linkus.base.util.JsonUtil;
import com.linkus.base.util.RSAEncrypt;
import com.linkus.base.util.StringUtil;
import com.linkus.base.web.CookieUtil;
import com.linkus.base.web.OAAuthUtil;
import com.linkus.base.web.OAAuthUtil.Sys;
import com.linkus.base.web.OAToken;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.jwt.server.model.CheckUserResult;
import com.linkus.jwt.server.model.LoginBO;
import com.linkus.jwt.server.model.LoginRequest;
import com.linkus.jwt.server.model.RegisterUserBO;
import com.linkus.jwt.server.model.SysUser;
import com.linkus.jwt.server.service.JwtSysUserService;
import com.linkus.jwt.server.service.LoginService;
import com.linkus.jwt.server.util.PasswordValidateUtil;
import com.linkus.jwt.server.util.RequestUtil;
import com.linkus.nacos.rule.constant.LinkusConstant;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysDefRoleUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Controller
public class LoginController extends BaseCtrl {

	private static final Logger LOG = LoggerFactory.getLogger(LoginController.class);

	@Value("${homeContextPath}")
	private String homeContextPath;

	@Value("${custHomeContextPath}")
	private String custHomeContextPath;

	@Value("${digitalsmartcityHost}")
	private String dscHost;

	@Value("${digitalsmartcityContextPath}")
	private String dscContextPath;

	@Value("${spring.jwt.shiro.server.expireDate}")
	private String expireDate;

	// BPM系统地址
	@Value("${bpmSysAddress}")
	private String bpmSysAddress;

	// 回款跟踪系统地址
	@Value("${paymentTrackAddress}")
	private String paymentTrackAddress;

	// AiPM地址
	@Value("${aiPmAddress}")
	private String aiPmAddress;
	@Value("${aiPmNewAddress}")
	private String aiPmNewAddress;

	// 知否地址
	@Value("${knowledgeAddress}")
	private String knowledgeAddress;

	// AiPms地址
	@Value("${aiPmsAddress}")
	private String aiPmsAddress;

	// 数字城市地址
	@Value("${digitalSmartCityAddress}")
	private String digitalSmartCityAddress;

	// ABS战略白皮书地址
	@Value("${ABSAddress}")
	private String ABSAddress;

	@Value("${productAddress}")
	private String productAddress;

	@Value("${dmpAddress}")
	private String dmpAddress;

	// 业财指标地址
	@Value("${financeIndexAddress}")
	private String financeIndexAddress;

	/**
	 * nacos 命名空间
	 */
	@Value("${spring.cloud.nacos.config.namespace}")
	private String namespace;

	@Autowired
	private JwtSysUserService jwtSysUserService;

	@Autowired
	private OAAuthUtil oaAuthUtil;
	@Autowired
	private ISysDefRoleUserService sysDefRoleUserService;

	@Autowired
	private RSAEncrypt rsaEncrypt;

	@Autowired
	private CaptchaService captchaService;

	@Autowired
	private LoginService loginService;

	private final String NT_LOGINNAME_OR_PASSWORD_ERROR_MESSAGE = "用户名或密码错误，内部员工请使用公司NT账号登录！";
	private final String ONLY_ALLOW_ITF_LOGINNAME = "仅允许接口账号登录，请联系DMP管理员";

	// OA认证的时候，state参数名
	private final String STATE_PARAM_NAME = "state";
	// aipm的state数据--新首页
	private static final String AIPM_STATE_VALUE_NEWINDEX = "newIndex";

	public LoginController() {
	}

	@RequestMapping("/")
	public String home() {
		return "redirect:/login";
	}

	@RequestMapping("/test")
	@ResponseBody
	public CommonResult<HashMap<Object, Object>> asd(HttpServletRequest request) {
		HashMap<Object, Object> result = new HashMap<>();
		result.put("scheme", request.getScheme());
		result.put("serverName", request.getServerName());
		result.put("serverPort", request.getServerPort());
		result.put("Host", request.getHeader("Host"));
		result.put("X-Forwarded-Host", request.getHeader("X-Forwarded-Host"));
		result.put("X-Forwarded-Proto", request.getHeader("X-Forwarded-Proto"));
		result.put("X-Forwarded-Port", request.getHeader("X-Forwarded-Port"));
		result.put("X-Forwarded-For", request.getHeader("X-Forwarded-For"));
		result.put("Custom-Host", request.getHeader("Custom-Host"));

		return CommonResult.success(result);
	}

	@RequestMapping("/bpmerror")
	public String bpmerror() {
		return "bpmerror";
	}

	// 跳转登录页面
	@RequestMapping("/login")
	public ModelAndView login(HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		String code = request.getParameter("code");
		if (StringUtil.isNotNull(code)) {
			try {
				// 1、根据code调用新部落接口查询token
				OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.DMP_HOME, true, null);
				// 2、根据token查询用户信息
				String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
				String userName = oaAuthUtil.getOAAccountUserName(authorization);

				String token = JwtUtil.generateToken(userName);
				long parseLong = Long.parseLong(expireDate);
				CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));
				mv.setView(new RedirectView(homeContextPath, false));
				return mv;
			} catch (Exception e) {
				LOG.error("使用OA登录失败", e);
			}
		}
		mv.setViewName("login");
		return mv;
	}

	/**
	 * 客户满意度登录页
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping("/satisfactionLogin")
	public ModelAndView satisfactionLogin(HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		mv.setViewName("satisfactionLogin");
		return mv;
	}

	@RequestMapping(value = "/jsonLoginItf")
	@ResponseBody
	@Deprecated
	@LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "jsonLoginItf", operation = OperationTypeEnum.LOGIN)
	public Object jsonLoginItf(HttpServletRequest request, HttpServletResponse response,
			@RequestBody LinkedHashMap<String, Object> params) {
		String loginName = params.get("loginName") + "";
		String passWord = params.get("passWord") + "";
		String token = loginItf(request, response, loginName, passWord);
		token = token.replace(JwtUtil.jwtTokenCookieName + "=", "");
		Map<String, Object> tokenMap = new HashMap<>();
		tokenMap.put(JwtUtil.jwtTokenCookieName, token);
		return JsonUtil.toJsonString(tokenMap);
	}

	// 外部接口用户登录
	@RequestMapping("/loginItf")
	@ResponseBody
	@Deprecated
	@LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "外部接口用户登录", operation = OperationTypeEnum.LOGIN)
    public String loginItf(HttpServletRequest request, HttpServletResponse response, String loginName, String passWord) {

		if(StringUtil.isNull(loginName)) {
			throw com.linkus.base.response.BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
		}
		if(StringUtil.isNull(passWord)) {
			throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
		}
        response.setCharacterEncoding("UTF-8");
		LoginRequest loginRequest = new LoginRequest();
		loginRequest.setUsername(loginName);
		loginRequest.setPassword(passWord);
		LoginBO bo = LoginBO.builder(loginRequest)
				.checkPassword(true)
				.itfUserCanLogin(true);
		String ln = loginService.loginCheck(bo).getLoginName();
        String token = JwtUtil.generateToken(ln);
		System.out.println(JwtUtil.jwtTokenCookieName + "=" + token);
		LOG.info(JwtUtil.jwtTokenCookieName + "=" + token);
        return JwtUtil.jwtTokenCookieName + "=" + token;
    }

	@RequestMapping("/loginSuccess")
	public String loginSuccess(HttpServletRequest httpServletResquest, HttpServletResponse httpServletResponse) {
		String requestURL = httpServletResquest.getRequestURL().toString();
		String requestURI = httpServletResquest.getRequestURI();
		String host = requestURL.substring(0, requestURL.indexOf(requestURI));
		String loginName = JwtUtil.getSubject(httpServletResquest);
		TeSysUser loginUser = jwtSysUserService.getSysUserByLoginName(loginName);
		String path = host;
		if (host.indexOf(dscHost) != -1) {
			path += dscContextPath;
		} else {
			path += homeContextPath;
			if (null != loginUser && null != loginUser.getCustCom() && null != loginUser.getCustCom()) {
				path = host + custHomeContextPath;
			}
		}
		return "redirect:" + path;
	}

	@RequestMapping("/update")
	public String update(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) {
		String casLoginUser = getCasLoginUser();
		if(StringUtil.isNull(casLoginUser)){
			return logout(httpServletRequest, httpServletResponse);
		}
		RequestUtil.returnResult("{}");
		return null;
	}

	@RequestMapping("/logout")
	public String logout(HttpServletRequest httpServletResquest, HttpServletResponse httpServletResponse) {
		CookieUtil.clear(httpServletResquest, httpServletResponse);
		return "redirect:/login";
	}

	@RequestMapping("/logoutDBC")
	@LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "logoutDBC", operation = OperationTypeEnum.LOGIN)
	public String logoutDBC(HttpServletRequest httpServletResquest, HttpServletResponse httpServletResponse) {
		CookieUtil.clear(httpServletResquest, httpServletResponse);
		String domain = httpServletResquest.getScheme() + "://" + httpServletResquest.getServerName();
		return "redirect:/login?redirect=" + domain + "/02vue-cli/dbcHome/index.html";
	}

	@RequestMapping("/updatePassword")
	public String updatePassword(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			String loginName, Boolean isOnlineServer) {
		httpServletResponse.setCharacterEncoding("UTF-8");
		httpServletRequest.setAttribute("loginName", loginName);
		httpServletRequest.setAttribute("isOnlineServer", isOnlineServer == null ? false : isOnlineServer);
		// 校验账号是否为外部账号
		if (StringUtil.isNull(loginName)) {
			return "login";
		}
		SysUser sysUser = jwtSysUserService.querySysUserByLoginName(loginName);
		if (sysUser == null) {
			return "login";
		}
		// 只有外部账号才可修改密码
		if (sysUser.getOaId() == null || sysUser.getOaId().equals(0L)) {
			return "updatePassword";
		}
		return "redirect:/loginSuccess";
	}

	@RequestMapping("/retrievePassword")
	public String retrievePassword(HttpServletRequest request, HttpServletResponse response) {
		return "retrievePassword";
	}

	@GetMapping("/rsaPublicKey")
	@ResponseBody
	public String rsaPublicKey() {
		String pk = rsaEncrypt.getPublicKey();
		if (StringUtil.isNull(pk)) {
			throw new BaseException("获取公钥失败！");
		}
		return pk;
	}

	/**
	 * 老版dmp在用
	 * @param loginName
	 * @return
	 */
	@PostMapping("/checkLoginName")
	@ResponseBody
	private CheckUserResult checkLoginName(String loginName) {
//		if (!"linkus-old-dmp".equals(getRequest().getHeader("Linkus-Client-Type"))) {
//			throw BusinessException.initExc(ErrorSsoEm.NO_AUTH);
//		}
		LOG.info("loginName:{}", loginName);
		CheckUserResult r = new CheckUserResult();
		SysUser sysUser = jwtSysUserService.querySysUserByLoginName(loginName);
		if (sysUser == null) {
			return r;
		}
		r.setLoginNameValid(true);
		if (sysUser.getOaId() != null && !sysUser.getOaId().equals(0L)) {
			r.setHasOaId(true);
		}
		int c = jwtSysUserService.countNonPasswordSysUser(loginName);
		r.setHasPassword(c == 0);
		LOG.info("loginName:{}", c == 0);
		return r;
	}

	@PostMapping("/checkPhoneNumber")
	@ResponseBody
	public String checkPhoneNumber(String phoneNumber, String loginName) {
		if (StringUtil.isNull(phoneNumber) || StringUtil.isNull(loginName)) {
			return "请检查用户名手机号！";
		}
		return jwtSysUserService.checkPhoneNumber(loginName, phoneNumber);
	}

	@PostMapping("/sendValidateCode")
	@ResponseBody
	public void sendValidateCode(String phoneNumber, String loginName) {
		if (StringUtil.isNull(phoneNumber) || StringUtil.isNull(loginName)) {
			return;
		}
		// 发送验证码
		jwtSysUserService.sendValidateCode(loginName, phoneNumber);
	}

	@PostMapping("/checkValidateCode")
	@ResponseBody
	public String checkValidateCode(String phoneNumber, String loginName, String validateCode) {
		if (StringUtil.isNull(phoneNumber) || StringUtil.isNull(loginName) || StringUtil.isNull(validateCode)) {
			return "请检查用户名手机号或验证码！";
		}
		return jwtSysUserService.checkUserValidateCode(loginName, phoneNumber, validateCode);
	}

	@PostMapping("/checkUserPassword")
	@ResponseBody
	public String checkUserPassword(String loginName, String password) {
		if (StringUtil.isNull(loginName) || StringUtil.isNull(password)) {
			return "请检查用户名密码！";
		}
		return jwtSysUserService.checkUserPassword(loginName, password);
	}

	@PostMapping("/updateUserPassword")
	@ResponseBody
	public String updateUserPassword(String loginName, String password, String phoneNumber, String validateCode) {
		if (StringUtil.isNull(loginName) || StringUtil.isNull(password)) {
			return "请检查用户名密码！";
		}
		String decryptedLoginName = rsaEncrypt.decrypt(loginName);
		CheckUserResult checkUser = checkLoginName(decryptedLoginName);
		if(Objects.isNull(checkUser)){
			return JwtSysUserService.ERROR_MSG_USER_NOT_EXISTS;
		}
		if (checkUser.getHasOaId() || !checkUser.getLoginNameValid()) {
			return JwtSysUserService.ERROR_MSG_USER_NOT_EXISTS;

		}
		String checkPhoneNumber = checkPhoneNumber(phoneNumber, decryptedLoginName);
		if(StringUtil.isNotNull(checkPhoneNumber)){
			return checkPhoneNumber;
		}
		String checkValidateCode = checkValidateCode(phoneNumber, decryptedLoginName, validateCode);
		if(StringUtil.isNotNull(checkValidateCode)){
			return checkValidateCode;
		}
		return jwtSysUserService.updateUserPassword(loginName, password);
	}

	@Override
	public String getCasLoginUser() {
		return super.getCasLoginUser();
	}

	/**
	 * 登录验证用户信息权限
	 *
	 * @param httpServletResquest
	 * @param httpServletResponse
	 * @param username
	 * @param password
	 * @param redirect
	 * @param captchaVerification
	 * @param captchaType
	 * @param model
	 * @return
	 */
	@RequestMapping(value = "login", method = RequestMethod.POST)
	public String login(HttpServletRequest httpServletResquest, HttpServletResponse httpServletResponse,
						String username, String password, String redirect,
						String captchaVerification, String captchaType,
						Model model) {

		if (StringUtil.isNull(username)) {
			throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
		}
		if (StringUtil.isNull(password)) {
			throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_EMPTY);
		}
		try {
			LoginBO bo = getLoginBO(username, password, captchaVerification, captchaType);
			TeSysUser sysUser = loginService.loginCheck(bo);

			String token = JwtUtil.generateToken(sysUser.getLoginName());
			CookieUtil.create(httpServletResquest, httpServletResponse, token, false, (int) (Long.parseLong(expireDate) / 1000));

			if (RequestUtil.isMobileRequest()) {
				Map<String, Object> mobileRes = new HashMap<String, Object>();
				SysUser user = jwtSysUserService.querySysUserByLoginName(sysUser.getLoginName());
				mobileRes.put("result", true);
				mobileRes.put("message", "成功");
				mobileRes.put("userInfo", user);
				mobileRes.put("userObjId", sysUser.getId().toHexString());
				RequestUtil.returnResult(mobileRes);
				return null;
			} else {
				return "redirect:" + (StringUtil.isNull(redirect) ? "/loginSuccess" : redirect);
			}
		} catch (Exception ex) {

			String errorCode = ErrorCmnEm.EXCEPTION.getErrCode();
			String errorMsg = ex.getMessage();
			if (ex instanceof BusinessException) {
				errorCode = StringUtil.to(ex, BusinessException.class).getErrCode();
			} else {
				LOG.error("login", ex);
			}

			if (RequestUtil.isMobileRequest()) {
				Map<String, Object> mobileRes = new HashMap<String, Object>();
				mobileRes.put("result", false);
				mobileRes.put("message", errorMsg);
				mobileRes.put("code", errorCode);
				RequestUtil.returnResult(mobileRes);
				return null;
			} else {
				model.addAttribute("error", errorMsg);
				model.addAttribute("code", errorCode);
				return "login";
			}
		}
	}

	/**
	 * 从OA登录BPM系统
	 *
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginBpm", method = RequestMethod.GET)
	public ModelAndView loginBpm(HttpServletRequest request, HttpServletResponse response) {
		String code = request.getParameter("code");
		String state = request.getParameter("state");
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}
		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.BPM);
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();

			// 2、根据token查询用户信息
			String userName = oaAuthUtil.getOAAccountUserName(authorization);

			// 3、登录token
			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			String redirectUrl = bpmSysAddress + "&from=oa&sys=bpm_from_oa";
			if ("bpmProcessFileView".equals(state)) {
				String fileId = request.getParameter("fileId");
				if (StringUtil.isNotNull(fileId)) {
					redirectUrl = "/02html/BPM/bpmProcessFileView.html?fileId=" + fileId + "&sys=bpm_from_oa";
				}
			} else if("zljys".equals(state)) {
				// 战略及预算
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=60dfd75bf166b7f67586a3b6&initialParam=60dfd75bf166b7f67586a3b6&sys=bpm_from_oa";
			} else if("jygl".equals(state)) {
				// 经营管理
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6cd&initialParam=5c2db5ec90310a35687ca6cd&sys=bpm_from_oa";
			} else if("cggl".equals(state)) {
				// 采购管理
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6cf&initialParam=5c2db5ec90310a35687ca6cf&sys=bpm_from_oa";
			} else if("csgl".equals(state)) {
				// 财税管理
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6d1&initialParam=5c2db5ec90310a35687ca6d1&sys=bpm_from_oa";
			} else if("rlzy".equals(state)) {
				// 人力资源
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6d0&initialParam=5c2db5ec90310a35687ca6d0&sys=bpm_from_oa";
			} else if("yfgl".equals(state)) {
				// 研发管理
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6cc&initialParam=5c2db5ec90310a35687ca6cc&sys=bpm_from_oa";
			} else if("zhgl".equals(state)) {
				// 综合管理
				redirectUrl = "/02html/BPM/bpmProcessQuery.html?initialValue=5c2db5ec90310a35687ca6d5&initialParam=5c2db5ec90310a35687ca6d5&sys=bpm_from_oa";
			}

			mv.setView(new RedirectView(redirectUrl, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	/**
	 * 从OA登录回款跟踪系统
	 *
	 * 1、OA提供code用于到oa中换取token 2、使用获取的token查询用户信息
	 *
	 * @param code     oa系统提供的code
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginPayTrac", method = RequestMethod.GET)
	public ModelAndView loginPayTrac(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}

		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.PAYTRAC, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(paymentTrackAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	/**
	 * 从OA登录AiPM
	 *
	 * 1、OA提供code用于到oa中换取token 2、使用获取的token查询用户信息
	 *
	 * @param code     oa系统提供的code
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginAiPM", method = RequestMethod.GET)
	public ModelAndView loginAiPM(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}

		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.AIPM, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			String redirectUrl = aiPmAddress;
			// aipm 首页有多个，根据state参数进行区分
			String state = request.getParameter(STATE_PARAM_NAME);
			if (StringUtil.isNotNull(state) && AIPM_STATE_VALUE_NEWINDEX.equals(state)) {
				redirectUrl = aiPmNewAddress;
			}
			mv.setView(new RedirectView(redirectUrl, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	/**
	 * 使用信部落的token登录
	 *
	 * 请求参数为authorization，调用信部落接口，校验合法性，如果合法，则查询用户信息，写入jwtToken，表示登录linkus成功，否则失败
	 *
	 * 生产环境 ：https://karagw.asiainfo.com/api/v1.0.0/account/getCurrentAccount
	 * 测试环境：https://xin-sandbox.asiainfo.com:16020/api/v1.0.0/account/getCurrentAccount
	 *
	 * @param authorization 信部落提供的token
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginForXin", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> loginForXin(String authorization, HttpServletRequest request,
			HttpServletResponse response) {
		Map<String, Object> returnResult = new HashMap<>();
		try {
			System.out.println("authorization---->" + authorization);
			String userName = oaAuthUtil.getOAAccountUserName(authorization);
			System.out.println("userName---->" + userName);
			String token = JwtUtil.generateToken(userName);
			System.out.println("token---->" + token);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			returnResult.put("status", 200);
			returnResult.put("message", "SUCCESS");
		} catch (Exception e) {
			returnResult.put("status", 500);
			returnResult.put("message", "ERROR");
		}
		return returnResult;
	}

	/**
	 * 信部落移动客户端 oauth2.0授权
	 *
	 * @param ticket      ⽤户信息的ticket
	 * @param ticketKey
	 * @param redirectUri 如果认证时候传 ⼊错误，则授权失败，授权成功后会跳转到该地址上
	 * @param state
	 * @return
	 */
	@RequestMapping(value = "/mobileAuth")
	@ResponseBody
	public String mobileAuth(String ticket, String ticketKey, String redirectUri, String state) {
		String auth = oaAuthUtil.oaAuth(Sys.KNOWLEDGE, ticket, ticketKey, redirectUri, state);
		return auth;
	}

	@RequestMapping(value = "/webAuth", produces = { MediaType.TEXT_HTML_VALUE })
	@ResponseBody
	public String webAuth(String redirectUri, String state) {
		String auth = oaAuthUtil.oaAuth(Sys.KNOWLEDGE, null, null, redirectUri, state);
		return auth;
	}

	/**
	 * 从OA登录知否
	 *
	 * 1、OA提供code用于到oa中换取token 2、使用获取的token查询用户信息
	 *
	 * @param code     oa系统提供的code
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginKnowledge")
	public ModelAndView loginKnowledge(String code, String redirectUri, boolean isMobile, HttpServletRequest request,
			HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}
		String subject = JwtUtil.getSubject(request);
		if (StringUtil.isNotNull(subject)) {
			mv.setView(new RedirectView(knowledgeAddress, false));
			return mv;
		}
		try {
			OAToken oaToken;
			String userName;
			// 1、根据code调用新部落接口查询token
			if (isMobile) {
				oaToken = oaAuthUtil.getOAToken(code, Sys.APP_KNOWLEDGE, redirectUri);
			} else {
				oaToken = oaAuthUtil.getOAToken(code, Sys.KNOWLEDGE, true, redirectUri);
			}
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			if (isMobile) {
				userName = oaAuthUtil.getOAAccountUserName(authorization);
			} else {
				userName = oaAuthUtil.getOAAccountUserName(authorization, true);
			}

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));
			SysUser user = jwtSysUserService.querySysUserByLoginName(userName);
			Map<String, Object> mobileRes = new HashMap<String, Object>();
			mobileRes.put("result", true);
			mobileRes.put("message", "成功");
			mobileRes.put("userInfo", user);

			TeSysUser sysUser = jwtSysUserService.getSysUserByLoginName(userName);
			if (sysUser != null) {
				mobileRes.put("userObjId", sysUser.getId().toHexString());
			}
			if (isMobile) {
				RequestUtil.returnResult(mobileRes);
			}
			mv.setView(new RedirectView(knowledgeAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	/**
	 * 从OA登录AiPMs
	 *
	 * 1、OA提供code用于到oa中换取token 2、使用获取的token查询用户信息
	 *
	 * @param code     oa系统提供的code
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/loginAiPMs")
	public ModelAndView loginAiPMs(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}
		String subject = JwtUtil.getSubject(request);
		if (StringUtil.isNotNull(subject)) {
			mv.setView(new RedirectView(aiPmsAddress, false));
			return mv;
		}
		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.AIPMS, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(aiPmsAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	@RequestMapping(value = "/loginDigitalsmartcity")
	public ModelAndView loginDigitalsmartcity(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}
		String subject = JwtUtil.getSubject(request);
		if (StringUtil.isNotNull(subject)) {
			mv.setView(new RedirectView(digitalSmartCityAddress, false));
			return mv;
		}
		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.DIGITAL_SMART_CITY, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(digitalSmartCityAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	@RequestMapping(value = "/loginFinanceIndex")
	public ModelAndView loginFinanceIndex(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView(financeIndexAddress, false));
			return mv;
		}

		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.FINANCE_INDEX, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(financeIndexAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	@RequestMapping(value = "/loginABS")
	public ModelAndView loginABS(String code, String param, HttpServletRequest request, HttpServletResponse response) throws Exception {
		String decode = Base64.decodeStr(param, "UTF-8");
		decode = URLDecoder.decode(decode);
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView("login"));
			return mv;
		}
		String subject = JwtUtil.getSubject(request);
		if (StringUtil.isNotNull(subject)) {
			mv.setView(new RedirectView(ABSAddress+decode, false));
			return mv;
		}
		try {
			Sys sys = LinkusConstant.PROD_NS.equals(namespace) ? Sys.ABS : Sys.ABS_SANDBOX;
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, sys, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(ABSAddress+decode, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	@RequestMapping(value = "/loginProduct")
	public ModelAndView loginProduct(String code,String state, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView(productAddress, false));
			return mv;
		}

		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.PRODUCT, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));
			if (StringUtil.isNotNull(state) && state.length() >10) {
				state = Base64.decodeStr(state, "UTF-8");
				mv.setView(new RedirectView(productAddress+state, false));
			} else {
				mv.setView(new RedirectView(productAddress, false));
			}

		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	@RequestMapping(value = "/loginDmp")
	public ModelAndView loginDmp(String code, HttpServletRequest request, HttpServletResponse response) {
		ModelAndView mv = new ModelAndView();
		if (StringUtil.isNull(code)) {
			mv.setView(new RedirectView(dmpAddress, false));
			return mv;
		}

		try {
			// 1、根据code调用新部落接口查询token
			OAToken oaToken = oaAuthUtil.getOAToken(code, Sys.DMP, true, null);
			// 2、根据token查询用户信息
			String authorization = oaToken.getTokenType() + " " + oaToken.getAccessToken();
			String userName = oaAuthUtil.getOAAccountUserName(authorization, true);

			String token = JwtUtil.generateToken(userName);
			long parseLong = Long.parseLong(expireDate);
			CookieUtil.clear(request, response);
			CookieUtil.create(request, response, token, false, (int) (parseLong / 1000));

			mv.setView(new RedirectView(dmpAddress, false));
		} catch (Exception e) {
			LOG.error("系统异常", e);
			mv.setView(new RedirectView("login"));
		}
		return mv;
	}

	/**
	 * 修改账号密码
	 */
	@RequestMapping("/sumbitPassword.action")
	@ResponseBody
	public Map<String, Object> savePassword(HttpServletRequest request, HttpServletResponse response) {
		Map<String, Object> resultMap = new HashMap<String, Object>();
		String loginName = request.getParameter("loginName");
		String oldPassword = request.getParameter("oldPassword");
		String newPassword = request.getParameter("newPassword");

		String decryctedLoginName = null;
		String decryctedOldPassword = null;
		String decryptedNewPassword = null;
		try {
			String privateKey = rsaEncrypt.getPrivateKey();
			decryctedLoginName = rsaEncrypt.decrypt(loginName, privateKey);
			decryctedOldPassword = rsaEncrypt.decrypt(oldPassword, privateKey);
			decryptedNewPassword = rsaEncrypt.decrypt(newPassword, privateKey);
		} catch (Exception e) {
			LOG.error("获取用户名或者密码失败！", e);
		}

		if (decryctedLoginName == null || decryctedOldPassword == null || decryptedNewPassword == null) {
			Map<String, Object> r = new HashMap<String, Object>();
			r.put("ErrorMessage", "获取用户名或者密码失败！");
			return r;
		}

		Boolean isSucess = true;
		Boolean flag = true;
		Boolean isMatch = true;
		Map<String, String> userNameMap = jwtSysUserService.getEnglishNameByLoginName(decryctedLoginName);
		List<String> errorMessage = PasswordValidateUtil.checkPassword(decryptedNewPassword, decryctedLoginName,
				userNameMap.get("firstName"), userNameMap.get("lastName"));
		if (CollectionUtils.isNotEmpty(errorMessage)) {
			isMatch = false;
		}

		if (isMatch) {
			if (!StringUtil.isNull(decryctedOldPassword) && !StringUtil.isNull(decryptedNewPassword)
					&& !StringUtil.isNull(decryctedLoginName)) {
				CheckUserResult r = jwtSysUserService.checkUserAndPassword(decryctedLoginName, decryctedOldPassword);
				flag = r.isUserValid();
				if (flag) {
					// 更新新密码
					isSucess = jwtSysUserService.saveNewPassword(decryctedLoginName, decryptedNewPassword,
							decryctedOldPassword);
				}

			}
		}
		resultMap.put("isMatch", isMatch);
		resultMap.put("isPasswordTure", flag);
		resultMap.put("isSucess", isSucess);
		if (RequestUtil.isMobileRequest()) {
			resultMap.put("ErrorMessage", errorMessage.size() > 0 ? errorMessage.get(0) : "");
		} else {
			StringBuffer msg = new StringBuffer();
			for (String item : errorMessage) {
				msg.append("<p>" + item + "</p></br>");
			}
			resultMap.put("ErrorMessage", msg);
		}
		return resultMap;
	}

	/**
	 * 用户注册接口
	 */
	@RequestMapping(value = "/register", method = RequestMethod.POST)
	@ResponseBody
	public Map<String, Object> registerUser(HttpServletRequest request, HttpServletResponse response) {

		String loginName = request.getParameter("loginName");
		String userName = request.getParameter("userName");
		String password = request.getParameter("password");

		RegisterUserBO registerUser = new RegisterUserBO();
		registerUser.setLoginName(loginName);
		registerUser.setPassword(password);
		registerUser.setUserName(userName);
		Map<String, Object> resultMap = jwtSysUserService.registerSysUser(registerUser);

		return resultMap;
	}

	/**
	 * 新密码是否含空格
	 *
	 * @param str
	 * @return
	 */
	public boolean hasEmpty(String str) {
		Boolean flag = true;
		if (str.indexOf(" ") != -1) {
			flag = false;
		}
		return flag;
	}

	/**
	 * 新密码是否英文或数字
	 *
	 * @param str
	 * @return
	 */
	public boolean isEnglishOrNum(String str) {
		return str.matches("[0-9a-zA-Z]*");
	}

	// 是否重复
	public boolean same(String str) {
		if (str != null && str.length() > 1) {
			for (int i = 0; i < str.length(); i++) {
				char c = str.charAt(i);
				// 最后一位
				if (i == str.length() - 1) {
					continue;
				}
				// 当前和后一位不同 返回
				if (c != str.charAt(i + 1)) {
					return false;
				}
			}
			return true;
		}
		return false;
	}

	/**
	 * 连续字符串 123 abc ABC
	 *
	 * @param str
	 * @return
	 */
	public boolean continuous(String str) {
		if (str != null && str.length() > 1) {
			// 第一位和第二位相减的结果
			int result = str.charAt(0) - str.charAt(1);
			// 1或-1说明是连续的
			if (result == 1 || result == -1) {
				for (int i = 1; i < str.length(); i++) {
					// 最后一位
					if (i == str.length() - 1) {
						continue;
					}
					// 相减的结果是否和result相同 不同则不连续
					if ((str.charAt(i) - str.charAt(i + 1)) != result) {
						return false;
					}
				}
			} else {
				return false;
			}
			// 这里说明是连续的
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 根据间距验证字符串，interval为0验证相同字符串，为1验证abc，为2验证eca。
	 *
	 * @param str
	 * @param interval
	 * @return
	 */
	public boolean continuous(String str, int interval) {
		if (str != null && str.length() > 1) {
			// 第一位和第二位相减的结果
			int result = str.charAt(0) - str.charAt(1);
			// 差值是否是间距
			if (result == interval || result == -interval) {
				for (int i = 1; i < str.length(); i++) {
					// 最后一位
					if (i == str.length() - 1) {
						continue;
					}
					// 相减的结果是否和result相同 不同则不连续
					if ((str.charAt(i) - str.charAt(i + 1)) != result) {
						return false;
					}
				}
			} else {
				return false;
			}
			return true;
		} else {
			return false;
		}
	}

	@RequestMapping(value = "isLogin")
	@ResponseBody
	public boolean isLogin(HttpServletRequest request) {
		String loginName = JwtUtil.getSubject(request);
		if (StringUtil.isNotNull(loginName)) {
			return true;
		}
		return false;
	}


	// 员工能力标签填报登录
	@RequestMapping("/userTagFilledInLogin.action")
	@ResponseBody
	public String userTagFilledInLogin(@RequestParam(value = "userName", required = true) String userName,
			@RequestParam(value = "passWord", required = true) String passWord,
			@RequestParam(value = "days", required = false) Integer days, HttpServletRequest request,
			HttpServletResponse response) {
		String res = "";
		if (StringUtil.isNull(userName)) {
			res = "用户名不能为空";
			return res;
		}
		if (StringUtil.isNull(passWord)) {
			res = "密码不能为空";
			return res;
		}
		CheckUserResult r = jwtSysUserService.checkUserAndPassword(userName, passWord);
		if (!r.isUserValid()) {
			res = NT_LOGINNAME_OR_PASSWORD_ERROR_MESSAGE;
			return res;
		}
		if (days != null) {
			if (days > 30) {
				res = "Cookie的失效时间不能大于30天";
				return res;
			}
			if (days <= 0) {
				res = "Cookie的失效时间不能小于或等于0天";
				return res;
			}
		}

		String token = JwtUtil.generateToken(userName);
		long parseLong = Long.parseLong(expireDate);
		CookieUtil.clear(request, response);
		CookieUtil.create(request, response, token, false,
				days == null ? (int) (parseLong / 1000) : (int) (parseLong / 30000 * days));
		res = "loginSuccess";
		return res;
	}

	private LoginBO getLoginBO(String username, String password, String captchaVerification, String captchaType) {
		if (RequestUtil.isMobileRequest()) {
			LoginRequest loginRequest = new LoginRequest();
			loginRequest.setUsername(username);
			loginRequest.setPassword(password);
			if ("linkus-old-dmp".equals(getRequest().getHeader("Linkus-Client-Type"))) {
				// 老版dmp登录
				return LoginBO.builder(loginRequest)
						.checkPassword(true)
						.customUserCanLogin(true)
						.oaUserCanLogin(true);
			}
			return LoginBO.builder(loginRequest)
					.rsa(true)
					.checkPassword(true)
					.oaUserCanLogin(true);
		} else {
			LoginRequest loginRequest = new LoginRequest();
			loginRequest.setUsername(username);
			loginRequest.setPassword(password);
			loginRequest.setCaptchaVerification(captchaVerification);
			loginRequest.setCaptchaType(captchaType);

			return LoginBO.builder(loginRequest)
					.rsa(true)
					.checkPassword(true)
					.checkCaptcha(true)
					.customUserCanLogin(true)
					.oaUserCanLogin(true);
		}
	}

	@PostMapping("/authToken")
	@ResponseBody
	public CommonResult<TeUser> authToken() {
		String loginName = getCasLoginUserName();
		if (StringUtil.isNull(loginName)) {
			throw BusinessException.initExc("未获取当前登录账号信息");
		}
		TeSysUser sysUser = jwtSysUserService.getSysUserByLoginName(loginName);
		if (sysUser == null) {
			throw BusinessException.initExc("未获取当前登录账号信息");
		}
		TeUser u = sysUser.trans2User();
		return CommonResult.success(u);
	}

}
