package com.linkus.jwt.server.controller;

import com.linkus.base.aop.LogAnnotation;
import com.linkus.base.aop.OperationTypeEnum;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.response.CommonController;
import com.linkus.base.response.CommonResult;
import com.linkus.jwt.server.model.JwtToken;
import com.linkus.jwt.server.model.ThirdPartyToken;
import com.linkus.jwt.server.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第三方系统授权
 * 测试环境：https://devlinkus.asiainfo.com <br>
 * 生产环境：https://dmp.asiainfo.com
 */
@Slf4j
@RestController
@RefreshScope
@RequestMapping("/third-party")
public class ThirdPartyController extends CommonController {

    @Autowired
    private LoginService loginService;

    /**
     * 第三方系统 token 转 DMP token
     * 互信第三方系统token
     *
     * @param token
     * @return data:DMP token
     */
    @PostMapping("/token/to/dmp")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "第三方系统 token 转 DMP token", operation = OperationTypeEnum.LOGIN)
    public CommonResult<JwtToken> tokenToDmp(@RequestBody ThirdPartyToken token) {
        return CommonResult.success(loginService.tokenToDmp(token));
    }

    /**
     * userId 转 DMP token
     * 需DMP侧分配接口账号权限,itf auth
     *
     * @param token
     * @return data:DMP token
     */
    @PostMapping("/userid/to/dmp_token")
    @LogAnnotation(subsystem = DMPSubsystem.BIZ, module = "userId 转 DMP token", operation = OperationTypeEnum.LOGIN)
    public CommonResult<JwtToken> token4UserId(@RequestBody ThirdPartyToken token) {
        return CommonResult.success(loginService.token4UserId(token));
    }
}
