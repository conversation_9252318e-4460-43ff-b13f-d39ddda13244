package com.linkus.jwt.server.service;

import com.alibaba.fastjson.JSONObject;
import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.linkus.base.constants.ServiceNameConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.error.ErrorSsoEm;
import com.linkus.base.shiro.JwtUtil;
import com.linkus.base.util.*;
import com.linkus.base.web.OAAuthUtil;
import com.linkus.base.web.OAToken;
import com.linkus.common.model.TeIdName;
import com.linkus.common.model.TeUser;
import com.linkus.common.web.CommonResult;
import com.linkus.jwt.server.model.JwtToken;
import com.linkus.jwt.server.model.LoginBO;
import com.linkus.jwt.server.model.LoginRequest;
import com.linkus.jwt.server.model.ThirdPartyToken;
import com.linkus.jwt.server.util.AesUtil;
import com.linkus.jwt.server.util.LdapAuthUtil;
import com.linkus.jwt.server.util.LdapCfg;
import com.linkus.jwt.server.util.PasswordValidateUtil;
import com.linkus.msg.api.client.SmsFeignClient;
import com.linkus.msg.api.model.MsgSmsInfo;
import com.linkus.nacos.rule.constant.LinkusConstant;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.model.TeSysUserLoginHist;
import com.linkus.sysuser.service.ISysUserLoginHistService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.DigestUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class LoginService {

    /**
     * 验证码
     */
    private RMapCache<String, String> VALIDATE_CODE_MAP;

    private final int TIMEOUT_MINUTES = 15;

    @Value("${paymentGetOaToken}")
    private String paymentGetOaToken;

    @Value("${xinGetToken}")
    private String xinGetToken;

    /**
     * nacos 命名空间
     */
    @Value("${spring.cloud.nacos.config.namespace}")
    private String namespace;

    @Resource
    private OAAuthUtil oaAuthUtil;
    @Resource
    private Environment environment;

    @Resource
    private CaptchaService captchaService;

    @Resource
    private RSAEncrypt rsaEncrypt;

    @Resource
    private JwtSysUserService jwtSysUserService;

    @Resource
    private ISysUserLoginHistService sysUserLoginHistService;

    @Resource
    private ISysUserDao sysUserDaoMongo;

    @Resource
    private SmsFeignClient smsFeignClient;

    @Resource
    private RedissonClient redissonClient;

    @PostConstruct
    public void init() {
        this.VALIDATE_CODE_MAP = redissonClient.getMapCache("c.l.j.s.s.LoginService.VALIDATE_CODE");
    }

    public JwtToken tokenToDmp(ThirdPartyToken token) {
        Assert.notNull(token, "未获取token信息");
        String oToken = token.getToken();
        String source = token.getSource();
        String account = token.getAccount();
        Assert.hasText(source, "来源参数错误");
        Assert.hasText(oToken, "token参数错误");

        String decryptKey = environment.getProperty("third-party." + token.getSource().toLowerCase() + ".aeskey");
        boolean isInner = Boolean.parseBoolean(environment.getProperty("third-party." + token.getSource().toLowerCase() + ".inner"));
        if (StringUtil.isNull(decryptKey)) {
            decryptKey = environment.getProperty("third-party.common.aeskey");
            isInner = Boolean.parseBoolean(environment.getProperty("third-party.common.inner"));
        }
        Assert.hasText(decryptKey, "密钥配置错误");
        String decryptedToken = AesUtil.decrypt(oToken, decryptKey);
        if (StringUtil.isNull(decryptedToken)) {
            throw BusinessException.initExc("token信息错误");
        }
        String[] tokenDatas = decryptedToken.split("\\|");
        if (tokenDatas.length != 4) {
            throw BusinessException.initExc("token信息错误");
        }

        String[] oAccountData = tokenDatas[0].split("=");
        if (oAccountData.length != 2) {
            throw BusinessException.initExc("账号错误");
        }
        if (StringUtil.isNotNull(account) && !account.equals(oAccountData[1])) {
            throw BusinessException.initExc("账号不匹配");
        }

        String[] oSourceData = tokenDatas[1].split("=");
        if (oSourceData.length != 2 || !source.equals(oSourceData[1])) {
            throw BusinessException.initExc("来源错误");
        }

        String[] slaTokens = tokenDatas[2].split("=");
        if (slaTokens.length != 2 || StringUtil.isNull(slaTokens[1])) {
            throw BusinessException.initExc("token错误");
        }

        String[] timestamp = tokenDatas[3].split("=");
        if (timestamp.length != 2 || StringUtil.isNull(timestamp[1])) {
            throw BusinessException.initExc("时间戳错误");
        }

        // 通过oa的accessToken获取nt账号
        String nt = oaAuthUtil.getOAAccountUserName("bearer " + slaTokens[1], isInner);
        if (StringUtil.isNull(nt)) {
            throw BusinessException.initExc("通过oa获取nt账号错误");
        }

        JwtToken jwtToken = new JwtToken();
        jwtToken.setAccount(nt);
        jwtToken.setSource(source);
        jwtToken.setTimestamp(timestamp[1]);
        jwtToken.setToken(JwtUtil.generateToken(nt));
        return jwtToken;
    }

    public JwtToken token4UserId(ThirdPartyToken token) {
        Assert.notNull(token, "未获取token信息");
        String oToken = token.getToken();
        String source = token.getSource();
        String account = token.getAccount();
        Assert.hasText(source, "来源参数错误");
        Assert.hasText(oToken, "token参数错误");
        Assert.hasText(account, "账号参数错误");

        String decryptKey = environment.getProperty("third-party." + token.getSource().toLowerCase() + ".aeskey");
        if (StringUtil.isNull(decryptKey)) {
            decryptKey = environment.getProperty("third-party.common.aeskey");
        }
        Assert.hasText(decryptKey, "密钥配置错误");
        String decryptedToken = AesUtil.decrypt(oToken, decryptKey);
        if (StringUtil.isNull(decryptedToken)) {
            throw BusinessException.initExc("token信息错误");
        }
        String[] tokenDatas = decryptedToken.split("\\|");
        if (tokenDatas.length != 4) {
            throw BusinessException.initExc("token信息错误");
        }

        String[] oAccountData = tokenDatas[0].split("=");
        if (oAccountData.length != 2 || !account.equals(oAccountData[1])) {
            throw BusinessException.initExc("账号错误");
        }

        String[] oSourceData = tokenDatas[1].split("=");
        if (oSourceData.length != 2 || !source.equals(oSourceData[1])) {
            throw BusinessException.initExc("来源错误");
        }

        String[] timestamp = tokenDatas[3].split("=");
        if (timestamp.length != 2 || StringUtil.isNull(timestamp[1])) {
            throw BusinessException.initExc("时间戳错误");
        }

        // 通过oa的accessToken获取nt账号 "UIH0OKC4R"
        String nt = oaAuthUtil.getOAUserNameById(oAccountData[1]);
        if (StringUtil.isNull(nt)) {
            throw BusinessException.initExc("token信息错误-7");
        }

        JwtToken jwtToken = new JwtToken();
        jwtToken.setAccount(nt);
        jwtToken.setSource(source);
        jwtToken.setTimestamp(timestamp[1]);
        jwtToken.setToken(JwtUtil.generateToken(nt));
        return jwtToken;
    }

    public OAToken getOAToken(String code, String clientId, String clientSecret, boolean isInner, String redirectUri) {
        OkHttpUtil httpUtil = OkHttpUtil.getInstance();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");

        Map<String, Object> body = new HashMap<>();
        body.put("code", code);
        body.put("grant_type", "authorization_code");
        body.put("client_id", clientId);
        body.put("client_secret", clientSecret);
        if (StringUtil.isNotNull(redirectUri)) {
            body.put("redirect_uri", redirectUri);
        }

        String accessUrl = "";
        if (isInner) {
            accessUrl = paymentGetOaToken;
        } else {
            accessUrl = xinGetToken;
        }
        String result = httpUtil.postData(accessUrl, body, headers);
        JSONObject json = JSONObject.parseObject(result);
        if (json.containsKey("error")) {
            throw BusinessException.initExc("调用信部落接口" + accessUrl + "，错误信息：" + json.getString("error"));
        }

        String accessToken = json.getString("access_token");
        String tokenType = json.getString("token_type");
        String refreshToken = json.getString("refresh_token");
        String expiresIn = json.getString("expires_in");

        if (StringUtil.isNull(accessToken)) {
            throw BusinessException.initExc("调用信部落接口" + accessUrl + "，获取access_token为空");
        }

        if (StringUtil.isNull(tokenType)) {
            throw BusinessException.initExc("调用信部落接口" + accessUrl + "，获取token_type为空");
        }

        if (StringUtil.isNull(refreshToken)) {
            throw BusinessException.initExc("调用信部落接口" + accessUrl + "，获取refresh_token为空");
        }

        if (StringUtil.isNull(expiresIn)) {
            throw BusinessException.initExc("调用信部落接口" + accessUrl + "，获取expires_in为空");
        }

        OAToken token = new OAToken();
        token.setAccessToken(accessToken);
        token.setExpiresIn(expiresIn);
        token.setRefreshToken(refreshToken);
        token.setTokenType(tokenType);
        return token;
    }

    public TeSysUser loginCheck(LoginBO loginRequest) {
        Assert.notNull(loginRequest, "参数错误");

        if (!loginRequest.getCheckPassword() && !loginRequest.getCheckSmsCaptcha()) {
            log.error("密码和短信登录至少需要一种");
            throw BusinessException.initExc(ErrorSsoEm.PARAM_ERROR);
        }

        log.info("{}|{} 正在登录", getIpAddr(), loginRequest);

        if (loginRequest.getCheckCaptcha()) {
            // 滑动验证码校验
            captchaVerification(loginRequest.getCaptchaVerification(), loginRequest.getCaptchaType());
        }

        String loginName = loginRequest.getLoginName();
        String password = loginRequest.getPassword();
        String phone = loginRequest.getPhone();
        String smsCaptcha = loginRequest.getSmsCaptcha();
        if (loginRequest.getRsa()) {
            String privateKey = rsaEncrypt.getPrivateKey();
            loginName = decrypt(loginName, privateKey);
            password = decrypt(password, privateKey);
            phone = decrypt(phone, privateKey);
            smsCaptcha = decrypt(smsCaptcha, privateKey);
        }

        TeSysUser sysUser = getSysUser(loginName, phone);

        if (loginRequest.getCheckSmsCaptcha()) {
            // 短信验证码校验
            smsCaptchaVerification(sysUser.getLoginName(), phone, smsCaptcha);
        }

        if (isOaAccount(sysUser.getOaId())) {
            // OA 用户
            userCanLogin(loginRequest, sysUser);

            if (loginRequest.getCheckPassword()) {
                checkPassword(password, sysUser);
            }
            // 记录登录历史
            sysUserLoginHistService.saveLoginSuccessHist(sysUser.getLoginName(), getIpAddr());
            return sysUser;
        } else {

            userCanLogin(loginRequest, sysUser);

            if (Boolean.TRUE.equals(loginRequest.getCheckPassword()) && StringUtil.isNull(sysUser.getPassword())) {
                log.info("首次登录修改密码");
                throw BusinessException.initExc(ErrorSsoEm.UPDATE_PWD);
            }

            if (loginRequest.getCheckPassword()) {
                checkPassword(password, sysUser);
            }

            if (Boolean.TRUE.equals(sysUser.getIsLocked())) {
                Date unlockDate = DateUtil.getDeltaDate(sysUser.getLockDueDate(), Calendar.DATE, 1);
                String errorMessage = "该账户已被锁定，将于" + DateUtil.formatDate2Str(unlockDate, DateUtil.DATE_FORMAT) + "后恢复使用！";
                throw BusinessException.initExc(errorMessage);
            }

            if (StringUtil.isNotNull(password)) {
                // 密码是否过于简单
                Map<String, String> userNameMap = jwtSysUserService.getEnglishNameByLoginName(sysUser.getLoginName());
                List<String> errorMessage = PasswordValidateUtil.checkPassword(password, sysUser.getLoginName(), userNameMap.get("firstName"), userNameMap.get("lastName"));
                if (CollectionUtils.isNotEmpty(errorMessage)) {
                    log.info("密码过于简单需修改密码");
                    throw BusinessException.initExc(ErrorSsoEm.UPDATE_SIMPLE_PWD);
                }
            }
            // 记录登录历史
            sysUserLoginHistService.saveLoginSuccessHist(sysUser.getLoginName(), getIpAddr());
            return sysUser;
        }
    }

    public void sendSmsCode(LoginRequest loginRequest) {

        log.info("{}|{} 正在发送短信", getIpAddr(), loginRequest);

        captchaVerification(loginRequest.getCaptchaVerification(), loginRequest.getCaptchaType());

        String phone = decrypt(loginRequest.getPhone(), rsaEncrypt.getPrivateKey());
        if (StringUtil.isNull(phone)) {
            throw BusinessException.initExc(ErrorSsoEm.PHONE_EMPTY);
        }
        TeSysUser sysUser = jwtSysUserService.getSysUserByPhone(phone);
        if (sysUser == null) {
            log.info("手机号不存在");
            throw BusinessException.initExc(ErrorSsoEm.PHONE_ERROR);
        }
        if (isOaAccount(sysUser.getOaId()) || Boolean.TRUE.equals(sysUser.getIsItfUser())) {
            log.info("OA和接口用户不允许短信登录");
            throw BusinessException.initExc(ErrorSsoEm.PHONE_ERROR);
        }
        // 6位数字
        String validateCode = StringUtil.getRandomString(6);

        // 调用发送短信接口发送验证码
        MsgSmsInfo smsInfo = new MsgSmsInfo();
        smsInfo.setMobile(phone);
        smsInfo.setMessageContent(String.format("您的验证码是%s，" + TIMEOUT_MINUTES + "分钟内有效。如非本人操作，请忽略此短信。",
                validateCode));
        smsInfo.setSubSys(new TeIdName(SysDefConstants.SUB_SYS_ID_NEW_DMP, SysDefConstants.SUB_SYS_NAME_NEW_DMP));
        smsInfo.setServiceName(ServiceNameConstants.LINKUS_PASSPORT);
        smsInfo.setSenderUser(new TeUser(sysUser.getId(), sysUser.getLoginName(), sysUser.getLoginName(), sysUser.getJobCode()));

        CommonResult<String> result = smsFeignClient.noLoginSendSms(smsInfo);
        log.info("验证码：{}", validateCode);
        log.info("短信发送结果：{}", JsonUtil.toJsonString(result));
        if (result.isSuccess()) {
            VALIDATE_CODE_MAP.put(phone, validateCode, TIMEOUT_MINUTES, TimeUnit.MINUTES);
        }
    }

    private String decrypt(String str, String privateKey) {
        try {
            if (StringUtil.isNull(str)) {
                return "";
            }
            return rsaEncrypt.decrypt(str, privateKey);
        } catch (Exception ex) {
            throw BusinessException.initExc(ErrorSsoEm.PARAM_ERROR);
        }
    }

    private TeSysUser getSysUser(String loginName, String phone) {
        TeSysUser sysUser;
        if (StringUtil.isNotNull(phone) && StringUtil.isNotNull(loginName)) {
            sysUser = jwtSysUserService.getSysUser(loginName, phone);
            if (Objects.isNull(sysUser)) {
                throw BusinessException.initExc(ErrorSsoEm.USER_PHONE_ERROR);
            }
        } else if (StringUtil.isNotNull(loginName)) {
            sysUser = jwtSysUserService.getSysUserByLoginName(loginName);
            if (Objects.isNull(sysUser)) {
                throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_ERROR);
            }
        } else if (StringUtil.isNotNull(phone)) {
            sysUser = jwtSysUserService.getSysUserByPhone(phone);
            if (Objects.isNull(sysUser)) {
                throw BusinessException.initExc(ErrorSsoEm.PHONE_CAPTCHA_ERROR);
            }
        } else {
            throw BusinessException.initExc(ErrorSsoEm.PARAM_ERROR);
        }
        return sysUser;
    }

    private void checkPassword(String password, TeSysUser sysUser) {
        if (isOaAccount(sysUser.getOaId())) {
            // OA账号使用ldap校验
            String name = LdapCfg.REALM_NAME + LdapAuthUtil.SLANT + sysUser.getLoginName();
            if (LdapAuthUtil.getInstance().validateUser(name, password)) {
                return;
            }
            if (!LinkusConstant.PROD_NS.contains(namespace)) {
                // 非生产环境OA账号使用数据库密码校验
                if (sysUser.getPassword().equals(getSaltedMD5PasswordStr(password))) {
                    return;
                }
            }
        } else {
            // 非OA账号使用数据库密码校验
            if (sysUser.getPassword().equals(getSaltedMD5PasswordStr(password))) {
                return;
            }
        }

        saveLoginFailHist(sysUser.getLoginName(), new TeIdNameCn(SysDefConstants.DEF_ID_SYS_PARA_VALUE_SYS_ERROR_ITEM_PWD_ERROR,
                SysDefConstants.DEF_NAME_SYS_PARA_VALUE_SYS_ERROR_PWD_ERROR, null));
        throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_ERROR);
    }

    private void userCanLogin(LoginBO loginRequest, TeSysUser sysUser) {
        if (isOaAccount(sysUser.getOaId())) {
            // OA用户
            if (!loginRequest.getOaUserCanLogin()) {
                log.info("OA用户不能调用此接口");
                throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_ERROR);
            }
        } else {
            if (Boolean.TRUE.equals(sysUser.getIsItfUser())) {
                // 接口用户
                if (!loginRequest.getItfUserCanLogin()) {
                    log.info("接口用户不能调用此接口");
                    throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_ERROR);
                }
                if (StringUtil.isNull(sysUser.getIpAddr())) {
                    throw BusinessException.initExc("用户未配置IP白名单");
                }

                String ipAddr = getIpAddr();
                log.info("getIpAddr():" + ipAddr);

                Set<String> ipWhiteSet = Arrays.stream((StringUtils.isBlank(sysUser.getIpAddr()) ? "" : sysUser.getIpAddr()).split(","))
                        .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
                if (!IpUtils.isPermited(ipAddr, ipWhiteSet)) {
                    throw BusinessException.initExc(ErrorSsoEm.IP_NO_AUTH, StringUtil.getNotNullStr(sysUser.getLoginName()) + "|" + ipAddr);
                }
            } else {
                // 第三方用户
                if (!loginRequest.getCustomUserCanLogin()) {
                    log.info("第三方用户不能调用此接口");
                    throw BusinessException.initExc(ErrorSsoEm.ACCOUNT_PWD_ERROR);
                }
            }
        }
    }

    private Boolean isOaAccount(Integer oaId) {
        return Objects.nonNull(oaId) && oaId != 0;
    }

    private String getSaltedMD5PasswordStr(String password) {
        // 密码盐值
        String PASSWORD_SLAT1 = "vZ2#";
        String PASSWORD_SLAT2 = "4Nj^fC";
        String pwd = password.substring(0, 2) + PASSWORD_SLAT1 + password.substring(2) + PASSWORD_SLAT2;
        return DigestUtils.md5DigestAsHex(pwd.getBytes());
    }

    /**
     * 滑动验证码
     *
     * @param captchaVerification
     * @param captchaType
     */
    private void captchaVerification(String captchaVerification, String captchaType) {

        if (StringUtil.isNull(captchaVerification)
                || StringUtil.isNull(captchaType)) {
            throw BusinessException.initExc("缺少双因子认证");
        }

        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(captchaVerification);
        captchaVO.setCaptchaType(captchaType);
        ResponseModel response = captchaService.verification(captchaVO);
        if (!response.isSuccess()) {
            String error = "验证码校验异常";
            if ("9999".equals(response.getRepCode())) {
                error = "验证码校验内部异常";
            } else if ("0011".equals(response.getRepCode())) {
                error = "验证码参数不能为空";
            } else if ("6110".equals(response.getRepCode())) {
                error = "验证码已失效，请重新获取";
            } else if ("6111".equals(response.getRepCode())) {
                error = "验证码验证失败";
            } else if ("6112".equals(response.getRepCode())) {
                error = "获取验证码失败，请联系管理员！";
            }
            log.info("验证码校验异常:{}", response);
            throw BusinessException.initExc(error);
        }
    }

    private void smsCaptchaVerification(String loginName, String phone, String smsCaptcha) {
        if (StringUtil.isNull(phone) || StringUtil.isNull(smsCaptcha)) {
            throw BusinessException.initExc(ErrorSsoEm.SMS_CAPTCHA_EMPTY);
        }
        String validateCode = VALIDATE_CODE_MAP.get(phone);
        if (StringUtil.isNull(validateCode)) {
            throw BusinessException.initExc(ErrorSsoEm.SEND_SMS_CAPTCHA);
        }
        if (!validateCode.equals(smsCaptcha)) {
            saveLoginFailHist(loginName, new TeIdNameCn(SysDefConstants.DEF_ID_SYS_PARA_VALUE_SYS_ERROR_ITEM_VALIDATE_CODE_ERROR,
                    SysDefConstants.DEF_NAME_SYS_PARA_VALUE_SYS_ERROR_ITEM_VALIDATE_CODE_ERROR, null));
            throw BusinessException.initExc(ErrorSsoEm.SMS_CAPTCHA_ERROR);
        }
    }

    private void saveLoginFailHist(String loginName, TeIdNameCn failItem) {
        String ipAddr = getIpAddr();
        log.warn("用户登陆失败:{},{}", ipAddr, loginName);
        Date now = new Date();

        // 登录失败，查询登录失败记录
        TeSysUserLoginHist hist = sysUserLoginHistService.queryLastLoginFailHist(loginName);
        if (hist == null) {
            sysUserLoginHistService.saveLoginFailHist(loginName, 1, failItem, now, ipAddr);
            return;
        }

        Date firstFailTime = hist.getFirstFailTime();
        Integer failTimes = hist.getFailTimes();
        // 大于15分钟
        if ((now.getTime() - firstFailTime.getTime()) > TIMEOUT_MINUTES * 60 * 1000L) {
            sysUserLoginHistService.saveLoginFailHist(loginName, 1, failItem, now, ipAddr);
            return;
        }
        // 15分钟内 登录失败之后存在登录成功记录，则从1开始记录登录失败次数
        TeSysUserLoginHist successHist = sysUserLoginHistService.queryLastLoginSuccessHist(loginName);
        if (successHist != null && (successHist.getLoginTime().after(hist.getLoginTime()) || successHist.getLoginTime().equals(hist.getLoginTime()))) {
            sysUserLoginHistService.saveLoginFailHist(loginName, 1, failItem, now, ipAddr);
            return;
        }
        // 错误次数大于5次
        // 内部账号不再锁定
        // 用户凭据错误锁定次数，包括密码，验证码，手机号后4位错误
        int LOCK_USER_ERROR_TIMES = 5;
        if (failTimes >= LOCK_USER_ERROR_TIMES - 1) {
            lockUser(loginName, now);
            log.warn("登录失败5次，账号锁定:{},{},{}", ipAddr, loginName, failItem.getName());
            failItem = new TeIdNameCn(SysDefConstants.DEF_ID_SYS_PARA_VALUE_SYS_ERROR_ITEM_LOGIN_LOCK,
                    SysDefConstants.DEF_NAME_SYS_PARA_VALUE_SYS_ERROR_ITEM_LOGIN_LOCK, null);
        }
        sysUserLoginHistService.saveLoginFailHist(loginName, failTimes + 1, failItem, firstFailTime, ipAddr);
    }

    /**
     * 锁定用户账号，校验在15分钟内是否有连续5次的失败
     * 包括以下场景： 1、连续5次登录失败 2、连续5次验证码输入失败 3、连续5次手机尾号输入错误
     *
     * @param loginName 用户登录账号
     * @return 锁定结果
     */
    public void lockUser(String loginName, Date lockTime) {
        // 更新sysUser中字段
        List<IDbCondition> conds = new ArrayList<>();
        conds.add(new DC_E(DFN.sysUser__loginName, loginName));
        conds.add(new DC_E(DFN.common_isValid, true));

        List<UpdataData> datas = new ArrayList<>();
        datas.add(new UpdataData(DFN.sysUser_isLocked, true));
        datas.add(new UpdataData(DFN.sysUser_lockDueDate, lockTime));
        int c = sysUserDaoMongo.updateByConds(conds, datas);
    }

    private String getIpAddr() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(requestAttributes)) {
            return "";
        }
        HttpServletRequest request = requestAttributes.getRequest();
        return IpUtils.getIpAddr(request);
    }
}
