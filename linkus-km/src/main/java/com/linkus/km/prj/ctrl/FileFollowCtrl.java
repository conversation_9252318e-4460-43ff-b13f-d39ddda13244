package com.linkus.km.prj.ctrl;

import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.StringUtil;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.km.km.prd.service.IPrdColumnService;
import com.linkus.km.km.vo.FileFollowVo;
import com.linkus.km.prj.service.IFileFollowService;
import com.linkus.km.prj.view.FileView;
import com.linkus.km.prj.view.FollowCardVo;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping("/fileFollowCtrl")
public class FileFollowCtrl extends BaseCtrl{
	
	@Resource(name = "SysUserServiceImpl")
	private ISysUserService sysUserService;
	
	@Resource(name="fileFollowServiceImpl")
	private IFileFollowService fileFollowService;

	@Autowired
	private IPrdColumnService prdColumnService;

	@RequestMapping("/queryCollectionFile.action")
	@ResponseBody
	public void queryCollectionFile(@RequestBody FollowCardVo followCardVo) {
		ObjectId followCardId = followCardVo.getFollowCardId();
		// 知否-微视收藏区分知识库
		ObjectId fileBaseDefId = followCardVo.getFileBaseDefId();
		TeSysUser loginUser = geTeSysUser();
		List<FileView> list = fileFollowService.queryCollectionFile(loginUser.getId(), followCardId, fileBaseDefId);
		returnResult(list);
	}
	
	@RequestMapping("/queryAllFileOfUser.action")
	@ResponseBody
	public void queryAllFileOfUser(Integer pageIndex, Integer pageSize){
		TeSysUser loginUser = geTeSysUser();
		List<FileView> list = fileFollowService.queryAllFileOfUser(loginUser.getId(), pageIndex, pageSize);
		returnResult(list);
	}
	
	@RequestMapping("/addCollection.action")
	@ResponseBody
	public void addCollection(@RequestBody FollowCardVo followCardVo) {
		String fileId = followCardVo.getFileId();
		ObjectId fileObId = null;
		if (StringUtil.isNotNull(fileId)) {
			fileObId = new ObjectId(fileId);
		}
		ObjectId followCardId = followCardVo.getFollowCardId();
		// 知否-微视收藏区分知识库
		ObjectId fileBaseDefId = followCardVo.getFileBaseDefId();
		TeSysUser loginUser = geTeSysUser();
		try {
			fileFollowService.addCollection(fileObId, loginUser, followCardId, fileBaseDefId);
			this.returnResult("success");
		} catch (BaseException e) {
			this.returnResult("failed");
		} catch (Exception e) {
			this.returnResult("failed");
		}
	}
	
	@RequestMapping("/removeFollowFile.action")
	@ResponseBody
	public void removeFollowFile(@RequestBody FollowCardVo followCardVo){
		String fileId  = followCardVo.getFileId();
		ObjectId fileObId = null;
		if(StringUtil.isNotNull(fileId)){
			fileObId = new ObjectId(fileId);
		}
		ObjectId followCardId = followCardVo.getFollowCardId();
		// 知否-微视收藏区分知识库
		ObjectId fileBaseDefId = followCardVo.getFileBaseDefId();
		TeSysUser loginUser = geTeSysUser();
		fileFollowService.removeFollowFile(fileObId, loginUser,followCardId);
		List<FileView> list = fileFollowService.queryCollectionFile(loginUser.getId(),followCardId, fileBaseDefId);
		this.returnResult(list);
		
	}
	
	//获取当前用户登录信息
	private TeSysUser geTeSysUser(){
		String casLoginUserName = getCasLoginUser();
		TeSysUser loginUser = sysUserService
				.queryByLoginName(casLoginUserName);
		return loginUser;
	}


	//添加推荐
	@RequestMapping("/addRecommend.action")
	@ResponseBody
	public void addRecommend(@RequestParam ObjectId fileId, @RequestParam int lineNum){
		TeSysUser currentUser = geTeSysUser();
		fileFollowService.addRecommend(fileId,lineNum,currentUser);
	}


	@PostMapping("/addRecommendColumn.action")
	@ResponseBody
	public void addRecommendColumn(@RequestParam ObjectId columnId, @RequestParam int lineNum){
		TeSysUser currentUser = geTeSysUser();
		fileFollowService.addRecommendColumn(columnId,lineNum,currentUser);
	}

	//添加关注标签
	@RequestMapping("/addFocusTag.action")
	@ResponseBody
	public void addFocusTag(@RequestBody FileFollowVo fileFollowVo){
		TeSysUser currentUser = geTeSysUser();
		fileFollowService.addFocusTag(fileFollowVo, currentUser);
		returnResult(CommonResult.success());
	}

	//修改关注标签
	@RequestMapping("/updateFocusTag.action")
	@ResponseBody
	public void updateFocusTag(@RequestBody FileFollowVo fileFollowVo){
		TeSysUser currentUser = geTeSysUser();
		fileFollowService.updateFocusTag(fileFollowVo, currentUser);
		returnResult(CommonResult.success());
	}

	//获取关注标签
	@RequestMapping("/queryFocusTag.action")
	@ResponseBody
	public void queryFocusTag(String fileBaseId){
		if (StringUtil.isNull(fileBaseId)) {
			throw BusinessException.initExc("知识库fileBaseId为空！");
		}
		TeSysUser currentUser = geTeSysUser();
		if (StringUtil.isNull(currentUser) || StringUtil.isNull(currentUser.getId())) {
			throw BusinessException.initExc("获取当前登录用户失败");
		}
		this.returnResult(CommonResult.success(fileFollowService.queryFocusTag(currentUser,StringUtil.to(fileBaseId,ObjectId.class))));
	}

	//查询推荐
	@RequestMapping("/queryRecommend.action")
	@ResponseBody
	public void queryRecommend(){
		List<FileFollowVo> result = fileFollowService.queryRecommend();
		this.returnResult(result);
	}

	@GetMapping("/recommendsByType.action")
	@ResponseBody
	public void recommendsByType(String type){
		List<FileFollowVo> result = fileFollowService.queryRecommendByType(type);
		this.returnResult(result);
	}

	//删除修改推荐
	@RequestMapping("/deleteRecommend.action")
	@ResponseBody
	public void deleteRecommend(@RequestParam ObjectId id){
		fileFollowService.deleteRecommend(id);
		this.returnResult("success");
	}

	//查询用户是否为知识库管理员
	@RequestMapping("/queryIsKmBaseAdmin.action")
	@ResponseBody
	public void queryIsKmBaseAdmin(@RequestParam ObjectId id){
		TeSysUser currentUser = geTeSysUser();
		this.returnResult(prdColumnService.queryIsKmBaseAdmin(currentUser.getId(),id));
	}
	@RequestMapping("/queryIsKmBaseAdminByFileId.action")
	@ResponseBody
	public void queryIsKmBaseAdminByFileId(@RequestParam ObjectId fileId){
		TeSysUser currentUser = geTeSysUser();
		this.returnResult(prdColumnService.queryIsKmBaseAdminByFileId(currentUser.getId(),fileId));
	}

	@RequestMapping("/isCollectionFile.action")
	@ResponseBody
	public void isCollectionFile(@RequestBody FollowCardVo followCardVo) {
		ObjectId followCardId = followCardVo.getFollowCardId();
		// 知否-微视收藏区分知识库
		ObjectId fileBaseDefId = followCardVo.getFileBaseDefId();
		String fileId = followCardVo.getFileId();
		TeSysUser loginUser = geTeSysUser();
		boolean collectionFile = fileFollowService.isCollectionFile(loginUser.getId(), followCardId, fileBaseDefId, StringUtil.toObjectId(fileId));
		returnResult(collectionFile);
	}

	/**
	 * 查询标签
	 *
	 */
	@RequestMapping("/queryTags.action")
	@ResponseBody
	public void queryTags(@RequestParam boolean isAll, @RequestParam(required = false) String keyWord, @RequestParam(required = false) ObjectId fileBaseDefId){
		TeSysUser loginUser = geTeSysUser();
		returnResult(CommonResult.success(fileFollowService.queryTags(isAll, loginUser.getId(), keyWord, fileBaseDefId)));
	}
}
