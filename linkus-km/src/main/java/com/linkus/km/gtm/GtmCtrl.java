package com.linkus.km.gtm;

import com.linkus.base.aop.LogAnnotation;
import com.linkus.base.aop.OperationTypeEnum;
import com.linkus.base.constants.DMPSubsystem;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.DownloadCallback;
import com.linkus.base.util.FastDFSClient;
import com.linkus.base.util.StorePath;
import com.linkus.base.util.StringUtil;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.km.km.gto.model.Data;
import com.linkus.km.km.gto.model.ResultInfo;
import com.linkus.km.km.gto.model.vo.Param;
import com.linkus.km.km.gto.service.IGtmService;
import com.linkus.km.km.prd.service.IPrdViewService;
import com.linkus.km.km.service.IKnowledgePointService;
import com.linkus.km.km.service.IKnowledgeService;
import com.linkus.km.km.vo.KnowledgePointVo;
import com.linkus.km.prj.model.TeFile;
import com.linkus.km.prj.service.IKmFileService;
import com.linkus.km.util.AnalyzeFileTypeUtil;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.apache.commons.io.IOUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/gtm")
public class GtmCtrl extends BaseCtrl {

    @Autowired
    private IGtmService gtmService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IKmFileService kmFileService;

    @Autowired
    private IPrdViewService prdViewService;

    @Autowired
    private FastDFSClient fastDFSClient;

    @Autowired
    private IKnowledgeService knowledgeService;

    @Autowired
    private IKnowledgePointService knowledgePointService;


    @RequestMapping("/queryTempFile")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "查询temp目录信息", operation = OperationTypeEnum.QUERY)
    public void queryFolderFilesItf(@RequestParam String kmBaseName,@RequestParam String kmFolderFullPath){
        returnResult(CommonResult.success(gtmService.queryFolderFilesItf(kmBaseName,kmFolderFullPath)));
    }

    @RequestMapping("/queryFileByPath")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "获取文档及子目录信息的接口", operation = OperationTypeEnum.QUERY)
    public void queryFileByPath(@RequestParam String kmBaseName,@RequestParam String kmFolderFullPath){
        returnResult(CommonResult.success(gtmService.queryFileByPath(kmBaseName,kmFolderFullPath)));
    }

    @RequestMapping("/synchronizeFiles")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "文档库文件在不同文件夹中移动", operation = OperationTypeEnum.CREATE)
    public void copyFilesItf(@RequestBody Param param){
        List<ObjectId> fileIds = param.getFileIds();
        String kmFolderFullPath = param.getKmFolderFullPath();
        returnResult(CommonResult.success(gtmService.copyFilesItf(fileIds,kmFolderFullPath)));
    }

    @RequestMapping("/downloadFiles")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "文档库批量下载文件", operation = OperationTypeEnum.DOWNLOAD)
    public void downloadFilesItf(@RequestBody Param param,HttpServletResponse response) {
        ResultInfo resultInfo = new ResultInfo();
        Data data = new Data();
        resultInfo.setData(data);
        List<ObjectId> fileIds = param.getFileIds();
        if (fileIds.size() == 0){
            resultInfo.setCode(-1);
            data.setMessage("fileIds不能为空");
            returnResult(CommonResult.success(resultInfo));
        }
        List<TeFile> files = kmFileService.queryFileByIdList(fileIds);
        if (fileIds.size() != files.size()){
            resultInfo.setCode(-1);
            data.setMessage("文件数量与传人数量不相同");
            returnResult(CommonResult.success(resultInfo));
        }

        long timeStamp = System.currentTimeMillis();
        String fileName = "attachment_" + timeStamp + ".zip";
        try {
            kmFileService.downloadBizFiles4Itf(fileIds, fileName, param.isPdf(),response.getOutputStream());
        } catch ( IOException e){
            resultInfo.setCode(-1);
            data.setMessage("下载失败:"+e.getMessage());
        }
    }

    @RequestMapping("/downloadFile")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "文档库下载单文件", operation = OperationTypeEnum.DOWNLOAD)
    public void downloadFile(@RequestParam ObjectId id,HttpServletResponse response){
        ResultInfo resultInfo = new ResultInfo();
        Data data = new Data();
        resultInfo.setData(data);
        TeFile file = kmFileService.findFileById(id);
        if (file != null) {
            try {
                String fileName = file.getFileName();
                response.reset();
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Connection", "close");
                response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
                response.setContentType("application/octet-stream");
                final OutputStream output = response.getOutputStream();

                StorePath storePath = StorePath.parseFromUrl(file.getFilePath());
                fastDFSClient.downloadFile(storePath.getGroup()+ "/" + storePath.getPath(), new DownloadCallback() {

                    @Override
                    public Object recv(byte[] fileData) throws IOException {
                        InputStream ins = new ByteArrayInputStream(fileData);
                        IOUtils.copy(ins, output);
                        return null;
                    }
                });
            } catch (IOException e) {
                resultInfo.setCode(-1);
                data.setMessage("下载失败");
                returnResult(CommonResult.success(resultInfo));
            }
        } else {
            resultInfo.setCode(-1);
            data.setMessage("文件不存在");
            returnResult(CommonResult.success(resultInfo));
        }
    }

    @RequestMapping("/deleteFile")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "删除文档", operation = OperationTypeEnum.DELETE)
    public void deleteFile(@RequestParam ObjectId id) {
        List<ObjectId> viewIds = new ArrayList<ObjectId>();
        viewIds.add(StringUtil.toObjectId(id));
        prdViewService.deleteViewByIds(viewIds,true);
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(0);
        returnResult(CommonResult.success(resultInfo));
    }

    @RequestMapping("/uploadFile")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "文档上传到文档库", operation = OperationTypeEnum.UPLOAD)
    public void uploadFile(MultipartFile[] files,@RequestParam String kmBaseName,@RequestParam String author
            ,@RequestParam String kmFolderFullPath,@RequestParam(required = false) String fileDesc
            ,@RequestParam(required = false) List<String> fileTypeTagList,@RequestParam(required = false) String secret) {
        TeSysUser teSysUser = getCurrentTeSysUser();
        ResultInfo resultInfo = gtmService.uploadFile(files, kmBaseName, kmFolderFullPath,author,teSysUser,fileDesc,fileTypeTagList,secret);
        returnResult(CommonResult.success(resultInfo));
    }

    @RequestMapping("/getNewFolderTree.action")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "获取文档目录树状结构", operation = OperationTypeEnum.QUERY)
    public void getNewFolderTree(@RequestParam ObjectId kmBaseDefId,@RequestParam(required = false) ObjectId nodeId
            ,boolean isAuth,boolean isAdmin,boolean isAll,@RequestParam(required = false) Integer level){
        TeSysUser teSysUser = this.getCurrentTeSysUser();
        returnResult(knowledgeService.getNewFolderTree(kmBaseDefId,teSysUser,nodeId,isAuth,isAdmin,isAll,level));
    }

    @RequestMapping("/queryKnowledgeView.action")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "获取某个文件夹下的文档信息", operation = OperationTypeEnum.QUERY)
    public void queryKnowledgeView(@RequestBody KnowledgePointVo knowledgePointVo){
        TeSysUser teSysUser = this.getCurrentTeSysUser();
        Map<String,Object> list = knowledgePointService.queryKnowledgeView(knowledgePointVo,teSysUser);
        returnResult(list);
    }

    @RequestMapping("/previewFile")
    @ResponseBody
    @LogAnnotation(subsystem = DMPSubsystem.KMS, module = "下载ppt预览图片的接口", operation = OperationTypeEnum.DOWNLOAD)
    public void previewFile(@RequestParam String filePath, HttpServletResponse response){
        String extName = AnalyzeFileTypeUtil.analyzeFileTypeBySuffix(filePath);
        ResultInfo resultInfo = gtmService.previewFile(filePath, response);
        response.setHeader("Connection", "close");
        response.setHeader("Content-Disposition", "inline;");
        if(extName.equals("png")) {
            response.setContentType("image/png");
        } else if (extName.equals("jpg") || extName.equals("jpeg")) {
            response.setContentType("image/jpeg");
        } else {
            response.setContentType("application/octet-stream");
        }

        returnResult(CommonResult.success(resultInfo));
    }

    /**
     * 获取当前用户登录信息
     *
     */
    private TeSysUser getCurrentTeSysUser(){
        String casLoginUserName = getCasLoginUser();
        TeSysUser loginUser = sysUserService
                .queryByLoginName(casLoginUserName);
        return loginUser;
    }
}
