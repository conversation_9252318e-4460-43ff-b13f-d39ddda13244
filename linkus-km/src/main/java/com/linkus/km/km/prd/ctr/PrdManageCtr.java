package com.linkus.km.km.prd.ctr;

import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.exception.BaseException;
import com.linkus.base.response.BusinessException;
import com.linkus.base.response.CommonResult;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.base.util.excel.ExcelCellStyle;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.km.km.prd.model.vo.ParamVo;
import com.linkus.km.km.prd.model.vo.PrdFile;
import com.linkus.km.km.prd.model.vo.PrdFileAttrValueVo;
import com.linkus.km.km.prd.service.IPrdManageService;
import com.linkus.sys.model.SysDefTypeCodeName;
import com.linkus.sys.model.po.TeSysDef;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import com.linkus.sysuser.vo.FeedbackQueryParam;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 知否-管理
 * 
 */
@Controller
@RequestMapping("/prdManage")
public class PrdManageCtr extends BaseCtrl{
	

	@Autowired
	private ISysUserService sysUserService;
	
	@Autowired
	private IPrdManageService prdManageService;
	
	@Autowired
	private ISysDefService sysDefService;
	
	/**
	 * 查询待更新的文档
	 * 
	 */
	@RequestMapping("/queryFile2Update.action")
	@ResponseBody
	public void queryFileById(@RequestParam(value = "fileId", required = true) ObjectId fileId) {				
		returnResult(prdManageService.queryFile2Update(fileId));
	}
	
	/**
	 * 查询申请状态
	 */
	@RequestMapping("/getApplyStatus.action")
	@ResponseBody
	public void getApplyStatus() {
		List<TeSysDef> sysDefs = sysDefService.getSysByDefTypeCodeNameAndSrcDefCodeName(
				SysDefTypeCodeName.SYS_PARA_VALUE.getValue(), SysDefConstants.USERAPPLYSTATUS_CODENAME);	
		this.returnResult(sysDefs);
	}	
	
	/**
	 * 根据当前登录用户获取待审批/已审批的文档
	 * 
	 */
	@RequestMapping("/getFileApproveList.action")
	@ResponseBody
	public void getFileApproveList(@RequestBody ParamVo param){
		if (null == param.getApplyStatusId()) {
			throw new BaseException("审批状态Id不能为空");
		}
		TeSysUser loginUser = getCurrentTeSysUser();
		param.setUserId(loginUser.getId());
		returnResult(prdManageService.getFileApproveList(param));
	}
	
	/**
	 * 更新或者审批文档
	 * 
	 */
	@RequestMapping("/updateOrApproveFile.action")
	@ResponseBody
	public void updateOrApproveFile(@RequestBody PrdFileAttrValueVo fileVo){
		TeSysUser loginUser = getCurrentTeSysUser();
		prdManageService.updateOrApproveFile(fileVo, loginUser);
	}
	
	/**
	 * 批量审批文档
	 * 
	 */
	@RequestMapping("/batchApproveFiles.action")
	@ResponseBody
	public void batchApproveFiles(@RequestBody PrdFileAttrValueVo fileVo){
		TeSysUser loginUser = getCurrentTeSysUser();
		prdManageService.batchApproveFiles(fileVo.getApplyIds(),
				fileVo.getApprovalType(), fileVo.getApprovalDesc(), loginUser);
	}
	
	/**
	 * 根据当前登录用户获取待审批的文档总数
	 * 
	 */
	@RequestMapping("/getFileToApproveCount.action")
	@ResponseBody
	public void getFileToApproveCount(@RequestParam(required = true) ObjectId fileBaseId){
		if(StringUtil.isNull(fileBaseId)){
			throw BusinessException.initExc("知识库id不能为空");
		}
		TeSysUser loginUser = getCurrentTeSysUser();
		returnResult(prdManageService.getFileToApproveCount(loginUser,fileBaseId));
	}

	/**
	 * 根据当前登录用户待修改的文档
	 * 
	 */
	@RequestMapping("/getFileToModify.action")
	@ResponseBody
	public void getFileToModify(@RequestBody ParamVo param){
		TeSysUser loginUser = getCurrentTeSysUser();
		param.setUserId(loginUser.getId());
		returnResult(prdManageService.getFileToModify(param));
	}
	
	/**
	 * 获取文件审批状态
	 * 
	 */
	@RequestMapping("/getFileApprovalStatus.action")
	@ResponseBody
	public void getFileApprovalStatus(){
		returnResult(prdManageService.getFileApprovalStatus());
	}
	
	/**
	 * 批量审批文件/观点
	 */
	@RequestMapping("/batchApprovalFiles.action")
	@ResponseBody
	public void batchApprovalFiles(@RequestParam(value = "fileIdList[]", required = true) List<ObjectId> fileIdList,
								   String approvalType, String reviewDesc, @RequestParam(required = false) boolean oprtToken) {
		if (null == fileIdList) {
			throw new BaseException("审批的文件id集合不能为空");
		}
		if (null == approvalType) {
			throw new BaseException("审批类型不能为空");
		}
		TeSysUser loginUser = getCurrentTeSysUser();
		prdManageService.batchApprovalFiles(fileIdList, approvalType, reviewDesc, loginUser, null, oprtToken);
	}
	
	/**
	 * 根据用户获取的专栏观点清单
	 * 
	 */
	@RequestMapping("/getColumnViewListToApproval.action")
	@ResponseBody
	public void getColumnViewListToApproval(@RequestParam(required=false) String fileStatusId,
									  @RequestParam(required=false) String columnIds,
									  @RequestParam(value="uploadTime[]", required=false) List<String> uploadTime,
									  @RequestParam(required=false) String keyWord,
									  int pageNum, int pageSize, @RequestParam (required = false)ObjectId fileBaseDefId){
		TeSysUser loginUser = getCurrentTeSysUser();
		returnResult(prdManageService.getColumnViewListToApproval(fileStatusId,columnIds, uploadTime, keyWord, loginUser.getId(), pageNum, pageSize, fileBaseDefId));
	}
	
	/**
	 * 根据用户获取待审批的专栏观点清单
	 * 
	 */
	@RequestMapping("/getColumnViewListToApprovalCount.action")
	@ResponseBody
	public void getColumnViewListToApprovalCount(@RequestParam(required=false) ObjectId fileStatusId,@RequestParam(required = true) ObjectId fileBaseDefId){
		TeSysUser loginUser = getCurrentTeSysUser();
		returnResult(prdManageService.getColumnViewListToApprovalCount(fileStatusId,fileBaseDefId,loginUser.getId()));
	}
	
	/**
	 * 根据用户投诉明细
	 * 
	 */
	@RequestMapping("/getFeedbackInfoToApproval.action")
	@ResponseBody
	public void getFeedbackInfoToApproval(String feedbackId) {
		returnResult(prdManageService.getFeedbackInfoToApproval(new ObjectId(feedbackId)));
	}
	
	/**
	 * 根据用户获取观点、专栏下观点和文档的评论动态
	 * 
	 */
	@RequestMapping("/getUserRemarkDynamic.action")
	@ResponseBody
	public void getUserRemarkDynamic(@RequestBody ParamVo param){
		if (StringUtils.isEmpty(param.getFileBaseDefId())) {
			throw new BaseException("知识库ID为空！");
		}
		TeSysUser loginUser = getCurrentTeSysUser();
		returnResult(prdManageService.getUserRemarkDynamic(loginUser.getId(), param));
	}
	
	/**
	 *查询知否文件的点赞,浏览,下载记录
	 * 
	 */
	@RequestMapping("/getOperateDynamic.action")
	@ResponseBody
	public void getOperateDynamic(@RequestBody ParamVo param){
		returnResult(prdManageService.getOperateDynamic(param));
	}
	
	/**
	 * 查询知否文档,观点,专栏观点上传动态
	 * 
	 */
	@RequestMapping("/getUploadDynamic.action")
	@ResponseBody
	public void getUploadDynamic(@RequestBody ParamVo param){
		returnResult(prdManageService.getUploadDynamic(param));
	}

	/**
	 * 查询用户自定义点击记录
	 *
	 */
	@RequestMapping("/getUserCustParaPrdClickLog.action")
	@ResponseBody
	public void getUserCustParaPrdClickLog(@RequestBody ParamVo param){
		returnResult(prdManageService.getUserCustParaPrdClickLog(param));
	}
	
	/**
	 * 根据关键字查询人员
	 * 
	 */
	@RequestMapping("/getUsersByKeyWord.action")
	@ResponseBody
	public void getUsersByKeyWord(@RequestBody ParamVo param){
		returnResult(prdManageService.getUsersByKeyWord(param));
	}
	
	/**
	 * 根据bu查cc
	 * 
	 */
	@RequestMapping("/getCcByBu.action")
	@ResponseBody
	public void getCcByBu(String buCodeName, String keyWord){
		returnResult(prdManageService.getCcByBu(buCodeName, keyWord));
	}
	
	
	/**
	 *  根据上级经理获取员工(为成长值管理页面,上级人员控件定制的方法)
	 * 
	 */
	@RequestMapping("/getUsersByManager.action")
	@ResponseBody
	public void getUsersByManager(@RequestParam(required=false) String managerId){
		
		ObjectId userId = null;
		boolean isLoginUser = false;
		if (StringUtil.isNotNull(managerId)) {
			userId = StringUtil.toObjectId(managerId);
			isLoginUser = false;
		} else {
			TeSysUser loginUser = getCurrentTeSysUser();
			userId = loginUser.getId();
			isLoginUser = true;
		}
		
		returnResult(prdManageService.getUsersByManager(userId, isLoginUser));
	}
	
	/**
	 *  查询知否积分动态
	 * 
	 */
	@RequestMapping("/getTokenDynamic.action")
	@ResponseBody
	public void getTokenDynamic(@RequestBody ParamVo param){
		if (null == param) {
			throw new BaseException("param不能为空");
		}
		if (StringUtil.isNull(param.getManagerId())) {
			TeSysUser loginUser = getCurrentTeSysUser();
			param.setManagerId(loginUser.getId());
		}
		returnResult(prdManageService.getTokenDynamic(param));
	}

	@RequestMapping("/getAiEEStatistic.action")
	@ResponseBody
	public void getAiEEStatistic(@RequestBody ParamVo param){
		if (null == param) {
			throw new BaseException("param不能为空");
		}
		if (StringUtil.isNull(param.getManagerId())) {
			TeSysUser loginUser = getCurrentTeSysUser();
			param.setManagerId(loginUser.getId());
		}
		returnResult(prdManageService.getAiEEStatistic(param));
	}
	
	/**
	 *  导出知否积分动态
	 * 
	 */
	@RequestMapping("/exportTokenDynamic.action")
	@ResponseBody
	public void exportTokenDynamic(HttpServletRequest request){
		
		String managerId  = request.getParameter("managerId");
		String isRecursion = request.getParameter("isRecursion");
		String loginName = request.getParameter("loginName");
		String ccId = request.getParameter("ccId");
		String beginTime  = request.getParameter("beginTime");
		String endTime    = request.getParameter("endTime");
		String fileId 	   = request.getParameter("fileId");
		String fileBaseDefId = request.getParameter("fileBaseDefId");
		String pageIndex  = request.getParameter("pageIndex");
		String pageSize   = request.getParameter("pageSize");
		ParamVo param = new ParamVo();
		if (StringUtil.isNotNull(managerId)) {
			param.setManagerId(StringUtil.toObjectId(managerId));
		} else {
			TeSysUser loginUser = getCurrentTeSysUser();
			param.setManagerId(loginUser.getId());
		}
		if (StringUtil.isNotNull(isRecursion)) {
			param.setIsRecursion(StringUtil.toBoolean(isRecursion));
		}
		param.setLoginName(loginName);
		param.setCcId(ccId);
		param.setBeginTime(StringUtil.isNull(beginTime) ? null : beginTime);
		param.setEndTime(StringUtil.isNull(endTime) ? null : endTime);
		param.setFileIdValue(fileId);
		param.setFileBaseDefId(fileBaseDefId);
		param.setPageIndex(StringUtil.toInteger(pageIndex));
		param.setPageSize(StringUtil.toInteger(pageSize));
		PageBean data = prdManageService.exportTokenDynamic(param);
		ExcelUtils.exportSXSSFExcelDataWithoutDate(data, getResponse(), 1, null, null, 2, "知否部门使用统计");
	}

	@RequestMapping("/exportAiEEStatistic.action")
	@ResponseBody
	public void exportAiEEStatistic(HttpServletRequest request){

		String managerId  = request.getParameter("managerId");
		String isRecursion = request.getParameter("isRecursion");
		String loginName = request.getParameter("loginName");
		String ccId = request.getParameter("ccId");
		String beginTime  = request.getParameter("beginTime");
		String endTime    = request.getParameter("endTime");
		String fileId 	   = request.getParameter("fileId");
		String fileBaseDefId = request.getParameter("fileBaseDefId");
		String pageIndex  = request.getParameter("pageIndex");
		String pageSize   = request.getParameter("pageSize");
		ParamVo param = new ParamVo();
		if (StringUtil.isNotNull(managerId)) {
			param.setManagerId(StringUtil.toObjectId(managerId));
		} else {
			TeSysUser loginUser = getCurrentTeSysUser();
			param.setManagerId(loginUser.getId());
		}
		if (StringUtil.isNotNull(isRecursion)) {
			param.setIsRecursion(StringUtil.toBoolean(isRecursion));
		}
		param.setLoginName(loginName);
		param.setCcId(ccId);
		param.setBeginTime(StringUtil.isNull(beginTime) ? null : beginTime);
		param.setEndTime(StringUtil.isNull(endTime) ? null : endTime);
		param.setFileIdValue(fileId);
		param.setFileBaseDefId(fileBaseDefId);
		param.setPageIndex(StringUtil.toInteger(pageIndex));
		param.setPageSize(StringUtil.toInteger(pageSize));
		PageBean data = prdManageService.exportAiEEStatistic(param);
		ExcelUtils.exportSXSSFExcelDataWithoutDate(data, getResponse(), 1, null, null, 2, "知否部门使用统计");
	}
	
	/**
	 *  根据文件名关键字查询文件
	 * 
	 */
	@RequestMapping("/getFileByKeyWord.action")
	@ResponseBody
	public void getFileByKeyWord(@RequestBody ParamVo param){
		returnResult(prdManageService.getFileByKeyWord(param));
	}

	/**
	 * 类型，关键字查询文件
	 * @param param
	 */
	@PostMapping("/getFilesByKeyWordByType.action")
	@ResponseBody
	public void getFilesByKeyWordByType(@RequestBody ParamVo param){
		returnResult(prdManageService.getFileByKeyWordByType(param));
	}

	/**
	 *  查询反馈
	 * 
	 */
	@RequestMapping("/queryFeedback.action")
	@ResponseBody
	public void queryFeedback(@RequestBody FeedbackQueryParam param){
		returnResult(prdManageService.queryFeedback(param));
	}
	
	@RequestMapping("/exportOperateDynamic.action")
	@ResponseBody
	public void exportOperateDynamic(HttpServletRequest request) {
		//String clickTypeId = request.getParameter("clickTypeId");
		String uploadTime = request.getParameter("uploadTime");
		String loginName = request.getParameter("loginName");
		String keyWord = request.getParameter("keyWord");
		String fileBaseDefId = request.getParameter("fileBaseDefId");
		String srcClickValue = request.getParameter("srcClickValue");
		String pageIndex = request.getParameter("pageIndex");
		String pageSize = request.getParameter("pageSize");
		ParamVo param = new ParamVo();
		//param.setClickTypeId(clickTypeId);
		param.setUploadTime(StringUtil.transIds2List(uploadTime, ",", String.class));
		param.setLoginName(loginName);
		param.setKeyWord(keyWord);
		param.setFileBaseDefId(fileBaseDefId);
		param.setSrcClickValue(srcClickValue);
		param.setPageIndex(StringUtil.toInteger(pageIndex));
		param.setPageSize(StringUtil.toInteger(pageSize));
		
		LinkedHashMap<String,List<LinkedHashMap<String, Object>>> dataMap = 
				prdManageService.getAllOperateDynamicExcelData(param);
		LinkedHashMap<String, Map<Integer, LinkedHashMap<String, ExcelCellStyle>>> styleMap = 
				prdManageService.getAllOperateDynamicExcelStyle(dataMap);
		HSSFWorkbook workbook = ExcelUtils.getExcelDatas(dataMap, null, styleMap);
		String fileName = "知否系统管理数据";
		
		ExcelUtils.outputExcelData(workbook, fileName, getResponse());
	}

	@RequestMapping("/getKmBaseOption.action")
	@ResponseBody
	public void getKmBaseOption(){
		TeSysUser sysUser = getCurrentTeSysUser();
		returnResult(prdManageService.getKmBaseOption(sysUser.getId()));
	}

	/**
	 * 查询当前用户角色权限
	 */
	@RequestMapping("/queryUserRolePermissions.action")
	@ResponseBody
	public void queryUserRolePermissions() {
		TeSysUser loginUser = getCurrentTeSysUser();
		Map<String, Boolean> userRolePermissions = prdManageService.queryUserRolePermissions(loginUser);
		returnResult(userRolePermissions);
	}
	
	/**
	 *	获取当前用户登录信息
	 */
	private TeSysUser getCurrentTeSysUser(){
		String casLoginUserName = getCasLoginUser();
		TeSysUser loginUser = sysUserService
				.queryByLoginName(casLoginUserName);
		return loginUser;
	}

	@RequestMapping("/queryColumnByKeyWord.action")
	@ResponseBody
	public CommonResult<List<TeSysDef>> queryColumnByKeyWord(@RequestParam(value = "fileBaseDefId",required = true)ObjectId fileBaseDefId) {
		List<TeSysDef> sysDefList = prdManageService.queryColumnByKeyWord(fileBaseDefId);
		return CommonResult.success(sysDefList);
	}
	@RequestMapping("/updateViewById.action")
	@ResponseBody
	public CommonResult<Void> updateViewById(@RequestBody PrdFile prdFile) {
		prdManageService.updateViewById(prdFile);
		return CommonResult.success();
	}
}
