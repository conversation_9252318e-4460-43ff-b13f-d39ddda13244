!function(e,f){"object"==typeof exports&&"undefined"!=typeof module?f(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],f):f(e.bootstrap={},e.j<PERSON><PERSON>y,e.<PERSON><PERSON>)}(this,function(e,f,k){function m(g,d){for(var a=0;a<d.length;a++){var b=d[a];b.enumerable=b.enumerable||!1;b.configurable=!0;"value"in b&&(b.writable=!0);Object.defineProperty(g,b.key,b)}}function t(g,d,a){return d&&m(g.prototype,d),a&&m(g,a),g}function p(g){for(var d=
1;d<arguments.length;d++){var a=null!=arguments[d]?arguments[d]:{},b=Object.keys(a);"function"==typeof Object.getOwnPropertySymbols&&(b=b.concat(Object.getOwnPropertySymbols(a).filter(function(b){return Object.getOwnPropertyDescriptor(a,b).enumerable})));b.forEach(function(b){var c;c=a[b];b in g?Object.defineProperty(g,b,{value:c,enumerable:!0,configurable:!0,writable:!0}):g[b]=c})}return g}f=f&&f.hasOwnProperty("default")?f.default:f;k=k&&k.hasOwnProperty("default")?k.default:k;var n,F,x,C,y,K,H,
z,h,aa,D,ba,I,L,v,ca,U,Y,M,N,u,da,ea,E,fa,ga,J,l,ha,V,ia,A,O,q,ja,ka,la,ma,na,oa,S,G,pa,qa,ra,sa,ta,T,B,ua,Z,va,W,P,w,wa,Q,R,r=function(g){var d={TRANSITION_END:"bsTransitionEnd",getUID:function(a){for(;a+=~~(1E6*Math.random()),document.getElementById(a););return a},getSelectorFromElement:function(a){var b=a.getAttribute("data-target");b&&"#"!==b||(b=a.getAttribute("href")||"");try{return document.querySelector(b)?b:null}catch(c){return null}},getTransitionDurationFromElement:function(a){if(!a)return 0;
a=g(a).css("transition-duration");return parseFloat(a)?(a=a.split(",")[0],1E3*parseFloat(a)):0},reflow:function(a){return a.offsetHeight},triggerTransitionEnd:function(a){g(a).trigger("transitionend")},supportsTransitionEnd:function(){return!0},isElement:function(a){return(a[0]||a).nodeType},typeCheckConfig:function(a,b,c){for(var g in c)if(Object.prototype.hasOwnProperty.call(c,g)){var h=c[g],e=b[g],e=e&&d.isElement(e)?"element":(f=e,{}.toString.call(f).match(/\s([a-z]+)/i)[1].toLowerCase());if(!(new RegExp(h)).test(e))throw Error(a.toUpperCase()+
': Option "'+g+'" provided type "'+e+'" but expected type "'+h+'".');}var f}};return g.fn.emulateTransitionEnd=function(a){var b=this,c=!1;return g(this).one(d.TRANSITION_END,function(){c=!0}),setTimeout(function(){c||d.triggerTransitionEnd(b)},a),this},g.event.special[d.TRANSITION_END]={bindType:"transitionend",delegateType:"transitionend",handle:function(a){if(g(a.target).is(this))return a.handleObj.handler.apply(this,arguments)}},d}(f),xa=(F=(n=f).fn.alert,x={CLOSE:"close.bs.alert",CLOSED:"closed.bs.alert",
CLICK_DATA_API:"click.bs.alert.data-api"},C=function(){function g(a){this._element=a}var d=g.prototype;return d.close=function(a){var b=this._element;a&&(b=this._getRootElement(a));this._triggerCloseEvent(b).isDefaultPrevented()||this._removeElement(b)},d.dispose=function(){n.removeData(this._element,"bs.alert");this._element=null},d._getRootElement=function(a){var b=r.getSelectorFromElement(a),c=!1;return b&&(c=document.querySelector(b)),c||(c=n(a).closest(".alert")[0]),c},d._triggerCloseEvent=function(a){var b=
n.Event(x.CLOSE);return n(a).trigger(b),b},d._removeElement=function(a){var b=this;if(n(a).removeClass("show"),n(a).hasClass("fade")){var c=r.getTransitionDurationFromElement(a);n(a).one(r.TRANSITION_END,function(c){return b._destroyElement(a,c)}).emulateTransitionEnd(c)}else this._destroyElement(a)},d._destroyElement=function(a){n(a).detach().trigger(x.CLOSED).remove()},g._jQueryInterface=function(a){return this.each(function(){var b=n(this),c=b.data("bs.alert");c||(c=new g(this),b.data("bs.alert",
c));"close"===a&&c[a](this)})},g._handleDismiss=function(a){return function(b){b&&b.preventDefault();a.close(this)}},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),g}(),n(document).on(x.CLICK_DATA_API,'[data-dismiss\x3d"alert"]',C._handleDismiss(new C)),n.fn.alert=C._jQueryInterface,n.fn.alert.Constructor=C,n.fn.alert.noConflict=function(){return n.fn.alert=F,C._jQueryInterface},C),ya=(K=(y=f).fn.button,H={CLICK_DATA_API:"click.bs.button.data-api",FOCUS_BLUR_DATA_API:"focus.bs.button.data-api blur.bs.button.data-api"},
z=function(){function g(a){this._element=a}var d=g.prototype;return d.toggle=function(){var a=!0,b=!0,c=y(this._element).closest('[data-toggle\x3d"buttons"]')[0];if(c){var d=this._element.querySelector("input");if(d){"radio"===d.type&&(d.checked&&this._element.classList.contains("active")?a=!1:(b=c.querySelector(".active"))&&y(b).removeClass("active"));if(a){if(d.hasAttribute("disabled")||c.hasAttribute("disabled")||d.classList.contains("disabled")||c.classList.contains("disabled"))return;d.checked=
!this._element.classList.contains("active");y(d).trigger("change")}d.focus();b=!1}}b&&this._element.setAttribute("aria-pressed",!this._element.classList.contains("active"));a&&y(this._element).toggleClass("active")},d.dispose=function(){y.removeData(this._element,"bs.button");this._element=null},g._jQueryInterface=function(a){return this.each(function(){var b=y(this).data("bs.button");b||(b=new g(this),y(this).data("bs.button",b));"toggle"===a&&b[a]()})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),
g}(),y(document).on(H.CLICK_DATA_API,'[data-toggle^\x3d"button"]',function(g){g.preventDefault();g=g.target;y(g).hasClass("btn")||(g=y(g).closest(".btn"));z._jQueryInterface.call(y(g),"toggle")}).on(H.FOCUS_BLUR_DATA_API,'[data-toggle^\x3d"button"]',function(g){var d=y(g.target).closest(".btn")[0];y(d).toggleClass("focus",/^focus(in)?$/.test(g.type))}),y.fn.button=z._jQueryInterface,y.fn.button.Constructor=z,y.fn.button.noConflict=function(){return y.fn.button=K,z._jQueryInterface},z);H=(aa=(h=f).fn.carousel,
D={interval:5E3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},ba={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},I={SLIDE:"slide.bs.carousel",SLID:"slid.bs.carousel",KEYDOWN:"keydown.bs.carousel",MOUSEENTER:"mouseenter.bs.carousel",MOUSELEAVE:"mouseleave.bs.carousel",TOUCHEND:"touchend.bs.carousel",LOAD_DATA_API:"load.bs.carousel.data-api",CLICK_DATA_API:"click.bs.carousel.data-api"},L=function(){function g(a,b){this._activeElement=this._interval=
this._items=null;this._isSliding=this._isPaused=!1;this.touchTimeout=null;this._config=this._getConfig(b);this._element=h(a)[0];this._indicatorsElement=this._element.querySelector(".carousel-indicators");this._addEventListeners()}var d=g.prototype;return d.next=function(){this._isSliding||this._slide("next")},d.nextWhenVisible=function(){!document.hidden&&h(this._element).is(":visible")&&"hidden"!==h(this._element).css("visibility")&&this.next()},d.prev=function(){this._isSliding||this._slide("prev")},
d.pause=function(a){a||(this._isPaused=!0);this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(r.triggerTransitionEnd(this._element),this.cycle(!0));clearInterval(this._interval);this._interval=null},d.cycle=function(a){a||(this._isPaused=!1);this._interval&&(clearInterval(this._interval),this._interval=null);this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},d.to=function(a){var b=
this;this._activeElement=this._element.querySelector(".active.carousel-item");var c=this._getItemIndex(this._activeElement);if(!(a>this._items.length-1||0>a))if(this._isSliding)h(this._element).one(I.SLID,function(){return b.to(a)});else{if(c===a)return this.pause(),void this.cycle();this._slide(c<a?"next":"prev",this._items[a])}},d.dispose=function(){h(this._element).off(".bs.carousel");h.removeData(this._element,"bs.carousel");this._indicatorsElement=this._activeElement=this._isSliding=this._isPaused=
this._interval=this._element=this._config=this._items=null},d._getConfig=function(a){return a=p({},D,a),r.typeCheckConfig("carousel",a,ba),a},d._addEventListeners=function(){var a=this;this._config.keyboard&&h(this._element).on(I.KEYDOWN,function(b){return a._keydown(b)});"hover"===this._config.pause&&(h(this._element).on(I.MOUSEENTER,function(b){return a.pause(b)}).on(I.MOUSELEAVE,function(b){return a.cycle(b)}),"ontouchstart"in document.documentElement&&h(this._element).on(I.TOUCHEND,function(){a.pause();
a.touchTimeout&&clearTimeout(a.touchTimeout);a.touchTimeout=setTimeout(function(b){return a.cycle(b)},500+a._config.interval)}))},d._keydown=function(a){if(!/input|textarea/i.test(a.target.tagName))switch(a.which){case 37:a.preventDefault();this.prev();break;case 39:a.preventDefault(),this.next()}},d._getItemIndex=function(a){return this._items=a&&a.parentNode?[].slice.call(a.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(a)},d._getItemByDirection=function(a,b){var c="next"===
a,d="prev"===a,g=this._getItemIndex(b),h=this._items.length-1;if((d&&0===g||c&&g===h)&&!this._config.wrap)return b;a=(g+("prev"===a?-1:1))%this._items.length;return-1===a?this._items[this._items.length-1]:this._items[a]},d._triggerSlideEvent=function(a,b){var c=this._getItemIndex(a),d=this._getItemIndex(this._element.querySelector(".active.carousel-item"));a=h.Event(I.SLIDE,{relatedTarget:a,direction:b,from:d,to:c});return h(this._element).trigger(a),a},d._setActiveIndicatorElement=function(a){if(this._indicatorsElement){var b=
[].slice.call(this._indicatorsElement.querySelectorAll(".active"));h(b).removeClass("active");(a=this._indicatorsElement.children[this._getItemIndex(a)])&&h(a).addClass("active")}},d._slide=function(a,b){var c,d,g,e=this,f=this._element.querySelector(".active.carousel-item"),D=this._getItemIndex(f),l=b||f&&this._getItemByDirection(a,f),m=this._getItemIndex(l);b=!!this._interval;if("next"===a?(c="carousel-item-left",d="carousel-item-next",g="left"):(c="carousel-item-right",d="carousel-item-prev",g=
"right"),l&&h(l).hasClass("active"))this._isSliding=!1;else if(!this._triggerSlideEvent(l,g).isDefaultPrevented()&&f&&l){this._isSliding=!0;b&&this.pause();this._setActiveIndicatorElement(l);var k=h.Event(I.SLID,{relatedTarget:l,direction:g,from:D,to:m});h(this._element).hasClass("slide")?(h(l).addClass(d),r.reflow(l),h(f).addClass(c),h(l).addClass(c),a=r.getTransitionDurationFromElement(f),h(f).one(r.TRANSITION_END,function(){h(l).removeClass(c+" "+d).addClass("active");h(f).removeClass("active "+
d+" "+c);e._isSliding=!1;setTimeout(function(){return h(e._element).trigger(k)},0)}).emulateTransitionEnd(a)):(h(f).removeClass("active"),h(l).addClass("active"),this._isSliding=!1,h(this._element).trigger(k));b&&this.cycle()}},g._jQueryInterface=function(a){return this.each(function(){var b=h(this).data("bs.carousel"),c=p({},D,h(this).data());"object"==typeof a&&(c=p({},c,a));var d="string"==typeof a?a:c.slide;if(b||(b=new g(this,c),h(this).data("bs.carousel",b)),"number"==typeof a)b.to(a);else if("string"==
typeof d){if("undefined"==typeof b[d])throw new TypeError('No method named "'+d+'"');b[d]()}else c.interval&&(b.pause(),b.cycle())})},g._dataApiClickHandler=function(a){var b=r.getSelectorFromElement(this);if(b&&(b=h(b)[0])&&h(b).hasClass("carousel")){var c=p({},h(b).data(),h(this).data()),d=this.getAttribute("data-slide-to");d&&(c.interval=!1);g._jQueryInterface.call(h(b),c);d&&h(b).data("bs.carousel").to(d);a.preventDefault()}},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",
get:function(){return D}}]),g}(),h(document).on(I.CLICK_DATA_API,"[data-slide], [data-slide-to]",L._dataApiClickHandler),h(window).on(I.LOAD_DATA_API,function(){for(var g=[].slice.call(document.querySelectorAll('[data-ride\x3d"carousel"]')),d=0,a=g.length;d<a;d++){var b=h(g[d]);L._jQueryInterface.call(b,b.data())}}),h.fn.carousel=L._jQueryInterface,h.fn.carousel.Constructor=L,h.fn.carousel.noConflict=function(){return h.fn.carousel=aa,L._jQueryInterface},L);var za=(ca=(v=f).fn.collapse,U={toggle:!0,
parent:""},Y={toggle:"boolean",parent:"(string|element)"},M={SHOW:"show.bs.collapse",SHOWN:"shown.bs.collapse",HIDE:"hide.bs.collapse",HIDDEN:"hidden.bs.collapse",CLICK_DATA_API:"click.bs.collapse.data-api"},N=function(){function g(a,b){this._isTransitioning=!1;this._element=a;this._config=this._getConfig(b);this._triggerArray=v.makeArray(document.querySelectorAll('[data-toggle\x3d"collapse"][href\x3d"#'+a.id+'"],[data-toggle\x3d"collapse"][data-target\x3d"#'+a.id+'"]'));b=[].slice.call(document.querySelectorAll('[data-toggle\x3d"collapse"]'));
for(var c=0,d=b.length;c<d;c++){var g=b[c],h=r.getSelectorFromElement(g),f=[].slice.call(document.querySelectorAll(h)).filter(function(b){return b===a});null!==h&&0<f.length&&(this._selector=h,this._triggerArray.push(g))}this._parent=this._config.parent?this._getParent():null;this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray);this._config.toggle&&this.toggle()}var d=g.prototype;return d.toggle=function(){v(this._element).hasClass("show")?this.hide():this.show()},
d.show=function(){var a,b,c=this;if(!(this._isTransitioning||v(this._element).hasClass("show")||(this._parent&&0===(a=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(a){return a.getAttribute("data-parent")===c._config.parent})).length&&(a=null),a&&(b=v(a).not(this._selector).data("bs.collapse"))&&b._isTransitioning))){var d=v.Event(M.SHOW);if(v(this._element).trigger(d),!d.isDefaultPrevented()){a&&(g._jQueryInterface.call(v(a).not(this._selector),"hide"),b||v(a).data("bs.collapse",
null));var h=this._getDimension();v(this._element).removeClass("collapse").addClass("collapsing");this._element.style[h]=0;this._triggerArray.length&&v(this._triggerArray).removeClass("collapsed").attr("aria-expanded",!0);this.setTransitioning(!0);a="scroll"+(h[0].toUpperCase()+h.slice(1));b=r.getTransitionDurationFromElement(this._element);v(this._element).one(r.TRANSITION_END,function(){v(c._element).removeClass("collapsing").addClass("collapse").addClass("show");c._element.style[h]="";c.setTransitioning(!1);
v(c._element).trigger(M.SHOWN)}).emulateTransitionEnd(b);this._element.style[h]=this._element[a]+"px"}}},d.hide=function(){var a=this;if(!this._isTransitioning&&v(this._element).hasClass("show")){var b=v.Event(M.HIDE);if(v(this._element).trigger(b),!b.isDefaultPrevented()){b=this._getDimension();this._element.style[b]=this._element.getBoundingClientRect()[b]+"px";r.reflow(this._element);v(this._element).addClass("collapsing").removeClass("collapse").removeClass("show");var c=this._triggerArray.length;
if(0<c)for(var d=0;d<c;d++){var g=this._triggerArray[d],h=r.getSelectorFromElement(g);null!==h&&(v([].slice.call(document.querySelectorAll(h))).hasClass("show")||v(g).addClass("collapsed").attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[b]="";b=r.getTransitionDurationFromElement(this._element);v(this._element).one(r.TRANSITION_END,function(){a.setTransitioning(!1);v(a._element).removeClass("collapsing").addClass("collapse").trigger(M.HIDDEN)}).emulateTransitionEnd(b)}}},d.setTransitioning=
function(a){this._isTransitioning=a},d.dispose=function(){v.removeData(this._element,"bs.collapse");this._isTransitioning=this._triggerArray=this._element=this._parent=this._config=null},d._getConfig=function(a){return(a=p({},U,a)).toggle=!!a.toggle,r.typeCheckConfig("collapse",a,Y),a},d._getDimension=function(){return v(this._element).hasClass("width")?"width":"height"},d._getParent=function(){var a=this,b=null;r.isElement(this._config.parent)?(b=this._config.parent,"undefined"!=typeof this._config.parent.jquery&&
(b=this._config.parent[0])):b=document.querySelector(this._config.parent);var c=[].slice.call(b.querySelectorAll('[data-toggle\x3d"collapse"][data-parent\x3d"'+this._config.parent+'"]'));return v(c).each(function(b,c){a._addAriaAndCollapsedClass(g._getTargetFromElement(c),[c])}),b},d._addAriaAndCollapsedClass=function(a,b){a&&(a=v(a).hasClass("show"),b.length&&v(b).toggleClass("collapsed",!a).attr("aria-expanded",a))},g._getTargetFromElement=function(a){return(a=r.getSelectorFromElement(a))?document.querySelector(a):
null},g._jQueryInterface=function(a){return this.each(function(){var b=v(this),c=b.data("bs.collapse"),d=p({},U,b.data(),"object"==typeof a&&a?a:{});if(!c&&d.toggle&&/show|hide/.test(a)&&(d.toggle=!1),c||(c=new g(this,d),b.data("bs.collapse",c)),"string"==typeof a){if("undefined"==typeof c[a])throw new TypeError('No method named "'+a+'"');c[a]()}})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return U}}]),g}(),v(document).on(M.CLICK_DATA_API,'[data-toggle\x3d"collapse"]',
function(g){"A"===g.currentTarget.tagName&&g.preventDefault();var d=v(this);g=r.getSelectorFromElement(this);g=[].slice.call(document.querySelectorAll(g));v(g).each(function(){var a=v(this),b=a.data("bs.collapse")?"toggle":d.data();N._jQueryInterface.call(a,b)})}),v.fn.collapse=N._jQueryInterface,v.fn.collapse.Constructor=N,v.fn.collapse.noConflict=function(){return v.fn.collapse=ca,N._jQueryInterface},N),Aa=(da=(u=f).fn.dropdown,ea=/38|40|27/,E={HIDE:"hide.bs.dropdown",HIDDEN:"hidden.bs.dropdown",
SHOW:"show.bs.dropdown",SHOWN:"shown.bs.dropdown",CLICK:"click.bs.dropdown",CLICK_DATA_API:"click.bs.dropdown.data-api",KEYDOWN_DATA_API:"keydown.bs.dropdown.data-api",KEYUP_DATA_API:"keyup.bs.dropdown.data-api"},fa={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic"},ga={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string"},J=function(){function g(a,b){this._element=a;this._popper=null;this._config=this._getConfig(b);
this._menu=this._getMenuElement();this._inNavbar=this._detectNavbar();this._addEventListeners()}var d=g.prototype;return d.toggle=function(){if(!this._element.disabled&&!u(this._element).hasClass("disabled")){var a=g._getParentFromElement(this._element),b=u(this._menu).hasClass("show");if(g._clearMenus(),!b){var b={relatedTarget:this._element},c=u.Event(E.SHOW,b);if(u(a).trigger(c),!c.isDefaultPrevented()){if(!this._inNavbar){if("undefined"==typeof k)throw new TypeError("Bootstrap dropdown require Popper.js (https://popper.js.org)");
c=this._element;"parent"===this._config.reference?c=a:r.isElement(this._config.reference)&&(c=this._config.reference,"undefined"!=typeof this._config.reference.jquery&&(c=this._config.reference[0]));"scrollParent"!==this._config.boundary&&u(a).addClass("position-static");this._popper=new k(c,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===u(a).closest(".navbar-nav").length&&u(document.body).children().on("mouseover",null,u.noop);this._element.focus();this._element.setAttribute("aria-expanded",
!0);u(this._menu).toggleClass("show");u(a).toggleClass("show").trigger(u.Event(E.SHOWN,b))}}}},d.dispose=function(){u.removeData(this._element,"bs.dropdown");u(this._element).off(".bs.dropdown");this._element=null;(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},d.update=function(){this._inNavbar=this._detectNavbar();null!==this._popper&&this._popper.scheduleUpdate()},d._addEventListeners=function(){var a=this;u(this._element).on(E.CLICK,function(b){b.preventDefault();
b.stopPropagation();a.toggle()})},d._getConfig=function(a){return a=p({},this.constructor.Default,u(this._element).data(),a),r.typeCheckConfig("dropdown",a,this.constructor.DefaultType),a},d._getMenuElement=function(){if(!this._menu){var a=g._getParentFromElement(this._element);a&&(this._menu=a.querySelector(".dropdown-menu"))}return this._menu},d._getPlacement=function(){var a=u(this._element.parentNode),b="bottom-start";return a.hasClass("dropup")?(b="top-start",u(this._menu).hasClass("dropdown-menu-right")&&
(b="top-end")):a.hasClass("dropright")?b="right-start":a.hasClass("dropleft")?b="left-start":u(this._menu).hasClass("dropdown-menu-right")&&(b="bottom-end"),b},d._detectNavbar=function(){return 0<u(this._element).closest(".navbar").length},d._getPopperConfig=function(){var a=this,b={};"function"==typeof this._config.offset?b.fn=function(b){return b.offsets=p({},b.offsets,a._config.offset(b.offsets)||{}),b}:b.offset=this._config.offset;b={placement:this._getPlacement(),modifiers:{offset:b,flip:{enabled:this._config.flip},
preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(b.modifiers.applyStyle={enabled:!1}),b},g._jQueryInterface=function(a){return this.each(function(){var b=u(this).data("bs.dropdown");if(b||(b=new g(this,"object"==typeof a?a:null),u(this).data("bs.dropdown",b)),"string"==typeof a){if("undefined"==typeof b[a])throw new TypeError('No method named "'+a+'"');b[a]()}})},g._clearMenus=function(a){if(!a||3!==a.which&&("keyup"!==a.type||9===a.which))for(var b=
[].slice.call(document.querySelectorAll('[data-toggle\x3d"dropdown"]')),c=0,d=b.length;c<d;c++){var h=g._getParentFromElement(b[c]),f=u(b[c]).data("bs.dropdown"),e={relatedTarget:b[c]};if(a&&"click"===a.type&&(e.clickEvent=a),f)if(f=f._menu,u(h).hasClass("show")&&!(a&&("click"===a.type&&/input|textarea/i.test(a.target.tagName)||"keyup"===a.type&&9===a.which)&&u.contains(h,a.target))){var l=u.Event(E.HIDE,e);u(h).trigger(l);l.isDefaultPrevented()||("ontouchstart"in document.documentElement&&u(document.body).children().off("mouseover",
null,u.noop),b[c].setAttribute("aria-expanded","false"),u(f).removeClass("show"),u(h).removeClass("show").trigger(u.Event(E.HIDDEN,e)))}}},g._getParentFromElement=function(a){var b,c=r.getSelectorFromElement(a);return c&&(b=document.querySelector(c)),b||a.parentNode},g._dataApiKeydownHandler=function(a){if(!((/input|textarea/i.test(a.target.tagName)?32===a.which||27!==a.which&&(40!==a.which&&38!==a.which||u(a.target).closest(".dropdown-menu").length):!ea.test(a.which))||(a.preventDefault(),a.stopPropagation(),
this.disabled||u(this).hasClass("disabled")))){var b=g._getParentFromElement(this),c=u(b).hasClass("show");(c||27===a.which&&32===a.which)&&(!c||27!==a.which&&32!==a.which)?(b=[].slice.call(b.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")),0!==b.length&&(c=b.indexOf(a.target),38===a.which&&0<c&&c--,40===a.which&&c<b.length-1&&c++,0>c&&(c=0),b[c].focus())):(27===a.which&&(a=b.querySelector('[data-toggle\x3d"dropdown"]'),u(a).trigger("focus")),u(this).trigger("click"))}},
t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return fa}},{key:"DefaultType",get:function(){return ga}}]),g}(),u(document).on(E.KEYDOWN_DATA_API,'[data-toggle\x3d"dropdown"]',J._dataApiKeydownHandler).on(E.KEYDOWN_DATA_API,".dropdown-menu",J._dataApiKeydownHandler).on(E.CLICK_DATA_API+" "+E.KEYUP_DATA_API,J._clearMenus).on(E.CLICK_DATA_API,'[data-toggle\x3d"dropdown"]',function(g){g.preventDefault();g.stopPropagation();J._jQueryInterface.call(u(this),"toggle")}).on(E.CLICK_DATA_API,
".dropdown form",function(g){g.stopPropagation()}),u.fn.dropdown=J._jQueryInterface,u.fn.dropdown.Constructor=J,u.fn.dropdown.noConflict=function(){return u.fn.dropdown=da,J._jQueryInterface},J),Ba=(ha=(l=f).fn.modal,V={backdrop:!0,keyboard:!0,focus:!0,show:!0},ia={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},A={HIDE:"hide.bs.modal",HIDDEN:"hidden.bs.modal",SHOW:"show.bs.modal",SHOWN:"shown.bs.modal",FOCUSIN:"focusin.bs.modal",RESIZE:"resize.bs.modal",CLICK_DISMISS:"click.dismiss.bs.modal",
KEYDOWN_DISMISS:"keydown.dismiss.bs.modal",MOUSEUP_DISMISS:"mouseup.dismiss.bs.modal",MOUSEDOWN_DISMISS:"mousedown.dismiss.bs.modal",CLICK_DATA_API:"click.bs.modal.data-api"},O=function(){function g(a,b){this._config=this._getConfig(b);this._element=a;this._dialog=a.querySelector(".modal-dialog");this._backdrop=null;this._ignoreBackdropClick=this._isBodyOverflowing=this._isShown=!1;this._scrollbarWidth=0}var d=g.prototype;return d.toggle=function(a){return this._isShown?this.hide():this.show(a)},
d.show=function(a){var b=this;if(!this._isTransitioning&&!this._isShown){l(this._element).hasClass("fade")&&(this._isTransitioning=!0);var c=l.Event(A.SHOW,{relatedTarget:a});l(this._element).trigger(c);this._isShown||c.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),l(document.body).addClass("modal-open"),this._setEscapeEvent(),this._setResizeEvent(),l(this._element).on(A.CLICK_DISMISS,'[data-dismiss\x3d"modal"]',function(a){return b.hide(a)}),
l(this._dialog).on(A.MOUSEDOWN_DISMISS,function(){l(b._element).one(A.MOUSEUP_DISMISS,function(a){l(a.target).is(b._element)&&(b._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return b._showElement(a)}))}},d.hide=function(a){var b=this;if(a&&a.preventDefault(),!this._isTransitioning&&this._isShown)if(a=l.Event(A.HIDE),l(this._element).trigger(a),this._isShown&&!a.isDefaultPrevented())this._isShown=!1,a=l(this._element).hasClass("fade"),(a&&(this._isTransitioning=!0),this._setEscapeEvent(),
this._setResizeEvent(),l(document).off(A.FOCUSIN),l(this._element).removeClass("show"),l(this._element).off(A.CLICK_DISMISS),l(this._dialog).off(A.MOUSEDOWN_DISMISS),a)?(a=r.getTransitionDurationFromElement(this._element),l(this._element).one(r.TRANSITION_END,function(a){return b._hideModal(a)}).emulateTransitionEnd(a)):this._hideModal()},d.dispose=function(){l.removeData(this._element,"bs.modal");l(window,document,this._element,this._backdrop).off(".bs.modal");this._scrollbarWidth=this._ignoreBackdropClick=
this._isBodyOverflowing=this._isShown=this._backdrop=this._dialog=this._element=this._config=null},d.handleUpdate=function(){this._adjustDialog()},d._getConfig=function(a){return a=p({},V,a),r.typeCheckConfig("modal",a,ia),a},d._showElement=function(a){var b=this,c=l(this._element).hasClass("fade");this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element);this._element.style.display="block";this._element.removeAttribute("aria-hidden");
this._element.scrollTop=0;c&&r.reflow(this._element);l(this._element).addClass("show");this._config.focus&&this._enforceFocus();var d=l.Event(A.SHOWN,{relatedTarget:a});a=function(){b._config.focus&&b._element.focus();b._isTransitioning=!1;l(b._element).trigger(d)};c?(c=r.getTransitionDurationFromElement(this._element),l(this._dialog).one(r.TRANSITION_END,a).emulateTransitionEnd(c)):a()},d._enforceFocus=function(){var a=this;l(document).off(A.FOCUSIN).on(A.FOCUSIN,function(b){document!==b.target&&
a._element!==b.target&&0===l(a._element).has(b.target).length&&a._element.focus()})},d._setEscapeEvent=function(){var a=this;this._isShown&&this._config.keyboard?l(this._element).on(A.KEYDOWN_DISMISS,function(b){27===b.which&&(b.preventDefault(),a.hide())}):this._isShown||l(this._element).off(A.KEYDOWN_DISMISS)},d._setResizeEvent=function(){var a=this;this._isShown?l(window).on(A.RESIZE,function(b){return a.handleUpdate(b)}):l(window).off(A.RESIZE)},d._hideModal=function(){var a=this;this._element.style.display=
"none";this._element.setAttribute("aria-hidden",!0);this._isTransitioning=!1;this._showBackdrop(function(){l(document.body).removeClass("modal-open");a._resetAdjustments();a._resetScrollbar();l(a._element).trigger(A.HIDDEN)})},d._removeBackdrop=function(){this._backdrop&&(l(this._backdrop).remove(),this._backdrop=null)},d._showBackdrop=function(a){var b=this,c=l(this._element).hasClass("fade")?"fade":"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className=
"modal-backdrop",c&&this._backdrop.classList.add(c),l(this._backdrop).appendTo(document.body),l(this._element).on(A.CLICK_DISMISS,function(a){b._ignoreBackdropClick?b._ignoreBackdropClick=!1:a.target===a.currentTarget&&("static"===b._config.backdrop?b._element.focus():b.hide())}),c&&r.reflow(this._backdrop),l(this._backdrop).addClass("show"),a){if(!c)return void a();c=r.getTransitionDurationFromElement(this._backdrop);l(this._backdrop).one(r.TRANSITION_END,a).emulateTransitionEnd(c)}}else if(!this._isShown&&
this._backdrop)if(l(this._backdrop).removeClass("show"),c=function(){b._removeBackdrop();a&&a()},l(this._element).hasClass("fade")){var d=r.getTransitionDurationFromElement(this._backdrop);l(this._backdrop).one(r.TRANSITION_END,c).emulateTransitionEnd(d)}else c();else a&&a()},d._adjustDialog=function(){var a=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&a&&(this._element.style.paddingLeft=this._scrollbarWidth+"px");this._isBodyOverflowing&&!a&&(this._element.style.paddingRight=
this._scrollbarWidth+"px")},d._resetAdjustments=function(){this._element.style.paddingLeft="";this._element.style.paddingRight=""},d._checkScrollbar=function(){var a=document.body.getBoundingClientRect();this._isBodyOverflowing=a.left+a.right<window.innerWidth;this._scrollbarWidth=this._getScrollbarWidth()},d._setScrollbar=function(){var a=this;if(this._isBodyOverflowing){var b=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top")),c=[].slice.call(document.querySelectorAll(".sticky-top"));
l(b).each(function(b,c){b=c.style.paddingRight;var d=l(c).css("padding-right");l(c).data("padding-right",b).css("padding-right",parseFloat(d)+a._scrollbarWidth+"px")});l(c).each(function(b,c){b=c.style.marginRight;var d=l(c).css("margin-right");l(c).data("margin-right",b).css("margin-right",parseFloat(d)-a._scrollbarWidth+"px")});b=document.body.style.paddingRight;c=l(document.body).css("padding-right");l(document.body).data("padding-right",b).css("padding-right",parseFloat(c)+this._scrollbarWidth+
"px")}},d._resetScrollbar=function(){var a=[].slice.call(document.querySelectorAll(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top"));l(a).each(function(a,c){a=l(c).data("padding-right");l(c).removeData("padding-right");c.style.paddingRight=a||""});a=[].slice.call(document.querySelectorAll(".sticky-top"));l(a).each(function(a,c){a=l(c).data("margin-right");"undefined"!=typeof a&&l(c).css("margin-right",a).removeData("margin-right")});a=l(document.body).data("padding-right");l(document.body).removeData("padding-right");
document.body.style.paddingRight=a||""},d._getScrollbarWidth=function(){var a=document.createElement("div");a.className="modal-scrollbar-measure";document.body.appendChild(a);var b=a.getBoundingClientRect().width-a.clientWidth;return document.body.removeChild(a),b},g._jQueryInterface=function(a,b){return this.each(function(){var c=l(this).data("bs.modal"),d=p({},V,l(this).data(),"object"==typeof a&&a?a:{});if(c||(c=new g(this,d),l(this).data("bs.modal",c)),"string"==typeof a){if("undefined"==typeof c[a])throw new TypeError('No method named "'+
a+'"');c[a](b)}else d.show&&c.show(b)})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return V}}]),g}(),l(document).on(A.CLICK_DATA_API,'[data-toggle\x3d"modal"]',function(g){var d,a=this,b=r.getSelectorFromElement(this);b&&(d=document.querySelector(b));b=l(d).data("bs.modal")?"toggle":p({},l(d).data(),l(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||g.preventDefault();var c=l(d).one(A.SHOW,function(b){b.isDefaultPrevented()||c.one(A.HIDDEN,function(){l(a).is(":visible")&&
a.focus()})});O._jQueryInterface.call(l(d),b,this)}),l.fn.modal=O._jQueryInterface,l.fn.modal.Constructor=O,l.fn.modal.noConflict=function(){return l.fn.modal=ha,O._jQueryInterface},O),X=(ja=(q=f).fn.tooltip,ka=/(^|\s)bs-tooltip\S+/g,na={animation:!0,template:'\x3cdiv class\x3d"tooltip" role\x3d"tooltip"\x3e\x3cdiv class\x3d"arrow"\x3e\x3c/div\x3e\x3cdiv class\x3d"tooltip-inner"\x3e\x3c/div\x3e\x3c/div\x3e',trigger:"hover focus",title:"",delay:0,html:!(ma={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",
LEFT:"left"}),selector:!(la={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)"}),placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent"},oa={HIDE:"hide.bs.tooltip",HIDDEN:"hidden.bs.tooltip",SHOW:"show.bs.tooltip",
SHOWN:"shown.bs.tooltip",INSERTED:"inserted.bs.tooltip",CLICK:"click.bs.tooltip",FOCUSIN:"focusin.bs.tooltip",FOCUSOUT:"focusout.bs.tooltip",MOUSEENTER:"mouseenter.bs.tooltip",MOUSELEAVE:"mouseleave.bs.tooltip"},S=function(){function g(a,b){if("undefined"==typeof k)throw new TypeError("Bootstrap tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0;this._timeout=0;this._hoverState="";this._activeTrigger={};this._popper=null;this.element=a;this.config=this._getConfig(b);this.tip=
null;this._setListeners()}var d=g.prototype;return d.enable=function(){this._isEnabled=!0},d.disable=function(){this._isEnabled=!1},d.toggleEnabled=function(){this._isEnabled=!this._isEnabled},d.toggle=function(a){if(this._isEnabled)if(a){var b=this.constructor.DATA_KEY,c=q(a.currentTarget).data(b);c||(c=new this.constructor(a.currentTarget,this._getDelegateConfig()),q(a.currentTarget).data(b,c));c._activeTrigger.click=!c._activeTrigger.click;c._isWithActiveTrigger()?c._enter(null,c):c._leave(null,
c)}else{if(q(this.getTipElement()).hasClass("show"))return void this._leave(null,this);this._enter(null,this)}},d.dispose=function(){clearTimeout(this._timeout);q.removeData(this.element,this.constructor.DATA_KEY);q(this.element).off(this.constructor.EVENT_KEY);q(this.element).closest(".modal").off("hide.bs.modal");this.tip&&q(this.tip).remove();this._hoverState=this._timeout=this._isEnabled=null;(this._activeTrigger=null)!==this._popper&&this._popper.destroy();this.tip=this.config=this.element=this._popper=
null},d.show=function(){var a=this;if("none"===q(this.element).css("display"))throw Error("Please use show on visible elements");var b=q.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){q(this.element).trigger(b);var c=q.contains(this.element.ownerDocument.documentElement,this.element);if(!b.isDefaultPrevented()&&c){b=this.getTipElement();c=r.getUID(this.constructor.NAME);b.setAttribute("id",c);this.element.setAttribute("aria-describedby",c);this.setContent();this.config.animation&&
q(b).addClass("fade");c="function"==typeof this.config.placement?this.config.placement.call(this,b,this.element):this.config.placement;c=this._getAttachment(c);this.addAttachmentClass(c);var d=!1===this.config.container?document.body:q(document).find(this.config.container);q(b).data(this.constructor.DATA_KEY,this);q.contains(this.element.ownerDocument.documentElement,this.tip)||q(b).appendTo(d);q(this.element).trigger(this.constructor.Event.INSERTED);this._popper=new k(this.element,b,{placement:c,
modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(b){b.originalPlacement!==b.placement&&a._handlePopperPlacementChange(b)},onUpdate:function(b){a._handlePopperPlacementChange(b)}});q(b).addClass("show");"ontouchstart"in document.documentElement&&q(document.body).children().on("mouseover",null,q.noop);b=function(){a.config.animation&&a._fixTransition();var b=
a._hoverState;a._hoverState=null;q(a.element).trigger(a.constructor.Event.SHOWN);"out"===b&&a._leave(null,a)};q(this.tip).hasClass("fade")?(c=r.getTransitionDurationFromElement(this.tip),q(this.tip).one(r.TRANSITION_END,b).emulateTransitionEnd(c)):b()}}},d.hide=function(a){var b=this,c=this.getTipElement(),d=q.Event(this.constructor.Event.HIDE),g=function(){"show"!==b._hoverState&&c.parentNode&&c.parentNode.removeChild(c);b._cleanTipClass();b.element.removeAttribute("aria-describedby");q(b.element).trigger(b.constructor.Event.HIDDEN);
null!==b._popper&&b._popper.destroy();a&&a()};(q(this.element).trigger(d),d.isDefaultPrevented())||((q(c).removeClass("show"),"ontouchstart"in document.documentElement&&q(document.body).children().off("mouseover",null,q.noop),this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,q(this.tip).hasClass("fade"))?(d=r.getTransitionDurationFromElement(c),q(c).one(r.TRANSITION_END,g).emulateTransitionEnd(d)):g(),this._hoverState="")},d.update=function(){null!==this._popper&&
this._popper.scheduleUpdate()},d.isWithContent=function(){return!!this.getTitle()},d.addAttachmentClass=function(a){q(this.getTipElement()).addClass("bs-tooltip-"+a)},d.getTipElement=function(){return this.tip=this.tip||q(this.config.template)[0],this.tip},d.setContent=function(){var a=this.getTipElement();this.setElementContent(q(a.querySelectorAll(".tooltip-inner")),this.getTitle());q(a).removeClass("fade show")},d.setElementContent=function(a,b){var c=this.config.html;"object"==typeof b&&(b.nodeType||
b.jquery)?c?q(b).parent().is(a)||a.empty().append(b):a.text(q(b).text()):a[c?"html":"text"](b)},d.getTitle=function(){var a=this.element.getAttribute("data-original-title");return a||(a="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),a},d._getAttachment=function(a){return ma[a.toUpperCase()]},d._setListeners=function(){var a=this;this.config.trigger.split(" ").forEach(function(b){if("click"===b)q(a.element).on(a.constructor.Event.CLICK,a.config.selector,
function(b){return a.toggle(b)});else if("manual"!==b){var c="hover"===b?a.constructor.Event.MOUSEENTER:a.constructor.Event.FOCUSIN;b="hover"===b?a.constructor.Event.MOUSELEAVE:a.constructor.Event.FOCUSOUT;q(a.element).on(c,a.config.selector,function(b){return a._enter(b)}).on(b,a.config.selector,function(b){return a._leave(b)})}q(a.element).closest(".modal").on("hide.bs.modal",function(){return a.hide()})});this.config.selector?this.config=p({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},
d._fixTitle=function(){var a=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==a)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},d._enter=function(a,b){var c=this.constructor.DATA_KEY;(b=b||q(a.currentTarget).data(c))||(b=new this.constructor(a.currentTarget,this._getDelegateConfig()),q(a.currentTarget).data(c,b));a&&(b._activeTrigger["focusin"===a.type?"focus":
"hover"]=!0);q(b.getTipElement()).hasClass("show")||"show"===b._hoverState?b._hoverState="show":(clearTimeout(b._timeout),b._hoverState="show",b.config.delay&&b.config.delay.show?b._timeout=setTimeout(function(){"show"===b._hoverState&&b.show()},b.config.delay.show):b.show())},d._leave=function(a,b){var c=this.constructor.DATA_KEY;(b=b||q(a.currentTarget).data(c))||(b=new this.constructor(a.currentTarget,this._getDelegateConfig()),q(a.currentTarget).data(c,b));a&&(b._activeTrigger["focusout"===a.type?
"focus":"hover"]=!1);b._isWithActiveTrigger()||(clearTimeout(b._timeout),b._hoverState="out",b.config.delay&&b.config.delay.hide?b._timeout=setTimeout(function(){"out"===b._hoverState&&b.hide()},b.config.delay.hide):b.hide())},d._isWithActiveTrigger=function(){for(var a in this._activeTrigger)if(this._activeTrigger[a])return!0;return!1},d._getConfig=function(a){return"number"==typeof(a=p({},this.constructor.Default,q(this.element).data(),"object"==typeof a&&a?a:{})).delay&&(a.delay={show:a.delay,
hide:a.delay}),"number"==typeof a.title&&(a.title=a.title.toString()),"number"==typeof a.content&&(a.content=a.content.toString()),r.typeCheckConfig("tooltip",a,this.constructor.DefaultType),a},d._getDelegateConfig=function(){var a={};if(this.config)for(var b in this.config)this.constructor.Default[b]!==this.config[b]&&(a[b]=this.config[b]);return a},d._cleanTipClass=function(){var a=q(this.getTipElement()),b=a.attr("class").match(ka);null!==b&&b.length&&a.removeClass(b.join(""))},d._handlePopperPlacementChange=
function(a){this.tip=a.instance.popper;this._cleanTipClass();this.addAttachmentClass(this._getAttachment(a.placement))},d._fixTransition=function(){var a=this.getTipElement(),b=this.config.animation;null===a.getAttribute("x-placement")&&(q(a).removeClass("fade"),this.config.animation=!1,this.hide(),this.show(),this.config.animation=b)},g._jQueryInterface=function(a){return this.each(function(){var b=q(this).data("bs.tooltip"),c="object"==typeof a&&a;if((b||!/dispose|hide/.test(a))&&(b||(b=new g(this,
c),q(this).data("bs.tooltip",b)),"string"==typeof a)){if("undefined"==typeof b[a])throw new TypeError('No method named "'+a+'"');b[a]()}})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return na}},{key:"NAME",get:function(){return"tooltip"}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return oa}},{key:"EVENT_KEY",get:function(){return".bs.tooltip"}},{key:"DefaultType",get:function(){return la}}]),g}(),q.fn.tooltip=S._jQueryInterface,
q.fn.tooltip.Constructor=S,q.fn.tooltip.noConflict=function(){return q.fn.tooltip=ja,S._jQueryInterface},S),Ca=(pa=(G=f).fn.popover,qa=/(^|\s)bs-popover\S+/g,ra=p({},X.Default,{placement:"right",trigger:"click",content:"",template:'\x3cdiv class\x3d"popover" role\x3d"tooltip"\x3e\x3cdiv class\x3d"arrow"\x3e\x3c/div\x3e\x3ch3 class\x3d"popover-header"\x3e\x3c/h3\x3e\x3cdiv class\x3d"popover-body"\x3e\x3c/div\x3e\x3c/div\x3e'}),sa=p({},X.DefaultType,{content:"(string|element|function)"}),ta={HIDE:"hide.bs.popover",
HIDDEN:"hidden.bs.popover",SHOW:"show.bs.popover",SHOWN:"shown.bs.popover",INSERTED:"inserted.bs.popover",CLICK:"click.bs.popover",FOCUSIN:"focusin.bs.popover",FOCUSOUT:"focusout.bs.popover",MOUSEENTER:"mouseenter.bs.popover",MOUSELEAVE:"mouseleave.bs.popover"},T=function(g){function d(){return g.apply(this,arguments)||this}d.prototype=Object.create(g.prototype);(d.prototype.constructor=d).__proto__=g;var a=d.prototype;return a.isWithContent=function(){return this.getTitle()||this._getContent()},
a.addAttachmentClass=function(a){G(this.getTipElement()).addClass("bs-popover-"+a)},a.getTipElement=function(){return this.tip=this.tip||G(this.config.template)[0],this.tip},a.setContent=function(){var a=G(this.getTipElement());this.setElementContent(a.find(".popover-header"),this.getTitle());var c=this._getContent();"function"==typeof c&&(c=c.call(this.element));this.setElementContent(a.find(".popover-body"),c);a.removeClass("fade show")},a._getContent=function(){return this.element.getAttribute("data-content")||
this.config.content},a._cleanTipClass=function(){var a=G(this.getTipElement()),c=a.attr("class").match(qa);null!==c&&0<c.length&&a.removeClass(c.join(""))},d._jQueryInterface=function(a){return this.each(function(){var b=G(this).data("bs.popover"),g="object"==typeof a?a:null;if((b||!/destroy|hide/.test(a))&&(b||(b=new d(this,g),G(this).data("bs.popover",b)),"string"==typeof a)){if("undefined"==typeof b[a])throw new TypeError('No method named "'+a+'"');b[a]()}})},t(d,null,[{key:"VERSION",get:function(){return"4.1.3"}},
{key:"Default",get:function(){return ra}},{key:"NAME",get:function(){return"popover"}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return ta}},{key:"EVENT_KEY",get:function(){return".bs.popover"}},{key:"DefaultType",get:function(){return sa}}]),d}(X),G.fn.popover=T._jQueryInterface,G.fn.popover.Constructor=T,G.fn.popover.noConflict=function(){return G.fn.popover=pa,T._jQueryInterface},T),Da=(ua=(B=f).fn.scrollspy,Z={offset:10,method:"auto",target:""},va={offset:"number",
method:"string",target:"(string|element)"},W={ACTIVATE:"activate.bs.scrollspy",SCROLL:"scroll.bs.scrollspy",LOAD_DATA_API:"load.bs.scrollspy.data-api"},P=function(){function g(a,b){var c=this;this._element=a;this._scrollElement="BODY"===a.tagName?window:a;this._config=this._getConfig(b);this._selector=this._config.target+" .nav-link,"+this._config.target+" .list-group-item,"+this._config.target+" .dropdown-item";this._offsets=[];this._targets=[];this._activeTarget=null;this._scrollHeight=0;B(this._scrollElement).on(W.SCROLL,
function(a){return c._process(a)});this.refresh();this._process()}var d=g.prototype;return d.refresh=function(){var a=this,b=this._scrollElement===this._scrollElement.window?"offset":"position",c="auto"===this._config.method?b:this._config.method,d="position"===c?this._getScrollTop():0;this._offsets=[];this._targets=[];this._scrollHeight=this._getScrollHeight();[].slice.call(document.querySelectorAll(this._selector)).map(function(a){var b;a=r.getSelectorFromElement(a);if(a&&(b=document.querySelector(a)),
b){var g=b.getBoundingClientRect();if(g.width||g.height)return[B(b)[c]().top+d,a]}return null}).filter(function(a){return a}).sort(function(a,b){return a[0]-b[0]}).forEach(function(b){a._offsets.push(b[0]);a._targets.push(b[1])})},d.dispose=function(){B.removeData(this._element,"bs.scrollspy");B(this._scrollElement).off(".bs.scrollspy");this._scrollHeight=this._activeTarget=this._targets=this._offsets=this._selector=this._config=this._scrollElement=this._element=null},d._getConfig=function(a){if("string"!=
typeof(a=p({},Z,"object"==typeof a&&a?a:{})).target){var b=B(a.target).attr("id");b||(b=r.getUID("scrollspy"),B(a.target).attr("id",b));a.target="#"+b}return r.typeCheckConfig("scrollspy",a,va),a},d._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},d._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},d._getOffsetHeight=function(){return this._scrollElement===
window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},d._process=function(){var a=this._getScrollTop()+this._config.offset,b=this._getScrollHeight(),c=this._config.offset+b-this._getOffsetHeight();if(this._scrollHeight!==b&&this.refresh(),c<=a)a=this._targets[this._targets.length-1],this._activeTarget!==a&&this._activate(a);else{if(this._activeTarget&&a<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(b=this._offsets.length;b--;)this._activeTarget!==
this._targets[b]&&a>=this._offsets[b]&&("undefined"==typeof this._offsets[b+1]||a<this._offsets[b+1])&&this._activate(this._targets[b])}},d._activate=function(a){this._activeTarget=a;this._clear();var b=this._selector.split(","),b=b.map(function(b){return b+'[data-target\x3d"'+a+'"],'+b+'[href\x3d"'+a+'"]'}),b=B([].slice.call(document.querySelectorAll(b.join(","))));b.hasClass("dropdown-item")?(b.closest(".dropdown").find(".dropdown-toggle").addClass("active"),b.addClass("active")):(b.addClass("active"),
b.parents(".nav, .list-group").prev(".nav-link, .list-group-item").addClass("active"),b.parents(".nav, .list-group").prev(".nav-item").children(".nav-link").addClass("active"));B(this._scrollElement).trigger(W.ACTIVATE,{relatedTarget:a})},d._clear=function(){var a=[].slice.call(document.querySelectorAll(this._selector));B(a).filter(".active").removeClass("active")},g._jQueryInterface=function(a){return this.each(function(){var b=B(this).data("bs.scrollspy");if(b||(b=new g(this,"object"==typeof a&&
a),B(this).data("bs.scrollspy",b)),"string"==typeof a){if("undefined"==typeof b[a])throw new TypeError('No method named "'+a+'"');b[a]()}})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}},{key:"Default",get:function(){return Z}}]),g}(),B(window).on(W.LOAD_DATA_API,function(){for(var g=[].slice.call(document.querySelectorAll('[data-spy\x3d"scroll"]')),d=g.length;d--;){var a=B(g[d]);P._jQueryInterface.call(a,a.data())}}),B.fn.scrollspy=P._jQueryInterface,B.fn.scrollspy.Constructor=P,B.fn.scrollspy.noConflict=
function(){return B.fn.scrollspy=ua,P._jQueryInterface},P),Ea=(wa=(w=f).fn.tab,Q={HIDE:"hide.bs.tab",HIDDEN:"hidden.bs.tab",SHOW:"show.bs.tab",SHOWN:"shown.bs.tab",CLICK_DATA_API:"click.bs.tab.data-api"},R=function(){function g(a){this._element=a}var d=g.prototype;return d.show=function(){var a=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&w(this._element).hasClass("active")||w(this._element).hasClass("disabled"))){var b,c,d=w(this._element).closest(".nav, .list-group")[0],
g=r.getSelectorFromElement(this._element);if(d){var h="UL"===d.nodeName?"\x3e li \x3e .active":".active";c=(c=w.makeArray(w(d).find(h)))[c.length-1]}var h=w.Event(Q.HIDE,{relatedTarget:this._element}),f=w.Event(Q.SHOW,{relatedTarget:c});(c&&w(c).trigger(h),w(this._element).trigger(f),f.isDefaultPrevented()||h.isDefaultPrevented())||(g&&(b=document.querySelector(g)),this._activate(this._element,d),d=function(){var b=w.Event(Q.HIDDEN,{relatedTarget:a._element}),d=w.Event(Q.SHOWN,{relatedTarget:c});
w(c).trigger(b);w(a._element).trigger(d)},b?this._activate(b,b.parentNode,d):d())}},d.dispose=function(){w.removeData(this._element,"bs.tab");this._element=null},d._activate=function(a,b,c){var d=this,g=("UL"===b.nodeName?w(b).find("\x3e li \x3e .active"):w(b).children(".active"))[0],h=c&&g&&w(g).hasClass("fade");b=function(){return d._transitionComplete(a,g,c)};g&&h?(h=r.getTransitionDurationFromElement(g),w(g).one(r.TRANSITION_END,b).emulateTransitionEnd(h)):b()},d._transitionComplete=function(a,
b,c){if(b){w(b).removeClass("show active");var d=w(b.parentNode).find("\x3e .dropdown-menu .active")[0];d&&w(d).removeClass("active");"tab"===b.getAttribute("role")&&b.setAttribute("aria-selected",!1)}if(w(a).addClass("active"),"tab"===a.getAttribute("role")&&a.setAttribute("aria-selected",!0),r.reflow(a),w(a).addClass("show"),a.parentNode&&w(a.parentNode).hasClass("dropdown-menu")){if(b=w(a).closest(".dropdown")[0])b=[].slice.call(b.querySelectorAll(".dropdown-toggle")),w(b).addClass("active");a.setAttribute("aria-expanded",
!0)}c&&c()},g._jQueryInterface=function(a){return this.each(function(){var b=w(this),c=b.data("bs.tab");if(c||(c=new g(this),b.data("bs.tab",c)),"string"==typeof a){if("undefined"==typeof c[a])throw new TypeError('No method named "'+a+'"');c[a]()}})},t(g,null,[{key:"VERSION",get:function(){return"4.1.3"}}]),g}(),w(document).on(Q.CLICK_DATA_API,'[data-toggle\x3d"tab"], [data-toggle\x3d"pill"], [data-toggle\x3d"list"]',function(g){g.preventDefault();R._jQueryInterface.call(w(this),"show")}),w.fn.tab=
R._jQueryInterface,w.fn.tab.Constructor=R,w.fn.tab.noConflict=function(){return w.fn.tab=wa,R._jQueryInterface},R);!function(g){if("undefined"==typeof g)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");g=g.fn.jquery.split(" ")[0].split(".");if(2>g[0]&&9>g[1]||1===g[0]&&9===g[1]&&1>g[2]||4<=g[0])throw Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0");}(f);e.Util=r;e.Alert=xa;e.Button=ya;e.Carousel=
H;e.Collapse=za;e.Dropdown=Aa;e.Modal=Ba;e.Popover=Ca;e.Scrollspy=Da;e.Tab=Ea;e.Tooltip=X;Object.defineProperty(e,"__esModule",{value:!0})});
!function(e){var f=!1;if("function"==typeof define&&define.amd&&(define(e),f=!0),"object"==typeof exports&&(module.exports=e(),f=!0),!f){var k=window.Cookies,m=window.Cookies=e();m.noConflict=function(){return window.Cookies=k,m}}}(function(){function e(){for(var f=0,e={};f<arguments.length;f++){var t=arguments[f],p;for(p in t)e[p]=t[p]}return e}function f(k){function m(f,p,n){var t;if("undefined"!=typeof document){if(1<arguments.length){if("number"==typeof(n=e({path:"/"},m.defaults,n)).expires){var x=
new Date;x.setMilliseconds(x.getMilliseconds()+864E5*n.expires);n.expires=x}n.expires=n.expires?n.expires.toUTCString():"";try{t=JSON.stringify(p),/^[\{\[]/.test(t)&&(p=t)}catch(h){}p=k.write?k.write(p,f):encodeURIComponent(p+"").replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent);f=(f=(f=encodeURIComponent(f+"")).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent)).replace(/[\(\)]/g,escape);t="";for(var C in n)n[C]&&(t+="; "+C,!0!==n[C]&&(t+="\x3d"+n[C]));return document.cookie=
f+"\x3d"+p+t}f||(t={});C=document.cookie?document.cookie.split("; "):[];for(var y=/(%[0-9A-Z]{2})+/g,K=0;K<C.length;K++){var H=C[K].split("\x3d"),z=H.slice(1).join("\x3d");this.json||'"'!==z.charAt(0)||(z=z.slice(1,-1));try{x=H[0].replace(y,decodeURIComponent);if(z=k.read?k.read(z,x):k(z,x)||z.replace(y,decodeURIComponent),this.json)try{z=JSON.parse(z)}catch(h){}if(f===x){t=z;break}f||(t[x]=z)}catch(h){}}return t}}return m.set=m,m.get=function(f){return m.call(m,f)},m.getJSON=function(){return m.apply({json:!0},
[].slice.call(arguments))},m.defaults={},m.remove=function(f,k){m(f,"",e(k,{expires:-1}))},m.withConverter=f,m}return f(function(){})});
(function(e){"function"===typeof define&&define.amd?"undefined"!==typeof jQuery?define(["jquery"],e):define([],e):"undefined"!==typeof jQuery?e(jQuery):e()})(function(e,f){function k(h,f){h=decodeURI(h);f=H[f?"strict":"loose"].exec(h);h={attr:{},param:{},seg:{}};for(var e=14;e--;)h.attr[y[e]]=f[e]||"";h.param.query=p(h.attr.query);h.param.fragment=p(h.attr.fragment);h.seg.path=h.attr.path.replace(/^\/+|\/+$/g,"").split("/");h.seg.fragment=h.attr.fragment.replace(/^\/+|\/+$/g,"").split("/");h.attr.base=
h.attr.host?(h.attr.protocol?h.attr.protocol+"://"+h.attr.host:h.attr.host)+(h.attr.port?":"+h.attr.port:""):"";return h}function m(h){h=h.tagName;return"undefined"!==typeof h?C[h.toLowerCase()]:h}function t(h,f,e,m){var k=h.shift();if(k){var n=f[e]=f[e]||[];if("]"==k)if(F(n))""!=m&&n.push(m);else if("object"==typeof n){f=h=n;e=[];for(prop in f)f.hasOwnProperty(prop)&&e.push(prop);h[e.length]=m}else f[e]=[f[e],m];else{~k.indexOf("]")&&(k=k.substr(0,k.length-1));if(!z.test(k)&&F(n))if(0==f[e].length)n=
f[e]={};else{var n={},D;for(D in f[e])n[D]=f[e][D];f[e]=n}t(h,n,k,m)}}else F(f[e])?f[e].push(m):f[e]="object"==typeof f[e]?m:"undefined"==typeof f[e]?m:[f[e],m]}function p(h){return n(String(h).split(/&|;/),function(h,e){try{e=decodeURIComponent(e.replace(/\+/g," "))}catch(Y){}var m=e.indexOf("\x3d"),k;a:{for(var n=e.length,p,D=0;D<n;++D)if(p=e[D],"]"==p&&(k=!1),"["==p&&(k=!0),"\x3d"==p&&!k){k=D;break a}k=void 0}n=e.substr(0,k||m);m=e.substr(k||m,e.length);m=m.substr(m.indexOf("\x3d")+1,m.length);
""==n&&(n=e,m="");e=n;if(~e.indexOf("]")){var x=e.split("[");t(x,h,"base",m)}else{if(!z.test(e)&&F(h.base)){n={};for(x in h.base)n[x]=h.base[x];h.base=n}x=h.base;n=x[e];f===n?x[e]=m:F(n)?n.push(m):x[e]=[n,m]}return h},{base:{}}).base}function n(e,m,n){for(var h=0,k=e.length>>0;h<k;)h in e&&(n=m.call(f,n,e[h],h,e)),++h;return n}function F(e){return"[object Array]"===Object.prototype.toString.call(e)}function x(e,n){1===arguments.length&&!0===e&&(n=!0,e=f);e=e||window.location.toString();return{data:k(e,
n||!1),attr:function(e){e=K[e]||e;return"undefined"!==typeof e?this.data.attr[e]:this.data.attr},param:function(e){return"undefined"!==typeof e?this.data.param.query[e]:this.data.param.query},fparam:function(e){return"undefined"!==typeof e?this.data.param.fragment[e]:this.data.param.fragment},segment:function(e){if("undefined"===typeof e)return this.data.seg.path;e=0>e?this.data.seg.path.length+e:e-1;return this.data.seg.path[e]},fsegment:function(e){if("undefined"===typeof e)return this.data.seg.fragment;
e=0>e?this.data.seg.fragment.length+e:e-1;return this.data.seg.fragment[e]}}}var C={a:"href",img:"src",form:"action",base:"href",script:"src",iframe:"src",link:"href"},y="source protocol authority userInfo user password host port relative path directory file query fragment".split(" "),K={anchor:"fragment"},H={strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*):?([^:@]*))?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/},
z=/^[0-9]+$/;"undefined"!==typeof e?(e.fn.url=function(f){var h="";this.length&&(h=e(this).attr(m(this[0]))||"");return x(h,f)},e.url=x):window.purl=x});function clearProgress(){$(".loading-mask").delay(100).fadeOut(300)}function serverBusy(){$(".loading-mask").is(":visible")&&window.location.reload()}window.setTimeout("serverBusy()",12E4);
!function(){var e="undefined"!=typeof window&&void 0!==window.document?window.document:{},f="undefined"!=typeof module&&module.exports,k="undefined"!=typeof Element&&"ALLOW_KEYBOARD_INPUT"in Element,m=function(){for(var f,m=["requestFullscreen exitFullscreen fullscreenElement fullscreenEnabled fullscreenchange fullscreenerror".split(" "),"webkitRequestFullscreen webkitExitFullscreen webkitFullscreenElement webkitFullscreenEnabled webkitfullscreenchange webkitfullscreenerror".split(" "),"webkitRequestFullScreen webkitCancelFullScreen webkitCurrentFullScreenElement webkitCancelFullScreen webkitfullscreenchange webkitfullscreenerror".split(" "),
"mozRequestFullScreen mozCancelFullScreen mozFullScreenElement mozFullScreenEnabled mozfullscreenchange mozfullscreenerror".split(" "),"msRequestFullscreen msExitFullscreen msFullscreenElement msFullscreenEnabled MSFullscreenChange MSFullscreenError".split(" ")],k=0,p=m.length,t={};k<p;k++)if((f=m[k])&&f[1]in e){for(k=0;k<f.length;k++)t[m[0][k]]=f[k];return t}return!1}(),t={change:m.fullscreenchange,error:m.fullscreenerror},p={request:function(f){return new Promise(function(n){var p=m.requestFullscreen,
t=function(){this.off("change",t);n()}.bind(this);f=f||e.documentElement;/ Version\/5\.1(?:\.\d+)? Safari\//.test(navigator.userAgent)?f[p]():f[p](k?Element.ALLOW_KEYBOARD_INPUT:{});this.on("change",t)}.bind(this))},exit:function(){return new Promise(function(f){if(this.isFullscreen){var k=function(){this.off("change",k);f()}.bind(this);e[m.exitFullscreen]();this.on("change",k)}else f()}.bind(this))},toggle:function(e){return this.isFullscreen?this.exit():this.request(e)},onchange:function(e){this.on("change",
e)},onerror:function(e){this.on("error",e)},on:function(f,k){(f=t[f])&&e.addEventListener(f,k,!1)},off:function(f,k){(f=t[f])&&e.removeEventListener(f,k,!1)},raw:m};m?(Object.defineProperties(p,{isFullscreen:{get:function(){return!!e[m.fullscreenElement]}},element:{enumerable:!0,get:function(){return e[m.fullscreenElement]}},enabled:{enumerable:!0,get:function(){return!!e[m.fullscreenEnabled]}}}),f?module.exports=p:window.screenfull=p):f?module.exports=!1:window.screenfull=!1}();
$(document).ready(function(){(function(){var e=!1,f=!1;/(iPhone|iPod|iPad)\sOS\s[0-4][_\d]+/i.test(navigator.userAgent)&&(e=!0);/Android\s+([0-2][\.\d]+)/i.test(navigator.userAgent)&&(f=!0);$("body").append('\x3ca href\x3d"#" id\x3d"back-to-top" title\x3d"返回顶部"\x3e返回顶部\x3c/a\x3e');$("#back-to-top").click(function(e){$("html, body").animate({scrollTop:0},800);e.preventDefault()});$(window).scroll(function(){var k=$(window).scrollTop();(e||f)&&$("#back-to-top").css({position:"absolute",top:k+$(window).height()});
200<k?$("#back-to-top").fadeIn(400):$("#back-to-top").fadeOut(400)})})()});
function bindBottomPagingProgress(){$(".scroll-page").each(function(e){e=$(this).position();$(this).scrollspy({min:e.top-20,max:e.top+$(this).height(),onEnter:function(e,f){e=Math.round(e.id/totalSize*100);var k=$(window).scrollTop()+$(window).height()-$(".span12").height()+200;-500<k&&setTimeout(function(){k=$(window).scrollTop()+$(window).height()-$(".span12").height()+200;0<k&&$(".bottom-paging-progress .bar").width("100%")},1E3);$(".bottom-paging-progress .bar").width(""+e+"%")},onLeave:function(e,
f){}})});var e=$(window).scrollTop()+$(window).height()-$(".span12").height()+200;-500<e&&setTimeout(function(){e=$(window).scrollTop()+$(window).height()-$(".span12").height()+200;0<e&&$(".bottom-paging-progress .bar").width("100%")},1E3)}
function bindBottomPagingProgressImg(){$(".scroll-page").each(function(e){e=$(this).position();$(this).scrollspy({min:e.top-60,max:e.top+$(this).height()-100,onEnter:function(e,k){e=e.id;k=Math.round(e/totalSize*100);$(".bottom-paging-progress .bar").width(""+k+"%");$(".select-page-selector").val(e);try{curPage=e}catch(m){}},onLeave:function(e,k){}})})}
function bindAnchorScroll(){$('a[href^\x3d"#"]').click(function(e){var f=$(this).attr("href").substring(1);f&&(e.preventDefault(),gotoAnchor(f))});$("span").removeClass("spanthcontent")}var isLoadAll=!1;
function gotoAnchor(e){var f=!1;$("a[name\x3d"+e+"]").length?(f=!0,setTimeout(function(){$("html, body").animate({scrollTop:$("a[name\x3d"+e+"]").position().top+20},"slow")},300)):$("#"+e).length&&(f=!0,setTimeout(function(){$("html, body").animate({scrollTop:$("#"+e).position().top+20},"slow")},300));bindBottomPagingProgress();if(!f&&!isLoadAll){var k;try{k=$.url().attr("query")}catch(m){}try{$(".word-content").infinitescroll("destroy")}catch(m){}$(".loader").show();$.ajax({type:"GET",url:contextPath+
"/view/"+(id?id:uuid)+".json?start\x3d1\x26size\x3d0\x26"+k,data:{},async:!0,dataType:"json"}).done(function(f){if(1==f.code){f=f.data;for(i=0;i<f.length;i++){var k=f[i];0==i?$(".span12 .word-page .word-content").html(k.content):$(".span12 .word-page .word-content").append(k.content)}isLoadAll=!0;setTimeout(function(){gotoAnchor(e)},300);$(".loader").hide();bindAnchorScroll();bindBottomPagingProgress()}else $(".span12").html('\x3cdiv class\x3d"alert alert-error"\x3e'+f.desc+"\x3c/div\x3e")})}}
function loadAllPage(e){var f=$.url().attr("query");$(".word-content").infinitescroll("destroy");$.ajax({type:"GET",url:contextPath+"/view/"+(id?id:uuid)+".json?start\x3d1\x26size\x3d0\x26"+f,data:{},async:!!e,dataType:"json"}).done(function(e){if(1==e.code){e=e.data;for(i=0;i<e.length;i++){var f=e[i];0==i?$(".span12 .word-page .word-content").html(f.content):$(".span12 .word-page .word-content").append(f.content)}$('a[href^\x3d"#"]').click(function(e){var f=$(this).attr("href").substring(1);f&&(e.preventDefault(),
$("html, body").animate({scrollTop:$("a[name\x3d"+f+"]").position().top+20},"slow"))});bindBottomPagingProgress()}else $(".span12").html('\x3cdiv class\x3d"alert alert-error"\x3e'+e.desc+"\x3c/div\x3e")})}
function getAllUrlParams(e){var f=e?e.split("?")[1]:window.location.search.slice(1);e={};if(f)for(var f=f.split("#")[0],f=f.split("\x26"),k=0;k<f.length;k++){var m=f[k].split("\x3d"),t=void 0,p=m[0].replace(/\[\d*\]/,function(e){t=e.slice(1,-1);return""}),m="undefined"===typeof m[1]?!0:m[1],m=m.replace(/\+/g," "),m=decodeURIComponent(m);e[p]?("string"===typeof e[p]&&(e[p]=[e[p]]),"undefined"===typeof t?e[p].push(m):e[p][t]=m):e[p]=m}return e}var isWatermark=!1;
function watermark(){try{var e=uuid.slice(-1),f=Cookies.get("IDOCV_THD_VIEW_CHECK_WATERMARK_TXT_"+uuid);f&&decodeURI(f);var f="//api.idocv.com/data/idocv_logo_up_transparent.png",k=Cookies.get("IDOCV_THD_VIEW_CHECK_WATERMARK_IMG_"+uuid);k&&(f=decodeURI(k));if(isWatermark||k){k='\x3cdiv class\x3d"watermark-container" style\x3d"background-image: url('+f+'); position: absolute; top: 0px; left: 0px; right: 0px; bottom: 0px;"\x3e\x3c/div\x3e';$("body").css("background-image","url("+f+")");$("body").css("background-attachment",
"fixed");$(".word-page").css("background-image","url("+f+")");"x"==e&&$(".tab-content").prepend(k);if("p"==e){if(0<$(".ppt-turn-left-mask").length||0<$(".ppt-turn-right-mask").length)$(".ppt-turn-left-mask").css("background-image","url("+f+")"),$(".ppt-turn-right-mask").css("background-image","url("+f+")");0<$(".pdf-content").length&&$(".pdf-content").append(k)}if("f"==e&&0<$(".pdfViewer").length){var m=$(".pdfViewer"),t=parseInt(m.height()/500);500<t&&(t=500);for(var p=0;p<t;p++)m.append('\x3cdiv class\x3d"watermark-pdf-page-container" style\x3d"width:100%;text-align:center;position:absolute; top:'+
500*p+'px;"\x3e\x3cimg src\x3d"'+f+'" /\x3e\x3c/div\x3e')}"f"==e&&0<$(".pdf-content").length&&$(".pdf-content").append(k)}}catch(n){}}
function afterLoad(){var e=navigator.userAgent.toLowerCase(),e=/micromessenger/.test(e)?!0:!1;768>document.documentElement.clientWidth&&$(".lnk-file-title").text("");e&&($(".lnk-file-title").text(""),$(".word-tab-title-li").length||($(".word-body .navbar").hide(),$(".word-body").css("padding-top","0px")),$(".ppt-body .navbar").hide(),$(".ppt-body").css("padding-top","20px"),$(".pdf-body .navbar").hide(),$(".pdf-body").css("padding-top","20px"),$(".img-body .navbar").hide(),$(".img-body").css("padding-top",
"20px"),$(".audio-body .navbar").hide(),$(".audio-body").css("padding-top","20px"),$(".zip-body .navbar").hide(),$(".zip-body").css("padding-top","20px"));try{var f=$.url().param("name");f&&$(".lnk-file-title").text(f)}catch(F){}if(f=$(".lnk-file-title").text())document.title=f;try{var k=authMap.down;k&&"0"==k&&$(".lnk-file-title").removeAttr("href");var m=authMap.copy;m&&"0"==m&&($("body").css("-webkit-user-select","none"),$("body").css("-khtml-user-select","none"),$("body").css("-moz-user-select",
"none"),$("body").css("-ms-user-select","none"),$("body").css("user-select","none"),$("body").on("selectstart",function(e){e.preventDefault()}));var t=authMap.menu;t&&"0"==t&&(document.oncontextmenu=document.body.oncontextmenu=function(){return!1});var p=authMap.print;p&&"0"==p&&$('\x3cstyle media\x3d"print"\x3e body {display: none;}\x3c/style\x3e').appendTo("head")}catch(F){}try{var n=$.url().param("puuid");n&&0<n.length&&uuid&&($(".lnk-file-title").removeAttr("href"),$(".lnk-file-title").text("\x3c 返回"),
$(".lnk-file-title").css("cursor","pointer"),$(".lnk-file-title").click(function(){var e=contextPath+"/view/"+n;if(queryStr){var f=queryStr+"\x26";-1<f.indexOf("puuid\x3d")&&(f=f.replace(/(puuid=).*?(&)/,""));(f=f.replace(/(&+)$/,""))&&(e=e+"?"+f)}window.location.href=e}))}catch(F){}watermark();$("body").append('\x3cdiv class\x3d"loader"\x3e加载中……\x3c/div\x3e')};