package com.linkus.event.handler;

import com.alibaba.fastjson.JSONArray;
import com.linkus.base.constants.BizConstants;
import com.linkus.base.constants.CpntAttrConstants;
import com.linkus.base.constants.SysDefConstants;
import com.linkus.base.db.base.UpdataData;
import com.linkus.base.db.base.condition.IDbCondition;
import com.linkus.base.db.base.condition.impl.mini.DC_E;
import com.linkus.base.db.base.field.DFN;
import com.linkus.base.db.mongo.model.TeIdName;
import com.linkus.base.db.mongo.model.TeIdNameCn;
import com.linkus.base.db.mongo.model.TeIdNameCnBt;
import com.linkus.base.db.mongo.model.TeUser;
import com.linkus.base.exception.BaseException;
import com.linkus.base.util.DateUtil;
import com.linkus.base.util.MapUtil;
import com.linkus.base.util.StringUtil;
import com.linkus.biz.db.model.TeBiz;
import com.linkus.biz.db.model.TeTestStep;
import com.linkus.biz.db.model.dao.ITestStepDao;
import com.linkus.cmpt.evtengine.EventParaPool;
import com.linkus.cmpt.flow.dao.IFlowTaskDao;
import com.linkus.cmpt.flow.model.TeFlowTask;
import com.linkus.cmpt.flow.service.IFlowBranchService;
import com.linkus.event.EventBizUpdateBizInfo;
import com.linkus.event.backendmethod.CpntAttrBackendMethodFactory;
import com.linkus.event.backendmethod.IBackendMethodService;
import com.linkus.event.backendmethod.impl.BackendMethod;
import com.linkus.event.dao.IBizDao;
import com.linkus.event.dao.ICpntAttrDao;
import com.linkus.event.handler.process.EventProcess;
import com.linkus.event.parm.EventBizConst;
import com.linkus.event.parm.val.EpnValueUpateBizInfo;
import com.linkus.event.service.IBizEventService;
import com.linkus.event.service.IBizService;
import com.linkus.fieldcust.model.po.TeSysCpntAttr;
import com.linkus.fieldcust.model.po.TeSysCustField;
import com.linkus.fieldcust.service.IFieldCustService;
import com.linkus.sys.model.SysDef;
import com.linkus.sys.model.SysDefType;
import com.linkus.sys.service.ISysDefService;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

@SuppressWarnings("unchecked")
@Component("EventBizUpdateBizInfoHandler")
public class EventBizUpdateBizInfoHandler {
	@Resource(name = "FieldCustServiceImpl")
	private IFieldCustService fieldCustService;
	@Resource(name = "SysDefServiceImpl")
	private ISysDefService sysDefService;
	@Resource(name = "CpntAttrDaoImpl")
	private ICpntAttrDao cpntAttrDao;
	@Resource(name = "CpntAttrBackendMethodFactory")
	private CpntAttrBackendMethodFactory cpntAttrBackendMethodFactory;
	@Autowired
	private IBizDao bizDao;
	@Autowired
	private ISysUserService sysUserService;
	@Autowired
	private IFlowTaskDao flowTaskDao;
	@Autowired
	private IBizService  bizService;
	@Autowired
	private ITestStepDao testStepDao;
	@Autowired
	private IBizEventService bizEventService;
	@Resource
	private IFlowBranchService flowBranchService;


	@EventListener
	public void handle(EventBizUpdateBizInfo evt) {
		EventParaPool pool = evt.getParms();
		updateBizDbObject(pool);
		updateFlowTaskDbObject(pool);
		handleWidgets(pool);
		saveTestStepsInfo(pool);
		if((SysDefConstants.DEF_CN_EVT_SCENE_ADD_BIZ).equals(evt.getSceneCodeName())){
			this.saveLinkedInfo(pool);//关联需求或产品功能
		}

	}
	private void handleWidgets(EventParaPool pool) {
		Map<ObjectId, Object> fieldValMap = (Map<ObjectId, Object>) pool.get(EventBizConst.EPN_FIELD_VAL_MAP);
		if (null == fieldValMap)
			fieldValMap = new HashMap<ObjectId, Object>();
		List<ObjectId> fieldIds = MapUtil.listKey(fieldValMap);
		//获取组件ID
		List<SysDef> sysDefs = sysDefService.getSysDefsByIds(fieldIds);
		List<ObjectId> widgetIds = new ArrayList<>();
		for(SysDef sysDef : sysDefs){
			SysDefType defType = sysDef.getDefType();
			if(null != defType && EventBizConst.VAR_NAME_WIDGET.equals(defType.getCodeName())){
				widgetIds.add(sysDef.getId());
			}
		}
		if(null == widgetIds || widgetIds.size() == 0 ) return;
		//查询组件
		List<TeSysCustField> widgetList = fieldCustService.getSysCustFieldByWidgetIds(widgetIds);

		if(null == widgetList || widgetList.size() == 0 ) return;

		Map<ObjectId, ObjectId> cmptMap = new HashMap<ObjectId, ObjectId>();
		Set<String> cpntSetIds = new HashSet<>();
		for (TeSysCustField widget : widgetList) {
			TeIdNameCn cpnt = widget.getCpnt();
			TeIdNameCn widgetDef = widget.getWidget();
			if ((null != cpnt && null != cpnt.getCid()) && (null != widgetDef && null != widgetDef.getCid())) {
				cmptMap.put(widgetDef.getCid(), cpnt.getCid());
				cpntSetIds.add(StringUtil.getNotNullStr(cpnt.getCid()));
			}
		}

		if(null == cpntSetIds || cpntSetIds.size() == 0 ) return;

		List<TeSysCpntAttr> cpntAttrs = cpntAttrDao.queryByCpnts(cpntSetIds);
		Map<ObjectId, BackendMethod> cpntBackendMethodMap = new HashMap<>();
		for (TeSysCpntAttr cpntAtrr : cpntAttrs) {
			BackendMethod method = cpntAttrBackendMethodFactory.getBackendMethod(cpntAtrr);
			if (method != null) {
				cpntBackendMethodMap.put(cpntAtrr.getCpnt().getCid(), method);
			}
		}
		for(ObjectId widgetId : widgetIds){
			ObjectId cpntId = cmptMap.get(widgetId);
			Object value = fieldValMap.get(widgetId);
			BackendMethod backendMethod = cpntBackendMethodMap.get(cpntId);
			if (backendMethod != null) {
				IBackendMethodService service = backendMethod.getService();
				TeSysCpntAttr attr = backendMethod.getCpntAttr();
				service.getBackendObject(widgetId, attr, value, backendMethod.getIsArrayFormat(), pool);
			}
		}
	}
	private void updateFlowTaskDbObject(EventParaPool pool) {
		ObjectId flowTaskId = pool.get(EventBizConst.EPN_FLOW_TASK, ObjectId.class);
		if (null == flowTaskId) {
			return;
		}
		TeFlowTask flowTask = flowTaskDao.getFlowTaskById(flowTaskId);

		if (flowTask == null) {
			return;
		}

		Map<ObjectId, Object> fieldValMap = (Map<ObjectId, Object>) pool.get(EventBizConst.EPN_FIELD_VAL_MAP);
		if (null == fieldValMap)
			fieldValMap = new HashMap<ObjectId, Object>();
		List<ObjectId> fields = MapUtil.listKey(fieldValMap);

		List<SysDef> sysFieldDefs = sysDefService.getSysDefsByIds(fields);
		Map<ObjectId, String> fieldsMap = new HashMap<ObjectId, String>();
		Map<ObjectId, Boolean> fieldsIsStdMap = new HashMap<ObjectId, Boolean>();
		for (SysDef sysField : sysFieldDefs) {
			SysDef srcDef = sysField.getSrcDef();
			if (null == srcDef || StringUtil.isNull(srcDef.getCodeName()))
				continue;
			if (SysDefConstants.FLOWTASK_CODENAME.equals(StringUtil.getNotNullStr(srcDef.getCodeName()))) {
				fieldsMap.put(sysField.getId(), sysField.getCodeName());
				fieldsIsStdMap.put(sysField.getId(), sysField.getIsStd());
			}
		}
		if (null == fieldsMap || fieldsMap.size() == 0) {
			return;
		}
		List<TeSysCustField> custFieldList = fieldCustService.getSysCustFieldsByFieldIds(fields);
		Map<ObjectId, ObjectId> cmptMap = new HashMap<ObjectId, ObjectId>();
		Set<String> cpntSetIds = new HashSet<>();
		for (TeSysCustField sysFieldCust : custFieldList) {
			TeIdNameCn cpnt = sysFieldCust.getCpnt();
			TeIdNameCn field = sysFieldCust.getField();
			if ((null != cpnt && null != cpnt.getCid()) && (null != field && null != field.getCid())) {
				cmptMap.put(field.getCid(), cpnt.getCid());
				cpntSetIds.add(StringUtil.getNotNullStr(cpnt.getCid()));
			}
			else {
				throw new BaseException("字段Id[" + field.getCid() + "]没有配置组件");
			}
		}
		List<TeSysCpntAttr> cpntAttrs = cpntAttrDao.queryByCpnts(cpntSetIds);
		Map<ObjectId, BackendMethod> cpntBackendMethodMap = new HashMap<>();
		for (TeSysCpntAttr cpntAtrr : cpntAttrs) {
			BackendMethod method = cpntAttrBackendMethodFactory.getBackendMethod(cpntAtrr);
			if (method != null) {
				cpntBackendMethodMap.put(cpntAtrr.getCpnt().getCid(), method);
			}
		}
		Set<Entry<ObjectId, String>> fieldsMapSet = fieldsMap.entrySet();

		// TeFlowTask flowTask = new TeFlowTask();
		Class<TeFlowTask> flowTaskClass = TeFlowTask.class;
		for (Entry<ObjectId, String> entry : fieldsMapSet) {
			ObjectId fieldId = entry.getKey();
			String fieldCodeName = entry.getValue();
			Object value = fieldValMap.get(fieldId);
			ObjectId cpntId = cmptMap.get(fieldId);
			BackendMethod method = cpntBackendMethodMap.get(cpntId);
			if (method != null) {
				IBackendMethodService service = method.getService();
				TeSysCpntAttr attr = method.getCpntAttr();
				Object backendObject = service.getBackendObject(fieldId, attr, value, method.getIsArrayFormat(), pool);
				Boolean isStdField = fieldsIsStdMap.get(fieldId);
				if (isStdField) {
					try {
						Method setMethod = getMethod(flowTaskClass, "set" + StringUtil.captureName(fieldCodeName));
						Class<?>[] parameterTypes = setMethod.getParameterTypes();
						if (null != setMethod) {
							setMethod.invoke(flowTask, StringUtil.to(backendObject, parameterTypes[0]));
						}
					}
					catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		}
		flowTaskDao.updateById(flowTaskId, flowTask);
	}

	private void updateBizDbObject(EventParaPool pool) {
		ObjectId bizId = pool.get(EventBizConst.EPN_BIZ_ID, ObjectId.class);
		ObjectId flowId = pool.get(EventBizConst.EPN_FLOW_ID, ObjectId.class);
		if (bizId == null) {
			throw new BaseException("未获取到参数bizId");
		}

		TeBiz biz = bizDao.findById(bizId);
		if (biz == null) {
			return;
		}

		Map<ObjectId, Object> fieldValMap = (Map<ObjectId, Object>) pool.get(EventBizConst.EPN_FIELD_VAL_MAP);
		if (null == fieldValMap)
			fieldValMap = new HashMap<ObjectId, Object>();
		List<ObjectId> fields = MapUtil.listKey(fieldValMap);
		EpnValueUpateBizInfo oBizInfo = pool.get(EventBizConst.EPN_BIZ_UPDATE_INFO, EpnValueUpateBizInfo.class);
		if (null != oBizInfo) {
			EpnValueUpateBizInfo bizInfo = (EpnValueUpateBizInfo) oBizInfo;
			fieldValMap.put(EventBizConst.DEF_ID_FIELD_BIZ_STATUS, bizInfo.getStatus());
		}
		List<SysDef> sysFieldDefs = sysDefService.getSysDefsByIds(fields);
		Map<ObjectId, String> fieldsMap = new HashMap<ObjectId, String>();
		Map<ObjectId, Boolean> fieldsIsStdMap = new HashMap<ObjectId, Boolean>();
		for (SysDef sysField : sysFieldDefs) {
			SysDef srcDef = sysField.getSrcDef();
			// 跳过没有配置原定义的CodeName的字段
			if (null != srcDef && StringUtil.isNull(srcDef.getCodeName()))
				continue;
			// 原定义为空或者原定义为业务的都认为是业务字段
			if (null == srcDef || SysDefConstants.BIZTB_CODENAME.equals(StringUtil.getNotNullStr(srcDef.getCodeName()))) {
				fieldsMap.put(sysField.getId(), sysField.getCodeName());
				fieldsIsStdMap.put(sysField.getId(), sysField.getIsStd());
			}
		}
		if (null == fieldsMap || fieldsMap.size() == 0) {
			return;
		}
		List<TeSysCustField> custFieldList = fieldCustService.getSysCustFieldsByFieldIds(fields);
		Map<ObjectId, ObjectId> cmptMap = new HashMap<ObjectId, ObjectId>();
		Set<String> cpntSetIds = new HashSet<>();
		for (TeSysCustField sysFieldCust : custFieldList) {
			TeIdNameCn cpnt = sysFieldCust.getCpnt();
			TeIdNameCn field = sysFieldCust.getField();
			if ((null != cpnt && null != cpnt.getCid()) && (null != field && null != field.getCid())) {
				cmptMap.put(field.getCid(), cpnt.getCid());
				cpntSetIds.add(StringUtil.getNotNullStr(cpnt.getCid()));
			}
			else {
				throw new BaseException("字段Id[" + field.getCid() + "]没有配置组件");
			}
		}
		List<TeSysCpntAttr> cpntAttrs = cpntAttrDao.queryByCpnts(cpntSetIds);
		Map<ObjectId, BackendMethod> cpntBackendMethodMap = new HashMap<>();
		for (TeSysCpntAttr cpntAtrr : cpntAttrs) {
			BackendMethod method = cpntAttrBackendMethodFactory.getBackendMethod(cpntAtrr);
			if (method != null) {
				cpntBackendMethodMap.put(cpntAtrr.getCpnt().getCid(), method);
			}
		}
		Set<Entry<ObjectId, String>> fieldsMapSet = fieldsMap.entrySet();
		Map<ObjectId, Object> custFieldInfoMap = biz.getCustFieldInfo();
		if (custFieldInfoMap == null) {
			custFieldInfoMap = new HashMap<>();
		}
		// TeBiz biz = new TeBiz();
		Class<TeBiz> bizClass = TeBiz.class;
		Boolean isBizNeedUpdate = false;
		for (Entry<ObjectId, String> entry : fieldsMapSet) {
			ObjectId fieldId = entry.getKey();
			String fieldCodeName = entry.getValue();
			Object value = fieldValMap.get(fieldId);
			ObjectId cpntId = cmptMap.get(fieldId);
			BackendMethod method = cpntBackendMethodMap.get(cpntId);
			if (method != null) {
				IBackendMethodService service = method.getService();
				TeSysCpntAttr attr = method.getCpntAttr();
				Object backendObject = service.getBackendObject(fieldId, attr, value, method.getIsArrayFormat(), pool);
				Boolean isStdField = fieldsIsStdMap.get(fieldId);
				if (isStdField) {
					try {
						Method setMethod = getMethod(bizClass, "set" + StringUtil.captureName(fieldCodeName));
						Class<?>[] parameterTypes = setMethod.getParameterTypes();
						if (null != setMethod) {
							Object v = StringUtil.to(backendObject, parameterTypes[0]);
							setMethod.invoke(biz, v);
							isBizNeedUpdate = true;
						}
					}
					catch (Exception e) {
						e.printStackTrace();
					}
				}
				else {
					custFieldInfoMap.put(fieldId, backendObject);
					isBizNeedUpdate = true;
				}
			}
		}

		// 没有字段时候，也不需要记录custFieldInfo字段的值
		if (custFieldInfoMap == null || custFieldInfoMap.isEmpty()) {
			custFieldInfoMap = null;
		}

		biz.setCustFieldInfo(custFieldInfoMap);
		if (isBizNeedUpdate) {
			bizDao.updateById(bizId, biz, true);
		}
		// 节点页面兼容节点安排能力
		List<Map> nodePlanInfo = EventProcess.processNodePlan(fieldValMap);
		if (CollectionUtils.isNotEmpty(nodePlanInfo) && flowId != null) {
			List<TeFlowTask> flowTasks = flowTaskDao.getFlowTasksByFlowId(flowId);
			setNodeInfo(nodePlanInfo, flowTasks);
		}
		if(biz.getFlowId() != null) {
			updateFlowTask(biz.getFlowId(), fieldValMap);
		}else{
		    // 兼容看板的故事及缺陷的项目设置
            updateKanBanFlowTask(biz, fieldValMap);
        }

	}

    /**
     * 兼容看板的故事及缺陷的任务设置项目
     * @param biz
     * @param fieldValMap
     */
    private void updateKanBanFlowTask(TeBiz biz, Map<ObjectId, Object> fieldValMap) {
        if(biz == null || MapUtils.isEmpty(fieldValMap)){
            return;
        }
        ObjectId bizTypeId = biz.getBizType() == null ? null : biz.getBizType().getCid();
        ObjectId storyId = null;
        ObjectId bugId = null;
        if(SysDefConstants.DEF_ID_BIZ_TYPE_BUG.equals(bizTypeId) && CollectionUtils.isNotEmpty(biz.getLinkedBizs())){
            // 缺陷
            for (TeIdNameCnBt linkedBiz : biz.getLinkedBizs()) {
                if(BooleanUtils.isFalse(linkedBiz.getIsValid())
                        || !SysDefConstants.DEF_ID_USERSTORY.equals(linkedBiz.getBizTypeId())){
                    continue;
                }
                storyId = linkedBiz.getCid();
                break;
            }
            bugId = biz.getId();
        }else if(SysDefConstants.DEF_ID_USERSTORY.equals(bizTypeId)){
            // 故事
            storyId = biz.getId();
        }
        List<IDbCondition> conds = new ArrayList<>();
        if(storyId != null){
            conds.add(new DC_E(DFN.flowTask_biz.dot(DFN.common_cid), storyId));
            if(bugId != null){
                conds.add(new DC_E(DFN.flowTask_linkedBizs.dot(DFN.flowTask_biz).dot(DFN.common_cid), bugId));
            }
        }

        ObjectId prjId = StringUtil.to(fieldValMap.get(EventBizConst.DEF_ID_FIELD_PRJ), ObjectId.class);
        List<UpdataData> updates = new ArrayList<>();
        if(prjId != null){
            SysDef sysDef = sysDefService.getSysDefById(prjId);
            if(sysDef != null){
                updates.add(new UpdataData(DFN.flowTask_prj, sysDef.trans2IdNameCn()));
            }
        }
		//DMP_REQ_8876,【看板IT研发管理】敏捷看板，列表模式故事更新问题
		String bizName = StringUtil.to(fieldValMap.get(SysDefConstants.DEF_ID_FIELD_BIZ_NAME), String.class);
		if (StringUtil.isNotNull(bizName)){
			if (SysDefConstants.DEF_ID_USERSTORY.equals(bizTypeId)) {
				// 故事，修改故事下所有任务对应的故事名称
				updates.add(new UpdataData(DFN.flowTask_biz.dot(DFN.common_name), bizName));
			}else if (SysDefConstants.DEF_ID_BIZ_TYPE_BUG.equals(bizTypeId)){
				// 缺陷，修改缺陷下所有任务对应的缺陷名称
				List<UpdataData> linkedBizNameUpdateList = new ArrayList<>();
				linkedBizNameUpdateList.add(new UpdataData(DFN.flowTask_linkedBizs.dot(DFN.flowTask_$_elem).dot(DFN.flowTask_biz).dot(DFN.common_name), bizName));
				List<Criteria> arrayFilterList = new ArrayList<>();
				arrayFilterList.add(Criteria.where(DFN.flowTask_elem.dot(DFN.flowTask_biz).dot(DFN.common_cid).n()).is(bugId));
				flowTaskDao.updateByCondsAndArrayFilter(conds, linkedBizNameUpdateList, arrayFilterList);
			}
		}

        if(CollectionUtils.isNotEmpty(conds) && CollectionUtils.isNotEmpty(updates)){
            flowTaskDao.updateByConds(conds, updates);
        }
    }

    private void updateFlowTask(ObjectId flowId, Map<ObjectId, Object> fieldValMap){

		if(flowId == null || fieldValMap == null || fieldValMap.isEmpty()){
			return;
		}

		ObjectId prjId = StringUtil.to(fieldValMap.get(EventBizConst.DEF_ID_FIELD_PRJ), ObjectId.class);

		List<IDbCondition> conds = new ArrayList<IDbCondition>();
		conds.add(new DC_E(DFN.flowTask_flowId, flowId));
		List<UpdataData> updates = new ArrayList<UpdataData>();

		if(prjId != null){
			SysDef sysDef = sysDefService.getSysDefById(prjId);
			if(sysDef != null){
				updates.add(new UpdataData(DFN.flowTask_prj, sysDef.trans2IdNameCn()));
			}
		}

		ObjectId srcPrdId = StringUtil.to(fieldValMap.get(EventBizConst.DEF_ID_FIELD_SRC_PRD), ObjectId.class);
		if(srcPrdId != null) {
			SysDef srcPrdDef = sysDefService.getSysDefById(srcPrdId);
			if(srcPrdDef != null) {
				updates.add(new UpdataData(DFN.flowTask_srcPrd, srcPrdDef.trans2IdNameCn()));
			}
		}

		if(!updates.isEmpty()){
			flowTaskDao.updateByConds(conds, updates);
		}
	}

	// 设置任务责任人，任务开始时间，任务工作量，任务描述
	@SuppressWarnings({ "rawtypes" })
	private void setNodeInfo(List<Map> nodeInfos, List<TeFlowTask> flowTasks) {
		Map<String, Map<String, Object>> nodeInfoMap = new HashMap<String, Map<String, Object>>();
		if (null != nodeInfos && nodeInfos.size() > 0) {
			for (int i = 0; i < nodeInfos.size(); i++) {
				Map<String, Object> nodeInfo = nodeInfos.get(i);
				nodeInfoMap.put(StringUtil.getNotNullStr(nodeInfo.get("id")), nodeInfo);
			}
			for (TeFlowTask flowTask : flowTasks) {
				TeIdName node = flowTask.getNode();
				if (null != node && null != node.getCid()) {
					Map<String, Object> taskInfo = nodeInfoMap.get(StringUtil.getNotNullStr(node.getCid()));
					if (null == taskInfo)
						continue;
					String resp = StringUtil.getNotNullStr(taskInfo.get("resp"));
					Date planStartDate = DateUtil.parseDate(StringUtil.getNotNullStr(taskInfo.get("planStartDate")), DateUtil.DATE_FORMAT);
					Date planEndDate = DateUtil.parseDate(StringUtil.getNotNullStr(taskInfo.get("planEndDate")), DateUtil.DATE_FORMAT);
					Double planEffort = StringUtil.toDouble(StringUtil.getNotNullStr(taskInfo.get("planEffort")));
					String desc = StringUtil.getNotNullStr(taskInfo.get("desc"));
					if (StringUtil.isNotNull(resp)) {
						TeSysUser sysUser = sysUserService.findById(StringUtil.toObjectId(resp));
						List<TeUser> resps = flowTask.getResp();
						TeUser respUser = new TeUser();
						respUser.setJobCode(sysUser.getJobCode());
						respUser.setLoginName(sysUser.getLoginName());
						respUser.setUserId(sysUser.getId());
						respUser.setUserName(sysUser.getUserName());
						if (null != resps) {
							resps.clear();
							resps.add(respUser);
						}
						else {
							resps = new ArrayList<TeUser>();
							resps.add(respUser);
						}
						flowTask.setResp(resps);
					}
					if (null != planStartDate) {
						flowTask.setPlanStartDate(planStartDate);
					}
					if (null != planEndDate) {
						flowTask.setPlanEndDate(planEndDate);
					}
					if (null != planEffort) {
						flowTask.setPlanEffort(planEffort);
					}
					if (StringUtil.isNotNull(desc)) {
						flowTask.setDesc(desc);
					}
					flowTaskDao.updateById(flowTask.getId(), flowTask);
				}
			}
		}
	}

	private Method getMethod(Class<?> objectClass, String methodName) {
		Method[] methods = objectClass.getMethods();
		Method returnMethod = null;
		for (int i = 0; i < methods.length; i++) {
			if (methods[i].getName().equals(methodName)) {
				returnMethod = methods[i];
			}
		}
		return returnMethod;
	}

    /**
     * 保存testSteps信息
     */
    private void saveTestStepsInfo(EventParaPool pool) {
    	ObjectId bizId = pool.get(EventBizConst.EPN_BIZ_ID, ObjectId.class);
    	if (bizId == null) {
			return;
		}
		TeBiz biz = bizDao.findById(bizId);
		if (biz == null) {
			return;
		}

		Map<ObjectId, Object> dataMap = pool.get(EventBizConst.EPN_FIELD_VAL_MAP, Map.class);
		if(dataMap == null || dataMap.isEmpty()) {
			return;
		}
		List<ObjectId> fieldIds = new ArrayList<>(dataMap.keySet());
		Map<ObjectId,BackendMethod> backendMethodMap = cpntAttrBackendMethodFactory.getBackendMethod(fieldIds);

		TeSysUser loginUser = pool.get(EventBizConst.EPN_LOGIN_USER, TeSysUser.class);
		Date currentDate = new Date();

		TeIdNameCn testCase = new TeIdNameCn();
		testCase.setCid(biz.getId());
		testCase.setCodeName(biz.getCode());
		testCase.setName(biz.getName());

		for (Map.Entry<ObjectId, BackendMethod> entry : backendMethodMap.entrySet()) {
			ObjectId fieldId = entry.getKey();
			BackendMethod method = entry.getValue();
			TeSysCpntAttr attr = method.getCpntAttr();
			IBackendMethodService service = method.getService();
			// testStep类型的数据，存储到TeTestStep表
			if (attr != null && CpntAttrConstants.LUUC_TEST_STEPS.getHtmlTag()
					.equals(attr.getHtmlTag()) && dataMap.containsKey(fieldId)) {
				List<Object> tableDatas = (List<Object>) service.getBackendObject(fieldId, attr, dataMap.get(fieldId), true, null);
				if (tableDatas.isEmpty()) {
					continue;
				}

				List<TeTestStep> testSteps = new ArrayList<>();
				for(Object tableData : tableDatas) {
					Map<String, Object> testStepMap = (Map<String, Object>) tableData;
					TeTestStep testStep = new TeTestStep();
					transMap2TestStepObj(testStep, testStepMap);
					testStep.setTestCase(testCase);
					testStep.setIsValid(true);
					testStep.setAddUser(loginUser.trans2User());
					testStep.setAddTime(currentDate);
					testSteps.add(testStep);
				}

				if(testSteps.size() > 0) {
					testStepDao.batchSave(testSteps);
				}
			}
		}
    }

    private void transMap2TestStepObj(TeTestStep testStep, Map<String, Object> testStepMap){
    	if(testStep == null) {
    		return;
    	}

    	Field[] fields = testStep.getClass().getDeclaredFields();
    	for(Field field : fields) {
    		int mod = field.getModifiers();

    		if(Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
    			continue;
    		}

    		if(testStepMap.containsKey(field.getName())) {
    			field.setAccessible(true);
    			try {
					field.set(testStep, testStepMap.get(field.getName()));
				} catch (IllegalArgumentException | IllegalAccessException e) {
					throw new BaseException("通过反射设置属性异常！");
				}
    		}
    	}
    }

    /**
     * 保存关联需求或产品功能
     */
    private void saveLinkedInfo(EventParaPool pool){
    	ObjectId bizId = pool.get(EventBizConst.EPN_BIZ_ID, ObjectId.class);
    	if (bizId == null) {
			return;
		}
		TeBiz biz = bizDao.findById(bizId);
		if (biz == null) {
			return;
		}
		Map<ObjectId, Object> dataMap = pool.get(EventBizConst.EPN_FIELD_VAL_MAP, Map.class);
		if(dataMap == null || dataMap.isEmpty()) {
			return;
		}
		TeSysUser loginUser = pool.get(EventBizConst.EPN_LOGIN_USER, TeSysUser.class);
		for (Map.Entry<ObjectId, Object> entry : dataMap.entrySet()){
			ObjectId fieldId = entry.getKey();
			Object dataValue = entry.getValue();
				if((BizConstants.LINKED_REQ_OR_FUNC_FIELD_ID).equals(fieldId)){
					List<Map> linkedBizs = (List<Map>) JSONArray.parseArray(dataValue.toString(),Map.class);
					List<ObjectId> linkedBizIds = new ArrayList<ObjectId>();
					Set<ObjectId> respSetIds = new HashSet();
					for (int i = 0; i < linkedBizs.size(); i++) {
						Map<String,Object> linkedBizMap = (Map<String,Object>)linkedBizs.get(i);
						Object linkedBizId = linkedBizMap.get("bizId");
						linkedBizIds.add(StringUtil.toObjectId(linkedBizId));
						Map<String, Object> paramsMap = new HashMap<>();
						paramsMap.put("bizId", linkedBizId);
						Map<ObjectId, Object> oprtData = new HashMap<ObjectId, Object>();
						for(Map.Entry<String, Object> e : linkedBizMap.entrySet()){
							String eField = e.getKey();
							Object eValue = e.getValue();
							if( (BizConstants.PUBLISH_TEXT).equals(eField) ){//发布内容
								oprtData.put(BizConstants.PUBLISH_TEXT_FIELD_ID, eValue);
							}else if( (BizConstants.PUBLISH_USER).equals(eField) ){//发布责任人
								oprtData.put(BizConstants.PUBLISH_USER_FIELD_ID, eValue);
								respSetIds.add(StringUtil.toObjectId(eValue));
							}else if( (BizConstants.BIZ_USER).equals(eField) ){//业务责任人
								oprtData.put(BizConstants.BIZ_USER_FIELD_ID, eValue);
							}else if( (BizConstants.TEST_IS_PASS).equals(eField) ){//业务是否测试通过
								if((BizConstants.TEST_IS_PASS_YES).equals(eValue)){
									oprtData.put(BizConstants.TEST_IS_PASS_FIELD_ID, BizConstants.TEST_IS_PASS_YES_FIELD_ID);
								}else if( (BizConstants.TEST_IS_PASS_NO).equals(eValue)){
									oprtData.put(BizConstants.TEST_IS_PASS_FIELD_ID, BizConstants.TEST_IS_PASS_NO_FIELD_ID);
								}
							}else if( (BizConstants.PUBLISH_TESTPALN).equals(eField) ){//上传测试方案
								if(eValue instanceof List){
									List<String> fileIdStrs = (List<String>)eValue;
									if(!fileIdStrs.isEmpty()){
										bizService.fileListAddToBiz(linkedBizId.toString(), fileIdStrs);
									}
								}
							}
						}
						bizEventService.oprt(BizConstants.BIZ_EDIT_OPT_ID, loginUser.getId(), paramsMap, oprtData, "", false);
					}
					//上传测试方案
					//保存关联关系
					bizService.addLinkedBizs(biz, linkedBizIds, loginUser);
					//根据发布责任人剔重后创建分支
					List<ObjectId> respUserIds = new ArrayList<ObjectId>(respSetIds);
					ObjectId parentBrchId = getParentBrchIdByBizId(bizId);
					if(null != parentBrchId){
						 flowBranchService.addGacBrch(parentBrchId, null, null, "",respUserIds);
					}


				  }
			}
    }

   private ObjectId getParentBrchIdByBizId(ObjectId bizId){
	   ObjectId parentBrchId = null;
	   List<IDbCondition> conds = new ArrayList<IDbCondition>();
	   conds.add(new DC_E(DFN.flowTask_biz.dot(DFN.biz_cid),bizId));
	   //conds.add(new DC_E(DFN.flowTask_isValid,true));//此时不能查isValid为true，否则父分支查不到
	   List<TeFlowTask> flowTasks = flowTaskDao.findByConds(conds, null);
	   if(flowTasks.isEmpty()){
		   return parentBrchId;
	   }
	   for (TeFlowTask task : flowTasks) {
		   TeIdName  node =  task.getNode();
		   if( StringUtil.isNotNull(node) && StringUtil.isNotNull(node.getCid())
				   && (BizConstants.BEFORE__PUBLIC_NODE_ID).equals(node.getCid())){
			   List<TeIdName> branches = task.getBranches();
			   if( null != branches && branches.size() == 1 ){
				   parentBrchId = task.getBranches().get(0).getCid();
			   }
		   }
	   }
	   return parentBrchId;
   }
}
