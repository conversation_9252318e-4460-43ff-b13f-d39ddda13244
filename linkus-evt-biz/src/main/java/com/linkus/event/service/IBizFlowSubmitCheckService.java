package com.linkus.event.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.linkus.cmpt.flow.model.TeFlowTask;
import com.linkus.sysuser.model.TeSysUser;
import org.bson.types.ObjectId;

import com.linkus.biz.db.model.TeBiz;
import com.linkus.event.model.vo.FlowTasksVo;

public interface IBizFlowSubmitCheckService {

	/**
	 * 查询回退任务所属业务的挂接业务
	 * 
	 * 用于汇入节点任务回退的时候，用户选择挂接业务使用
	 * 
	 * @param flowTaskId
	 * @return 当任务为汇入节点，并且任务所属业务有挂接业务，返回挂接业务，否则返回空集合
	 */
	List<TeBiz> getFlowNodeBackHangupBizs(ObjectId flowTaskId);
	
	/**
	 * 批量检查提交节点是否有责任人和时间
	 */
	Map<ObjectId, List<Map<String, Object>>> checkFlowNodesSubmit(List<ObjectId> flowTaskIds);

	/**
	 * 检查提交节点是否有责任人和时间
	 * 
	 * @param flowTaskId
	 * @return
	 */
	List<Map<String, Object>> checkFlowNodeSubmit(ObjectId flowTaskId);

	/**
	 * 检查节点回退是否有责任人和时间
	 * 
	 * @param flowTaskId
	 * @return
	 */
	List<Map<String, Object>> checkFlowNodeBack(ObjectId flowTaskId);

	List<Map<String, String>> checkFlowPostPose(ObjectId bizId);

	/**
	 * 检查到指定节点列表是否有责任人和时间
	 * 
	 * @param toNodeIdList
	 * @return
	 */
	List<Map<String, Object>> checkFlowToNodes(List<ObjectId> toNodeIdList);

	/**
	 * @param flowTasks
	 */
	void updateFlowTasks(List<FlowTasksVo> flowTasks, Boolean nullUpdate, String loginName);

	/**
	 * 更新任务责任人
	 * 
	 * @param flowTaskId
	 * @param oprtId
	 * @param respUserId
	 * @param refer
	 * @param bizProcessMailOpen
	 * @return
	 */
	Map<String, Object> updateFlowTaskRespUser(ObjectId flowTaskId, ObjectId oprtId, ObjectId respUserId, TeSysUser loginUser,
											   Double planEffort, String refer, boolean bizProcessMailOpen);

	/**
	 * 批量提交任务责任人
	 * 
	 * @param flowTaskIdList
	 * @param respUserId
	 * @param loginUser
	 * @return
	 */
	Map<String, Object> updateFlowTaskRespUsers(List<ObjectId> flowTaskIdList, String respUserId, TeSysUser loginUser);

	/**
	 * 批量转发我的任务
	 *
	 * @param flowTaskIds 转发任务
	 * @param respUserId 转发给的责任人
	 * @param oprtDesc 转发说明
	 * @param req
	 * @return
	 */
	void forwardMyFlowTask(List<ObjectId> flowTaskIds, ObjectId respUserId, String oprtDesc, HttpServletRequest req);

	/**
	 * 批量更新任务计划完成时间
	 * 
	 * @param flowTaskIdList
	 * @param planEndDate
	 * @return
	 */
	Map<String, Object> updateFlowTaskPlanEndDates(List<ObjectId> flowTaskIdList, Date planEndDate);

	/**
	 * 批量更新任务计划开始时间
	 * 
	 * @param flowTaskIdList
	 * @param planStartDate
	 * @return
	 */
	Map<String, Object> updateFlowTaskPlanStartDates(List<ObjectId> flowTaskIdList, Date planStartDate);

	/**
	 * 批量平移计划时间
	 *
	 * @param flowTaskIdList
	 * @param num
	 * @param reason
	 * @return
	 */
	Map<String, Object> updateFlowTaskTranslationPlanDates(List<ObjectId> flowTaskIdList, int num, String reason);

	/**
	 * 批量更新任务实际时间
	 *
	 * @param flowTaskIdList
	 * @param actualStartDate
	 * @param actualEndDate
	 * @return
	 */
	Map<String, Object> updateFlowTaskActualStartTimeAndActualEndTime(List<ObjectId> flowTaskIdList, Date actualStartDate,Date actualEndDate);

	/**
	 * 设置当前业务工作量确认节点责任人为父业务的提出人
	 * @param bizId
	 */
	void updateWorkConfirmResp(ObjectId bizId, String loginName);

	/**
	 * 验证敏捷是任务是否可以转发责责任人
	 * @param flowTask
	 * @param oprtId
	 * @return
	 */
    Boolean validateScrumTaskIsCanForwardResp(TeFlowTask flowTask, ObjectId oprtId);
}
