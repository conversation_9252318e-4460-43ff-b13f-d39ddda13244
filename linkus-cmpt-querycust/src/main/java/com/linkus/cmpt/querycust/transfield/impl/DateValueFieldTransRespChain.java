package com.linkus.cmpt.querycust.transfield.impl;

import java.util.Date;
import java.util.Map;

import com.linkus.base.util.DateUtil;
import com.linkus.cmpt.querycust.transfield.FieldTransRespChain;
import com.linkus.cmpt.querycust.transfield.bo.TransFieldBO;

@SuppressWarnings("rawtypes")
public class DateValueFieldTransRespChain extends FieldTransRespChain {
	@SuppressWarnings("unchecked")
	@Override
	public void procField(Map newRecord,Map oldRecord, TransFieldBO bo) {
		String key = bo.getKey();
		Object value = oldRecord.get(key);
		if (null != value && value instanceof Date) {
			newRecord.put(PRE_FIELD_KEY + key, DateUtil.formatDate2Str((Date) value, DateUtil.DATETIME_FORMAT));
		}else{
			this.successHandle(newRecord,oldRecord, bo);
		}
	}
}
