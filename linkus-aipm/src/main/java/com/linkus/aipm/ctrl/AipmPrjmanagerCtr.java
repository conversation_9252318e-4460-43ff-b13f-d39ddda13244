package com.linkus.aipm.ctrl;

import com.linkus.aipm.service.IAipmPrjmanagerService;
import com.linkus.base.util.PageBean;
import com.linkus.base.util.StringUtil;
import com.linkus.base.util.excel.ExcelUtils;
import com.linkus.base.web.OAAuthUtil;
import com.linkus.base.web.ctrl.BaseCtrl;
import com.linkus.sysuser.dao.ISysUserDao;
import com.linkus.sysuser.model.TeSysUser;
import com.linkus.sysuser.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目经理绩效类
 * <AUTHOR>
 *
 */

@Controller
@RequestMapping("/aipmPrjmanagerCtr")
public class AipmPrjmanagerCtr extends BaseCtrl{
	
	@Autowired
	private IAipmPrjmanagerService aipmPrjmanagerService;
	@Autowired
	private ISysUserService sysUserService;
	@Autowired
	private ISysUserDao sysUserDao;
	@Autowired
	private OAAuthUtil oaAuthUtil;
	
	
	/**
	 * 绩效考评及时性
	 * @param file
	 */
	@RequestMapping("/importTimelyCheck.action")
	@ResponseBody
	public void importTimelyCheck(MultipartFile file) {
		returnResult(aipmPrjmanagerService.importTimelyCheck(file));
	}
	
	
	/**
	 * 月度计划及时性
	 * @param file
	 */
	@RequestMapping("/importMonthTimelyCheck.action")
	@ResponseBody
	public void importMonthTimelyCheck(MultipartFile file) {
		returnResult(aipmPrjmanagerService.importMonthTimelyCheck(file));
	}
	
	/**
	 * 手工导入修改项目经理得分
	 * @param file
	 */
	@RequestMapping("/updateManagerScoreByImport.action")
	@ResponseBody
	public void updateManagerScoreByImport(MultipartFile file,HttpServletRequest request) {
		TeSysUser currentUser = getTeSysUser(request);
		returnResult(aipmPrjmanagerService.updateManagerScoreByImport(file, currentUser));
	}
	
	/**
	 * 项目经理绩效导出
	 */
	@RequestMapping("/exportPrjManager.action")
	@ResponseBody
	public void exportPrjManager(String ym) {
		PageBean pageBean = aipmPrjmanagerService.exportPrjManager(ym);
		String title = ym+"-月度绩效导出";
		ExcelUtils.exportSXSSFExcelData(pageBean, getResponse(), 1, null, null, 3, title);
	}
	
	/**
	 * 获取该项目经理所拥有的所有有年度绩效的年份清单
	 */
	@RequestMapping("/getAllYearByHasYearScore.action")
	@ResponseBody
	public void getAllYearByHasYearScore(HttpServletRequest request,String loginName) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getAllYearByHasYearScore(currentUser));
	}
	
	
	/**
	 * 项目经理查询
	 */
	@RequestMapping("/queryManagerScore.action")
	@ResponseBody
	public void queryManagerScore(HttpServletRequest request,String loginName) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.queryManagerScore(currentUser));
	}
	
	
	/**
	 * 项目分类，月度
	 * ym 为空时，表示取数据库中最大的
	 * ym 不为空时，表示取传入月份数据
	 * @param 
	 * @return
	 */
	@RequestMapping("/getPrjTypeInfos.action")
	@ResponseBody
	public void getPrjTypeInfos(HttpServletRequest request,String loginName,String ym) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjTypeInfos(currentUser, ym));
	}
	
	/**
	 * 清单列表查询
	 * @return
	 */
	@RequestMapping("/getPrjHeathList.action")
	@ResponseBody
	public void getPrjHeathList(HttpServletRequest request,String loginName,String ym) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjHeathList(currentUser, ym));
	}
	
	/**
	 * 过程指标得分
	 * @return
	 */
	@RequestMapping("/getPrjProcessPoint.action")
	@ResponseBody
	public void getPrjProcessPoint(HttpServletRequest request,String loginName,String ym) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjProcessPoint(currentUser, ym));
	}
	
	
	/**
	 * 手工绩效调整说明
	 * @return
	 */
	@RequestMapping("/getAdJustScoreDesc.action")
	@ResponseBody
	public void getAdJustScoreDesc(HttpServletRequest request,String loginName,String ym) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getAdJustScoreDesc(currentUser, ym));
	}
	
	/**
	 * 项目经理绩效几个月的得分
	 * @return
	 */
	@RequestMapping("/getPrjManagerMonthPoint.action")
	@ResponseBody
	public void getPrjManagerMonthPoint(HttpServletRequest request,String loginName) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjManagerMonthPoint(currentUser));
	}
	
	/**
	 * 项目经理的排名信息
	 * @return
	 */
	@RequestMapping("/getPrjManagerRankInfo.action")
	@ResponseBody
	public void getPrjManagerRankInfo(HttpServletRequest request,String loginName,String ym) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjManagerRankInfo(currentUser, ym));
	}
	
	
	/**
	 * 项目经理的年度绩效 
	 * @return
	 */
	@RequestMapping("/caculatePrjManagerYearScore.action")
	@ResponseBody
	public void caculatePrjManagerYearScore(String year,String month,Boolean isLocked) {
		returnResult(aipmPrjmanagerService.caculatePrjManagerYearScore(year,month, isLocked));
	}
	
	/**
	 * 项目经理年度绩效导出
	 * @return
	 */
	@RequestMapping("/exportPrjManagerYearScore.action")
	@ResponseBody
	public void exportPrjManagerYearScore(String year) {
		PageBean pageBean = aipmPrjmanagerService.exportPrjManagerYearScore(year);
		String title = "年度绩效导出";
		ExcelUtils.exportSXSSFExcelData(pageBean, getResponse(), 1, null, null, 10, title);
	}
	
	
	/**
	 * 查询项目经理年度绩效最新版本数据
	 * @return
	 */
	@RequestMapping("/getMaxPrjManagerYear.action")
	@ResponseBody
	public void getMaxPrjManagerYear(HttpServletRequest request,String loginName) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getMaxPrjManagerYear(currentUser));
	}
	
	/**
	 * 获取项目经理所有月份的项目清单
	 * */
	@RequestMapping("/getPrjManagerMonthScoreListByYear.action")
	@ResponseBody
	public void getPrjManagerMonthScoreListByYear( HttpServletRequest request,String yearMonth, String loginName ) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjManagerMonthScoreListByYear(yearMonth, currentUser));
	}
	
	/**
	 * 获取截止年月的项目经理的数据
	 * @return
	 */
	@RequestMapping("/getPrjManagerYearScoreInfo.action")
	@ResponseBody
	public void getPrjManagerYearScoreInfo(HttpServletRequest request, String loginName ,String year ) {
		//获取当前登录人
		TeSysUser currentUser = transSysUser(request,loginName);
		returnResult(aipmPrjmanagerService.getPrjManagerYearScoreInfo( currentUser,year));
	}



	private TeSysUser transSysUser(HttpServletRequest request,String loginName){
		TeSysUser currentUser = null;
		if(StringUtil.isNull( loginName )){
			currentUser = getTeSysUser(request);
		}else{
			TeSysUser teSysUser = new TeSysUser();
			teSysUser.setLoginName( loginName );
			teSysUser.setIsValid(true);
			List<TeSysUser> sysUsers = sysUserDao.findByEntity(teSysUser, null);
			currentUser = sysUsers.get(0);
		}
		return currentUser;
	}
	
	private TeSysUser getTeSysUser(HttpServletRequest request) {
		String casLoginUserName = getCasLoginUser();
		TeSysUser loginUser = sysUserService.queryByLoginName(casLoginUserName);
		if( loginUser == null ){
			//如果当前登录人没有信息，则再通过请求头 Authorization 去获取对应的token，然后再进行人员数据转换
			//从请求中获取当前登录人信息
			String userName = oaAuthUtil.getOAAccountUserName(request.getHeader("Authorization"),true);
			loginUser = sysUserService.queryByLoginName(userName);
		}
		return loginUser;
	}

	/**
	 * 校验计算年度绩效条件
	 * @return
	 */
	@RequestMapping("/validSysUserYearData.action")
	@ResponseBody
	public void validSysUserYearData(String year) {
		returnResult(aipmPrjmanagerService.validSysUserYearData(year));
	}

	/**
	 * 计算员工年度绩效
	 * @return
	 */
	@RequestMapping("/caculateSysUserYearData.action")
	@ResponseBody
	public void caculateSysUserYearData(String loginName, String year) {
		returnResult(aipmPrjmanagerService.caculateSysUserYearData(loginName, year));
	}

	/**
	 * 锁定员工年度绩效
	 * @return
	 */
	@RequestMapping("/lockSysUserYearData.action")
	@ResponseBody
	public void lockSysUserYearData() {
		returnResult(aipmPrjmanagerService.lockSysUserYearData());
	}

	/**
	 * 查询我的年度绩效
	 * @return
	 */
	@RequestMapping("/queryComposePfmReviewYearData.action")
	@ResponseBody
	public void queryComposePfmReviewYearData(String loginName, Integer index, Integer size) {
		returnResult(aipmPrjmanagerService.queryComposePfmReviewYearData(loginName, index, size));
	}

	/**
	 * 查询下属年度绩效
	 * @return
	 */
	@RequestMapping("/queryComposeDeptPfmReviewYear.action")
	@ResponseBody
	public void queryComposeDeptPfmReviewYear(String filterMap, Integer index, Integer size) {
		returnResult(aipmPrjmanagerService.queryComposeDeptPfmReviewYear(filterMap, index, size));
	}

	/**
	 * 查询BU/CC年度绩效
	 * @return
	 */
	@RequestMapping("/queryComposeEmpPfmReviewYear.action")
	@ResponseBody
	public void queryComposeEmpPfmReviewYear(String filterMap, Integer index, Integer size) {
		returnResult(aipmPrjmanagerService.queryComposeEmpPfmReviewYear(filterMap, index, size));
	}
}
