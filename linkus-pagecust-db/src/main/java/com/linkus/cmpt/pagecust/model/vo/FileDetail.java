package com.linkus.cmpt.pagecust.model.vo;

import org.bson.types.ObjectId;

import java.util.List;

public class FileDetail {
    private ObjectId srcCid;
    private ObjectId cid;
    private String name;
    private Long downloadCount;
    private List<DetailVo> downloadDetail;

    public ObjectId getSrcCid() {
        return srcCid;
    }

    public void setSrcCid(ObjectId srcCid) {
        this.srcCid = srcCid;
    }

    public ObjectId getCid() {
        return cid;
    }

    public void setCid(ObjectId cid) {
        this.cid = cid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<DetailVo> getDownloadDetail() {
        return downloadDetail;
    }

    public void setDownloadDetail(List<DetailVo> downloadDetail) {
        this.downloadDetail = downloadDetail;
    }

    public Long getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }
}
